package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.*;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EventTicketsServiceImplTest {

    @InjectMocks @Spy EventTicketsServiceImpl eventTicketsServiceImpl = new EventTicketsServiceImpl();
    
    @Mock
    private EventTicketsRepository eventTicketsRepository;
    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private TicketingEmailService ticketingEmailService;

    @Mock
    private TicketingRefundService ticketingRefundService;

    private Event event;
    private User user;
	private TicketingType ticketingType;

    private Long id = 1L;
    private String startDate = "01/01/2010 00:00:00";
    private String endDate = "04/04/2019 00:00:00";
    private Date startDate1 = new Date(startDate);
    private Date endDate1 = new Date(endDate);
    private long oldEventId = id;
    private long newRecurringId = 3L;
    private Long recurringEventId = id;
    private Long orderId = id;
    private Long eventTicketingId = id;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();

        user = EventDataUtil.getUser();

        ticketingType = new TicketingType();
        ticketingType.setId(id);

		EventTickets eventTickets = new EventTickets();
        eventTickets.setId(id);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setNumberoftickets(1L);
        eventTickets.setTicketingTypeId(ticketingType);
    }

 /*   @Test
    void test_updateEventTicketsWithDbRecurringEvent_success() {

        //Execution
        eventTicketsServiceImpl.updateEventTicketsWithDbRecurringEvent(oldEventId, newRecurringId);

        Mockito.verify(eventTicketsCommonRepo, Mockito.times(1)).updateRecurringId(oldEventId, newRecurringId);
    }*/

    /*@Test
    void test_updateEventTicketOrderStatusWithCard_success() throws TemplateException, JAXBException, DocumentException, IOException {

        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(id);
        ticketingOrder.setEventid(event);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        //Mock
        when(ticketingOrderRepository.findById(orderId)).thenReturn(Optional.of(ticketingOrder));
        when(eventTicketsCommonRepo.findByTicketingOrder(ticketingOrder.getId())).thenReturn(eventTicketList);
        doNothing().when(ticketingEmailService).reSendTicketingPurchaseOrderEmail(event, ticketingOrder, eventTicketList, true);

        //Execution
        eventTicketsServiceImpl.updateEventTicketOrderStatus(ticketingOrder.getId(), user);

        ArgumentCaptor<EventTickets> argumentCaptorSchedule = ArgumentCaptor.forClass(EventTickets.class);

        verify(eventTicketsRepository, Mockito.times(eventTicketList.size())).save(argumentCaptorSchedule.capture());

        EventTickets eventTickets = argumentCaptorSchedule.getValue();
        assertEquals(ticketingOrder.getStatus().toString(), eventTickets.getStatus());
    }*/

   /* @Test
    void test_updateEventTicketOrderStatusWithComplimentary_success() throws TemplateException, JAXBException, DocumentException, IOException {

        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(id);
        ticketingOrder.setEventid(event);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.COMPLIMENTARY);

        //Mock
        when(ticketingOrderRepository.findById(orderId)).thenReturn(Optional.of(ticketingOrder));
        when(eventTicketsCommonRepo.findByTicketingOrder(ticketingOrder.getId())).thenReturn(eventTicketList);
        doNothing().when(ticketingEmailService).reSendTicketingPurchaseOrderEmail(event, ticketingOrder, eventTicketList, true);

        //Execution
        eventTicketsServiceImpl.updateEventTicketOrderStatus(ticketingOrder.getId(), user);

        ArgumentCaptor<EventTickets> argumentCaptorSchedule = ArgumentCaptor.forClass(EventTickets.class);

        verify(eventTicketsRepository, Mockito.times(eventTicketList.size())).save(argumentCaptorSchedule.capture());

        EventTickets eventTickets = argumentCaptorSchedule.getValue();
        assertEquals("CREATED", eventTickets.getStatus());
    }
*/

  /*  @Test
    void test_updateEventTicketsToDelete_successWithEventTicketingId() throws StripeException, ApiException, com.squareup.connect.ApiException {

        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);

        List<RefundInfo> refundInfos = new ArrayList<>();

        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(id);
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(100D);
        refundInfos.add(refundInfo);
        RefundInfoArray refundDto = new RefundInfoArray(refundInfos,getDefaultFormatDateStr());
        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);
        TicketingRefundService mock = mock(TicketingRefundService.class);
        doNothing().when(mock).refund(orderId, event,refundDto.getRefunds(),startDate1);

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, eventTicketingId);

        // Verify call of only single event ticket delete
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> deleteEventTicketSize = argument.getValue();
        assertTrue(deleteEventTicketSize.size() == 1);

        deleteEventTicketSize.forEach(e -> {
            assertEquals(e.getStatus(), Constants.DELETE);
        });
    }*/

   /* @Test
    void test_updateEventTicketsToDelete_successWithOutEventTicketingId() throws StripeException, ApiException {
        //setup
        Long eventTicketingId = 0L;

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);

        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);
        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
        doNothing().when(autoAssignedAttendeeNumbersService).deleteAssignedNumbersByEventTicketId(eventTicketList);

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, eventTicketingId);

        // Verify call of only single event ticket delete
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> deleteEventTicketSize = argument.getValue();
        assertTrue(deleteEventTicketSize.size() == eventTicketList.size());

        deleteEventTicketSize.forEach(e -> {
            assertEquals(e.getStatus(), Constants.DELETE);
        });

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();

        assertEquals(ticketingOrderData.getStatus(), TicketingOrder.TicketingOrderStatus.PAID_DELETE);
    }
*/
    private TicketingOrder createTicketingOrder(Long orderId) {
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(orderId);
        ticketingOrder.setEventid(event);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        return ticketingOrder;
    }

   /* @Test
    void test_updateEventTicketsToDelete_successWithOutEventTicketingIdAndOrderStatusUnPaid() throws StripeException, ApiException {
        //setup
        Long eventTicketingId = 0L;

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID_DELETE);

        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, eventTicketingId);

        // Verify call of only single event ticket delete
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> deleteEventTicketSize = argument.getValue();
        assertTrue(deleteEventTicketSize.size() == eventTicketList.size());

        deleteEventTicketSize.forEach(e -> {
            assertEquals(e.getStatus(), Constants.DELETE);
        });

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();

        assertEquals(ticketingOrderData.getStatus(), TicketingOrder.TicketingOrderStatus.UNPAID_DELETE);
    }
*/
    private EventTickets createEventTickets(Long eventTicketingId, TicketStatus ticketStatus) {
        EventTickets eventTickets = new EventTickets();
        eventTickets.setId(eventTicketingId);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setNumberoftickets(1L);
        eventTickets.setTicketStatus(ticketStatus);
        eventTickets.setTicketingTypeId(ticketingType);
        return eventTickets;
    }

    public static Object[] getTicketingBuyerParameters(){
        return new Object[]{
                new Object[]{true, true} ,
                new Object[]{false, false} ,
        };
    }

    public static Object[] getRecurringEventIds(){
        return new Object[]{
                new Object[]{-1L} ,
                new Object[]{null} ,
        };
    }
  /* @ParameterizedTest
	@MethodSource("getRecurringEventIds")
    void test_getTicketingBuyerData_success(Long recuringEventId){
        // setup
        Pageable pageable=PageRequest.of(1, Integer.MAX_VALUE);

        //Execute
        eventTicketsServiceImpl.getTicketingBuyerData(event.getEventId(),pageable, recuringEventId, DataType.TICKET);
        Class<ArrayList<TicketType>> listClass = (Class<ArrayList<TicketType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketType>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsCommonRepo).getTicketingBuyerData(any(),any(),anyString(),argument.capture(),anyLong(),anyBoolean(), any(TicketStatus.class), anyBoolean(), any());

        List<TicketType> ticketTypeList = argument.getValue();
        assertTrue(ticketTypeList.contains(TicketType.DONATION));
    }
*/
   /* @ParameterizedTest
	@MethodSource("getTicketingBuyerParameters")
    void test_getTicketingBuyerData_success(boolean isIncludeDonationTicketType, boolean includeDonationCheck){

        //setup
        Pageable pageable=PageRequest.of(1, Integer.MAX_VALUE);

        //Execute
        eventTicketsServiceImpl.getTicketingBuyerData(event.getEventId(),pageable,isIncludeDonationTicketType,
                recurringEventId, false, false, DataType.TICKET);

        Class<ArrayList<TicketType>> listClass = (Class<ArrayList<TicketType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketType>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsCommonRepo).getTicketingBuyerData(any(),any(),anyString(),argument.capture(),anyLong(),anyBoolean(), any(TicketStatus.class), anyBoolean(), any());

        List<TicketType> ticketTypeList = argument.getValue();
        assertTrue(includeDonationCheck ? ticketTypeList.contains(TicketType.DONATION) : !ticketTypeList.contains(TicketType.DONATION));
    }
*/
   /* @ParameterizedTest
	@MethodSource("getTicketingBuyerParameters")
    void test_getTicketingBuyerDataBetweenDates_success(boolean isIncludeDonationTicketType, boolean includeDonationCheck){

        //setup
        Pageable pageable=PageRequest.of(1, Integer.MAX_VALUE);

        //Execute
        eventTicketsServiceImpl.getTicketingBuyerDataBetweenDates(event.getEventId(),pageable,isIncludeDonationTicketType,startDate1,endDate1, recurringEventId);

        Class<ArrayList<TicketType>> listClass = (Class<ArrayList<TicketType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketType>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).getTicketingBuyerDataBetweenDates(any(),any(),anyString(),argument.capture(),any(),any(),anyLong(), any(TicketStatus.class));

        List<TicketType> ticketTypeList = argument.getValue();
        assertTrue(includeDonationCheck ? ticketTypeList.contains(TicketType.DONATION) : !ticketTypeList.contains(TicketType.DONATION));
    }*/

   /* @Test
    void test_updateEventTicketsToDelete_RefundedTicketStatus() {

        //setup
        Long eventTicketingId = 0L;

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.REFUNDED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID_DELETE);

        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, eventTicketingId);

        // Verify call of only single event ticket delete
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> deleteEventTicketSize = argument.getValue();
        assertTrue(deleteEventTicketSize.size() == eventTicketList.size());

        deleteEventTicketSize.forEach(e -> {
            assertEquals(e.getStatus(), Constants.DELETE);
        });

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder ticketingOrderData = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingOrderData.getStatus(), TicketingOrder.TicketingOrderStatus.PAID_DELETE);
    }
*/
   /* @Test
    void test_updateEventTicketsToDelete_throwStripeException_TICKET_CAN_NOT_DELETED_REFUND_FAILED() throws StripeException, ApiException, com.squareup.connect.ApiException {

        //setup
        Long eventTicketingId = 0L;

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);

        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);

        doThrow(ApiException.class).when(ticketingRefundService).refund(anyLong(), any(),anyList(),any());

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, eventTicketingId);
    }*/

   /* @Test
    void test_isLastTicketOrFullOrderIsDeleted_success_with_ticketingId(){

        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));

        //Execution
        boolean isLastTicketOrFullOrderIsDeleted = eventTicketsServiceImpl.isLastTicketOrFullOrderIsDeleted(eventTicketingId, eventTicketList);

        assertTrue(isLastTicketOrFullOrderIsDeleted);
    }*/

   /* @Test
    void test_isLastTicketOrFullOrderIsDeleted_success_with_EventTickets(){

        //setup
        Long eventTicketingId = 0L;

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add( createEventTickets(eventTicketingId, TicketStatus.BOOKED));
        eventTicketList.add( createEventTickets(2L, TicketStatus.BOOKED));

        //Execution
        boolean isLastTicketOrFullOrderIsDeleted = eventTicketsServiceImpl.isLastTicketOrFullOrderIsDeleted(eventTicketingId, eventTicketList);

        assertTrue(isLastTicketOrFullOrderIsDeleted);
    }*/

   /* @Test
    void test_updateEventTicketsToDelete_success() throws StripeException, ApiException {
        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();

        TicketingOrder ticketingOrder = createTicketingOrder(orderId);

        //Mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, Constants.DELETE)).thenReturn(eventTicketList);

        //Execute
        eventTicketsServiceImpl.updateEventTicketsToDelete(event, orderId, 0L);

        // Verify call of only single event ticket delete
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> deleteEventTicketSize = argument.getValue();
        assertFalse(deleteEventTicketSize.size() == 1);
    }*/

    @Test
    void test_getEventTicketIdAndHolderInfoByEventForRegisterSession_success(){

        //mock
        when(eventTicketsRepository.getEventTicketIdAndHolderInfoByEventForRegisterSession(user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED), event)).thenReturn(Collections.emptyList());

        //Execution
        eventTicketsServiceImpl.getEventTicketIdAndHolderInfoByEventForRegisterSession(user,event);

        //assert
        verify(eventTicketsRepository).getEventTicketIdAndHolderInfoByEventForRegisterSession(user,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED), event);
    }

    @Test
    void test_findByTicketingOrderAndTicketingTypeIdAndRecurringEvent_success(){

        //mock
        when(eventTicketsRepository.findByTicketingOrderAndTicketingTypeIdAndRecurringEvent(orderId,1L,recurringEventId)).thenReturn(Collections.emptyList());

        //Execution
        eventTicketsServiceImpl.findByTicketingOrderAndTicketingTypeIdAndRecurringEvent(orderId,1L,recurringEventId);

        //assert
        verify(eventTicketsRepository).findByTicketingOrderAndTicketingTypeIdAndRecurringEvent(orderId,1L,recurringEventId);
    }

    @Test
    void test_updateEventTicketsRecStatusByRecurringEventIds_success(){

        //setup
        List<Long> recList = new ArrayList<>();
        recList.add(recurringEventId);

        //mock
        doNothing().when(eventTicketsRepository).updateEventTicketsRecStatusByRecurringEventIds(recList, RecordStatus.CANCEL);

        //Execution
        eventTicketsServiceImpl.updateEventTicketsRecStatusByRecurringEventIds(recList,RecordStatus.CANCEL);

        //assert
        verify(eventTicketsRepository).updateEventTicketsRecStatusByRecurringEventIds(recList,RecordStatus.CANCEL);
    }

}