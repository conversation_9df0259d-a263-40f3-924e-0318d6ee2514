package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.DataType;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.TicketingCouponRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.TicketingOrderManagerService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.TicketingCouponDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingOrderServiceImplTest {


    @Spy
    @InjectMocks
    private  TicketingOrderServiceImpl ticketingOrderServiceImpl;
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private TicketingCouponRepository ticketingCouponRepository;

	private TicketingOrder ticketingOrder;
    private TicketingOrderManager ticketingOrderManager;
    private TicketingCoupon ticketingCoupon;
    private TicketingType ticketingType;
    private Event event;
    private TicketingCouponDto ticketingCouponDto;
    private TrackingLinks trackingLinks;

	private String startDate = "01/01/2019 00:00:00";
    private String endDate = "04/04/2019 00:00:00";
    private String eventTicketTypeId = "1";
	private String couponCode = "couponCode";
    private Date startDate1 = new Date(startDate);
    private Date endDate1 = new Date(endDate);
    private long uses = 1L;
    private Date orderDate = new Date("2019/03/01 05:30");
    private Date searchTimeStamp = new Date("2019/03/01 05:30");

    @BeforeEach
    public void setUp() throws Exception {
		String eventName = "TestEvent";
		event = new Event(eventName, true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
		Long eventId = 1L;
		event.setEventId(eventId);
        event.setCurrency(Currency.AUD);
		String eventUrl = "asd";
		event.setEventURL(eventUrl);

		User user = new User();
        user.setUserId(1L);

        TrackingLinks trackingLinks = new TrackingLinks();
        trackingLinks.setLinkUrl("test1");
        trackingLinks.setActive(true);
        trackingLinks.setEvent(event);
        trackingLinks.setId(1L);

        ticketingOrder = new TicketingOrder();
		long orderId = 1L;
		ticketingOrder.setId(orderId);
        ticketingOrder.setEventid(event);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder.setTrackingLinks(trackingLinks);
    }

    @Test
    public void test_findByid_success() {
        //mock
        when(ticketingOrderRepository.findById(ticketingOrder.getId())).thenReturn(Optional.of(ticketingOrder));

        //Excecution
        TicketingOrder actual = ticketingOrderServiceImpl.findByid(ticketingOrder.getId());
        assertEquals(ticketingOrder.getId(), actual.getId());

    }

   @Test
    public void test_findByid_throwException_OrderNotFound() throws IOException {

        //mock
        when(ticketingOrderRepository.findById(ticketingOrder.getId())).thenReturn(Optional.empty());

        //Excecution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingOrderServiceImpl.findByid(ticketingOrder.getId()));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_findByidAndEventid_success() {


        //mock
        when(ticketingOrderRepository.findByIdAndEventid(ticketingOrder.getId(),event)).thenReturn(ticketingOrder);

        //Excecution
        TicketingOrder actual = ticketingOrderServiceImpl.findByidAndEventid(ticketingOrder.getId(),event);
        assertEquals(ticketingOrder.getId(), actual.getId());
    }

    @Test
    public void test_findByidAndEventid_throwException_OrderNotFound() {
        //mock
        when(ticketingOrderRepository.findByIdAndEventid(ticketingOrder.getId(),event)).thenReturn(null);

        //Excecution
        Exception exception = assertThrows(NotFoundException.class,
            () -> ticketingOrderServiceImpl.findByidAndEventid(ticketingOrder.getId(),event));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

     @Test
     public void test_setReferencesToNull_success() {

        //setup
        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock
         when(ticketingOrderRepoService.getTicketOrdersByTrackingLinks(trackingLinks)).thenReturn(ticketingOrders);

         //Execution
         ticketingOrderServiceImpl.setReferencesToNull(trackingLinks);
         ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
         verify(ticketingOrderRepository,times(1)).save(ticketingOrderArgumentCaptor.capture());

         TicketingOrder actual = ticketingOrderArgumentCaptor.getValue();
         assertEquals(null,actual.getTrackingLinks());

     }

     @Test
     public void test_findByEventidAndStatusIsPaid_success_withCustomSearch() {

        //setup
         String search = "customSearch";
         long recurringEventId = 1L;
         List<TicketingOrder.TicketingOrderStatus> statusList = new ArrayList<>();
         statusList.add(TicketingOrder.TicketingOrderStatus.PAID);
         statusList.add(TicketingOrder.TicketingOrderStatus.UNPAID);

         Pageable pageable = PageRequest.of(1, 10);

        //mock
         Page<TicketingOrder> ticketingOrder = Mockito.mock(Page.class);

        //Execution
         ticketingOrderServiceImpl.findByEventidAndStatusIsPaid(event, 10, 1, search, recurringEventId, orderDate, DataType.TICKET, searchTimeStamp,false);


    }

    @Test
    public void test_findByEventidAndStatusIsPaid_success_withoutCustomSearch() {

        //setup
        long recurringEventId = 1L;
        List<TicketingOrder.TicketingOrderStatus> statusList = new ArrayList<>();
        statusList.add(TicketingOrder.TicketingOrderStatus.PAID);
        statusList.add(TicketingOrder.TicketingOrderStatus.UNPAID);

        Pageable pageable = PageRequest.of(1, 10);

        //mock
        Page<TicketingOrder> ticketingOrder = Mockito.mock(Page.class);


       //Execution
        ticketingOrderServiceImpl.findByEventidAndStatusIsPaid(event, 10, 1, null, recurringEventId, orderDate, DataType.TICKET, searchTimeStamp,false);
    }
}