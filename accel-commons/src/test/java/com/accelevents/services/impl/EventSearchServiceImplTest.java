
package com.accelevents.services.impl;

import com.accelevents.domain.enums.EventType;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.EventSearchCardDto;
import com.accelevents.dto.EventSearchDetailsDto;
import com.accelevents.repositories.EventRepository;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.TicketingDisplayService;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EventSearchServiceImplTest {

    @Spy
    @InjectMocks
    private EventSearchServiceImpl eventSearchServiceImpl;
    @Mock
    private EventRepository eventRepository;
    @Mock
    private ROEventService roEventService;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private TicketingDisplayService ticketingDisplayService;

    private EventSearchDetailsDto eventSearchDetailsDto;
    private EventSearchCardDto eventSearchCardDto;
    private Date eventStartDate = DateUtils.getCurrentDate();
    private int page = 0;
    private int size = 10;
	private BigDecimal latitude = BigDecimal.valueOf(23.025427);
    private BigDecimal longitude = BigDecimal.valueOf(72.600496);
    private String whiteLabelUrl = "NareshTest";

    static String eventAddress = "AHMEDABAD";
    static String eventDesc = "Racing Event";
    static String eventName = "Something Different";
    static String organizerName = "Racer";
    static String organizerUrl = "Organizer_Event";
    static String eventUrl = "LiveForPassion";
    static Date eventEndDate = DateUtils.getCurrentDate();

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        eventSearchCardDto = new EventSearchCardDto();
        eventSearchCardDto.setEventLocation(eventAddress);
        eventSearchCardDto.setEventDescription(eventDesc);
		Long eventId = 1414l;
        eventSearchCardDto.setEventId(eventId);
		String eventLogo = "134f96a2-bfee-4080-8247-044176abd29b";
        eventSearchCardDto.setEventLogo(eventLogo);
        eventSearchCardDto.setEventName(eventName);
        eventSearchCardDto.setEventUrl(eventUrl);
        eventSearchCardDto.setEventEndDate(eventEndDate);
        eventSearchCardDto.setEquivalentTimezone("US/Eastern");
        eventSearchCardDto.setEventContactEmailAddress("newYearEveAdmin@gmail");
        eventSearchCardDto.setEventTicketingLink("https://www.stagingaccel.com/e/u/checkout/NewYearEvents0/tickets/order");
    }

    static Object[] getSearchStringWithExistingEventOrOrganizerData(){
		Double minimumTicketPrice = 30d;
		Double maximumTicketPrice = 100d;
		Object[] priceReange = {maximumTicketPrice, minimumTicketPrice};
        List<Object[]> ticketPriceRange = new ArrayList<>();
        ticketPriceRange.add(priceReange);
        

        return new Object[]{
          new Object[]{eventAddress, "MUSIC"},
          new Object[]{eventDesc, "FUNDRAISER"},
          new Object[]{eventName, "BUSINESS_AND_PROFESSIONAL"},
          new Object[]{organizerName, "SPORTS"},
          new Object[]{eventUrl, "FOOD_AND_DRINK"},
          new Object[]{eventEndDate.toString(), "OTHER"},

          new Object[]{eventAddress, "MUSIC"},
          new Object[]{eventDesc, "FUNDRAISER"},
          new Object[]{eventName, "BUSINESS_AND_PROFESSIONAL"},
          new Object[]{organizerName, "SPORTS"},
          new Object[]{organizerUrl, "FOOD_AND_DRINK"},
          new Object[]{eventUrl, "OTHER"},
          new Object[]{eventEndDate.toString(), ""},

          new Object[]{eventAddress, "MUSIC"},
          new Object[]{eventDesc, "FUNDRAISER"},
          new Object[]{eventName, "BUSINESS_AND_PROFESSIONAL"},
          new Object[]{organizerName, "SPORTS"},
          new Object[]{organizerUrl, "FOOD_AND_DRINK"},
          new Object[]{eventUrl, "OTHER"},
          new Object[]{eventEndDate.toString(), ""},

        };
    }
    @ParameterizedTest
    @MethodSource("getSearchStringWithExistingEventOrOrganizerData")
    void test_getAllEvents_searchWithExistingEventOrOrganizerDataWithTicketingEnableTrueAndEventTypeMusic(String searchString, String eventType) {

        //setup
        Object[] eventIdAndDistanceBasedOnLatLong = {BigInteger.valueOf(46),2.2671246702066146d};
        List<Object[]> eventIdWithDistance = new ArrayList<>();
        eventIdWithDistance.add(eventIdAndDistanceBasedOnLatLong);
        List<EventSearchCardDto> eventDetailsDtoList = new ArrayList<>();
        eventDetailsDtoList.add(eventSearchCardDto);
        Page<EventSearchCardDto> pageEvents = new PageImpl<>(eventDetailsDtoList);

        List<Object[]> externalEventIdWithDistance = new ArrayList<>();
        externalEventIdWithDistance.add(eventIdAndDistanceBasedOnLatLong);
        List<EventSearchCardDto> externalEventsDetail = new ArrayList<>();
        externalEventsDetail.add(eventSearchCardDto);
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(externalEventsDetail);

        //mock
        when(eventRepository.findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any())).thenReturn(pageEvents);
        when(eventRepository.findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any())).thenReturn(externalEvents);
        when(eventRepository.findAllEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(eventIdWithDistance);
        when(eventRepository.findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(externalEventIdWithDistance);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, searchString, Collections.singletonList(0L), eventType, size, whiteLabelUrl, latitude, longitude, null, null, null,false);

        //Assertion
         verify(eventRepository).findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any());
         verify(eventRepository).findAllEventIdByLatitudeLongitude(any(), any(), anyInt());

         assertFalse(dataTableResponse.getData().isEmpty());
         assertEquals(2,dataTableResponse.getRecordsTotal() );
    }

    @ParameterizedTest
    @MethodSource("getSearchStringWithExistingEventOrOrganizerData")
    void test_getAllEvents_searchWithExistingEventOrOrganizerDataWithTicketingEnableTrueAndEventIdWithDistanceEmpty(String searchString, String eventType) {

        //setup
        Object[] eventIdAndDistanceBasedOnLatLong = {BigInteger.valueOf(46),2.2671246702066146d};
        List<Object[]> eventIdWithDistance = new ArrayList<>();

        List<Object[]> externalEventIdWithDistance = new ArrayList<>();
        externalEventIdWithDistance.add(eventIdAndDistanceBasedOnLatLong);
        List<EventSearchCardDto> externalEventsDetail = new ArrayList<>();
        externalEventsDetail.add(eventSearchCardDto);
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(externalEventsDetail);

        //mock
        when(eventRepository.findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any())).thenReturn(externalEvents);
        when(eventRepository.findAllEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(eventIdWithDistance);
        when(eventRepository.findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(externalEventIdWithDistance);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, searchString,  Collections.singletonList(0L), eventType, size, whiteLabelUrl, latitude, longitude, null, null,null, false);

        //Assertion
        verify(eventRepository).findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any());
        verify(eventRepository).findAllEventIdByLatitudeLongitude(any(), any(), anyInt());
        verify(eventRepository).findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt());

        assertFalse(dataTableResponse.getData().isEmpty());
        assertEquals(1,dataTableResponse.getRecordsTotal());
    }

    @ParameterizedTest
    @MethodSource("getSearchStringWithExistingEventOrOrganizerData")
    void test_getAllEvents_searchWithExistingEventOrOrganizerDataAndTicketingEnableFalseAndExternalEventEmpty(String searchString, String eventType) {

        //setup
        Object[] eventIdAndDistanceBasedOnLatLong = {BigInteger.valueOf(46),2.2671246702066146d};
        List<Object[]> eventIdWithDistance = new ArrayList<>();
        eventIdWithDistance.add(eventIdAndDistanceBasedOnLatLong);
        List<EventSearchCardDto> eventDetailsDtoList = new ArrayList<>();
        eventDetailsDtoList.add(eventSearchCardDto);
        Page<EventSearchCardDto> pageEvents = new PageImpl<>(eventDetailsDtoList);

        List<Object[]> externalEventIdWithDistance = new ArrayList<>();
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(Collections.emptyList());

        //mock
        when(eventRepository.findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), nullable(String.class), anyBoolean(), any(), any())).thenReturn(pageEvents);
        when(eventRepository.findAllEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(eventIdWithDistance);
        when(eventRepository.findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(externalEventIdWithDistance);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, searchString,  Collections.singletonList(0L), eventType, size, "", latitude, longitude, null, null, null,false);

        //Assertion
        verify(eventRepository).findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), nullable(String.class), anyBoolean(), any(), any());
        verify(eventRepository).findAllEventIdByLatitudeLongitude(any(), any(), anyInt());
        verify(eventRepository).findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt());

        assertEquals(1,dataTableResponse.getRecordsTotal() );
    }

    static Object[] getSearch(){
        Date date = DateUtils.addDaysToNow(5);
        return new Object[]{
                new Object[]{"DELHI"},
                new Object[]{"desc"},
                new Object[]{"5L"},
                new Object[]{"logo"},
                new Object[]{"name"},
                new Object[]{"Aryan"},
                new Object[]{"Aryan"},
                new Object[]{"url"},
                new Object[]{date.toString()},
        };
    }

    @ParameterizedTest
    @MethodSource("getSearch")
    void test_getAllEvents_searchWithNotMatchedEventOrOrganizerDataAndLatlongNull(String searchString) {

        //setup
        Page<EventSearchCardDto> events = new PageImpl<>(Collections.emptyList());
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(Collections.emptyList());

        //mock
        when(eventRepository.findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any())).thenReturn(events);
        when(eventRepository.findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any())).thenReturn(externalEvents);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, searchString,  Collections.singletonList(0L), EventType.MUSIC.name(), size, whiteLabelUrl, null, null, null, null, null,false);

        //Assertion
        verify(eventRepository).findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any());
        verify(eventRepository).findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any());

        assertTrue(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getAllEvents_withLongitudeNull() {

        //setup
        Page<EventSearchCardDto> events = new PageImpl<>(Collections.emptyList());
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(Collections.emptyList());

        //mock
        when(eventRepository.findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any())).thenReturn(events);
        when(eventRepository.findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any())).thenReturn(externalEvents);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, "OrganizerUrl",  Collections.singletonList(0L), EventType.MUSIC.name(), size, whiteLabelUrl, latitude, null, null, null, null,false);

        //Assertion
        verify(eventRepository).findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any());
        verify(eventRepository).findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any());

        assertTrue(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getAllEvents_withLatitudeNull() {

        //setup
        Page<EventSearchCardDto> events = new PageImpl<>(Collections.emptyList());
        Page<EventSearchCardDto> externalEvents = new PageImpl<>(Collections.emptyList());

        //mock
        when(eventRepository.findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any())).thenReturn(events);
        when(eventRepository.findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any())).thenReturn(externalEvents);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, "OrganizerName",  Collections.singletonList(0L),"SPORTS", size, whiteLabelUrl, null, latitude, null, null, null,false);

        //Assertion
        verify(eventRepository).findAllEventByNameAndEventStatusAndSearchString(anyString(), anyList(), anyList(), anyList(), anyString(), anyBoolean(), any(), any());
        verify(eventRepository).findAllEventByNameAndLatLong(anyString(), anyList(), anyList(), anyString(), anyBoolean(), any());

        assertTrue(dataTableResponse.getData().isEmpty());
    }

    @Test
    void test_getAllEvents_witheventIdWithDistanceEmpty() {

        //setup
        List<Object[]> eventIdWithDistance = new ArrayList<>();
        List<Object[]> externalEventIdWithDistance = new ArrayList<>();

        //mock
        when(eventRepository.findAllEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(eventIdWithDistance);
        when(eventRepository.findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt())).thenReturn(externalEventIdWithDistance);

        //Execution
        DataTableResponse dataTableResponse = eventSearchServiceImpl.getAllEvents(page, "OrganizerName",  Collections.singletonList(0L),"SPORTS", size, whiteLabelUrl, longitude, latitude, null, null, null,false);

        //Assertion
        verify(eventRepository).findAllEventIdByLatitudeLongitude(any(), any(), anyInt());
        verify(eventRepository).findAllExternalEventIdByLatitudeLongitude(any(), any(), anyInt());

        assertNull(dataTableResponse.getData());
    }
}
