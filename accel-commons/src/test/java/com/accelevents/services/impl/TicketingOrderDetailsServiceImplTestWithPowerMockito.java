//TODO: fix test-case
/*
package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.AttributeKeyValueDto;
import com.accelevents.dto.TicketAttributeValueDto;
import com.accelevents.dto.ValueDto;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@PrepareForTest(JAXBContext.class)
public class TicketingOrderDetailsServiceImplTestWithPowerMockito {

    @Spy
    @InjectMocks
    private TicketingOrderDetailsServiceImpl ticketingOrderDetailsServiceImpl = new TicketingOrderDetailsServiceImpl();

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TicketingOrderService ticketingOrderService;

    private Event event;
    private User user;
	private TicketingOrder ticketingOrder;

	private Long id = 1L;
    private String firstName = "Jon";
	private String search = "search String";
    private int size = 10;
    private int page = 1;
    private Date orderDate = new Date("2019/03/01 05:30");
    private Map<TicketingOrder, List<EventTickets>> ticketingOrderListMap;

    @BeforeEach
    public void setUp() throws Exception {
        event = EventDataUtil.getEvent();
		EventTickets eventTickets = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        user = EventDataUtil.getUser();
		Ticketing ticketing = EventDataUtil.getTicketing(event);
		TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketingOrderListMap.put(ticketingOrder, Collections.singletonList(eventTickets));
    }

    */
/*@Test
    public void test_getOrderData__throwJAXBException() throws JAXBException{

        //setup
        PowerMockito.mockStatic(JAXBContext.class);
        event.setCurrency(Currency.USD);
        event.setTimezoneId("India Standard Time (IST)");

        attendeeDtoV2 = new AttendeeDtoV2();
        attendeeDtoV2.setFirstName(firstName);
        attendeeDtoV2.setLastName(lastName);

        List<AttendeeDtoV2> attendeeDtoV2List = new ArrayList<>();
        attendeeDtoV2List.add(attendeeDtoV2);

        orderDtoV2 = new OrderDtoV2();
        orderDtoV2.setId(id);
        orderDtoV2.setCurrency(String.valueOf(Currency.USD));
        orderDtoV2.setAttendee(attendeeDtoV2List);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketHolderAttributes.setValue(EventDataUtil.getJsonValue());

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        //mock
        doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET);
        when(eventTicketsCommonRepo.findByTicketingOrder(ticketingOrder.getId())).thenReturn(eventTicketsList);
        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(ticketingOrderListMap, any(),any(),any(), any(), ticketing.isRecurringEvent());
        //when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);
        doReturn(null).when(ticketingOrderDetailsServiceImpl).getUnmashler();
        doThrow(JAXBException.class).when(ticketingOrderDetailsServiceImpl).unmashler(any(),any());

        //Execution
        ticketingOrderDetailsServiceImpl.getOrderData(event, size, page, search, id, orderDate, DataType.TICKET);

    }

    @Test
    public void test_getUnmashler_throwJAXBException() throws JAXBException{

        PowerMockito.mockStatic(JAXBContext.class);

        when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);
        Unmarshaller unmarshaller = ticketingOrderDetailsServiceImpl.getUnmashler();
        assertNull(unmarshaller);
    }

    @Test
    public void test_unmashler_throwJAXBException() throws JAXBException{
        PowerMockito.mockStatic(JAXBContext.class);

        //setup
        String xml = EventDataUtil.getSampleXML();

        thrown.expect(JAXBException.class);
        thrown.expectMessage("unmarshaller not found!");

        //Execution
        ticketingOrderDetailsServiceImpl.unmashler(xml, null);
    }*//*


    private TicketAttributeValueDto getTicketAttributeValueDTO2() {

		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute1 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute1.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute1.setValue(firstName);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute1);

		ValueDto valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);

		TicketAttributeValueDto ticketAttributeValueDto2 = new TicketAttributeValueDto();
        ticketAttributeValueDto2.setPurchaser(valueDtoPurchaser);

        return ticketAttributeValueDto2;
    }

    private OrderDtoV2 getOrderDtoV2() {
		String email = "<EMAIL>";
		user.setEmail(email);

		PurchaserInfo purchaserInfo = new PurchaserInfo(user);

        event.setCurrency(Currency.USD);
        event.setTimezoneId("India Time");
        event.setEquivalentTimeZone("Asia/Calcutta");

		AttendeeDtoV2 attendeeDtoV2 = new AttendeeDtoV2();
        attendeeDtoV2.setFirstName(firstName);
		String lastName = "Kaz";
		attendeeDtoV2.setLastName(lastName);

        List<AttendeeDtoV2> attendeeDtoV2List = new ArrayList<>();
        attendeeDtoV2List.add(attendeeDtoV2);

		OrderDtoV2 orderDtoV2 = new OrderDtoV2();
        orderDtoV2.setId(id);
        orderDtoV2.setCurrency(String.valueOf(Currency.USD));
        orderDtoV2.setAttendee(attendeeDtoV2List);
        orderDtoV2.setPurchaser(purchaserInfo);

        return orderDtoV2;
    }

    private Page<TicketingOrder> getTicketingOrdersPages() {
        ticketingOrder.setId(id);

        List<TicketingOrder> ticketingOrderList = new ArrayList<>();
        ticketingOrderList.add(ticketingOrder);
        Page<TicketingOrder> ticketingOrdersPages = new PageImpl<>(ticketingOrderList);
        ticketingOrdersPages.getTotalElements();
        ticketingOrdersPages.getTotalPages();

        return ticketingOrdersPages;
    }
}*/
