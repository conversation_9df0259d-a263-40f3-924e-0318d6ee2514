package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Stripe;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.WhiteLabelTransfer;
import com.accelevents.domain.enums.Currency;
import com.accelevents.messages.TransferStatusEnum;
import com.accelevents.messages.WLTypeEnum;
import com.accelevents.repositories.StripeRepository;
import com.accelevents.repositories.WhiteLabelTransferRepository;
import com.accelevents.services.StripePaymentService;
import com.accelevents.services.TransactionFeeConditionalLogicService;
import com.stripe.exception.StripeException;
import com.stripe.model.Transfer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class WhiteLabelTransferServiceImplTest {

    @Spy
    @InjectMocks
    private WhiteLabelTransferServiceImpl whiteLabelTransferServiceImpl;

    @Mock
    private WhiteLabelTransferRepository whiteLabelTransferRepository;

    @Mock
    private StripeRepository stripeRepository;

    @Mock
    private Event event;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private StripePaymentService stripePaymentService;

    private long whiteLabelId = 1L;

	private WhiteLabel whiteLabel;

    private String whiteLabelStripeCustomerId = "XYZ_CDCDD";
    double amount = new Double(40);

    @BeforeEach
    void setUpData() {
        whiteLabel = new WhiteLabel(whiteLabelId,"testWhiteLabelUrl");

        event = new Event();
		long eventId = 11L;
		event.setEventId(eventId);
        event.setEventURL("testEvent");
        event.setWhiteLabel(whiteLabel);
        event.setCurrency(Currency.USD);
    }

    @Test
    void testSave(){
        WhiteLabelTransfer whiteLabelTransfer = new WhiteLabelTransfer();
        whiteLabelTransfer.setEvent(event);
        whiteLabelTransfer.setWhiteLabel(whiteLabel);
        whiteLabelTransfer.setStripeCustomerId(whiteLabelStripeCustomerId);
        whiteLabelTransfer.setAmount(new Double(11));

        whiteLabelTransferServiceImpl.save(whiteLabelTransfer);

        verify(whiteLabelTransferRepository, Mockito.times(1)).save(whiteLabelTransfer);
    }

    @Test
    void testStartWhiteLabelTransfer() throws StripeException {
        String API_KEY = "API_KEY";

        Stripe stripe = new Stripe();
        stripe.setDefaultAccount(true);
        stripe.setStripeUserId(whiteLabelStripeCustomerId);
        stripe.setAccessToken("XYVZ");
        stripe.setWhiteLabelId(whiteLabelId);
        stripe.setActivated(true);
        stripe.setActivatedOn(new Date());
        stripe.setAccountSource("External");
        stripe.setEvent(event);
        stripe.setStripePublishableKey("Publish_Lablesh");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("EVENT_URL",event.getEventURL());
        metadata.put("EVENT_NAME",event.getName());
        metadata.put("BUYER_NAME", null);
        metadata.put("WL_TYPE",WLTypeEnum.A);

        Transfer transfer = new Transfer();
        transfer.setId("TXDFDSFDS");
        transfer.setAmount(BigDecimal.valueOf(amount * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN).longValue());

        doReturn(Optional.of(stripe.getStripeUserId())).when(transactionFeeConditionalLogicService).getStripeUserId(event,WLTypeEnum.A);
        doReturn(transfer).when(stripePaymentService).createTransfer(amount,stripe.getStripeUserId(),event.getCurrency().getISOCode(),whiteLabel.getWhiteLabelUrl(),metadata);

        whiteLabelTransferServiceImpl.startWhiteLabelTransfer(22L, amount, new Date(), event, WLTypeEnum.A,
                Collections.emptyMap());

        ArgumentCaptor<WhiteLabelTransfer> captor = ArgumentCaptor.forClass(WhiteLabelTransfer.class);
        verify(whiteLabelTransferRepository, Mockito.times(2)).save(captor.capture());

        WhiteLabelTransfer whiteLabelTransfer = captor.getValue();
        assertTrue (whiteLabelTransfer.getStatus().equals(TransferStatusEnum.TRANSFERRED));

    }

}
