package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.StripeTransaction;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.repositories.StripeTransactionRepository;
import com.accelevents.services.PhoneNumberService;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StripeTransactionServiceImplTest {

    @Spy
    @InjectMocks
    private StripeTransactionServiceImpl stripeTransactionServiceImpl = new StripeTransactionServiceImpl();

    @Mock
    private StripeTransactionRepository stripeTransactionRepository;

    @Mock
    private PhoneNumberService phoneNumberService;

    private StripeTransaction stripeTransaction;
    private Event event;
    private User user;
    private StripeTransactionSource stripeTransactionSource;

	private String phoneNumber = "4844557575";
	private String stripeAccessToken = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private String productId = "prod_FVRZhck5T6nI9X";
    private String currency = "USD";
    private String note = "note";
	private double ccPercentageFee = CREDIT_CARD_PROCESSING_PERCENTAGE;
    private double ccFlatFee = CREDIT_CARD_PROCESSING_FLAT;

    @BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();

        user = EventDataUtil.getUser();

        stripeTransaction = new StripeTransaction();
		Long transactionId = 1L;
		stripeTransaction.setId(transactionId);
        stripeTransaction.setEvent(event);
        stripeTransaction.setSource(StripeTransactionSource.DONATION_ONLINE);
		double amount = 100d;
		stripeTransaction.setAmount(amount);
        stripeTransaction.setUser(user);
		String customerId = "cus_FSjuTT9VAXQn8x";
		stripeTransaction.setStripecustomerid(customerId);
		String cardType = "Visa";
		stripeTransaction.setCardType(cardType);
    }

    @Test
    void test_save_success() {

        //Execution
        stripeTransactionServiceImpl.save(stripeTransaction);

        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionRepository,times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction actual = stripeTransactionArgumentCaptor.getValue();
        assertEquals(actual.getId(), stripeTransaction.getId());
        assertEquals(actual.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(actual.getSource(), stripeTransaction.getSource());
        assertEquals(actual.getAmount(), stripeTransaction.getAmount());
        assertEquals(actual.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(actual.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(actual.getCardType(), stripeTransaction.getCardType());
    }

    @Test
    void test_findAllByEvent_success() {

        //setup
        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
        stripeTransactionList.add(stripeTransaction);

        //mock
        when(stripeTransactionRepository.findAllByEvent(event)).thenReturn(stripeTransactionList);

        //Execution
        List<StripeTransaction> stripeTransactionData = stripeTransactionServiceImpl.findAllByEvent(event);

        assertEquals(stripeTransactionData.iterator().next().getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.iterator().next().getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.iterator().next().getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.iterator().next().getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.iterator().next().getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.iterator().next().getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.iterator().next().getCardType(), stripeTransaction.getCardType());
    }

    @Test
    void test_updateUserByNewUser_success() {

        //setup
        User newUser = new User();
		String email = "<EMAIL>";
		newUser.setEmail(email);

        //mock
        Mockito.doNothing().when(stripeTransactionRepository).updateByNewUser(user, newUser);

        //Execution
        stripeTransactionServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_findBySourceAndSourceId_success() {

        //setup
        long chargeId = 1L;

        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
        stripeTransactionList.add(stripeTransaction);
        //mock
        when(stripeTransactionRepository.findBySourceAndSourceId(stripeTransactionSource, chargeId)).thenReturn(stripeTransactionList);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.findBySourceAndSourceId(stripeTransactionSource, chargeId);

        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
    }

//    @Test
//    void test_findBySourceAndSourceIdAndEvent_success() {
//
//        //setup
//        long sourceId = 1L;
//
//        //mock
//        when(stripeTransactionRepository.findBySourceAndSourceIdAndEvent(stripeTransactionSource, sourceId, event)).thenReturn(stripeTransaction);
//
//        //Execution
//        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.findBySourceAndSourceIdAndEvent(stripeTransactionSource, sourceId, event);
//
//        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
//        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
//        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
//        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
//        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
//        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
//        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
//    }

//    @Test
//    void test_getCustomerTransaction_success() {
//
//        //setup
//        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
//        stripeTransactionList.add(stripeTransaction);
//
//        //mock
//        when(stripeTransactionRepository.findAllByEvent(event)).thenReturn(stripeTransactionList);
//        when(phoneNumberService.getDisplayNumber(stripeTransaction.getUser())).thenReturn(phoneNumber);
//
//        //Execution
//        List<StripeTransactionDto> customerTransactionData = stripeTransactionServiceImpl.getCustomerTransaction(event);
//
//        assertEquals(customerTransactionData.iterator().next().getAmount(), stripeTransaction.getAmount());
//        assertEquals(customerTransactionData.iterator().next().getSource(), stripeTransaction.getSource().toString());
//        assertEquals(customerTransactionData.iterator().next().getStatus(), "SUCCESS");
//        assertEquals(customerTransactionData.iterator().next().getName(), stripeTransaction.getUser().getFirstName());
//        assertEquals(customerTransactionData.iterator().next().getPhoneNumber(), phoneNumber);
//    }

//    @Test
//    void test_getCustomerTransaction_success1() {
//
//        //setup
//        stripeTransaction.setSource(StripeTransactionSource.MODULE_ACTIVE);
//
//        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
//        stripeTransactionList.add(stripeTransaction);
//
//        //mock
//        when(stripeTransactionRepository.findAllByEvent(event)).thenReturn(stripeTransactionList);
//        when(phoneNumberService.getDisplayNumber(stripeTransaction.getUser())).thenReturn(phoneNumber);
//
//        //Execution
//        List<StripeTransactionDto> customerTransactionData = stripeTransactionServiceImpl.getCustomerTransaction(event);
//
//        assertTrue(customerTransactionData.size() == 0);
//    }

//    @Test
//    void test_getCustomerTransaction_success_with_stripeTransactionList_empty() {
//
//        //setup
//        stripeTransaction.setSource(StripeTransactionSource.MODULE_ACTIVE);
//
//        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
//
//        //mock
//        when(stripeTransactionRepository.findAllByEvent(event)).thenReturn(stripeTransactionList);
//        when(phoneNumberService.getDisplayNumber(stripeTransaction.getUser())).thenReturn(phoneNumber);
//
//        //Execution
//        List<StripeTransactionDto> customerTransactionData = stripeTransactionServiceImpl.getCustomerTransaction(event);
//
//        assertTrue(customerTransactionData.size() == 0);
//    }

    @Test
    void test_getTextToGiveSubscriptionId_success() {

        //mock
        when(stripeTransactionRepository.findBySourceAndTextToGiveSubscriptionIdIsNotNullAndEvent(StripeTransactionSource.MODULE_ACTIVE, event)).thenReturn(stripeTransaction);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.getTextToGiveSubscriptionId(event);

        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
    }

    @Test
    void test_getStripeTransactionForStripeWebHook_success() {

        //mock
		String subscriptionId = "sub_FVRS57a8mj0MJ7";
		when(stripeTransactionRepository.findBySourceAndTextToGiveSubscriptionId(stripeTransactionSource, subscriptionId)).thenReturn(stripeTransaction);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.getStripeTransactionForStripeWebHook(stripeTransactionSource, subscriptionId);

        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
    }

//    @Test
//    void test_findBySourceAndEvent_success() {
//
//        //setup
//        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
//        stripeTransactionList.add(stripeTransaction);
//
//        //mock
//        when(stripeTransactionRepository.findBySourceAndEvent(stripeTransactionSource, event)).thenReturn(stripeTransactionList);
//
//        //Execution
//        List<StripeTransaction> stripeTransactionData = stripeTransactionServiceImpl.findBySourceAndEvent(stripeTransactionSource, event);
//
//        assertEquals(stripeTransactionData.iterator().next().getId(), stripeTransaction.getId());
//        assertEquals(stripeTransactionData.iterator().next().getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
//        assertEquals(stripeTransactionData.iterator().next().getSource(), stripeTransaction.getSource());
//        assertEquals(stripeTransactionData.iterator().next().getAmount(), stripeTransaction.getAmount());
//        assertEquals(stripeTransactionData.iterator().next().getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
//        assertEquals(stripeTransactionData.iterator().next().getStripecustomerid(), stripeTransaction.getStripecustomerid());
//        assertEquals(stripeTransactionData.iterator().next().getCardType(), stripeTransaction.getCardType());
//    }

    @Test
    void test_findByUserAndEvent_success() {

        //setup
        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
        stripeTransactionList.add(stripeTransaction);

        //mock
        when(stripeTransactionRepository.findByUserAndEvent(user, event)).thenReturn(stripeTransactionList);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.findByUserAndEvent(user, event);

        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
    }

    @Test
    void test_findByUserAndEvent_success_with_stripeTransaction_empty() {

        //setup
        List<StripeTransaction> stripeTransactionList = new ArrayList<>();

        //mock
        when(stripeTransactionRepository.findByUserAndEvent(user, event)).thenReturn(stripeTransactionList);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.findByUserAndEvent(user, event);

        assertNull(stripeTransactionData);
    }

    @Test
    void test_getAllStripeTransactions_success_with_status_unpaid() {

        //setup
        Pageable pageable = PageRequest.of(1, 10);
        String status = Constants.UNPAID;
        String searchString = "Search String";

        Object[] objArray = {stripeTransaction};

        Page<Object[]> pageImpl = new PageImpl<Object[]>(Collections.singletonList(objArray));

        //mock
        when(stripeTransactionRepository.getAllPaidOrUnpaidStripeTransactionsToDisplaySearchedEvent(StringUtils.lowerCase(searchString),pageable)).thenReturn(pageImpl);

        //Execution
        Page<Object[]> allStripeTransactionsData = stripeTransactionServiceImpl.getAllStripeTransactions(status, pageable, searchString, null, null, null);

        assertTrue(allStripeTransactionsData.getContent().contains(objArray));
    }

    @Test
    void test_getAllStripeTransactions_success_with_status_paid() {

        //setup
        Pageable pageable = PageRequest.of(1, 10);
        String status = Constants.PAID;
        String searchString = "Search String";

        Object[] objArray = {stripeTransaction};

        Page<Object[]> pageImpl = new PageImpl<Object[]>(Collections.singletonList(objArray));

        //mock
        when(stripeTransactionRepository.getAllPaidOrUnpaidStripeTransactionsToDisplaySearchedEvent(StringUtils.lowerCase(searchString),pageable)).thenReturn(pageImpl);

        //Execution
        Page<Object[]> allStripeTransactionsData = stripeTransactionServiceImpl.getAllStripeTransactions(status, pageable, searchString, null,null,null);

        assertTrue(allStripeTransactionsData.getContent().contains(objArray));
    }

    @Test
    void test_geEventBillingDetailByEvent_success() {

        //mock
        when(stripeTransactionRepository.getUnpaidOrFailedStripeTransactions(event)).thenReturn(stripeTransaction);

        //Execution
        StripeTransaction stripeTransactionData = stripeTransactionServiceImpl.getEventBillingDetailByEvent(event);

        assertEquals(stripeTransactionData.getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.getCardType(), stripeTransaction.getCardType());
    }

    @Test
    void test_getAllUnpaidStripeTransactionsByEventId_success() {

        //setup
        List<Long> eventIds = new ArrayList<>();
        eventIds.add(event.getEventId());

        List<StripeTransaction> stripeTransactionList = new ArrayList<>();
        stripeTransactionList.add(stripeTransaction);

        //mock
        when(stripeTransactionRepository.getAllUnpaidStripeTransactionsByEventId(eventIds)).thenReturn(stripeTransactionList);

        //Execution
        List<StripeTransaction> stripeTransactionData = stripeTransactionServiceImpl.getAllUnpaidStripeTransactionsByEventId(eventIds);

        assertEquals(stripeTransactionData.iterator().next().getId(), stripeTransaction.getId());
        assertEquals(stripeTransactionData.iterator().next().getEvent().getEventId(), stripeTransaction.getEvent().getEventId());
        assertEquals(stripeTransactionData.iterator().next().getSource(), stripeTransaction.getSource());
        assertEquals(stripeTransactionData.iterator().next().getAmount(), stripeTransaction.getAmount());
        assertEquals(stripeTransactionData.iterator().next().getUser().getFirstName(), stripeTransaction.getUser().getFirstName());
        assertEquals(stripeTransactionData.iterator().next().getStripecustomerid(), stripeTransaction.getStripecustomerid());
        assertEquals(stripeTransactionData.iterator().next().getCardType(), stripeTransaction.getCardType());
    }
}