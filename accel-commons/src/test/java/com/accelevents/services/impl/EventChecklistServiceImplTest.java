package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.EventChecklist;
import com.accelevents.repositories.EventChecklistRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;

@ExtendWith(MockitoExtension.class)
public class EventChecklistServiceImplTest {

    @Spy
    @InjectMocks
    private EventChecklistServiceImpl eventChecklistServiceImpl;

    @Mock
    private EventChecklistRepository eventChecklistRepository;

    private Event event;
    private EventChecklist eventChecklist;

    @BeforeEach
    public void setUp() throws Exception {

        event = EventDataUtil.getEvent();

        eventChecklist = new EventChecklist();
        eventChecklist.setId(1L);
        eventChecklist.setEventId(event.getEventId());
        eventChecklist.setTextToDonate(true);
        eventChecklist.setAuctionBidSubmitted(true);
        eventChecklist.setCauseAuctionPledgeSubmitted(true);
        eventChecklist.setRaffleTicketSubmitted(true);
        eventChecklist.setCardLinked(true);
    }

    @Test
    public void test_findByEvent_success() {

        //mock
        Mockito.when(eventChecklistRepository.findEventChecklistByEventId(anyLong())).thenReturn(eventChecklist);

        //Execution
        EventChecklist eventCheckListData = eventChecklistServiceImpl.findByEvent(event);

        assertEquals(eventCheckListData.getEventId(), eventChecklist.getEventId());
        assertEquals(eventCheckListData.getId(), eventChecklist.getId());
        assertEquals(eventCheckListData.isTextToDonate(), eventChecklist.isTextToDonate());
        assertEquals(eventCheckListData.isAuctionBidSubmitted(), eventChecklist.isAuctionBidSubmitted());
        assertEquals(eventCheckListData.isCauseAuctionPledgeSubmitted(), eventChecklist.isCauseAuctionPledgeSubmitted());
        assertEquals(eventCheckListData.isRaffleTicketSubmitted(), eventChecklist.isRaffleTicketSubmitted());
        assertEquals(eventCheckListData.isCardLinked(), eventChecklist.isCardLinked());
    }

    @Test
    public void test_save_success() {

        //Execution
        eventChecklistServiceImpl.save(eventChecklist);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        Mockito.verify(eventChecklistRepository, Mockito.times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventCheckListData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventCheckListData.getEventId(), eventChecklist.getEventId());
        assertEquals(eventCheckListData.getId(), eventChecklist.getId());
        assertEquals(eventCheckListData.isTextToDonate(), eventChecklist.isTextToDonate());
        assertEquals(eventCheckListData.isAuctionBidSubmitted(), eventChecklist.isAuctionBidSubmitted());
        assertEquals(eventCheckListData.isCauseAuctionPledgeSubmitted(), eventChecklist.isCauseAuctionPledgeSubmitted());
        assertEquals(eventCheckListData.isRaffleTicketSubmitted(), eventChecklist.isRaffleTicketSubmitted());
        assertEquals(eventCheckListData.isCardLinked(), eventChecklist.isCardLinked());
    }
}