package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.EventChargebeePlanConfigDto;
import com.accelevents.billing.chargebee.dto.RegistrationConfigDto;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.AttendeeResponseContainer;
import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.common.dto.EventInfoDto;
import com.accelevents.common.dto.TicketTypeCountDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.ticketing.TicketTypeDto;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.perfomance.dto.TicketReportData;
import com.accelevents.perfomance.dto.TicketTypeReportData;
import com.accelevents.repositories.*;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.session_speakers.dto.RegisterdHolderUsers;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.UserSessionRepoService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.FeeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingDisplayServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingDisplayServiceImpl ticketingDisplayServiceImpl = new TicketingDisplayServiceImpl();

    @Mock
    private RecurringEventsRepository recurringEventsRepository;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private StripeService stripeService;

    @Mock
    private SeatsIoService seatsIoService;

    @Mock
    private TicketingRepository ticketingRepository;

    @Mock
    private WaitListSettingService waitListSettingService;

    @Mock
    private WaitListService waitListService;

    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingManageService  ticketingManageService;

    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;

    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private AllRequiresAttributesService allRequiresAttributesService;

    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Mock
    private ViewFilterDetailsService viewFilterDetailsService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private SalesTaxService salesTaxService;

    @Mock
    private TicketingCouponService ticketingCouponService;

    @Mock
    private SeatingCategoryService seatingCategoryService;

    @Mock
    private VirtualEventService virtualEventService;

    @Mock
    private TicketRequiresAttributesRepo ticketRequiresAttributesRepo;

    @Mock
    private EventPlanConfigService eventPlanConfigService;

    @Mock
    private BadgesService badgesService;

    @Mock
    private TicketingAccessCodeRepository ticketingAccessCodeRepository;

    @Mock
    private VatTaxService vatTaxService;

    @Mock
    private UserSessionRepoService userSessionRepoService;

    @Mock
    private SessionRepoService sessionRepoService;
    @Mock
    private HostEventOfflinePaymentConfigRepository hostEventOfflinePaymentConfigRepository;

    @Mock
    private TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;

    // RO Services for optimized read-only operations
    @Mock
    private com.accelevents.ro.eventTicket.ROTicketingTypeTicketService roTicketingTypeTicketService;
    @Mock
    private com.accelevents.ro.eventTicket.ROTicketingAccessCodeService roTicketingAccessCodeService;
    @Mock
    private com.accelevents.ro.eventTicket.ROTicketingLimitedDisplayCodeService roTicketingLimitedDisplayCodeService;
    @Mock
    private com.accelevents.ro.eventTicket.ROTicketingDisplayService roTicketingDisplayService;
    @Mock
    private com.accelevents.ro.eventTicket.repo.ROTicketingRepo roTicketingRepo;
    @Mock
    private com.accelevents.ro.waitlist.ROWaitListSettingService roWaitListSettingService;
    @Mock
    private com.accelevents.repository.RORecurringEventsRepository roRecurringEventsRepository;
    private Event event;
    private User user;
    private Ticketing ticketing;
    private EventTickets eventTickets;
    private TicketingOrder ticketingOrder;
    private TicketingType ticketingType1;
    private TicketingType ticketingTypeAddOn;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private StripeDTO stripeDTO;

    private Page<RegisterdHolderUsers> registerdHolderUsersPage;

    private Page<EventTickets> eventTicketsPage;

    private WaitListSettings waitListSettings;
    private TicketingAccessCode ticketingAccessCode;
    private WaitListDto waitListDto;
    private WaitList waitList;
    private TransactionFeeConditionalLogic transactionFeeConditionalLogic;
    private CreditCardChargesDto creditCardChargesDto;
    private PageSizeSearchObj pageSizeSearchObj;
    private TicketingTable ticketingTable;
    private AttendeeDto attendeeDto;
    private TicketTypeReportData ticketTypeReportData;
    private CustomAttribute customAttribute;
    private CustomAttributeNew customAttributeNew;
    private CustomAttributeCommon customAttributeCommon;
    private RecurringEvents recurringEventsOpt;
    private EventChargebeePlanConfigDto eventChargebeePlanConfigDto;
    private RegistrationConfigDto registrationConfigDto;

    private TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto;
    List<CustomAttributeCommon> customAttributeCommonList;
    private BadgesResponseData badgesResponseData;
    private List<Long> ids = new ArrayList<>();

    private Long id = 1L;
    private Long recurringEventId = 1L;
    private String firstName = "Jon";
    private String eventKey = "Event Key";
    private String accessCode = "Access Code";
    private static String startDate = "2019/01/01 05:30";
    private static String endDate = "2019/04/04 05:30";
    private static Date startDate1 = new Date(startDate);
    private static Date endDate1 = new Date(endDate);
    private int highestPosition = 1000;
    private  int numberOfTicketsInWaitList = 7;
    private long remainingTicket = 5;
    private     Long totalPaidTickets = 5L;
    private Long totalFreeTickets = 5L;
    private Long totalCheckedInTickets = 5L;
    private Long totalBookedTickets=5L;
    private SalesTaxFeeDto salesTaxFeeDto;
    private String eventLogo =  "b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg";
    private String eventLocation = "newYork";

    private Long sessionId = 1L;


    @BeforeEach
    public void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        eventTickets = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        ticketingType1 = EventDataUtil.getTicketingType(event);
        ticketingTypeAddOn = EventDataUtil.getTicketingType(event);
        ticketingTypeAddOn.setId(2L);
        ticketingTypeAddOn.setDataType(DataType.ADDON);
        RecurringEvents recurringEvents = EventDataUtil.getRecurringEvents();

        stripeDTO = new StripeDTO();
        double ccPercentageFee = 2.9d;
        stripeDTO.setCCPercentageFee(ccPercentageFee);
        double ccFlat = 0.3d;
        stripeDTO.setCCFlatFee(ccFlat);

        customAttribute = new CustomAttribute();
        customAttribute.setAtrributeName("Attribute Name");
        customAttribute.setAttribute(true);
        customAttribute.setAttributeType(AttributeValueType.TEXT);
        customAttribute.setDefaultValue("Default Value");
        customAttribute.setEnabledForTicketHolder(true);
        customAttribute.setEnabledForTicketPurchaser(true);
        customAttribute.setRequiredForTicketHolder(true);
        customAttribute.setRequiredForTicketPurchaser(true);
        customAttribute.setEventTicketTypeId("1");
        customAttribute.setHiddenForHolder(false);
        customAttribute.setHiddenForPurchaser(false);

        customAttributeCommon = new CustomAttributeCommon();
        customAttributeCommon.setAttributeName("Attribute Name");
        customAttributeCommon.setDefaultValueJsonPurchaser("Default Value");
        customAttributeCommon.setEventTicketTypeId("1");
        customAttributeCommon.setId(1L);
        customAttributeCommonList  = new ArrayList<>();
        customAttributeCommonList.add(customAttributeCommon);

        customAttributeNew = new CustomAttributeNew();
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        customAttributeNew.setAttribute(true);
        customAttributeNew.setAttributeType(AttributeValueType.TEXT);
        customAttributeNew.setEnabledForTicketHolder(true);
        customAttributeNew.setEnabledForTicketPurchaser(true);
        customAttributeNew.setRequiredForTicketHolder(true);
        customAttributeNew.setRequiredForTicketPurchaser(true);
        customAttributeNew.setHiddenForHolder(false);
        customAttributeNew.setHiddenForPurchaser(false);
        customAttributeNew.setDataType(DataType.TICKET);

        waitListDto = new WaitListDto();
        waitListDto.setCountryCode(CountryCode.US);
        String email = "<EMAIL>";
        waitListDto.setEmail(email);
        waitListDto.setFirstName(firstName);
        String lastName = "Kaz";
        waitListDto.setLastName(lastName);

        waitListSettings = new WaitListSettings();
        waitListSettings.setWaitListEnabled(true);
        waitListSettings.setWaitListTrigger(0L);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId("1");
        ticketingAccessCode.setId(id);
        ticketingAccessCode.setEventId(event);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName("Attribute Name");

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setId(id);

        creditCardChargesDto = new CreditCardChargesDto(ccFlat, ccPercentageFee);

        pageSizeSearchObj = new PageSizeSearchObj();
        pageSizeSearchObj.setPage(1);
        pageSizeSearchObj.setSearch("Search String");
        pageSizeSearchObj.setSize(10);

        recurringEventsOpt = new RecurringEvents();
        recurringEventsOpt.setId(id);
        recurringEventsOpt.setShowRemainingTicketsForRecurringEvent(RecurringEvents.ShowRemainingTicketsForRecurringEvent.TRUE);

        salesTaxFeeDto = new SalesTaxFeeDto(true,10d,"1");

        ticketTypeReportData=new TicketTypeReportData();

        eventChargebeePlanConfigDto =new EventChargebeePlanConfigDto();
        registrationConfigDto = new RegistrationConfigDto();

        registrationConfigDto.setMultipleChoiceQuestions("");
        eventChargebeePlanConfigDto.setRegistrationConfigDto(registrationConfigDto);

        badgesResponseData = new BadgesResponseData();
        badgesResponseData.setBadgeId(1L);
        ids.add(1L);
        ids.add(2L);
        badgesResponseData.setTicketingTypeIds(ids);

        ticketingTypeSettingStatusDto=new TicketingTypeSettingStatusDto();
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_waitListTrigger_0() {

        //setup
        waitList = new WaitList(waitListDto);

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());

        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);
        when(ticketAccessCode.getUses()).thenReturn(-1L);
        when(ticketAccessCode.getEventTicketTypeId()).thenReturn(String.valueOf(ticketingType1.getId()));
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(), any(), anyLong())).thenReturn(Optional.of(ticketAccessCode));
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, true, accessCode, recurringEventId,false);

        assertEquals(ticketData.getEventKey(), eventKey);
        assertEquals(ticketData.getSeatingChartKey(), ticketing.getChartKey());
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_ticketSoldCount_null() {

        //setup
        waitList = new WaitList(waitListDto);

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        TicketingType ticketingType1 = new TicketingType();
        ticketingType1.setId(0L);
        ticketingType1.setTicketTypeName("General Admission");
        ticketingType1.setMaxTicketsPerBuyer(1L);
        ticketingType1.setMinTicketsPerBuyer(1L);
        ticketingType1.setNumberOfTickets(10);
        ticketingType1.setBundleType(TicketBundleType.TABLE);
        ticketingType1.setPosition(1000d);
        ticketingType1.setTicketTypeDescription("Ticket type description");
        ticketingType1.setTicketType(TicketType.PAID);
        ticketingType1.setPassfeetobuyer(true);
        ticketingType1.setNumberOfTicketPerTable(1);
        ticketingType1.setEndDate(endDate1);
        ticketingType1.setStartDate(startDate1);
        ticketingType1.setPrice(10D);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ZERO);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        ticketingAccessCode.setEventTicketTypeId("");
        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        recurringEventsOpt.setEventCapacity(Double.valueOf("10"));

        //mock
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(null);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());

        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);
        when(ticketAccessCode.getUses()).thenReturn(-1L);
        when(ticketAccessCode.getEventTicketTypeId()).thenReturn(String.valueOf(ticketingType1.getId()));
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(), any(), anyLong())).thenReturn(Optional.of(ticketAccessCode));
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, true, accessCode, recurringEventId,false);
        assertFalse(ticketData.isEventCapacityReach());
        assertEquals(ticketData.getEventKey(), eventKey);
        assertEquals(ticketData.getSeatingChartKey(), ticketing.getChartKey());
    }

    public static Object[] getRecurringEventIds(){
        return new Object[]{
                new Object[]{1L} ,
                new Object[]{0L} ,
        };
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_waitListSettings_empty() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        waitListSettings.setWaitListEnabled(false);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(true);
        ticketingType1.setNumberOfTickets(0L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);

        when(roTicketingTypeTicketService.getTicketingTypesDisplay(any(), anyBoolean(), any(), any())).thenReturn(ticketingTypeList);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);


        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);
        when(ticketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(event.getEventId(), accessCode, recurringEventId)).thenReturn(new ArrayList<>());
        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertEquals(ticketData.getEventKey(), eventKey);
        assertEquals(ticketData.getSeatingChartKey(), ticketing.getChartKey());
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_listOfRecurringEventsHavingEventCapacity_Not_Reach() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        waitListSettings.setWaitListEnabled(false);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(true);
        ticketingType1.setNumberOfTickets(0L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(10.0);

        recurringEventId = 0L;

        //mock
        when(ticketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(event.getEventId(), accessCode, recurringEventId)).thenReturn(new ArrayList<>());
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);
        when(roTicketingTypeTicketService.getTicketingTypesDisplay(any(), anyBoolean(), any(), any())).thenReturn(ticketingTypeList);
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);
        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);
        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertFalse(ticketData.isEventCapacityReach());
        assertEquals(ticketData.getEventKey(), eventKey);
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_listOfRecurringEventsHavingEventCapacity_Reach() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        waitListSettings.setWaitListEnabled(false);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(true);
        ticketingType1.setNumberOfTickets(0L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(1.0);

        recurringEventId = 0L;

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);

        when(roTicketingTypeTicketService.getTicketingTypesDisplay(any(), anyBoolean(), any(), any())).thenReturn(ticketingTypeList);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);


        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(ticketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(event.getEventId(), accessCode, recurringEventId)).thenReturn(new ArrayList<>());
        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);





        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertTrue(ticketData.isEventCapacityReach());
        assertEquals(ticketData.getEventKey(), eventKey);
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_NonRecurringEventsEventCapacity_Not_Present() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        waitListSettings.setWaitListEnabled(false);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(true);
        ticketingType1.setNumberOfTickets(0L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        recurringEventId = 0L;

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);

        when(roTicketingTypeTicketService.getTicketingTypesDisplay(any(), anyBoolean(), any(), any())).thenReturn(ticketingTypeList);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);


        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(ticketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(event.getEventId(), accessCode, recurringEventId)).thenReturn(new ArrayList<>());
        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);





        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertFalse(ticketData.isEventCapacityReach());
        assertEquals(ticketData.getEventKey(), eventKey);
    }


    @Test
    public void test_getDisplayPageSettingData_success_with_enableTicketDescription_false() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "accessCode";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(false);
        ticketingType1.setNumberOfTickets(0L);
        ticketingType1.setId(1L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(null);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(),any(), anyLong())).thenReturn(Optional.empty());

        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);
        when(ticketAccessCode.getUses()).thenReturn(-1L);
        when(ticketAccessCode.getEventTicketTypeId()).thenReturn(String.valueOf(ticketingType1.getId()));
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(), any(), anyLong())).thenReturn(Optional.of(ticketAccessCode));
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertEquals(ticketData.getEventKey(), eventKey);
        assertEquals(ticketData.getSeatingChartKey(), ticketing.getChartKey());
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_enableTicketDescription_false_TicketingAccessCodeCount() {

        //setup
        List<WaitList> waitListList = new ArrayList<>();

        String accessCode = "accessCode";

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        ticketingType1.setPrice(0d);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType1.setEnableTicketDescription(false);
        ticketingType1.setNumberOfTickets(0L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(ticketingAccessCodeService.countByEventOrRecurringEvent(event,recurringEventId)).thenReturn(1L);
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(null);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(),any(), anyLong())).thenReturn(Optional.empty());

        TicketingAccessCode ticketAccessCode = mock(TicketingAccessCode.class);
        when(ticketAccessCode.getUses()).thenReturn(-1L);
        when(ticketAccessCode.getEventTicketTypeId()).thenReturn(String.valueOf(ticketingType1.getId()));
        when(roTicketingAccessCodeService.getByCodeAndCheckEndDate(anyString(), any(), anyLong())).thenReturn(Optional.of(ticketAccessCode));
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);

        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, false, accessCode, recurringEventId,false);

        assertEquals(ticketData.getEventKey(), eventKey);
        assertEquals(ticketData.getSeatingChartKey(), ticketing.getChartKey());
        assertTrue(ticketData.isAvailableAccessCode());
    }

    @Test
    public void test_getDisplayPageSettingData_success_with_waitListTrigger_greaterThan_0() {

        //setup
        waitList = new WaitList(waitListDto);

        List<WaitList> waitListList = new ArrayList<>();
        waitListList.add(waitList);

        ticketingType1.setPassfeetobuyer(false);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L,BigDecimal.ONE);

        waitListSettings.setWaitListTrigger(1L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(roRecurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(seatsIoService.getEventKey(event.getEventId(), recurringEventId)).thenReturn(eventKey);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(roWaitListSettingService.getWaitListSettingsByEventId(event.getEventId())).thenReturn(waitListSettings);

        when(roTicketingTypeTicketService.getTicketingTypesDisplay(event, false, recurringEventId, ticketing)).thenReturn(ticketingTypeList);

        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);
        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        when(ticketingCouponService.countByEventOrRecurringEvent(event, recurringEventId)).thenReturn(1L);
        when(seatingCategoryService.getCategoriesByEventWithDataType(event)).thenReturn(new ArrayList<EventCategoryDto>());
        when(ticketingManageService.parseJsonToObject(recurringEventsOpt.getRecurringJson())).thenReturn(ticketingTypeSettingStatusDto);
        when(ticketingLimitedDisplayCodeService.getTicketTypeIdsByEventIdAndLimitedDisplayCode(anyLong(), anyString(), anyLong())).thenReturn(new ArrayList<>());
        //Execution
        TicketDisplayPageDto ticketData = ticketingDisplayServiceImpl.getDisplayPageSettingData(event, true, "", recurringEventId,false);

        assertEquals(ticketData.getAddress(), ticketing.getEventAddress());
        assertEquals(ticketData.getTicketTypes().iterator().next().getTicketType(), ticketingType1.getTicketType());
    }

    @Test
    public void test_getTicketFeesLogic_success_with_whiteLabel() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();

        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(id);

        event.setWhiteLabel(whiteLabel);

        //mock
        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        //Execution
        List<TicketingFeeDto> ticketFeesLogic = ticketingDisplayServiceImpl.getTicketFeesLogic(event);
        for (TicketingFeeDto ticketFeesLogicData : ticketFeesLogic) {
            assertEquals(FeeConstants.WL_FEE_FLAT, ticketFeesLogicData.getWlFeeFlat());
            assertEquals(FeeConstants.WL_FEE_PERCENTAGE, ticketFeesLogicData.getWlFeePercentage());
            assertEquals(FeeConstants.AE_FLAT_FEE_ONE, ticketFeesLogicData.getAeFeeFlat());
        }
    }

    @Test
    public void test_getTicketFeesLogic_success() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();

        //mock
        when(transactionFeeConditionalLogicService.getRecordByEvent(event)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        //Execution
        List<TicketingFeeDto> ticketFeesLogic = ticketingDisplayServiceImpl.getTicketFeesLogic(event);
        for (TicketingFeeDto ticketFeesLogicData : ticketFeesLogic) {
            assertEquals(0, ticketFeesLogicData.getWlFeeFlat());
            assertEquals(0, ticketFeesLogicData.getWlFeePercentage());
            assertEquals(FeeConstants.AE_FLAT_FEE_ONE, ticketFeesLogicData.getAeFeeFlat());
        }
    }

    @Test
    public void test_getNumberOfTicketsInWaitList_success_with_numberOfTicketsInWaitList_greaterThan_remainingTicket() {

        //Execution
        int numberOfTicketsInWaitListData = ticketingDisplayServiceImpl.getNumberOfTicketsInWaitList(numberOfTicketsInWaitList, remainingTicket);
        assertEquals(numberOfTicketsInWaitList - remainingTicket, numberOfTicketsInWaitListData);
    }

    @Test
    public void test_getNumberOfTicketsInWaitList_success_with_numberOfTicketsInWaitList_lessThan_remainingTicket() {

        //setup
        long remainingTicket = 9;

        //Execution
        int numberOfTicketsInWaitListData = ticketingDisplayServiceImpl.getNumberOfTicketsInWaitList(numberOfTicketsInWaitList, remainingTicket);
        assertEquals(0, numberOfTicketsInWaitListData);
    }

    @Test
    public void test_getRemainingTicket_success_with_numberOfTicketsInWaitList_lessThan_remainingTicket() {

        //setup
        long remainingTicket = 9;

        //Execution
        long remainingTickettData = ticketingDisplayServiceImpl.getRemainingTicket(numberOfTicketsInWaitList, remainingTicket);
        assertEquals(remainingTicket - numberOfTicketsInWaitList, remainingTickettData);
    }

    @Test
    public void test_getRemainingTicket_success_with_numberOfTicketsInWaitList_greaterThan_remainingTicket() {

        //Execution
        long remainingTickettData = ticketingDisplayServiceImpl.getRemainingTicket(numberOfTicketsInWaitList, remainingTicket);
        assertEquals(0, remainingTickettData);
    }

    @Test
    public void test_checkAvalableAccessCode_success_with_ticketingAccessCode() {

        //setup
        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();
        ticketingAccessCodelist.add(ticketingAccessCode);

        //mock
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(ticketingAccessCodelist);

        //Execution
        boolean availableAccessCode = ticketingDisplayServiceImpl.checkAvalableAccessCode(event);
        assertTrue(availableAccessCode);
    }

    @Test
    public void test_checkAvalableAccessCode_success() {

        //setup
        List<TicketingAccessCode> ticketingAccessCodelist = new ArrayList<>();

        //mock
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(ticketingAccessCodelist);

        //Execution
        boolean availableAccessCode = ticketingDisplayServiceImpl.checkAvalableAccessCode(event);
        assertFalse(availableAccessCode);
    }

    @Test
    public void test_getTicketType_success() {

        //setup
        Long recurringEventId = 0L;

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> seatingCategoriesData = ticketingDisplayServiceImpl.getTicketType(event, recurringEventId);
        for (TicketingType actualData : seatingCategoriesData) {
            assertEquals(actualData.getPrice(), ticketingType1.getPrice());
        }
    }

    @Test
    public void test_getSeatingCategories_success() {

        //setup
        Long createdFrom = 1L;
        String categories = "1";
        ticketingType1.setCreatedFrom(createdFrom);
        //ticketingType1.setCategories(categories);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);
        ticketingTypeList.add(ticketingType1);
        List<TicketingTypeIdAndCategoryDTO> ticketingTypeIdAndCategoryDTOS = List.of(new TicketingTypeIdAndCategoryDTO(ticketingType1.getId(), ticketingType1.getCategoryId()));
        //mock

        when(ticketingTypeService.findTicketingTypeIdAndCategoryIdByEventAndDataType(event)).thenReturn(ticketingTypeIdAndCategoryDTOS);

        //Execution
        List<CategoryDto> seatingCategoriesData = ticketingDisplayServiceImpl.getSeatingCategories(event, recurringEventId);
        for (CategoryDto actualData : seatingCategoriesData) {
            assertEquals(actualData.getCategory().toString(), categories);
        }
    }

    @Test
    public void test_getSeatingCategories_successAndAccessCodeTicketTypeIdsListWithEmptyString() {

        //setup
        Long createdFrom = 1L;
        ticketingType1.setCreatedFrom(createdFrom);
        ticketingType1.setCategoryId(1L);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);
        List<String> accessCodeTicketTypeIds=new ArrayList<>();
        accessCodeTicketTypeIds.add("");
        List<TicketingTypeIdAndCategoryDTO> ticketingTypeIdAndCategoryDTOS = List.of(new TicketingTypeIdAndCategoryDTO(ticketingType1.getId(), ticketingType1.getCategoryId()));
        //mock
        when(ticketingAccessCodeService.findTicketTypeIdsByEventForAccessCode(any(),anyLong())).thenReturn(accessCodeTicketTypeIds);
        when(ticketingTypeTicketService.getAllTicketingTypesByRecurringEventBasedOnAllowSeating(anyLong(),anyList())).thenReturn(ticketingTypeList);
        when(ticketingTypeService.findTicketingTypeIdAndCategoryIdByEventAndDataType(any())).thenReturn(ticketingTypeIdAndCategoryDTOS);
        //Execution
        List<CategoryDto> seatingCategoriesData = ticketingDisplayServiceImpl.getSeatingCategories(event, recurringEventId);

        //assert
        assertEquals(1L, (long) seatingCategoriesData.get(0).getCategory());

        verify(ticketingAccessCodeService).findTicketTypeIdsByEventForAccessCode(any(),anyLong());
        verify(ticketingTypeTicketService).getAllTicketingTypesByRecurringEventBasedOnAllowSeating(anyLong(),anyList());
        verify(ticketingTypeService).findTicketingTypeIdAndCategoryIdByEventAndDataType(any());
    }

    @Test
    public void test_getSeatingCategories_successAndAccessCodeTicketTypeIds() {

        //setup
        Long createdFrom = 1L;
        ticketingType1.setCreatedFrom(createdFrom);
        ticketingType1.setCategoryId(1L);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);
        List<String> accessCodeTicketTypeIds=new ArrayList<>();
        accessCodeTicketTypeIds.add("1,2,3");
        List<TicketingTypeIdAndCategoryDTO> ticketingTypeIdAndCategoryDTOS = List.of(new TicketingTypeIdAndCategoryDTO(ticketingType1.getId(), ticketingType1.getCategoryId()));

        //mock
        when(ticketingAccessCodeService.findTicketTypeIdsByEventForAccessCode(any(),anyLong())).thenReturn(accessCodeTicketTypeIds);
        when(ticketingTypeTicketService.getAllTicketingTypesByRecurringEventBasedOnAllowSeating(anyLong(),anyList())).thenReturn(ticketingTypeList);
        when(ticketingTypeService.findTicketingTypeIdAndCategoryIdByEventAndDataType(any())).thenReturn(ticketingTypeIdAndCategoryDTOS);

        //Execution
        List<CategoryDto> seatingCategoriesData = ticketingDisplayServiceImpl.getSeatingCategories(event, recurringEventId);

        //assert
        assertEquals(1L, (long) seatingCategoriesData.get(0).getCategory());

        verify(ticketingAccessCodeService).findTicketTypeIdsByEventForAccessCode(any(),anyLong());
        verify(ticketingTypeTicketService).getAllTicketingTypesByRecurringEventBasedOnAllowSeating(anyLong(),anyList());
        verify(ticketingTypeService).findTicketingTypeIdAndCategoryIdByEventAndDataType(any());
    }

    @Test
    public void test_getCategoryDto_success_with_categories_empty() {

        //setup
        Long createdFrom = 1L;
        String categories = "";
        Long recurringEventId = 0L;
        ticketingType1.setCreatedFrom(createdFrom);
        //ticketingType1.setCategories(categories);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        List<TicketingTypeIdAndCategoryDTO> ticketingTypeIdAndCategoryDTOS = List.of(new TicketingTypeIdAndCategoryDTO(ticketingType1.getId(), ticketingType1.getCategoryId()));
        ticketingTypeList.add(ticketingType1);


        //mock

        when(ticketingTypeService.findTicketingTypeIdAndCategoryIdByEventAndDataType(event)).thenReturn(ticketingTypeIdAndCategoryDTOS);
        Mockito.doReturn(ticketingTypeList).when(ticketingDisplayServiceImpl).getTicketType(any(), any());

        //Execution
        List<CategoryDto> seatingCategoriesData = ticketingDisplayServiceImpl.getCategoryDto(event, recurringEventId);

        assertTrue(seatingCategoriesData.isEmpty());
    }

    @Test
    public void test_setPurchaserInTicketingOrder_success() {

        //setup
        ticketingOrder.setPurchaser(null);

        //mock
        when(ticketingOrderService.findByid(id)).thenReturn(ticketingOrder);

        //Execution
        ticketingDisplayServiceImpl.setPurchaserInTicketingOrder(user, id, event);

        ArgumentCaptor<TicketingOrder> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingOrder actualData = ticketingArgumentCaptor.getValue();

        assertEquals(actualData.getPurchaser().getUserId(), user.getUserId());
    }

    @Test
    public void test_setPurchaserInTicketingOrder_success_with_purchaser() {

        //setup
        ticketingOrder.setPurchaser(user);

        //mock
        when(ticketingOrderService.findByid(id)).thenReturn(ticketingOrder);

        //Execution
        ticketingDisplayServiceImpl.setPurchaserInTicketingOrder(user, id, event);
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus_success_with_pageSizeSearchObj_getSearch(Long recurringEventId) {

        //setup
        List<String> ticketStatus = new ArrayList<>();

        List<TicketTypeCountDto> ticketTypeCountDtos = new ArrayList<>();

        List<Long> ticketTypeIds = new ArrayList<>();

        List<TicketType> ticketTypesList = new ArrayList<>();

        List<Long> ticketTypeIdsList = new ArrayList<>();

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingService.isRecurringEvent(any())).thenReturn(true);



        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(event, ticketTypesList, pageSizeSearchObj, ticketStatus, recurringEventId, ticketTypeIdsList, DataType.TICKET, true,false, false);
        //Assertion
        assertNotNull(allAttendeesContainerData);
    }


    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus_success_with_ticketStatus_and_ticketTypesList_and_ticketTypeIdsStringList() {

        //setup
        List<String> ticketStatus = new ArrayList<>();
        ticketStatus.add(TicketStatus.CANCELED.name());
        ticketStatus.add(TicketStatus.REGISTERED.name());
        ticketStatus.add(TicketStatus.CHECKED_IN.name());

        List<TicketType> ticketTypesList = new ArrayList<>();
        ticketTypesList.add(TicketType.PAID);
        ticketTypesList.add(TicketType.FREE);

        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);

        //mock
        when(eventCommonRepoService.findByEventIdAndTypeAndStatusAndSearchStr(any(), any(),any(), any(), any(), any(),anyList(),any())).thenReturn(eventTicketspage);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(event, ticketTypesList, pageSizeSearchObj, ticketStatus, recurringEventId,ticketTypeIdsList, DataType.TICKET, true,false, false);
        assertNotNull(allAttendeesContainerData);

    }


    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus_success_with_ticketStatus_and_ticketTypesList_and_ticketTypeIdsStringList_withSession_Checkin_StatusTrue() {

        //setup
        List<String> ticketStatus = new ArrayList<>();
        ticketStatus.add(TicketStatus.CANCELED.name());
        ticketStatus.add(TicketStatus.REGISTERED.name());
        ticketStatus.add(TicketStatus.CHECKED_IN.name());

        List<TicketType> ticketTypesList = new ArrayList<>();
        ticketTypesList.add(TicketType.PAID);
        ticketTypesList.add(TicketType.FREE);

        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);
        List<Object[]> userSessionList = new ArrayList<>();

        //mock
        when(eventCommonRepoService.findByEventIdAndTypeAndStatusAndSearchStr(any(), any(),any(), any(), any(), any(),anyList(),any())).thenReturn(eventTicketspage);
        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(event, ticketTypesList, pageSizeSearchObj, ticketStatus, recurringEventId,ticketTypeIdsList, DataType.TICKET, true,false, true);
        assertNotNull(allAttendeesContainerData);

    }

    //TODO : Junit5 review test
    /*@ParameterizedTest
    @MethodSource("getRecurringEventId")
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus_success_and_ticketTypeIdsStringListEmpty(Long recurringEventId) {

        //setup
        List<String> ticketStatus = new ArrayList<>();

        List<TicketType> ticketTypesList = new ArrayList<>();

        List<Long> ticketTypeIdsList = Collections.EMPTY_LIST;
        List<Long> ticketTypeIds = new ArrayList<>();

        pageSizeSearchObj.setSearch("");

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        attendeeDto = new AttendeeDto();
        attendeeDto.setFirstName(eventTickets.getHolderFirstName());
        attendeeDto.setLastName(eventTickets.getHolderLastName());
        attendeeDto.setEventTicketingId(eventTickets.getId());
        attendeeDto.setPaid(eventTickets.getPaidAmount());
        attendeeDto.setTicketPrice(eventTickets.getTicketPrice());
        attendeeDto.setRefundedAmount(eventTickets.getRefundedAmount());
        attendeeDto.setQty(1);
        attendeeDto.setSeatNumber(eventTickets.getSeatNumber());
        attendeeDto.setStatus(eventTickets.getTicketStatus().getStatus());
        attendeeDto.setTicketStatus(eventTickets.getStatus());
        attendeeDto.setBarcode(eventTickets.getBarcodeId());

        List<AttendeeDto> attendeeDtoList = new ArrayList<>();
        attendeeDtoList.add(attendeeDto);

        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingTypeService.findAllIdByEventId(any())).thenReturn(ticketTypeIds);

        when(ticketingTypeTicketService.findAllIdByEventIdAndRecurringEvent(any(), any())).thenReturn(ticketTypeIds);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatus(any(), anyList(),any(), any(), any(), any())).thenReturn(eventTicketsList);


        doReturn(attendeeDtoList).when(ticketingDisplayServiceImpl).getAllTicketAttendee(eventTicketspage.getContent());

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(event, null, pageSizeSearchObj, null, recurringEventId,ticketTypeIdsList, DataType.TICKET, true,false, false);
        assertNotNull(allAttendeesContainerData);
    }*/

    //TODO : Junit5 review test
    /*@ParameterizedTest
    @MethodSource("getRecurringEventId")
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus_success_and_ticketTypeIdsStringList_Null(Long recurringEventId) {

        //setup
        List<String> ticketStatus = new ArrayList<>();
        List<Long> ticketTypeIds = new ArrayList<>();
        List<TicketType> ticketTypesList = new ArrayList<>();

        pageSizeSearchObj.setSearch("");
        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        attendeeDto = new AttendeeDto();
        attendeeDto.setFirstName(eventTickets.getHolderFirstName());
        attendeeDto.setLastName(eventTickets.getHolderLastName());
        attendeeDto.setEventTicketingId(eventTickets.getId());
        attendeeDto.setPaid(eventTickets.getPaidAmount());
        attendeeDto.setTicketPrice(eventTickets.getTicketPrice());
        attendeeDto.setRefundedAmount(eventTickets.getRefundedAmount());
        attendeeDto.setQty(1);
        attendeeDto.setSeatNumber(eventTickets.getSeatNumber());
        attendeeDto.setStatus(eventTickets.getTicketStatus().getStatus());
        attendeeDto.setTicketStatus(eventTickets.getStatus());
        attendeeDto.setBarcode(eventTickets.getBarcodeId());
        List<AttendeeDto> attendeeDtoList = new ArrayList<>();
        attendeeDtoList.add(attendeeDto);
        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingTypeService.findAllIdByEventId(any())).thenReturn(ticketTypeIds);
        when(ticketingTypeTicketService.findAllIdByEventIdAndRecurringEvent(any(), any())).thenReturn(ticketTypeIds);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatus(any(), anyList(), any(), any(), any(), any())).thenReturn(eventTicketsList);

        Mockito.doReturn(attendeeDtoList).when(ticketingDisplayServiceImpl).getAllTicketAttendee(eventTicketspage.getContent());

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatus(event, null, pageSizeSearchObj, null, recurringEventId, null, DataType.TICKET, true,false, false);
        //Assertion
        assertNotNull(allAttendeesContainerData);
    }*/

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_with_CheckInStatus_and_and_ticketTypeIdsStringList() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        List<EnumUserSessionStatus> checkInStatus = new ArrayList<>(List.of(EnumUserSessionStatus.CHECK_IN_AVAILABLE));

        //mock
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(any(),any(),any(),any(),any(),any(),anyString())).thenReturn(registerdHolderUsersPage);
        when(eventTicketsCommonRepo.findByIds(any())).thenReturn(eventTicketsList);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, checkInStatus, pageSizeSearchObj, recurringEventId, ticketTypeIdsList, sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_with_RegisteredStatus_and_and_ticketTypeIdsStringList() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        List<EnumUserSessionStatus> checkInStatus = new ArrayList<>(List.of(EnumUserSessionStatus.REGISTERED));

        //mock
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(any(),any(),any(),any(),any(),any(),anyString())).thenReturn(registerdHolderUsersPage);
        when(eventTicketsCommonRepo.findByIds(any())).thenReturn(eventTicketsList);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, checkInStatus, pageSizeSearchObj, recurringEventId, ticketTypeIdsList, sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_without_CheckInStatus_and_and_ticketTypeIdsStringList() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        eventTicketsPage = new PageImpl<>(eventTicketsList);

        //mock
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(any(),any(),any(),any(),any(),any(),anyString())).thenReturn(registerdHolderUsersPage);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatusAndSearchStr(any(),any(),any(),anyList(),any(),anyLong(),any(),any())).thenReturn(eventTicketsPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, new ArrayList<>(), pageSizeSearchObj, recurringEventId, ticketTypeIdsList, sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_without_CheckInStatus_and_and_wihtout_ticketTypeIdsStringList() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        eventTicketsPage = new PageImpl<>(eventTicketsList);

        //mock

        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(any(),any(),any(),any(),any(),any(),anyString())).thenReturn(registerdHolderUsersPage);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatusAndSearchStr(any(),any(),any(),anyList(),any(),anyLong(),any(),any())).thenReturn(eventTicketsPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, new ArrayList<>(), pageSizeSearchObj, recurringEventId, new ArrayList<>(), sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_recurringEvent_and_recurringIdZero() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        eventTicketsPage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);
        when(ticketingTypeTicketService.getTicketTypeIdsByCreatedFroms(any())).thenReturn(ticketTypeIdsList);
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPaginationAndSearch(any(),any(),any(),any(),any(),any(),anyString())).thenReturn(registerdHolderUsersPage);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatusAndSearchStr(any(),any(),any(),anyList(),any(),anyLong(),any(),any())).thenReturn(eventTicketsPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);
        when(sessionRepoService.isSessionPrivate(any())).thenReturn(false);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, new ArrayList<>(), pageSizeSearchObj, 0L, ticketTypeIdsList, sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_without_filter() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        pageSizeSearchObj.setPage(0);
        pageSizeSearchObj.setSize(10);
        pageSizeSearchObj.setSearch("");

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        eventTicketsPage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingTypeService.findAllTypeIdByEvent(any(), any())).thenReturn(ticketTypeIdsList);
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPagination(any(),any(),any(),any(),any(),any())).thenReturn(registerdHolderUsersPage);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatus(any(),any(),any(),anyList(),any(),anyLong(),any())).thenReturn(eventTicketsPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, new ArrayList<>(), pageSizeSearchObj, 0L, new ArrayList<>(), sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_with_CheckinStatus() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        pageSizeSearchObj.setPage(0);
        pageSizeSearchObj.setSize(10);
        pageSizeSearchObj.setSearch("");

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EnumUserSessionStatus> checkInStatus = new ArrayList<>(List.of(EnumUserSessionStatus.CHECK_IN_AVAILABLE));


        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        //mock
        when(ticketingTypeService.findAllTypeIdByEvent(any(), any())).thenReturn(ticketTypeIdsList);
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPagination(any(),any(),any(),any(),any(),any())).thenReturn(registerdHolderUsersPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);


        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event,checkInStatus , pageSizeSearchObj, 0L, new ArrayList<>(), sessionId);
        assertEquals(0,allAttendeesContainerData.getRecordsTotal());
        assertEquals(0,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_with_RegisteredStatus() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        pageSizeSearchObj.setPage(0);
        pageSizeSearchObj.setSize(10);
        pageSizeSearchObj.setSearch("");

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EnumUserSessionStatus> checkInStatus = new ArrayList<>(Arrays.asList(EnumUserSessionStatus.REGISTERED));


        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);


        //mock
        when(ticketingTypeService.findAllTypeIdByEvent(any(), any())).thenReturn(ticketTypeIdsList);
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPagination(any(),any(),any(),any(),any(),any())).thenReturn(registerdHolderUsersPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);


        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event,checkInStatus , pageSizeSearchObj, 0L, new ArrayList<>(), sessionId);
        assertEquals(0,allAttendeesContainerData.getRecordsTotal());
        assertEquals(0,allAttendeesContainerData.getRecordsFiltered());
    }

    @Test
    public void test_getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId_success_without_filter_recurringEvent() {

        //setup
        List<Long> ticketTypeIdsList = new ArrayList<>();
        ticketTypeIdsList.add(1L);
        ticketTypeIdsList.add(2L);

        ticketingTable = new TicketingTable();
        ticketingTable.setId(id);

        pageSizeSearchObj.setPage(0);
        pageSizeSearchObj.setSize(10);
        pageSizeSearchObj.setSearch("");

        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderUserId(user);
        eventTickets.setUserId(user.getUserId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RegisterdHolderUsers> registerdHolderUsersList = new ArrayList<>();
        RegisterdHolderUsers registerdHolderUsers = new RegisterdHolderUsers();
        registerdHolderUsers.setEventTicketId(eventTickets.getId());
        registerdHolderUsers.setAttended(true);
        registerdHolderUsers.setFirstName("Jon");
        registerdHolderUsers.setEmail("<EMAIL>");
        registerdHolderUsers.setUserId(1L);
        registerdHolderUsers.setTicketTypeId(1L);
        registerdHolderUsers.setUserSessionStatus(EnumUserSessionStatus.CHECK_IN_AVAILABLE);
        registerdHolderUsersList.add(registerdHolderUsers);
        registerdHolderUsersPage = new PageImpl<>(registerdHolderUsersList);

        eventTicketsPage = new PageImpl<>(eventTicketsList);

        //mock
        when(ticketingTypeTicketService.findAllIdByEventIdAndRecurringEvent(any(), any())).thenReturn(ticketTypeIdsList);
        when(userSessionRepoService.findRegisteredUserSessionBySessionIdWithPagination(any(),any(),any(),any(),any(),any())).thenReturn(registerdHolderUsersPage);
        when(eventCommonRepoService.findByEventIdAndTypeAndStatus(any(),any(),any(),anyList(),any(),anyLong(),any())).thenReturn(eventTicketsPage);
        when(userSessionRepoService.countUserSessionByCheckInStatusAndSessionId(any())).thenReturn(totalCheckedInTickets);
        when(userSessionRepoService.countUserSessionBySessionStatusAndSessionId(any())).thenReturn(totalBookedTickets);

        //Execution
        AttendeeResponseContainer allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesContainerByEventAndTicketTypesAndTicketStatusAndSessionId(event, new ArrayList<>(), pageSizeSearchObj, recurringEventId, new ArrayList<>(), sessionId);
        assertEquals(totalCheckedInTickets,allAttendeesContainerData.getTotalCheckedInTickets());
        assertEquals(totalBookedTickets,allAttendeesContainerData.getTotalBookedTickets());
        assertEquals(1,allAttendeesContainerData.getRecordsTotal());
        assertEquals(1,allAttendeesContainerData.getRecordsFiltered());
    }


    @Test
    public void test_getAllAttendeesByEvent_success() {

        //setup
        eventTickets.setTicketPrice(10d);
        eventTickets.setTicketingTable(ticketingTable);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        attendeeDto = new AttendeeDto();
        attendeeDto.setFirstName(eventTickets.getHolderFirstName());
        attendeeDto.setLastName(eventTickets.getHolderLastName());
        attendeeDto.setEventTicketingId(eventTickets.getId());
        attendeeDto.setPaid(eventTickets.getPaidAmount());
        attendeeDto.setTicketPrice(eventTickets.getTicketPrice());
        attendeeDto.setRefundedAmount(eventTickets.getRefundedAmount());
        attendeeDto.setQty(1);
        attendeeDto.setSeatNumber(eventTickets.getSeatNumber());
        attendeeDto.setStatus(eventTickets.getTicketStatus().getStatus());
        attendeeDto.setTicketStatus(eventTickets.getTicketPaymentStatus().name());
        attendeeDto.setBarcode(eventTickets.getBarcodeId());

        List<AttendeeDto> attendeeDtoList = new ArrayList<>();
        attendeeDtoList.add(attendeeDto);

        Page<EventTickets> eventTicketspage = new PageImpl<>(eventTicketsList);

        //mock
        when(eventTicketsRepoService.findByEventid(event, PageRequest.of(0, Integer.MAX_VALUE))).thenReturn(eventTicketspage);

        Mockito.doReturn(attendeeDtoList).when(ticketingDisplayServiceImpl).getAllTicketAttendee(any());

        //Execution
        List<AttendeeDto> allAttendeesContainerData = ticketingDisplayServiceImpl.getAllAttendeesByEvent(event);
        for (AttendeeDto actualData : allAttendeesContainerData) {
            assertEquals(actualData.getFirstName(), attendeeDto.getFirstName());
            assertEquals(actualData.getLastName(), attendeeDto.getLastName());
            assertEquals(actualData.getStatus(), attendeeDto.getStatus());
        }
    }

    @Test
    public void test_getAllTicketAttendee_success_with_eventTickets_null() {

        //setup
        String search = "Search String";

        //Execution
        List<AttendeeDto> allAttendeesContainerData = ticketingDisplayServiceImpl.getAllTicketAttendee(null);
        assertEquals(0, allAttendeesContainerData.size());
    }

    @Test
    public void test_getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent_eventTicketsNull() {

        //setup
        List<TicketTypeCountDto> ticketTypeCountDtos = new ArrayList<>();

        List<Long> ticketTypeIds = new ArrayList<>();

        List<EventTickets> eventTickets = null;

        //Execution
        List<TicketTypeCountDto> ticketTypeCountDto = ticketingDisplayServiceImpl.getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent(eventTickets,ticketTypeIds, event);

        assertTrue(ticketTypeCountDto.isEmpty());
    }

    @Test
    public void test_getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent_sucess() {

        //setup
        TicketTypeCountDto ticketTypeCountDto = new TicketTypeCountDto(1L,"General Admission",0L);

        List<TicketTypeCountDto> ticketTypeCountDtos = new ArrayList<>();
        ticketTypeCountDtos.add(ticketTypeCountDto);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setCreatedFrom(1L);

        List<TicketingType> defaultEventTicketTypes = new ArrayList<>();
        ticketingType1.setCreatedFrom(1L);
        defaultEventTicketTypes.add(ticketingType1);

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(1L);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Map<TicketingType,Integer> mapNoOfSoldTicketWithTicketType = new HashMap<TicketingType,Integer>();
        mapNoOfSoldTicketWithTicketType.put(ticketingType,2);

        //mock
        when(ticketingTypeService.findAllByTicketing(anyLong(), any())).thenReturn(defaultEventTicketTypes);

        //Execution
        List<TicketTypeCountDto> ticketTypeCountDto1 = ticketingDisplayServiceImpl.getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent(eventTicketsList,ticketTypeIds, event);

        assertSame("General Admission", ticketTypeCountDto.getTicketTypeName());
        assertEquals(0L, (long) ticketTypeCountDto.getTotalTickets());
    }

    @Test
    public void test_getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent_defaultTicketTypeNull() {

        //setup
        List<TicketTypeCountDto> ticketTypeCountDtos = new ArrayList<>();

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(2L);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //Execution
        List<TicketTypeCountDto> ticketTypeCountDto = ticketingDisplayServiceImpl.getNumberOfTotalTicketPerTicketTypeForAllRecurringEvent(eventTicketsList,ticketTypeIds, event);

        assertTrue(ticketTypeCountDto.isEmpty());
    }

    @Test
    public void test_setTicketType_success_with_ticketingTable_null() {

        //setup
        attendeeDto = new AttendeeDto();
        attendeeDto.setTicketTypeId(id);

        //Execution
        ticketingDisplayServiceImpl.setTicketType(eventTickets, attendeeDto, null);
    }

    @Test
    public void test_getAllTicketsReport_success() {

        //setup
        String startDate = "01/01/2019 05:30:00";

        ticketTypeReportData = setTicketTypeReportData();

        List<TicketTypeReportData> ticketTypeReportDataList = new ArrayList<>();
        ticketTypeReportDataList.add(ticketTypeReportData);
        RecurringIdAndDateDto  recurringIdAndDateDto=new RecurringIdAndDateDto(1L,new Date());
        List<RecurringIdAndDateDto> recurringIdAndDateMap=new ArrayList<>();
        recurringIdAndDateMap.add(recurringIdAndDateDto);

        //mock
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        when(eventTicketsRepoService.getAllTicketTypeReportsForNonRec(any())).thenReturn(ticketTypeReportDataList);

        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(roTicketingTypeTicketService.getByTicketingTypeIds(anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketReportData> allTicketsReportData = ticketingDisplayServiceImpl.getAllTicketsReport(event, recurringEventId, DataType.TICKET);

        //assert
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsAvailable(), allTicketsReportData.get(0).getTotalTicketsAvailable());
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsCheckedIn(), allTicketsReportData.get(0).getTotalTicketsCheckedIn());
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsSold(), allTicketsReportData.get(0).getTotalTicketsSold());
        assertEquals(ticketTypeReportDataList.get(0).getGrossSale(),allTicketsReportData.get(0).getGrossSale(),1);

        //verify(recurringEventsScheduleService).getRecEventByIdsIn(anySet(), anyString());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(roTicketingTypeTicketService).getByTicketingTypeIds(anyList());
        verify(ticketingService).isRecurringEvent(any());
    }

    @Test
    public void test_getAllTicketsReport_startDateNullsuccess() {

        //setup
        ticketTypeReportData = setTicketTypeReportData();

        List<TicketTypeReportData> ticketTypeReportDataList = new ArrayList<>();
        ticketTypeReportDataList.add(ticketTypeReportData);

        RecurringIdAndDateDto dateDto = new RecurringIdAndDateDto(1L, new Date());
        List<RecurringIdAndDateDto> dto = new ArrayList<>();
        dto.add(dateDto);

        //mock
        when(recurringEventsScheduleService.getRecEventByIdsIn(anySet(), anyString())).thenReturn(dto);
        when(ticketingService.isRecurringEvent(any())).thenReturn(true);
        when(eventTicketsRepoService.getAllTicketTypeReports(any(), anyLong())).thenReturn(ticketTypeReportDataList);

        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(roTicketingTypeTicketService.getByTicketingTypeIds(anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketReportData> allTicketsReportData = ticketingDisplayServiceImpl.getAllTicketsReport(event, recurringEventId, DataType.TICKET);

        //assert
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsAvailable(), allTicketsReportData.get(0).getTotalTicketsAvailable());
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsCheckedIn(), allTicketsReportData.get(0).getTotalTicketsCheckedIn());
        assertEquals(ticketTypeReportDataList.get(0).getTotalTicketsSold(), allTicketsReportData.get(0).getTotalTicketsSold());
        assertEquals(ticketTypeReportDataList.get(0).getGrossSale(),allTicketsReportData.get(0).getGrossSale(),1);
        assertNotNull(allTicketsReportData.get(0).getRecurringDate());

        verify(eventTicketsRepoService).getAllTicketTypeReports(any(), anyLong());
        verify(recurringEventsScheduleService).getRecEventByIdsIn(anySet(), anyString());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(roTicketingTypeTicketService).getByTicketingTypeIds(anyList());
        verify(ticketingService).isRecurringEvent(any());
    }

    private TicketTypeReportData setTicketTypeReportData() {
        ticketTypeReportData = new TicketTypeReportData();
        ticketTypeReportData.setRecurringEventId(recurringEventId);
        ticketTypeReportData.setGrossSale(100d);
        ticketTypeReportData.setTicketingTypeId(ticketingType1.getId());
        ticketTypeReportData.setTicketTypeName(ticketingType1.getTicketTypeName());
        ticketTypeReportData.setTotalTicketsAvailable(5L);
        ticketTypeReportData.setTotalTicketsCheckedIn(5L);
        ticketTypeReportData.setTotalTicketsSold(5L);
        return ticketTypeReportData;
    }

    @Test
    public void test_getAccessCode_ticketTypePresentInList_true() {

        //setup
        ticketingAccessCode.setCode("Access Code");

        List<TicketingAccessCode> accessCodesList = new ArrayList<>();
        accessCodesList.add(ticketingAccessCode);

        //mock
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(accessCodesList);

        //Execution
        List<String> accessCodeData = ticketingDisplayServiceImpl.getAccessCode(id, event);
        for (String actualData : accessCodeData) {
            assertEquals(actualData, ticketingAccessCode.getCode());
        }
    }

    @Test
    public void test_getAccessCode_success_with_ticketTypePresentInList_false() {

        //setup
        ticketingAccessCode.setCode("Access Code");

        List<TicketingAccessCode> accessCodesList = new ArrayList<>();
        accessCodesList.add(ticketingAccessCode);

        //mock
        when(ticketingAccessCodeService.findByEvent(event)).thenReturn(accessCodesList);
        Mockito.doReturn(false).when(ticketingDisplayServiceImpl).isTicketTypePresentInList(anyLong(), any());

        //Execution
        List<String> accessCodeData = ticketingDisplayServiceImpl.getAccessCode(id, event);

        assertTrue(accessCodeData.isEmpty());
    }


    @Test
    public void test_isTicketTypePresentInList_success() {

        //setup
        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventId(event);
        ticketingAccessCode.setId(id);
        ticketingAccessCode.setCode("Access Code");

        //Execution
        boolean ticketTypePresentInList = ticketingDisplayServiceImpl.isTicketTypePresentInList(id, ticketingAccessCode);
        assertFalse(ticketTypePresentInList);
    }

    @Test
    public void test_unhideTicketType_success() {

        //setup
        List<String> accessCodesList = new ArrayList<>();
        accessCodesList.add("Access Code");

        //mock
        when(ticketingTypeCommonRepo.findByid(id)).thenReturn(ticketingType1);

        Mockito.doReturn(accessCodesList).when(ticketingDisplayServiceImpl).getAccessCode(id, event);
        Mockito.doNothing().when(ticketingAccessCodeService).removeTicketTypeFromAccessCode(accessCodesList,id);

        //Execution
        ticketingDisplayServiceImpl.unhideTicketType(id, event);

        ArgumentCaptor<TicketingType> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingType actualData = ticketingArgumentCaptor.getValue();

        assertEquals(actualData.getPrice(), ticketingType1.getPrice());
        assertFalse(actualData.isHidden());
    }

    @Test
    public void test_unhideTicketType_success_with_accessCode_empty() {

        //setup
        List<String> accessCodesList = new ArrayList<>();

        //mock
        when(ticketingTypeCommonRepo.findByid(id)).thenReturn(ticketingType1);

        Mockito.doReturn(accessCodesList).when(ticketingDisplayServiceImpl).getAccessCode(id, event);


        //Execution
        ticketingDisplayServiceImpl.unhideTicketType(id, event);

        ArgumentCaptor<TicketingType> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingType actualData = ticketingArgumentCaptor.getValue();

        assertEquals(actualData.getPrice(), ticketingType1.getPrice());
        assertFalse(actualData.isHidden());
    }

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketing.setRecurringEvent(false);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
        assertEquals(actualData.getBuyerAttributeOrder(), highestPosition);
    }*/

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success_non_recurring_conditional() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(0L);
        CustomAttributeCommon customAttributeCommon1;
        customAttributeCommon1 = new CustomAttributeCommon();
        customAttributeCommon1.setAttributeName("Attribute Name1");
        customAttributeCommon1.setDefaultValueJsonPurchaser("Default Value1");
        customAttributeCommon1.setEventTicketTypeId("1");
        customAttributeCommon1.setId(1L);
        customAttributeCommon1.setParentQuestionId(1L);
        customAttributeCommon1.setSelectedAnsId(1L);
        customAttributeCommonList.add(customAttributeCommon1);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(customAttributeCommon.getAttributeName(), event, DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L, DataType.TICKET);
        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        assertEquals("Attribute Name",  customAttribute.getAtrributeName());
    }*/

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success_recurring() {

        //setup

        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);
        ticketHolderRequiredAttributes.setEventid(event);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(0L);
         CustomAttributeCommon customAttributeCommon1;
        customAttributeCommon1 = new CustomAttributeCommon();
        customAttributeCommon1.setAttributeName("Attribute Name1");
        customAttributeCommon1.setDefaultValueJsonPurchaser("Default Value1");
        customAttributeCommon1.setEventTicketTypeId("1");
        customAttributeCommon1.setId(1L);
        customAttributeCommon1.setParentQuestionId(1L);
        customAttributeCommon1.setSelectedAnsId(1L);
        customAttributeCommonList.add(customAttributeCommon1);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(true);
        Map<Long, Long> mapOldAndNewParentQueId = new HashMap<>();
        List<TicketHolderRequiredAttributes> attributesList = new ArrayList<>();
        attributesList.add(ticketHolderRequiredAttributes);
        mapOldAndNewParentQueId.put(customAttribute.getId(),ticketHolderRequiredAttributes.getId());
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(customAttributeCommon.getAttributeName(), event, 1L, DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 1L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }*/
    @Test
    public void test_addCustomAttribute_success_recurring1() {
        //setup
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);
        ticketHolderRequiredAttributes.setEventid(event);
        customAttributeNew.setAttributeType(AttributeValueType.TEXT);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(true);
        Map<Long, Long> mapOldAndNewParentQueId = new HashMap<>();
        List<TicketHolderRequiredAttributes> attributesList = new ArrayList<>();
        attributesList.add(ticketHolderRequiredAttributes);
        mapOldAndNewParentQueId.put(customAttribute.getId(),ticketHolderRequiredAttributes.getId());
        //mock




        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(customAttributeCommon.getAttributeName(), event, 1L, DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 1L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success_non_recurring() {

        //setup

        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);
        ticketHolderRequiredAttributes.setEventid(event);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(0L);
        CustomAttributeCommon customAttributeCommon1;
        customAttributeCommon1 = new CustomAttributeCommon();
        customAttributeCommon1.setAttributeName("Attribute Name1");
        customAttributeCommon1.setDefaultValueJsonPurchaser("Default Value1");
        customAttributeCommon1.setEventTicketTypeId("1");
        customAttributeCommon1.setId(1L);
        customAttributeCommon1.setParentQuestionId(1L);
        customAttributeCommon1.setSelectedAnsId(1L);
        customAttributeCommonList.add(customAttributeCommon1);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(true);
        Map<Long, Long> mapOldAndNewParentQueId = new HashMap<>();
        List<TicketHolderRequiredAttributes> attributesList = new ArrayList<>();
        attributesList.add(ticketHolderRequiredAttributes);
        mapOldAndNewParentQueId.put(customAttribute.getId(),ticketHolderRequiredAttributes.getId());
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true, DataType.TICKET)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(customAttributeCommon.getAttributeName(), event, 0L, DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(2)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

      //  assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }*/
    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success_recurring_update() {

        //setup

        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setDeletedForBuyer(false);
        ticketHolderRequiredAttributes.setDeletedForBuyer(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerAttributeOrder(1);
        ticketHolderRequiredAttributes.setEventid(event);

        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(customAttributeCommon.getAttributeName(), event, 0L, DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
        assertEquals(actualData.getBuyerAttributeOrder(), highestPosition);
    }*/

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttributeForRecurringDatesBySelectingAll_success() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketing.setRecurringEvent(true);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(anyString(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        doNothing().when(ticketingDisplayServiceImpl).setRequiredDisableIfHidden(any(), any());
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(Collections.EMPTY_LIST);
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateTicketHolderRequiredAttributes(anyList(), anyList(), anyBoolean(), anyBoolean());
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
        assertEquals(actualData.getBuyerAttributeOrder(), highestPosition);
    }*/

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttributeForRecurringDatesBySelectingSpecificDate_success() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketing.setRecurringEvent(true);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(anyString(), any(), anyLong(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true, DataType.TICKET)).thenReturn(highestPosition);
        doNothing().when(ticketingDisplayServiceImpl).setRequiredDisableIfHidden(any(), any());
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(new RecurringEvents());
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateTicketHolderRequiredAttributes(anyList(), anyList(), anyBoolean(), anyBoolean());
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 1L,  DataType.TICKET);

        verify(allRequiresAttributesService, times(0)).save(any(TicketHolderRequiredAttributes.class));
    }*/

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttributeForRecurringDatesBySelectingSpecificDate_VerifyCallSuccessfully_StAttributeAsCustom() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketing.setRecurringEvent(true);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(anyString(), any(), anyLong(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        doNothing().when(ticketingDisplayServiceImpl).setRequiredDisableIfHidden(any(), any());
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(new RecurringEvents());
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateTicketHolderRequiredAttributes(anyList(), anyList(), anyBoolean(), anyBoolean());
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 1L,  DataType.TICKET);

        verify(ticketingDisplayServiceImpl, times(1)).setAttributeAsCustom(anyLong(), any());
        verify(allRequiresAttributesService, times(0)).save(any(TicketHolderRequiredAttributes.class));
    }*/

    @Test
    public void test_setAttributeAsCustom_setMinusOne(){

        //mock
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes =  new TicketHolderRequiredAttributes();


        //Execution
        ticketingDisplayServiceImpl.setAttributeAsCustom(1L, ticketHolderRequiredAttributes);
        assertEquals(Long.valueOf(-1), ticketHolderRequiredAttributes.getCreatedFrom());
    }

    @Test
    public void test_setAttributeAsCustom_NotSetMinusOne(){

        //mock
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes =  new TicketHolderRequiredAttributes();

        //Execution
        ticketingDisplayServiceImpl.setAttributeAsCustom(0L, ticketHolderRequiredAttributes);
        assertNull(ticketHolderRequiredAttributes.getCreatedFrom());
    }

    //TODO : Junit5 review test
    /*@Test
    public void test_addCustomAttribute_success_with_eventTicketTypeId_empty() {

        //setup
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");

        customAttribute.setEventTicketTypeId("");
        ticketing.setRecurringEvent(false);
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(null);
        when(ticketHolderRequiredAttributesService.findHighestAttributePosition(event, true)).thenReturn(highestPosition);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

        //Execution
        ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
        assertEquals(actualData.getBuyerAttributeOrder(), highestPosition);
    }*/

    @Test
    public void test_addCustomAttribute_throwException_ATTRIBUTE_NAME_EXIST() {

        //setup
        ticketHolderRequiredAttributes.setName(firstName);

        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setAtrributeName("Attribute Name");
        ticketing.setRecurringEvent(false);
        //mock

        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(ticketHolderRequiredAttributes);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET));
        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NAME_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_addCustomAttributeForRecurringDatesBySelectingAll_throwException_ATTRIBUTE_NAME_EXIST() {

        //setup
        ticketHolderRequiredAttributes.setName(firstName);

        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setAtrributeName("Attribute Name");
        ticketing.setRecurringEvent(true);
        //mock
        doReturn(true).when(ticketingService).isRecurringEvent(event);
        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(anyString(), any(), anyLong(), any())).thenReturn(ticketHolderRequiredAttributes);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 1L,  DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NAME_EXIST.getDeveloperMessage(), exception.getMessage());
    }



    @Test
    public void test_updateCustomAttribute_success() {

        ticketing.setRecurringEvent(false);
        //mock

        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));


        ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttributeNew, event, true, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }

    @Test
    public void test_updateCustomAttribute_success2() {

        ticketing.setRecurringEvent(false);
        //mock

        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        Mockito.doNothing().when(ticketHolderAttributesService).updateAttributes(anyString(), anyLong(), anyString());
        Mockito.doNothing().when(viewFilterDetailsService).updateAdvanceJSONAttributes(anyLong(),anyString(),anyLong());


        ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttributeNew, event, true, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }

    @Test
    public void test_updateCustomAttribute_success1() {

        //setup
        customAttribute.setEventTicketTypeId("");

        ticketing.setRecurringEvent(false);
        //mock

        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        Mockito.doNothing().when(ticketHolderAttributesService).updateAttributes(anyString(), anyLong(), anyString());


        ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttributeNew, event, true, 0L, DataType.TICKET);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService,times(1)).saveAndReturn(ticketHolderRequiredAttributesArgumentCaptor.capture());
        TicketHolderRequiredAttributes actualData = ticketHolderRequiredAttributesArgumentCaptor.getValue();

        assertEquals(actualData.getName(), customAttribute.getAtrributeName());
        assertEquals(actualData.getEnabledForTicketHolder(), customAttribute.isEnabledForTicketHolder());
    }

    /*@Test
    public void test_updateCustomAttribute_throwException_ATTRIBUTE_NAME_EXIST() {

        //setup
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setName("Attribute Name1");
        ticketing.setRecurringEvent(false);
        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(allRequiresAttributesService.findBynameAndEventid(customAttribute.getAtrributeName(), event,  DataType.TICKET)).thenReturn(ticketHolderRequiredAttributes1);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        //Execution
        ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttribute, event, true, 0L);
    }*/

    @Test
    public void test_updateCustomAttribute_throwException_ATTRIBUTE_NOT_EXISTS() {

        //setup
        CustomAttribute customAttribute = new CustomAttribute();
        ticketing.setRecurringEvent(false);
        //mock

        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(Optional.empty());
        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttributeNew, event, true, 0L, DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    private static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{1L},
                new Object[]{0L},
        };
    }

    @Test
    public void test_updateCustomAttribute_recurringEventIdGraterThanZero(){

        //setup
        ticketing.setRecurringEvent(true);
        customAttributeCommon.setEventTicketTypeId("1");

        //mock

        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        doReturn(null).when(ticketingDisplayServiceImpl).updateCustomTicketAttribute(anyLong(), any(),any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());
        //execute
        ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 1L, DataType.TICKET);
        verify(ticketingDisplayServiceImpl, times(1)).updateCustomTicketAttribute(anyLong(), any(), any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());

    }
    @Test
    public void test_updateCustomAttribute_recurringEventIdGraterThanNonZero(){

        //setup
        ticketing.setRecurringEvent(true);
        customAttributeCommon.setEventTicketTypeId("1");
        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setId(1L);
        attributes.setName("Attribute Name");
        attributes.setRecurringEventId(0L);
        List<TicketHolderRequiredAttributes> requiredAttributes = new ArrayList<>();
        requiredAttributes.add(attributes);

        //mock

        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketingService.isRecurringEvent(event)).thenReturn(true);
        when(ticketHolderRequiredAttributesService.getAllAttributesByCreatedFromAndEventId(anyLong(), any())).thenReturn(requiredAttributes);
        doReturn(null).when(ticketingDisplayServiceImpl).updateCustomTicketAttribute(anyLong(), any(),any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());
        //execute
        ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 0L, DataType.TICKET);
        verify(ticketingDisplayServiceImpl, times(1)).updateCustomTicketAttribute(anyLong(), any(), any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());

    }

    //TODO : Junit5 review test
    /*@Test
    public void test_updateCustomAttribute_recurringEvent_defaultUpdateAndAllAttributeUpdateOfRecurringEvents(){

        //setup
        ticketing.setRecurringEvent(true);
        customAttribute.setEventTicketTypeId("1");

        TicketHolderRequiredAttributes attributes = new TicketHolderRequiredAttributes();
        attributes.setId(1L);
        attributes.setName("Attribute Name");
        attributes.setRecurringEventId(0L);
        List<TicketHolderRequiredAttributes> requiredAttributes = new ArrayList<>();
        requiredAttributes.add(attributes);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setRecurringEventId(1L);
        List<TicketingType> ticketTypesBelongToRecurrings = new ArrayList<>();
        ticketTypesBelongToRecurrings.add(ticketingType);
        ticketing.setRecurringEvent(true);

        List<Long> newTicketingTypeIds = new ArrayList<>();
        newTicketingTypeIds.add(1L);

        //mock

        when(ticketHolderRequiredAttributesService.findCustomAttributeById(anyLong())).thenReturn(Optional.of(attributes));
        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(anyString(), any(), any())).thenReturn(null);
        when(ticketingService.isRecurringEvent(any())).thenReturn(true);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));
        when(ticketingTypeRepository.findByCreatedFrom(anyList())).thenReturn(ticketTypesBelongToRecurrings);
        when(ticketHolderRequiredAttributesService.getAllAttributesByCreatedFromAndEventId(anyLong(), any())).thenReturn(requiredAttributes);
        doNothing().when(ticketingDisplayServiceImpl).updateCustomTicketAttribute(attributes, customAttributeCommon, customAttributeNew, event, true, false);
        //execute
        ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 0L, DataType.TICKET);
        verify(ticketingDisplayServiceImpl, times(2)).updateCustomTicketAttribute(attributes, customAttributeCommon, customAttributeNew,  event, true, false);

    }*/

    @Test
    public void test_updateCustomAttribute_nonRecurringEvent(){

        //setup
        customAttributeCommon.setNewSubQue(false);
        ticketing.setRecurringEvent(false);
        customAttribute.setEventTicketTypeId("1");

        //mock
        when(ticketRequiresAttributesRepo.findById(1L)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        doReturn(null).when(ticketingDisplayServiceImpl).updateCustomTicketAttribute(anyLong(), any(), any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());
        //execute
        ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 1L, DataType.TICKET);
        verify(ticketingDisplayServiceImpl, times(1)).updateCustomTicketAttribute(anyLong(), any(), any(), any(), anyBoolean(), anyLong(), anyBoolean(), any());

    }

    @Test
    public void test_updateCustomAttribute_ThrowException(){

        //setup
        customAttribute.setEventTicketTypeId("1");

        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(anyLong())).thenReturn(Optional.empty());
        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.updateCustomTicketAttribute(1L, customAttributeCommon, customAttributeNew,  event, true, 1L, true, DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NOT_EXISTS.getErrorMessage(), exception.getMessage());
    }


    @Test
    public void test_updateCustomAttribute_CallingSubMethodSuccessfully(){

        //setup
        customAttribute.setEventTicketTypeId("1");

        TicketHolderRequiredAttributes attributesOpt = new TicketHolderRequiredAttributes();
        attributesOpt.setId(1L);
        attributesOpt.setName(firstName);

        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(anyLong())).thenReturn(Optional.of(attributesOpt));
        doNothing().when(ticketingDisplayServiceImpl).updateCustomTicketAttribute(attributesOpt, customAttributeCommon, customAttributeNew,  event, true, true);
        //execute
        ticketingDisplayServiceImpl.updateCustomTicketAttribute(1L, customAttributeCommon, customAttributeNew,  event, true, 1L, true, DataType.TICKET);
        verify(ticketingDisplayServiceImpl, times(1)).updateCustomTicketAttribute(attributesOpt, customAttributeCommon, customAttributeNew,  event, true, true);
    }


    @Test
    public void test_checkAndUpdateEventSearchDetailsDot_verifyMethodCall_LimitEventCapacityFalse(){

        EventSearchDetailsDto eventSearchDetailsDto = new EventSearchDetailsDto();
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType1);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();

        //Mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);
        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventSearchDetailsDto(eventSearchDetailsDto, ticketingTypes);

        verify(eventCommonRepoService, times(1)).findSoldCountGroupByType(any(),any());
    }

    @Test
    public void test_checkAndUpdateEventSearchDetailsDot_verifyMethodCall_LimitEventCapacityTrue_RecurringFalse(){

        EventSearchDetailsDto eventSearchDetailsDto = new EventSearchDetailsDto();
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(10.0);
        ticketing.setRecurringEvent(false);
        ticketingType1.setTicketing(ticketing);
        ticketingTypes.add(ticketingType1);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L, new BigDecimal(10));

        //Mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventSearchDetailsDto(eventSearchDetailsDto, ticketingTypes);

        verify(eventCommonRepoService, times(1)).findSoldCountGroupByType(any(),any());
        assertTrue(eventSearchDetailsDto.getEventCapacityReach());
    }

    @Test
    public void test_checkAndUpdateEventSearchDetailsDot_verifyMethodCall_LimitEventCapacityTrue_RecurringTrue_NumberOfRecurringDatesZERO(){

        EventSearchDetailsDto eventSearchDetailsDto = new EventSearchDetailsDto();
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(10.0);
        ticketing.setRecurringEvent(true);
        ticketingType1.setTicketing(ticketing);
        ticketingTypes.add(ticketingType1);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L, new BigDecimal(5));

        //Mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventSearchDetailsDto(eventSearchDetailsDto, ticketingTypes);

        verify(eventCommonRepoService, times(1)).findSoldCountGroupByType(any(),any());
        assertFalse(eventSearchDetailsDto.getEventCapacityReach());
    }

    @Test
    public void test_checkAndUpdateEventSearchDetailsDot_verifyMethodCall_LimitEventCapacityTrue_RecurringTrue_NumberOfRecurringDates_NOT_ZERO(){

        EventSearchDetailsDto eventSearchDetailsDto = new EventSearchDetailsDto();
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(10.0);
        ticketing.setRecurringEvent(true);
        ticketingType1.setTicketing(ticketing);
        ticketingTypes.add(ticketingType1);

        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();
        ticketSoldCount.put(1L, new BigDecimal(5));

        List<RecurringEventsTicketingTypeAndSoldCountDTO> eventIdAndSoldCountDTO = new ArrayList<>();
        eventIdAndSoldCountDTO.add(new RecurringEventsTicketingTypeAndSoldCountDTO(1L, true));

        //Mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);
        when(eventTicketsRepoService.countNumberOfSoldTicketForRecurringEventsByEventId(event.getEventId())).thenReturn(eventIdAndSoldCountDTO);

        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventSearchDetailsDto(eventSearchDetailsDto, ticketingTypes);

        verify(eventCommonRepoService, times(1)).findSoldCountGroupByType(any(),any());
        assertTrue(eventSearchDetailsDto.getEventCapacityReach());
    }

    @Test
    public void test_checkAndUpdateEventInfoDto_NoResponse_Empty(){

        EventInfoDto eventInfoDto = new EventInfoDto(event.getName(), event.getEventURL(), ticketing.getEventEndDate(), eventLogo, eventLocation);

        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventInfoDto(eventInfoDto, Collections.emptyList());
    }

    @Test
    public void test_checkAndUpdateEventInfoDto_success_Seted_EventCapacityLimit(){

        EventInfoDto eventInfoDto = new EventInfoDto(event.getName(), event.getEventURL(), ticketing.getEventEndDate(), eventLogo, eventLocation);
        ticketing.setLimitEventCapacity(true);
        ticketing.setEventCapacity(10.0);
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingType1.setTicketing(ticketing);
        ticketingTypes.add(ticketingType1);
        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();

        List<RecurringEventsTicketingTypeAndSoldCountDTO> soldCountDTOS = new ArrayList<>();
        soldCountDTOS.add(new RecurringEventsTicketingTypeAndSoldCountDTO(1L,1L,1L));
        //mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        when(eventTicketsRepoService.countNumberOfSoldTicketForRecurringEventsByEventId(event.getEventId())).thenReturn(soldCountDTOS);
        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventInfoDto(eventInfoDto, ticketingTypes);
        assertFalse(eventInfoDto.getEventCapacityReach());
    }

    @Test
    public void test_checkAndUpdateEventInfoDto_success_Seted_EventCapacity_NOT_Limit(){

        EventInfoDto eventInfoDto = new EventInfoDto(event.getName(), event.getEventURL(), ticketing.getEventEndDate(), eventLogo, eventLocation);
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketing.setLimitEventCapacity(false);
        ticketingType1.setTicketing(ticketing);
        ticketingTypes.add(ticketingType1);
        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();

        //mock
        when(eventCommonRepoService.findSoldCountGroupByType(event.getEventId(), event.getTicketingId())).thenReturn(ticketSoldCount);

        //Execution
        ticketingDisplayServiceImpl.checkAndUpdateEventInfoDto(eventInfoDto, ticketingTypes);
        assertNull(eventInfoDto.getEventCapacityReach());
    }
    @Test
    public void test_addCustomAttribute_throwException_VALIDPLANFOR_MULTIPLE_CHOICE() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);

        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setAtrributeName("Attribute Name");
        customAttributeNew.setAttributeType(AttributeValueType.MULTIPLE_CHOICE);
        ticketing.setRecurringEvent(false);
        //mock

        when(allRequiresAttributesService.findByNameAndEventIdAndRecurringEventIdNull(any(), any(), any())).thenReturn(ticketHolderRequiredAttributes);


        //Execution
        assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET));
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_PARENT_QUESTION_NOT_FOUND() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(1L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.PARENT_QUESTION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_QUESTION_SELECTED_ANS_ID() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(1L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock




        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.CONDITIONAL_QUESTION_SELECTED_ANS_ID.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_MINIMUM_ONE_SUB_QUESTION_NEEDED() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(0L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock




        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.addCustomAttributeNew(event, customAttributeNew, 0L,  DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.MINIMUM_ONE_SUB_QUESTION_NEEDED.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_addCustomAttribute_throwException_VALIDPLANFOR_MULTIPLE_CHOICE1() {

        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        Optional<TicketHolderRequiredAttributes> optAttributes = Optional.of(new TicketHolderRequiredAttributes());
        optAttributes.get().setName("Attribute Name");

        CustomAttribute customAttribute = new CustomAttribute();
        customAttribute.setAtrributeName("Attribute Name");
        customAttributeNew.setAttributeType(AttributeValueType.MULTIPLE_CHOICE);
        ticketing.setRecurringEvent(false);
        //mock



        when(ticketRequiresAttributesRepo.findById(id)).thenReturn(Optional.ofNullable(ticketHolderRequiredAttributes));
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(id)).thenReturn(optAttributes);

        //Execution
        ticketingDisplayServiceImpl.updateCustomAttribute(id, customAttributeNew, event, true, 1L, DataType.TICKET);
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_PARENT_QUESTION_NOT_FOUND1() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(1L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock




        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 1L, DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.PARENT_QUESTION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_QUESTION_SELECTED_ANS_ID1() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(1L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock




        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 1L, DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.CONDITIONAL_QUESTION_SELECTED_ANS_ID.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_addCustomAttribute_throwException_CONDITIONAL_MINIMUM_ONE_SUB_QUESTION_NEEDED1() {
        //setup
        ticketHolderRequiredAttributes.setName(firstName);
        customAttributeCommon.setParentQuestionId(0L);
        customAttributeCommon.setSelectedAnsId(0L);
        customAttributeNew.setAttributeType(AttributeValueType.CONDITIONAL_QUE);
        customAttributeNew.setCustomAttributeCommons(customAttributeCommonList);
        ticketing.setRecurringEvent(false);

        //mock

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDisplayServiceImpl.updateCustomAttribute(1L, customAttributeNew, event, true, 1L, DataType.TICKET));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.MINIMUM_ONE_SUB_QUESTION_NEEDED.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    public void test_getDisplayPageSettingData_throwException_ticket_access_code_is_expired() {
        //mock
        when(ticketingAccessCodeService.getByCodeANDCheckEnddate(anyString(), any(), anyLong())).thenReturn(Optional.empty());


        //Execution
        Optional<TicketingAccessCode> ticketingAccessCode = ticketingAccessCodeService.getByCodeANDCheckEnddate(accessCode, event, 0L);

        //assertion
        assertFalse(ticketingAccessCode.isPresent());
    }

    @Test
    public void testAddonVisibilityTrue_AttachedToALLTicketTypesOrSomeTicketTypes() {
        // Base ticket type
        ticketingType1.setDataType(DataType.TICKET);
        ticketingType1.setHidden(false);

        ticketingTypeAddOn.setHidden(false);
        ticketingTypeAddOn.setListOfTicketTypesForAddOn("1");

        // Mock RO service to return both
        when(roTicketingTypeTicketService.getTicketingTypesDisplay(eq(event), anyBoolean(), eq(recurringEventId), eq(ticketing)))
                .thenReturn(List.of(ticketingType1, ticketingTypeAddOn));

        // When: calling method without access code
        List<TicketTypeDto> result = ticketingDisplayServiceImpl.getTicketTypesForCachedAPI(
                "", event, ticketing, recurringEventId, null
        );

        // Then: should include both ticket and add-on
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.stream().filter(ticketTypeDto -> ticketTypeDto.getDataType().equals(DataType.ADDON)).count());

        Set<DataType> resultTypes = result.stream().map(TicketTypeDto::getDataType).collect(Collectors.toSet());
        assertTrue(resultTypes.contains(DataType.ADDON), "Add-on should be included when not hidden");
        assertTrue(resultTypes.contains(DataType.TICKET), "Ticket should be included");
    }

    @Test
    public void testAddonVisibilityTrue_NotAttachedToALLTicketTypes() {
        // Base ticket type
        ticketingType1.setDataType(DataType.TICKET);
        ticketingType1.setHidden(false);

        ticketingTypeAddOn.setHidden(false);
        ticketingTypeAddOn.setListOfTicketTypesForAddOn("");

        // Mock RO service to return both
        when(roTicketingTypeTicketService.getTicketingTypesDisplay(eq(event), anyBoolean(), eq(recurringEventId), eq(ticketing)))
                .thenReturn(List.of(ticketingType1, ticketingTypeAddOn));

        // When: calling method without access code
        List<TicketTypeDto> result = ticketingDisplayServiceImpl.getTicketTypesForCachedAPI(
                "", event, ticketing, recurringEventId, null
        );

        // Then: should include both ticket and add-on
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.stream().filter(ticketTypeDto -> ticketTypeDto.getDataType().equals(DataType.ADDON)).count());

        Set<DataType> resultTypes = result.stream().map(TicketTypeDto::getDataType).collect(Collectors.toSet());
        assertTrue(resultTypes.contains(DataType.ADDON), "Add-on should be included when not hidden");
        assertTrue(resultTypes.contains(DataType.TICKET), "Ticket should be included");
    }

    @Test
    public void testAddonVisibilityFalse_AttachedToTicketTypes_NoAccessCode() {
        // Base ticket type
        ticketingType1.setDataType(DataType.TICKET);
        ticketingType1.setHidden(false);

        ticketingTypeAddOn.setHidden(true);
        ticketingTypeAddOn.setListOfTicketTypesForAddOn("1");

        doReturn(List.of(ticketingType1, ticketingTypeAddOn))
                .when(roTicketingTypeTicketService)
                .getTicketingTypesDisplay(eq(event), anyBoolean(), eq(recurringEventId), eq(ticketing));


        // When: calling method without access code
        List<TicketTypeDto> result = ticketingDisplayServiceImpl.getTicketTypesForCachedAPI(
                "", event, ticketing, recurringEventId, null
        );

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(0, result.stream().filter(ticketTypeDto -> ticketTypeDto.getDataType().equals(DataType.ADDON)).count());

        Set<DataType> resultTypes = result.stream().map(TicketTypeDto::getDataType).collect(Collectors.toSet());
        assertTrue(resultTypes.contains(DataType.TICKET), "Ticket should be included");
    }

    @Test
    public void testAddonVisibilityFalse_AttachedToTicketTypes_WithAccessCode() {
        // Base ticket type
        ticketingType1.setDataType(DataType.TICKET);
        ticketingType1.setHidden(false);

        ticketingTypeAddOn.setHidden(true);
        ticketingTypeAddOn.setListOfTicketTypesForAddOn("1");

        ticketingAccessCode.setId(10L);
        ticketingAccessCode.setUses(5);
        ticketingAccessCode.setEventTicketTypeId("1,2");

        // Mock: RO Access code service returns the code
        doReturn(Optional.of(ticketingAccessCode))
                .when(roTicketingAccessCodeService)
                .getByCodeAndCheckEndDate(anyString(), any(Event.class), anyLong());

        // Mock: Access code used count = 2 < 5
        doReturn(0)
                .when(ticketingOrderService)
                .getAccessCodeUsed(anyLong(), anyLong());

        // Mock: The ticket types returned for this access code
        doReturn(List.of(ticketingTypeAddOn))
                .when(ticketingDisplayServiceImpl)
                .getTicketTypeFromAccessCode(eq(ticketingAccessCode), eq(event));


        // When: calling method without access code
        List<TicketTypeDto> result = ticketingDisplayServiceImpl.getTicketTypesForCachedAPI(
                "ACCESS_CODE_ATTACHED_TO_TICKET", event, ticketing, recurringEventId, null
        );

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(DataType.ADDON, result.get(0).getDataType());

        Set<DataType> resultTypes = result.stream().map(TicketTypeDto::getDataType).collect(Collectors.toSet());
        assertTrue(resultTypes.contains(DataType.ADDON), "Add-on should be included when accesscode is attached to ticket");
    }

    @Test
    public void testAddonVisibilityFalse_NotAttachedToTicketTypes_WithAccessCode() {
        // Base ticket type
        ticketingType1.setDataType(DataType.TICKET);
        ticketingType1.setHidden(false);

        ticketingTypeAddOn.setHidden(true);
        ticketingTypeAddOn.setListOfTicketTypesForAddOn("");

        ticketingAccessCode.setId(10L);
        ticketingAccessCode.setUses(5);
        ticketingAccessCode.setEventTicketTypeId("1,2");

        // Mock: RO Access code service returns the code
        doReturn(Optional.of(ticketingAccessCode))
                .when(roTicketingAccessCodeService)
                .getByCodeAndCheckEndDate(anyString(), any(Event.class), anyLong());

        // Mock: Access code used count = 2 < 5
        doReturn(0)
                .when(ticketingOrderService)
                .getAccessCodeUsed(anyLong(), anyLong());

        // Mock: The ticket types returned for this access code
        doReturn(List.of(ticketingTypeAddOn))
                .when(ticketingDisplayServiceImpl)
                .getTicketTypeFromAccessCode(eq(ticketingAccessCode), eq(event));


        // When: calling method without access code
        List<TicketTypeDto> result = ticketingDisplayServiceImpl.getTicketTypesForCachedAPI(
                "ACCESS_CODE_ATTACHED_TO_TICKET", event, ticketing, recurringEventId, null
        );

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(DataType.ADDON, result.get(0).getDataType());

        Set<DataType> resultTypes = result.stream().map(TicketTypeDto::getDataType).collect(Collectors.toSet());
        assertTrue(resultTypes.contains(DataType.ADDON), "Add-on should be included when accesscode is attached to ticket");
    }
}
