package com.accelevents.services.impl;

import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.session_speakers.OwnerAvailabilityRule;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.ro.neptune.RONeptuneAttendeeDetailService;
import com.accelevents.services.UserService;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.session_speakers.services.OwnerAvailabilityRuleService;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AttendeeProfileServiceImplTest {

    @Spy
    @InjectMocks
    private AttendeeProfileServiceImpl attendeeProfileServiceImpl;
    @Mock
    private OwnerAvailabilityRuleService ownerAvailabilityRuleService;
    @Mock
    private RONeptuneAttendeeDetailService roNeptuneAttendeeDetailService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private UserService userService;
    @Mock
    private NeptuneAttendeeDetailService neptuneDBService;


    @Test
    void test_getAttendeeProfile_sortByLastName() {

        //setup
        Event event = EventDataUtil.getEvent();
        User user = EventDataUtil.getUser();
        List<BigInteger> userIds = new ArrayList<>();
        userIds.add(BigInteger.ONE);

        AttendeeProfileDto attendeeProfileDto = getAttendeeProfileDto("title", 1L, "Jay");

        AttendeeProfileDto attendeeProfileDto1 = getAttendeeProfileDto("title1", 2L, "Aakash");

        AttendeeProfileDto attendeeProfileDto2 = getAttendeeProfileDto("title2", 3L, "Ravi");

        AttendeeProfileDto attendeeProfileDto3 = getAttendeeProfileDto("title3", 4l, "aayushi");

        AttendeeProfileDto attendeeProfileDto4 = getAttendeeProfileDto("title4", 5L, null);

        AttendeeProfileDto attendeeProfileDto5 = getAttendeeProfileDto("title5", 6l, "jadeja");

        AttendeeProfileDto attendeeProfileDto6 = getAttendeeProfileDto("title6", 7l, "Bijal");

        AttendeeProfileDto attendeeProfileDto7 = getAttendeeProfileDto("title7", 8l, null);

        AttendeeProfileDto attendeeProfileDto8 = getAttendeeProfileDto("title8", 9l, "Chitra");

        List<AttendeeProfileDto> attendeeProfiles = new ArrayList<>();
        attendeeProfiles.add(attendeeProfileDto);
        attendeeProfiles.add(attendeeProfileDto1);
        attendeeProfiles.add(attendeeProfileDto2);
        attendeeProfiles.add(attendeeProfileDto3);
        attendeeProfiles.add(attendeeProfileDto4);
        attendeeProfiles.add(attendeeProfileDto5);
        attendeeProfiles.add(attendeeProfileDto6);
        attendeeProfiles.add(attendeeProfileDto7);
        attendeeProfiles.add(attendeeProfileDto8);

        OwnerAvailabilityRule ownerAvailabilityRule1 = findOwnerAvailabilityRule(event.getEventId(), 1L);
        OwnerAvailabilityRule ownerAvailabilityRule2 = findOwnerAvailabilityRule(event.getEventId(), 2L);
        OwnerAvailabilityRule ownerAvailabilityRule3 = findOwnerAvailabilityRule(event.getEventId(), 3L);
        OwnerAvailabilityRule ownerAvailabilityRule4 = findOwnerAvailabilityRule(event.getEventId(), 4l);
        OwnerAvailabilityRule ownerAvailabilityRule5 = findOwnerAvailabilityRule(event.getEventId(), 5L);
        OwnerAvailabilityRule ownerAvailabilityRule6 = findOwnerAvailabilityRule(event.getEventId(), 6l);
        OwnerAvailabilityRule ownerAvailabilityRule7 = findOwnerAvailabilityRule(event.getEventId(), 7l);
        OwnerAvailabilityRule ownerAvailabilityRule8 = findOwnerAvailabilityRule(event.getEventId(), 8l);
        OwnerAvailabilityRule ownerAvailabilityRule9 = findOwnerAvailabilityRule(event.getEventId(), 9l);

        List<OwnerAvailabilityRule> ownerAvailabilityRuleList = new ArrayList<>();
        ownerAvailabilityRuleList.add(ownerAvailabilityRule1);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule2);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule3);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule4);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule5);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule6);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule7);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule8);
        ownerAvailabilityRuleList.add(ownerAvailabilityRule9);

        //mock
        when(neptuneDBService.getAttendees(anyString(), anyString(),anyInt(),anyInt(),anyString())).thenReturn(attendeeProfiles);
        when(ownerAvailabilityRuleService.findByOwnerAvailabilityRuleList(anyLong(), anyList())).thenReturn(ownerAvailabilityRuleList);

        //Execution
        DataTableResponse dataTableResponse = attendeeProfileServiceImpl.getAllAttendeeProfileData(event, user, 1, 10, "");

        //Assertion
        assertFalse(dataTableResponse.getData().isEmpty());
    }

    private AttendeeProfileDto getAttendeeProfileDto(String title, long userId, String lastName) {
        AttendeeProfileDto attendeeProfileDto8 = new AttendeeProfileDto();
        attendeeProfileDto8.setTitle(title);
        attendeeProfileDto8.setUserId(userId);
        attendeeProfileDto8.setLastName(lastName);
        return attendeeProfileDto8;
    }

    private OwnerAvailabilityRule findOwnerAvailabilityRule(long eventId, long userId) {
        OwnerAvailabilityRule ownerAvailabilityRule = new OwnerAvailabilityRule();
        ownerAvailabilityRule.setEventId(eventId);
        ownerAvailabilityRule.setUserId(userId);
        ownerAvailabilityRule.setMeetingRules(Constants.DEFAULT_SLOT);
        return ownerAvailabilityRule;
    }
}
