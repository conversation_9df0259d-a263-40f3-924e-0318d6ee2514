package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.IntegrationSourceType;
import com.accelevents.domain.enums.IntegrationType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.dto.TrayMappingDTO;
import com.accelevents.dto.tray.io.IntegrationFieldDto;
import com.accelevents.dto.tray.io.SolutionInstancesDto;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.TrayIntegrationMappingRepository;
import com.accelevents.repositories.TrayIntegrationRepository;
import com.accelevents.services.IntegrationService;
import com.accelevents.services.TicketingOrderService;
import com.accelevents.services.tray.io.impl.TrayIntegrationServiceImpl;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.accelevents.utils.TrayIOConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TrayIntegrationServiceImplTest {
    @InjectMocks
    @Spy
    private TrayIntegrationServiceImpl trayIntegrationServiceImpl;
    @Mock
    private TrayIntegrationRepository trayIntegrationRepository;
    @Mock
    private TrayIntegrationMappingRepository trayIntegrationMappingRepository;
    @Mock
    private IntegrationService integrationService;
    @Mock
    private TicketingOrderService mockTicketingOrderService;
    private TrayIntegration trayIntegration;
    private Event event;
    private TicketingOrder ticketingOrder;
    private EventTickets eventTicket;
    private WhiteLabel whiteLabel;
    private Integration integration;
    private TrayIntegrationMappings trayIntegrationMappings;
    private Organizer organizer;
    private User user;
    private SolutionInstancesDto solutionInstancesDto;
    TrayMappingDTO mappingDTO;

    private Long id = 1L;
    private Long integrationId = 1L;
    private String solutionId = "11b781e3-4028-4152-a91c-41c959cd3074f";
    private String instanceId = "f920b843-c2b9-4854-a2e3-62160de805441";
    private String email = "<EMAIL>";
    private String mappingTriggerUrl = "https://9e7c68e8-d2cf-4c47-b1a9-cd121fba3d32.trayapp.io";
    private String mappingFields = "{\"Email\":\"Email\",\"First Name\":\"FirstName\",\"Prefix\":\"Salutation\",\"Last Name\":\"LastName\"}";


    @BeforeEach
    void setUp() throws Exception {
        trayIntegrationServiceImpl= null;
        MockitoAnnotations.openMocks(this);
        trayIntegration = new TrayIntegration();
        event = EventDataUtil.getEvent();
        eventTicket = EventDataUtil.getEventTickets();
        ticketingOrder = eventTicket.getTicketingOrder();
        eventTicket.setTicketingOrderId(ticketingOrder.getId());
        whiteLabel = new WhiteLabel(id, "URL");
        integration = new Integration();
        trayIntegrationMappings = new TrayIntegrationMappings();
        organizer = new Organizer();
        user = EventDataUtil.getUser();
        solutionInstancesDto = new SolutionInstancesDto(id, SALESFORCE);

        mappingDTO = new TrayMappingDTO();
        mappingDTO.setMappingFields(mappingFields);
    }

    @Test
    void test_saveTrayIntegration() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);

        //Execution
        trayIntegrationServiceImpl.saveTrayIntegration(trayIntegration);

        //Assertion
        ArgumentCaptor<TrayIntegration> trayIntegrationArgumentCaptor = ArgumentCaptor.forClass(TrayIntegration.class);
        verify(trayIntegrationRepository).save(trayIntegrationArgumentCaptor.capture());

        TrayIntegration saveTrayIntegration = trayIntegrationArgumentCaptor.getValue();
        assertEquals(trayIntegration.getId(), saveTrayIntegration.getId());
        assertEquals(trayIntegration.getInstanceName(), saveTrayIntegration.getInstanceName());
    }

    @Test
    void test_findAllByIntegrationId() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);

        //mock
        when(trayIntegrationRepository.findAllByIntegrationId(integrationId)).thenReturn(Collections.singletonList(trayIntegration));

        //Execution
        List<TrayIntegration> trayIntegrations =  trayIntegrationServiceImpl.findAllByIntegrationId(integrationId);

        //Assertion
        verify(trayIntegrationRepository).findAllByIntegrationId(integrationId);

        assertEquals(trayIntegration.getId(), trayIntegrations.get(0).getId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrations.get(0).getInstanceName());
    }

    @Test
    void test_findAll() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);

        //mock
        when(trayIntegrationRepository.findAll()).thenReturn(Collections.singletonList(trayIntegration));
        System.out.println(trayIntegrationRepository.hashCode());

        //Execution
        Iterable<TrayIntegration> trayIntegrations =  trayIntegrationServiceImpl.findAll();

        //Assertion
        verify(trayIntegrationRepository).findAll();

        assertTrue(trayIntegrations.iterator().hasNext());
        assertEquals(trayIntegration.getId(), trayIntegrations.iterator().next().getId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrations.iterator().next().getInstanceName());
    }

    @Test
    void test_findBySolutionIdAndIntegrationId() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setIntegrationId(integrationId);

        //mock
        when(trayIntegrationRepository.findBySolutionIdAndIntegrationId(solutionId, integrationId)).thenReturn(Optional.of(trayIntegration));

        //Execution
        Optional<TrayIntegration> trayIntegrationOptional = trayIntegrationServiceImpl.findBySolutionIdAndIntegrationId(solutionId, integrationId);

        //Assertion
        verify(trayIntegrationRepository).findBySolutionIdAndIntegrationId(solutionId, integrationId);

        assertTrue(trayIntegrationOptional.isPresent());
        assertEquals(trayIntegration.getId(), trayIntegrationOptional.get().getId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrationOptional.get().getInstanceName());
        assertEquals(trayIntegration.getSolutionId(), trayIntegrationOptional.get().getSolutionId());
        assertEquals(trayIntegration.getIntegrationId(), trayIntegrationOptional.get().getIntegrationId());
    }

    @Test
    void test_findByInstanceIdAndIntegrationId() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);
        trayIntegration.setInstanceId(instanceId);
        trayIntegration.setIntegrationId(integrationId);

        //mock
        when(trayIntegrationRepository.findByInstanceIdAndIntegrationId(instanceId, integrationId)).thenReturn(Optional.of(trayIntegration));

        //Execution
        Optional<TrayIntegration> trayIntegrationOptional = trayIntegrationServiceImpl.findByInstanceIdAndIntegrationId(instanceId, integrationId);

        //Assertion
        verify(trayIntegrationRepository).findByInstanceIdAndIntegrationId(instanceId, integrationId);

        assertTrue(trayIntegrationOptional.isPresent());
        assertEquals(trayIntegration.getId(), trayIntegrationOptional.get().getId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrationOptional.get().getInstanceName());
        assertEquals(trayIntegration.getInstanceId(), trayIntegrationOptional.get().getInstanceId());
        assertEquals(trayIntegration.getIntegrationId(), trayIntegrationOptional.get().getIntegrationId());
    }

    @Test
    void test_triggerTrayWebHookForTicketPurchase_NullWLAndNullOrganizer() { //NOSONAR
        //setup
        List<EventTickets> eventTickets = new ArrayList<>();
        eventTickets.add(eventTicket);

        //Execution
        trayIntegrationServiceImpl.triggerTrayWebHookForTicketPurchase(eventTickets, event, ticketingOrder, false);
    }

    @Test
    void test_triggerTrayWebHookForTicketPurchase_WLAndNullOrganizer() {
        //setup
        List<EventTickets> eventTickets = new ArrayList<>();
        eventTickets.add(eventTicket);
        event.setWhiteLabel(whiteLabel);

        //mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.triggerTrayWebHookForTicketPurchase(eventTickets, event, ticketingOrder, false);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
    }

    @Test
    void test_triggerTrayWebHookForTicketPurchase_WLAndNullOrganizer_IntegrationPresent_TrayIntegration_Empty() {
        //setup
        List<EventTickets> eventTickets = new ArrayList<>();
        eventTickets.add(eventTicket);
        event.setWhiteLabel(whiteLabel);
        integration.setId(id);

        //mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO)).thenReturn(Optional.of(integration));
        when(trayIntegrationRepository.findAllByIntegrationId(integration.getId())).thenReturn(Collections.emptyList());

        //Execution
        trayIntegrationServiceImpl.triggerTrayWebHookForTicketPurchase(eventTickets, event, ticketingOrder, false);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
        verify(trayIntegrationRepository).findAllByIntegrationId(integration.getId());
    }

    @Test
    void test_triggerTrayWebHookForUpdateHolder_NullWLAndNullOrganizer() { //NOSONAR
        when(mockTicketingOrderService.findByid(eventTicket.getTicketingOrderId())).thenReturn(ticketingOrder);
        //Execution
        trayIntegrationServiceImpl.triggerTrayWebHookForUpdateHolder(event, eventTicket, email);
    }

    @Test
    void test_processUserCheckIn_NullWLAndNullOrganizer() { //NOSONAR
        //Execution
        trayIntegrationServiceImpl.processUserCheckIn(event, user, 0L);
    }

    @Test
    void test_processUserCheckIn_WLAndNullOrganizer() {
        //setup
        event.setWhiteLabel(whiteLabel);

        //mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.processUserCheckIn(event, user, 0L);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
    }

    @Test
    void test_processUserCheckIn_WLAndNullOrganizer_IntegrationPresent() {
        //setup
        event.setWhiteLabel(whiteLabel);
        integration.setId(id);

        //mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO)).thenReturn(Optional.of(integration));
        when(trayIntegrationRepository.findAllByIntegrationId(integration.getId())).thenReturn(Collections.emptyList());
        //Execution
        trayIntegrationServiceImpl.processUserCheckIn(event, user, 0L);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
        verify(trayIntegrationRepository).findAllByIntegrationId(integration.getId());
    }

    @Test
    void test_processUserRefundOrCancelWorkflow_NullWLAndNullOrganizer() { //NOSONAR
        //Setup
        List<String> userSet = new ArrayList<>();
        userSet.add(user.getEmail());

        Set<Long> eventTicketList = new HashSet<>();
        eventTicketList.add(0L);

        //Execution
        trayIntegrationServiceImpl.processUserRefundOrCancelWorkflow(event, userSet, eventTicketList, 1, ticketingOrder.getId());
    }

    @Test
    void test_processUserRefundOrCancelWorkflow_WLAndNullOrganizer() {
        //setup
        event.setWhiteLabel(whiteLabel);
        List<String> userSet = new ArrayList<>();
        userSet.add(user.getEmail());

        Set<Long> eventTicketList = new HashSet<>();
        eventTicketList.add(0L);

        //mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.processUserRefundOrCancelWorkflow(event, userSet, eventTicketList, 10, ticketingOrder.getId());

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(event.getWhiteLabel().getId(), IntegrationSourceType.WHITE_LABEL, IntegrationType.TRAY_IO);
    }

    @Test
    void test_saveAllTrayIntegration() {
        //setup
        trayIntegration.setId(id);
        trayIntegration.setInstanceName(SALESFORCE);
        List<TrayIntegration> trayIntegrationList = new ArrayList<>();
        trayIntegrationList.add(trayIntegration);

        //Execution
        trayIntegrationServiceImpl.saveAllTrayIntegration(trayIntegrationList);

        //Assertion
        Class<ArrayList<TrayIntegration>> listClass = (Class<ArrayList<TrayIntegration>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<TrayIntegration>> arrayListArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(trayIntegrationRepository, times(1)).saveAll(arrayListArgumentCaptor.capture());

        List<TrayIntegration> saveTrayIntegrations = arrayListArgumentCaptor.getAllValues().get(0);
        assertEquals(trayIntegration.getId(), saveTrayIntegrations.get(0).getId());
        assertEquals(trayIntegration.getInstanceName(), saveTrayIntegrations.get(0).getInstanceName());
    }

    @Test
    void test_getAllEnabledIntegrations() {
        //Mock
        when(trayIntegrationRepository.getAllEnabledIntegrations(integrationId)).thenReturn(Collections.singletonList(solutionInstancesDto));

        //Execution
        List<SolutionInstancesDto> solutionInstancesDtos = trayIntegrationServiceImpl.getAllEnabledIntegrations(integrationId);

        //Assertion
        verify(trayIntegrationRepository).getAllEnabledIntegrations(integrationId);
        assertEquals(id, solutionInstancesDtos.get(0).getId());
        assertEquals(SALESFORCE, solutionInstancesDtos.get(0).getInstanceName());
    }

    @Test
    void test_getIntegrationMappingFields() {
        //Mock
        when(trayIntegrationRepository.findById(id)).thenReturn(Optional.empty());

        //Execution
        IntegrationFieldDto integrationFieldDto = trayIntegrationServiceImpl.getIntegrationMappingFields(id, event, user);

        //Assertion
        verify(trayIntegrationRepository).findById(id);
        assertNotNull(integrationFieldDto);
    }

    @Test
    void test_updateIntegrationMappingField() {
        //Setup
        trayIntegration.setMappingTriggerUrl(mappingTriggerUrl);

        //Mock
        when(trayIntegrationRepository.findById(id)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.updateIntegrationMappingField(id,  event, mappingDTO, user);

        //Assertion
        verify(trayIntegrationRepository).findById(id);
    }

    @Test
    void test_updateIntegrationMappingField_TrayIntegrationPresent() {
        //Setup
        trayIntegration.setMappingTriggerUrl(mappingTriggerUrl);

        //Mock
        when(trayIntegrationRepository.findById(id)).thenReturn(Optional.of(trayIntegration));
        when(trayIntegrationMappingRepository.findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId())).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.updateIntegrationMappingField(id,  event, mappingDTO, user);

        //Assertion
        verify(trayIntegrationRepository).findById(id);
        verify(trayIntegrationMappingRepository).findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId());

        ArgumentCaptor<TrayIntegrationMappings> trayIntegrationMappingArgumentCaptor = ArgumentCaptor.forClass(TrayIntegrationMappings.class);
        verify(trayIntegrationMappingRepository).save(trayIntegrationMappingArgumentCaptor.capture());

        TrayIntegrationMappings saveTrayIntegrationMappings = trayIntegrationMappingArgumentCaptor.getValue();
        assertEquals(event.getEventId(), saveTrayIntegrationMappings.getEventId());
        assertEquals(trayIntegration, saveTrayIntegrationMappings.getTrayIntegrationId());
        assertEquals(mappingFields, saveTrayIntegrationMappings.getIntegrationFieldsMapping());
    }

    @Test
    void test_updateIntegrationMappingField_TrayIntegrationPresent_TrayIntegrationMappingPresent() {
        //Setup
        trayIntegrationMappings.setEventId(event.getEventId());
        trayIntegrationMappings.setTrayIntegrationId(trayIntegration);

        //Mock
        when(trayIntegrationRepository.findById(id)).thenReturn(Optional.of(trayIntegration));
        when(trayIntegrationMappingRepository.findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId())).thenReturn(Optional.of(trayIntegrationMappings));

        //Execution
        trayIntegrationServiceImpl.updateIntegrationMappingField(id,  event, mappingDTO, user);

        //Assertion
        verify(trayIntegrationRepository).findById(id);
        verify(trayIntegrationMappingRepository).findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId());

        ArgumentCaptor<TrayIntegrationMappings> trayIntegrationMappingArgumentCaptor = ArgumentCaptor.forClass(TrayIntegrationMappings.class);
        verify(trayIntegrationMappingRepository).save(trayIntegrationMappingArgumentCaptor.capture());

        TrayIntegrationMappings saveTrayIntegrationMappings = trayIntegrationMappingArgumentCaptor.getValue();
        assertEquals(event.getEventId(), saveTrayIntegrationMappings.getEventId());
        assertEquals(trayIntegration, saveTrayIntegrationMappings.getTrayIntegrationId());
        assertEquals(mappingFields, saveTrayIntegrationMappings.getIntegrationFieldsMapping());
    }

    @Test
    void test_findAllByInstanceNameAndMappingTriggerUrl() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findAllByInstanceNameIgnoreCaseAndMappingTriggerUrlAndSolutionId(SALESFORCE, Constants.STRING_EMPTY, solutionId)).thenReturn(Collections.singletonList(trayIntegration));

        //Execution
        List<TrayIntegration> trayIntegrations = trayIntegrationServiceImpl.findAllByInstanceNameAndMappingTriggerUrl(solutionId, SALESFORCE);

        //Assertion
        verify(trayIntegrationRepository).findAllByInstanceNameIgnoreCaseAndMappingTriggerUrlAndSolutionId(SALESFORCE, Constants.STRING_EMPTY, solutionId);
        assertEquals(trayIntegration.getSolutionId(), trayIntegrations.get(0).getSolutionId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrations.get(0).getInstanceName());
    }

    @Test
    void test_findByInstanceName() {
        //Setup
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findFirstByInstanceNameIgnoreCase(SALESFORCE)).thenReturn(Optional.of(trayIntegration));

        //Execution
        Optional<TrayIntegration> trayIntegrationOptional = trayIntegrationServiceImpl.findByInstanceName(SALESFORCE);

        //Assertion
        verify(trayIntegrationRepository).findFirstByInstanceNameIgnoreCase(SALESFORCE);
        assertEquals(trayIntegration.getInstanceName(), trayIntegrationOptional.get().getInstanceName());
    }

    @Test
    void test_findAllByInstanceNameAndSolutionId() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findAllByInstanceNameIgnoreCaseAndSolutionId(SALESFORCE, solutionId)).thenReturn(Collections.singletonList(trayIntegration));

        //Execution
        List<TrayIntegration> trayIntegrations = trayIntegrationServiceImpl.findAllByInstanceNameAndSolutionId(solutionId, SALESFORCE);

        //Assertion
        verify(trayIntegrationRepository).findAllByInstanceNameIgnoreCaseAndSolutionId(SALESFORCE, solutionId);
        assertEquals(trayIntegration.getSolutionId(), trayIntegrations.get(0).getSolutionId());
        assertEquals(trayIntegration.getInstanceName(), trayIntegrations.get(0).getInstanceName());
    }

    @Test
    void test_getTrayIntegrationMappingFields_TrayIntegration_NotFound() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId)).thenReturn(Optional.empty());
        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> trayIntegrationServiceImpl.getTrayIntegrationMappingFields(event, SALESFORCE, integrationId));

        assertEquals(NotFoundException.NotFound.TRAY_INTEGRATION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

        //Assertion
        verify(trayIntegrationRepository).findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId);
    }

    @Test
    void test_getTrayIntegrationMappingFields_TrayIntegration_Success() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId)).thenReturn(Optional.of(trayIntegration));
        when(trayIntegrationMappingRepository.findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId())).thenReturn(Optional.empty());

        //Execution
        String mappings =  trayIntegrationServiceImpl.getTrayIntegrationMappingFields(event, SALESFORCE, integrationId);

        //Assertion
        verify(trayIntegrationRepository).findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId);
        verify(trayIntegrationMappingRepository).findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId());
        assertEquals(SALESFORCE_DEFAULT_MAPPING, mappings);
    }

    @Test
    void test_getTrayIntegrationMappingFields_TrayIntegration_TrayIntegrationMappingPresent_Success() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);

        //Mock
        when(trayIntegrationRepository.findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId)).thenReturn(Optional.of(trayIntegration));
        when(trayIntegrationMappingRepository.findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId())).thenReturn(Optional.of(trayIntegrationMappings));

        //Execution
        String mappings =  trayIntegrationServiceImpl.getTrayIntegrationMappingFields(event, SALESFORCE, integrationId);

        //Assertion
        verify(trayIntegrationRepository).findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId);
        verify(trayIntegrationMappingRepository).findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId());
        assertEquals(SALESFORCE_DEFAULT_MAPPING, mappings);
    }

    @Test
    void test_getTrayIntegrationMappingFields_TrayIntegration_TrayIntegrationMappingPresent_WithMapping_Success() {
        //Setup
        trayIntegration.setSolutionId(solutionId);
        trayIntegration.setInstanceName(SALESFORCE);
        trayIntegrationMappings.setIntegrationFieldsMapping(mappingFields);

        //Mock
        when(trayIntegrationRepository.findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId)).thenReturn(Optional.of(trayIntegration));
        when(trayIntegrationMappingRepository.findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId())).thenReturn(Optional.of(trayIntegrationMappings));

        //Execution
        String mappings =  trayIntegrationServiceImpl.getTrayIntegrationMappingFields(event, SALESFORCE, integrationId);

        //Assertion
        verify(trayIntegrationRepository).findByInstanceNameIgnoreCaseAndIntegrationId(SALESFORCE, integrationId);
        verify(trayIntegrationMappingRepository).findByTrayIntegrationIdAndEventId(trayIntegration, event.getEventId());
        assertEquals(trayIntegrationMappings.getIntegrationFieldsMapping(), mappings);
    }

    @Test
    void test_updateOrganizerToWLIntegration() {
        //Setup
        organizer.setId(id);

        //Mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.updateOrganizerToWLIntegration(organizer, whiteLabel);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER);
    }

    @Test
    void test_updateOrganizerToWLIntegration_OrgIntegrationPresent() {
        //Setup
        organizer.setId(id);

        //Mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER)).thenReturn(Optional.of(integration));
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationType(whiteLabel.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.WHITE_LABEL)).thenReturn(Optional.empty());

        //Execution
        trayIntegrationServiceImpl.updateOrganizerToWLIntegration(organizer, whiteLabel);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER);
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationType(whiteLabel.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.WHITE_LABEL);

        ArgumentCaptor<Integration> integrationArgumentCaptor = ArgumentCaptor.forClass(Integration.class);
        verify(integrationService).save(integrationArgumentCaptor.capture());

        Integration saveOrgIntegration = integrationArgumentCaptor.getValue();
        assertEquals(whiteLabel.getId(), saveOrgIntegration.getIntegrationSourceId().longValue());
        assertEquals(IntegrationSourceType.WHITE_LABEL, saveOrgIntegration.getSourceType());
    }

    @Test
    void test_updateOrganizerToWLIntegration_OrgAndWLIntegrationPresent() {
        //Setup
        organizer.setId(id);
        integration.setId(1L);
        Integration wlIntegration = new Integration();
        wlIntegration.setId(2L);
        List<TrayIntegration> orgTrayIntegrationList = new ArrayList<>();
        trayIntegration.setInstanceName(SALESFORCE);
        orgTrayIntegrationList.add(trayIntegration);
        TrayIntegration orgTrayIntegration = new TrayIntegration();
        orgTrayIntegration.setInstanceName(MARKETO);
        orgTrayIntegrationList.add(orgTrayIntegration);
        List<TrayIntegration> wlTrayIntegrationList = new ArrayList<>();
        TrayIntegration wltrayIntegration = new TrayIntegration();
        wltrayIntegration.setInstanceName(SALESFORCE);
        wlTrayIntegrationList.add(wltrayIntegration);


        //Mock
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER)).thenReturn(Optional.of(integration));
        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationType(whiteLabel.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.WHITE_LABEL)).thenReturn(Optional.of(wlIntegration));
        when(trayIntegrationRepository.findAllByIntegrationId(integration.getId())).thenReturn(orgTrayIntegrationList);
        when(trayIntegrationRepository.findAllByIntegrationId(wlIntegration.getId())).thenReturn(wlTrayIntegrationList);
        doNothing().when(trayIntegrationMappingRepository).updateTrayIntegrationIdWithWLTrayIntegrationId(trayIntegration, wltrayIntegration);

        //Execution
        trayIntegrationServiceImpl.updateOrganizerToWLIntegration(organizer, whiteLabel);

        //Assertion
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationType(organizer.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.ORGANIZER);
        verify(integrationService).getByIntegrationSourceIdAndSourceTypeAndIntegrationType(whiteLabel.getId(), IntegrationType.TRAY_IO, IntegrationSourceType.WHITE_LABEL);
        verify(trayIntegrationRepository).findAllByIntegrationId(integration.getId());
        verify(trayIntegrationRepository).findAllByIntegrationId(wlIntegration.getId());
        verify(trayIntegrationMappingRepository).updateTrayIntegrationIdWithWLTrayIntegrationId(trayIntegration, wltrayIntegration);

        ArgumentCaptor<Integration> integrationArgumentCaptor = ArgumentCaptor.forClass(Integration.class);
        verify(integrationService).save(integrationArgumentCaptor.capture());

        Integration saveOrgIntegration = integrationArgumentCaptor.getValue();
        assertEquals(RecordStatus.DELETE, saveOrgIntegration.getRecordStatus());
    }
}
