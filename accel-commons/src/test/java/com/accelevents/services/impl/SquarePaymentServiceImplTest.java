
package com.accelevents.services.impl;

//@ExtendWith(MockitoExtension.class)
public class SquarePaymentServiceImplTest {

/*    @Spy
    @InjectMocks
    private SquarePaymentServiceImpl squarePaymentServiceImpl = new SquarePaymentServiceImpl();

    @Mock
    private SquareConfiguration squareConfiguration;

    @Mock
    private ListLocationsResponse listLocationsResponse;

    @Mock
    private StripeRepository stripeRepository;

   *//* @Mock
    private SquarePaymentService squarePaymentService;*//*

    @Mock
    private EventService eventService;

    @Mock
    private TransactionsApi transactionsApi;

    @Mock
    private ChargeResponse chargeResponse;

    @Mock
    private ChargeRequest chargeRequest;

    private Location location;
    private ChargeDto chargeDto;
    private Stripe stripe;
    private Charge charge;
    //private ChargeResponse chargeResponse;
    // private ChargeRequest chargeRequest;
    private CustomerCardDto customerCardDto;
    private User user;
    private Event event;
    private Card card;
    private RefundInfoDto refundInfoDto;
    private FeeRefund feeRefund;
    private Account account;
    private StripeCreditCardDto stripeCreditCardDto;
    private CardInfoDto cardInfoDto;
    private StripeTransaction stripeTransaction;
    private StripeCustomers stripeCustomers;
    private Transaction transaction;
    private Tender tender;
    private Customer customer;
    private com.squareup.connect.models.Money money;
    private Subscription subscription;
    private Coupon coupon;
    private Plan plan;
    private ExternalAccountCollection externalAccountCollection;

    private String customerId = "cus_FSjuTT9VAXQn8x";
    private String stripeAccessToken = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private String successStatus = "succeeded";
    private String cardId = "card_1ExoPaLm9ntTNZl0mVfh9vY1";
    private String cardNonce = "CBASECZyZ3eFJXUp0-KJAufzOUggAQ";
    private String currency = Currency.USD.toString();
    private String statementDescriptor = "1SDR";
    private String locationId = "CBASEGMgZfZldaslqzMCk33YnHggAQ";
    private String tenderId = "tender Id";
    private String id = "1";
    private String token = "tok_br";
    private String email = "<EMAIL>";
    private String description = "description";
    private String lastFour = "1111";
    private String accessToken = "accessToken";
    private String chargeId = "ch_1ExooeLm9ntTNZl0jQxqRc5F";
    private String stripeUserId = "1";
    private String cardType = "Visa";
    private String tokenOrCardNonce = "tokenOrCardNonce";
    private String refundStatus = "refunded";
    private String reason = "duplicate";
    private String apiKey = "sandbox-sq0atb-9WqiKmejeAoJfNTLDNBpWQ";
    private int limit = 1;
    private double amount = 100d;
    private double applicationFee = 1d;
    private double applicationFeeRefundAmount = 1d;
    private double ccPercentageFee = credit_card_processing_percentage;
    private double ccFlatFee = credit_card_processing_flat;
    private String ALPHA_NUMERIC_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";


    @BeforeEach
    public void setUp() throws Exception {
        PowerMockito.mockStatic(TransactionsApi.class);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

        stripe = new Stripe();

        charge = new Charge();

        chargeDto = new ChargeDto();

        chargeResponse = new ChargeResponse();

        chargeRequest = new ChargeRequest();

        transaction = new Transaction();

        tender = new Tender();

        card = new Card();

        customer = new Customer();

        feeRefund = new FeeRefund();

        refundInfoDto = new RefundInfoDto();

        account = new Account();

        stripeCreditCardDto = new StripeCreditCardDto();

        stripeCustomers = new StripeCustomers();

        subscription = new Subscription();

        coupon = new Coupon();

        plan = new Plan();

        location = new Location();
    }

    @Test
    public void test_log_success() {

        //setup
        String logInfo = "log info";

        //Execution
        squarePaymentServiceImpl.log(logInfo);
    }


    *//*@Test
    public void test_createChargeToCustomer_success() throws ApiException {

        //setup
        location.setId(locationId);
        String apiKey = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";
        String customerId = "CBASEDpyQVCPjk-mxt-Nu2ffneEgAQ";
        String cardId = "ccof:c17025d7-69c2-5fb6-b9d6-975ee1419f3e";

        ApiClient apiClient = new ApiClient();
        apiClient.setAccessToken(apiKey);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.STRING_EMAIL, email);

        stripe.setAccessToken(apiKey);

        ChargeRequest body = new ChargeRequest();
        body.setCustomerId(customerId);

        transaction.setId(id);
        transaction.setLocationId(locationId);

        chargeResponse.setTransaction(transaction);

        //mock
        when(squareConfiguration.isSquareSandBoMode()).thenReturn(true);
        when(squareConfiguration.getAeLocationId()).thenReturn(locationId);
        when(squarePaymentServiceImpl.getClientApi(apiKey)).thenReturn(apiClient);
        when(transactionsApi.charge(locationId, body)).thenReturn(chargeResponse);

        //Execution
        ChargeResponse chargeData = squarePaymentServiceImpl.createChargeToCustomer(stripe, customerId, amount, applicationFee, currency, true, cardId, metadata);

        assertEquals(chargeData.getTransaction().getLocationId(), locationId);
        assertEquals(chargeData.getTransaction().getTenders().listIterator(0).next().getCustomerId(), customerId);
        assertEquals(chargeData.getTransaction().getTenders().listIterator(0).next().getCardDetails().getCard().getId(), cardId);
    }*//*


    @Test
    public void test_getAdditionalRecipientsAsAe_success() {

        //setup
        String description = "AE Application Fee";

        //mock
        when(squareConfiguration.isSquareSandBoMode()).thenReturn(false);
        when(squareConfiguration.getAeLocationId()).thenReturn(locationId);

        //Execution
        List<AdditionalRecipient> additionalRecipientsAsAeData = squarePaymentServiceImpl.getAdditionalRecipientsAsAe(applicationFee, currency);

        assertEquals(additionalRecipientsAsAeData.iterator().next().getDescription(), description);
        assertEquals(additionalRecipientsAsAeData.iterator().next().getLocationId(), locationId);
    }

    @Test
    public void test_getAdditionalRecipientsAsAe_squareSandBoMode_true() {

        //setup
        double applicationFee = 0d;

        //Execution
        List<AdditionalRecipient> additionalRecipientsAsAeData = squarePaymentServiceImpl.getAdditionalRecipientsAsAe(applicationFee, currency);

        assertEquals(additionalRecipientsAsAeData, Collections.EMPTY_LIST);
    }

    @Test
    public void test_getLocationId_success() throws ApiException {

        //Execution
        Location locationData = squarePaymentServiceImpl.getLocationId(apiKey);

        assertEquals(locationData.getId(), locationId);
    }

    @Test
    public void test_getLocationId_success1() throws ApiException {

        //Execution
        Location locationData = squarePaymentServiceImpl.getLocationId(apiKey);

        assertEquals(locationData.getId(), locationId);
    }

    @Test
    public void test_getLocationId_throwException_LOCATION_NOT_FOUND() {

        //setup
        String apiKey = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";

        //Execution
        squarePaymentServiceImpl.getLocationId(apiKey);
    }


    @Test
    public void createCharge() throws ApiException {

        //setup
        location.setId(locationId);
        String apiKey = "sandbox-sq0atb-9WqiKmejeAoJfNTLDNBpWQ";
        String customerId = "CBASEIE5y2rkqiyzfNxnO7SzbqsgAQ";
        String cardId = "a132439f-fad6-5eef-68de-613bf02e6ac6";

        ApiClient apiClient = new ApiClient();
        apiClient.setAccessToken(apiKey);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.STRING_EMAIL, email);

        stripe.setAccessToken(apiKey);

        //mock
        when(squareConfiguration.isSquareSandBoMode()).thenReturn(false);
        when(squareConfiguration.getAeLocationId()).thenReturn(locationId);
        when(squarePaymentServiceImpl.getClientApi(apiKey)).thenReturn(apiClient);

        //Execution
        ChargeResponse chargeData = squarePaymentServiceImpl.createCharge(apiKey, customerId, cardId, amount, currency, metadata);

        assertNotNull(chargeData);
        assertEquals(chargeData.getTransaction().getLocationId(), locationId);
    }




   *//* @Test
    public void test_createCustomer_success() throws ApiException{                  //cardNonce not found

        //setup
        String email = "<EMAIL>";
        Map<String, Object> metadata = new HashMap<String, Object>();
		metadata.put(Constants.STRING_EMAIL, email);

        //String apiKey = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";
        String customerId = "CBASEDpyQVCPjk-mxt-Nu2ffneEgAQ";
        String cardId = "ccof:c17025d7-69c2-5fb6-b9d6-975ee1419f3e";

        String cardNonce = "cnon:card-nonce-ok";

        //Execution
        CustomerCardDto customerData = squarePaymentServiceImpl.createCustomerAndCardToCustomer(apiKey, cardNonce, metadata, email, description);
    }*//*



    @Test
    public void test_createCustomer1_success() throws ApiException{

        //Execution
        com.squareup.connect.models.Customer customerData = squarePaymentServiceImpl.createCustomer(apiKey, email);

        assertEquals(customerData.getEmailAddress(), email);
    }

    @Test
    public void test_getAllCardsForCustomer_success() throws ApiException{

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String cardType = "VISA";
        String lastFour = "1111";

        //Execution
        List<StripeCreditCardDto> cardData = squarePaymentServiceImpl.getAllCardsForCustomer(apiKey, customerId);

        assertEquals(cardData.iterator().next().getCardType(), cardType);
        assertEquals(cardData.iterator().next().getLast4(), lastFour);
    }

    @Test
    public void test_getAllCards_success() throws ApiException{

        //setup
        String customerId = "CBASEFnwjxuctpwLqmOoZ1C7CkEgAQ";
        String cardType = "VISA";
        String lastFour = "4242";

        //Execution
        List<com.squareup.connect.models.Card> cardData = squarePaymentServiceImpl.getAllCards(apiKey, customerId);

        assertEquals(cardData.iterator().next().getLast4(), lastFour);
        assertEquals(cardData.iterator().next().getCardBrand().toString(), cardType);
    }

    @Test
    public void test_getLast4Digits_success() {

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String lastFour = "1111";

        //Execution
        String lastFourDigit = squarePaymentServiceImpl.getLast4Digits(apiKey, customerId);

        assertEquals(lastFourDigit, lastFour);
    }

    @Test
    public void test_getLast4Digits_getEmptyData() {

        //setup
        String customerId = "CBASEGMgZfZldaslqzMCk33YnHggAQ";
        String lastFour = "";

        //Execution
        try{
            String lastFourDigit = squarePaymentServiceImpl.getLast4Digits(apiKey, customerId);

            assertEquals(lastFourDigit, lastFour);
        } catch(Exception e){}

    }



   *//* @Test
    public void addCardToCustomer() throws ApiException{            // cardNonce not fond

        //setup
        String customerId = "CBASEIE5y2rkqiyzfNxnO7SzbqsgAQ";
        String cardNonce = "a132439f-fad6-5eef-68de-613bf02e6ac6";
        String apiKey = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";

        String cardId = squarePaymentServiceImpl.addCardToCustomer(apiKey, customerId, cardNonce, true,true);
    }*//*



    @Test
    public void test_retrieveDefaultCardOfCustomer_success() throws ApiException{

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String lastFour = "1111";
        String cardBrand = com.squareup.connect.models.Card.CardBrandEnum.VISA.toString();

        //Execution
        com.squareup.connect.models.Card customerData = squarePaymentServiceImpl.retrieveDefaultCardOfCustomer(apiKey, customerId);

        assertEquals(customerData.getLast4(), lastFour);
        assertEquals(customerData.getCardBrand().toString(), cardBrand);
    }

    @Test
    public void test_createRefund_success() throws ApiException{

        location.setId(locationId);
        String apiKey = "sandbox-sq0atb-9WqiKmejeAoJfNTLDNBpWQ";
        String customerId = "CBASEIE5y2rkqiyzfNxnO7SzbqsgAQ";
        String cardId = "a132439f-fad6-5eef-68de-613bf02e6ac6";

        ApiClient apiClient = new ApiClient();
        apiClient.setAccessToken(apiKey);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.STRING_EMAIL, email);

        stripe.setAccessToken(apiKey);

        //mock
        when(squareConfiguration.isSquareSandBoMode()).thenReturn(false);
        when(squareConfiguration.getAeLocationId()).thenReturn(locationId);
        when(squarePaymentServiceImpl.getClientApi(apiKey)).thenReturn(apiClient);

        //Execution
        ChargeResponse chargeData = squarePaymentServiceImpl.createCharge(apiKey, customerId, cardId, amount, currency, metadata);

        //setup
        String chargeId = chargeData.getTransaction().getId();
        double amount = 1d;
        String squareLocationId = chargeData.getTransaction().getLocationId();
        String squareTenderId = chargeData.getTransaction().getTenders().iterator().next().getId();
        String lastFour = chargeData.getTransaction().getTenders().iterator().next().getCardDetails().getCard().getLast4();
        String cardBrand = com.squareup.connect.models.Card.CardBrandEnum.VISA.toString();
        String refundStatus = Refund.StatusEnum.APPROVED.toString();

        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setCardType(cardBrand);
        stripeTransaction.setLastFour(lastFour);
        stripeTransaction.setChargeid(chargeId);
        stripeTransaction.setSquareLocationId(squareLocationId);
        stripeTransaction.setSquareTenderId(squareTenderId);


        Map<String, String> metadata1 = new HashMap<>();

        //Execution
        Refund refundData = squarePaymentServiceImpl.createRefund(apiKey, stripeTransaction, currency, amount, metadata1, reason, true);

        assertEquals(refundData.getStatus().toString(), refundStatus);
        assertEquals(refundData.getLocationId(), squareLocationId);
        assertEquals(refundData.getTenderId(), squareTenderId);
        assertEquals(refundData.getReason(), reason);
        assertEquals(refundData.getAmountMoney().getCurrency().toString(), currency);
    }

    @Test
    public void test_getDefaultSource_success() {

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String defaultSource = "b38df8f7-716f-55ba-4820-9d20edb5a329";

        //Execution
        String defaultSourceData = squarePaymentServiceImpl.getDefaultSource(apiKey, customerId);

        assertEquals(defaultSourceData, defaultSource);
    }

    @Test
    public void test_getDefaultSource_throwException_and_return_emptyString() {

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String apiKey = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";

        //Execution
        String defaultSourceData = squarePaymentServiceImpl.getDefaultSource(apiKey, customerId);
        assertEquals(defaultSourceData, Constants.STRING_EMPTY);
    }


    @Test
    public void test_updateDefaultCard_success()  throws ApiException{

        //setup
        String customerId = "CBASEHDXq0LZ06urkyHD6r9wj-AgAQ";
        String cardId = "b38df8f7-716f-55ba-4820-9d20edb5a329";

        //Execution
        squarePaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId);
    }



    *//*@Test
    public void addCardToCustomer1() throws ApiException{               //card nonce not found

        //setup
        String customerId = "CBASEIE5y2rkqiyzfNxnO7SzbqsgAQ";
        String cardNonce = "a132439f-fad6-5eef-68de-613bf02e6ac6";
        String apiKey = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";

        //Execution
        com.squareup.connect.models.Card customerData = squarePaymentServiceImpl.addCardToCustomer(apiKey, customerId, cardNonce, false);
    }*//*



    @Test
    public void test_activateSquareAccount_success() {

        //setup
        String accessToken = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";
        String merchantLocation = "SANDBOX";

        stripe.setAccessToken(accessToken);
        stripe.setSquareMerchantLocation(merchantLocation);
        stripe.setEvent(event);

        List<Stripe> squareAccounts = new ArrayList<>();
        squareAccounts.add(stripe);

        List<Location.CapabilitiesEnum> capabilitiesEnumList = new ArrayList<>();
        capabilitiesEnumList.add(Location.CapabilitiesEnum.PROCESSING);

        Location location = new Location();
        location.setStatus(Location.StatusEnum.ACTIVE);
        location.setCapabilities(capabilitiesEnumList);

        //mock
        when(stripeRepository.findAllByPaymentGatewayAndNotActivated(anyString())).thenReturn(squareAccounts);
        Mockito.doNothing().when(eventService).enablePaymentProcessing(stripe, event);

        //Execution
        squarePaymentServiceImpl.activateSquareAccount();
    }

    @Test
    public void test_activateSquareAccount_throwException_NotFoundException() {

        //setup
        String accessToken = "sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";
        String merchantLocation = "SANDBOX";

        stripe.setAccessToken(accessToken);
        stripe.setSquareMerchantLocation(merchantLocation);
        stripe.setEvent(event);

        List<Stripe> squareAccounts = new ArrayList<>();
        squareAccounts.add(stripe);

        List<Location.CapabilitiesEnum> capabilitiesEnumList = new ArrayList<>();
        capabilitiesEnumList.add(Location.CapabilitiesEnum.PROCESSING);

        Location location = new Location();
        location.setStatus(Location.StatusEnum.ACTIVE);
        location.setCapabilities(capabilitiesEnumList);

        //mock
        when(stripeRepository.findAllByPaymentGatewayAndNotActivated(anyString())).thenReturn(squareAccounts);
        Mockito.doThrow(NotFoundException.class).when(eventService).enablePaymentProcessing(stripe, event);

        //Execution
        squarePaymentServiceImpl.activateSquareAccount();
    }*/
}

