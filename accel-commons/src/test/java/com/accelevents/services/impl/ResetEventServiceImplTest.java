package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Organizer;
import com.accelevents.domain.User;
import com.accelevents.networking.services.NetworkingLoungeService;
import com.accelevents.services.*;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.utils.Constants;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ResetEventServiceImplTest {

    @Spy
    @InjectMocks
    private  ResetEventServiceImpl resetEventServiceImpl;

    @Mock
    private SessionService sessionService;
    @Mock
    private TicketingService ticketingService;
    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;
    @Mock
    private TicketingCouponService ticketingCouponService;
    @Mock
    private AttendeeAnalyticsService attendeeAnalyticsService;
    @Mock
    private EventService eventService;
    @Mock
    private AttendeeProfileService attendeeProfileService;
    @Mock
    private ContactService contactService;
    @Mock
    private AuctionService auctionService;
    @Mock
    private CauseAuctionService causeAuctionService;
    @Mock
    private RaffleService raffleService;
    @Mock
    private DonationService donationService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private ChallengeConfigService challengeConfigService;
    @Mock
    private FeedService feedService;
    @Mock
    private QuestionsAnswersService questionsAnswersService;
    @Mock
    private PollsQuestionsAnswersService pollsQuestionsAnswersService;
    @Mock
    private UserService userService;
    @Mock
    private  VirtualEventPortalService virtualEventPortalService;
    @Mock
    private GetStreamService getStreamService;
    @Mock
    private NetworkingLoungeService networkingLoungeService;
    @Mock
    private UserActivityService userActivityService;
    @Mock
    private ResetEventDetailsService resetEventDetailsService;

    private Event event;

    private User user;

    private Organizer organizer;

    @BeforeEach
    void setUp() throws Exception {
        organizer = new Organizer();
        organizer.setId(1L);

        event = EventDataUtil.getEvent();
        event.setOrganizer(organizer);
        event.setOrganizerId(organizer.getId());

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());
    }

    @Test
    void test_resetEventData_success1_profile_stage() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = new ArrayList<>();
        eventListByIdsAndOrganiserId.add(event);
        String profile = "stage";

        //mock
        when(eventService.getEventListByIdsAndOrganiserId(anyList(), anyLong())).thenReturn(eventListByIdsAndOrganiserId);
        when(userService.findById(user.getUserId())).thenReturn(user);
        when(virtualEventPortalService.getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList());
        when(virtualEventPortalService.getStreamChannelIdsByEventId(any())).thenReturn(Arrays.asList());
        doNothing().when(getStreamService).truncateAllChannelsOfEvent(any(), any(), anyList());

        //execution
        resetEventServiceImpl.resetEventData(String.valueOf(event.getEventId()), 7, organizer.getId(), profile);

        //assertion
        verify(ticketingService).resetEventDates(anyList(),anyInt());
        verify(ticketingAccessCodeService).resetAccessCodeDates(anyList(),anyInt());
        verify(ticketingTypeService).resetTicketTypesDateByDays(anyList(),anyInt());
        verify(ticketingCouponService).resetCouponDates(anyList(),anyInt());
        verify(attendeeAnalyticsService).deleteAttendeeAnalyticsDataForEvent(any());
        verify(questionsAnswersService).updateDeleteStatusForAllQNAByEvent(any());
        verify(pollsQuestionsAnswersService).updateDeleteStatusForAllPollsByEvent(any());
        verify(userService).findById(anyLong());
        verify(virtualEventPortalService).getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
        verify(virtualEventPortalService).getStreamChannelIdsByEventId(any());
        verify(getStreamService).truncateAllChannelsOfEvent(any(), any(), anyList());
        verify(resetEventDetailsService).addResetEventDetails(any());
    }

    @Test
    void test_resetEventData_success2_profile_Dev() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = new ArrayList<>();
        eventListByIdsAndOrganiserId.add(event);
        String profile = "dev";

        //mock
        when(eventService.getEventListByIdsAndOrganiserId(anyList(), anyLong())).thenReturn(eventListByIdsAndOrganiserId);
        when(userService.findById(user.getUserId())).thenReturn(user);
        when(virtualEventPortalService.getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList());
        when(virtualEventPortalService.getStreamChannelIdsByEventId(any())).thenReturn(Arrays.asList());
        doNothing().when(getStreamService).truncateAllChannelsOfEvent(any(), any(), anyList());

        //execution
        resetEventServiceImpl.resetEventData(String.valueOf(event.getEventId()), 7, organizer.getId(), profile);

        //assertion
        verify(attendeeProfileService).resetAttendeeDetails(anyList());
        verify(contactService).deleteContactsByEventId(anyLong());
        verify(auctionService).resetAuctionDataByDays(anyList(),anyInt(), anyBoolean());
        verify(causeAuctionService).resetCauseAuctionDataByDays(anyList(),anyInt(), anyBoolean());
        verify(raffleService).resetRaffleDataByDays(anyList(),anyInt(), anyBoolean());
        verify(donationService).deleteByEventIdIn(anyList());
        verify(challengeConfigService).resetChallengeDates(anyList(),anyInt());
        verify(sessionService).resetSessionDates(anyList(),anyInt());
        verify(feedService).removeFeedByForeignId(anyString());
        verify(feedService).removeLoungesFeedsByEventIds(anyList());
        verify(networkingLoungeService).removeNetworkingLoungePhotoByEventIds(anyList());
        verify(networkingLoungeService).removeNetworkingLoungeVideoByEventIds(anyList());
        verify(userActivityService).deleteUserActivity(anyList());
        verify(resetEventDetailsService).completeResetEventDetails(any());
    }

    @Test
    void test_resetEventData_no_event_found_by_wrong_orgId_or_eventId1() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = new ArrayList<>();
        String profile = "stage";

        //mock
        when(eventService.getEventListByIdsAndOrganiserId(anyList(), anyLong())).thenReturn(eventListByIdsAndOrganiserId);

        //execution
        resetEventServiceImpl.resetEventData("485", 7, 38L,profile);

        //assertion
        verify(ticketingService, never()).resetEventDates(anyList(),anyInt());
        verify(ticketingAccessCodeService, never()).resetAccessCodeDates(anyList(),anyInt());
        verify(ticketingTypeService, never()).resetTicketTypesDateByDays(anyList(),anyInt());
        verify(ticketingCouponService, never()).resetCouponDates(anyList(),anyInt());
        verify(attendeeAnalyticsService, never()).deleteAttendeeAnalyticsDataForEvent(any());
        verify(questionsAnswersService, never()).updateDeleteStatusForAllQNAByEvent(any());
        verify(pollsQuestionsAnswersService, never()).updateDeleteStatusForAllPollsByEvent(any());
        verify(userService, never()).findById(anyLong());
        verify(virtualEventPortalService, never()).getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
        verify(virtualEventPortalService, never()).getStreamChannelIdsByEventId(any());
        verify(getStreamService, never()).truncateAllChannelsOfEvent(any(), any(), anyList());
        verify(resetEventDetailsService, never()).addResetEventDetails(any());
    }

    @Test
    void test_resetEventData_no_event_found_by_wrong_orgId_or_eventId2() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = new ArrayList<>();
        String profile = "stage";

        //mock
        when(eventService.getEventListByIdsAndOrganiserId(anyList(), anyLong())).thenReturn(eventListByIdsAndOrganiserId);

        //execution
        resetEventServiceImpl.resetEventData("485", 7, 38L,profile);

        //assertion
        verify(attendeeProfileService, never()).resetAttendeeDetails(anyList());
        verify(contactService, never()).deleteContactsByEventId(anyLong());
        verify(auctionService, never()).resetAuctionDataByDays(anyList(),anyInt(), anyBoolean());
        verify(causeAuctionService, never()).resetCauseAuctionDataByDays(anyList(),anyInt(), anyBoolean());
        verify(raffleService, never()).resetRaffleDataByDays(anyList(),anyInt(), anyBoolean());
        verify(donationService, never()).deleteByEventIdIn(anyList());
        verify(ticketingAccessCodeService, never()).resetAccessCodeDates(anyList(),anyInt());
        verify(challengeConfigService, never()).resetChallengeDates(anyList(),anyInt());
        verify(sessionService, never()).resetSessionDates(anyList(),anyInt());
        verify(feedService, never()).removeFeedByForeignId(anyString());
        verify(feedService, never()).removeLoungesFeedsByEventIds(anyList());
        verify(networkingLoungeService, never()).removeNetworkingLoungePhotoByEventIds(anyList());
        verify(networkingLoungeService, never()).removeNetworkingLoungeVideoByEventIds(anyList());
        verify(userActivityService, never()).deleteUserActivity(anyList());
        verify(resetEventDetailsService, never()).completeResetEventDetails(any());
    }

    @Test
    void eventNotResetWhenEnvironmentIsProdAndEventIdDoesNotBelongToAllowedEventsForProd() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = Lists.newArrayList(event);

        String profile = Constants.ENV_PROD;

        //mock


        //execution
        resetEventServiceImpl.resetEventData("485", 7, 9245L,profile);

        //assertion
        verify(ticketingService, never()).resetEventDates(anyList(),anyInt());
        verify(ticketingAccessCodeService, never()).resetAccessCodeDates(anyList(),anyInt());
        verify(ticketingTypeService, never()).resetTicketTypesDateByDays(anyList(),anyInt());
        verify(ticketingCouponService, never()).resetCouponDates(anyList(),anyInt());
        verify(attendeeAnalyticsService, never()).deleteAttendeeAnalyticsDataForEvent(any());
        verify(questionsAnswersService, never()).updateDeleteStatusForAllQNAByEvent(any());
        verify(pollsQuestionsAnswersService, never()).updateDeleteStatusForAllPollsByEvent(any());
        verify(userService, never()).findById(anyLong());
        verify(virtualEventPortalService, never()).getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
        verify(virtualEventPortalService, never()).getStreamChannelIdsByEventId(any());
        verify(getStreamService, never()).truncateAllChannelsOfEvent(any(), any(), anyList());
        verify(resetEventDetailsService, never()).addResetEventDetails(any());

    }

    @Test
    void eventNotResetWhenEnvironmentIsProdAndOrganiserIdDoesNotBelongToAllowedOrganiserIds() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = Lists.newArrayList(event);

        String profile = Constants.ENV_PROD;

        //mock


        //execution
        resetEventServiceImpl.resetEventData("60153", 7, 2L,profile);

        //assertion
        verify(ticketingService, never()).resetEventDates(anyList(),anyInt());
        verify(ticketingAccessCodeService, never()).resetAccessCodeDates(anyList(),anyInt());
        verify(ticketingTypeService, never()).resetTicketTypesDateByDays(anyList(),anyInt());
        verify(ticketingCouponService, never()).resetCouponDates(anyList(),anyInt());
        verify(attendeeAnalyticsService, never()).deleteAttendeeAnalyticsDataForEvent(any());
        verify(questionsAnswersService, never()).updateDeleteStatusForAllQNAByEvent(any());
        verify(pollsQuestionsAnswersService, never()).updateDeleteStatusForAllPollsByEvent(any());
        verify(userService, never()).findById(anyLong());
        verify(virtualEventPortalService, never()).getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
        verify(virtualEventPortalService, never()).getStreamChannelIdsByEventId(any());
        verify(getStreamService, never()).truncateAllChannelsOfEvent(any(), any(), anyList());
        verify(resetEventDetailsService, never()).addResetEventDetails(any());

    }

    @Test
    void resetsEventsSuccessfullyForProdEnvWhenEventIdAndOrganiserIdBelongToAllowedEventAndOrganiserIds() {

        //setup
        List<Event> eventListByIdsAndOrganiserId = new ArrayList<>();
        eventListByIdsAndOrganiserId.add(event);
        String profile = Constants.ENV_PROD;

        //mock
        when(eventService.getEventListByIdsAndOrganiserId(anyList(), anyLong())).thenReturn(eventListByIdsAndOrganiserId);
        when(userService.findById(user.getUserId())).thenReturn(user);
        when(virtualEventPortalService.getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList());
        when(virtualEventPortalService.getStreamChannelIdsByEventId(any())).thenReturn(Arrays.asList());
        doNothing().when(getStreamService).truncateAllChannelsOfEvent(any(), any(), anyList());

        //execution
        resetEventServiceImpl.resetEventData("60153,60947", 7, 38229L, profile);

        //assertion
        verify(ticketingService).resetEventDates(anyList(),anyInt());
        verify(ticketingAccessCodeService).resetAccessCodeDates(anyList(),anyInt());
        verify(ticketingTypeService).resetTicketTypesDateByDays(anyList(),anyInt());
        verify(ticketingCouponService).resetCouponDates(anyList(),anyInt());
        verify(attendeeAnalyticsService).deleteAttendeeAnalyticsDataForEvent(any());
        verify(questionsAnswersService).updateDeleteStatusForAllQNAByEvent(any());
        verify(pollsQuestionsAnswersService).updateDeleteStatusForAllPollsByEvent(any());
        verify(userService).findById(anyLong());
        verify(virtualEventPortalService).getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
        verify(virtualEventPortalService).getStreamChannelIdsByEventId(any());
        verify(getStreamService).truncateAllChannelsOfEvent(any(), any(), anyList());
        verify(resetEventDetailsService).addResetEventDetails(any());
    }


}
