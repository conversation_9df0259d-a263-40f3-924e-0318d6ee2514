package com.accelevents.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.services.AttendeeSequenceNumberService;
import com.accelevents.services.EventDesignDetailService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.TicketingOrderService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingDownloadServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingDownloadServiceImpl ticketingDownloadServiceImpl;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private ImageConfiguration imageConfigurations;

    @Mock
    private AttendeeSequenceNumberService attendeeSequenceNumberService;

    private Event event;
	private Ticketing ticketing;
    private EventTickets eventTickets;
    private TicketingOrder ticketingOrder;
    private EventDesignDetail eventDesignDetail;
    private TicketingTable ticketingTable;

    private Long id = 1L;
    private  String barcode = "1d44e288-38f2-425e-929f-68a014b08c88";

	@BeforeEach
    public void setUp() throws Exception {
        event = EventDataUtil.getEvent();
		User user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        eventTickets = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();

        eventDesignDetail = new EventDesignDetail();
		String logoImage = "Accelevents_Default_Event_Logo.jpg";
		eventDesignDetail.setLogoImage(logoImage);
		String headerLogoImage = "newheaderlogo.JPG";
		eventDesignDetail.setHeaderLogoImage(headerLogoImage);
    }

    @Test
    public void test_downloadTicketPdf_success() throws Exception{

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        long tableNumberSequence = 1L;

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTable(ticketingTable);

        //mock
        when(eventCommonRepoService.findByIdJoinFetch(id)).thenReturn(eventTickets);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);


        when(attendeeSequenceNumberService.getSequenceNumberByEventTicket(eventTickets, event)).thenReturn("001");

        //Execution
        ticketingDownloadServiceImpl.downloadTicketPdf(id, httpServletResponse);
    }

    @Test
    public void test_downloadTicketPdf_success_with_holderFirstName_and_lastName_empty() throws Exception{

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        long tableNumberSequence = 1L;

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setHolderFirstName("");
        eventTickets.setHolderLastName("");
        eventTickets.setTicketingTable(null);

        //mock
        when(eventCommonRepoService.findByIdJoinFetch(id)).thenReturn(eventTickets);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);



        //Execution
        ticketingDownloadServiceImpl.downloadTicketPdf(id, httpServletResponse);
    }

    @Test
    public void test_downloadTicketPdf_throwException() throws Exception{

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        long tableNumberSequence = 1L;

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTable(ticketingTable);

        //mock
        when(eventCommonRepoService.findByIdJoinFetch(id)).thenReturn(eventTickets);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);



        //Execution
        ticketingDownloadServiceImpl.downloadTicketPdf(id, httpServletResponse);
    }

    @Test
    public void test_downloadTicketPass_success() throws IOException {

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcode)).thenReturn(eventTickets);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);


        //Execution
        ticketingDownloadServiceImpl.downloadTicketPass(barcode, httpServletResponse);
    }

    @Test
    public void test_downloadTicketPass_success_with_ticketPassLength_lessThanZero() throws IOException {

        //setup
        byte[] ticketPass = {};

        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcode)).thenReturn(eventTickets);
        Mockito.doReturn(ticketPass).when(ticketingDownloadServiceImpl).getTicketPassBytes(eventTickets);

        //Execution
        ticketingDownloadServiceImpl.downloadTicketPass(barcode, httpServletResponse);
    }

    @Test
    public void test_downloadTicketPass_throwException_barcodeNotExist() throws Exception {

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        //mock
        when(eventCommonRepoService.findByBarcodeId(barcode)).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingDownloadServiceImpl.downloadTicketPass(barcode, httpServletResponse));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_downloadTicketOrderPassZip_success() throws Exception{

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);



        //mock
        when(ticketingOrderService.findByid(id)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(any())).thenReturn(eventTicketsList);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);


        //Execution
        ticketingDownloadServiceImpl.downloadTicketOrderPassZip(id, httpServletResponse);
    }

    @Test
    public void test_downloadTicketOrderPassZip_success1() throws Exception{

        //setup
        byte[] ticketPass = {};

        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        CommonUtil.prepareDownloadableResponseHeader(httpServletResponse, Constants.TICKET_PASS_ZIP, Constants.APPLICATION_ZIP);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(ticketingOrderService.findByid(id)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(any())).thenReturn(eventTicketsList);
        Mockito.doReturn(ticketPass).when(ticketingDownloadServiceImpl).getTicketPassBytes(eventTickets);

        //Execution
        ticketingDownloadServiceImpl.downloadTicketOrderPassZip(id, httpServletResponse);
    }

}