package com.accelevents.services.impl;

import com.accelevents.continuing.ed.dto.ContinuingEdUserDTO;
import com.accelevents.continuing.ed.service.impl.ContinuingEdServiceImpl;
import com.accelevents.domain.Event;
import com.accelevents.domain.SurveyResponse;
import com.accelevents.domain.User;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.services.UserService;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.services.repo.helper.SurveyResponseRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.SessionTagAndTrackService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.continuing.ed.ContinuingEdConstants.ContinuingEdStatus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContinuingEdServiceImplTest {

    @Spy
    @InjectMocks
    private ContinuingEdServiceImpl continuingEdServiceImpl;

    @Mock
    private UserService userService;
    @Mock
    private SurveyResponseRepoService surveyResponseRepoService;
    @Mock
    private SurveyConfigRepoService surveyConfigRepoService;
    @Mock
    private SessionTagAndTrackService tagAndTrackService;
    @Mock
    private SessionService sessionService;

    private EventCECriteriaDTO ceCriteriaDTO;
    private final Event event = EventDataUtil.getEvent();
    private final List<User> userList = List.of(EventDataUtil.getUser(), EventDataUtil.getOtherUser());
    List<Object[]> userAttendeeDTOS = userList.stream().map(user -> new Object[]{BigInteger.valueOf(user.getUserId()), user.getFirstName(), user.getLastName(), user.getEmail(), user.getPhoto()} ).collect(Collectors.toList());
    private final Page<Object[]> users = new PageImpl<>(userAttendeeDTOS);
    private final Map<Long, Integer> surveyQuizScoreMap = new HashMap<>();
    private final PageSizeSearchObj pageSizeSearchObj = new PageSizeSearchObj(0, Integer.MAX_VALUE, null);
    private final List<Long> dummyListId = List.of(1L);

    List<SurveyResponse> surveyResponses = new ArrayList<>();

    @BeforeEach
    void setup() {
        ceCriteriaDTO = EventDataUtil.getSurveyEventCECriteriaDTO();
        ceCriteriaDTO.setCriteriaId(1L);
        surveyQuizScoreMap.put(1L, 60);

        SurveyResponse surveyResponse = new SurveyResponse();
        surveyResponse.setEventId(1L);
        surveyResponse.setSurveyId(1L);
        surveyResponse.setUserId(1L);
        surveyResponse.setUserQuizScore(80);
        surveyResponse.setSubmissionDate(new Date());
        surveyResponse.setSessionId(1L);
        surveyResponses.add(surveyResponse);
    }

    void mockContinueEdMethods() {
        when(userService.getAllAttendeesByTicketType(event.getEventId(),pageSizeSearchObj.getSearchWithEscapeSpecialChars(), ceCriteriaDTO.getTicketTypeAllowInCriteria(), pageSizeSearchObj.getPage(), pageSizeSearchObj.getSize()))
                .thenReturn(users);
        when(surveyConfigRepoService.isAnySurveyQuizAble(anyList())).thenReturn(true);
        when(surveyResponseRepoService.getSurveyCompletionDataBySurveyIds(
                anyList(),
                anyLong(),
                any(Date.class),
                any(Date.class)
        )).thenReturn(surveyResponses);
        when(tagAndTrackService.findSessionIdsByIds(anyList())).thenReturn(Collections.emptyList());
        when(sessionService.findSurveyIdBySessionIds(anyList())).thenReturn(dummyListId);
    }

    @ParameterizedTest
    @CsvSource({"ALL"})
    void testGetContinuingEdAnalyticsWithSurveyAreaWithStatusAll(ContinuingEdStatus status) {

        // Mock
        this.mockContinueEdMethods();

        // execution
        DataTableResponse continuingEdAnalytics = continuingEdServiceImpl.getContinuingEdAnalytics(ceCriteriaDTO, status, event, userList.get(0), pageSizeSearchObj);

        // verify
        ContinuingEdUserDTO continuingEdUserEligible = (ContinuingEdUserDTO) continuingEdAnalytics.getData().get(0);
        ContinuingEdUserDTO continuingEdUserNonEligible = (ContinuingEdUserDTO) continuingEdAnalytics.getData().get(1);

        assertEquals(2L, continuingEdAnalytics.getRecordsTotal());
        assertEquals(2L, continuingEdAnalytics.getRecordsFiltered());
        // Eligible User
        assertEquals(1L, continuingEdUserEligible.getUserId());
        assertEquals(100, continuingEdUserEligible.getUserQuizScore());
        assertEquals(200, continuingEdUserEligible.getRewardPoint());
        // NonEligible User
        assertEquals(2L, continuingEdUserNonEligible.getUserId());
        assertEquals(0, continuingEdUserNonEligible.getUserQuizScore());
        assertEquals(0, continuingEdUserNonEligible.getRewardPoint());
    }

    @ParameterizedTest
    @CsvSource({"ELIGIBLE", "INELIGIBLE"})
    void testGetContinuingEdAnalyticsWithSurveyAreaWithEligibleAndNotEligibleStatus(ContinuingEdStatus status) {

        // Mock
        this.mockContinueEdMethods();

        // execution
        DataTableResponse continuingEdAnalytics = continuingEdServiceImpl.getContinuingEdAnalytics(ceCriteriaDTO, status, event, userList.get(0), pageSizeSearchObj);

        // verify
        assertEquals(1L, continuingEdAnalytics.getRecordsTotal());
        assertEquals(1L, continuingEdAnalytics.getRecordsFiltered());

        if(ContinuingEdStatus.ELIGIBLE.equals(status)) {
            // Eligible User
            ContinuingEdUserDTO continuingEdUserEligible = (ContinuingEdUserDTO) continuingEdAnalytics.getData().get(0);
            assertEquals(1L, continuingEdUserEligible.getUserId());
            assertEquals(100, continuingEdUserEligible.getUserQuizScore());
            assertEquals(200, continuingEdUserEligible.getRewardPoint());
        } else {
            // NonEligible User
            ContinuingEdUserDTO continuingEdUserNonEligible = (ContinuingEdUserDTO) continuingEdAnalytics.getData().get(0);
            assertEquals(2L, continuingEdUserNonEligible.getUserId());
            assertEquals(0, continuingEdUserNonEligible.getUserQuizScore());
            assertEquals(0, continuingEdUserNonEligible.getRewardPoint());
        }
    }
}
