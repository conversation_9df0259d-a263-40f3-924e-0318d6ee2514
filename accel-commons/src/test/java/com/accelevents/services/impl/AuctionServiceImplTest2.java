//TODO: PowerMock issue with java17
/*
package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.MessageAction;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.messages.EnumAuctionSMS;
import com.accelevents.services.AuctionBidService;
import com.accelevents.services.CommonEventService;
import com.accelevents.services.ItemService;
import com.accelevents.services.WinnerService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest(DateUtils.class)
public class AuctionServiceImplTest2 {

	@Spy
	@InjectMocks
	private AuctionServiceImpl auctionServiceImpl = new AuctionServiceImpl();

	@Mock
	private AuctionBidService auctionBidService;
	@Mock
	private TextMessageUtils textMessageUtils;


	@Mock
	private ItemService itemService;

	@Mock
	private WinnerService winnerService;
	@BeforeEach
	public void setUp(){
		MockitoAnnotations.openMocks(this);
		PowerMockito.mockStatic(DateUtils.class);
	}
	
	String dateFormate = "yyyy/MM/dd HH:mm:ss";

	@Test
	public void testIsApplicableForExtend(){
		Item item1 = new Item();
		item1.setCode("ITEM1");

		Item item2 = new Item();
		item2.setCode("ITEM2");

		Item item3 = new Item();
		item3.setCode("ITEM3");

        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languagecode = "EN";

		//SetUp
		Auction auction = new Auction();
		auction.setEndDate(getFormattedDate("2018/10/10 10:00:00",dateFormate));
		auction.setExtendedBiddingWaitTime(300);
		auction.setExtendedBidding(true);

		Date firstBidItem1 = getFormattedDate("2018/10/10 09:56:00",dateFormate);
		Date firstBidItem2 = getFormattedDate("2018/10/10 09:54:00",dateFormate);

		// Before auction end date
		Date secondBidItem1 = getFormattedDate("2018/10/10 09:58:00",dateFormate);

		// After auction end date
		Date secondBidItem2 = getFormattedDate("2018/10/10 10:01:00",dateFormate);

		Date thirdBidItem1 = getFormattedDate("2018/10/10 10:02:00",dateFormate);

		Date fourthBidItem1 = getFormattedDate("2018/10/10 10:08:00",dateFormate);

		Date fourthBidItem3 = getFormattedDate("2018/10/10 10:08:00",dateFormate);

		//first bid for item3 after auction end
		String errorCode=null;
		try {
			auctionServiceImpl.isApplicableForExtend(Optional.empty(),auction,item3.getCode(),fourthBidItem3,languageMap,languagecode);
		} catch (NotAcceptableException e){
			errorCode = e.getErrorCode();
		}

		assertTrue(StringUtils.isNotBlank(errorCode));
		assertTrue(errorCode.equals(NotAcceptableException.AuctionExceptionMsg.BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE.getStatusCode()));
		// first bid item1
		//when(DateUtils.getCurrentDate()).thenReturn(firstBidItem1);
		boolean actual = auctionServiceImpl.isApplicableForExtend(Optional.empty(),auction,item1.getCode(),firstBidItem1,languageMap,languagecode);
		assertFalse(actual);

		// first bid item2
		//when(DateUtils.getCurrentDate()).thenReturn(firstBidItem2);
		actual = auctionServiceImpl.isApplicableForExtend(Optional.empty(),auction,item2.getCode(),firstBidItem2,languageMap,languagecode);
		assertFalse(actual);

		// ---------------- Both first bid are before auction end date but before interval, interval will not extend --------------

		// second bid item1
		AuctionBid firstAuctinBidItem1 = new AuctionBid(new User(),1l,item1,100d, BiddingSource.SMS,false);
		firstAuctinBidItem1.setBidTime(firstBidItem1);

		actual = auctionServiceImpl.isApplicableForExtend(Optional.of(firstAuctinBidItem1),auction,item1.getCode(),secondBidItem1,languageMap,languagecode);
		assertTrue(actual);
		auction.setLastExtendedEndTime(addMinutesToDate(secondBidItem1,auction.getExtendedBiddingWaitTime()));

		//SecondBid item2
		AuctionBid firstAuctinBidItem2 = new AuctionBid(new User(),1l,item2,100d, BiddingSource.SMS,false);
		firstAuctinBidItem2.setBidTime(firstBidItem2);

		errorCode=null;
		try {
			auctionServiceImpl.isApplicableForExtend(Optional.of(firstAuctinBidItem2),auction,item2.getCode(),secondBidItem2,languageMap,languagecode);
		} catch (NotAcceptableException e){
			errorCode = e.getErrorCode();
		}

		assertTrue(StringUtils.isNotBlank(errorCode));
		assertTrue(errorCode.equals(NotAcceptableException.AuctionExceptionMsg.BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE.getStatusCode()));

		//third bid for item 1
		AuctionBid secondAuctinBidItem1 = new AuctionBid(new User(),1l,item1,100d, BiddingSource.SMS,false);
		secondAuctinBidItem1.setBidTime(secondBidItem1);

		actual = auctionServiceImpl.isApplicableForExtend(Optional.of(secondAuctinBidItem1),auction,item1.getCode(),thirdBidItem1,languageMap,languagecode);
		assertTrue(actual);
		auction.setLastExtendedEndTime(addMinutesToDate(thirdBidItem1,auction.getExtendedBiddingWaitTime()));

		//forth bid out of interval time
		AuctionBid thirdAuctionBidItem =  new AuctionBid(new User(),1l,item1,100d, BiddingSource.SMS,false);
		thirdAuctionBidItem.setBidTime(thirdBidItem1);

		errorCode=null;
		try {
			auctionServiceImpl.isApplicableForExtend(Optional.of(thirdAuctionBidItem),auction,item1.getCode(),fourthBidItem1,languageMap,languagecode);
		} catch (NotAcceptableException e){
			errorCode = e.getErrorCode();
		}

		assertTrue(StringUtils.isNotBlank(errorCode));
		assertTrue(errorCode.equals(NotAcceptableException.AuctionExceptionMsg.BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE.getStatusCode()));
	}

	private Date addMinutesToDate(Date secondBidItem1, long extendedBiddingWaitTime) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(secondBidItem1);
		calendar.add(Calendar.SECOND, (int) extendedBiddingWaitTime);
		return calendar.getTime();
	}

	@Test
	public void testAuctionBidWhenAuctionNotExtended(){
		Event event = new Event();
		event.setPhoneNumber(854123654);
		event.setCountryCode(CountryCode.US);
		event.setSilentAuctionEnabled(true);

		Item item1 = new Item();
		item1.setCode("ITEM1");
		item1.setId(1l);

		User user = new User();

		MessageAction messageAction = new MessageAction();
		messageAction.setItemCode("ITEM1");
		messageAction.setAmount(56);

		AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(event);

		TwilioMessage twilioMessage = new TwilioMessage("body",accelEventsPhoneNumber,event);

		Auction auction = new Auction();
		auction.setEndDate(getFormattedDate("2018/10/10 10:00:00",dateFormate));
		auction.setExtendedBiddingWaitTime(300);
		auction.setExtendedBidding(true);
		Date currentDate = getFormattedDate("2018/10/10 10:00:05",dateFormate);

		//Mock Data
		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item1.getId())).thenReturn(Collections.emptyList());
		when(DateUtils.getCurrentDate()).thenReturn(currentDate);
		when(textMessageUtils.getAuctionModuleEndedMessage(any())).thenReturn(EnumAuctionSMS.AUCTION_MODULE_ENDED.getValue());
		when(textMessageUtils.getAuctionModuleNotActiveMessage(any())).thenReturn(EnumAuctionSMS.AUCTION_MODULE_NOT_ACTIVATED.getValue());

		doReturn(auction).when(auctionServiceImpl).findByEvent(any());

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item1, user, false);
		assertEquals(message, textMessageUtils.getAuctionModuleEndedMessage(twilioMessage.getEvent()));
	}

	@Test
	public void testAuctionBidWhenAuctionModuleEND(){
		Event event = new Event();
		event.setPhoneNumber(854123654);
		event.setCountryCode(CountryCode.US);
		event.setSilentAuctionEnabled(true);

		Item item1 = new Item();
		item1.setCode("ITEM1");
		item1.setId(1l);

		User user = new User();

		MessageAction messageAction = new MessageAction();
		messageAction.setItemCode("ITEM1");
		messageAction.setAmount(56);

		AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(event);

		TwilioMessage twilioMessage = new TwilioMessage("body",accelEventsPhoneNumber,event);

		Auction auction = new Auction();
		auction.setEndDate(getFormattedDate("2018/10/10 10:00:00",dateFormate));
		auction.setExtendedBiddingWaitTime(300);
		auction.setExtendedBidding(true);
		auction.setLastExtendedEndTime(getFormattedDate("2018/10/10 10:05:00",dateFormate));
		Date currentDate = getFormattedDate("2018/10/10 10:03:00",dateFormate);

		//Mock Data
		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item1.getId())).thenReturn(Collections.emptyList());
		when(DateUtils.getCurrentDate()).thenReturn(currentDate);
		when(textMessageUtils.getAuctionModuleEndedMessage(any())).thenReturn(EnumAuctionSMS.AUCTION_MODULE_ENDED.getValue());
		when(textMessageUtils.getAuctionModuleNotActiveMessage(any())).thenReturn(EnumAuctionSMS.AUCTION_MODULE_NOT_ACTIVATED.getValue());

		doReturn(auction).when(auctionServiceImpl).findByEvent(any());

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item1, user, false);
		assertEquals(message, textMessageUtils.getAuctionModuleNotActiveMessage(twilioMessage.getEvent()));
	}

	@Test
	public void testAuctionBidWhenAuctionExtendedEND(){

		//SetUp
		Event event = new Event();
		event.setPhoneNumber(854123654);
		event.setCountryCode(CountryCode.US);
		event.setSilentAuctionEnabled(true);

		Item item1 = new Item();
		item1.setCode("ITEM1");
		item1.setLiveAuctionItem(false);
		item1.setCurrentBid(100d);
		item1.setId(1l);
		User user = new User();

		MessageAction messageAction = new MessageAction();
		messageAction.setItemCode("ITEM1");
		messageAction.setAmount(56);

		AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(event);

		TwilioMessage twilioMessage = new TwilioMessage("body",accelEventsPhoneNumber,event);

		Auction auction = new Auction();
		auction.setEndDate(getFormattedDate("2018/10/10 10:00:00",dateFormate));
		auction.setExtendedBiddingWaitTime(300);
		auction.setExtendedBidding(true);
		auction.setLastExtendedEndTime(getFormattedDate("2018/10/10 10:04:00",dateFormate));
		auction.setActivated(true);

		Date secondBidItem1 = getFormattedDate("2018/10/10 09:59:00",dateFormate);
		Date currentDate = getFormattedDate("2018/10/10 10:06:00",dateFormate);

		when(DateUtils.getCurrentDate()).thenReturn(currentDate);

		AuctionBid secondAuctinBidItem1 = new AuctionBid(new User(),1l,item1,100d, BiddingSource.SMS,false);
		secondAuctinBidItem1.setBidTime(secondBidItem1);
		double bidIncrement = 50d;
		//Mock Data
		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item1.getId())).thenReturn(Collections.emptyList());
		when(itemService.getBidIncrement(item1, auction)).thenReturn(bidIncrement);
		when(textMessageUtils.getBelowAuctionBidMessage(twilioMessage.getEvent(), item1,messageAction.getAmount(), bidIncrement)).thenReturn("belowBidMessage");
		when(textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(
				twilioMessage.getEvent(), item1, user, messageAction.getAmount())).thenReturn("returnMessage");
		when(auctionBidService.findHighestBidItemForAuction(item1,auction.getId())).thenReturn(Optional.of(secondAuctinBidItem1));
		doReturn(auction).when(auctionServiceImpl).findByEvent(any());

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item1, user, true);
		assertEquals(message, textMessageUtils.getAuctionModuleEndedMessage(twilioMessage.getEvent()));

	}

	@Test
	public void testAuctionBidWhenAuctionEndForOneItemAndAuctionExtendedForSecondItem(){

		//SetUp
		Event event = new Event();
		event.setPhoneNumber(854123654);
		event.setCountryCode(CountryCode.US);
		event.setSilentAuctionEnabled(true);

		Item item1 = new Item();
		item1.setCode("ITEM1");
		item1.setLiveAuctionItem(false);
		item1.setCurrentBid(100d);

		Item item2 = new Item();
		item2.setCode("ITEM2");
		item2.setLiveAuctionItem(false);
		item2.setCurrentBid(100d);
		item2.setId(1l);
		User user = new User();

		MessageAction messageAction = new MessageAction();
		messageAction.setItemCode("ITEM1");
		messageAction.setAmount(56);

		//First bid
		Date firstBidItem1 = getFormattedDate("2018/10/10 09:56:00",dateFormate);
		Date firstBidItem2 = getFormattedDate("2018/10/10 09:54:00",dateFormate);

		//Second bid
		Date secondBidItem1 = getFormattedDate("2018/10/10 09:58:00",dateFormate);
		Date secondBidItem2 = getFormattedDate("2018/10/10 10:01:00",dateFormate);
		AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(event);

		TwilioMessage twilioMessage = new TwilioMessage("body",accelEventsPhoneNumber,event);
		Auction auction = new Auction();
		auction.setEndDate(getFormattedDate("2018/10/10 10:00:00",dateFormate));
		auction.setExtendedBiddingWaitTime(300);
		auction.setExtendedBidding(true);
		auction.setActivated(true);


		// Before auction end date
		auction.setLastExtendedEndTime(addMinutesToDate(secondBidItem1,auction.getExtendedBiddingWaitTime()));//10:03

		when(DateUtils.getCurrentDate()).thenReturn(secondBidItem2);

		AuctionBid secondAuctinBidItem2 = new AuctionBid(new User(),1l,item2,100d, BiddingSource.SMS,false);
		secondAuctinBidItem2.setBidTime(firstBidItem2);
		double bidIncrement = 50d;

		//Mock Data
		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item2.getId())).thenReturn(Collections.emptyList());
		when(itemService.getBidIncrement(item2, auction)).thenReturn(bidIncrement);
		when(textMessageUtils.getBelowAuctionBidMessage(twilioMessage.getEvent(), item1,messageAction.getAmount(), bidIncrement)).thenReturn("belowBidMessage");
		when(textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(
				twilioMessage.getEvent(), item2, user, messageAction.getAmount())).thenReturn("returnMessage");
		when(auctionBidService.findHighestBidItemForAuction(item2,auction.getId())).thenReturn(Optional.of(secondAuctinBidItem2));
		doReturn(auction).when(auctionServiceImpl).findByEvent(any());

		String errorCode = null;
		try {
			auctionServiceImpl.auctionBid(twilioMessage, messageAction, item2, user, false);
		}catch(NotAcceptableException e){
			errorCode =e.getErrorCode();
		}

		assertTrue(StringUtils.isNotBlank(errorCode));
		assertTrue(errorCode.equals(NotAcceptableException.AuctionExceptionMsg.BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE.getStatusCode()));
	}

	public static Date getFormattedDate(String date, String pattern) {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		try {
			return formatter.parse(date);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

}
*/
