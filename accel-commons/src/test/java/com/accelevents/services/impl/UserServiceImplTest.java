package com.accelevents.services.impl;

import com.accelevents.common.dto.PasswordResetCodeDto;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.repositories.UserRepository;
import com.accelevents.repositories.UserRolesRepository;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.CacheStoreService;
import com.accelevents.services.PaymentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceImplTest {

	@Spy
	@InjectMocks
	private UserServiceImpl userServiceImpl;
    @Mock
	private TextMessageUtils mockTextMessageUtils;
	@Mock
	private UserRepository mockUserRepository;
	@Mock
	private PaymentService paymentService;
	@Mock
	private UserRolesRepository mockUserRolesRepository;
	@Mock
	private UserRepository userRepository;
	@Mock
	private CacheStoreService cacheStoreService;
    @Mock
    private ImageConfiguration imageConfiguration;
    @Mock
    private EmailImageHelper emailImageHelper;
    @Mock
    private ROUserService roUserService;

	private User user;
	private Event event;
	private Payment payment;
	private TwilioMessage twilioMessage;
	Raffle raffle;
	private PasswordResetCodeDto passwordResetCodeDto;

	@BeforeEach
	void setUpData() {
		// Prepare data
		MockitoAnnotations.openMocks(this);
		user = new User("<EMAIL>", 9999999999L);
		user.setCountry(CountryCode.US.getCode());
		user.setCountryCode(CountryCode.US);
		user.setUserId(1L);

		event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
		event.setEventId(1L);
		event.setPhoneNumber(8888888888L);
		event.setCountryCode(CountryCode.US);
		event.setCreditCardEnabled(true);

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(user);
		twilioMessage = new TwilioMessage("Text Message", from, event);
		payment = new Payment(user.getUserId(), event.getEventId(), null, new Date());

		raffle = new Raffle();
	}

	@Test
	void testProcessNameTextForSweepstakesForExistingUser() {
		doReturn(user).when(userServiceImpl).save(user);
        when(roUserService.getUserByAEPhoneNumber(twilioMessage.getFrom())).thenReturn(Optional.of(user));

		userServiceImpl.processNameTextForSweepstakes(twilioMessage, raffle, user);

		assertEquals("Text", user.getFirstName(), "First Name should be Save");
		assertEquals("Message", user.getLastName(), "Last Name should be save");
		verify(mockTextMessageUtils).getProcessNameTextForSweepstakesMessage(user, event, user);
		verify(userServiceImpl).save(user);
	}

	@Test
	void testProcessNameTextForSweepstakesForUserDoesNotExist() {
		doNothing().when(userServiceImpl).addUserRole(any(User.class), anyString());
        when(userRepository.save(user)).thenReturn(user);
		ArgumentCaptor<User> captor = ArgumentCaptor.forClass(User.class);
        when(roUserService.getUserByAEPhoneNumber(twilioMessage.getFrom())).thenReturn(Optional.of(user));

		userServiceImpl.processNameTextForSweepstakes(twilioMessage, raffle, user);

		verify(userServiceImpl).save(captor.capture());
		User savedUser = captor.getValue();
		savedUser.setUserId(11L);
        savedUser.setMarketingOptIn(true);
		verify(mockTextMessageUtils).getProcessNameTextForSweepstakesMessage(user, event, savedUser);
		assertEquals("Text", savedUser.getFirstName(), "First Name should be Save");
		assertEquals( "Message", savedUser.getLastName(), "Last Name should be save");
		assertEquals(user.getPhoneNumber(), savedUser.getPhoneNumber(), "User's phone number saved");
		assertEquals(user.getCountryCode(), savedUser.getCountryCode(), "User's country code saved");
		assertEquals(true, savedUser.isMarketingOptIn(), "User's Marketing Option saved as true");
		assertEquals( event.getEventId(), savedUser.getMostRecentEventId(), "User's most recent evnetId get updated");
	}

	@Test
	void isCCRequiredWithStripeTokenForAuctionReturnsFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithOutStripeTokenForAuctionReturnsTrue() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledForAuctionReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, false));
	}

	@Test
	void isCCRequiredCCWithEnabledForAuctionReturnsTrue() {
		// Prepare data
		event.setCreditCardEnabled(false);

		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithStripeTokenForCauseAuctionReturnFalse() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.CAUSEAUCTION, user, event, false), "CC is required ");
	}

	@Test
	void testIsCCRequiredWithOutStripeTokenForCauseAuctionReturnFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.CAUSEAUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNotNullForCauseAuctionReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.CAUSEAUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNullForCauseAuctionReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.CAUSEAUCTION, user, event, false));
	}

	@Test
	void isCCRequiredWithStripeTokenForDonationReturnFalse() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.DONATION, user, event, false), "CC is required ");
	}

	@Test
	void testIsCCRequiredWithOutStripeTokenForDonationReturnFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.DONATION, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNotNullForDonationReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.DONATION, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNullForDonationReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.DONATION, user, event, false));
	}

	@Test
	void isCCRequiredWithStripeTokenForRaffleReturnFalse() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.RAFFLE, user, event, false), "CC is required ");
	}

	@Test
	void testIsCCRequiredWithOutStripeTokenForRaffleReturnFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.RAFFLE, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNotNullForRaffleReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.RAFFLE, user, event, false));
	}

	@Test
	void isCCRequiredWithCCDisabledAndTokenNullForRaffleReturnsFalse() {
		// Prepare data
		event.setCreditCardEnabled(false);

		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.RAFFLE, user, event, false));
	}

	@Test
	void isCCRequiredWithStripeTokenForTicketingReturnTrue() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.TICKETING, user, event, false), "CC is required ");
	}

	@Test
	void isCCRequiredWithStripeTokenAndFromBidPageForAuctionReturnsFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, true));
	}

	@Test
	void isCCRequiredWithOutStripeTokenAndFromBidPageForAuctionReturnsFalse() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);

		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, true));
	}

	@Test
	void isCCRequiredWithCcRequiredForBidConfirmAndFromBidPageForAuctionReturnsTrue() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);
		event.setCcRequiredForBidConfirm(true);
		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertTrue(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, true));
	}

	@Test
	void isCCRequiredWithCcDisabledForBidConfirmAndFromBidPageForAuctionReturnsFalse() {
		// Prepare data
		Optional<Payment> optPayment = Optional.ofNullable(payment);
		event.setCreditCardEnabled(false);
		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, true));
	}

	@Test
	void isCCRequiredWithCcDisabledStripeTokenNotNullBidConfirmFromBidPageForAuctionReturnsFalse() {
		// Prepare data
		payment.setStripeToken("TEST_TOKEN");
		Optional<Payment> optPayment = Optional.ofNullable(payment);
		event.setCreditCardEnabled(false);
		// Mock data
		when(paymentService.findByUserIdAndEventId(user.getUserId(), event.getEventId())).thenReturn(optPayment);

		// Run test
		assertFalse(userServiceImpl.isCCRequired(ModuleType.AUCTION, user, event, true));
	}

	@Test
	void test_validatePasswordRestCode_withUserNotPresent() {
		//Prepare data
		User user = null;
		Optional<User> optUser = Optional.ofNullable(user);
		passwordResetCodeDto = new PasswordResetCodeDto();

		//Mock data
		when(roUserService.findOpUserByEmail(any())).thenReturn(optUser);

		//Run test
		assertFalse(userServiceImpl.validatePasswordRestCode(passwordResetCodeDto));
	}

	@Test
	void test_validatePasswordRestCode_withUserPresentAndObjectNull() {
		//Prepare data
		User user = new User("<EMAIL>", 9999999999L);
		Optional<User> optUser = Optional.ofNullable(user);
		passwordResetCodeDto = new PasswordResetCodeDto();

		//Mock data
		when(roUserService.findOpUserByEmail(any())).thenReturn(optUser);
		when(cacheStoreService.get("PASSWORD_RESET_CODE_" + optUser.get().getUserId())).thenReturn(null);

		//Run test
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> userServiceImpl.validatePasswordRestCode(passwordResetCodeDto));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_EXPIRE.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_validatePasswordRestCode_withUserPresentAndCodeNotMatch() {
		//Prepare data
		User user = new User("<EMAIL>", 9999999999L);
		Optional<User> optUser = Optional.ofNullable(user);
		passwordResetCodeDto = new PasswordResetCodeDto();
		passwordResetCodeDto.setPasswordResetCode("123456");
		Object object = "654321";

		//Mock data
		when(roUserService.findOpUserByEmail(any())).thenReturn(optUser);
		when(cacheStoreService.get("PASSWORD_RESET_CODE_" + optUser.get().getUserId())).thenReturn(object);

		//Run test
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> userServiceImpl.validatePasswordRestCode(passwordResetCodeDto));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.PASSWORD_RESET_CODE_NOT_MATCH.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_validatePasswordRestCode_withUserPresentAndCodeMatch() {
		//Prepare data
		User user = new User("<EMAIL>", 9999999999L);
		Optional<User> optUser = Optional.ofNullable(user);
		passwordResetCodeDto = new PasswordResetCodeDto();
		passwordResetCodeDto.setPasswordResetCode("123456");
		Object object = "123456";

		//Mock data
		when(roUserService.findOpUserByEmail(any())).thenReturn(optUser);
		when(cacheStoreService.get("PASSWORD_RESET_CODE_" + optUser.get().getUserId())).thenReturn(object);

		//Run test
		assertTrue(userServiceImpl.validatePasswordRestCode(passwordResetCodeDto));
	}

}