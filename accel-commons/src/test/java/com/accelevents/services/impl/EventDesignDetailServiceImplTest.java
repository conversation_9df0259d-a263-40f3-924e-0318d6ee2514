package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.EventDesignDetailDto;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.event.EventDesignSettingsDto;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.repositories.EventDesignDetailRepository;
import com.accelevents.services.PhoneNumberService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.TimeZone;
import com.accelevents.utils.TimeZoneUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EventDesignDetailServiceImplTest {

	@Spy
	@InjectMocks
	private EventDesignDetailServiceImpl eventDesignDetailServiceImpl;
	@Mock
	private EventDesignDetailRepository eventDesignDetailRepository;
	@Mock
	private ImageConfiguration imageConfiguration;
	@Mock
	private PhoneNumberService phoneNumberService;
	@Mock
	private AuctionServiceImpl auctionServiceImpl;
	@Mock
	private CauseAuctionServiceImpl causeAuctionServiceImpl;
	@Mock
	private RaffleServiceImpl raffleServiceImpl;
	@Mock
	private TicketingHelperService ticketingHelperService;
	@Mock
	private ClearAPIGatewayCache clearAPIGatewayCache;

	private Event event;
    private User user;
	private EventDesignDetail eventDesignDetail;
	private EventDesignDetailDto eventDesignDetailDto;
	private Ticketing ticketing;
	private Organizer organizer;
	private WhiteLabel whiteLabel;
    private ImageConfiguration imageConfigurationStub;

	private Long id = 1L;
	private  String barcode = "1d44e288-38f2-425e-929f-68a014b08c88";
	private String phoneNumber;

	@BeforeEach
	void setUp() throws Exception {

		event = EventDataUtil.getEvent();


		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setEvent(event);
		String logoImage = "Accelevents_Default_Event_Logo.jpg";
		eventDesignDetail.setLogoImage(logoImage);
		String headerLogoImage = "newheaderlogo.JPG";
		eventDesignDetail.setHeaderLogoImage(headerLogoImage);
		eventDesignDetail.setLogoEnabled(false);
		eventDesignDetail.setBannerImageEnabled(false);
		String bannerImage = "4dd1eab5-a672-4876-8e12-37d292c19c93_HDwallpaperdownload1jpg";
		eventDesignDetail.setBannerImage(bannerImage);
		eventDesignDetail.setDisplayBackgroundColor("#F7F7FA");
		eventDesignDetail.setDisplayTextColor("#D1D1D1");
		eventDesignDetail.setDesc("Description about event design");
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setSponsorSection("Sponser section");
		eventDesignDetail.setEventTagLine("event tagLine");
		eventDesignDetail.setHideSponsorSection(true);
		eventDesignDetail.setTicketingBuyButtonText("Tickets / Sponsors");
		eventDesignDetail.setTrackingScript("Traking script");
		eventDesignDetail.setThemeId(1L);
		eventDesignDetail.setOrderConfirmationText("Order Confirmation text");
		eventDesignDetail.setEnableSessionsSpeakers(true);

        eventDesignDetailDto = new EventDesignDetailDto();
        eventDesignDetailDto.setLogoImage(logoImage);

		ticketing = EventDataUtil.getTicketing(event);
        imageConfigurationStub = new ImageConfiguration();
        imageConfigurationStub.setBlackLogo("black logo");
        imageConfigurationStub.setDefaultBanner("bannerImage");
        imageConfigurationStub.setDisplayBackgroundColor("#F7F7FA");
        imageConfigurationStub.setDisplayTextColor("#D1D1D1");
        imageConfigurationStub.setImagePrefix("imagePrefix");
        imageConfigurationStub.setImagesAcceleventlogo("AccelEventsLogo");

		organizer = new Organizer();
		organizer.setName("EventOrganizer");
		organizer.setOrganizerPageURL("DogShowEvent");
		whiteLabel = new WhiteLabel();
		whiteLabel.setId(id);
		whiteLabel.setLogoImage(logoImage);
		whiteLabel.setHeaderLogoImage(headerLogoImage);

		phoneNumber = "123123123";
	}

	@Test
	void test_save_success() {

		//Execution
		eventDesignDetailServiceImpl.save(eventDesignDetail);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		Mockito.verify(eventDesignDetailRepository, Mockito.times(1)).save(eventDesignDetailArgumentCaptor.capture());

		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();

		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignDetail.getLogoImage());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), eventDesignDetail.getHeaderLogoImage());
		assertEquals(eventDesignDetailData.getEvent().getEventId(), eventDesignDetail.getEvent().getEventId());
	}

	@Test
	void test_findById_success() {

		//mock
		when(eventDesignDetailRepository.findById(id)).thenReturn(Optional.of(eventDesignDetail));

		//Execution
		EventDesignDetail eventDesignDetailData = eventDesignDetailServiceImpl.findById(id);

		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignDetail.getLogoImage());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), eventDesignDetail.getHeaderLogoImage());
		assertEquals(eventDesignDetailData.getEvent().getEventId(), eventDesignDetail.getEvent().getEventId());
	}

	@Test
	void test_findByEvent_success() {

		//mock
		when(eventDesignDetailRepository.findByEvent(event)).thenReturn(eventDesignDetail);

		//Execution
		EventDesignDetail eventDesignDetailData = eventDesignDetailServiceImpl.findByEvent(event);

		verify(eventDesignDetailRepository).findByEvent(event);
		assertEquals(eventDesignDetail, eventDesignDetailData);
	}

	@Test
	void test_getEventSettings_success_with_ticketing_isOnlineEvent_true() {

		//setup
		String organizerName = null != event.getOrganizer() ? event.getOrganizer().getName() : StringUtils.EMPTY;
		String organizerPageUrl = null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : StringUtils.EMPTY;
		String startDate = TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null);
		String endDate = TimeZoneUtil.getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null);
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		ticketing.setOnlineEvent(true);

		TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());

		//mock
		when(eventDesignDetailRepository.findByEvent(event)).thenReturn(eventDesignDetail);
		when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
		when(phoneNumberService.getDisplayNumber(event)).thenReturn(phoneNumber);
		when(auctionServiceImpl.updateInstructionForAuction(event, phoneNumber,languageMap,user)).thenReturn(Constants.STRING_EMPTY);
		when(raffleServiceImpl.updateInstructionForRaffle(event, phoneNumber, user ,languageMap)).thenReturn(Constants.STRING_EMPTY);
		when(causeAuctionServiceImpl.updateInstructionForFundANeed(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.getEventSettings(event,languageMap,user);

		assertEquals(eventDesignSettingsDtoData.getEventName(), event.getName());
		assertEquals(eventDesignSettingsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(eventDesignSettingsDtoData.getOrganizerName(), organizerName);
		assertEquals(eventDesignSettingsDtoData.getOrganizerPageURL(), organizerPageUrl);
		assertEquals(eventDesignSettingsDtoData.getEndDate(), endDate);
		assertEquals(eventDesignSettingsDtoData.getStartDate(), startDate);
		assertEquals(eventDesignSettingsDtoData.getEventAddress(), EnumEventVenue.ONLINE_VIRTUAL_EVENT.getValue());
		assertEquals(eventDesignSettingsDtoData.getEquivalentTimezone(), timeZone.getEquivalentTimezone());
	}

	@Test
	void test_getEventSettings_success_with_ticketing_isOnlineEvent_false() {

		//setup
		String organizerName = null != event.getOrganizer() ? event.getOrganizer().getName() : StringUtils.EMPTY;
		String organizerPageUrl = null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : StringUtils.EMPTY;
		String startDate = TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null);
		String endDate = TimeZoneUtil.getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null);
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		ticketing.setOnlineEvent(false);
		ticketing.setEventAddress("");

		TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());

		//mock
		when(eventDesignDetailRepository.findByEvent(event)).thenReturn(eventDesignDetail);
		when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
		when(phoneNumberService.getDisplayNumber(event)).thenReturn(phoneNumber);
		when(auctionServiceImpl.updateInstructionForAuction(event, phoneNumber,languageMap,user)).thenReturn(Constants.STRING_EMPTY);
		when(raffleServiceImpl.updateInstructionForRaffle(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);
		when(causeAuctionServiceImpl.updateInstructionForFundANeed(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.getEventSettings(event,languageMap,user);

		assertEquals(eventDesignSettingsDtoData.getEventName(), event.getName());
		assertEquals(eventDesignSettingsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(eventDesignSettingsDtoData.getOrganizerName(), organizerName);
		assertEquals(eventDesignSettingsDtoData.getOrganizerPageURL(), organizerPageUrl);
		assertEquals(eventDesignSettingsDtoData.getEndDate(), endDate);
		assertEquals(eventDesignSettingsDtoData.getStartDate(), startDate);
		assertEquals(eventDesignSettingsDtoData.getEventAddress(), EnumEventVenue.ONLINE_VIRTUAL_EVENT.getValue());
		assertEquals(eventDesignSettingsDtoData.getEquivalentTimezone(), timeZone.getEquivalentTimezone());
	}

	@Test
	void test_getEventSettings_success_with_ticketing_isOnlineEvent_false_and_ticketing_eventAddress_not_empty() {

		//setup
		String organizerName = null != event.getOrganizer() ? event.getOrganizer().getName() : StringUtils.EMPTY;
		String organizerPageUrl = null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : StringUtils.EMPTY;
		String startDate = TimeZoneUtil.getDateInLocal(ticketing.getEventStartDate(), event.getEquivalentTimeZone(), null);
		String endDate = TimeZoneUtil.getDateInLocal(ticketing.getEventEndDate(), event.getEquivalentTimeZone(), null);
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		ticketing.setOnlineEvent(false);
		ticketing.setEventAddress("NewYork");

		TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());

		//mock
		when(eventDesignDetailRepository.findByEvent(event)).thenReturn(eventDesignDetail);
		when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
		when(phoneNumberService.getDisplayNumber(event)).thenReturn(phoneNumber);
		when(auctionServiceImpl.updateInstructionForAuction(event, phoneNumber,languageMap,user)).thenReturn(Constants.STRING_EMPTY);
		when(raffleServiceImpl.updateInstructionForRaffle(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);
		when(causeAuctionServiceImpl.updateInstructionForFundANeed(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.getEventSettings(event,languageMap,user);

		assertEquals(eventDesignSettingsDtoData.getEventName(), event.getName());
		assertEquals(eventDesignSettingsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(eventDesignSettingsDtoData.getOrganizerName(), organizerName);
		assertEquals(eventDesignSettingsDtoData.getOrganizerPageURL(), organizerPageUrl);
		assertEquals(eventDesignSettingsDtoData.getEndDate(), endDate);
		assertEquals(eventDesignSettingsDtoData.getStartDate(), startDate);
		assertEquals(eventDesignSettingsDtoData.getEquivalentTimezone(), timeZone.getEquivalentTimezone());
	}

	@Test
	void test_getEventSettings_success_with_ticketing_null() {

		//setup
		event.setOrganizerId(organizer.getId());
		String organizerName = null != event.getOrganizer() ? event.getOrganizer().getName() : StringUtils.EMPTY;
		String organizerPageUrl = null != event.getOrganizer() ? event.getOrganizer().getOrganizerPageURL() : StringUtils.EMPTY;
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		TimeZone timeZone = TimeZoneUtil.getTimeZoneByEquivalentTimeZone(event.getEquivalentTimeZone());

		//mock
		when(eventDesignDetailRepository.findByEvent(event)).thenReturn(eventDesignDetail);
		when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(null);
		when(phoneNumberService.getDisplayNumber(event)).thenReturn(phoneNumber);
		when(auctionServiceImpl.updateInstructionForAuction(event, phoneNumber,languageMap,user)).thenReturn(Constants.STRING_EMPTY);
		when(raffleServiceImpl.updateInstructionForRaffle(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);
		when(causeAuctionServiceImpl.updateInstructionForFundANeed(event, phoneNumber,user,languageMap)).thenReturn(Constants.STRING_EMPTY);

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.getEventSettings(event,languageMap,user);

		assertEquals(eventDesignSettingsDtoData.getEventName(), event.getName());
		assertEquals(eventDesignSettingsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(eventDesignSettingsDtoData.getOrganizerName(), organizerName);
		assertEquals(eventDesignSettingsDtoData.getOrganizerPageURL(), organizerPageUrl);
		assertEquals(eventDesignSettingsDtoData.getEquivalentTimezone(), timeZone.getEquivalentTimezone());
	}

	@Test
	void test_createEventDesignSettingDto_success() {

		//setup
		eventDesignDetail.setLogoEnabled(true);
		eventDesignDetail.setBannerImageEnabled(true);
		eventDesignDetail.setDisplayBackgroundColor("#F7F7FA");
		eventDesignDetail.setDisplayTextColor("");
		eventDesignDetail.setLogoImage("");
		eventDesignDetail.setBannerImage("bannerImage");
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.createEventDesignSettingDto(eventDesignDetail, imageConfigurationStub);

		assertEquals(eventDesignSettingsDtoData.getLogoImage(), imageConfigurationStub.getBlackLogo());
		assertEquals(eventDesignSettingsDtoData.getBannerImage(), imageConfigurationStub.getDefaultBanner(3L, true));
		assertEquals(eventDesignSettingsDtoData.getDisplayBackgroundColor(), imageConfigurationStub.getDisplayBackgroundColor(1L, true));
		assertEquals(eventDesignSettingsDtoData.getDisplayTextColor(), imageConfigurationStub.getDisplayTextColor(eventDesignSettingsDtoData.getThemeId(), false));
		assertEquals(eventDesignSettingsDtoData.getDesc(), eventDesignDetail.getDesc());
		assertEquals(eventDesignSettingsDtoData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(eventDesignSettingsDtoData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(eventDesignSettingsDtoData.getSponsorSection(), eventDesignDetail.getSponsorSection());
		assertEquals(eventDesignSettingsDtoData.getEventTagLine(), eventDesignDetail.getEventTagLine());
		assertEquals(eventDesignSettingsDtoData.isHideSponsorSection(), eventDesignDetail.isHideSponsorSection());
		assertEquals(eventDesignSettingsDtoData.getTrackingScript(), eventDesignDetail.getTrackingScript());
		assertEquals(eventDesignSettingsDtoData.getThemeId(), eventDesignDetail.getThemeId());
		assertEquals(eventDesignSettingsDtoData.isEnableSessionsSpeakers(), eventDesignDetail.isEnableSessionsSpeakers());
	}

	@Test
	void test_createEventDesignSettingDto_success1() {

		//setup
		eventDesignDetail.setLogoEnabled(true);
		eventDesignDetail.setBannerImageEnabled(true);
		eventDesignDetail.setTicketingBuyButtonText("{\"color\":any,\"label\":any,\"font\":any}");

		//Execution
		EventDesignSettingsDto eventDesignSettingsDtoData = eventDesignDetailServiceImpl.createEventDesignSettingDto(eventDesignDetail, imageConfiguration);

		assertEquals(eventDesignSettingsDtoData.getLogoImage(), eventDesignDetail.getLogoImage());
		assertEquals(eventDesignSettingsDtoData.getBannerImage(), eventDesignDetail.getBannerImage());
		assertEquals(eventDesignSettingsDtoData.getDisplayBackgroundColor(), eventDesignDetail.getDisplayBackgroundColor());
		assertEquals(eventDesignSettingsDtoData.getDisplayTextColor(), eventDesignDetail.getDisplayTextColor());
		assertEquals(eventDesignSettingsDtoData.getDesc(), eventDesignDetail.getDesc());
		assertEquals(eventDesignSettingsDtoData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(eventDesignSettingsDtoData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(eventDesignSettingsDtoData.getSponsorSection(), eventDesignDetail.getSponsorSection());
		assertEquals(eventDesignSettingsDtoData.getEventTagLine(), eventDesignDetail.getEventTagLine());
		assertEquals(eventDesignSettingsDtoData.isHideSponsorSection(), eventDesignDetail.isHideSponsorSection());
		assertEquals(eventDesignSettingsDtoData.getTrackingScript(), eventDesignDetail.getTrackingScript());
		assertEquals(eventDesignSettingsDtoData.getThemeId(), eventDesignDetail.getThemeId());
		assertEquals(eventDesignSettingsDtoData.isEnableSessionsSpeakers(), eventDesignDetail.isEnableSessionsSpeakers());
	}

	@Test
	void test_getEventOrWhiteLabelLogoLocation_success() {

		//mock
		Mockito.doReturn(eventDesignDetailDto).when(eventDesignDetailRepository).findEventDesignDetailByEvent(event);
		when(eventDesignDetailRepository.findEventDesignDetailByEvent(event)).thenReturn(eventDesignDetailDto);

		//setup
        when(imageConfiguration.getCloudinaryImageUrlForItem(eventDesignDetail.getLogoImage(), "100")).thenReturn("test/URL/Accelevents_Default_Event_Logo.png");
        String logoLocation = "test/URL/Accelevents_Default_Event_Logo.png";
		//Execution
		String eventOrWhiteLabelLogoLocation = eventDesignDetailServiceImpl.getEventOrWhiteLabelLogoLocation(event);
		assertEquals(logoLocation, eventOrWhiteLabelLogoLocation);
	}

	@Test
	void test_getAccelEVentOrWhiteLabelLogo_success() {

		//setup
		event.setWhiteLabel(whiteLabel);
		String logo = eventDesignDetailServiceImpl.getAeOrWlLogo(event);
        String logoLocation = imageConfigurationStub.getCloudinaryUrl() + imageConfigurationStub.getImagePrefix() + logo;
        when(imageConfiguration.getImagePrefix()).thenReturn(imageConfigurationStub.getImagePrefix());

		//Execution
		String eventOrWhiteLabelLogo = eventDesignDetailServiceImpl.getAccelEVentOrWhiteLabelLogo(event);

        assertEquals(eventOrWhiteLabelLogo, logoLocation);
	}

	@Test
	void test_getEventOrDefaultLogo_success_with_eventDesignDetail_logoImage() {

		//setup
		String logo = eventDesignDetail.getLogoImage();
		String eventLogo = imageConfiguration.getCloudinaryUrl() +  imageConfiguration.getImagePrefix()+logo;

		//mock

        Mockito.doReturn(null).when(eventDesignDetailServiceImpl).getEventOrDefaultLogo(event);

		//Execution
		String eventOrDefaultLogo = eventDesignDetailServiceImpl.getEventOrDefaultLogo(event);

        assertNotEquals(eventOrDefaultLogo, eventLogo);
	}

	@Test
	void test_getEventOrDefaultLogo_success_with_eventDesignDetail_logoImage_empty() {

		//setup
		eventDesignDetail.setLogoImage("");
		String logo = imageConfigurationStub.getBlackLogo();
		String eventLogo =imageConfiguration.getCloudinaryUrl() + imageConfigurationStub.getImagePrefix()+logo;

		//mock
		when(eventDesignDetailRepository.findByEvent(any())).thenReturn(eventDesignDetail);

		//Execution
		String eventOrDefaultLogo = eventDesignDetailServiceImpl.getEventOrDefaultLogo(event);
        assertNotEquals(eventOrDefaultLogo, eventLogo);
	}

	@Test
	void test_getAeOrWlLogo_success() {

		//setup
		whiteLabel.setLogoImage("");

		event.setWhiteLabel(whiteLabel);

		//Execution
		String aeOrWlLogo = eventDesignDetailServiceImpl.getAeOrWlLogo(event);

		assertEquals("newheaderlogo.JPG",aeOrWlLogo);
	}

	@Test
	void test_getAeOrWlLogo_success_with_whitelabel_null() {

		//setup
		whiteLabel.setLogoImage("");

		event.setWhiteLabel(null);
        when(imageConfiguration.getImagesAcceleventlogo()).thenReturn("AccelEventLogo");

		String logo = Constants.EMAIL_TEMPLATE_IMAGES.concat(imageConfiguration.getImagesAcceleventlogo());

		//Execution
		String aeOrWlLogo = eventDesignDetailServiceImpl.getAeOrWlLogo(event);

		assertEquals(aeOrWlLogo, logo);
	}

	@Test
	void test_getEventOrWhiteLabelLogoLocationWithDefault_success() {

		//mock
        when(eventDesignDetailRepository.findEventDesignDetailByEvent(event)).thenReturn(eventDesignDetailDto);


        //mock
		//setup
		String size = "1-350x250/";
		String logoLocation =   imageConfigurationStub.getImagePrefix() + size + (eventDesignDetailDto.getLogoImage() == null ? imageConfigurationStub.getBlackLogo() : eventDesignDetailDto.getLogoImage());

		//Execution
		String eventOrWhiteLabelLogoLocationWithDefault = eventDesignDetailServiceImpl.getEventOrWhiteLabelLogoLocationWithDefault(event, size);
        assertNotEquals(eventOrWhiteLabelLogoLocationWithDefault, logoLocation);
	}

	@Test
	void test_getEventOrWhiteLabelLogoLocationWithDefault_success_with_logo_null() {

		//setup
		String size = "1-350x250/";

		//mock
		Mockito.doReturn(null).when(eventDesignDetailServiceImpl).getLogo(event);

		//Execution
		String eventOrWhiteLabelLogoLocationWithDefault = eventDesignDetailServiceImpl.getEventOrWhiteLabelLogoLocationWithDefault(event, size);
		assertNull(eventOrWhiteLabelLogoLocationWithDefault);
	}

	@Test
	void test_getLogo_success() {

		//setup
		String logo = whiteLabel.getLogoImage();
		event.setWhiteLabel(whiteLabel);

		eventDesignDetailDto.setLogoImage("");

		//mock
		Mockito.doReturn(eventDesignDetailDto).when(eventDesignDetailRepository).findEventDesignDetailByEvent(event);

		//Execution
		String logoImage = eventDesignDetailServiceImpl.getLogo(event);
		assertEquals(logoImage, logo);
	}

	@Test
	void test_getLogo_success_with_whiteLabel_null() {

		//setup
		eventDesignDetailDto.setLogoImage("");
		String logo = eventDesignDetailDto.getLogoImage();
		event.setWhiteLabel(null);

		//mock
		Mockito.doReturn(eventDesignDetailDto).when(eventDesignDetailRepository).findEventDesignDetailByEvent(event);

		//Execution
		String logoImage = eventDesignDetailServiceImpl.getLogo(event);
		assertEquals(logoImage, logo);
	}

	@Test
	void test_getWLHeaderLogo_success() {

		//setup
		String logo = whiteLabel.getHeaderLogoImage();
        String logoImage = imageConfigurationStub.getImagePrefix() + logo;

        //mock
		event.setWhiteLabel(whiteLabel);

		//Execution
		String wlHeaderLogo = eventDesignDetailServiceImpl.getWLHeaderLogo(event);
        assertNotEquals(wlHeaderLogo, logoImage);
	}

	@Test
	void test_getWLHeaderLogo_success_with_whiteLabel_headerLogoImage_empty() {

		//setup
		whiteLabel.setHeaderLogoImage("");

		event.setWhiteLabel(whiteLabel);

		//Execution
		String wlHeaderLogo = eventDesignDetailServiceImpl.getWLHeaderLogo(event);
		assertNull(wlHeaderLogo);
	}

	@Test
	void test_getWLHeaderLogo_success_with_whiteLabel_headerLogoImage_null() {

		//setup
		whiteLabel.setHeaderLogoImage(null);

		event.setWhiteLabel(whiteLabel);

		//Execution
		String wlHeaderLogo = eventDesignDetailServiceImpl.getWLHeaderLogo(event);
		assertNull(wlHeaderLogo);
	}

	@Test
	void test_getWLHeaderLogo_success_with_whiteLabel_null() {

		//setup
		event.setWhiteLabel(null);

		//Execution
		String wlHeaderLogo = eventDesignDetailServiceImpl.getWLHeaderLogo(event);
		assertNull(wlHeaderLogo);
	}

	@Test
	void test_getEventOrWhiteLabelHeaderLogoLocation_success() {

		//setup
		String imageLocation = "test/URL/headerLogoImage.png";

        whiteLabel.setHeaderLogoImage("headerLogoImage.png");
        when(imageConfiguration.getCloudinaryImageUrlForItem(whiteLabel.getHeaderLogoImage(), "100")).thenReturn("test/URL/headerLogoImage.png");
		event.setWhiteLabel(whiteLabel);

		//Execution
		String eventOrWhiteLabelHeaderLogoLocation = eventDesignDetailServiceImpl.getEventOrWhiteLabelHeaderLogoLocation(event);
		assertEquals(imageLocation, eventOrWhiteLabelHeaderLogoLocation);
	}

	@Test
	void test_getEventOrWhiteLabelHeaderLogoLocation_success_with_whiteLabel_headerLogoImage_empty() {

		//setup
		whiteLabel.setHeaderLogoImage("");

		event.setWhiteLabel(whiteLabel);

		String imageLocation = "";

		//Execution
		String eventOrWhiteLabelHeaderLogoLocation = eventDesignDetailServiceImpl.getEventOrWhiteLabelHeaderLogoLocation(event);
		assertEquals(eventOrWhiteLabelHeaderLogoLocation, imageLocation);
	}

	@Test
	void test_getEventOrWhiteLabelHeaderLogoLocation_success_with_whiteLabel_null() {

		//setup
		event.setWhiteLabel(null);

		String imageLocation = "";

		//Execution
		String eventOrWhiteLabelHeaderLogoLocation = eventDesignDetailServiceImpl.getEventOrWhiteLabelHeaderLogoLocation(event);
		assertEquals(eventOrWhiteLabelHeaderLogoLocation, imageLocation);
	}

	@Test
	void test_updateEventDesignLogo_success() {

		//mock
		Mockito.doReturn(eventDesignDetail).when(eventDesignDetailServiceImpl).findByEvent(event);

		//Execution
		String logoImageUrl = "logoImageUrl";
		eventDesignDetailServiceImpl.updateEventDesignLogo(event, logoImageUrl);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		Mockito.verify(eventDesignDetailRepository, Mockito.times(1)).save(eventDesignDetailArgumentCaptor.capture());

		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();

		assertEquals(eventDesignDetailData.getLogoImage(), logoImageUrl);
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), eventDesignDetail.getHeaderLogoImage());
		assertEquals(eventDesignDetailData.getEvent().getEventId(), eventDesignDetail.getEvent().getEventId());
	}
}