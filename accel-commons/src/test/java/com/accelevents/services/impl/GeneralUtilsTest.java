package com.accelevents.services.impl;


import com.accelevents.utils.Constants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.accelevents.utils.GeneralUtils.isValidEmailAddress;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class GeneralUtilsTest {



    @Test
    void test_isValidEmailAddress_withValidEmailAddress() {

        String email = "<EMAIL>";
        //Execution
        boolean validEmailAddress = isValidEmailAddress(email);

        //Assertion
        assertTrue(validEmailAddress);
    }

    @Test
    void test_isValidEmailAddress_withInvalidEmailAddress(){

        String email = "abc.com@Invalid";
        //Execution
        boolean validEmailAddress = isValidEmailAddress(email);

        //Assertion
        assertFalse(validEmailAddress);
    }

    @Test
    void test_isValidEmailAddress_withEmptyEmailAddress(){

        String email = Constants.STRING_EMPTY;
        //Execution
        boolean validEmailAddress = isValidEmailAddress(email);

        //Assertion
        assertFalse(validEmailAddress);
    }

    @Test
    void test_isValidEmailAddress_withNullEmailAddress(){

        String email=null;
        //Execution
        boolean validEmailAddress = isValidEmailAddress(email);

        //Assertion
        assertFalse(validEmailAddress);
    }
}
