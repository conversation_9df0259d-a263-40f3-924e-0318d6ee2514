package com.accelevents.services.impl;

import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.*;
import com.accelevents.dto.CardInfoDto;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.dto.StripeCreditCardDto;
import com.accelevents.messages.StripeIntervalEnum;
import com.accelevents.messages.WLTypeEnum;
import com.accelevents.repositories.StripeCustomersRepository;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.SquarePaymentService;
import com.accelevents.services.StripePaymentService;
import com.accelevents.services.UserService;
import com.accelevents.ticketing.dto.ChargeDto;
import com.accelevents.utils.Constants;
import com.squareup.square.models.CreatePaymentResponse;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
public class StripePaymentServiceImplTest {

    @Spy
    @InjectMocks
    private StripePaymentServiceImpl stripePaymentServiceImpl;
    @Mock
    private StripePaymentService stripePaymentService;
    @Mock
    private SquarePaymentService squarePaymentService;
    @Mock
    private StripeCustomersRepository stripeCustomersRepository;
    @Spy
    private StripeConfiguration stripeConfiguration = new StripeConfiguration();
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

	private static final Account class2 = mock(Account.class);

	private CustomerCardDto customerCardDto;
	private Event event;
	private CardInfoDto cardInfoDto;
    private StripeTransaction stripeTransaction;
	private com.squareup.square.models.Money money;
	private Coupon coupon;

	private String customerId = "cus_FSjuTT9VAXQn8x";
    private String stripeAccessToken = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private String successStatus = "succeeded";
    private String cardId = "card_1ExoPaLm9ntTNZl0mVfh9vY1";
    private String currency = "USD";
    private String statementDescriptor = "1SDR";
    private String locationId = "location id";
    private String tenderId = "tender Id";
	private String token = "tok_br";
    private String email = "<EMAIL>";
    private String description = "description";
    private String lastFour = "1111";
    private String accessToken = "accessToken";
	private String stripeUserId = "1";
	private String tokenOrCardNonce = "tokenOrCardNonce";
    private String refundStatus = "refunded";
    private String reason = "duplicate";
    private String apiKey = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private int limit = 1;
    private double amount = 100d;
    private double applicationFee = 0d;
    private double applicationFeeRefundAmount = 1d;
    private double ccPercentageFee = CREDIT_CARD_PROCESSING_PERCENTAGE;
    private double ccFlatFee = CREDIT_CARD_PROCESSING_FLAT;

	@BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();
		User user = EventDataUtil.getUser();

		Stripe stripe = new Stripe();

		Charge charge = new Charge();

		ChargeDto chargeDto = new ChargeDto();

        CreatePaymentResponse chargeResponse = new CreatePaymentResponse(null, null);

		Card card = new Card();

		Customer customer = new Customer();

		FeeRefund feeRefund = new FeeRefund();

		RefundInfoDto refundInfoDto = new RefundInfoDto();

		Account account = new Account();

		StripeCreditCardDto stripeCreditCardDto = new StripeCreditCardDto();

		StripeCustomers stripeCustomers = new StripeCustomers();

		Subscription subscription = new Subscription();

        coupon = new Coupon();

		Plan plan = new Plan();

		ExternalAccountCollection externalAccountCollection = new ExternalAccountCollection();

        stripeConfiguration.setAPI_KEY("sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf");
        stripeConfiguration.setAuctionProductId("prod_Cen5qwPvDrUYv9");
        stripeConfiguration.setRaffleProductId("prod_Cen5RURzoolzB6");
        stripeConfiguration.setFundANeedProductId("prod_Cen5hDjNygMuzg");
    }

    //TODO: Junit review lots of test commented since 2019 if no more valid remove them else fix them
    /*@Test
    void test_createChargeToCustomer_success() throws StripeException {

        //setup
        String chargeId = "ch_1ExooeLm9ntTNZl0jQxqRc5F";

        stripe.setCCPercentageFee(ccPercentageFee);
        stripe.setCCFlatFee(ccFlatFee);
        stripe.setAccessToken(stripeAccessToken);

        charge = new Charge();
        charge.setId(chargeId);

        BigDecimal amount1 = BigDecimal.valueOf(StripeUtil.getAmountToCharge(amount, stripe.getCCPercentageFee(),
                stripe.getCCFlatFee()) * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN);

        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> chargeParams = new HashMap<>();
        RequestOptions requestOptions = RequestOptions.builder().setApiKey(stripe.getAccessToken()).build();
        String transferGrp = event.getEventId() + "TICKETING";
        //Execution
        Charge chargeData = stripePaymentServiceImpl.createChargeToCustomer(stripe, customerId,
                amount, currency, true, applicationFee, statementDescriptor, metadata, transferGrp,null, null);

        assertEquals(chargeData.getStatus(), successStatus);
        assertEquals(chargeData.getCurrency(), currency.toString().toLowerCase());
        assertEquals(chargeData.getStatementDescriptor(), statementDescriptor);
        assertEquals(chargeData.getAmount().longValue(), amount1.longValue());
    }*/

    /*@Test
    void test_createCharge_success() throws StripeException{

        //setup
        String customerId = "cus_FSkzZqbZBONC6a";
        String cardId1 = "card_1ExpTSLm9ntTNZl0dwBlcWya";
        BigDecimal amount1 = BigDecimal.valueOf(amount * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN);
        stripe.setCCPercentageFee(ccPercentageFee);
        stripe.setCCFlatFee(ccFlatFee);
        stripe.setAccessToken(stripeAccessToken);

        Map<String, Object> metadata = new HashMap<>();

        //Execution
        Charge chargeData = stripePaymentServiceImpl.createCharge(eventBillingInfo.getTokenOrIntentId(), apiKey, customerId, cardId1, amount, currency, metadata, statementDescriptor);

        assertEquals(chargeData.getStatus(), successStatus);
        assertEquals(chargeData.getCurrency(), currency.toString().toLowerCase());
        assertEquals(chargeData.getStatementDescriptor(), statementDescriptor);
        assertEquals(chargeData.getAmount().longValue(), amount1.longValue());
    }*/

    /*@Test
    void test_createCustomer_success() throws StripeException{

        //setup
        Map<String, Object> metadata = new HashMap<>();

        //Execution
        CustomerCardDto customerCardData = stripePaymentServiceImpl.createCustomerAndCardToCustomer(apiKey, token, metadata, email, description);

        assertNotNull(customerCardData.getCustomerId());
        assertNull(customerCardData.getCardId());
    }*/

    @Test
    void test_createCustomer1_success() throws StripeException {
        //setup
        User user = new User();
        user.setFirstName("Jon");
        user.setLastName("Kazarian");

        //mock
        when(roUserService.findByEmail(email)).thenReturn(user);

        //Execution
        Customer customerData = stripePaymentServiceImpl.createCustomerAndAddCardToCustomer(apiKey, email);

        assertEquals(customerData.getEmail(), email);
        assertEquals(customerData.getName(), user.getFirstName()+ " "+ user.getLastName());
        assertNotNull(customerData);
    }

    /*@Test
    void test_retrieveCustomer_success() throws StripeException{

        //Execution
        Customer customerData = stripePaymentServiceImpl.retrieveCustomer(apiKey, customerId);

        assertEquals(customerData.getEmail(), email);
        assertEquals(customerData.getId(), customerId);
        assertNotNull(customerData);
    }

    @Test
    void test_retrieveDefaultCardOfCustomer_success() throws StripeException{

        //setup
        String lastFour =  "0002";
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";

        //Execution
        Card defaultCard = stripePaymentServiceImpl.retrieveDefaultPaymentIdOfCustomer(apiKey, customerId);

        assertEquals(defaultCard.getLast4(), lastFour);
        assertEquals(defaultCard.getBrand(), cardType);
        assertEquals(defaultCard.getCustomer(), customerId);
        assertEquals(defaultCard.getId(), cardId);
        assertNotNull(defaultCard);
    }*/

    @Test
    void test_getDefaultSource_success() {

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";

        //Execution
        String defaultSource = stripePaymentServiceImpl.getDefaultSource(apiKey, customerId);

        assertEquals(defaultSource, cardId);
    }

    @Test
    void test_getDefaultSource_throwException() {

        //mock
        Customer customer = new Customer();
        Customer spy = Mockito.spy(customer);



        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";

        //Execution
        try{
            stripePaymentServiceImpl.getDefaultSource(apiKey, customerId);
        } catch(Exception e){

        }
    }

    /*@Test
    void test_addCardToCustomer_success() throws StripeException {

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";

        //Execution
        String defaultSource = stripePaymentServiceImpl.addCardToCustomer(apiKey, customerId, token, true, true);

        assertEquals(defaultSource, cardId);
    }*/

    /*@Test
    void test_addCardToCustomer_throwException_CARD_ALREADY_LINKED() throws StripeException {

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";


        //Execution
        stripePaymentServiceImpl.addCardToCustomer(apiKey, customerId, token, true, false);
    }*/

    @Test
    void test_updateDefaultCard_success() throws StripeException{

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";

        //Execution
        stripePaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId);
    }

    @Test
    void test_retrieveCardById_success() throws StripeException{

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";
        String lastFour =  "0002";

        Map<String, Object> cardParams = new HashMap<>();
        cardParams.put("limit", 30);
        cardParams.put("object", "card");

        //Execution
        StripeCreditCardDto cardData = stripePaymentServiceImpl.retrieveCardById(apiKey, customerId, cardId);

        assertEquals(cardData.getLast4(), lastFour);
		String cardType = "visa";
		assertEquals(cardData.getCardType(), cardType);
        assertEquals(cardData.getId(), cardId);
    }

//    @Test
    void test_createTransfer_success() throws StripeException{

        //setup
        String wlStripeId = "acct_15STGkC8ZWKjh9MC";
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("EVENT_URL",event.getEventURL());
        metadata.put("EVENT_NAME",event.getName());
        metadata.put("BUYER_NAME", null);
        metadata.put("WL_TYPE", WLTypeEnum.A);

        //Execution
        Transfer transferData = stripePaymentServiceImpl.createTransfer(amount,wlStripeId,event.getCurrency().getISOCode(),"testWhiteLabelUrl",metadata);

        assertEquals(transferData.getCurrency(), currency.toLowerCase());
    }

   /* @Test
    void test_reversalTransfer_success() throws StripeException {

        //setup
        Map<String, String> metadata = new HashMap<>();
        String transferId = "tr_1F9piJIkDe2ww12p4GHnBswk";

        //mock
        PowerMockito.when(stripeConfiguration.getAPI_KEY()).thenReturn(apiKey);

        //Execution
        Reversal reversalTransferData = stripePaymentServiceImpl.reversalTransfer(transferId, metadata, reason, amount);

        assertEquals(reversalTransferData.getTransfer(), transferId);
    }*/

    @Test
    void test_createApplicationFeeRefund_success_with_fee_ApplicationNull() throws StripeException {

        //setup
        Map<String, String> metadata = new HashMap<>();
        metadata.put("EVENT_URL",event.getEventURL());
        metadata.put("EVENT_NAME",event.getName());
        metadata.put("BUYER_NAME", null);

        //Execution
		String chargeId = "ch_1ExooeLm9ntTNZl0jQxqRc5F";
		FeeRefund applicationFreeRefund = stripePaymentServiceImpl.createApplicationFeeRefund(apiKey, chargeId, amount, metadata);

        assertNull(applicationFreeRefund);
    }

   /* @Test
    void test_getAllCardsForCustomer_success() throws StripeException {

        //setup
        String cardId = "card_1Exrr7Lm9ntTNZl0F63q1D8v";
        String lastFour =  "0002";

        //Execution
        List<StripeCreditCardDto> allCardsForCustomer = stripePaymentServiceImpl.getAllCardsForCustomer(apiKey, customerId, 1);

        for (StripeCreditCardDto actualData : allCardsForCustomer) {
            assertEquals(actualData.getId(), cardId);
            assertEquals(actualData.getCardType(), cardType);
            assertEquals(actualData.getLast4(), lastFour);
            assertTrue(actualData.isDefaultCard());
        }
    }*/

    /*@Test
    void createDifferedAccount() throws StripeException{             //com.stripe.exception.InvalidRequestException: Deferred Account activation is currently in private beta for grandfathered livemode users only.
                                                                            // We highly recommend using Standard Accounts for a better quality experience. For more information, please contact <NAME_EMAIL>.
        com.stripe.Stripe.apiKey = apiKey;

        //setup
        String country = Country.US.toString();
        String email = "<EMAIL>";

        RequestOptions requestOptions = RequestOptions.builder().setApiKey(stripeConfiguration.getAPI_KEY()).build();

        Map<String, Object> accountParams = new HashMap<>();
        accountParams.put("country", country);
        accountParams.put("type", "standard");

        //Execution
        Account accountData = stripePaymentServiceImpl.createDifferedAccount(country, email);

        assertNotNull(accountData);
    }*/

    @Test
    void test_getLast4Digits_success() {

        //setup
        String lastFour =  "0002";

        //Execution
        String lastFourDigitOfCard  = stripePaymentServiceImpl.getLast4Digits(apiKey, customerId);

        assertEquals(lastFourDigitOfCard, lastFour);
    }

    @Test
    void test_getLast4Digits_throwException() throws StripeException{

        String customerId = "cus_FSjuTT9VAXQn8x1zx";

        //Execution
        stripePaymentServiceImpl.getLast4Digits(apiKey, customerId);
    }

    @Test
    void retrieveCharges() throws StripeException {

        //Execution
        ChargeCollection retrieveCharges = stripePaymentServiceImpl.retrieveCharges(apiKey);
        assertNotNull(retrieveCharges);
    }

    /*@Test
    void test_createRefund_success_with_refundApplicationFee_false() throws StripeException{

        //setup
        String apiKey = "sk_test_oD5couK6XtUZsGhYgCmy1Wmh";
        double refundAmount = 1d;
        Map<String, String> metadata = new HashMap<>();
        metadata.put("EVENT_URL",event.getEventURL());
        metadata.put("EVENT_NAME",event.getName());

        ChargeCollection retrieveCharges = stripePaymentServiceImpl.retrieveCharges(apiKey);
        String chargeId = retrieveCharges.getData().iterator().next().getId();


        //Execution
        Refund refundData = stripePaymentServiceImpl.createRefund(apiKey, chargeId, refundAmount, metadata, reason, false);

        assertNotNull(refundData);
        assertTrue(refundData.getAmount() == amount);
        assertEquals(refundData.getStatus(), "succeeded");
        assertEquals(refundData.getCurrency(), currency.toLowerCase());
        assertEquals(refundData.getCharge(), chargeId);
    }*/

    /* @Test
     void test_createRefund_success_with_refundApplicationFee_true() throws StripeException{      //refund_application_fee can only be used by the Connect application that created the charge.

         //setup
         double refundAmount = 10d;
         String chargeId = "ch_1ExooeLm9ntTNZl0jQxqRc5F";
         Map<String, String> metadata = new HashMap<>();
         metadata.put("EVENT_URL",event.getEventURL());
         metadata.put("EVENT_NAME",event.getName());

         //Execution
         Refund refundData = stripePaymentServiceImpl.createRefund(apiKey, chargeId, refundAmount, metadata, reason, true);

         assertNotNull(refundData);
     }*/

//    @Test
//    void test_retrieveAccount_success() throws StripeException{
//
//        //setup
//        String email = "<EMAIL>";
//        String apiKey = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
//        String accountId = "acct_19KYL3I9f3YkXHM0";
//
//        //mock
//        when(stripeConfiguration.getAPI_KEY()).thenReturn(apiKey);
//
//        //Execution
//        Account accountData = stripePaymentServiceImpl.retrieveAccount(accountId);
//
//        assertEquals(accountData.getId(), accountId);
//        assertEquals(accountData.getEmail(), email);
//    }

    @Test
    void test_getAccountByEventType_notConnectedAccount() {

        //setup
        String json = EventDataUtil.getJsonValue();

        //Execution
        Account accountDetails = stripePaymentServiceImpl.getAccountByEventType(json);

        assertNull(accountDetails);
    }

    @Test
    void test_getAccountByEventType_success() {

        //setup
        String json = "{\"created\":**********,\"livemode\":false,\"id\":\"evt_00000000000000\",\"type\":\"account.updated\",\"object\"" +
                ":\"event\",\"request\":null,\"pending_webhooks\":1,\"account\":accountData,\"api_version\":\"2019-05-16\",\"data\":{\"object\"" +
                ":{\"id\":\"acct_00000000000000\",\"object\":\"account\",\"business_profile\":{\"mcc\":null,\"name\":null," +
                "\"product_description\":null,\"support_address\":null,\"support_email\":null,\"support_phone\":null," +
                "\"support_url\":null,\"url\":null},\"business_type\":null,\"charges_enabled\":false,\"country\":\"US\"," +
                "\"created\":**********,\"default_currency\":\"usd\",\"details_submitted\":true,\"email\":\"<EMAIL>\"" +
                ",\"external_accounts\":{\"object\":\"list\",\"data\":[],\"has_more\":false,\"total_count\":0,\"url\":\"" +
                "/v1/accounts/acct_1Ar2YqIycx4IvA0K/external_accounts\"},\"metadata\":{},\"payouts_enabled\":false," +
                "\"requirements\":{\"current_deadline\":null,\"currently_due\":[\"business_type\",\"business_url\"," +
                "\"company.address.city\",\"company.address.line1\",\"company.address.postal_code\",\"company.address.state\"," +
                "\"external_account\",\"person_BDTVd7anH77eVp.dob.day\",\"person_BDTVd7anH77eVp.dob.month\",\"person_BDTVd7anH77eVp.dob.year\"" +
                ",\"person_BDTVd7anH77eVp.first_name\",\"person_BDTVd7anH77eVp.last_name\",\"product_description\",\"support_phone\"," +
                "\"tos_acceptance.date\",\"tos_acceptance.ip\"],\"disabled_reason\":\"requirements.past_due\"," +
                "\"eventually_due\":[\"business_url\",\"external_account\",\"product_description\",\"support_phone\",\"tos_acceptance.date\"," +
                "\"tos_acceptance.ip\"],\"past_due\":[]},\"settings\":{\"branding\":{\"icon\":null,\"logo\":null,\"primary_color\":null}," +
                "\"card_payments\":{\"decline_on\":{\"avs_failure\":false,\"cvc_failure\":false},\"statement_descriptor_prefix\":null}," +
                "\"dashboard\":{\"display_name\":\"Viki\",\"timezone\":\"Asia/Calcutta\"},\"payments\":{\"statement_descriptor\":null\"," +
                "\"statement_descriptor_kana\":null,\"statement_descriptor_kanji\":null},\"payouts\":{\"debit_negative_balances\":true," +
                "\"schedule\":{\"delay_days\":2,\"interval\":\"manual\"},\"statement_descriptor\":null}},\"tos_acceptance\":{\"date\":null,\"ip\":null," +
                "\"user_agent\":null},\"type\":\"standard\",\"statement_descriptor\":\"TEST\"},\"previous_attributes\":{\"verification\":{\"fields_needed\":[],\"due_by\":null}}}}";

        //mock
        com.stripe.model.Event event1 = new com.stripe.model.Event();
        com.stripe.model.Event spy =  Mockito.spy(event1);


        //Execution
        Account accountDetails = stripePaymentServiceImpl.getAccountByEventType(json);

        assertEquals(accountDetails.getId(), "acct_00000000000000");
        assertEquals(accountDetails.getEmail(), "<EMAIL>");
        assertEquals(accountDetails.getCountry(), "US");
        assertEquals(accountDetails.getObject(), "account");
        assertEquals(accountDetails.getDefaultCurrency(), "usd");
    }

    @Test
    void test_getAccountByEventType_success_accountType_different() {

        //setup
        String json = "{\"created\":**********,\"livemode\":false,\"id\":\"evt_00000000000000\",\"type\":\"account.updated1\",\"object\"" +
                ":\"event\",\"request\":null,\"pending_webhooks\":1,\"account\":accountData,\"api_version\":\"2019-05-16\",\"data\":{\"object\"" +
                ":{\"id\":\"acct_00000000000000\",\"object\":\"account\",\"business_profile\":{\"mcc\":null,\"name\":null," +
                "\"product_description\":null,\"support_address\":null,\"support_email\":null,\"support_phone\":null," +
                "\"support_url\":null,\"url\":null},\"business_type\":null,\"charges_enabled\":false,\"country\":\"US\"," +
                "\"created\":**********,\"default_currency\":\"usd\",\"details_submitted\":true,\"email\":\"<EMAIL>\"" +
                ",\"external_accounts\":{\"object\":\"list\",\"data\":[],\"has_more\":false,\"total_count\":0,\"url\":\"" +
                "/v1/accounts/acct_1Ar2YqIycx4IvA0K/external_accounts\"},\"metadata\":{},\"payouts_enabled\":false," +
                "\"requirements\":{\"current_deadline\":null,\"currently_due\":[\"business_type\",\"business_url\"," +
                "\"company.address.city\",\"company.address.line1\",\"company.address.postal_code\",\"company.address.state\"," +
                "\"external_account\",\"person_BDTVd7anH77eVp.dob.day\",\"person_BDTVd7anH77eVp.dob.month\",\"person_BDTVd7anH77eVp.dob.year\"" +
                ",\"person_BDTVd7anH77eVp.first_name\",\"person_BDTVd7anH77eVp.last_name\",\"product_description\",\"support_phone\"," +
                "\"tos_acceptance.date\",\"tos_acceptance.ip\"],\"disabled_reason\":\"requirements.past_due\"," +
                "\"eventually_due\":[\"business_url\",\"external_account\",\"product_description\",\"support_phone\",\"tos_acceptance.date\"," +
                "\"tos_acceptance.ip\"],\"past_due\":[]},\"settings\":{\"branding\":{\"icon\":null,\"logo\":null,\"primary_color\":null}," +
                "\"card_payments\":{\"decline_on\":{\"avs_failure\":false,\"cvc_failure\":false},\"statement_descriptor_prefix\":null}," +
                "\"dashboard\":{\"display_name\":\"Viki\",\"timezone\":\"Asia/Calcutta\"},\"payments\":{\"statement_descriptor\":null\"," +
                "\"statement_descriptor_kana\":null,\"statement_descriptor_kanji\":null},\"payouts\":{\"debit_negative_balances\":true," +
                "\"schedule\":{\"delay_days\":2,\"interval\":\"manual\"},\"statement_descriptor\":null}},\"tos_acceptance\":{\"date\":null,\"ip\":null," +
                "\"user_agent\":null},\"type\":\"standard\",\"statement_descriptor\":\"TEST\"},\"previous_attributes\":{\"verification\":{\"fields_needed\":[],\"due_by\":null}}}}";

        //mock
        com.stripe.model.Event event1 = new com.stripe.model.Event();
        com.stripe.model.Event spy =  Mockito.spy(event1);

        //Execution
        Account accountDetails = stripePaymentServiceImpl.getAccountByEventType(json);

        assertNull(accountDetails);
    }

    @Test
    void test_createRecurringStripePlan_success() throws StripeException{

        //setup
        String planName = "Text To Donate";
        StripeIntervalEnum interval = StripeIntervalEnum.DAY;
        String productId = "prod_Cenq4VrQpqZ3EU";

        //Execution
        Plan planData = stripePaymentServiceImpl.createRecurringStripePlan(apiKey, planName, interval, com.accelevents.domain.enums.Currency.USD, amount, true, productId, ccPercentageFee, ccFlatFee);

        assertEquals(planData.getInterval(), interval.toString().toLowerCase());
        assertEquals(planData.getNickname(), planName);
    }

    @Test
    void test_createRecurringStripePlan_success_with_productId_empty() throws StripeException{

        //setup
        String planName = randomAlphaNumeric(1);
        StripeIntervalEnum interval = StripeIntervalEnum.DAY;
        String productId = "";

        //Execution
        Plan planData = stripePaymentServiceImpl.createRecurringStripePlan(apiKey, planName, interval, com.accelevents.domain.enums.Currency.USD, amount, true, productId, ccPercentageFee, ccFlatFee);

        assertEquals(planData.getInterval(), interval.toString().toLowerCase());
        assertEquals(planData.getNickname(), planName);
    }

    @Test
    void test_createRecurringStripePlan_success_with_planName_empty() throws StripeException{

        //setup
        String planName = randomAlphaNumeric(1);
        StripeIntervalEnum interval = StripeIntervalEnum.DAY;
        String productId = "prod_FSrOm2QbNraNPU";

        //Execution
        Plan planData = stripePaymentServiceImpl.createRecurringStripePlan(apiKey, planName, interval, com.accelevents.domain.enums.Currency.USD, amount, true, productId, ccPercentageFee, ccFlatFee);

        assertEquals(planData.getInterval(), interval.toString().toLowerCase());
        assertEquals(planData.getNickname(), planName);
    }

//    @Test
    void test_subscribeStripePlanToCustomer_success() throws StripeException{

        //setup
        String customerId = "cus_FVOnQCUIsSwnVV";
        String billing = "charge_automatically";
        String planId = "plan_FSrOyqCojnEYPJ";
        String message = Constants.STRIPE_PLAN_UNSUBSCRIBED;
        String defaultPaymentMethod = "pm_card_visa";

        //Execution
        Subscription planData = stripePaymentServiceImpl.subscribeStripePlanToCustomer(apiKey, customerId, planId, defaultPaymentMethod,event.getEventURL());

        assertEquals(planData.getCollectionMethod(), billing);
        assertEquals(planData.getItems().getData().get(0).getPlan().getId(), planId);

        String stripeSubscriptionId = stripePaymentServiceImpl.unSubscribeStripePlanToCustomer(apiKey, planData.getId(), false);
        assertEquals(stripeSubscriptionId, message);
    }

    //TODO: Commenting failing on stage need to fix.
    /*@Test
    void test_getCustomerSubscriptions_success() throws StripeException {

        //setup
        Plan plan = new Plan();
        plan.setAmount(1000L);
        plan.setInterval("1");
        plan.setCurrency(currency);
        plan.setInterval("month");

        Subscription subscription = new Subscription();
		String id = "1";
		subscription.setId(id);
        subscription.setBilling("charge_automatically");
        subscription.setQuantity(1L);
        subscription.setPlan(plan);
        subscription.setStatus("active");

        String planName = com.accelevents.domain.enums.Currency.USD.getSymbol() + subscription.getPlan().getAmount()/100 + Constants.SPACE_EACH_SPACE + subscription.getPlan().getInterval();

        //Execution
        List<StripeSubscriptionDto> customerSubscriptionData = stripePaymentServiceImpl.getCustomerSubscriptions(apiKey, customerId, com.accelevents.domain.enums.Currency.USD.getSymbol());

        for (StripeSubscriptionDto actualData : customerSubscriptionData) {
            assertEquals(actualData.getPlanName(), planName);
            assertEquals(actualData.getBillingType(), subscription.getBilling());
            assertEquals(subscription.getStatus(), actualData.getStatus());
            assertEquals(actualData.getCurrency(), subscription.getPlan().getCurrency().toLowerCase());
            assertTrue(actualData.getAmount() == subscription.getPlan().getAmount().longValue());
            assertEquals(Long.valueOf(actualData.getNumberOfSubscription()), subscription.getQuantity());
        }
    }*/

    /*@Test
    void test_unSubscribeStripePlanToCustomer_success_with_cancelAtPeriodEnd_true() throws StripeException{

        //will work on stage
        //setup
        com.stripe.Stripe.apiKey = apiKey;
        String planId = "plan_FSrOyqCojnEYPJ";
        Map<String, Object> metadata = new HashMap<>();
        CustomerCardDto customerCardData = stripePaymentServiceImpl.createCustomerAndCardToCustomer(apiKey, token, metadata, email, description);

        String customerId = customerCardData.getCustomerId();

        Subscription planData = stripePaymentServiceImpl.subscribeStripePlanToCustomer(apiKey, customerId, planId);
        String subscriptionId = planData.getId();
        Long currentPriorEnd = planData.getCurrentPeriodEnd();

        Map<String, Object> params = new HashMap<>();
        params.put("cancel_at_period_end", true);

        subscription.setCurrentPeriodEnd(currentPriorEnd);
        subscription.setId(subscriptionId);

        String message = Constants.STRIPE_PLAN_UNSUBSCRIBED_WITH_END_DATE.replace("{SUBSCRIPTION_END_DATE}", new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH ).format(new Date(subscription.getCurrentPeriodEnd()*1000)));

        //Execution
        String stripeSubscriptionId = stripePaymentServiceImpl.unSubscribeStripePlanToCustomer(apiKey, subscriptionId, true);
        assertEquals(stripeSubscriptionId, message);
    }*/

//    @Test
    void test_unSubscribeStripePlanToCustomer_success_with_cancelAtPeriodEnd_false() throws StripeException{

        //setup
        com.stripe.Stripe.apiKey = apiKey;

        String customerId = "cus_FVOnQCUIsSwnVV";
        String planId = "plan_FSrOyqCojnEYPJ";
        Long currentPriorEnd = 1566456245L;
        String subscriptionId = "sub_FTtG2GAqRMMtDn";
        String defaultPaymentMethod = "pm_card_visa";

        String message = Constants.STRIPE_PLAN_UNSUBSCRIBED;

        Subscription subscription = new Subscription();
        subscription.setCurrentPeriodEnd(currentPriorEnd);

        //Execution
        Subscription planData = stripePaymentServiceImpl.subscribeStripePlanToCustomer(apiKey, customerId, planId, defaultPaymentMethod,event.getEventURL());

        String stripeSubscriptionId = stripePaymentServiceImpl.unSubscribeStripePlanToCustomer(apiKey, planData.getId(), false);
        assertEquals(stripeSubscriptionId, message);
    }

//    @Test
    /* hide below code as not used, now we are using Chargbee instead of Stripe for module activation
    void test_createOrder_successwith_couponId_null() throws StripeException {

        //setup
        String status = "created";
        String type = "sku";
        String parent = "sku_DUep9V0ipQmvqj";

        Map<String, String> item1 = new HashMap<>();
        item1.put("type", type);
        item1.put("parent", parent);

        List<Object> itemsParams = new LinkedList<>();
        itemsParams.add(item1);

        Map<String, Object> metadata = new HashMap<>();

        //Execution
        Order orderDetails = stripePaymentServiceImpl.createOrder(apiKey, customerId, currency, metadata, statementDescriptor, itemsParams, email, null, null);
        assertEquals(orderDetails.getEmail(), email);
        assertEquals(orderDetails.getCurrency(), currency.toLowerCase());
        assertEquals(orderDetails.getStatus(), status);
        assertEquals(orderDetails.getItems().iterator().next().getParent(), parent);
        assertEquals(orderDetails.getItems().iterator().next().getType(), type);
    }
     */

//    @Test
    /* hide below code as not used, now we are using Chargbee instead of Stripe for module activation
    void test_createOrder_success_with_couponId() throws StripeException {

        //setup
        String status = "created";
        String type = "sku";
        String parent = "sku_DUep9V0ipQmvqj";
        //String orderId = "or_1EyxZfLm9ntTNZl0050btf6l";
        Map<String, String> item1 = new HashMap<>();
        item1.put("type", type);
        item1.put("parent", parent);

        List<Object> itemsParams = new LinkedList<>();
        itemsParams.add(item1);

        Map<String, Object> metadata = new HashMap<>();

        coupon.setId("15PER");

        //Execution
        Order orderDetails = stripePaymentServiceImpl.createOrder(apiKey, customerId, currency, metadata, statementDescriptor, itemsParams, email, coupon, null);

        assertEquals(orderDetails.getItems().iterator().next().getType(), type);
        assertEquals(orderDetails.getItems().iterator().next().getParent(), parent);
        assertEquals(orderDetails.getStatus(), status);
        assertEquals(orderDetails.getCurrency(), currency.toLowerCase());
        assertEquals(orderDetails.getEmail(), email);
    }

     */

   /* @Test
    void test_cardToCustomer_success_with_absorbCardFoundException_true() throws StripeException {

        //setup
        String brand = "Visa";
        String currency = "usd";
        String country = "BR";
        String lastFour = "0002";

        //Execution
        Card cardData = stripePaymentServiceImpl.cardToCustomer(apiKey, customerId, token, true, true);
        assertEquals(cardData.getBrand(), brand);
        assertEquals(cardData.getLast4(), lastFour);
        assertEquals(cardData.getCountry(), country);
        assertEquals(cardData.getCurrency(), currency);
    }*/

   /* @Test
    void test_cardToCustomer_throwException_CARD_ALREADY_LINKED() throws StripeException {


        //Execution
        stripePaymentServiceImpl.cardToCustomer(apiKey, customerId, token, true, false);
    }*/

    /*@Test
    void test_cardToCustomer_success() throws StripeException {

        //setup
        String brand = "Visa";
        String currency = "usd";
        String country = "BR";
        String lastFour = "0002";

        com.stripe.model.PaymentSourceCollection paymentSourceCollection = new com.stripe.model.PaymentSourceCollection();
        paymentSourceCollection.setHasMore(true);

        Map<String, Object> cardParams = new HashMap<>();
        cardParams.put("limit", 30);
        cardParams.put("object", "card");

        //mock
        com.stripe.model.Customer customer1 = new com.stripe.model.Customer();
        com.stripe.model.Customer spy =  Mockito.spy(customer1);
        Mockito.doReturn(paymentSourceCollection).when(spy).getSources();

        //Execution
        Card cardData = stripePaymentServiceImpl.cardToCustomer(apiKey, customerId, token, true, true);
        assertEquals(cardData.getBrand(), brand);
        assertEquals(cardData.getLast4(), lastFour);
        assertEquals(cardData.getCountry(), country);
        assertEquals(cardData.getCurrency(), currency);
    }*/



    public String randomAlphaNumeric(int count) {
        StringBuilder builder = new StringBuilder();
        while (count-- != 0) {
			String ALPHA_NUMERIC_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
			int character = (int)(Math.random()* ALPHA_NUMERIC_STRING.length());
            builder.append(ALPHA_NUMERIC_STRING.charAt(character));
        }
        return builder.toString();
    }
}
