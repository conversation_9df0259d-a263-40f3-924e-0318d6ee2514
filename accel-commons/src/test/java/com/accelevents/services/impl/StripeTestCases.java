package com.accelevents.services.impl;

import com.accelevents.messages.StripeIntervalEnum;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.stripe.Stripe;
import com.stripe.exception.*;
import com.stripe.model.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class StripeTestCases {

//    private Logger logger = LoggerFactory.getLogger(StripeTestCases.class);

    private Customer customer;

    private Plan plan;

    private String stripeKey = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";

    @Test
    void testStripePlanCreation() throws StripeException {
        createMonthlyPlan();
        Assertions.assertNotNull(plan.getId());
        try {
            plan.delete();
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        }
    }

    @Test
    void testStripePlanSubscription() {
        Stripe.apiKey = stripeKey;
        try {
            createMonthlyPlan();
            //createCustomerAndCardToCustomer();
            if (plan != null) {
                Map<String, Object> item = new HashMap<>();
                item.put("plan", plan.getId());
                Map<String, Object> items = new HashMap<>();
                items.put("0", item);
                Map<String, Object> params = new HashMap<>();
                params.put("customer", "cus_FSjuTT9VAXQn8x");
                params.put("items", items);
                Subscription subscription = Subscription.create(params);
                Assertions.assertNotNull(subscription.getId());
            }
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    @Test
    void testStripeDuplicatePlanSubscription() {
        Stripe.apiKey = stripeKey;
        try {
            createMonthlyPlan();
            createCustomer();
            if (customer != null && plan != null) {
                Map<String, Object> item = new HashMap<>();
                item.put("plan", plan.getId());
                Map<String, Object> items = new HashMap<>();
                items.put("0", item);
                Map<String, Object> params = new HashMap<>();
                params.put("customer", customer.getId());
                params.put("items", items);
                Subscription subscription = Subscription.create(params);
                Assertions.assertNotNull(subscription.getId());
                Subscription subscription1 = Subscription.create(params);
                Assertions.assertNotNull(subscription1.getId());
            }
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    //sub_Cbi86sG9W1B4VH
    //TODO: Fix test case after spring-boot-upgrade
    /*@Test
    void testCustomerSubscriptionRetrieve() {
        Stripe.apiKey = stripeKey;
        try {
            Customer customer = Customer.retrieve("cus_CesvNvrsBtb7Rp");
            List<Subscription> subscriptions = customer.getSubscriptions().getData();
            subscriptions.forEach(subscription -> {
                Plan stripePlan = subscription.getItems().getData().get(0).getPlan();
                if (Constants.TEXT_TO_GIVE.equalsIgnoreCase(stripePlan.getNickname()) && stripePlan.getAmount() == 2900)
                    logger.info(subscription.getId());
            });
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }*/

    @Test
    void testPlanRetrieve() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> planParams = new HashMap<>();
            planParams.put("limit", "100");
            PlanCollection plans = Plan.list(planParams);
            List<Plan> planList = plans.getData();
            planList.forEach(plan -> {
//                logger.info(plan.toJson());
				/*if("Text To Donate".equals(plan.getName())){
					logger.info(plan.getName());
				}*/
            });

        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    @Test
    void testUnSubscription() {
        Stripe.apiKey = stripeKey;
        try {
            Subscription subscription = Subscription.retrieve("sub_Cbi86sG9W1B4VH");
            subscription.cancel();
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    @Test
    void testProductCreate() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("name", "Text to Give");
            params.put("type", "service");
            Product product = Product.create(params);
//            logger.info(product.toJson());


        } catch (StripeException se) {
//            logger.error(se.getMessage(), se);
        }
    }

    @Test
    void testProductRetrieve() {
        Stripe.apiKey = "********************************";
        try {
            String productId = "prod_DUejuBD26hCY0O";
            Product product = Product.retrieve(productId);
//            logger.info(product.toJson());
			/*Map<String, Object> productParams = new HashMap<>();
			productParams.put("limit", "100");
			ProductCollection productCollection = Product.list(productParams);
			productCollection.getData().forEach(product1 -> {
				logger.info(product1.toJson());
			});*/

        } catch (StripeException se) {
//            logger.error(se.getMessage(), se);
        }
    }

    /* Below code unused , because in Stripe java sdk version 22 remove SKU class.
    @Test
    void testInventoryRetrieve() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> skuParams = new HashMap<>();
			skuParams.put("limit", "100");
            skuParams.put("product", "prod_DUejuBD26hCY0O");
			SkuCollection list = Sku.list(skuParams);
            list.getData().forEach(sku -> logger.info(sku.toJson()));

        } catch (StripeException se) {
            logger.error(se.getMessage(), se);
        }
    }

     */

    /* Below code unused , because in Stripe java sdk version 22 remove Order class.
    @Test
    void testOrderRetrieve() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> orderParams = new HashMap<>();
            orderParams.put("limit", "10");

            OrderCollection orders = Order.list(orderParams);
            orders.getData().forEach(order -> logger.info(order.getId() + " - " + order.getEmail() + " - " + order.getAmount() + " - " + order.getCurrency() + " - " + order.getStatus()));

        } catch (StripeException se) {
            logger.error(se.getMessage(), se);
        }
    }

     */
    @Test
    void testCouponRetrieve(){
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> couponParams = new HashMap<>();
            couponParams.put("limit", "10");

            CouponCollection coupons = Coupon.list(couponParams);
//            coupons.getData().forEach(coupon -> logger.info(coupon.toJson()));

        } catch (StripeException se) {
//            logger.error(se.getMessage(), se);
        }
    }

    @Test
    void testCreateCoupon(){
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> couponParams = new HashMap<>();
            couponParams.put("percent_off", 15);
            couponParams.put("duration", "once");
            couponParams.put("id", "15PER");

            Coupon.create(couponParams);

        } catch (StripeException se) {
//            logger.error(se.getMessage(), se);
        }
    }

    /* Below code unused , because in Stripe java sdk version 22 remove Order class.
    @Test
    void testCreateAndPayOrder() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> orderParams = new HashMap<>();
            orderParams.put("currency", "usd");
            List<Object> itemsParams = new LinkedList<>();
            Map<String, String> item1 = new HashMap<>();
            item1.put("type", "sku");
            item1.put("parent", "sku_DUep9V0ipQmvqj");
            itemsParams.add(item1);
            orderParams.put("items", itemsParams);
            Order order = Order.create(orderParams);
            createCustomer();
            orderParams = new HashMap<>();
            orderParams.put("customer", customer.getId());
            orderParams.put("email", customer.getEmail());
            order = order.pay(orderParams);
            logger.info(order.getId() + " - " + order.getEmail());

        } catch (StripeException se) {
            logger.error(se.getMessage(), se);
        }
    }
    /* Below code unused , because in Stripe java sdk version 22 remove Order class.
     */

    void makeProductsNonShippable(){
        List<String> productIds = Arrays.asList("prod_DUejuBD26hCY0O", "prod_DUejoxjET0lsuS", "prod_DUejBSZ33kdFY2");
        productIds.forEach(s -> {
            try {
                updateProduct(s);
            } catch (StripeException e) {
//                logger.error(e.getMessage(), e);
            }
        });
    }

    @Test
    void testSubscriptionsRetrieve() {
        Stripe.apiKey = stripeKey;
        try {
            Map<String, Object> subscriptionParams = new HashMap<>();
            subscriptionParams.put("limit", "100");
            SubscriptionCollection list = Subscription.list(subscriptionParams);
//            list.getData().forEach(product1 -> logger.info(product1.toJson()));

        } catch (StripeException se) {
//            logger.error(se.getMessage(), se);
        }
    }

    private void createMonthlyPlan() {
        try {
            Stripe.apiKey = stripeKey;
            Map<String, Object> productParams = new HashMap<>();
            productParams.put("name", "Text To Donate");

            Map<String, Object> params = new HashMap<>();
            params.put("product", productParams);
            params.put("nickname", "Text To Donate $10 Recurring Plan");
            params.put("interval", StripeIntervalEnum.MONTH.getValue());
            params.put("currency", "usd");
            params.put("amount", 1000);
            plan = Plan.create(params);
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    private void updateProduct(String id) throws StripeException {
        Stripe.apiKey = stripeKey;
        Product product = Product.retrieve(id);
        Map<String, Object> params = new HashMap<>();
        params.put("shippable", false);
        product.update(params);
    }

    private void createCustomer() {
        Stripe.apiKey = stripeKey;
        Map<String, Object> params = new HashMap<>();
        params.put("email", "<EMAIL>");
        String token = generateStripeToken();
        Assertions.assertNotNull(token);
        params.put("source", token);
        try {
            customer = Customer.create(params);
            Assertions.assertNotNull(customer.getId());
        } catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException | ApiException e) {
//            logger.error(e.getMessage(), e);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    private String generateStripeToken() {
        try {
            HttpResponse<JsonNode> jsonResponse = Unirest.post("https://api.stripe.com/v1/tokens")
                    .field("key", "pk_test_0xmci0YghCzVIK8y2PIlDhdY")
                    .field("card[number]", "****************")
                    .field("card[cvc]", "123")
                    .field("card[exp_month]", "10")
                    .field("card[exp_year]", "2030")
                    .asJson();
            return jsonResponse.getBody().getObject().get("id").toString();
        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
