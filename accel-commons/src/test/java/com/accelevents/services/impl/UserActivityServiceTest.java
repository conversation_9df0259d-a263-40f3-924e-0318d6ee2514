package com.accelevents.services.impl;

import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.repositories.EventRepository;
import com.accelevents.repositories.UserRepository;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.AttendeeProfileService;
import com.accelevents.services.DownloadService;
import com.accelevents.services.dynamodb.DynamoDBService;
import com.accelevents.services.dynamodb.user.activity.AttendeeTimelineDTO;
import com.accelevents.services.dynamodb.user.activity.UserActivity;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.session_speakers.repo.SpeakerRepo;
import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.ComparisonOperator;
import com.amazonaws.services.dynamodbv2.model.Condition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import javax.xml.bind.JAXBContext;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest(JAXBContext.class)
public class UserActivityServiceTest {

    @Spy
    @InjectMocks
    private UserActivityService userActivityService;

    @Mock
    private AttendeeProfileService attendeeProfileService;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private DynamoDBService<UserActivity> dynamoDBService;

    @Mock
    private NeptuneAttendeeDetailService neptuneAttendeeDetailService;

    @Mock
    private SpeakerRepo speakerRepo;

    @Mock
    private UserRepository userRepository;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private DownloadService downloadService;

    @Mock
    private ROStaffService roStaffService;

    private User user;
    private Event event;
    AttendeeProfileDto attendeeProfileDto;

    @BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

        attendeeProfileDto = new AttendeeProfileDto();
        attendeeProfileDto.setTitle("title");
        attendeeProfileDto.setUserId(user.getUserId());
        attendeeProfileDto.setLastName(user.getFirstName());
        attendeeProfileDto.setLastName(user.getLastName());

    }

    @Test
    void test_getAttendeeDetails(){

        Long currentTime = System.currentTimeMillis();

        List<UserActivity> userActivity = getUserActivity(currentTime);

        when(attendeeProfileService.getOrUpsertAttendeeProfile(user,event,false)).thenReturn(attendeeProfileDto);

        AttendeeProfileDto attendeeDetails = userActivityService.getAttendeeDetails(user, event);

        assertNotNull(attendeeDetails);
    }


    @Test
    void test_getUserActivity(){
        List<AttendeeTimelineDTO> userActivities = userActivityService.getUserActivity(user, event, true);

        assertNotNull(userActivities);
    }

    private Condition buildEventCondition(){

        Condition condition = new Condition();
        List<AttributeValue> attributeValueList = new ArrayList<>();

        Collections.singletonList(event.getEventId()).forEach(eventId-> attributeValueList.add(new AttributeValue().withN(String.valueOf(eventId))));

        return condition.withComparisonOperator(ComparisonOperator.IN)
                .withAttributeValueList(attributeValueList);

    }

    private UserActivity getHashFilter(){
        UserActivity hashFilter = new UserActivity();
        hashFilter.setUserId(user.getUserId());
        return hashFilter;
    }

    private List<UserActivity> getUserActivity(Long currentTime){
        UserActivity userActivity = new UserActivity();
        userActivity.setEventId(event.getEventId());
        userActivity.setActionType("Joined a event");
        userActivity.setDescription(new HashMap<>());
        userActivity.setTimestamp(currentTime);
        userActivity.setUserId(user.getUserId());
        return Collections.singletonList(userActivity);
    }


}