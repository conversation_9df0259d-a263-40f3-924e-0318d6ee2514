///*
//package com.accelevents.services.impl;
//
//import com.accelevents.configuration.SquareConfiguration;
//import com.accelevents.domain.Event;
//import com.accelevents.domain.Stripe;
//import com.accelevents.exceptions.NotAcceptableException;
//import com.accelevents.services.StripeService;
//import com.accelevents.utils.Constants;
//import com.mashape.unirest.http.HttpResponse;
//import com.mashape.unirest.http.JsonNode;
//import com.mashape.unirest.http.Unirest;
//import com.mashape.unirest.http.exceptions.UnirestException;
//import com.mashape.unirest.request.HttpRequestWithBody;
//import com.mashape.unirest.request.body.MultipartBody;
//import com.mashape.unirest.request.body.RequestBodyEntity;
//import org.json.JSONException;
//import org.json.JSONObject;
//import org.junit.rules.ExpectedException;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Spy;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//@PrepareForTest({ Unirest.class})
//public class SquareServiceImplTest {
//
//    @Spy
//    @InjectMocks
//    private SquareServiceImpl squareServiceImpl = new SquareServiceImpl();
//
//    @Mock
//    private SquareConfiguration squareConfiguration;
//
//    @Mock
//    private StripeService stripeService;
//
//    @Mock
//    private HttpResponse<JsonNode> httpResponse;
//
//    @Mock
//    private HttpRequestWithBody httpRequestWithBody;
//
//    @Mock
//    private RequestBodyEntity requestBodyEntity;
//
//    @Mock
//    private MultipartBody multipartBody;
//
//    @Mock
//    private JsonNode jsonNode;
//
//    private Event event;
//    private Stripe stripe;
//
//	private String accessToken = "Bearer sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w";
//
//    @BeforeEach
//    public void setUp() throws Exception {
//
//        PowerMockito.mockStatic(Unirest.class);
//
//        event = EventDataUtil.getEvent();
//        stripe = new Stripe();
//        stripe.setEvent(event);
//		String merchantLocation = "CBASEGMgZfZldaslqzMCk33YnHggAQ";
//		stripe.setSquareMerchantLocation(merchantLocation);
//        stripe.setAccessToken(accessToken);
//    }
//
//    @Test
//    public void test_getMobileAuthorizationCode_throwException_SQUARE_NOT_CONNECTED()  throws UnirestException, JSONException {
//
//        //mock
//        when(stripeService.getByEventId(event.getEventId())).thenReturn(null);
//
//        //Execution
//        try {
//            squareServiceImpl.getMobileAuthorizationCode(event);
//        } catch (NotAcceptableException e){ }
//    }
//
//    @Test
//    public void test_getMobileAuthorizationCode_throwException()  throws UnirestException, JSONException {
//
//        //setup
//        String mobileAuthorizedUri = "https://connect.squareup.com/mobile/authorization-code";
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.optString("authorization_code", accessToken);
//
//        //mock
//        when(stripeService.getByEventId(event.getEventId())).thenReturn(stripe);
//        PowerMockito.when(squareConfiguration.getMobileAuthorize_URI()).thenReturn(mobileAuthorizedUri);
//
//        when(Unirest.post(squareConfiguration.getMobileAuthorize_URI())).thenReturn(httpRequestWithBody);
//        when(httpRequestWithBody.header(Constants.Authorization, Constants.BEARER+stripe.getAccessToken())).thenReturn(httpRequestWithBody);
//        when(httpRequestWithBody.field("location_id", stripe.getSquareMerchantLocation())).thenReturn(multipartBody);
//        when(multipartBody.asJson()).thenReturn(httpResponse);
//        when(httpResponse.getBody()).thenReturn(jsonNode);
//
//        when(jsonNode.getObject()).thenReturn(jsonObject);
//
//        //Execution
//        try {
//            squareServiceImpl.getMobileAuthorizationCode(event);
//        } catch (Exception e){}
//    }
//
//    @Test
//    public void test_getMobileAuthorizationCode_success()  throws UnirestException, JSONException {
//
//        //setup
//        String mobileAuthorizedUri = "https://connect.squareup.com/mobile/authorization-code";
//        String authorizationCode = stripe.getAccessToken();
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.accumulate("authorization_code", accessToken);
//
//        //mock
//        when(stripeService.getByEventId(event.getEventId())).thenReturn(stripe);
//        PowerMockito.when(squareConfiguration.getMobileAuthorize_URI()).thenReturn(mobileAuthorizedUri);
//
//        when(Unirest.post(squareConfiguration.getMobileAuthorize_URI())).thenReturn(httpRequestWithBody);
//        when(httpRequestWithBody.header(Constants.Authorization, Constants.BEARER+stripe.getAccessToken())).thenReturn(httpRequestWithBody);
//        when(httpRequestWithBody.field("location_id", stripe.getSquareMerchantLocation())).thenReturn(multipartBody);
//        when(multipartBody.asJson()).thenReturn(httpResponse);
//        when(httpResponse.getBody()).thenReturn(jsonNode);
//
//        when(jsonNode.getObject()).thenReturn(jsonObject);
//
//        //Execution
//        String mobileAuthorizationCode = squareServiceImpl.getMobileAuthorizationCode(event);
//        assertEquals(mobileAuthorizationCode, authorizationCode);
//    }
//}*/
