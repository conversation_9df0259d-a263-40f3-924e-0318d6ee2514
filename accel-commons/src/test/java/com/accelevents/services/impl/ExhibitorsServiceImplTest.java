package com.accelevents.services.impl;

import com.accelevents.auction.dto.ExhibitorDetailsDto;
import com.accelevents.auction.dto.*;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.billing.chargebee.service.impl.EventPlanConfigServiceImpl;
import com.accelevents.common.dto.*;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EnumLabel;
import com.accelevents.domain.enums.EnumMultiLanguageLabelType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.exhibitors.EventExhibitorSettingsDto;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.exhibitors.ExhibitorDashboardDTO;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.virtual.LeadRetriverData;
import com.accelevents.domain.virtual.Sponsors;
import com.accelevents.domain.virtual.VirtualEventLogging;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.enums.StaffRole;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.exhibitors.dto.ExhibitorAttendeeAnalytics;
import com.accelevents.exhibitors.dto.ExpoSettingsDetails;
import com.accelevents.exhibitors.services.ExhibitorProductRepoService;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.BoothSize;
import com.accelevents.messages.SponsorSize;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.staff.ROStaffRoleService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.exhibitor.ExhibitorElasticsearchService;
import com.accelevents.services.elasticsearch.leaderboard.LeaderboardService;
import com.accelevents.services.neptune.NeptuneAttendeeDetailService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import com.accelevents.session_speakers.dto.MeetingAttendeeDto;
import com.accelevents.session_speakers.services.MeetingScheduleService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionSpeakerService;
import com.accelevents.session_speakers.services.VirtualEventLoggingService;
import com.accelevents.utils.Constants;
import com.accelevents.dto.PageUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.*;

import static com.accelevents.utils.Constants.LEAD_ALREADY_GENERATED;
import static com.accelevents.utils.Constants.SUCCESS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ExhibitorsServiceImplTest {

    @Spy
    @InjectMocks
    private ExhibitorsServiceImpl exhibitorsServiceImpl;
    @Mock
    private ExhibitorRepository exhibitorRepository;
    @Mock
    private MultiLanguageLabelRepoService multiLanguageLabelRepoService;
    @Mock
    private ExhibitorProductRepoService exhibitorProductRepoService;
    @Mock
    private ExhibitorDocumentService exhibitorDocumentService;
    @Mock
    private ExhibitorRepoService exhibitorRepoService;
    @Mock
    private VirtualEventLoggingRepository virtualEventLoggingRepository;
    @Mock
    private StaffRepository staffRepository;
    @Mock
    private GetStreamService getStreamService;
    @Mock
    private ExhibitorStaffCacheService exhibitorStaffCacheService;
    @Mock
    private LeadRetriverDataRepoService leadRetriverDataRepoService;
    @Mock
    private VirtualEventService virtualEventService;
    @Mock
    private ROVirtualEventService roVirtualEventService;
    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffRoleService roStaffRoleService;
    @Mock
    private EventChecklistService eventChecklistService;
    @Mock
    private ChallengeConfigService challengeConfigService;
    @Mock private ExhibitorSettingsService exhibitorSettingsService;
    @Mock
    private ChargebeeService chargebeeService;
    @Mock
    private MultiLanguageMasterService multiLanguageMasterService;
    @Mock
    private SponsorsRepoService sponsorsRepoService;
    @Mock
    private SponsorsService sponsorsService;
    @Mock
    private ExhibitorCategoryService exhibitorCategoryService;
    @Mock
    private TicketHolderRequiredAttributesService holderReqAttributeService;
    @Mock
    private ExhibitorElasticsearchService exhibitorElasticsearchService;
    @Mock
    private NeptuneAttendeeDetailService neptuneDBService;
    @Mock
    private AttendeeProfileService attendeeProfileService;
    @Mock
    private SessionRepoService sessionService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private LeadService leadService;
    @Mock
    private UserService userService;
    @Mock
    private StaffRepoService staffRepoService;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private VirtualEventLoggingService virtualEventLoggingService;
    @Mock
    private LeaderboardService leaderboardService;
    @Mock
    private MeetingScheduleService meetingSchedule;
    @Mock
    private ChimeService chimeService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private SessionSpeakerService sessionSpeakerService;
    @Mock
    TrayTrackingService trayTrackingService;
    @Mock
    private EventPlanConfigServiceImpl eventPlanConfigService;
    @Mock
    private ExternalEventSyncTrackingRepository externalEventSyncTrackingRepository;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Mock
    private EventExhibitorSettingsService eventExhibitorSettingsService;

    @Mock
    private EventTaskService eventTaskService;
    private Event event;
    private User user;
    private ExhibitorDto exhibitorDto;
    private Exhibitor exhibitor;
    private MultiLanguageMaster multiLanguageMaster;
    private ExhibitorDocument exhibitorDocument;
    private KeyValueDto keyValueDTO;
    private KeyValuePositionDto keyValuePositionDto;
    private Sponsors sponsors;
    private EventChecklist eventChecklist;
    private ExhibitorCategory exhibitorCategory;
    private EventChallengeDTO eventChallengeDTO;
    private Staff staff;
    private ExhibitorSetting exhibitorSetting;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private StaffAndLeadCountDto staffAndLeadCountDto;
    private LeadRetriverData leadRetriverData;
    private ExhibitorBasicAnalytics exhibitorBasicAnalytics;
    private ExhibitorsActivity exhibitorsActivity;
    private AttendeeProfileDto attendeeProfileDto;
    private Session session;
    private EventTickets eventTickets;
    private LogClickDocumentDto logClickDocumentDto;
    private VirtualEventLogging virtualEventLogging;
    private ExhibitorDashboardDTO exhibitorDashboardDTO;
    private EventExhibitorSettingsDto eventExhibitorSettingsDto;
    private Ticketing ticketing;
    private long exhibitorId = 1L;
    private String testAppsyncAuthToken = "test-auth-token";

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

        exhibitor = new Exhibitor();
        exhibitor.setId(exhibitorId);
        exhibitor.setEvent(event);
        exhibitor.setEventId(event.getEventId());
        exhibitor.setCurrency(event.getCurrency());
        exhibitor.setLeadRetrieversAllowed(true);
        exhibitor.setMaxLeadRetrivers(10);
        exhibitor.setName("Exhibitor");
        exhibitor.setSeeAllLeads(true);
        exhibitor.setExpoLayout(Constants.ExpoLayout.LOGO_AND_IMAGE);

        keyValueDTO = new KeyValueDto();
        keyValueDTO.setKey("EXPO");
        keyValueDTO.setValue("BOOTH");

        exhibitorDto = new ExhibitorDto();
        exhibitorDto.setExhibitorName("Exhibitor");
        exhibitorDto.setLogo("expo_logo");
        exhibitorDto.setShortDescription("test");
        exhibitorDto.setExpoCardImage("expo_card_image");
        exhibitorDto.setBoothSize(BoothSize.MEDIUM);
        exhibitorDto.setIncludeInExhibitorCarousel(true);
        exhibitorDto.setCarouselLogoSliderDelaySeconds(1000);
        exhibitorDto.setLeadRetrieversAllowed(true);
        exhibitorDto.setMaxLeadRetrievers(10);
        exhibitorDto.setSeeAllLeads(true);
        exhibitorDto.setCreateSponsor(true);
        exhibitorDto.setEventId(event.getEventId());
        exhibitorDto.setEventURL(event.getEventURL());
        exhibitorDto.setDocumentKeyValue(Collections.singletonList(keyValueDTO));

        multiLanguageMaster = new MultiLanguageMaster();
        multiLanguageMaster.setLanguageCode("EN");
        multiLanguageMaster.setCustomLabel("test");
        multiLanguageMaster.setDefaultLabel("Exhibitor");
        multiLanguageMaster.setId(1L);
        multiLanguageMaster.setLabel(EnumLabel.EXPO);
        multiLanguageMaster.setType(EnumMultiLanguageLabelType.EXPO);

        exhibitorDocument = new ExhibitorDocument();
        exhibitorDocument.setId(1L);
        exhibitorDocument.setPosition(1000);
        exhibitorDocument.setEventId(event.getEventId());
        exhibitorDocument.setEvent(event);
        exhibitorDocument.setExhibitorId(exhibitorId);
        exhibitorDocument.setExhibitor(exhibitor);
        exhibitorDocument.setName("expo_document");
        exhibitorDocument.setRecordStatus(RecordStatus.CREATE);

        keyValuePositionDto = new KeyValuePositionDto();
        keyValuePositionDto.setDocumentId(1L);
        keyValuePositionDto.setDocumentPosition(1000D);
        keyValuePositionDto.setKey("DOC");
        keyValuePositionDto.setValue("DOC");

        sponsors = new Sponsors();
        sponsors.setId(1L);
        sponsors.setSponsorName("sponsor_name");
        sponsors.setPosition(1000D);
        sponsors.setEventId(event.getEventId());
        sponsors.setEvent(event);
        sponsors.setSponsorSize(SponsorSize.MEDIUM);
        sponsors.setExhibitorId(exhibitorId);
        sponsors.setSponsorUrl("sponsor_url");

        eventChecklist = new EventChecklist();
        eventChecklist.setExhibitorAdded(false);

        exhibitorCategory = new ExhibitorCategory();
        exhibitorCategory.setPosition(1000L);
        exhibitorCategory.setEventId(event.getEventId());
        exhibitorCategory.setEvent(event);
        exhibitorCategory.setId(1L);
        exhibitorCategory.setName("Exhibitor_Category");

        Map<String, List<Long>> triggerMap = new HashMap<>();
        triggerMap.put(Constants.CHALLENGE_EXHIBITOR,new ArrayList(Arrays.asList(1L,2L)));

        eventChallengeDTO = new EventChallengeDTO();
        eventChallengeDTO.setName("challenge_DTO");
        eventChallengeDTO.setTicketTypeAllowInChallenge(new ArrayList(Arrays.asList(1L,2L)));
        eventChallengeDTO.setTrigger(triggerMap);

        staff = new Staff();
        staff.setEvent(event);
        staff.setUser(user);
        staff.setUserId(user.getUserId());
        staff.setRole(StaffRole.whitelabeladmin);
        staffService.save(staff);

        exhibitorSetting = new ExhibitorSetting();
        exhibitorSetting.setId(1L);
        exhibitorSetting.setExhibitorId(exhibitorId);
        exhibitorSetting.setExhibitor(exhibitor);
        exhibitorSetting.setExpoLiveStream(true);
        exhibitorSetting.setDescription("Hello test");
        exhibitorSetting.setEventId(1L);
        exhibitorSetting.setEvent(event);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName("Milan");
        ticketHolderRequiredAttributes.setId(1L);
        ticketHolderRequiredAttributes.setEventid(event);

        staffAndLeadCountDto = new StaffAndLeadCountDto(exhibitorId,10L);

        leadRetriverData = new LeadRetriverData();
        leadRetriverData.setId(1L);
        leadRetriverData.setUserId(user.getUserId());
        leadRetriverData.setStaffId(staff.getId());
        leadRetriverData.setExhibitorId(exhibitorId);
        leadRetriverData.setExhibitor(exhibitor);
        leadRetriverData.setNotes("test");

        exhibitorBasicAnalytics = new ExhibitorBasicAnalytics();
        exhibitorBasicAnalytics.setId(1L);
        exhibitorBasicAnalytics.setClickCount(1);

        exhibitorsActivity = new ExhibitorsActivity("Milan","Dobariya",new Date());

        attendeeProfileDto = new AttendeeProfileDto();
        attendeeProfileDto.setFirstName(user.getFirstName());
        attendeeProfileDto.setLastName(user.getLastName());
        attendeeProfileDto.setUserDetail(user);
        attendeeProfileDto.setEmail(user.getEmail());
        attendeeProfileDto.setCountry(user.getCountry());

        session = new Session();
        session.setId(1L);
        session.setEventId(1L);
        session.setEvent(event);

        eventTickets = new EventTickets();
        eventTickets.setId(1L);

        logClickDocumentDto = new LogClickDocumentDto();
        logClickDocumentDto.setDocumentName("DOC_NAME");
        logClickDocumentDto.setDocumentLink("DOC_LINK");

        virtualEventLogging = new VirtualEventLogging(event,1L,1L,"DOC_NAME","DOC_LINK");

        exhibitorDashboardDTO = new ExhibitorDashboardDTO();
        exhibitorDashboardDTO.setEventId(1L);
        exhibitorDashboardDTO.setId(1L);
        ticketing = new Ticketing();

        eventExhibitorSettingsDto = new EventExhibitorSettingsDto();
        eventExhibitorSettingsDto.setChatEnabled(true);
        eventExhibitorSettingsDto.setPollEnabled(true);
        eventExhibitorSettingsDto.setQuesAndAnsEnabled(true);
    }

    @Test
    void test_addExhibitor_success() {

        //setup
        exhibitorDto.setDocumentKeyPosition(Collections.singletonList(keyValuePositionDto));
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);

        //Mock
        User user = new User();
        user.setUserId(1L);
        Staff staff = new Staff();
        staff.setEvent(event);
        staff.setUser(user);
        exhibitor.setId(1L);
        exhibitor.setEvent(event);
        doReturn(exhibitor).when(exhibitorRepoService).save(any());


        doNothing().when(chargebeeService).validateExhibitorsByEventPlan(any(), anyLong());
        when(multiLanguageMasterService.getMultiLanguageMasterByType(EnumMultiLanguageLabelType.EXPO)).thenReturn(Collections.singletonList(multiLanguageMaster));
        when(exhibitorDocumentService.findByDocumentId(anyLong())).thenReturn(exhibitorDocument);
        when(exhibitorDocumentService.findByNameAndExhibitorId(anyString(), anyLong())).thenReturn(Optional.ofNullable(null));
        when(staffService.findAllEventAdminId(any())).thenReturn(Collections.singletonList(staff.getUser().getUserId()));
        when(sponsorsRepoService.getSponsorPresentByExhibitorIdForEvent(anyLong(), anyLong())).thenReturn(sponsors);
        when(eventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
        when(eventExhibitorSettingsService.getOrCreateEventExhibitorSettingsByEventId(event)).thenReturn(eventExhibitorSettingsDto);


        //Execution
        ExhibitorDto exhibitorDtoResult = exhibitorsServiceImpl.addExhibitor(this.exhibitorDto, event);

        //Assertion
        assertEquals(exhibitorDto.getMaxLeadRetrievers(), exhibitorDtoResult.getMaxLeadRetrievers());
        assertEquals(exhibitorDto.isLeadRetrieversAllowed(), exhibitorDtoResult.isLeadRetrieversAllowed());
        assertEquals(exhibitorDto.isSeeAllLeads(), exhibitorDtoResult.isSeeAllLeads());

        verify(staffService).findAllEventAdminId(any());
        verify(sponsorsRepoService).save(any(), anyString());
    }

    @Test
    void test_deleteExhibitor_success() {
        //Setup
        exhibitor.setName("");

        //mock
        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.of(exhibitor));

        doNothing().when(multiLanguageLabelRepoService).deleteByExpoId(anyLong(),any());
        doReturn(null).when(eventChecklistService).findByEvent(any());
        doNothing().when(exhibitorSettingsService).markExhibitorRecordDeleted(exhibitorId);
        doNothing().when(exhibitorDocumentService).deleteExhibitorDocumentByexhibitorId(exhibitorId);
        doNothing().when(exhibitorProductRepoService).changeStatusToDelete(exhibitor);
        doNothing().when(eventTaskService).removeTaskParticipantsFromExhibitor(event,Collections.singletonList(exhibitorId));

        //Execution
        exhibitorsServiceImpl.setExhibitorStatusAsDeleted(exhibitorId, event);

        //Assertion
        verify(exhibitorRepoService).findById(anyLong());
        verify(multiLanguageLabelRepoService).deleteByExpoId(anyLong(),any());
        verify(exhibitorRepoService).save(any());
    }

    @Test
    void test_deleteExhibitor_throwExceptionExhibitorNotExist() {
        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.setExhibitorStatusAsDeleted(exhibitorId, event));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_updateExhibitor_success() {
        //Setup
        exhibitor.setEvent(event);

        //Mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.of(exhibitor));

        doNothing().when(exhibitorStaffCacheService).updateStaffAfterUpdateExhibitor(exhibitor.getId(),exhibitor.getEventId());

        when(exhibitorDocumentService.findByNameAndExhibitorId(anyString(), anyLong())).thenReturn(Optional.ofNullable(exhibitorDocument));
        when(sponsorsRepoService.getSponsorPresentByExhibitorIdForEvent(anyLong(), anyLong())).thenReturn(null);

        //Execution
        exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId);


        //Assertion
        verify(exhibitorRepoService).findById(anyLong());
        verify(sponsorsService).addSponsor(any(),any());

        ArgumentCaptor<Exhibitor> exhibitorArgumentCaptor = ArgumentCaptor.forClass(Exhibitor.class);
        verify(exhibitorRepoService).save(exhibitorArgumentCaptor.capture());

        Exhibitor exhibitor = exhibitorArgumentCaptor.getValue();
        assertEquals(exhibitor.getEvent(),event);
        assertEquals(exhibitor.isLeadRetrieversAllowed(),exhibitorDto.isLeadRetrieversAllowed());
        assertEquals(exhibitor.isSeeAllLeads(),exhibitorDto.isSeeAllLeads());
    }

    @Test
    void test_updateExhibitor_throwExceptionExhibitorNotExist() {

        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_updateExhibitor_trowExhibitorDoesNotBelongsToUser() {
        exhibitor.setEventId(3L);

        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_DOES_NOT_BELONGS_TO_USER.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_updateExhibitor_trowExhibitorAlreadyExists() {

        exhibitorDto.setExhibitorName("ExhibitorDTO");

        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(exhibitorRepoService.getExhibitorCountFromExhibitorName(anyString(), anyLong())).thenReturn(1L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_ALREADY_EXISTS.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_updateExhibitor_trowLiveStreamExhibitorReach() {

        exhibitorDto.setExhibitorName("Exhibitor");
        exhibitorDto.setAcceleventsStreamingEnabled(true);
        Exhibitor exhibitor1 = new Exhibitor();
        exhibitor1.setAcceleventsStreamingEnabled(true);
        Exhibitor exhibitor2 = new Exhibitor();
        exhibitor2.setAcceleventsStreamingEnabled(true);
        exhibitor.setAcceleventsStreamingEnabled(Boolean.FALSE);

        List<Exhibitor> exhibitorList = new ArrayList<>(Arrays.asList(exhibitor1,exhibitor2));

        VirtualEventSettings virtualEventSettings = new VirtualEventSettings();
        virtualEventSettings.setId(1L);

        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(roVirtualEventService.findVirtualEventSettingsByEventId(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(exhibitorList);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId));

        //Assertion
        assertEquals("You have purchased 1 number of exhibitors for live streaming and you have reached that number.", exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_updateExhibitor_trowExhibitorAlreadyUsedLiveStream() {

        exhibitorDto.setExhibitorName("Exhibitor");
        exhibitorDto.setAcceleventsStreamingEnabled(false);
        Exhibitor exhibitor1 = new Exhibitor();
        exhibitor1.setAcceleventsStreamingEnabled(true);
        Exhibitor exhibitor2 = new Exhibitor();
        exhibitor2.setAcceleventsStreamingEnabled(true);
        exhibitor.setAcceleventsStreamingEnabled(Boolean.TRUE);
        exhibitor.setMeetingObj("test");

        List<Exhibitor> exhibitorList = new ArrayList<>(Arrays.asList(exhibitor1,exhibitor2));

        //mock
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));



        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.updateExhibitorByEventHost(exhibitorDto,event, exhibitorId));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.EXHIBITOR_ALREADY_USED_LIVE_STREAM.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorRepoService).findById(anyLong());
    }

    @Test
    void test_uploadExhibitorCarouselImage_success(){

        //mock
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));

        //Execution
        exhibitorsServiceImpl.uploadExhibitorCarouselImage(1L,event,"profile_url");

        //Assertion
        verify(exhibitorRepoService).save(any());

    }

    @Test
    void test_uploadExhibitorCarouselImage_EVENT_NOT_FOUND(){

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> exhibitorsServiceImpl.uploadExhibitorCarouselImage(1L,null,"profile_url"));

        //Assertion
        assertEquals(NotFoundException.EventNotFound.EVENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_uploadExhibitorCarouselImage_EXHIBITOR_NOT_FOUND(){

        //mock
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> exhibitorsServiceImpl.uploadExhibitorCarouselImage(1L,event,"profile_url"));

        //Assertion
        assertEquals(NotFoundException.NotFound.EXHIBITOR_NOT_FOUND.getDeveloperMessage(), exception.getMessage());


    }

    @Test
    void test_updateExhibitorByExhibitorAdmin(){

        List<Long> ids = new ArrayList<>(Arrays.asList(1L,2L));
        exhibitorDto.setCategoryIdList(ids);
        exhibitorDto.setExhibitorName("ExhibitorDTO");

        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(exhibitorRepoService.getExhibitorCountFromExhibitorName(anyString(), anyLong())).thenReturn(0L);
        doNothing().when(getStreamService).updateChannelName(anyString(), anyString(), anyString(), anyBoolean(), anyLong());
        when(exhibitorCategoryService.findByCategoryIds(anyList(), anyLong())).thenReturn(Collections.singletonList(exhibitorCategory));

        when(exhibitorDocumentService.findByNameAndExhibitorId(anyString(), anyLong())).thenReturn(Optional.ofNullable(exhibitorDocument));


        exhibitorsServiceImpl.updateExhibitorByExhibitorAdmin(exhibitorDto,event,1L);

        //Assertion
        verify(exhibitorRepoService).save(any());
    }

    @Test
    void test_setExhibitorStatusAsDeleted_Success(){

        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(challengeConfigService.getAllEventChallengeDetails(any())).thenReturn(Collections.singletonList(eventChallengeDTO));
        when(staffRepository.findByEventAndExhibitorId(any(), anyLong())).thenReturn(Collections.singletonList(staff));
        doNothing().when(challengeConfigService).updateEventChallengeDetail(any(), any(), any());


        exhibitorsServiceImpl.setExhibitorStatusAsDeleted(1L,event);

        verify(multiLanguageLabelRepoService).deleteByExpoId(anyLong(),any());
        verify(exhibitorDocumentService).deleteExhibitorDocumentByexhibitorId(anyLong());

    }

    @Test
    void test_setExhibitorStatusAsDeleted_SWITCH_OFF_PRO_EXHIBITOR_SETTING_BEFORE_DELETING_EXHIBITOR(){

        exhibitor.setAcceleventsStreamingEnabled(true);
        exhibitor.setMeetingObj(null);

        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(challengeConfigService.getAllEventChallengeDetails(any())).thenReturn(Collections.singletonList(eventChallengeDTO));
        doNothing().when(challengeConfigService).updateEventChallengeDetail(any(), any(), any());


        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.setExhibitorStatusAsDeleted(1L,event));

        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.SWITCH_OFF_PRO_EXHIBITOR_SETTING_BEFORE_DELETING_EXHIBITOR.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_setExhibitorStatusAsDeleted_NOT_POSSIBLE_TO_DELETE_PRO_EXHIBITOR(){

        exhibitor.setAcceleventsStreamingEnabled(true);
        exhibitor.setMeetingObj("test");

        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));
        when(challengeConfigService.getAllEventChallengeDetails(any())).thenReturn(Collections.singletonList(eventChallengeDTO));
        doNothing().when(challengeConfigService).updateEventChallengeDetail(any(), any(), any());
        
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.setExhibitorStatusAsDeleted(1L,event));
        
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.NOT_POSSIBLE_TO_DELETE_PRO_EXHIBITOR.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getExhibitorById(){

        exhibitor.setCategoryId("1,2,3");
        exhibitorDocument.setName("{\"key\":\"Milan\",\"value\":\"Jay\"}");

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(staffRepository.findStaffByExhibitorIdsAndExhibitorRoles(anyList(), any())).thenReturn(Collections.singletonList(staff));
        when(exhibitorCategoryService.findCategoryNameByCategoryIds(anyList())).thenReturn(new ArrayList(Arrays.asList("Milan","Jay")));
        when(exhibitorDocumentService.findExhibitorDocumentByExhibitorId(anyLong())).thenReturn(Collections.singletonList(exhibitorDocument));
        when(leadRetriverDataRepoService.isRequestedDemoLeadExistForUser(anyLong(), anyLong())).thenReturn(true);

        ExhibitorDto exhibitorById = exhibitorsServiceImpl.getExhibitorById(1L, event, user, true);

        assertEquals(exhibitor.getId(),exhibitorById.getId());
        assertEquals(exhibitor.getName(),exhibitorById.getExhibitorName());
    }

    @Test
    void test_getAllExhibitorDataForDisplaySide(){

        exhibitor.setCategoryId("1,2,3");
        exhibitorDocument.setName("{\"key\":\"Milan\",\"value\":\"Jay\"}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);

        Pageable pageable = PageRequest.of(0, 10);


        when(exhibitorCategoryService.findCategoryNameByEventId(anyLong())).thenReturn(new ArrayList<>(Arrays.asList("Milan","Jay")));
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(virtualEventService.getTheNumberOfExhibitorsPurchased(any())).thenReturn(10L);
        when(staffRepository.findStaffByExhibitorIdsAndExhibitorRoles(anyList(), any())).thenReturn(Collections.singletonList(staff));
        when(challengeConfigService.getAllEventChallengeDetails(any())).thenReturn(Collections.singletonList(eventChallengeDTO));
        when(exhibitorCategoryService.findByCategoryIds(anyList(), anyLong())).thenReturn(Collections.singletonList(exhibitorCategory));
        when(exhibitorDocumentService.findByExhibitorIds(anyList())).thenReturn(Collections.singletonList(exhibitorDocument));
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDescWithoutCache(anyLong(), any())).thenReturn(PageUtil.getPageable(Collections.singletonList(exhibitor), pageable));
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDescSearchStrWithoutCache(anyLong(), any(), anyString())).thenReturn(PageUtil.getPageable(Collections.singletonList(exhibitor), pageable));


        DataTableResponseForExhibitor allExhibitorDataForDisplaySide = exhibitorsServiceImpl.getAllExhibitorDataForDisplaySide(event, 10, 0, "", true);
        DataTableResponseForExhibitor allExhibitorDataForDisplaySide1 = exhibitorsServiceImpl.getAllExhibitorDataForDisplaySide(event, 10, 0, "", false);
        DataTableResponseForExhibitor allExhibitorDataForDisplaySide2 = exhibitorsServiceImpl.getAllExhibitorDataForDisplaySide(event, 10, 0, "test", false);

        List<ExhibitorDetailsDto> data = (List<ExhibitorDetailsDto>) allExhibitorDataForDisplaySide.getData();
        data.forEach(e->{
            assertEquals(exhibitor.getId(),e.getId());
            assertEquals(exhibitor.getEventId().longValue(),e.getEventId());
            assertEquals(exhibitor.getName(),e.getExhibitorName());
        });

        List<ExhibitorDetailsDto> data1 = (List<ExhibitorDetailsDto>) allExhibitorDataForDisplaySide1.getData();
        data1.forEach(e->{
            assertEquals(exhibitor.getId(),e.getId());
            assertEquals(exhibitor.getEventId().longValue(),e.getEventId());
            assertEquals(exhibitor.getName(),e.getExhibitorName());
        });

        List<ExhibitorDetailsDto> data2 = (List<ExhibitorDetailsDto>) allExhibitorDataForDisplaySide2.getData();
        data2.forEach(e->{
            assertEquals(exhibitor.getId(),e.getId());
            assertEquals(exhibitor.getEventId().longValue(),e.getEventId());
            assertEquals(exhibitor.getName(),e.getExhibitorName());
        });
    }

    @Test
    void test_getExpoSettingsDetailsMap(){

        List<ExhibitorSetting> exhibitorSettingList = new ArrayList<>();
        exhibitorSettingList.add(exhibitorSetting);

        when(exhibitorSettingsService.getExhibitorSettingsByEventAndExhibitorIdIn(anyList(), any())).thenReturn(exhibitorSettingList);
        doReturn(exhibitorSettingList).when(exhibitorSettingsService).prepareAllExhibitorSetting(anyList(), any());
        when(holderReqAttributeService.getTicketHolderRequiredAttributesByExhibitor(any(),anyList())).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        when(ticketingHelperService.findTicketingByEventIdOrThrowError(any(Event.class))).thenReturn(ticketing);

        Map<Long, List<ExpoSettingsDetails>> expoSettingsDetailsMap = exhibitorsServiceImpl.getExpoSettingsDetailsMap(new ArrayList<>(Arrays.asList(1L, 2L)), event);
        assertEquals(1,expoSettingsDetailsMap.size());
    }

    @Test
    void test_getMyExhibitorsDetails(){
        List<ExhibitorSetting> exhibitorSettingList = new ArrayList<>();
        exhibitorSettingList.add(exhibitorSetting);





        List<ExhibitorBasicDto> myExhibitorsDetails = exhibitorsServiceImpl.getMyExhibitorsDetails(event);

        myExhibitorsDetails.forEach(e->{
            assertEquals(exhibitor.getName(),e.getName());
            assertEquals(exhibitor.getId(),e.getId().longValue());
        });
    }

    @Test
    void test_getMyExhibitorsDetailsUsingStaff(){

        exhibitorSetting.setAllowForTicketHolderData(true);

        List<ExhibitorSetting> exhibitorSettingList = new ArrayList<>();
        exhibitorSettingList.add(exhibitorSetting);
        staff.setExhibitorId(exhibitorId);

        when(exhibitorSettingsService.getExhibitorSettingsByEventAndExhibitorIdIn(anyList(), any())).thenReturn(exhibitorSettingList);
        when(ticketingHelperService.findTicketingByEventIdOrThrowError(any(Event.class))).thenReturn(ticketing);
        when(holderReqAttributeService.getTicketHolderRequiredAttributesByExhibitor(any(),anyList())).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable(exhibitor));

        List<ExhibitorBasicDto> myExhibitorsDetailsUsingStaff = exhibitorsServiceImpl.getMyExhibitorsDetailsUsingStaff(Collections.singletonList(staff), event);
        Staff staff1=staff;
        staff1.setExhibitor(exhibitor);
        staff1.setExhibitorId(exhibitorId);
        List<ExhibitorBasicDto> myExhibitorsDetailsUsingStaffstaff1 = exhibitorsServiceImpl.getMyExhibitorsDetailsUsingStaff(Collections.singletonList(staff1), event);

        myExhibitorsDetailsUsingStaff.forEach(e->{
            assertEquals(exhibitor.getName(),e.getName());
            assertEquals(exhibitor.getId(),e.getId().longValue());
        });

        myExhibitorsDetailsUsingStaffstaff1.forEach(e->{
            assertEquals(exhibitor.getName(),e.getName());
            assertEquals(exhibitor.getId(),e.getId().longValue());
        });
    }

    @Test
    void test_getAllExhibitorData(){

        leadRetriverData.setLeadData("{\"eventTicketId\":1,\"holderFirstName\":\"Milan\",\"holderLastName\":\"Dobariya\",\"holderEmail\":\"<EMAIL>\",\"holderCountryCode\":null,\"holderPhoneNumber\":0,\"title\":null,\"company\":null}");

        Pageable pageable = PageRequest.of(0, 10);
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDescSearchStrWithoutCache(anyLong(), any(), anyString())).thenReturn(PageUtil.getPageable(Collections.singletonList(exhibitor), pageable));
        when(staffRepository.findAllStaffAndLeadCountByExhibitorIdsAndExhibitorRoles(anyList(), any())).thenReturn(Collections.singletonList(staffAndLeadCountDto));
        when(leadRetriverDataRepoService.getAllByExhibitorsId(anyList())).thenReturn(Collections.singletonList(leadRetriverData));

        DataTableResponse test = exhibitorsServiceImpl.getAllExhibitorData(event, 10, 0, "test");

        assertEquals(1,test.getRecordsTotal());
    }

    @Test
    void test_getExhibitorsAnalytics() throws GeneralSecurityException, IOException {

        HashMap<Long,Long> newMap = new HashMap<>();
        newMap.put(1L,1L);

        exhibitor.setId(1L);

        leadRetriverData.setLeadData("{\"eventTicketId\":1,\"holderFirstName\":\"Milan\",\"holderLastName\":\"Dobariya\",\"holderEmail\":\"<EMAIL>\",\"holderCountryCode\":null,\"holderPhoneNumber\":0,\"title\":null,\"company\":null}");

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(leadRetriverDataRepoService.getAllByExhibitorsId(anyList())).thenReturn(Collections.singletonList(leadRetriverData));
        when(leadRetriverDataRepoService.findRequestADemoCountForEvent(anyList())).thenReturn(10L);
        when(exhibitorCategoryService.findCategoryNameByEventId(anyLong())).thenReturn(new ArrayList<>(Arrays.asList("Milan","Jay")));
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(virtualEventLoggingRepository.findUniqueVisitersWithDocuments(anyList())).thenReturn(Collections.singletonList(exhibitorBasicAnalytics));
        when(exhibitorElasticsearchService.getExhibitorVisitsByEvent(anyLong())).thenReturn(newMap);

        ExhibitorAnalyticsDto test = exhibitorsServiceImpl.getExhibitorsAnalytics(event, 10, 0, "");

        assertEquals(1L,test.getRecordsTotal());
    }

    @Test
    void test_visitorList(){

        when(virtualEventLoggingRepository.findByExpoIdAndDocumentNameIsNull(anyLong())).thenReturn(Collections.singletonList(exhibitorsActivity));

        List<ExhibitorsActivity> exhibitorsActivitiesResult = exhibitorsServiceImpl.visitorList(exhibitorId, event);

        exhibitorsActivitiesResult.forEach(e-> assertEquals(exhibitorsActivity,e));
    }

    @Test
    void test_documentDownloadedList(){

        when(virtualEventLoggingRepository.findByExpoIdAndDocumentNameNotNull(anyLong())).thenReturn(Collections.singletonList(exhibitorsActivity));

        List<ExhibitorsActivity> exhibitorsActivitiesResult = exhibitorsServiceImpl.documentDownloadedList(exhibitorId, event);

        exhibitorsActivitiesResult.forEach(e->{
            assertEquals(exhibitorsActivity,e);
        });
    }

    @Test
    void test_loggingData(){

        when(virtualEventLoggingRepository.findByExpoId(anyLong())).thenReturn(Collections.singletonList(exhibitorsActivity));

        List<ExhibitorsActivity> exhibitorsActivitiesResult = exhibitorsServiceImpl.loggingData(exhibitorId, event);

        exhibitorsActivitiesResult.forEach(e->{
            assertEquals(exhibitorsActivity,e);
        });
    }

    @Test
    void test_uploadExhibitorLogoImage(){

        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.ofNullable(exhibitor));

        exhibitorsServiceImpl.uploadExhibitorLogoImage(exhibitorId, "test_img_url");

        ArgumentCaptor<Exhibitor> getExhibitorArgumentCaptor = ArgumentCaptor.forClass(Exhibitor.class);
        verify(exhibitorRepoService, times(1)).save(getExhibitorArgumentCaptor.capture());

       Exhibitor resultExhibitor = getExhibitorArgumentCaptor.getValue();
       assertEquals("test_img_url",resultExhibitor.getLogo());
    }

    @Test
    void test_getCompanyRepresentatives(){

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(staffService.findListOfStaffUsersByExhibitorIdAndRole(anyLong(), any())).thenReturn(Collections.singletonList(staff));


        List<CompanyRepresentativesDto> companyRepresentatives = exhibitorsServiceImpl.getCompanyRepresentatives(exhibitorId, event);
        companyRepresentatives.forEach(e->{
            assertEquals(user.getUserId(),e.getUserId());
            assertEquals(user.getFirstName()+" "+user.getLastName(),e.getName());
        });
        assertEquals(1L,companyRepresentatives.size());
    }

    @Test
    void test_uploadExpoCardImage(){

        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.ofNullable(exhibitor));

        exhibitorsServiceImpl.uploadExpoCardImage(exhibitorId,event,"test_img_url");

        ArgumentCaptor<Exhibitor> getExhibitorArgumentCaptor = ArgumentCaptor.forClass(Exhibitor.class);
        verify(exhibitorRepoService, times(1)).save(getExhibitorArgumentCaptor.capture());

        Exhibitor resultExhibitor = getExhibitorArgumentCaptor.getValue();
        assertEquals("test_img_url",resultExhibitor.getExpoCardImage());
    }

    @Test
    void test_uploadExpoBannerImage(){

        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.ofNullable(exhibitor));

        exhibitorsServiceImpl.uploadExpoBannerImage(exhibitorId,"test_img_url");

        ArgumentCaptor<Exhibitor> getExhibitorArgumentCaptor = ArgumentCaptor.forClass(Exhibitor.class);
        verify(exhibitorRepoService, times(1)).save(getExhibitorArgumentCaptor.capture());

        Exhibitor resultExhibitor = getExhibitorArgumentCaptor.getValue();
        assertEquals("test_img_url",resultExhibitor.getExpoBannerImage());
    }

    @Test
    void test_updateExhibitorSequence(){

        exhibitor.setPosition(1D);

        Exhibitor exhibitor1 = new Exhibitor();
        exhibitor1.setId(2L);
        exhibitor1.setEvent(event);
        exhibitor1.setEventId(event.getEventId());
        exhibitor1.setCurrency(event.getCurrency());
        exhibitor1.setLeadRetrieversAllowed(true);
        exhibitor1.setMaxLeadRetrivers(10);
        exhibitor1.setName("Exhibitor");
        exhibitor1.setSeeAllLeads(true);
        exhibitor1.setPosition(2D);

        Exhibitor exhibitor2 = new Exhibitor();
        exhibitor2.setId(3L);
        exhibitor2.setEvent(event);
        exhibitor2.setEventId(event.getEventId());
        exhibitor2.setCurrency(event.getCurrency());
        exhibitor2.setLeadRetrieversAllowed(true);
        exhibitor2.setMaxLeadRetrivers(10);
        exhibitor2.setName("Exhibitor");
        exhibitor2.setSeeAllLeads(true);
        exhibitor2.setPosition(0D);

        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.ofNullable(exhibitor));
        when(exhibitorRepoService.findById(2L)).thenReturn(Optional.ofNullable(exhibitor1));
        when(exhibitorRepoService.findById(3L)).thenReturn(Optional.ofNullable(null));
        doNothing().when(exhibitorRepoService).updatePositionExhibitor(anyLong(), anyDouble(), anyDouble(), anyDouble());
        when(exhibitorRepoService.getFirstByIdAndEventIdAndPositionGreaterThanOrderByPosition(1L,1L,1D)).thenReturn(exhibitor1);
        when(exhibitorRepoService.getFirstByIdAndEventIdAndPositionLessThanOrderByPositionDesc(1L,1L,1D)).thenReturn(exhibitor2);
        doNothing().when(exhibitorRepoService).updatePositionForAllExhibitorByEventId(anyDouble(), anyLong());

        exhibitorsServiceImpl.updateExhibitorSequence(exhibitorId,1L,1L,event,user);
        exhibitorsServiceImpl.updateExhibitorSequence(exhibitorId,2L,1L,event,user);
        exhibitorsServiceImpl.updateExhibitorSequence(exhibitorId,2L,3L,event,user);
        exhibitorsServiceImpl.updateExhibitorSequence(exhibitorId,3L,2L,event,user);

        verify(exhibitorRepoService,times(4)).save(any());
    }

    @Test
    void test_getExhibitorDataForSession(){

        session.setSponsorExhibitorJson("{\"sponsors\":[1],\"exhibitors\":[1]}");

        doReturn(session).when(sessionService).getSessionById(anyLong(), any());
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));

        DataTableResponse test = exhibitorsServiceImpl.getExhibitorDataForSession(event, 10, 0, "test", 1L);
        assertEquals(0L,test.getRecordsTotal());
    }

    @Test
    void test_addExhibitorToSession_with_all_value(){
        session.setSponsorExhibitorJson("{\"sponsors\":[1],\"exhibitors\":[1]}");

        when(sessionService.getSessionByIdWithoutCache(anyLong(), any())).thenReturn(session);

        exhibitorsServiceImpl.addExhibitorToSession(1L,1L,event);

        ArgumentCaptor<Session> sessionArgumentCaptor = ArgumentCaptor.forClass(Session.class);
        verify(sessionService).save(sessionArgumentCaptor.capture());

        Session resultSession = sessionArgumentCaptor.getValue();

        assertEquals(session.getId(),resultSession.getId());
        assertEquals(session.getEventId(),resultSession.getEventId());
        assertEquals(session.getEvent(),resultSession.getEvent());
        assertEquals(session.getSponsorExhibitorJson(),resultSession.getSponsorExhibitorJson());

    }

    @Test
    void test_addExhibitorToSession_without_value(){

        Session session1 = new Session();
        session1.setSponsorExhibitorJson("{\"sponsors\":[],\"exhibitors\":[1]}");
        Session session2 = new Session();
        session2.setSponsorExhibitorJson("{\"sponsors\":[1],\"exhibitors\":[]}");

        when(sessionService.getSessionByIdWithoutCache(1L, event)).thenReturn(session);
        when(sessionService.getSessionByIdWithoutCache(2L, event)).thenReturn(session1);
        when(sessionService.getSessionByIdWithoutCache(3L, event)).thenReturn(session2);

        exhibitorsServiceImpl.addExhibitorToSession(1L,1L,event);
        exhibitorsServiceImpl.addExhibitorToSession(2L,1L,event);
        exhibitorsServiceImpl.addExhibitorToSession(3L,1L,event);

        verify(sessionService,times(3)).save(any());

    }

    @Test
    void test_removeExhibitorFromSession(){
        session.setSponsorExhibitorJson("{\"sponsors\":[1],\"exhibitors\":[1]}");
        when(sessionService.getSessionByIdWithoutCache(1L, event)).thenReturn(session);
        exhibitorsServiceImpl.removeExhibitorFromSession(1L,1L,event);
        assertTrue(true);
    }

    @Test
    void test_generateAutoLead_LEAD_ALREADY_GENERATED(){

        when(leadRetriverDataRepoService.isRequestedDemoLeadExistForUser(anyLong(), anyLong())).thenReturn(true);

        String s = exhibitorsServiceImpl.generateAutoLead(1L, event, user, true, 1L);

        assertEquals(LEAD_ALREADY_GENERATED,s);
    }

    @Test
    void test_generateAutoLead_EVENT_TICKETS_NOT_FOUND(){

        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.emptyList(), pageable);

        when(leadRetriverDataRepoService.isRequestedDemoLeadExistForUser(anyLong(), anyLong())).thenReturn(false);
        when(staffService.findByEventAndUserAndRole(anyLong(), anyLong(), any())).thenReturn(Collections.singletonList(staff));
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);

        String s = exhibitorsServiceImpl.generateAutoLead(1L, event, user, true, 1L);

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND.getErrorMessage(),s);
    }

    @Test
    void test_generateAutoLead_success(){

        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.singletonList(eventTickets), pageable);
        exhibitor.setLeadRetrieversAllowed(true);
        exhibitor.setAutoGenerateLead(true);

        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setJsonValue("{\"attribute\":\"value\"}");
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(new User());
        eventTickets.setTicketPurchaserId(new User());
        when(ticketingHelperService.isCollectTicketHolderAttributesByEvent(event)).thenReturn(true);
        when(attendeeProfileService.getAttendeeProfileInfo(any(User.class), any(Event.class))).thenReturn(new AttendeeProfileDto());
        when(leadRetriverDataRepoService.isRequestedDemoLeadExistForUser(anyLong(), anyLong())).thenReturn(false);
        when(staffService.findByEventAndUserAndRole(anyLong(), anyLong(), any())).thenReturn(Collections.singletonList(staff));
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);
        doNothing().when(leadService).checkUserIsStaffForExhibitorAndThrowError(anyLong(), any(), any());
        doReturn(Optional.ofNullable(exhibitor)).when(exhibitorRepoService).findByIdAndEventId(anyLong(),anyLong());
        when(userService.findUserIdByEmail(anyString())).thenReturn(1L);
        doNothing().when(leadRetriverDataRepoService).save(any());

        String s = exhibitorsServiceImpl.generateAutoLead(1L, event, user, true, 1L);
        assertEquals(SUCCESS,s);
    }

    @Test
    void test_generateAutoLead(){

        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.singletonList(eventTickets), pageable);
        exhibitor.setLeadRetrieversAllowed(false);
        exhibitor.setAutoGenerateLead(false);


        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setJsonValue("{\"attribute\":\"value\"}");
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(new User());
        eventTickets.setTicketPurchaserId(new User());
        when(ticketingHelperService.isCollectTicketHolderAttributesByEvent(event)).thenReturn(true);
        when(attendeeProfileService.getAttendeeProfileInfo(any(User.class), any(Event.class))).thenReturn(new AttendeeProfileDto());

        when(staffService.findByEventAndUserAndRole(anyLong(), anyLong(), any())).thenReturn(Collections.singletonList(staff));
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);
        doNothing().when(leadService).checkUserIsStaffForExhibitorAndThrowError(anyLong(), any(), any());
        doReturn(Optional.ofNullable(null)).when(exhibitorRepoService).findByIdAndEventId(1L,1L);
        doReturn(Optional.ofNullable(exhibitor)).when(exhibitorRepoService).findByIdAndEventId(1L,2L);


        when(leadRetriverDataRepoService.isEventTicketExistByStaffIdANDExhibitorIdAndEventTicketId(anyLong(), anyLong(), anyLong())).thenReturn(true);

        String s = exhibitorsServiceImpl.generateAutoLead(1L, event, user, false, 1L);
        assertEquals(NotFoundException.NotFound.EXHIBITOR_NOT_FOUND.getErrorMessage(),s);

        event.setEventId(2L);
        String s1 = exhibitorsServiceImpl.generateAutoLead(1L, event, user, false, 1L);
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.NOT_ALLOW_TO_AUTO_GENERATE_LEAD.getErrorMessage(),s1);

        event.setEventId(2L);
        exhibitor.setAutoGenerateLead(true);
        String s2 = exhibitorsServiceImpl.generateAutoLead(1L, event, user, false, 1L);
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.CAN_NOT_GENERATE_LEAD.getErrorMessage(),s2);event.setEventId(2L);

        exhibitor.setLeadRetrieversAllowed(true);
        exhibitor.setAutoGenerateLead(true);
        staff.setId(5L);
        String s3 = exhibitorsServiceImpl.generateAutoLead(1L, event, user, false, 5L);
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.ALREADY_USED_TICKET.getErrorMessage(),s3);
    }

    @Test
    void test_findByCategoryId(){

        exhibitor.setCategoryId("1");

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));

        Long byCategoryId = exhibitorsServiceImpl.findByCategoryId(1L, event);

        assertEquals(1L,byCategoryId.longValue());
    }

    @Test
    void test_parseExhibitorCSV() throws IOException {

        //setup
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.STARTER.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(planConfig);

        InputStream pathWith5Element1 = this.getClass().getClassLoader().getResourceAsStream("csvFile/ExhibitorCsvFile .csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/ExhibitorCsvFile .csv", "multipart/form-data", pathWith5Element1);

        when(exhibitorRepoService.save(any())).thenReturn(exhibitor);
        when(multiLanguageMasterService.getMultiLanguageMasterByType(EnumMultiLanguageLabelType.EXPO)).thenReturn(Collections.singletonList(multiLanguageMaster));
        doNothing().when(multiLanguageLabelRepoService).saveAll(anyList());


        final UploadExhibitorsResponseContainer uploadExhibitorsResponseContainer = exhibitorsServiceImpl.parseExhibitorCSV(file, event, user);
        assertNotNull(uploadExhibitorsResponseContainer);
    }

    @Test
    void test_getAllExhibitorByCategoryId(){

        exhibitor.setCategoryId("1");
        exhibitor.setName("EXPO");
        exhibitor.setCategoryPositionJson("{\"1\":[{\"key\":1,\"value\":2000.0}]}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);exhibitor.setCategoryId("1");
        exhibitor.setName("EXPO");
        exhibitor.setCategoryPositionJson("{\"1\":[{\"key\":1,\"value\":2000.0}]}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);

        when(exhibitorCategoryService.findCategoryNameById(anyLong())).thenReturn("EXPO");
        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(staffRepository.findStaffByExhibitorIdsAndExhibitorRoles(anyList(), any())).thenReturn(Collections.singletonList(staff));

        DataTableResponse test = exhibitorsServiceImpl.getAllExhibitorByCategoryId(event, 10, 0, "EXPO", 1L);

        assertEquals(1L,test.getRecordsTotal());
    }

    @Test
    void test_findAllExhibitorIdByEvent(){

        List<Long> allExhibitorIdByEvent = exhibitorsServiceImpl.findAllExhibitorIdByEvent(event);

        assertEquals(Collections.emptyList(),allExhibitorIdByEvent);
    }

    @Test
    void test_logClickDocument(){

        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.singletonList(eventTickets), pageable);
        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setJsonValue("{\"attribute\":\"value\"}");
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(new User());
        eventTickets.setTicketPurchaserId(new User());
        when(ticketingHelperService.isCollectTicketHolderAttributesByEvent(event)).thenReturn(true);
        when(attendeeProfileService.getAttendeeProfileInfo(any(User.class), any(Event.class))).thenReturn(new AttendeeProfileDto());

        doReturn(virtualEventLogging).when(virtualEventLoggingRepository).save(any());
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);

        exhibitorsServiceImpl.logClickDocument(1L,logClickDocumentDto,user,event, null);

        verify(leadService).addLeadDetailData(any(), any(), anyLong(), any(), any());
    }

    @Test
    void test_logDocumentDownload(){

        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.singletonList(eventTickets), pageable);
        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setJsonValue("{\"attribute\":\"value\"}");
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(new User());
        eventTickets.setTicketPurchaserId(new User());
        when(ticketingHelperService.isCollectTicketHolderAttributesByEvent(event)).thenReturn(true);
        when(attendeeProfileService.getAttendeeProfileInfo(any(User.class), any(Event.class))).thenReturn(new AttendeeProfileDto());
        doReturn(virtualEventLogging).when(virtualEventLoggingRepository).save(any());
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);

        exhibitorsServiceImpl.logDocumentDownload(1L,"DOC_NAME",user,event, null);

        verify(leadService).addLeadDetailData(any(), any(), anyLong(), any(), any());
    }

    @Test
    void test_logExpoVisit(){

        exhibitor.setCountBoothVisitorAsLead(true);
        Pageable pageable = PageRequest.of(0, 10);
        Page<EventTickets> eventTicketsPage = PageUtil.getPageable(Collections.singletonList(eventTickets), pageable);
        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setJsonValue("{\"attribute\":\"value\"}");
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(new User());
        eventTickets.setTicketPurchaserId(new User());
        when(ticketingHelperService.isCollectTicketHolderAttributesByEvent(event)).thenReturn(true);
        when(attendeeProfileService.getAttendeeProfileInfo(any(User.class), any(Event.class))).thenReturn(new AttendeeProfileDto());

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(virtualEventLoggingRepository.save(any())).thenReturn(virtualEventLogging);
        doNothing().when(leadService).checkUserIsStaffForExhibitorAndThrowError(anyLong(), any(), any());
        when(leadRetriverDataRepoService.needToCreateVisitLeadForPassedExhibitorId(anyLong(), anyLong(), anyString())).thenReturn(true);
        when(eventTicketsService.findEventTicketsByHolderUserIdAndEventId(any(), any())).thenReturn(eventTicketsPage);
        when(leadService.getLocation(anyLong(), any())).thenReturn("KOL");
        when(roStaffService.getAllUserRolesWithVirtualAndHybridTickets(anyLong(), anyLong())).thenReturn(Collections.singletonList("admin"));

        exhibitorsServiceImpl.logExpoVisit(1L,user,event, testAppsyncAuthToken, null);

        verify(leadService).saveLeadRetriever(any(), any(), any(), anyLong(), isNull());
    }

    @Test
    void test_logExpoLeave(){

        when(roStaffService.getAllUserRolesWithVirtualAndHybridTickets(anyLong(), anyLong())).thenReturn(Collections.singletonList("admin"));

        exhibitorsServiceImpl.logExpoLeave(exhibitorId,user,event, testAppsyncAuthToken);

        verify(roStaffService).getAllUserRolesWithVirtualAndHybridTickets(anyLong(), anyLong());
    }

    @Test
    void test_save(){
        when(exhibitorRepoService.save(any())).thenReturn(exhibitor);
        Exhibitor save = exhibitorsServiceImpl.save(exhibitor);

        assertEquals(exhibitor,save);
    }

    @Test
    void test_findAllByEvent(){

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(any())).thenReturn(Collections.singletonList(exhibitor));

        List<Exhibitor> allByEvent = exhibitorsServiceImpl.findAllByEvent(event);

        assertEquals(Collections.singletonList(exhibitor),allByEvent);
    }

    @Test
    void test_saveAll(){

        doNothing().when(exhibitorRepoService).saveAll(anyList());
        exhibitorsServiceImpl.saveAll(Collections.singletonList(exhibitor));

        assertTrue(true);
    }

    @Test
    void test_updateExhibitorById(){
        doNothing().when(exhibitorRepoService).updateExhibitorById(anyLong(),anyString());
        exhibitorsServiceImpl.updateExhibitorById(1L,"test");
        assertTrue(true);
    }

    @Test
    void test_updateExhibitorCategoryPosition(){

        exhibitor.setPosition(1D);

        Exhibitor exhibitor1 = new Exhibitor();
        exhibitor1.setId(2L);
        exhibitor1.setEvent(event);
        exhibitor1.setEventId(event.getEventId());
        exhibitor1.setCurrency(event.getCurrency());
        exhibitor1.setLeadRetrieversAllowed(true);
        exhibitor1.setMaxLeadRetrivers(10);
        exhibitor1.setName("Exhibitor");
        exhibitor1.setSeeAllLeads(true);
        exhibitor1.setPosition(2D);

        Exhibitor exhibitor2 = new Exhibitor();
        exhibitor2.setId(3L);
        exhibitor2.setEvent(event);
        exhibitor2.setEventId(event.getEventId());
        exhibitor2.setCurrency(event.getCurrency());
        exhibitor2.setLeadRetrieversAllowed(true);
        exhibitor2.setMaxLeadRetrivers(10);
        exhibitor2.setName("Exhibitor");
        exhibitor2.setSeeAllLeads(true);
        exhibitor2.setPosition(0D);

        when(exhibitorRepoService.findById(exhibitorId)).thenReturn(Optional.ofNullable(exhibitor));
        when(exhibitorRepoService.findById(2L)).thenReturn(Optional.ofNullable(exhibitor1));
        when(exhibitorRepoService.findById(3L)).thenReturn(Optional.ofNullable(null));




        when(exhibitorRepoService.getAllExhibitorsByCategoryId(anyLong(), anyLong())).thenReturn(Collections.singletonList(exhibitor));

        exhibitorsServiceImpl.updateExhibitorCategoryPosition(1L,exhibitorId,1L,1L,event,user);
        exhibitorsServiceImpl.updateExhibitorCategoryPosition(1L,exhibitorId,2L,1L,event,user);
        exhibitorsServiceImpl.updateExhibitorCategoryPosition(1L,exhibitorId,2L,3L,event,user);
        exhibitorsServiceImpl.updateExhibitorCategoryPosition(1L,exhibitorId,3L,2L,event,user);

        exhibitorsServiceImpl.updateExhibitorCategoryPosition(1L,1L,1L,1L,event,user);

        assertTrue(true);

    }

    @Test
    void test_getAllExhibitorSummary(){

        exhibitor.setCategoryId("1");
        exhibitor.setName("EXPO");
        exhibitor.setCategoryPositionJson("{\"1\":[{\"key\":1,\"value\":2000.0}]}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);

        exhibitorDashboardDTO.setExhibitorName("test");
        exhibitorDashboardDTO.setCategoryId("1");
        exhibitorDashboardDTO.setCategoryNames(Collections.singletonList("CAT_NAME"));


        when(exhibitorRepoService.findAllExhibitorsByEvent(event.getEventId())).thenReturn(Collections.singletonList(exhibitorDashboardDTO));
        when(staffRepository.findStaffByExhibitorIdsAndExhibitorRoles(anyList(), any())).thenReturn(Collections.singletonList(staff));

        DataTableResponseForExhibitor test = exhibitorsServiceImpl.getAllExhibitorSummary(event, 10, 0, "test");

        assertEquals(0L,test.getNumberOfExhibitorsRemainingToEnable());
    }

    @Test
    void test_getAllExhibitorsSummaryByCategoryId(){

        CategoryPositionDto categoryPositionDto = new CategoryPositionDto();
        categoryPositionDto.setKey(1L);
        categoryPositionDto.setValue(2000D);

        Map<Long, List<CategoryPositionDto>> test = new HashMap<>();
        test.put(1L,Collections.singletonList(categoryPositionDto));

        exhibitorDashboardDTO.setExhibitorName("test");
        exhibitorDashboardDTO.setCategoryId("1");
        exhibitorDashboardDTO.setCategoryNames(Collections.singletonList("CAT_NAME"));
        exhibitorDashboardDTO.setCategoryPositionJson(test);

        when(exhibitorRepoService.findAllExhibitorsByEvent(anyLong())).thenReturn(Collections.singletonList(exhibitorDashboardDTO));
        when(exhibitorCategoryService.findCategoryNameById(anyLong())).thenReturn("test");

        DataTableResponse allExhibitorsSummaryByCategoryId = exhibitorsServiceImpl.getAllExhibitorsSummaryByCategoryId(event, 10, 0, "test", 1L);

        assertNotNull(allExhibitorsSummaryByCategoryId);
    }

    @Test
    void test_getAttendeeExhibitorsAnalytics() throws IOException {

        when(virtualEventLoggingService.getUserTotalExhibitorVisit(anyLong(), anyLong())).thenReturn(1L);
        when(virtualEventLoggingService.getUserTotalDocumentDownload(anyLong(), anyLong())).thenReturn(1L);
        when(leaderboardService.getAttendeeExhibitorEngageThroughChat(event.getEventId(), user.getUserId())).thenReturn(1L);
        when(exhibitorRepoService.countByEventId(anyLong())).thenReturn(1L);
        when(exhibitorElasticsearchService.getExhibitorsVisitsByUserAndEvent(anyLong(),anyLong())).thenReturn(1L);
        when(leaderboardService.getProductViewsByUser(anyLong(), anyLong())).thenReturn(1L);
        when(leadRetriverDataRepoService.getAverageLeadRatingByUser(anyLong())).thenReturn(1D);

        ExhibitorAttendeeAnalytics attendeeExhibitorsAnalytics = exhibitorsServiceImpl.getAttendeeExhibitorsAnalytics(event, user);

        assertEquals(0L,attendeeExhibitorsAnalytics.getMeetingBooked().longValue());
        assertEquals(1L,attendeeExhibitorsAnalytics.getAverageViewPerExhibitor().longValue());
    }

    @Test
    void test_getEventIdByExhibitorId(){
        when(exhibitorRepoService.getEventIdByExhibitorId(anyLong())).thenReturn(Optional.of(1L));
        Long eventIdByExhibitorId = exhibitorsServiceImpl.getEventIdByExhibitorId(1L);

        assertEquals(1L,eventIdByExhibitorId.longValue());
    }

    @Test
    void test_getExhibitorAnalytics() throws IOException {

        when(exhibitorRepoService.findById(anyLong())).thenReturn(Optional.ofNullable((exhibitor)));
        when(meetingSchedule.getTotalMeetingBookedInExhibitor(anyLong(), anyLong())).thenReturn(1L);
        when(leaderboardService.getProductViewsByExhibitor(anyLong(), anyLong())).thenReturn(1L);
        when(leadRetriverDataRepoService.getAverageLeadRatingByExhibitor(anyLong())).thenReturn(2D);

        ExhibitorBasicAnalytics exhibitorAnalytics = exhibitorsServiceImpl.getExhibitorAnalytics(event, 1L);

        assertEquals(1L,exhibitorAnalytics.getMeetingBooked().longValue());
    }

    @Test
    void test_isExhibitorExisting(){
        when(exhibitorRepoService.isExhibitorExisting(anyString())).thenReturn(true);
        Boolean exhibitorExisting = exhibitorsServiceImpl.isExhibitorExisting("1");

        assertTrue(exhibitorExisting);
    }

    @Test
    void test_findExhibitorCarouselDetailByIds(){

        ExhibitorCarouselDetailDto exhibitorCarouselDetailDto = new ExhibitorCarouselDetailDto();
        exhibitorCarouselDetailDto.setCarouselImage("IMAGE");

        when(exhibitorRepoService.findExhibitorCarouselDetailByIds(anyList())).thenReturn(Collections.singletonList(exhibitorCarouselDetailDto));

        List<ExhibitorCarouselDetailDto> exhibitorCarouselDetailByIds = exhibitorsServiceImpl.findExhibitorCarouselDetailByIds(Collections.singletonList(1L));

        exhibitorCarouselDetailByIds.forEach(e->{
            assertEquals(exhibitorCarouselDetailDto,e);
        });
    }

    @Test
    void test_joinMeetingByExhibitorId(){

        exhibitor.setCategoryId("1");
        exhibitor.setName("EXPO");
        exhibitor.setCategoryPositionJson("{\"1\":[{\"key\":1,\"value\":2000.0}]}");
        exhibitor.setMeetingObj("{\n" +
                "    \"MeetingId\": \"cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "    \"MediaPlacement\": {\n" +
                "        \"AudioHostUrl\": \"7ed95a9f6511a9113a3d22cf36a0d415.k.m1.ue1.app.chime.aws:3478\",\n" +
                "        \"AudioFallbackUrl\": \"wss://haxrp.m1.ue1.app.chime.aws:443/calls/cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "        \"ScreenDataUrl\": \"wss://bitpw.m1.ue1.app.chime.aws:443/v2/screen/cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "        \"ScreenSharingUrl\": \"wss://bitpw.m1.ue1.app.chime.aws:443/v2/screen/cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "        \"ScreenViewingUrl\": \"wss://bitpw.m1.ue1.app.chime.aws:443/ws/connect?passcode=null&viewer_uuid=null&X-BitHub-Call-Id=cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "        \"SignalingUrl\": \"wss://signal.m1.ue1.app.chime.aws/control/cbfa96cb-725c-474a-948c-86866560404e\",\n" +
                "        \"TurnControlUrl\": \"https://ccp.cp.ue1.app.chime.aws/v2/turn_sessions\"\n" +
                "    },\n" +
                "    \"MediaRegion\": \"us-east-1\"\n" +
                "}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);
        MeetingAttendeeDto meetingAttendeeDto = new MeetingAttendeeDto();
        meetingAttendeeDto.setSessionId(1L);
        meetingAttendeeDto.setEventAdmins(Collections.singletonList(1L));
        meetingAttendeeDto.setAttendee("attendee");
        meetingAttendeeDto.setMeeting("meetingTest");

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(staffService.findListOfExhibitorAdminByExhibitorIdOrEventAdminRole(anyLong(), any(), any())).thenReturn(Collections.singletonList(staff));

        when(chimeService.joinAttendee(any(), anyString())).thenReturn(meetingAttendeeDto);

        MeetingAttendeeDto meetingAttendeeDto1 = exhibitorsServiceImpl.joinMeetingByExhibitorId(1L, "test", event, user);

        assertEquals(meetingAttendeeDto.getMeeting(),meetingAttendeeDto1.getMeeting());
    }

    @Test
    void test_joinMeetingByExhibitorId_createTime(){

        exhibitor.setCategoryId("1");
        exhibitor.setName("EXPO");
        exhibitor.setCategoryPositionJson("{\"1\":[{\"key\":1,\"value\":2000.0}]}");
        staff.setExhibitorId(exhibitorId);
        staff.setExhibitor(exhibitor);
        MeetingAttendeeDto meetingAttendeeDto = new MeetingAttendeeDto();
        meetingAttendeeDto.setSessionId(1L);
        meetingAttendeeDto.setEventAdmins(Collections.singletonList(1L));
        meetingAttendeeDto.setAttendee("attendee");
        meetingAttendeeDto.setMeeting("meetingTest");

        when(exhibitorRepoService.findAllByEventIdOrderByPositionDesc(anyLong())).thenReturn(Collections.singletonList(exhibitor));
        when(staffService.findListOfExhibitorAdminByExhibitorIdOrEventAdminRole(anyLong(), any(), any())).thenReturn(Collections.singletonList(staff));
        when(chimeService.createExhibitorMeetingAndJoinAttendee(any(), anyString(), any(), any())).thenReturn(meetingAttendeeDto);


        MeetingAttendeeDto meetingAttendeeDto1 = exhibitorsServiceImpl.joinMeetingByExhibitorId(1L, "test", event, user);
        assertEquals(meetingAttendeeDto,meetingAttendeeDto1);
    }

    @Test
    void test_checkUserTryToUseExpoBoothAndUserRestrictedForExpo(){

        when(eventTicketsRepoService.getAllEventTicketTypeIdsWithoutExpoRestrictedTicketsByEventUserANDNotCanceled(any(), any())).thenReturn(Collections.emptyList());
        when(sessionSpeakerService.isUserSpeakerInSessionByEventId(anyLong(),anyLong())).thenReturn(false);
        when(roStaffRoleService.getUserRoleByEvent(any(),any())).thenReturn(UserRole.ROLE_ADMIN);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> exhibitorsServiceImpl.checkUserTryToUseExpoBoothAndUserRestrictedForExpo(event,user,false));
        
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.TICKET_DOES_NOT_ALLOW_EXPO.getDeveloperMessage(), exception.getMessage());
    }
}