package com.accelevents.services.impl;

import com.accelevents.dto.AttendeeProfileDto;
import com.accelevents.common.dto.LeadDetailDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.Staff;
import com.accelevents.domain.User;
import com.accelevents.domain.exhibitors.Exhibitor;
import com.accelevents.domain.virtual.LeadRetriverData;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.dto.TicketHolderDetailDto;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exhibitors.services.ExhibitorSettingsService;
import com.accelevents.services.*;
import com.accelevents.utils.JsonMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LeadServiceImplTest {

    @Spy
    @InjectMocks
    private LeadServiceImpl leadServiceImpl = new LeadServiceImpl();
    @Mock
    private LeadRetriverDataRepoService leadRetriverDataRepoService;

    @Mock
    private StaffService staffService;

    @Mock
    private ExhibitorService exhibitorService;

    @Mock
    private AttendeeProfileService attendeeProfileService;

    @Mock
    private UserService userService;

    @Mock
    private ExhibitorSettingsService exhibitorSettingsService;

    private LeadDetailDto leadDetailDto;
    private Exhibitor exhibitor;
    private Staff staff;
    private TicketHolderDetailDto leadData;
    private LeadRetriverData leadRetriverData;
    private Event event;
    private User user;
    private DataTableResponse dataTableResponse1;
    private AttendeeProfileDto attendeeProfileDto;


    private Long userId = 1L;
    private Long exhibitorId = 1L;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        leadDetailDto = new LeadDetailDto();
        exhibitor = new Exhibitor();
        staff = new Staff();
        leadData = new TicketHolderDetailDto();
        leadRetriverData = new LeadRetriverData();
        user = EventDataUtil.getUser();
        dataTableResponse1 = new DataTableResponse();
        attendeeProfileDto = new AttendeeProfileDto();
    }

    @Test
    public void test_addLeadDetailData_throwCanNotGenerateLeadException() {
        //setup
        exhibitor.setLeadRetrieversAllowed(false);

        //Mock
        when(exhibitorService.findByIdAndEvent(exhibitorId, event)).thenReturn(exhibitor);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> leadServiceImpl.addLeadDetailData(leadDetailDto, staff, exhibitorId, event, null));

        //Assertion
        verify(exhibitorService).findByIdAndEvent(exhibitorId, event);
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.CAN_NOT_GENERATE_LEAD.getDeveloperMessage(), exception.getMessage());
    }

   // @Test
    public void test_addLeadDetailData_throwAlreadyUsedTicketException() {
        //setup

        leadData.setEventTicketId(1L);
        leadDetailDto.setLeadData(leadData);
        exhibitor.setLeadRetrieversAllowed(true);

        //Mock
        when(exhibitorService.findByIdAndEvent(exhibitorId, event)).thenReturn(exhibitor);
        Mockito.when(leadRetriverDataRepoService.isEventTicketExistByEventTicketIdAndExhibitorIdAndSourceAndStaffIdNull(anyLong(), anyLong(), anyString())).thenReturn(true);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> leadServiceImpl.addLeadDetailData(leadDetailDto, staff, exhibitorId, event, null));

        //Assertion
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.ALREADY_USED_TICKET.getDeveloperMessage(), exception.getMessage());
        verify(exhibitorService).findByIdAndEvent(exhibitorId, event);
        verify(leadRetriverDataRepoService).isEventTicketExistByEventTicketIdAndExhibitorIdAndSourceAndStaffIdNull(anyLong(), anyLong(), anyString());
    }

    //@Test
    public void test_addLeadDetailData_success() {
        //setup

        leadDetailDto = setLeadDetailDto();
        exhibitor.setLeadRetrieversAllowed(true);

        //Mock
        when(exhibitorService.findByIdAndEvent(exhibitorId, event)).thenReturn(exhibitor);
        Mockito.when(leadRetriverDataRepoService.isEventTicketExist(leadDetailDto.getLeadData().getEventTicketId(), exhibitorId)).thenReturn(false);
        when(userService.findUserIdByEmail(leadDetailDto.getLeadData().getHolderEmail())).thenReturn(userId);

        //Execution
        leadServiceImpl.addLeadDetailData(leadDetailDto, staff, 1L, event, null);

        //Assertion
        ArgumentCaptor<LeadRetriverData> leadRetriverDataArgumentCaptor = ArgumentCaptor.forClass(LeadRetriverData.class);
        verify(leadRetriverDataRepoService).save(leadRetriverDataArgumentCaptor.capture());

        LeadRetriverData leadRetriverData = leadRetriverDataArgumentCaptor.getValue();
        assertEquals(leadRetriverData.getExhibitorId(), exhibitor.getId(), 1);
        assertEquals(leadRetriverData.getLeadData(), JsonMapper.convertToString(leadDetailDto.getLeadData()));
        assertEquals(leadRetriverData.getLeadRating(), leadDetailDto.getRating());
        assertEquals(leadRetriverData.getNotes(), leadDetailDto.getNote());
        assertEquals(leadRetriverData.isScan(), leadDetailDto.isScan());
        assertEquals(leadRetriverData.getStaffId().longValue(), staff.getId());
        assertEquals(leadRetriverData.getEventTicketId().longValue(), leadDetailDto.getLeadData().getEventTicketId());
        assertEquals(leadRetriverData.getUserId(), userId);

        verify(exhibitorService).findByIdAndEvent(exhibitorId, event);
    }

    private LeadDetailDto setLeadDetailDto() {
        LeadDetailDto leadDetailDto = new LeadDetailDto();
        leadDetailDto.setLeadData(leadData);
        leadDetailDto.setScan(true);
        leadDetailDto.setNote("note");
        leadDetailDto.setRating("warm");
        return leadDetailDto;
    }

    @Test
    public void test_deleteLeadDetailData_throwLeadNotExistException() {

        //mock
        when(leadRetriverDataRepoService.getLeadRetriverData(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> leadServiceImpl.deleteLeadDetailData(new User(), event, 1L, 1L, false));

        //Assertion
        verify(leadRetriverDataRepoService).getLeadRetriverData(anyLong());
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.LEAD_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_deleteLeadDetailData_success() {

        //mock
        when(leadRetriverDataRepoService.getLeadRetriverData(anyLong())).thenReturn(Optional.of(leadRetriverData));

        //Execution
        leadServiceImpl.deleteLeadDetailData(new User(), event, 1L, 1L, false);

        //Assertion
        ArgumentCaptor<LeadRetriverData> leadRetriverDataArgumentCaptor = ArgumentCaptor.forClass(LeadRetriverData.class);
        verify(leadRetriverDataRepoService).save(leadRetriverDataArgumentCaptor.capture());
        verify(leadRetriverDataRepoService).getLeadRetriverData(anyLong());
    }

    //@Test
    public void test_updateLeadDetailData_throwLeadNotExistException() {
        //setup

        //mock
        when(leadRetriverDataRepoService.getLeadRetriverData(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> leadServiceImpl.updateLeadDetailData(1L, leadDetailDto, event, staff, null));

        //Assertion
        verify(leadRetriverDataRepoService).getLeadRetriverData(anyLong());
        assertEquals(NotAcceptableException.ExhibitorExceptionMsg.LEAD_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    //@Test
    public void test_updateLeadDetailData_success() {

        //setup
        leadDetailDto = setLeadDetailDto();

        //mock
        when(leadRetriverDataRepoService.getLeadRetriverData(anyLong())).thenReturn(Optional.of(leadRetriverData));
        when(userService.findUserIdByEmail(leadDetailDto.getLeadData().getHolderEmail())).thenReturn(userId);

        //Execution
        leadServiceImpl.updateLeadDetailData(1L, leadDetailDto, event,staff, null);

        //Assertion
        ArgumentCaptor<LeadRetriverData> leadRetriverDataArgumentCaptor = ArgumentCaptor.forClass(LeadRetriverData.class);
        verify(leadRetriverDataRepoService).save(leadRetriverDataArgumentCaptor.capture());

        LeadRetriverData leadRetriverData = leadRetriverDataArgumentCaptor.getValue();

        assertEquals(leadRetriverData.getLeadData(), JsonMapper.convertToString(leadDetailDto.getLeadData()));
        assertEquals(leadRetriverData.getLeadRating(), leadDetailDto.getRating());
        assertEquals(leadRetriverData.getNotes(), leadDetailDto.getNote());
        assertEquals(leadRetriverData.isScan(), leadDetailDto.isScan());
        assertEquals(leadRetriverData.getEventTicketId().longValue(), leadDetailDto.getLeadData().getEventTicketId());
        assertEquals(leadRetriverData.getUserId(), userId);

        verify(leadRetriverDataRepoService).getLeadRetriverData(anyLong());
    }

    //@Test
    /*public void test_getAllLeadsData_leadDataNull() {

        //mock
        when(staffService.getExhibitorIds(userId)).thenReturn(Arrays.asList());

        //Execution
        DataTableResponse dataTableResponse = leadServiceImpl.getAllLeadsData(userId,1,10,null, staff, 1L);

        //Assertion
        assertNull(dataTableResponse.getData());
        assertEquals(dataTableResponse.getRecordsFiltered(),0);
        assertEquals(dataTableResponse.getRecordsTotal(),0);

        verify(staffService).getExhibitorIds(userId);
    }*/

   // @Test
    public void test_getAllLeadsData_StaffRoleLeadRetriverAndSearchStringNull() {

        //setup
        List<Long> exhibitorIds = new ArrayList<>();
        exhibitorIds.add(1L);

        staff = getStaffDetails(true);
        leadData.setCompany("Company");

        leadRetriverData.setExhibitor(exhibitor);
        leadRetriverData.setLeadData(JsonMapper.convertToString(leadData));
        leadRetriverData.setUserId(userId);
        leadRetriverData.setEventTicketId(1L);

        List<AttendeeProfileDto> attendeeProfileDtos = new ArrayList<>();
        attendeeProfileDto.setUserId(userId);
        attendeeProfileDto.setCompany("Company");
        attendeeProfileDtos.add(attendeeProfileDto);
        dataTableResponse1.setData(attendeeProfileDtos);


        //mock
        when(leadRetriverDataRepoService.getAllByExhibitorsId(anyList())).thenReturn(Collections.singletonList(leadRetriverData));
        when(attendeeProfileService.getAllAttendeeProfileData(any(), any(), anyInt(), anyInt(), anyString())).thenReturn(dataTableResponse1);

        //Execution
        DataTableResponse dataTableResponse = leadServiceImpl.getAllLeadsData(user, event, 10, 0, null, staff, 1L);

        //Assertion
        LeadDetailDto leadDetailDto = ((LeadDetailDto) dataTableResponse.getData().get(0));

        assertEquals(leadDetailDto.getLeadData().toString(), JsonMapper.stringtoObject(leadRetriverData.getLeadData(), TicketHolderDetailDto.class).toString());
        assertEquals(leadDetailDto.getLeadId(), leadRetriverData.getId());
        assertEquals(leadDetailDto.getCreatedOn(), leadRetriverData.getCreatedOn());
        assertEquals(leadDetailDto.getRating(), leadRetriverData.getLeadRating());
        assertEquals(1,dataTableResponse.getRecordsFiltered());
        assertEquals(1,dataTableResponse.getRecordsTotal());

        verify(leadRetriverDataRepoService).getAllByExhibitorsId(anyList());
        verify(exhibitorSettingsService).prepareAttributeValueDto(anyList());
        verify(attendeeProfileService).getAllAttendeeProfileData(any(), any(), anyInt(), anyInt(), anyString());
    }

 //   @Test
    public void test_getAllLeadsData_StaffRoleAdminAndSearchString() {

        //setup
        List<Long> exhibitorIds = new ArrayList<>();
        exhibitorIds.add(1L);

        staff = getStaffDetails(true);
        staff.setRole(StaffRole.exhibitoradmin);
        leadData.setCompany("Company");

        leadRetriverData.setExhibitor(exhibitor);
        leadRetriverData.setLeadData(JsonMapper.convertToString(leadData));
        leadRetriverData.setUserId(userId);
        leadRetriverData.setEventTicketId(1L);

        List<AttendeeProfileDto> attendeeProfileDtos = new ArrayList<>();
        attendeeProfileDto.setUserId(userId);
        attendeeProfileDto.setCompany("Company");
        attendeeProfileDtos.add(attendeeProfileDto);
        dataTableResponse1.setData(attendeeProfileDtos);

        List<Long> userIds = new ArrayList<>();
        userIds.add(1L);
        Map<Long, TicketAttributeValueDto1> ticketHolderAttributes = new HashMap<>();

        //mock
        when(leadRetriverDataRepoService.getAllByExhibitorsIdAndSearch(anyList(), anyString())).thenReturn(Collections.singletonList(leadRetriverData));
        when(exhibitorSettingsService.prepareAttributeValueDto(anyList())).thenReturn(ticketHolderAttributes);
        when(staffService.findListOfUsersIdByExhibitorIdAndRole(anyLong(), anyList())).thenReturn(userIds);
        when(attendeeProfileService.getAttendees(anyString(), anyList())).thenReturn(attendeeProfileDtos);

        //Execution
        DataTableResponse dataTableResponse = leadServiceImpl.getAllLeadsData(user, event, 10, 0, "search", staff, 1L);

        //Assertion
        LeadDetailDto leadDetailDto = ((LeadDetailDto) dataTableResponse.getData().get(0));

        assertEquals(leadDetailDto.getLeadData().toString(), JsonMapper.stringtoObject(leadRetriverData.getLeadData(), TicketHolderDetailDto.class).toString());
        assertEquals(leadDetailDto.getLeadId(), leadRetriverData.getId());
        assertEquals(leadDetailDto.getCreatedOn(), leadRetriverData.getCreatedOn());
        assertEquals(leadDetailDto.getRating(), leadRetriverData.getLeadRating());
        assertEquals(1,dataTableResponse.getRecordsFiltered());
        assertEquals(1,dataTableResponse.getRecordsTotal());

        verify(leadRetriverDataRepoService).getAllByExhibitorsIdAndSearch(anyList(), anyString());
        verify(exhibitorSettingsService).prepareAttributeValueDto(anyList());
        verify(staffService).findListOfUsersIdByExhibitorIdAndRole(anyLong(), anyList());
        verify(attendeeProfileService).getAttendees(anyString(), anyList());
    }

   // @Test
    public void test_getAllLeadsData_StaffRoleLeadRetriverAndSearchStringNullAndIsSeeLeadsFalse() {

        //setup
        List<Long> exhibitorIds = new ArrayList<>();
        exhibitorIds.add(1L);

        staff = getStaffDetails(false);
        leadData.setCompany("Company");

        leadRetriverData.setExhibitor(exhibitor);
        leadRetriverData.setLeadData(JsonMapper.convertToString(leadData));
        leadRetriverData.setUserId(userId);
        leadRetriverData.setEventTicketId(1L);

        List<AttendeeProfileDto> attendeeProfileDtos = new ArrayList<>();
        attendeeProfileDto.setUserId(userId);
        attendeeProfileDto.setCompany("Company");
        attendeeProfileDtos.add(attendeeProfileDto);
        dataTableResponse1.setData(attendeeProfileDtos);

        //mock
        when(leadRetriverDataRepoService.getAllByExhibitorsIdAndStaffId(anyList(), anyLong())).thenReturn(Collections.singletonList(leadRetriverData));
        when(attendeeProfileService.getAttendees(anyString(), anyList())).thenReturn(attendeeProfileDtos);

        //Execution
        DataTableResponse dataTableResponse = leadServiceImpl.getAllLeadsData(user, event, 10, 0, null, staff, 1L);

        //Assertion
        LeadDetailDto leadDetailDto = ((LeadDetailDto) dataTableResponse.getData().get(0));

        assertEquals(leadDetailDto.getLeadData().toString(), JsonMapper.stringtoObject(leadRetriverData.getLeadData(), TicketHolderDetailDto.class).toString());
        assertEquals(leadDetailDto.getLeadId(), leadRetriverData.getId());
        assertEquals(leadDetailDto.getCreatedOn(), leadRetriverData.getCreatedOn());
        assertEquals(leadDetailDto.getRating(), leadRetriverData.getLeadRating());
        assertEquals(1,dataTableResponse.getRecordsFiltered());
        assertEquals(1,dataTableResponse.getRecordsTotal());

        verify(leadRetriverDataRepoService).getAllByExhibitorsIdAndStaffId(anyList(), anyLong());
        verify(attendeeProfileService).getAttendees(anyString(), anyList());
    }

   // @Test
    public void test_getAllLeadsData_StaffRoleLeadRetriverAndSearchStringAndIsSeeLeadsFalse() {

        //setup
        List<Long> exhibitorIds = new ArrayList<>();
        exhibitorIds.add(1L);

        staff = getStaffDetails(false);
        leadData.setCompany("Company");

        leadRetriverData.setExhibitor(exhibitor);
        leadRetriverData.setLeadData(JsonMapper.convertToString(leadData));
        leadRetriverData.setUserId(userId);
        leadRetriverData.setEventTicketId(1L);

        List<AttendeeProfileDto> attendeeProfileDtos = new ArrayList<>();
        attendeeProfileDto.setUserId(userId);
        attendeeProfileDto.setCompany("Company");
        attendeeProfileDtos.add(attendeeProfileDto);
        dataTableResponse1.setData(attendeeProfileDtos);

        //mock
        when(leadRetriverDataRepoService.getAllByExhibitorsIdAndStaffIdAndSearch(anyList(), anyLong(), anyString())).thenReturn(Collections.singletonList(leadRetriverData));
        when(attendeeProfileService.getAttendees(anyString(), anyList())).thenReturn(attendeeProfileDtos);

        //Execution
        DataTableResponse dataTableResponse = leadServiceImpl.getAllLeadsData(user, event, 10, 0, "search", staff, 1L);

        //Assertion
        LeadDetailDto leadDetailDto = ((LeadDetailDto) dataTableResponse.getData().get(0));

        assertEquals(leadDetailDto.getLeadData().toString(), JsonMapper.stringtoObject(leadRetriverData.getLeadData(), TicketHolderDetailDto.class).toString());
        assertEquals(leadDetailDto.getLeadId(), leadRetriverData.getId());
        assertEquals(leadDetailDto.getCreatedOn(), leadRetriverData.getCreatedOn());
        assertEquals(leadDetailDto.getRating(), leadRetriverData.getLeadRating());
        assertEquals(1,dataTableResponse.getRecordsFiltered());
        assertEquals(1,dataTableResponse.getRecordsTotal());

        verify(leadRetriverDataRepoService).getAllByExhibitorsIdAndStaffIdAndSearch(anyList(), anyLong(), anyString());
        verify(attendeeProfileService).getAttendees(anyString(), anyList());
    }

    private Staff getStaffDetails(boolean isSeeLeads) {
        exhibitor.setSeeAllLeads(isSeeLeads);

        Staff staff = new Staff();
        staff.setExhibitor(exhibitor);
        staff.setRole(StaffRole.leadretriever);
        return staff;
    }
}
