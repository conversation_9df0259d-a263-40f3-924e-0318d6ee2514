package com.accelevents.services.impl;

import com.accelevents.domain.RefundTransaction;
import com.accelevents.repositories.RefundTranctionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class RefundTransactionServiceImplTest {


    @Spy
    @InjectMocks
    private RefundTransactionServiceImpl refundTransactionService;

    @Mock
    private RefundTranctionRepository refundTranctionRepository;

    @BeforeEach
    void setUp(){
    }

    @Test
    void test_save(){
        //setup
        RefundTransaction refundTransaction = new RefundTransaction();
        //mock
        //execute
        refundTransactionService.save(refundTransaction);
        //verify
        ArgumentCaptor<RefundTransaction> refundTransactionArgumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTranctionRepository, times(1)).save(refundTransactionArgumentCaptor.capture());
        RefundTransaction refundTransactionSave = refundTransactionArgumentCaptor.getValue();
        assertEquals(refundTransactionSave.getId(), refundTransaction.getId());
    }

}
