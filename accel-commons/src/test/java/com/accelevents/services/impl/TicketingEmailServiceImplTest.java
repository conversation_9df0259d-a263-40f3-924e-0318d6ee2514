package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.services.StripeTransactionService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.TicketingOrderService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.PurchaserInfo;
import com.itextpdf.text.DocumentException;
import freemarker.template.TemplateException;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingEmailServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingEmailServiceImpl ticketingEmailService = new TicketingEmailServiceImpl();

    @Mock
    private TicketingOrderService ticketingOrderService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    Event event;
    User user;
    TicketingOrder ticketingOrder;
    EventTickets eventTickets;
    Ticketing ticketing;
    StripeTransaction stripeTransaction;
    PurchaserInfo purchaserInfo;

    private Long eventTicketingID = 1L;

	@BeforeEach
    void setUp() {
        // Prepare data
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(1L);
        event.setCurrency(Currency.AUD);
        event.setEventURL("asd");

        user = new User();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setFirstName("Normal");
        user.setLastName("User");
        user.setMostRecentEventId(1L);

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setActivated(true);
        ticketing.setEventid(event);
        ticketing.setEventAddress("testAddress");
        ticketing.setShowRemainingTickets(false);
        ticketing.setEventStartDate(new Date());
        ticketing.setEventEndDate(LocalDate.now().plusDays(10).toDate());
        ticketing.setCheckoutminutes(10);

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setExpirein(LocalDateTime.now().plusMinutes(10).toDate());
        ticketingOrder.setEventid(event);

        eventTickets = TestTicketDataUtil.getEventTickets();
        eventTickets.setEventId(event.getEventId());

        stripeTransaction = new StripeTransaction();
        stripeTransaction.setLastFour("1111");
        stripeTransaction.setCardType("visa");

        purchaserInfo = new PurchaserInfo(user);
    }

    @Test
    void test_resendEmail() throws TemplateException, IOException, DocumentException, JAXBException {
        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(ticketingOrderService.findByidAndEventid(anyLong(), any())).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrderAndStatusIdNotIn(any(), anyList())).thenReturn(eventTicketsList);
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrder.getId())).thenReturn(stripeTransaction);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingHelperService.getPurchaserInfo(any(), any())).thenReturn(purchaserInfo);

        //expect

        //execution
        assertThrows(RuntimeException.class,
                () -> ticketingEmailService.resendEmail(ticketingOrder.getId(), event, null));

    }

    @Test
    void test_resendEmai_success() throws TemplateException, IOException, DocumentException, JAXBException {

        //mock
        when(ticketingOrderService.findByidAndEventid(anyLong(), any())).thenReturn(ticketingOrder);
        Mockito.doNothing().when(ticketingEmailService).resendEmail(event, eventTicketingID, ticketingOrder);

        //execution
        ticketingEmailService.resendEmail(ticketingOrder.getId(), event, eventTicketingID);
    }

    @Test
    void test_resendEmai_success1() throws TemplateException, IOException, DocumentException, JAXBException {

        //mock
        when(eventCommonRepoService.findByIdAndTicketingOrderAndNotInTicketStatus(anyLong(), any(),anyList())).thenReturn(eventTickets);
        Mockito.doNothing().when(ticketingEmailService).reSendTicketingPurchaseOrderEmail(any(), any(), anyList(), anyBoolean(),anyBoolean());
        //execution
        ticketingEmailService.resendEmail(event, eventTicketingID, ticketingOrder);
    }

    @Test
    void test_resendEmai_success2() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
		Long orderId = 1L;
        when(eventTicketsRepoService.findByIdAndPurchaserIdAndTicketingId(orderId,user.getUserId(),eventTicketingID)).thenReturn(ticketingOrder);
        Mockito.doNothing().when(ticketingEmailService).resendEmail(ticketingOrder.getEventid(), eventTicketingID, ticketingOrder);
        //execution
        ticketingEmailService.resendEmail(orderId, user.getUserId(), eventTicketingID);
    }

    @Test
    void test_reSendTicketingPurchaseOrderEmail() throws TemplateException, IOException, DocumentException, JAXBException {
        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.get(0).setTicketPurchaserId(user);

        //mock


        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrder.getId())).thenReturn(null);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingHelperService.getPurchaserInfo(any(), any())).thenReturn(purchaserInfo);
        when(ticketingHelperService.getTicketOrderHeaderText(any())).thenReturn("");


        //execution
        ticketingEmailService.reSendTicketingPurchaseOrderEmail(event, ticketingOrder, eventTicketsList, true,false);
    }

    @Test
    void test_resendEmailWithEventTicketId() throws TemplateException, IOException, DocumentException, JAXBException {

        //mock
        when(ticketingOrderService.findByidAndEventid(anyLong(), any())).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByIdAndTicketingOrderAndNotInTicketStatus(anyLong(), any(),anyList())).thenReturn(eventTickets);
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrder.getId())).thenReturn(stripeTransaction);
        when(ticketingHelperService.getPurchaserInfo(any(), any())).thenReturn(purchaserInfo);

        //expect
        
        //execution
        assertThrows(RuntimeException.class,
                () -> ticketingEmailService.resendEmail(anyLong(), (Event) any(), anyLong()));

    }

    @Test
    void test_resendEmailWithThrowORDER_NOT_FOUND() throws TemplateException, IOException, DocumentException, JAXBException {

        //expect
        //mock
        when(ticketingOrderService.findByidAndEventid(anyLong(), any())).thenReturn(ticketingOrder);

        //execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingEmailService.resendEmail(ticketingOrder.getId(), event, null));
        
        assertEquals(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_resendEmailWithoutEvent() throws TemplateException, IOException, DocumentException, JAXBException {
        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock

        //expect
        
        //execution
        assertThrows(RuntimeException.class,
                () -> ticketingEmailService.resendEmail(anyLong(), anyLong(), anyLong()));

    }

}
