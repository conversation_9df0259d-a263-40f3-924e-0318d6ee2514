package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.common.dto.EventChallengeDTO;
import com.accelevents.common.dto.RedisChallengeAreaDTO;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.domain.session_speakers.SessionTagAndTrack;
import com.accelevents.domain.session_speakers.Speaker;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.TotalTicketsAndTotalTicketPriceDto;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant;
import com.accelevents.session_speakers.dto.SessionBasicDetailsDTO;
import com.accelevents.session_speakers.dto.SessionDTO;
import com.accelevents.session_speakers.dto.SpeakerBasicDTO;
import com.accelevents.session_speakers.dto.SpeakerDTO;
import com.accelevents.ticketing.dto.StaffTicketingOrderDto;
import com.accelevents.ticketing.dto.TicketTypeSettingDto;
import com.accelevents.ticketing.dto.TicketingTicketTypeDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.JsonMapper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.continuing.ed.ContinuingEdConstants.REQUIRED_QUIZ_SCORE;
import static com.accelevents.continuing.ed.ContinuingEdConstants.SURVEY_SUBMISSION;

public class EventDataUtil {


    private static Long sessionId = 1l;
    private static Long speakerId = 1l;
    private static Date sessionStartDate = DateUtils.getCurrentDate();
    private static Date sessionEndDate = DateUtils.getAddedHours(new Date(), 5);
    private static String startDate = "2019/01/01 05:30";
    private static String endDate = "2019/04/04 05:30";
    private static Date startDate1 = new Date(startDate);
    private static Date endDate1 = new Date(endDate);
    private static String firstName = "Jon";
    private static String lastName = "Kaz";
	private static Long id = 1L;
    private static String seats = "D1-15";


	public static Event getEvent(){
        Event event =  new Event("TestEvent1", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(id);
        event.setEventURL("TestEvent1");
        event.setCurrency(Currency.USD);
        event.setTimezoneId("India Time");
        event.setAuctionId(id);
        event.setCreditCardEnabled(true);
        event.setEquivalentTimeZone("Asia/Calcutta");
        return event;
    }

    public static Ticketing getTicketing(Event event){
        Ticketing
        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setEventid(event);
        ticketing.setEventStartDate(startDate1);
        ticketing.setEventEndDate(endDate1);
        ticketing.setEventAddress("Address");
        ticketing.setActivated(true);
        ticketing.setRecurringEvent(true);
        ticketing.setTicketPdfDesign("pdfDesign");
        ticketing.setChartKey("chartKey");
        ticketing.setAllowEditingOfDisclaimer(true);
        ticketing.setCustomDisclaimer("customerDisclaimer");
        ticketing.setShowRemainingTickets(true);
        ticketing.setCollectTicketHolderAttributes(true);
        ticketing.setCheckoutminutes(10);
        ticketing.setOnlineEvent(false);
        ticketing.setAllowAttendeeToEditInfo(true);
        ticketing.setExitIntentPopupEnabled(false);
        ticketing.setPreEventAccessMinutes(30);
        ticketing.setPostEventAccessMinutes(1800);
        return ticketing;
    }

    public static TicketingType getTicketingType(Event event){
       TicketingType ticketingType = new TicketingType();
       ticketingType.setId(id);
       ticketingType.setBundleType(TicketBundleType.TABLE);
       ticketingType.setNumberOfTicketPerTable(1);
       ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
       ticketingType.setRecurringEventSalesEndTime(60);
       ticketingType.setCreatedFrom(1L);
       ticketingType.setEndDate(endDate1);
       ticketingType.setStartDate(startDate1);
       ticketingType.setRecurringEventId(id);
       ticketingType.setMaxTicketsPerBuyer(1L);
       ticketingType.setPassfeetobuyer(true);
       ticketingType.setPassFeeVatToBuyer(true);
       ticketingType.setTicketTypeName("General Admission");
       ticketingType.setNumberofticket(10);
       ticketingType.setNumberOfTickets(10);
       ticketingType.setStartDateStr(startDate);
       ticketingType.setSalePrice(10D);
       ticketingType.setEndDateStr(endDate);
       ticketingType.setMinTicketsPerBuyer(1L);
       ticketingType.setPrice(10D);
       ticketingType.setHidden(false);
       ticketingType.setTicketType(TicketType.PAID);
       ticketingType.setTicketing(getTicketing(event));
        return ticketingType;
    }

    public static TicketTypeSettingDto getTicketTypeSettingDtoData(Event event){
        TicketTypeSettingDto ticketTypeSettingDtoData = new TicketTypeSettingDto();
        ticketTypeSettingDtoData.setTypeId(id);
        ticketTypeSettingDtoData.setHidden(true);
        ticketTypeSettingDtoData.setCategoryColor("#D1D1D1");
        ticketTypeSettingDtoData.setPrice(10D);
        ticketTypeSettingDtoData.setName("General Admission");
        ticketTypeSettingDtoData.setRecurringEventId(id);
        ticketTypeSettingDtoData.setTicketType(TicketType.PAID);
        ticketTypeSettingDtoData.setNumberOfTicket(10);
        ticketTypeSettingDtoData.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDtoData.setEndDate(endDate);
        ticketTypeSettingDtoData.setStartDate(startDate);
        ticketTypeSettingDtoData.setPassfeetobuyer(true);
        ticketTypeSettingDtoData.setPassFeeVatToBuyer(true);
        ticketTypeSettingDtoData.setTicketsPerTable(1);
        ticketTypeSettingDtoData.setMaxTickerPerBuyer(1L);
        ticketTypeSettingDtoData.setMinTickerPerBuyer(1L);
        ticketTypeSettingDtoData.setRecurringEventSalesEndTime(50);
        ticketTypeSettingDtoData.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketTypeSettingDtoData.setCreatedFrom(1L);

        return ticketTypeSettingDtoData;
    }

    public static EventDesignDetail getEventDesignDetail(Event event){
        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setEnableAutoAssignedSequence(true);
        eventDesignDetail.setDesc("Description");
        eventDesignDetail.setEvent(event);
        return eventDesignDetail;
    }

    public static AttendeeSequenceNumber getAttendeeSequenceNumber(EventTickets eventTicket){
        AttendeeSequenceNumber attendeeSequenceNumber= new AttendeeSequenceNumber();
        attendeeSequenceNumber.setId(1L);
        attendeeSequenceNumber.setEventTicketId(eventTicket);
        attendeeSequenceNumber.setNumber(1);
        attendeeSequenceNumber.setModified(false);
        return attendeeSequenceNumber;
    }

    public static EventTickets getEventTickets(Event event){
        EventTickets eventTickets = new EventTickets();
        eventTickets.setId(id);
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        return eventTickets;
    }

    public static TicketingTable getTicketingTable(Event event){
        TicketingTable ticketingTable = new TicketingTable();
        ticketingTable.setId(1L);
        ticketingTable.setEventId(event);
        ticketingTable.setTableNoSequence(2L);

        return ticketingTable;
    }

    /*public static TicketStatus getTicketStatus(Event event){
        TicketStatus ticketStatus = new TicketStatus();
        ticketStatus.setId(id);
        ticketStatus.setStaus(Constants.TICKETING_STATUS_BOOKED);

        return ticketStatus;
    }*/

    public static StaffTicketingOrderDto getStaffTicketingOrderDto(){
        StaffTicketingOrderDto staffTicketingOrderDto = new StaffTicketingOrderDto();
        staffTicketingOrderDto.setPaymentType(Constants.FAIL);

        return staffTicketingOrderDto;
    }

    public static RecurringEvents getRecurringEvents(){
        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setCheckInXMinutesBefore(60);
        recurringEvents.setRecurringEventStartDate(startDate1);

        return recurringEvents;
    }

    public static TicketingOrder getTicketingOrder(){
        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setPurchaser(EventDataUtil.getUser());
        ticketingOrder.setEventid(getEvent());
        return ticketingOrder;
    }

    public static User getUser(){
        User user = new User();
        user.setUserId(id);
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setEmail("<EMAIL>");

        return user;
    }
    public static User getOtherUser(){
        User user = new User();
        user.setUserId(2L);
        user.setFirstName("Jhon");
        user.setLastName("watt");

        return user;
    }
    public static String getSampleXML() {

		String email = "<EMAIL>";
		return getSampleXML(firstName, lastName, email);
    }

    public static String getSampleXML(String firstName, String lastName, String email) {

        return "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<ticketAttributeValueDto>\n" +
                "    <holder>\n" +
                "        <attributes>\n" +
                "            <key>"+ Constants.FIRST_NAME+"</key>\n" +
                "            <value>"+firstName+"</value>\n" +
                "        </attributes>\n" +
                "        <attributes>\n" +
                "            <key>"+Constants.LAST_NAME+"</key>\n" +
                "            <value>"+lastName+"</value>\n" +
                "        </attributes>\n" +
                "        <attributes>\n" +
                "            <key>"+Constants.EMAIL+"</key>\n" +
                "            <value>"+email+"</value>\n" +
                "        </attributes>\n" +
                "        <questions>\n" +
                "            <key>"+Constants.LAST_NAME+"</key>\n" +
                "            <value>"+lastName+"</value>\n" +
                "        </questions>\n" +
                "    </holder>\n" +
                "    <purchaser>\n" +
                "        <attributes>\n" +
                "            <key>"+Constants.FIRST_NAME+"</key>\n" +
                "            <value>"+firstName+"</value>\n" +
                "        </attributes>\n" +
                "        <attributes>\n" +
                "            <key>"+Constants.LAST_NAME+"</key>\n" +
                "            <value>"+lastName+"</value>\n" +
                "        </attributes>\n" +
                "        <attributes>\n" +
                "            <key>"+Constants.EMAIL+"</key>\n" +
                "            <value>"+email+"</value>\n" +
                "        </attributes>\n" +
                "        <questions>\n" +
                "            <key>"+Constants.LAST_NAME+"</key>\n" +
                "            <value>"+lastName+"</value>\n" +
                "        </questions>\n" +
                "    </purchaser>\n" +
                "</ticketAttributeValueDto>";
    }

    public static String getJsonValue() {
        String firstName = "Jon";
        String lastName = "Kaz";
        String email = "<EMAIL>";

        return getJsonValue(firstName, lastName, email);
    }

    public static String getJsonValue(String firstName, String lastName, String email) {
        return "{\"holder\":{\"questions\":{\""+Constants.LAST_NAME+"\":\""+lastName+"\"},\"attributes\":{\""+Constants.EMAIL+"\":\""+email+"\",\""+Constants.FIRST_NAME+"\":\""+firstName+"\",\""+Constants.LAST_NAME+"\":\""+lastName+"\"}},\"purchaser\":{\"questions\":{\""+Constants.LAST_NAME+"\":\""+lastName+"\"},\"attributes\":{\""+Constants.EMAIL+"\":\""+email+"\",\""+Constants.FIRST_NAME+"\":\""+firstName+"\",\""+Constants.LAST_NAME+"\":\""+lastName+"\"}}}";
    }

    public static TicketingOrderManager getTicketingOrderManager(){
        TicketingOrderManager ticketingOrderManager = new TicketingOrderManager();
        ticketingOrderManager.setId(id);
        ticketingOrderManager.setSeats(seats);
        ticketingOrderManager.setTicketType(EventDataUtil.getTicketingType(EventDataUtil.getEvent()));
        ticketingOrderManager.setOrderId(EventDataUtil.getTicketingOrder());

        return ticketingOrderManager;
    }

    public static EventTickets getEventTickets(){
        EventTickets eventTickets = new EventTickets();
        TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        eventTickets.setId(id);
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderPhoneNumber(9898989898L);
        eventTickets.setHolderCountryCode("US");
        eventTickets.setHolderEmail(Constants.STRING_EMAIL_SPACE);
        eventTickets.setHolderFirstName(Constants.STRING_FIRST_SPACE_NAME);
        TicketingOrder ticketingOrder = EventDataUtil.getTicketingOrder();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        eventTickets.setRecurringEventId(id);
        eventTickets.setHolderLastName(Constants.STRING_LAST_SPACE_NAME);
        eventTickets.setTicketingTypeId(EventDataUtil.getTicketingType(EventDataUtil.getEvent()));
        eventTickets.setSeatNumber(seats);
		String barcodeId = "1d44e288-38f2-425e-929f-68a014b08c88";
		eventTickets.setBarcodeId(barcodeId);
        eventTickets.setPaidAmount(50d);
        eventTickets.setRefundedAmount(10d);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setAutoAssignedAttendeeNumber(null);
        eventTickets.setHolderUserId(getUser());
        eventTickets.setHolderPhoneNumber(1L);
        eventTickets.setEvent(getEvent());
        eventTickets.setEventId(getEvent().getEventId());

        return eventTickets;
    }

    public static TicketingTicketTypeDto getTicketingTicketTypeDto(){
        TicketingTicketTypeDto ticketingTicketTypeDto = new TicketingTicketTypeDto();
        ticketingTicketTypeDto.setSeating(false);
        ticketingTicketTypeDto.setShowRemainingTickets(true);
        ticketingTicketTypeDto.setTicketType(EventDataUtil.getTicketTypeSettingDtoData(EventDataUtil.getEvent()));

        return ticketingTicketTypeDto;
    }

    public static TicketingCoupon getTicketingCoupon(){
        TicketingCoupon ticketingCoupon = new TicketingCoupon();

        ticketingCoupon.setAmount(50d);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
		String eventTicketTypeId = "1";
		ticketingCoupon.setEventTicketTypeId(eventTicketTypeId);
        ticketingCoupon.setCouponEndDate(endDate1);
        ticketingCoupon.setCouponStartDate(startDate1);
		long uses = 1l;
		ticketingCoupon.setUsesPerUser(uses);
		long couponCodeId = 1l;
		ticketingCoupon.setId(couponCodeId);
        ticketingCoupon.setUses(uses);
		String couponCode = "couponCode";
		ticketingCoupon.setName(couponCode);
        ticketingCoupon.setApplyToHiddenTickets(false);
        ticketingCoupon.setEventId(EventDataUtil.getEvent().getEventId());

        return ticketingCoupon;
    }

    public static StripeDTO getStripeDTO(){
        StripeDTO stripeDTO = new StripeDTO();
		double ccFlatFee = 0.3;
		stripeDTO.setCCFlatFee(ccFlatFee);
		double ccPercentageFee = 2.9;
		stripeDTO.setCCPercentageFee(ccPercentageFee);

        return stripeDTO;
    }

    public static Speaker getSpeaker(){
        Speaker speaker = new Speaker();
        speaker.setId(speakerId);
        speaker.setFirstName("jay");
        speaker.setLastName("jay");
        speaker.setBio("speaker bio");
        speaker.setCompany("company");
        speaker.setEmail("<EMAIL>");
        speaker.setUserId(getUser().getUserId());
        speaker.setTitle("title");
        speaker.setEventId(getEvent().getEventId());
        return speaker;
    }

    public static SpeakerDTO getSpeakerDto(){
        SpeakerDTO speakerDTO = new SpeakerDTO();
        speakerDTO.setBio("bio");
        speakerDTO.setCompany("brillworks");
        speakerDTO.setImageUrl("imageUrl");
        speakerDTO.setInstagram("instagram");
        speakerDTO.setLinkedIn("linkedIn");
        speakerDTO.setTwitter("twitter");
        speakerDTO.setEmail("<EMAIL>");
        speakerDTO.setFirstName("jay");
        speakerDTO.setLastName("jay");
        speakerDTO.setSpeakerId(speakerId);
        speakerDTO.setTitle("title");
        return speakerDTO;
    }

    public static Session getSession(){
        Session session = new Session();
        session.setId(1l);
        session.setCapacity(100);
        session.setDescription("session description");
        session.setEndTime(sessionEndDate);
        session.setEventId(getEvent().getEventId());
        session.setFormat(EnumSessionFormat.MAIN_STAGE);
//        session.setLocation("Ahmedabad");
        session.setStreamUrl("Stream Url");
        session.setStreamKey("streamKey");
        session.setStreamProvider(StreamProvider.ACCELEVENTS);
        session.setRtmpUrl("Rtmp url");
        session.setStartTime(sessionStartDate);
        session.setTitle("Demo Session");
        session.setStatus(EnumSessionStatus.VISIBLE);
        session.setTicketTypesThatCanBeRegistered("1");
        return session;
    }

    public static SessionDTO getSessionDto(){
        SessionDTO sessionDTO = new SessionDTO();
        sessionDTO.setTitle("title");
        sessionDTO.setAccelEventsStudio(true);
        sessionDTO.setRtmpUrl("Rtmp url");
        sessionDTO.setStreamUrl("streamUrl");
        sessionDTO.setStreamKey("streamKey");
        sessionDTO.setStreamProvider(StreamProvider.ACCELEVENTS);
        sessionDTO.setCapacity(100);
        sessionDTO.setDescription("Description");
        sessionDTO.setStartTime("2025/12/01 00:00");
        sessionDTO.setEndTime("2025/12/02 00:00");
        sessionDTO.setSessionLocation("ahmedabad");
        sessionDTO.setSurveyEnabled(true);
        sessionDTO.setSurveyId(1l);
        sessionDTO.setLocationId(1L);
        return sessionDTO;
    }

    public static SessionTagAndTrack getSessionTagAndTrack(){
        SessionTagAndTrack sessionTagAndTrack = new SessionTagAndTrack();
        sessionTagAndTrack.setTagOrTrackId(1L);
        sessionTagAndTrack.setSession(getSession());
        sessionTagAndTrack.setSessionId(getSession().getId());
        sessionTagAndTrack.setId(1L);
        return sessionTagAndTrack;
    }

    public static SpeakerDTO getSpeakerDTO(){
        SpeakerDTO speakerDTO = new SpeakerDTO();
        speakerDTO.setFirstName("Aayushi");
        speakerDTO.setLastName("Chauhan");
        speakerDTO.setEmail("<EMAIL>");
        speakerDTO.setBio("This is bio field");
        speakerDTO.setCompany("brilworks");
        speakerDTO.setSpeakerId(1L);
        speakerDTO.setTwitter("Twitter/Aayushi");
        speakerDTO.setLinkedIn("LinkedIn/Aayushi");
        speakerDTO.setInstagram("Instagram/Aayushi");
        speakerDTO.setTitle("Demo Speaker");
        speakerDTO.setUserId(getUser().getUserId());
        return speakerDTO;
    }

    public static EventPlanConfig getEventPlanConfig(){
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setId(1);
        eventPlanConfig.setEventId(getEvent().getEventId());
        PlatformConfigDto platformConfigDto = new PlatformConfigDto();
        platformConfigDto.setPostEventAccessDay(String.valueOf(30));
        platformConfigDto.setPreEventAccess(String.valueOf(1));
        eventPlanConfig.setPlatformConfigJson(JsonMapper.parseToJsonString(platformConfigDto));
        return eventPlanConfig;
    }

    public static SessionBasicDetailsDTO getSessionBasicDetailsDTO(){
       return new SessionBasicDetailsDTO(1L,"Session Name",sessionStartDate,sessionEndDate,EnumSessionFormat.MAIN_STAGE,EnumSessionStatus.VISIBLE);
    }

    public static SpeakerBasicDTO getSpeakerBasicDTO(){
        SpeakerBasicDTO speakerBasicDTO = new SpeakerBasicDTO();
        speakerBasicDTO.setSpeakerId(speakerId);
        speakerBasicDTO.setEmail("<EMAIL>");
        speakerBasicDTO.setEventId(getEvent().getEventId());
        speakerBasicDTO.setFirstName("Speaker");
        speakerBasicDTO.setLastName("Test");
        speakerBasicDTO.setImageUrl("ImageUrl");
        speakerBasicDTO.setCompany("Brilworks");
        return speakerBasicDTO;
    }

    public static EventCECriteriaDTO getSurveyEventCECriteriaDTO() {
        EventCECriteriaDTO ceCriteriaDTO = new EventCECriteriaDTO();
        ceCriteriaDTO.setName("Test Criteria");
        ceCriteriaDTO.setStartDate(DateUtils.getFormattedDateWithPattern(new Date(), Constants.LOCAL_DATE_FORMAT));
        ceCriteriaDTO.setEndDate(DateUtils.getFormattedDateWithPattern(new Date(), Constants.LOCAL_DATE_FORMAT));
        ceCriteriaDTO.setStartDateUtc(new Date());
        ceCriteriaDTO.setEndDateUtc(new Date());

        ceCriteriaDTO.setTicketTypeAllowInCriteria(List.of(1L));
        ceCriteriaDTO.setArea(LeaderBoardConstant.AREA.SESSIONS);

        Map<String, List<Long>> triggerMapForSurvey = new HashMap<>();
        triggerMapForSurvey.put(RedisChallengeAreaDTO.Sessions.breakout_session.name(), List.of(1L));

        ceCriteriaDTO.setAction(getActionListForContinueEdSurvey());
        ceCriteriaDTO.setTrigger(triggerMapForSurvey);

        return ceCriteriaDTO;
    }


    public static List<Map<String, Object>> getActionListForContinueEdSurvey() {
        Map<String, Object> actionMapForSurvey = new HashMap<>();
        actionMapForSurvey.put(Constants.NAME_SMALL, SURVEY_SUBMISSION);
        actionMapForSurvey.put(REQUIRED_QUIZ_SCORE, 50);
        actionMapForSurvey.put(LeaderBoardConstant.ChallengeConstants.POINT, 200);
        return List.of(actionMapForSurvey);
    }

    public static Staff getUserStaff(User user) {
        Staff staff = new Staff();
        staff.setId(1L);
        staff.setUserId(user.getUserId());
        staff.setUser(user);
        return staff;
    }

    public static TotalTicketsAndTotalTicketPriceDto getTotalTicketsAndTotalTicketPriceDto() {
        return new TotalTicketsAndTotalTicketPriceDto(2, 0);
    }
}