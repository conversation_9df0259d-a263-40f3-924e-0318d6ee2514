package com.accelevents.services.impl;

import com.accelevents.domain.AttendeeSequenceNumber;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventDesignDetail;
import com.accelevents.domain.EventTickets;
import com.accelevents.dto.TicketPDFSequenceNumbersDto;
import com.accelevents.repositories.AttendeeSequenceNumberRepository;
import com.accelevents.services.EventDesignDetailService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AttendeeSequenceNumberImplTest {

    @Spy
    @InjectMocks
    private AttendeeSequenceNumberImpl attendeeSequenceNumberImpl = new AttendeeSequenceNumberImpl();

    @Mock
    private AttendeeSequenceNumberRepository attendeeSequenceNumberRepository;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    private Event event;
    private EventTickets eventTickets;
    private EventDesignDetail eventDesignDetail;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        eventTickets = EventDataUtil.getEventTickets();
        eventDesignDetail = EventDataUtil.getEventDesignDetail(event);
    }

    @Test
    void test_getSequenceNumberByEventTicket_success() {
        //Mock
        TicketPDFSequenceNumbersDto sequeceNum = new TicketPDFSequenceNumbersDto(2, 1);
        Mockito.when(attendeeSequenceNumberRepository.getSequenceNumberByEventTicket(eventTickets)).thenReturn(sequeceNum);
        Mockito.when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        //Execution
        String sequenceNumber = attendeeSequenceNumberImpl.getSequenceNumberByEventTicket(eventTickets, event);
        //Assertion
        assertEquals(sequenceNumber, sequeceNum.getNumber().toString());
    }

    @Test
    void test_getMaxSequenceNumber_success() {
        //Mock
        Integer sequeceNum = 1;
        Mockito.when(attendeeSequenceNumberRepository.getMaxSequenceNumber(event)).thenReturn(sequeceNum);
        //Execution
        Integer sequenceNumber = attendeeSequenceNumberImpl.getMaxSequenceNumber(event);
        //Assertion
        assertEquals(sequenceNumber, sequeceNum);
    }

    @Test
    void test_saveAttendeeSequeceNumber_success() {
        //Setup
        AttendeeSequenceNumber attendeeSequenceNumber = new AttendeeSequenceNumber();
        attendeeSequenceNumber.setModified(false);
        attendeeSequenceNumber.setNumber(1);
        attendeeSequenceNumber.setEventTicketId(eventTickets);
        //Execution
        attendeeSequenceNumberImpl.save(attendeeSequenceNumber);
        //Assertion
        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberRepository, Mockito.times(1)).save(argumentCaptor.capture());
    }

    @Test
    void test_deleteByEventTicketIds_success() {
        doNothing().when(attendeeSequenceNumberRepository).deleteByEventTicketIds(any());
        attendeeSequenceNumberImpl.deleteByEventTicketIds(Collections.singletonList(1L));

        verify(attendeeSequenceNumberRepository).deleteByEventTicketIds(Collections.singletonList(1L));
    }

    @Test
    void updateSequenceSetModified() {
        doNothing().when(attendeeSequenceNumberRepository).updateSequenceSetModified(eventTickets,null);
        attendeeSequenceNumberImpl.updateSequenceSetModified(eventTickets,null);

        verify(attendeeSequenceNumberRepository).updateSequenceSetModified(eventTickets,null);
    }

    @Test
    void isSequenceNumberExist() {
        //Mock
        Integer sequeceNum = 1;
        Mockito.when(attendeeSequenceNumberRepository.isSequenceNumberExist(Collections.singletonList(sequeceNum),event)).thenReturn(true);
        //Executiona
        Boolean isExist = attendeeSequenceNumberImpl.isSequenceNumberExist(Collections.singletonList(sequeceNum),event);
        //Assertion
        assertTrue(isExist);
    }
}