//TODO: PowerMock issue with java17
/*
package com.accelevents.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.ImageHelper;
import com.accelevents.services.EventDesignDetailService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.TicketingOrderService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.pdf.TicketOrderPdf;
import com.itextpdf.text.Image;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingDownloadServiceImplTestWithPowerMockito {

    @Spy
    @InjectMocks
    private TicketingDownloadServiceImpl ticketingDownloadServiceImpl = new TicketingDownloadServiceImpl();

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private ImageHelper imageHelper;

    private Event event;
    private User user;
    private Ticketing ticketing;
    private EventTickets eventTickets;
    private TicketingOrder ticketingOrder;
    private EventDesignDetail eventDesignDetail;
    private TicketingTable ticketingTable;
    private RecurringEvents recurringEvents;

    private Long id = 1L;
    private String logoImage = "Accelevents_Default_Event_Logo.jpg";
    private String headerLogoImage = "newheaderlogo.JPG";


    @BeforeEach
    public void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        eventTickets = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        recurringEvents = EventDataUtil.getRecurringEvents();

    }

    @Test
    public void test_writeOrderPdfToResponse_success() throws Exception{

        TicketOrderPdf ticketOrderPdf = mock(TicketOrderPdf.class);

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        httpServletResponse.setHeader("Content-Disposition", "attachment; filename=TicketPassZip.zip");
        httpServletResponse.setContentType("application/zip");

        PowerMockito.whenNew(TicketOrderPdf.class).withArguments(any(), any(), any(), any(),
                anyString(), anyList(), any(),any()).thenReturn(ticketOrderPdf);

        long tableNumberSequence = 1l;

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        List<TicketStatus> ticketStatusList = new ArrayList<>();
        ticketStatusList.add(TicketStatus.BOOKED);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTable(ticketingTable);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets);

        eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setLogoImage(logoImage);
        eventDesignDetail.setHeaderLogoImage(headerLogoImage);

        //mock
        when(ticketingOrderService.findByidAndEventid(id, event)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrderAndStatusIdNotIn(ticketingOrder, ticketStatusList)).thenReturn(eventTicketsList);
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(imageHelper.getImageInstance(any(),any())).thenReturn(Image.getInstance("test.png"));
        doNothing().when(ticketOrderPdf).createPdf(any());

        //Execution
        try {
            ticketingDownloadServiceImpl.writeOrderPdfToResponse(event,id, httpServletResponse);
            assertEquals(user.getUserId(), ticketingOrder.getPurchaser().getUserId());
        } catch (Exception e){}
    }

    @Test
    public void test_writeOrderPdfToResponse_success_with_RecurringEvent() throws Exception{

        TicketOrderPdf ticketOrderPdf = mock(TicketOrderPdf.class);

        //setup
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();

        httpServletResponse.setHeader("Content-Disposition", "attachment; filename=TicketPassZip.zip");
        httpServletResponse.setContentType("application/zip");

        PowerMockito.whenNew(TicketOrderPdf.class).withArguments(any(), any(), any(), any(),
                anyString(), anyList(), any(),any()).thenReturn(ticketOrderPdf);

        long tableNumberSequence = 1l;

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        List<TicketStatus> ticketStatusList = new ArrayList<>();
        ticketStatusList.add(TicketStatus.BOOKED);
        Date recurringEventEndDate = new Date("2019/04/04 05:30");
        recurringEvents.setRecurringEventEndDate(recurringEventEndDate);

        ticketingOrder.setEventid(event);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTable(ticketingTable);
        eventTickets.setRecurringEvents(recurringEvents);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets);

        eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setLogoImage(logoImage);
        eventDesignDetail.setHeaderLogoImage(headerLogoImage);

        //mock
        when(ticketingOrderService.findByidAndEventid(id, event)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrderAndStatusIdNotIn(ticketingOrder, ticketStatusList)).thenReturn(eventTicketsList);
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(imageHelper.getImageInstance(any(),any())).thenReturn(Image.getInstance("test.png"));
        doNothing().when(ticketOrderPdf).createPdf(any());

        //Execution
        try {
            ticketingDownloadServiceImpl.writeOrderPdfToResponse(event,id, httpServletResponse);
        } catch (Exception e){}

    }
}*/
