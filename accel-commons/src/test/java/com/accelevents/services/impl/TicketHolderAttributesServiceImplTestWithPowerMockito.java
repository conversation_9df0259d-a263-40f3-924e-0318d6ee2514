//TODO: PowerMock issue with java17
/*
package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.dto.TicketAttributeValueDto;
import com.accelevents.dto.TicketAttributeValueDto1;
import com.accelevents.repositories.require.attributes.TicketHolderAttributesRepository;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest(JAXBContext.class)
public class TicketHolderAttributesServiceImplTestWithPowerMockito {

    @Spy
    @InjectMocks
    private  TicketHolderAttributesServiceImpl ticketHolderAttributesServiceImpl = new TicketHolderAttributesServiceImpl();

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private TicketHolderAttributesRepository ticketHolderAttributesRepository;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

	private long Id = 1l;
    private Long eventId = 1L;
	private String email = "<EMAIL>";

    @BeforeEach
    public void setUp() throws Exception {

		Event event = EventDataUtil.getEvent();
		User user = new User();
        user.setUserId(1l);
    }

    @Test
    public void test_getUnmarshaller_throwJAXBException() throws JAXBException{
        PowerMockito.mockStatic(JAXBContext.class);

        when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);
        Unmarshaller unmarshaller = ticketHolderAttributesServiceImpl.getUnmarshaller();
        assertNull(unmarshaller);
    }

    @Test
    public void test_parseJsonToXML_throwJAXBException() throws JAXBException {

        //setup
        Map<String, String> holder = new HashMap<>();
		String firstName = "Jon";
		holder.put(Constants.FIRST_NAME, firstName);
        Map<String, String> purchaser = new HashMap<>();
		String lastName = "Kaz";
		holder.put(Constants.LAST_NAME, lastName);
		TicketAttributeValueDto1 ticketAttributeValueDto1 = new TicketAttributeValueDto1();
        ticketAttributeValueDto1.setHolder(holder);
        ticketAttributeValueDto1.setPurchaser(purchaser);

        PowerMockito.mockStatic(JAXBContext.class);

        when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);
        String xmlData = ticketHolderAttributesServiceImpl.parseJsonToXML(ticketAttributeValueDto1);
        assertNull(xmlData);
    }
}*/
