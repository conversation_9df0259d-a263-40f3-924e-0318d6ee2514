package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.IPLookUpData;
import com.accelevents.domain.TicketingOrder;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.dto.IPLookUpDataDto;
import com.accelevents.dto.IPLookUpDto;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.IPLookDataRepository;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IPLookUpDateServiceImplTest {

    @Spy
    @InjectMocks
    private IPLookUpDateServiceImpl ipLookUpDateServiceImpl;

    @Mock
    private IPLookDataRepository ipLookDataRepository;

    private IPLookUpData ipLookUpData;
    private Event event;
    private IPLookUpDto ipLookUpDto;
    private Date date;

	@BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);

        event = new Event();
        event.setTimezoneId("India Time");
        event.setEquivalentTimeZone("Asia/Calcutta");
        event.setEventId(1L);
        date = DateUtils.getCurrentDate();

        ipLookUpData = new IPLookUpData();
        ipLookUpData.setId(1L);
        ipLookUpData.setEventId(10L);
        ipLookUpData.setTicketingOrderId(1161);
        ipLookUpData.setSource(StripeTransactionSource.EVENT_TICKETING);
        ipLookUpData.setCity("Mumbai");
        ipLookUpData.setDistrict("Malad West");
        ipLookUpData.setZipcode("400064");
        ipLookUpData.setCountry("India");
        ipLookUpData.setCountryCode(CountryCode.IN);
        ipLookUpData.setLongitude(BigDecimal.valueOf(72.83507));
        ipLookUpData.setLatitude(BigDecimal.valueOf(19.18401));
        ipLookUpData.setCreatedDate(date);
		String jsonString = "{\n" +
				"    \"ip\": \"**************\",\n" +
				"    \"continent_code\": \"AS\",\n" +
				"    \"continent_name\": \"Asia\",\n" +
				"    \"countryCode\": \"IN\",\n" +
				"    \"country_code3\": \"IND\",\n" +
				"    \"country\": \"India\",\n" +
				"    \"country_capital\": \"New Delhi\",\n" +
				"    \"state_prov\": \"Maharashtra\",\n" +
				"    \"district\": \"Malad West\",\n" +
				"    \"city\": \"Mumbai\",\n" +
				"    \"zipcode\": \"400064\",\n" +
				"    \"latitude\": \"19.18401\",\n" +
				"    \"longitude\": \"72.83507\",\n" +
				"    \"is_eu\": false,\n" +
				"    \"source\": \"EVENT_TICKETING\",\n" +
				"    \"sourceId\": \"1161\",\n" +
				"    \"calling_code\": \"+91\",\n" +
				"    \"country_tld\": \".in\",\n" +
				"    \"languages\": \"en-IN,hi,bn,te,mr,ta,ur,gu,kn,ml,or,pa,as,bh,sat,ks,ne,sd,kok,doi,mni,sit,sa,fr,lus,inc\",\n" +
				"    \"country_flag\": \"https://ipgeolocation.io/static/flags/in_64.png\",\n" +
				"    \"geoname_id\": \"1275339\",\n" +
				"    \"isp\": \"BHARTI\",\n" +
				"    \"event_id\": \"1414\",\n" +
				"    \"connection_type\": \"dsl\",\n" +
				"    \"organization\": \"Bharti Airtel Limited\",\n" +
				"    \"eventId\": \"10\",\n" +
				"    \"currency\": {\n" +
				"      \"code\": \"INR\",\n" +
				"      \"name\": \"Indian Rupee\",\n" +
				"      \"symbol\": \"?\"\n" +
				"    },\n" +
				"    \"time_zone\": {\n" +
				"      \"name\": \"Asia/Kolkata\",\n" +
				"      \"offset\": 5.5,\n" +
				"      \"current_time\": \"2019-11-26 20:42:39.616+0530\",\n" +
				"      \"current_time_unix\": 1574781159.616,\n" +
				"      \"is_dst\": false,\n" +
				"      \"dst_savings\": 0\n" +
				"    }\n" +
				"  }";
		ipLookUpData.setLookUpText(jsonString);

        ipLookUpDto = new IPLookUpDto();
        ipLookUpDto.setEventId(10L);
        ipLookUpDto.setSourceId(1161);
        ipLookUpDto.setSource(StripeTransactionSource.EVENT_TICKETING);
        ipLookUpDto.setCity("Mumbai");
        ipLookUpDto.setDistrict("Malad West");
        ipLookUpDto.setZipcode("400064");
        ipLookUpDto.setCountry("India");
        ipLookUpDto.setCountryCode(CountryCode.IN);
        ipLookUpDto.setLongitude(BigDecimal.valueOf(72.83507));
        ipLookUpDto.setLatitude(BigDecimal.valueOf(19.18401));
        ipLookUpDto.setLookUpText(jsonString);
        ipLookUpDto.setCreatedDate(date);

    }

    @Test
    void test_save() {

        //Execution
        ipLookUpDateServiceImpl.save(ipLookUpData);

        //Assertion
        ArgumentCaptor<IPLookUpData> ipLookUpDataArgumentCaptor = ArgumentCaptor.forClass(IPLookUpData.class);
        verify(ipLookDataRepository).save(ipLookUpDataArgumentCaptor.capture());

        IPLookUpData actual = ipLookUpDataArgumentCaptor.getValue();
        assertEquals(actual.getId(), ipLookUpData.getId());
        assertEquals(actual.getEventId(), ipLookUpData.getEventId());
        assertEquals(actual.getTicketingOrderId(), ipLookUpData.getTicketingOrderId());
        assertEquals(actual.getSource(), ipLookUpData.getSource());
        assertEquals(actual.getCity(), ipLookUpData.getCity());
        assertEquals(actual.getDistrict(), ipLookUpData.getDistrict());
        assertEquals(actual.getZipcode(), ipLookUpData.getZipcode());
        assertEquals(actual.getCountry(), ipLookUpData.getCountry());
        assertEquals(actual.getCountryCode(), ipLookUpData.getCountryCode());
        assertEquals(actual.getLongitude(), ipLookUpData.getLongitude());
        assertEquals(actual.getLatitude(), ipLookUpData.getLatitude());
        assertEquals(actual.getLookUpText(), ipLookUpData.getLookUpText());
        assertEquals(actual.getCreatedDate(), ipLookUpData.getCreatedDate());

    }

    @Test
    void test_setIpLookUpData() {

        when(ipLookDataRepository.getIpLookupDataByOrderIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

        //Execution
        ipLookUpDateServiceImpl.saveIpLookUpDataInDB(ipLookUpDto);

        //Assertion
        ArgumentCaptor<IPLookUpData> ipLookUpDataArgumentCaptor = ArgumentCaptor.forClass(IPLookUpData.class);
        verify(ipLookDataRepository).save(ipLookUpDataArgumentCaptor.capture());

        IPLookUpData actual = ipLookUpDataArgumentCaptor.getValue();
        assertNotNull(actual);
        assertEquals(actual.getEventId(), 10);
        assertEquals(actual.getTicketingOrderId(), ipLookUpDto.getSourceId());
        assertEquals(actual.getSource(), ipLookUpDto.getSource());
        assertEquals(actual.getCity(), ipLookUpDto.getCity());
        assertEquals(actual.getDistrict(), ipLookUpDto.getDistrict());
        assertEquals(actual.getZipcode(), ipLookUpDto.getZipcode());
        assertEquals(actual.getCountry(), ipLookUpDto.getCountry());
        assertEquals(actual.getCountryCode(), ipLookUpDto.getCountryCode());
        assertEquals(actual.getLongitude(), ipLookUpDto.getLongitude());
        assertEquals(actual.getLatitude(), ipLookUpDto.getLatitude());
        assertEquals(actual.getCreatedDate(), ipLookUpDto.getCreatedDate());
    }

    static Object[] searchByCityDistrictCountry(){
        return new Object[]{
          new Object[]{"Mumbai", null, null},
          new Object[]{"Malad West", null, null},
          new Object[]{"India", null, null},
          new Object[]{null, "21/11/2019 00:00:00" ,null},
          new Object[]{null, null, "26/12/2019 00:00:00"},
          new Object[]{null, "21/11/2019 00:00:00", "26/12/2019 00:00:00"},
        };
    }
    @ParameterizedTest
    @MethodSource("searchByCityDistrictCountry")
    void test_getLatLong_searchWithCityDistrictOrCountry(String search, String fromDate, String toDate) {

        //setup
        IPLookUpDataDto ipLookUpDataDto = new IPLookUpDataDto();
        ipLookUpDataDto.setLatitude(ipLookUpData.getLatitude());
        ipLookUpDataDto.setLongitude(ipLookUpData.getLongitude());

        List<IPLookUpDataDto> latitudeLongitude = new ArrayList<>();
        latitudeLongitude.add(ipLookUpDataDto);

        //mock
        when(ipLookDataRepository.getLatitudeLongitudeByDateRangeAndCityStateCountry(nullable(String.class), anyLong(), any(), any(), any(), any())).thenReturn(latitudeLongitude);

        //Execution
        List<IPLookUpDataDto> latLong = ipLookUpDateServiceImpl.getLatLong(search, event, null, fromDate, toDate);

        //Assertion
        assertEquals(latLong.get(0).getLatitude(), ipLookUpData.getLatitude());
        assertEquals(latLong.get(0).getLongitude(), ipLookUpData.getLongitude());
    }

    static Object[] searchByTicketType(){
        return new Object[]{
             new Object[]{Collections.singletonList(TicketType.PAID), "Mumbai", null, null},
             new Object[]{Collections.singletonList(TicketType.FREE), "Malad West", null, null},
             new Object[]{Collections.singletonList(TicketType.DONATION), "India", null, null},
             new Object[]{Arrays.asList(TicketType.PAID, TicketType.FREE), null, "21/11/2019 00:00:00" ,null},
             new Object[]{Arrays.asList(TicketType.PAID, TicketType.FREE, TicketType.DONATION), null, null, "26/12/2019 00:00:00"},
             new Object[]{Arrays.asList(TicketType.FREE, TicketType.DONATION), null, "21/11/2019 00:00:00", "26/12/2019 00:00:00"},
             new Object[]{Arrays.asList(TicketType.PAID, TicketType.DONATION), "Mumbai", null, null},
        };
    }

    @ParameterizedTest
    @MethodSource("searchByTicketType")
    void test_getLatLong_searchWithTicketType(List<TicketType> ticketType, String search, String fromDate, String toDate) {

        //setup
        IPLookUpDataDto ipLookUpDataDto = new IPLookUpDataDto();
        ipLookUpDataDto.setLatitude(ipLookUpData.getLatitude());
        ipLookUpDataDto.setLongitude(ipLookUpData.getLongitude());

        List<IPLookUpDataDto> latitudeLongitude = new ArrayList<>();
        latitudeLongitude.add(ipLookUpDataDto);

        //mock
        when(ipLookDataRepository.getLatitudeLongitudeByTicketType(nullable(String.class), anyLong(), anyList(),
                isA(StripeTransactionSource.class), isA(TicketingOrder.TicketingOrderStatus.class),
                any(), nullable(Date.class), nullable(Date.class))).thenReturn(latitudeLongitude);

        //Execution
        List<IPLookUpDataDto> latLong = ipLookUpDateServiceImpl.getLatLong(search, event, ticketType, fromDate, toDate);

        //Assertion
        assertEquals(latLong.get(0).getLatitude(), ipLookUpData.getLatitude());
        assertEquals(latLong.get(0).getLongitude(), ipLookUpData.getLongitude());
    }

    @Test
    void test_getLatLong_passNullInParameter() {

        //mock
        when(ipLookDataRepository.getLatitudeLongitudeByDateRangeAndCityStateCountry(nullable(String.class), isA(Long.class), nullable(Date.class), nullable(Date.class), any(),any())).thenReturn(Collections.emptyList());

        //Execution
        List<IPLookUpDataDto> latLong = ipLookUpDateServiceImpl.getLatLong(null, event, null, null, null);

        //Assertion
        assertTrue(latLong.isEmpty());
    }

    @Test
    void test_getLatLong_passWrongSearchString() { //search string not contain any city, state or country name

        //mock
        when(ipLookDataRepository.getLatitudeLongitudeByDateRangeAndCityStateCountry(anyString(), anyLong(), any(), any(), any(),any())).thenReturn(Collections.emptyList());

        //Execution
        List<IPLookUpDataDto> latLong = ipLookUpDateServiceImpl.getLatLong("abcd", event, Collections.emptyList(), null, null);

        //Assertion
        assertTrue(latLong.isEmpty());
    }
}