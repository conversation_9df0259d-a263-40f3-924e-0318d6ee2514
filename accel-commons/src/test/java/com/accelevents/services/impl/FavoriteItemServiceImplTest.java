package com.accelevents.services.impl;

import com.accelevents.domain.FavoriteItem;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.FavoriteItemRepository;
import com.accelevents.services.FavoriteItemService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FavoriteItemServiceImplTest {

    @Spy
    @InjectMocks
    private FavoriteItemServiceImpl favoriteItemServiceiImpl;

    @Mock
    private FavoriteItemRepository favoriteItemRepository;

    @Mock
    private FavoriteItemService favoriteItemService;

    @Test
    public void testMarkAsFavItemWithItemIdAndUserId(){
        //SetUp
        Long itemId = 1L;
        User user = new User();
        user.setUserId(1L);

        FavoriteItem favoriteItem = new FavoriteItem();
        favoriteItem.setUserId(1L);
        favoriteItem.setItemId(1L);

        //Mock Data
        when(favoriteItemRepository.findFavoriteItemByItemIdAndUserId(itemId,user.getUserId())).thenReturn(Optional.empty());
        //Execute
        favoriteItemServiceiImpl.makeFavoriteItem(itemId,user);

        //Verify
        ArgumentCaptor<FavoriteItem> favCaptor = ArgumentCaptor.forClass(FavoriteItem.class);
        verify(favoriteItemRepository, Mockito.times(1)).save(favCaptor.capture());
        assertEquals(favoriteItem.getItemId(),favCaptor.getValue().getItemId());
        assertEquals(favoriteItem.getUserId(),favCaptor.getValue().getUserId());
    }

    @Test
    public  void testMarkAsFavItemNotAcceptableException(){
        //SetUp
        Long itemId = 1L;
        User user = new User();
        user.setUserId(1L);

        FavoriteItem favoriteItem = new FavoriteItem();
        favoriteItem.setUserId(1L);
        favoriteItem.setItemId(1L);

        //Mock Data
        when(favoriteItemRepository.findFavoriteItemByItemIdAndUserId(itemId,user.getUserId())).thenReturn(Optional.of(favoriteItem));

        //Execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> favoriteItemServiceiImpl.makeFavoriteItem(itemId,user));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_ALREADY_FAVOURITE.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void testGetFavItemCountByUserIdAndItemIdsInEmpty(){

        //SetUp
        User user = new User();
        user.setUserId(1L);

        //Execute
        BigInteger favItemCount = favoriteItemServiceiImpl.countFavoriteItemByUserIdAndModuleIdAndActive(user.getUserId(), 1L);

        //Verify
        assertEquals(BigInteger.ZERO,favItemCount);
    }

}
