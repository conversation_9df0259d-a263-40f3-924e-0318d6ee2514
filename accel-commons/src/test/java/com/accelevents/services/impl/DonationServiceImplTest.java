package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.DonationSmsCheckout;
import com.accelevents.auction.dto.TextToGiveGoalDto;
import com.accelevents.auction.dto.TextToGiveScrollViewData;
import com.accelevents.common.dto.DonationDisplaySetting;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.CalculateFees;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.messages.EnumDonationSMS;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.perfomance.dto.PerformanceGraphDetail;
import com.accelevents.repositories.DonationRepository;
import com.accelevents.repositories.RecurringEventsRepository;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.GeneralUtils;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DonationServiceImplTest {

    @InjectMocks
    @Spy
    private DonationServiceImpl donationServiceImpl;
    @Mock
    private TextMessageUtils mockTextMessageUtils;
    @Mock
    private DonationRepository mockDonationRepository;
    @Mock
    private DonationSettingsService mockDonationSettingsService;
    @Mock
    private EventChecklistService mockEventChecklistService;
    @Mock
    private StaffService mockStaffService;
    @Mock
    private UserService mockUserService;
    @Mock
    private StripeService mockStripeService;
    @Mock
    private RecurringEventsRepository recurringEventsRepository;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private RefundTransactionService refundTransactionService;
    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private AllPaymentService allPaymentService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private ServiceHelper serviceHelper;
    @Mock
    private PhoneNumberService phoneNumberService;
    @Mock
    private EventService eventService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private PaymentHandlerService paymentHandlerService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private StripePaymentService stripePaymentService;

	private Event event;
	private TwilioMessage twilioMessage;
	private User user;
	private MessageAction messageAction;
	private DonationSettings donationSettings;
	private DonationDisplaySetting donationDisplaySetting;
	private Donation donation;
	private StripeTransaction stripeTransaction;
    private EventChecklist eventChecklist;

    @BeforeEach
    void setUp(){
        MockitoAnnotations.openMocks(this);
        String body = "donate$20";
        event = new Event();
        event.setEventId(1L);
        event.setEventURL("testdonation");
        event.setTextToGiveEnabled(true);
        event.setCreditCardEnabled(true);
        event.setCurrency(Currency.USD);
        AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
        twilioMessage = new TwilioMessage(body, from, event);
        user = new User();
        messageAction = new MessageAction();
        messageAction.setAmount(20);

        eventChecklist = new EventChecklist();
        eventChecklist.setId(1L);


		donationSettings = new DonationSettings();
		donationSettings.setTextToGiveActivated(true);
		donationSettings.setHideDonationGoal(true);
		donationDisplaySetting = new DonationDisplaySetting(5L,5000d);


		donation = new Donation();
		donation.setId(1L);
		donation.setAmount(50d);
		donation.setPaidAmount(52.32);
		donation.setStripeFee(1.82);
		donation.setAeFee(0.50);
		donation.setHasPaid(true);
		donation.setEventId(event.getEventId());
		donation.setUser(user);
        donation.setTime(new Date(1699593940210L));

		stripeTransaction= new StripeTransaction();
		stripeTransaction.setId(1L);
	}

	@Test
	void testDonateWhenTextToGiveActivated() {
		String expectedMsg = "Success Donation Message";
		when(mockTextMessageUtils.getDonationSuccessfulMessage(twilioMessage.getEvent(), user , messageAction.getAmount())).thenReturn(expectedMsg);
        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);

		String returnedMsg = donationServiceImpl.donate(twilioMessage, messageAction, user);

		assertEquals(expectedMsg, returnedMsg, "Donation response message when Text to Give Activated");
		verify(mockTextMessageUtils).getDonationSuccessfulMessage(event, user, messageAction.getAmount());
	}

    @Test
    void test_Donate_throwInvalidFormatWholeNumberDonationMessageException() {
        String expectedMsg = EnumDonationSMS.INVALID_FORMAT_WHOLE_NUMBERS.getValue();

        doThrow(new RuntimeException()).when(mockTextMessageUtils).getDonationSuccessfulMessage(twilioMessage.getEvent(), user , messageAction.getAmount());
        when(mockTextMessageUtils.getInvalidFormatWholeNumberDonationMessage()).thenReturn(expectedMsg);
        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);

        String returnedMsg = donationServiceImpl.donate(twilioMessage, messageAction, user);

        assertEquals(expectedMsg, returnedMsg);
        verify(mockTextMessageUtils).getDonationSuccessfulMessage(event, user, messageAction.getAmount());
        verify(mockTextMessageUtils).getInvalidFormatWholeNumberDonationMessage();
    }

    @Test
    void testDonateWhenTextToGiveNotActivatedAndUserisStaff() {
        String expectedMsg = "Success Donation Message";
        donationSettings.setTextToGiveActivated(false);



        String returnedMsg = donationServiceImpl.donate(twilioMessage, messageAction, user);

        assertNull(returnedMsg);
    }

    @Test
    void test_Donate_throwExceptionPaymentProcessingIsNotSetup() {

        //setup
        String expectedMsg = "Success Donation Message";
        event.setCreditCardEnabled(false);
        twilioMessage.setEvent(event);
        donationSettings.setTextToGiveActivated(true);

        //mock


        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
        //Execution

        String errorMessage = donationServiceImpl.donate(twilioMessage, messageAction, user);

        assertEquals(NotAcceptableException.DonationExceptionMsg.PAYMENT_PROCESSING_IS_NOT_SETUP.getDeveloperMessage(), errorMessage);
    }

    @Test
    void testDonateReturnMsgWhenThrowException() {
        String returnMsg = "Please donate in whole numbers.";
        messageAction.setType("Type");
        messageAction.setItemCode("ItemCode");
        when(mockTextMessageUtils.getDonationSuccessfulMessage(twilioMessage.getEvent(), user , messageAction.getAmount())).thenThrow(NotAcceptableException.class);
        when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);
        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);

		String msg = donationServiceImpl.donate(twilioMessage, messageAction, user);

		assertNull(msg);
	}

	@Test
	void testTextToGiveNotActivatedAndUserIsNotStaffSendErrorMessage() {
		String expectedMsg = EnumDonationSMS.TEXT_TO_GIVE_NOT_ACTIVATED.getValue();
		DonationSettings donationSettings = new DonationSettings();
		donationSettings.setTextToGiveActivated(false);
		when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);
		when(mockTextMessageUtils.getTextToGiveModuleNotActivatedMessage()).thenReturn(expectedMsg);
        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);

		String msg = donationServiceImpl.donate(twilioMessage, messageAction, user);

		assertEquals("Text to Give is not currently active. Please contact the organizer.", expectedMsg, msg);
		verify(mockTextMessageUtils).getTextToGiveModuleNotActivatedMessage();
        verify(mockEventChecklistService).findByEvent(event);
        verify(mockEventChecklistService).save(eventChecklist);
	}

	@Test
	void testTextToGiveNotEnableAndUserIsNotStaffOrAdminSendErrorMessage() {
		String expectedMsg = "Text to Give is not yet enabled.";
		event.setTextToGiveEnabled(false);

		when(mockTextMessageUtils.getTextToGiveModuleNotEnabledMessage()).thenReturn(expectedMsg);

        when(mockEventChecklistService.findByEvent(event)).thenReturn(eventChecklist);

        String msg = donationServiceImpl.donate(twilioMessage, messageAction, user);

        assertEquals(expectedMsg, msg, "Text to Give is not yet enabled and user is not staff or admin.");
        verify(mockTextMessageUtils).getTextToGiveModuleNotEnabledMessage();
    }

    @Test
    void testTextToGiveNotEnableAndUserIsStaffOrAdminSendErrorMessage() {
        String expectedMsg = "Text to Give is not yet enabled.";
        event.setTextToGiveEnabled(false);



        String msg = donationServiceImpl.donate(twilioMessage, messageAction, user);

        assertEquals(null, msg);
    }

    @Test
    void testSave(){
        Donation donation = new Donation(user , event.getEventId(), 100d, null, null,
                true, false, 100d, "note", null, false);
        donationServiceImpl.save(donation);

        verify(mockDonationRepository).save(donation);
    }

    static Object[] getData(){
        return new Object[]{
                new Object[]{Constants.ACCOUNT_ACTIVATION_REQUIRED, 100d, false, "", "", "", ""},
                new Object[]{"**********", 200d, true, "jon", "kaz", "<EMAIL>", "sub_CjXwFwbej7nqLv"},
                new Object[]{Constants.ACCOUNT_ACTIVATION_REQUIRED, 100d, true, "", "", "", ""},
                new Object[]{"**********", 300d, false, "jon", "kaz", "<EMAIL>", "sub_CjXwFwbej7nqLv"},
                new Object[]{Constants.ACCOUNT_ACTIVATION_REQUIRED, null, true, "", "", "", ""},
        };
    }
    @ParameterizedTest
    @MethodSource("getData")
    void test_getPaidDonationForEvent(String phone, Double amount, boolean isRecurringDonationEnabled, String firstName, String lastName, String email, String subscriptionId){

        //setup
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setEmail(email);
        String phoneNumber = Constants.ACCOUNT_ACTIVATION_REQUIRED.equals(phone) ? Constants.STRING_UNKNOWN : phone;
        event.setGoalStartingAmount(100);
        donation.setUser(user);
        donation.setTime(DateUtils.getCurrentDate());
        donation.setAmount(amount);
        donation.setId(1L);
        donation.setRefunded(false);
        donation.setSubscriptionId(subscriptionId);

        PerformanceGraphDetail performanceGraphDetail = new PerformanceGraphDetail(donation.getTime(), donation.getAmount());
        List<PerformanceGraphDetail> performanceGraphDetails = new ArrayList<>();
        performanceGraphDetails.add(performanceGraphDetail);

        List<Donation> donationList = new ArrayList<>();
        donationList.add(donation);
        donationSettings.setEnableRecurringDonations(isRecurringDonationEnabled);

        List<Donation> confirmedDonationList = new ArrayList<>();
        confirmedDonationList.add(donation);
        int page = 0, size = 10;
        String searchString = null;
        String sortColumn = "id";
        boolean isAsc = false;

        //mock
        when(mockDonationRepository.getByEventIdAndHasPaidOrderByIdDesc(anyLong(), anyBoolean(), nullable(String.class))).thenReturn(donationList);
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);

        when(phoneNumberService.getDisplayNumber(any())).thenReturn(phone);


        //Execution
        DataTableResponse donationPerformanceWrapperData = donationServiceImpl.getPaidDonationForEvent(event, sortColumn, isAsc, searchString);

        //Assertion
        verify(mockDonationRepository).getByEventIdAndHasPaidOrderByIdDesc(anyLong(), anyBoolean(), nullable(String.class));
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(phoneNumberService).getDisplayNumber(any());

        assertNotNull(donationPerformanceWrapperData);
        DonationPerformance donationPerformance = (DonationPerformance) donationPerformanceWrapperData.getData().get(0);
        assertEquals(donationPerformance.getFirstName(), StringUtils.hasText(user.getFirstName()) ? user.getFirstName() : Constants.STRING_UNKNOWN);
        assertEquals(donationPerformance.getLastName(), StringUtils.hasText(user.getLastName()) ? user.getLastName() : Constants.STRING_UNKNOWN);
        assertEquals(donationPerformance.getEmail(), StringUtils.hasText(user.getEmail()) ? user.getEmail() : Constants.STRING_UNKNOWN);
        assertEquals(donationPerformance.getPhoneNumber(), phoneNumber);
        assertEquals(donationPerformance.getDonationAmount(), amount != null ? String.valueOf(amount) : Constants.STRING_UNKNOWN);
        assertEquals(donationPerformance.isRecurring(), isRecurringDonationEnabled && StringUtils.hasText(donation.getSubscriptionId()));
        assertEquals(donationPerformance.getId().longValue(), donation.getId());
        assertEquals(donationPerformance.isRefunded(), donation.getRefunded());
    }

    @Test
    void test_getEventIdsByPayoutStatus(){

        //setup
        EnumEventPayoutStatus enumEventPayoutStatus = EnumEventPayoutStatus.COMPLETED;
        List<EnumEventPayoutStatus> eventPayoutStatuses = new ArrayList<>();
        eventPayoutStatuses.add(enumEventPayoutStatus);

        //mock
        when(mockDonationRepository.getDistinctEventIdsByPayoutStatus(anyList())).thenReturn(Collections.singletonList(event.getEventId()));

        //Execution
        List<Long> eventIds = donationServiceImpl.getEventIdsByPayoutStatus(eventPayoutStatuses);

        //Assertion
        assertEquals(eventIds.get(0).longValue(), event.getEventId());
        verify(mockDonationRepository).getDistinctEventIdsByPayoutStatus(anyList());
    }

    @Test
    void test_findAllDonationForEvent(){

        //setup
        List<Donation> donationList = new ArrayList<>();
        donationList.add(donation);

        //mock
        when(mockDonationRepository.getByEventIdAndHasPaidOrderByIdDesc(anyLong(), anyBoolean())).thenReturn(donationList);

        //Execution
        List<Donation> donations = donationServiceImpl.findAllDonationForEvent(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getByEventIdAndHasPaidOrderByIdDesc(anyLong(), anyBoolean());

        assertEquals(donations.get(0).getId(), donation.getId());
        assertEquals(donations.get(0).getAmount(), donation.getAmount());
        assertEquals(donations.get(0).getAeFee(), donation.getAeFee());
        assertEquals(donations.get(0).getStripeFee(), donation.getStripeFee());
        assertEquals(donations.get(0).getEventId(), donation.getEventId());
        assertEquals(donations.get(0).getUser().getUserId(), donation.getUser().getUserId());
        assertEquals(donations.get(0).getPaidAmount(), donation.getPaidAmount());
        assertEquals(donations.get(0).isHasPaid(), donation.isHasPaid());
    }

    @Test
    void test_getDonations_withDonation(){

        //setup
        List<Donation> donations = new ArrayList<>();
        donations.add(donation);

        //mock
        when(mockDonationRepository.getByEventId(anyLong())).thenReturn(donations);

        //Execution
        List<DonationUser> donationUsers = donationServiceImpl.getDonations(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getByEventId(anyLong());

        assertEquals(donationUsers.get(0).getDonation().isHasPaid(), donation.isHasPaid());
        assertEquals(donationUsers.get(0).getUser().getUserId(), donation.getUser().getUserId());
    }

    @Test
    void test_getDonations_withoutDonation(){

        //mock
        when(mockDonationRepository.getByEventId(anyLong())).thenReturn(Collections.emptyList());

        //Execution
        List<DonationUser> donationUsers = donationServiceImpl.getDonations(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getByEventId(anyLong());

        assertTrue(donationUsers.isEmpty());
    }

    static Object[] getStripe(){
        Stripe stripe = new Stripe();
        stripe.setStripePublishableKey("pk_test_x9BXBdwFPlxaTKaWz1iv8Jzz");
        stripe.setProcessingFeesToPurchaser(true);
        return new Object[]{
                new Object[]{stripe},
                new Object[]{null}
        };
    }
    @ParameterizedTest
    @MethodSource("getStripe")
    void test_getDonation_withStripe(Stripe stripe){

        //setup
        donationSettings.setDonationAmountA(50);
        donationSettings.setDonationAmountB(100);
        donationSettings.setDonationAmountC(250);
        donationSettings.setDonationAmountD(500);

        Boolean isProcessingFeesToPurchaser= null != stripe && stripe.isProcessingFeesToPurchaser();

        StripeCreditCardDto stripeCreditCardDto = new StripeCreditCardDto();
        stripeCreditCardDto.setLast4("1111");
        stripeCreditCardDto.setCardType("VISA");
        stripeCreditCardDto.setId("1");

        EventUserInfoDto eventUserInfoDto = new EventUserInfoDto(user);

        //mock
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(eventService.getLinkedCreditCard(any(), any())).thenReturn(Collections.singletonList(stripeCreditCardDto));
        when(mockUserService.isCCRequired(any(), any(), any(), anyBoolean())).thenReturn(true);
        when(mockUserService.extractUserInfoForEvent(user, event, true,ModuleType.DONATION)).thenReturn(eventUserInfoDto);
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        DonationSmsCheckout smsCheckout = donationServiceImpl.getDonation(event, user);

        //Assertion
        verify(roStripeService).findByEvent(any());
        verify(eventService).getLinkedCreditCard(any(), any());
        verify(mockUserService).isCCRequired(any(), any(), any(), anyBoolean());
        verify(mockUserService).extractUserInfoForEvent(user, event, true, ModuleType.DONATION);
        verify(mockDonationSettingsService, times(2)).getByEventId(anyLong());

        assertEquals(smsCheckout.isProcessingFeesToPurchaser(), isProcessingFeesToPurchaser);
        assertEquals(smsCheckout.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
    }

    @Test
    void testUpdateUserByNewUser(){
        User newUser = new User();
        donationServiceImpl.updateUserByNewUser(user, newUser);

        verify(mockDonationRepository).updateByNewUser(user, newUser);
    }

    @Test
    void testFindAllPaidDonationForEvent(){
        List<Donation> donations = new ArrayList<>();
        when(mockDonationRepository.getByEventIdAndHasPaidAndIsRefunded(event.getEventId(), true, false)).thenReturn(donations);

        List<Donation> find = donationServiceImpl.findAllPaidDonationForEvent(event.getEventId());

        assertEquals(donations, find);
        verify(mockDonationRepository).getByEventIdAndHasPaidAndIsRefunded(anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void testGetPaidDonation(){
        User superAdminUser1 = new User();
        Event adminEvent = new Event();
        Donation donation = new Donation(superAdminUser1 , adminEvent.getEventId(), 100d, null, null,
                true, false, 100d, "testNote", null, false);
        when(mockDonationRepository.getByIdAndEventIdAndHasPaid(event.getEventId(), adminEvent.getEventId(), true)).thenReturn(donation);

        Donation find = donationServiceImpl.getPaidDonation(adminEvent, event.getEventId());

        assertEquals(donation, find);
        verify(mockDonationRepository).getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean());
    }

    @Test
    void testDisplayPageSettings(){
        //Prepare Data
        event.setGoalStartingAmount(50);
        donationDisplaySetting.setTotalFundRaised(120);
        //Mock data
        DonationSettings donationSettings = this.getDonationSettingsStub();
        when(stripeService.getByEventId(anyLong())).thenReturn(null);
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);
        when(donationServiceImpl.getNumberOfDonorsWithSumOfTotalPaidDonation(event)).thenReturn(donationDisplaySetting);

        //Run Test
        DonationDisplaySetting modulesDisplayPage = donationServiceImpl.getDisplayPageSettings(event);

        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(donationServiceImpl, times(2)).getNumberOfDonorsWithSumOfTotalPaidDonation(any());
        verify(stripeService).getByEventId(anyLong());

       assertEquals(modulesDisplayPage.getDonationAmounts().get(1).intValue(), donationSettings.getDonationAmountA());
       assertEquals(modulesDisplayPage.getDonationAmounts().get(0).intValue(), donationSettings.getDonationAmountB());
        assertEquals((int) modulesDisplayPage.getAeFeePercentage(), donationSettings.getFee());
       assertFalse(modulesDisplayPage.isAdsorbAEFees());
       assertEquals(modulesDisplayPage.getDonationPageDescription(), donationSettings.getDonationPageDescription());
       assertEquals(modulesDisplayPage.isTextToGiveActivated(), donationSettings.isTextToGiveActivated());
       assertEquals(modulesDisplayPage.getGoalAmount(), donationSettings.getDonationGoalAmount());
       assertEquals(modulesDisplayPage.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
       assertEquals(modulesDisplayPage.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
       assertEquals(modulesDisplayPage.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
       assertEquals(modulesDisplayPage.isHideDonationGoal(), donationSettings.isHideDonationGoal());
    }

    private DonationSettings getDonationSettingsStub(){
        DonationSettings donationSettings = new DonationSettings();
        donationSettings.setDonationAmountA(10);
        donationSettings.setDonationAmountB(5);
        donationSettings.setDonationAmountC(50);
        donationSettings.setDonationAmountD(25);
        donationSettings.setFee(3);
        donationSettings.setDonationPageDescription("Test Desc");
        donationSettings.setDonationGoalAmount(5000);
        return donationSettings;
    }

    static Object[] isProcessingFeesToPurchaser(){
        return new Object[]{
                new Object[]{true},
                new Object[]{false}
        };
    }
    @ParameterizedTest
    @MethodSource("isProcessingFeesToPurchaser")
    void test_getDisplayPageSettings(Boolean isProcessingFeesToPurchaser){

        //setup
        event.setGoalStartingAmount(100);
        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(isProcessingFeesToPurchaser);
        donationSettings.setDonationAmountA(50);
        donationSettings.setDonationAmountB(100);
        donationSettings.setDonationAmountC(250);
        donationSettings.setDonationAmountD(500);
        donationSettings.setFee(1);
        donationSettings.setDonationPageDescription("<p>Test Description</p>");
        donationSettings.setTextToGiveActivated(true);
        donationSettings.setDonationGoalAmount(1000);
        donationSettings.setEnableRecurringDonations(false);
        donationSettings.setDonationViewBackgroundImage("cd6585ea-eec1-4b9b-9a98-bc55897de732");
        donationSettings.setCustomThermometerImage("dc5d039b-d1b6-4d13-948d-c3ed5924e821");
        donationSettings.setHideDonationGoal(false);

        //mock
        when(stripeService.getByEventId(anyLong())).thenReturn(stripe);
        when(mockDonationRepository.getNumberOfDonorsWithSumOfTotalPaidDonation(anyLong(), anyBoolean(), anyBoolean())).thenReturn(donationDisplaySetting);
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);

        //Execution
        DonationDisplaySetting donationDisplaySettingData = donationServiceImpl.getDisplayPageSettings(event);

        //Assertion
        verify(stripeService).getByEventId(anyLong());
        verify(mockDonationRepository).getNumberOfDonorsWithSumOfTotalPaidDonation(anyLong(), anyBoolean(), anyBoolean());

        assertEquals(donationDisplaySettingData.getAeFeePercentage().intValue(), donationSettings.getFee());
    }

    @Test
    void testDonationRefundThrowDonationNotFoundException() throws StripeException, ApiException {
        //setUp
        long donationId=1L;
        doReturn(null).when(donationServiceImpl).getPaidDonation(event, donationId);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.refund(donationId,event,new Date(),user));

        //Assertion
        verify(donationServiceImpl).getPaidDonation(event, donationId);
        assertEquals(NotAcceptableException.DonationExceptionMsg.DONATION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void testDonationRefundThrowDonationAlreadyRefundedException() {
        //setUp
        donation.setRefunded(true);
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event, donation.getId());
        
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.refund(donation.getId(),event,new Date(),user));

        //Assertion
        verify(donationServiceImpl).getPaidDonation(event, donation.getId());
        assertEquals(NotAcceptableException.DonationExceptionMsg.DONATION_ALREADY_REFUNDED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void testDonationRefundThrowDonationRefundFailedException() throws StripeException, ApiException {
        //setUp
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event,donation.getId());
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId())).thenReturn(null);
        
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.refund(donation.getId(),event,new Date(),user));

        //Assertion
        assertEquals(NotAcceptableException.DonationExceptionMsg.DONATION_REFUND_FAILED.getDeveloperMessage(), exception.getMessage());
        verify(donationServiceImpl).getPaidDonation(any(), anyLong());
        verify(stripeTransactionService).findBySourceAndSourceId(any(), anyLong());
    }

    @Test
    void testDonationCashRefundSuccess_withStripeTransactionNull() throws StripeException, ApiException {
        //setUp
        donation.setSource(BiddingSource.STAFF);
        Date clientDate=new Date();
        //Mock
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event,donation.getId());
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId())).thenReturn(null);
        // verify exception
        //Execution
        donationServiceImpl.refund(donation.getId(),event,clientDate,user);

        //Assertion
        verify(donationServiceImpl, times(1)).getPaidDonation(event, donation.getId());
        verify(stripeTransactionService).findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId());

        ArgumentCaptor<RefundTransaction> argumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService).save(argumentCaptor.capture());

        RefundTransaction refundTransaction = argumentCaptor.getValue();

        assertEquals(StripeTransactionSource.STAFF_DONATION_REFUND, refundTransaction.getSource());
        assertNull(refundTransaction.getStripeTransaction());
        assertEquals("SUCCESS", refundTransaction.getStatus());
        assertEquals(RefundTransaction.RefundType.CASH,refundTransaction.getRefundType());
        assertEquals(donation.getAmount(),refundTransaction.getRefundAmount());
        assertEquals(0,refundTransaction.getRefundedApplicationFeeAmount());
        assertEquals(clientDate,refundTransaction.getCreatedDate());

        ArgumentCaptor<Donation> argumentCaptorForDonation = ArgumentCaptor.forClass(Donation.class);
        verify(donationServiceImpl).save(argumentCaptorForDonation.capture());

        Donation captorDonation = argumentCaptorForDonation.getValue();
        assertTrue(captorDonation.getRefunded());
        assertEquals(EnumEventPayoutStatus.COMPLETED,captorDonation.getEventPayoutStatus());

	}

	@Test
	void testDonationCCRefundSuccessWithPaymentGatewaySQUAREAndProcessingFeesToPurchaserTrue() throws StripeException, ApiException {
		//setUp
		donation.setSubscriptionId("subscription_id");
		Date clientDate=new Date();
		Stripe stripe = new Stripe();
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		RefundInfoDto refundInfoDto = new RefundInfoDto();
		refundInfoDto.setId("refund_id");
		refundInfoDto.setRefundedApplicationFee((long) 0.50);
		Map<String, String> metadata = new HashMap<>();
		metadata.put("donationid", String.valueOf(donation.getId()));

		//Mock
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event,donation.getId());
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);
		when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId())).thenReturn(stripeTransaction);
		when(roStripeService.findByEvent(event)).thenReturn(stripe);
		when(allPaymentService.createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
				,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode())).thenReturn(refundInfoDto);
		//doNothing().when(clearAPIGatewayCache).clearDonationCache(any(Donation.class));
        reset(sendGridMailPrepareService);
		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(),
                anyDouble(), anyBoolean(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),anyString());
		//Execution
		donationServiceImpl.refund(donation.getId(),event,clientDate,user);

		//Assertion
        verify(donationServiceImpl, times(1)).getPaidDonation(event, donation.getId());
        verify(stripeTransactionService).findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId());
        verify(roStripeService).findByEvent(event);
        verify(allPaymentService).createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
                ,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(),
                anyDouble(), anyBoolean(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),anyString());
		ArgumentCaptor<RefundTransaction> argumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
		verify(refundTransactionService).save(argumentCaptor.capture());

		RefundTransaction refundTransaction = argumentCaptor.getValue();

		assertEquals(StripeTransactionSource.DONATION_ONLINE, refundTransaction.getSource());
        assertNull(refundTransaction.getStripeTransaction());
		assertEquals(RefundTransaction.RefundType.SQUARE,refundTransaction.getRefundType());
		assertEquals(donation.getAmount(),refundTransaction.getRefundAmount());
		assertEquals(0,refundTransaction.getRefundedApplicationFeeAmount());
		assertEquals(clientDate,refundTransaction.getCreatedDate());

		ArgumentCaptor<Donation> argumentCaptorForDonation = ArgumentCaptor.forClass(Donation.class);
		verify(donationServiceImpl).save(argumentCaptorForDonation.capture());

		Donation captorDonation = argumentCaptorForDonation.getValue();
		assertTrue(captorDonation.getRefunded());
		assertEquals(EnumEventPayoutStatus.COMPLETED,captorDonation.getEventPayoutStatus());

	}

	static Object[] getPaymentGatewayAndProcessingFeesToPurchaser(){
        return new Object[]{
          new Object[]{EnumPaymentGateway.SQUARE.value(), false},
          new Object[]{EnumPaymentGateway.STRIPE.value(), true},
        };
    }

    @ParameterizedTest
    @MethodSource("getPaymentGatewayAndProcessingFeesToPurchaser")
    void testDonationCCRefundSuccess_WithPaymentGatewayAndProcessingFeesToPurchaser(String paymentGateWay, Boolean isProcessingFeesToPurchaser) throws StripeException, ApiException {
        //setUp
        donation.setSubscriptionId("subscription_id");
        Date clientDate=new Date();
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(paymentGateWay);
        stripe.setProcessingFeesToPurchaser(isProcessingFeesToPurchaser);
        RefundInfoDto refundInfoDto = new RefundInfoDto();
        refundInfoDto.setId("refund_id");
        refundInfoDto.setRefundedApplicationFee((long) 0.50);
        Map<String, String> metadata = new HashMap<>();
        metadata.put("donationid", String.valueOf(donation.getId()));

        //Mock
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event,donation.getId());
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId())).thenReturn(stripeTransaction);
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(allPaymentService.createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
                ,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode())).thenReturn(refundInfoDto);
        doNothing().when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(),
                anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        //Execution
        donationServiceImpl.refund(donation.getId(),event,clientDate,user);

        //Assertion
        verify(donationServiceImpl, times(1)).getPaidDonation(event, donation.getId());
        verify(stripeTransactionService).findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId());
        verify(roStripeService).findByEvent(event);
        verify(allPaymentService).createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
                ,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(),
                anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));
        ArgumentCaptor<RefundTransaction> argumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService).save(argumentCaptor.capture());

        RefundTransaction refundTransaction = argumentCaptor.getValue();

        assertEquals(StripeTransactionSource.DONATION_ONLINE, refundTransaction.getSource());
        assertNull(refundTransaction.getStripeTransaction());
        assertEquals(refundTransaction.getRefundType(), RefundTransaction.RefundType.valueOf(GeneralUtils.getRefundType(stripe.getPaymentGateway())));
        assertEquals(donation.getAmount(),refundTransaction.getRefundAmount());
        assertEquals(0,refundTransaction.getRefundedApplicationFeeAmount());
        assertEquals(clientDate,refundTransaction.getCreatedDate());

        ArgumentCaptor<Donation> argumentCaptorForDonation = ArgumentCaptor.forClass(Donation.class);
        verify(donationServiceImpl).save(argumentCaptorForDonation.capture());

        Donation captorDonation = argumentCaptorForDonation.getValue();
        assertTrue(captorDonation.getRefunded());
        assertEquals(EnumEventPayoutStatus.COMPLETED,captorDonation.getEventPayoutStatus());

    }

    @Test
    void testDonationCCRefundSuccessThrowExceptionSendBuyerReceiptDonation() throws StripeException, ApiException {
        //setUp
        donation.setSubscriptionId("");
        Date clientDate=new Date();
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setProcessingFeesToPurchaser(true);
        RefundInfoDto refundInfoDto = new RefundInfoDto();
        refundInfoDto.setId("refund_id");
        refundInfoDto.setRefundedApplicationFee((long) 0.50);
        Map<String, String> metadata = new HashMap<>();
        metadata.put("donationid", String.valueOf(donation.getId()));

        //Mock
        doReturn(donation).when(donationServiceImpl).getPaidDonation(event,donation.getId());
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId())).thenReturn(stripeTransaction);
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(allPaymentService.createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
                ,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode())).thenReturn(refundInfoDto);
        doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(),
                anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));
        when(mockDonationSettingsService.getByEventId(event.getEventId())).thenReturn(donationSettings);

        //Execution
        donationServiceImpl.refund(donation.getId(),event,clientDate,user);

        //Assertion
        verify(donationServiceImpl, times(1)).getPaidDonation(event, donation.getId());
        verify(stripeTransactionService).findBySourceAndSourceId(StripeTransactionSource.DONATION_ONLINE, donation.getId());
        verify(roStripeService).findByEvent(event);
        verify(allPaymentService).createRefund(stripeTransaction,stripe,donation.getAmount(),metadata
                ,Constants.REQUESTED_BY_CUSTOMER,false,event.getCurrency().getISOCode());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(),
                anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));
        ArgumentCaptor<RefundTransaction> argumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService).save(argumentCaptor.capture());

        RefundTransaction refundTransaction = argumentCaptor.getValue();

        assertEquals(StripeTransactionSource.DONATION_ONLINE, refundTransaction.getSource());
        assertNull(refundTransaction.getStripeTransaction());
        assertEquals(RefundTransaction.RefundType.STRIPE,refundTransaction.getRefundType());
        assertEquals(donation.getAmount(),refundTransaction.getRefundAmount());
        assertEquals(0,refundTransaction.getRefundedApplicationFeeAmount());
        assertEquals(clientDate,refundTransaction.getCreatedDate());

        ArgumentCaptor<Donation> argumentCaptorForDonation = ArgumentCaptor.forClass(Donation.class);
        verify(donationServiceImpl).save(argumentCaptorForDonation.capture());

        Donation captorDonation = argumentCaptorForDonation.getValue();
        assertTrue(captorDonation.getRefunded());
        assertEquals(EnumEventPayoutStatus.COMPLETED,captorDonation.getEventPayoutStatus());

    }

    @Test
    void test_refund_withDonationAmountZero() throws StripeException, ApiException {

        //setup
        donation.setAmount(0d);
        donation.setRefunded(false);

        //mock
        when(mockDonationRepository.getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean())).thenReturn(donation);

        //Execution
        donationServiceImpl.refund(donation.getId(), event, DateUtils.getCurrentDate(),user);

        //Assertion
        verify(mockDonationRepository).getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean());

        ArgumentCaptor<Donation> donationArgumentCaptor = ArgumentCaptor.forClass(Donation.class);
        verify(mockDonationRepository).save(donationArgumentCaptor.capture());

        Donation donationData = donationArgumentCaptor.getValue();
        assertTrue(donationData.getRefunded());
        assertEquals(donationData.getEventPayoutStatus(), EnumEventPayoutStatus.COMPLETED);
    }

    @Test
    void getDonationTimeAndPaidAmountForGraph(){

        //setup
        Long eventId = event.getEventId();
        Date donateATime = DateUtils.getCurrentDate();
        Double paidAmount = 100d;
        DonationAmountAndTimeDto donationAmountAndTimeDto = new DonationAmountAndTimeDto(donateATime, paidAmount);

        //mock
        when(mockDonationRepository.getDonationTimeAndPaidAmountByEventId(anyLong())).thenReturn(Collections.singletonList(donationAmountAndTimeDto));

        //Execution
        List<DonationAmountAndTimeDto> donationAmountAndTimeDtos = donationServiceImpl.getDonationTimeAndPaidAmountForGraph(eventId);

        //Assert
        verify(mockDonationRepository).getDonationTimeAndPaidAmountByEventId(anyLong());

        assertEquals(donationAmountAndTimeDtos.get(0).getPaidAmount(), donationAmountAndTimeDto.getPaidAmount());
        assertEquals(donationAmountAndTimeDtos.get(0).getTime().toString(), donationAmountAndTimeDto.getTime().toString());

        verify(mockDonationRepository).getDonationTimeAndPaidAmountByEventId(anyLong());
    }

    @Test
    void test_getDonationTimeAndPaidAmountForGraph(){

        //setup
        DonationAmountAndTimeDto donationAmountAndTimeDto = new DonationAmountAndTimeDto();
        donationAmountAndTimeDto.setPaidAmount(100d);
        donationAmountAndTimeDto.setTime(DateUtils.getCurrentDate());

        List<DonationAmountAndTimeDto> donationAmountAndTimeDtoList = new ArrayList<>();
        donationAmountAndTimeDtoList.add(donationAmountAndTimeDto);

        //mock
        when(mockDonationRepository.getDonationTimeAndPaidAmountByEventId(anyLong())).thenReturn(donationAmountAndTimeDtoList);

        //Execution
        List<DonationAmountAndTimeDto> donationAmountAndTimeDtos = donationServiceImpl.getDonationTimeAndPaidAmountForGraph(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getDonationTimeAndPaidAmountByEventId(anyLong());

        assertEquals(donationAmountAndTimeDtos.get(0).getTime(), donationAmountAndTimeDto.getTime());
        assertEquals(donationAmountAndTimeDtos.get(0).getPaidAmount(), donationAmountAndTimeDto.getPaidAmount());
    }

    @Test
    void test_updateDonations(){

        //setup
        List<Long> donations = new ArrayList<>();
        donations.add(donation.getId());

        EnumEventPayoutStatus eventPayoutStatus = EnumEventPayoutStatus.COMPLETED;

        //mock
        Mockito.doNothing().when(mockDonationRepository).updateDonations(anyList(), any());

        //Execution
        donationServiceImpl.updateDonations(donations, eventPayoutStatus);

        //Assertion
        verify(mockDonationRepository).updateDonations(anyList(), any());
    }

    @Test
    void test_getDonationIdsByEventId(){

        //setup
        DonationNetSaleDto donationNetSaleDto = new DonationNetSaleDto(1L, 1000d);
        List<DonationNetSaleDto> donationNetSaleDtoList = new ArrayList<>();
        donationNetSaleDtoList.add(donationNetSaleDto);

        //mock
        when(mockDonationRepository.getDonationIdsByEventId(anyLong())).thenReturn(donationNetSaleDtoList);

        //Execution
        List<DonationNetSaleDto> donationNetSaleDtos = donationServiceImpl.getDonationIdsByEventId(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getDonationIdsByEventId(anyLong());

        assertEquals(donationNetSaleDtos.get(0).getId(), donationNetSaleDto.getId());
        assertEquals(donationNetSaleDtos.get(0).getNetSale(), donationNetSaleDto.getNetSale());
    }

    static Object[] getNetSale(){

        return new Object[]{
                new Object[]{null},
                new Object[]{BigDecimal.TEN}
        };
    }
    @ParameterizedTest
    @MethodSource("getNetSale")
    void test_getNetSale(BigDecimal netsale){

        //setup
        Double netSale = netsale!=null ? netsale.doubleValue() : 0d;

        //mock
        when(mockDonationRepository.getNetSale(anyLong())).thenReturn(netsale);

        //Execution
        Double netSAle = donationServiceImpl.getNetSale(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getNetSale(anyLong());

        assertTrue(netSAle.doubleValue() == netSale);
    }

    @Test
    void test_getTicketingSalesDataForDashboard(){

        //setup
        DonationHomeDto donationHomeDto = new DonationHomeDto();
        donationHomeDto.setNumberOfDonors(10L);
        donationHomeDto.setTotalDonation(1000d);

        //mock
        when(mockDonationRepository.getTicketingSalesDataForDashboard(anyLong())).thenReturn(donationHomeDto);

        //Execution
        DonationHomeDto donationHomeDtoData = donationServiceImpl.getTicketingSalesDataForDashboard(event.getEventId());

        //Assertion
        verify(mockDonationRepository).getTicketingSalesDataForDashboard(anyLong());

        assertSame(donationHomeDtoData.getTotalDonation(), donationHomeDto.getTotalDonation());
        assertEquals(donationHomeDtoData.getNumberOfDonors(), donationHomeDto.getNumberOfDonors());
    }

    @Test
    void test_getDonationsByEventAndNeonDonationIdIsNull(){

        //mock
        when(mockDonationRepository.findByEventIdAndNeonDonationIdIsNull(anyLong())).thenReturn(Collections.singletonList(donation));

        //Execution
        List<Donation> donations = donationServiceImpl.getDonationsByEventAndNeonDonationIdIsNull(event.getEventId());

        //Assertion
        verify(mockDonationRepository).findByEventIdAndNeonDonationIdIsNull(anyLong());

        assertEquals(donations.get(0).isHasPaid(), donation.isHasPaid());
        assertEquals(donations.get(0).getUser().getUserId(), donation.getUser().getUserId());
        assertEquals(donations.get(0).getEventId(), donation.getEventId());
        assertEquals(donations.get(0).getAeFee(), donation.getAeFee());
        assertSame(donations.get(0).getAmount(), donation.getAmount());
    }

    @Test
    void test_getDonationBySubscriptionId(){

        //setup
        String subscriptionId = "sub_CjXwFwbej7nqLv";
        donation.setSubscriptionId(subscriptionId);

        //mock
        when(mockDonationRepository.findBySubscriptionIdAndEventId(anyString(), anyLong())).thenReturn(donation);

        //Execution
        Donation donationData = donationServiceImpl.getDonationBySubscriptionId(event, subscriptionId);

        //Assertion
        verify(mockDonationRepository).findBySubscriptionIdAndEventId(anyString(), anyLong());

        assertEquals(donationData.getSubscriptionId(), donation.getSubscriptionId());
        assertEquals(donationData.isHasPaid(), donation.isHasPaid());
        assertEquals(donationData.getUser().getUserId(), donation.getUser().getUserId());
        assertEquals(donationData.getEventId(), donation.getEventId());
        assertEquals(donationData.getAeFee(), donation.getAeFee());
        assertSame(donationData.getAmount(), donation.getAmount());
    }

    static Object[] getLastNameFirstName(){
        return new Object[]{
                new Object[]{"",""},
                new Object[]{"jon","kaz"},
                new Object[]{"jon",""},
                new Object[]{"","kaz"}
        };
    }
    @ParameterizedTest
    @MethodSource("getLastNameFirstName")
    void test_getScrollData(String firstName, String lastName){

        //setup
        user.setFirstName(firstName);
        user.setLastName(lastName);
        donation.setNote("donation note");
        donation.setSubscriptionId("sub_CjXwFwbej7nqLv");
        donation.setUser(user);

        donationDisplaySetting.setNumberOfDonors(100);

        donationSettings.setShowDonorName(true);
        donationSettings.setDonationGoalAmount(1000d);
        donationSettings.setEnableRecurringDonations(true);
        donationSettings.setHideDonationGoal(true);


        String userName = StringUtils.isEmpty(user.getFirstName()) && StringUtils.isEmpty(user.getLastName()) ? Constants.STRING_UNKNOWN : user.getFirstName() + Constants.STRING_BLANK + user.getLastName();
        Boolean hideTotalFundRaised = true;
        //mock
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(mockDonationRepository.getNumberOfDonorsWithSumOfTotalPaidDonation(anyLong(), anyBoolean(), anyBoolean())).thenReturn(donationDisplaySetting);
        when(mockDonationRepository.getByEventIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Collections.singletonList(donation));
        when(eventDesignDetailService.isTotalFundRaisedShow(any())).thenReturn(true);

        //Execution
        TextToGiveScrollViewData textToGiveScrollViewData = donationServiceImpl.getScrollData(event);

        //Assertion
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(mockDonationRepository).getNumberOfDonorsWithSumOfTotalPaidDonation(anyLong(), anyBoolean(), anyBoolean());
        verify(mockDonationRepository).getByEventIdAndIsRefunded(anyLong(), anyBoolean());
        verify(eventDesignDetailService).isTotalFundRaisedShow(any());

        assertEquals(textToGiveScrollViewData.getCurrencySymbol(), event.getCurrency().getSymbol());
        assertEquals(textToGiveScrollViewData.getItems().get(0).getDonorName(), userName);
        assertEquals(textToGiveScrollViewData.getTotalFundRaised(), BigDecimal.valueOf(donationDisplaySetting.getTotalFundRaised()));
        assertEquals(textToGiveScrollViewData.getGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(textToGiveScrollViewData.getNumberOfDonors(), donationDisplaySetting.getNumberOfDonors());
        assertEquals(textToGiveScrollViewData.isRecurringEnabled(), donationSettings.isEnableRecurringDonations());
        assertEquals(textToGiveScrollViewData.isShowTotalFundRaised(), donationSettings.isShowDonorName());
        assertTrue(textToGiveScrollViewData.isShowTotalFundRaised());
        assertEquals(textToGiveScrollViewData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
    }

    @Test
    void test_getSumOfTotalPaidDonation_withConfirmedDonationListEmpty(){

        //setup
        event.setGoalStartingAmount(100);

        //mock
        when(mockDonationRepository.getByEventIdAndHasPaidAndIsRefunded(anyLong(), anyBoolean(), anyBoolean())).thenReturn(null);

        //Execution
        double sumOfTotalPaidDonation = donationServiceImpl.getSumOfTotalPaidDonation(event);

        //Assertion
        verify(mockDonationRepository).getByEventIdAndHasPaidAndIsRefunded(anyLong(), anyBoolean(), anyBoolean());

        assertEquals(sumOfTotalPaidDonation, event.getGoalStartingAmount());
    }

    @Test
    void test_getSumOfTotalPaidDonation_withConfirmedDonationList(){

        //setup
        event.setGoalStartingAmount(100);

        List<Donation> confirmedDonationList = new ArrayList<>();
        confirmedDonationList.add(donation);

        //mock
        when(mockDonationRepository.getByEventIdAndHasPaidAndIsRefunded(anyLong(), anyBoolean(), anyBoolean())).thenReturn(confirmedDonationList);

        //Execution
        double sumOfTotalPaidDonation = donationServiceImpl.getSumOfTotalPaidDonation(event);

        //Assertion
        verify(mockDonationRepository).getByEventIdAndHasPaidAndIsRefunded(anyLong(), anyBoolean(), anyBoolean());

        assertEquals(sumOfTotalPaidDonation, donation.getAmount() + event.getGoalStartingAmount());
    }

    static Object[] getSubscriptionIdAndIsEnableRecurringDonations(){
        return  new Object[]{
                new Object[]{true, "sub_CjXwFwbej7nqLv"},
                new Object[]{true, null},
                new Object[]{false, "sub_CjXwFwbej7nqLv"},
                new Object[]{false, null}
        };
    }
    @ParameterizedTest
    @MethodSource("getSubscriptionIdAndIsEnableRecurringDonations")
    void test_getTextToGiveGoal_withEnableScrollOnGoalPageTrue(Boolean isEnableRecurringDonations, String subscriptionId){

        //setup
        user.setFirstName("jon");
        user.setLastName("kaz");
        donation.setUser(user);
        donation.setNote("donation note");
        event.setGoalStartingAmount(100);
        donationSettings.setDonationGoalAmount(1000);
        donationSettings.setEnableScrollOnGoalPage(true);
        donationSettings.setShowDonorName(true);
        donationSettings.setDonationViewBackgroundImage("cb41f20c-0734-4878-b798-9530a4a546c4_jellyfish.jpg");
        donationSettings.setCustomThermometerImage("dc5d039b-d1b6-4d13-948d-c3ed5924e821");
        donationSettings.setEnableRecurringDonations(isEnableRecurringDonations);
        donationSettings.setHideDonationGoal(false);
        donation.setSubscriptionId(subscriptionId);
        boolean isTotalFundRaisedShow = true;
        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setViewScrollSpeed(300L);

        //mock
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        Mockito.doReturn(donationDisplaySetting).when(donationServiceImpl).getNumberOfDonorsWithSumOfTotalPaidDonation(any());
        when(eventDesignDetailService.isTotalFundRaisedShow(any())).thenReturn(true);
        when(mockDonationRepository.getByEventIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Collections.singletonList(donation));

        //Execution
        TextToGiveGoalDto textToGiveGoalDtoData = donationServiceImpl.getTextToGiveGoal(event);

        //Assertion
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(eventDesignDetailService).findByEvent(any());
        verify(donationServiceImpl).getNumberOfDonorsWithSumOfTotalPaidDonation(any());
        verify(eventDesignDetailService).isTotalFundRaisedShow(any());
        verify(mockDonationRepository).getByEventIdAndIsRefunded(anyLong(), anyBoolean());

        assertEquals(textToGiveGoalDtoData.getGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(textToGiveGoalDtoData.getTotalRaised(), BigDecimal.valueOf(donationDisplaySetting.getTotalFundRaised()));
        assertTrue(textToGiveGoalDtoData.isShowTotalFundRaised());
        assertEquals(textToGiveGoalDtoData.getNumberOfDonors(), donationDisplaySetting.getNumberOfDonors());
        assertTrue(textToGiveGoalDtoData.isEnableScroll());
        assertEquals(textToGiveGoalDtoData.getItems().get(0).getDonorName(), user.getFirstName() + Constants.STRING_BLANK + user.getLastName());
        assertEquals(textToGiveGoalDtoData.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(textToGiveGoalDtoData.getViewScrollSpeed(), eventDesignDetail.getViewScrollSpeed());
        assertEquals(textToGiveGoalDtoData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(textToGiveGoalDtoData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(textToGiveGoalDtoData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
    }

    @Test
    void test_getTextToGiveGoal_withEnableScrollOnGoalPageFalse(){

        //setup
        event.setGoalStartingAmount(100);
        donationSettings.setDonationGoalAmount(1000);
        donationSettings.setEnableScrollOnGoalPage(false);
        donationSettings.setShowDonorName(false);
        donationSettings.setDonationViewBackgroundImage("cb41f20c-0734-4878-b798-9530a4a546c4_jellyfish.jpg");
        donationSettings.setCustomThermometerImage("dc5d039b-d1b6-4d13-948d-c3ed5924e821");
        donationSettings.setEnableRecurringDonations(false);
        donation.setSubscriptionId("subscriptionId");
        boolean isTotalFundRaisedShow = true;
        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setViewScrollSpeed(300L);

        //mock
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        doReturn(donationDisplaySetting).when(donationServiceImpl).getNumberOfDonorsWithSumOfTotalPaidDonation(any());
        when(eventDesignDetailService.isTotalFundRaisedShow(any())).thenReturn(true);

        //Execution
        TextToGiveGoalDto textToGiveGoalDtoData = donationServiceImpl.getTextToGiveGoal(event);

        //Assertion
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(eventDesignDetailService).findByEvent(any());
        verify(donationServiceImpl).getNumberOfDonorsWithSumOfTotalPaidDonation(any());
        verify(eventDesignDetailService).isTotalFundRaisedShow(any());

        assertEquals(textToGiveGoalDtoData.getGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(textToGiveGoalDtoData.getTotalRaised(), BigDecimal.valueOf(donationDisplaySetting.getTotalFundRaised()));
        assertTrue(textToGiveGoalDtoData.isShowTotalFundRaised());
        assertEquals(textToGiveGoalDtoData.getNumberOfDonors(), donationDisplaySetting.getNumberOfDonors());
        assertEquals(textToGiveGoalDtoData.getViewScrollSpeed(), eventDesignDetail.getViewScrollSpeed());
        assertEquals(textToGiveGoalDtoData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(textToGiveGoalDtoData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
    }

    @Test
    void test_resendBuyerReceipt(){

        //mock
        when(mockDonationRepository.getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.resendBuyerReceipt(donation.getId(), event));
        
        assertEquals(NotAcceptableException.DonationExceptionMsg.DONATION_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(mockDonationRepository).getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean());
    }

    static Object[] getDonationData(){
        return new Object[]{
          new Object[]{0d, 103d},
          new Object[]{0d, 100d},
          new Object[]{2.9d, 103d},
          new Object[]{2.9d, 100d}
        };
    }
    @ParameterizedTest
    @MethodSource("getDonationData")
    void test_resendBuyerReceipt_withDifferentStripefeeAndStripePaidAmount(double stripeFee, double stripePaidAmount){

        //setup
        donation.setAmount(100d);
        donation.setStripeFee(stripeFee);
        donation.setStripePaidAmount(stripePaidAmount);
        donation.setAeFee(1d);

        //mock
        when(mockDonationRepository.getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean())).thenReturn(donation);
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        doNothing().when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class), nullable(String.class));

        //Execution
        donationServiceImpl.resendBuyerReceipt(donation.getId(), event);

        //Assertion
        verify(mockDonationRepository).getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class), nullable(String.class));
    }

    @Test
    void test_resendBuyerReceipt_throwExceptionWhileSendMail(){

        //setup
        donation.setAmount(100d);
        donation.setStripeFee(2.9d);
        donation.setStripePaidAmount(103d);
        donation.setAeFee(1d);

        //mock
        when(mockDonationRepository.getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean())).thenReturn(donation);
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        //Execution
        donationServiceImpl.resendBuyerReceipt(donation.getId(), event);

        //Assertion
        verify(mockDonationRepository).getByIdAndEventIdAndHasPaid(anyLong(), anyLong(), anyBoolean());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));
    }

    @Test
    void test_donation_withPaymentTypeNotCCOrCash() throws StripeException, ApiException {

        //setup
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = "CHEQUE";
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = true;

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, true));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PAYMENT.getDeveloperMessage(), exception.getMessage());        
    }

    @Test
    void test_donation_withPaymentTypeCCAndCreditCardDisabled() throws StripeException, ApiException {

        //setup
        event.setCreditCardEnabled(false);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = Constants.CC;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = true;

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, true));
        
        assertEquals(NotAcceptableException.DonationExceptionMsg.PAYMENT_PROCESSING_IS_NOT_SETUP.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_donation_withPaymentTypeCCAndCreditCardEnabled() throws StripeException, ApiException {

        //setup
        donationSettings.setTextToGiveActivated(false);
        event.setCreditCardEnabled(false);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = Constants.CASH;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = true;

        //mock
        doNothing().when(mockUserService).saveName(anyString(), anyString(), any());
        doNothing().when(mockUserService).saveAddress(any(), any());
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(roStaffService.isEventStaffOrAdmin(any(), any())).thenReturn(false);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, true));

        //Assertion
        assertEquals(NotAcceptableException.DonationExceptionMsg.DONATION_NOT_ACTIVE.getDeveloperMessage(), exception.getMessage());
        verify(mockUserService).saveName(anyString(), anyString(), any());
        verify(mockUserService).saveAddress(any(), any());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(roStaffService).isEventStaffOrAdmin(any(), any());
    }

    @Test
    void test_donation_withPaymentTypeCashAndCreditCardDisabled() throws StripeException, ApiException {

        //setup
        donationSettings.setTextToGiveActivated(false);
        event.setCreditCardEnabled(false);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = Constants.CASH;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = false;

        //mock
        doNothing().when(mockUserService).saveName(anyString(), anyString(), any());
        doNothing().when(mockUserService).saveAddress(any(), any());
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, false));
        
        assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CREDIT_CARD_PROCESSING_NOT_ENABLE.getDeveloperMessage(), exception.getMessage());
        verify(mockUserService).saveName(anyString(), anyString(), any());
        verify(mockUserService).saveAddress(any(), any());
        verify(mockDonationSettingsService).getByEventId(anyLong());
    }

    static Object[] getProcessingFeesToPurchaserAndPaymentType(){
        return new Object[]{
          new Object[]{true, null},
          new Object[]{false, ""}
        };
    }
    @ParameterizedTest
    @MethodSource("getProcessingFeesToPurchaserAndPaymentType")
    void test_donation_withPaymentTypeNullAndCreditCardEnabled(Boolean isProcessingFeesToPurchaser, String donationPaymentType) throws StripeException, ApiException {

        //setup
        double paidAmount = 100d;
        donationSettings.setTextToGiveActivated(false);
        donationSettings.setFee(1);
        event.setCreditCardEnabled(true);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        donationPurchaseDto.setAmount(paidAmount);
        donationPurchaseDto.setNote("donation note");
        donationPurchaseDto.setRecurringDonation(false);
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = donationPaymentType;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = false;
        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(isProcessingFeesToPurchaser);
        stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        double applicationFee = (donationPurchaseDto.getAmount() * donationSettings.getFee()) / 100;
        double chargeAmount = donationPurchaseDto.getAmount();
        if (stripe.isProcessingFeesToPurchaser()) {
            chargeAmount += applicationFee;
        }
        double stripeFee = CalculateFees.getStripeFee(paidAmount,chargeAmount,stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);

        //mock
        doNothing().when(mockUserService).saveName(anyString(), anyString(), any());
        doNothing().when(mockUserService).saveAddress(any(), any());
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(paymentHandlerService.handlePaymentForDonation(any(), any(), any(), any(), any(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(paidAmount);
        doNothing().when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        //Execution
        donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, false);

        //Assertion
        verify(mockUserService).saveName(anyString(), anyString(), any());
        verify(mockUserService).saveAddress(any(), any());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(roStripeService).findByEvent(any());
        verify(paymentHandlerService).handlePaymentForDonation(any(), any(), any(), any(), any(), anyBoolean(), any(), anyDouble(), anyDouble());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        ArgumentCaptor<Donation> donationArgumentCaptor = ArgumentCaptor.forClass(Donation.class);
        verify(mockDonationRepository, times(2)).save(donationArgumentCaptor.capture());

        Donation donationData = donationArgumentCaptor.getValue();
        assertEquals(donationData.getUser().getUserId(), user.getUserId());
        assertEquals(donationData.getEventId().longValue(), event.getEventId());
        assertEquals(donationData.getAmount(), paidAmount);
        assertEquals(donationData.getSource(), biddingSource);
        assertEquals(donationData.getTime().toString(), new Date().toString());
        assertTrue(donationData.isConfirmed());
        assertTrue(donationData.isHasPaid());
        assertEquals(donationData.getPaidAmount(), paidAmount);
        assertEquals(donationData.getNote(), donationPurchaseDto.getNote());
        assertEquals(donationData.getStaffUserId().getUserId(), staffUser.getUserId());
        assertFalse(donationData.getRefunded());
        assertEquals(donationData.getAeFee(), applicationFee);
        assertEquals(donationData.getStripePaidAmount(), paidAmount);
        assertEquals(donationData.getStripeFee(), stripeFee);

    }

    @Test
    void test_donation_withPaymentTypeCashAndCreditCardEnabled() throws StripeException, ApiException {

        //setup
        double paidAmount = 100d;
        donationSettings.setTextToGiveActivated(false);
        donationSettings.setFee(1);
        event.setCreditCardEnabled(true);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        donationPurchaseDto.setAmount(paidAmount);
        donationPurchaseDto.setNote("donation note");
        donationPurchaseDto.setRecurringDonation(false);
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = Constants.CASH;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = true;
        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(false);
        stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);

        //mock
        doNothing().when(mockUserService).saveName(anyString(), anyString(), any());
        doNothing().when(mockUserService).saveAddress(any(), any());
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(roStaffService.isEventStaffOrAdmin(any(), any())).thenReturn(true);
        doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        //Execution
        donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, true);

        //Assertion
        verify(mockUserService).saveName(anyString(), anyString(), any());
        verify(mockUserService).saveAddress(any(), any());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(roStaffService).isEventStaffOrAdmin(any(), any());
//        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(any(), any(), anyDouble(), anyDouble(), anyBoolean(), anyBoolean(),anyString(),any(),anyString());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        ArgumentCaptor<Donation> donationArgumentCaptor = ArgumentCaptor.forClass(Donation.class);
        verify(mockDonationRepository).save(donationArgumentCaptor.capture());

        Donation donationData = donationArgumentCaptor.getValue();
        assertEquals(donationData.getUser().getUserId(), user.getUserId());
        assertEquals(donationData.getEventId().longValue(), event.getEventId());
        assertEquals(donationData.getAmount(), paidAmount);
        assertEquals(donationData.getSource(), biddingSource);
        assertTrue(donationData.isConfirmed());
        assertTrue(donationData.isHasPaid());
        assertEquals(donationData.getPaidAmount(), paidAmount);
        assertEquals(donationData.getNote(), donationPurchaseDto.getNote());
        assertEquals(donationData.getStaffUserId().getUserId(), staffUser.getUserId());
        assertFalse(donationData.getRefunded());
        assertEquals(0, donationData.getAeFee());

    }

    @Test
    void test_donation_withTextToGiveActivatedTrue() throws StripeException, ApiException {

        //setup
        double paidAmount = 100d;
        donationSettings.setTextToGiveActivated(true);
        donationSettings.setFee(1);
        event.setCreditCardEnabled(true);
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setFirstName("jon");
        donationPurchaseDto.setLastName("kaz");
        donationPurchaseDto.setAmount(paidAmount);
        donationPurchaseDto.setNote("donation note");
        donationPurchaseDto.setRecurringDonation(false);
        BiddingSource biddingSource = BiddingSource.ONLINE;
        String paymentType = Constants.CC;
        User staffUser = new User();
        staffUser.setFirstName("vikas");
        boolean isSMSTextToGive = true;
        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(false);
        stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        double applicationFee = (donationPurchaseDto.getAmount() * donationSettings.getFee()) / 100;
        double chargeAmount = donationPurchaseDto.getAmount();
        if (stripe.isProcessingFeesToPurchaser()) {
            chargeAmount += applicationFee;
        }

        double stripeFee = CalculateFees.getStripeFee(paidAmount,chargeAmount,stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);

        //mock
        doNothing().when(mockUserService).saveName(anyString(), anyString(), any());
        doNothing().when(mockUserService).saveAddress(any(), any());
        when(mockDonationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
        when(roStaffService.isEventStaffOrAdmin(any(), any())).thenReturn(true);
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(paymentHandlerService.handlePaymentForDonation(any(), any(), any(), any(), any(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(paidAmount);
        doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        //Execution
        donationServiceImpl.donation(user, event, donationPurchaseDto, biddingSource, paymentType, staffUser, true);

        //Assertion
        verify(mockUserService).saveName(anyString(), anyString(), any());
        verify(mockUserService).saveAddress(any(), any());
        verify(mockDonationSettingsService).getByEventId(anyLong());
        verify(roStaffService).isEventStaffOrAdmin(any(), any());
        verify(roStripeService).findByEvent(any());
        verify(paymentHandlerService).handlePaymentForDonation(any(), any(), any(), any(), any(), anyBoolean(), any(), anyDouble(), anyDouble());
        verify(sendGridMailPrepareService).sendBuyerReceiptDonation(isA(User.class), isA(Event.class),anyDouble(), anyDouble(), anyBoolean(), anyBoolean(), nullable(String.class), isA(Date.class),nullable(String.class));

        ArgumentCaptor<Donation> donationArgumentCaptor = ArgumentCaptor.forClass(Donation.class);
        verify(mockDonationRepository, times(2)).save(donationArgumentCaptor.capture());

        Donation donationData = donationArgumentCaptor.getValue();
        assertEquals(donationData.getUser().getUserId(), user.getUserId());
        assertEquals(donationData.getEventId().longValue(), event.getEventId());
        assertEquals(donationData.getAmount(), paidAmount);
        assertEquals(donationData.getSource(), biddingSource);
        /*assertEquals(donationData.getTime().toString(), new Date().toString());*/
        assertTrue(donationData.isConfirmed());
        assertTrue(donationData.isHasPaid());
        assertEquals(donationData.getPaidAmount(), paidAmount);
        assertEquals(donationData.getNote(), donationPurchaseDto.getNote());
        assertEquals(donationData.getStaffUserId().getUserId(), staffUser.getUserId());
        assertFalse(donationData.getRefunded());
        assertEquals(donationData.getAeFee(), applicationFee);
        assertEquals(donationData.getStripePaidAmount(), paidAmount);
        assertEquals(donationData.getStripeFee(), stripeFee);
    }
}
