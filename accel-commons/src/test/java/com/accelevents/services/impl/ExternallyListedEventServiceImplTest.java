package com.accelevents.services.impl;

import com.accelevents.domain.ExternallyListedEvent;
import com.accelevents.domain.User;
import com.accelevents.domain.WhiteLabel;
import com.accelevents.domain.enums.EventType;
import com.accelevents.dto.ExternallyListedEventsDto;
import com.accelevents.repositories.ExternalEventRepository;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import static com.accelevents.utils.Constants.LOCAL_DATE_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class ExternallyListedEventServiceImplTest {

    @Spy
    @InjectMocks
    private ExternallyListedEventServiceImpl externallyListedEventServiceImpl = new ExternallyListedEventServiceImpl();

    @Mock
    private ExternalEventRepository externalEventRepository;

	private ExternallyListedEventsDto externallyListedEventsDto;
    private User user;
    private WhiteLabel whiteLabel;

    private String eventName = "new year eve";
    private Date eventStartDate = DateUtils.getFormattedDate("2019/12/31 19:00", LOCAL_DATE_FORMAT);
	private Date eventEndDate = DateUtils.getFormattedDate("2020/01/01 00:00", LOCAL_DATE_FORMAT);
	private String eventLogo = "EveNight2019.jpg";
    private String eventDescription = "Last night of this year";
    private EventType eventType = EventType.BUSINESS_AND_PROFESSIONAL;
    private BigDecimal latitude = BigDecimal.valueOf(39.806992);
    private BigDecimal longitude = BigDecimal.valueOf(-76.278809);
    private String eventTicketingLink = "https://www.stagingaccel.com/e/u/checkout/NewYearEvents0/tickets/order";

	@BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);
        user = EventDataUtil.getUser();
        user.setEmail("<EMAIL>");
		String eventContactEmailAddress = "newYearEveAdmin@gmail";
		externallyListedEventsDto = new ExternallyListedEventsDto(eventName, "2019/12/31 19:00",  "2020/01/01 00:00", eventLogo, eventDescription, eventType, latitude, longitude, eventTicketingLink);
        whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);
    }

    @Test
    void test_save() {

        //setup
		ExternallyListedEvent externallyListedEvent1 = new ExternallyListedEvent(externallyListedEventsDto, user, whiteLabel);

        //Execution
        externallyListedEventServiceImpl.save(externallyListedEvent1);

        //Assertion
        ArgumentCaptor<ExternallyListedEvent> externallyListedEventArgumentCaptor = ArgumentCaptor.forClass(ExternallyListedEvent.class);
        verify(externalEventRepository).save(externallyListedEventArgumentCaptor.capture());

        ExternallyListedEvent externallyListedEvent = externallyListedEventArgumentCaptor.getValue();
        assertEquals(externallyListedEvent.getUserId().getUserId(), user.getUserId());
        assertEquals(externallyListedEvent.getWhiteLabelId().getId(), whiteLabel.getId());
        assertEquals(externallyListedEvent.getEventName(), eventName);
        assertEquals(externallyListedEvent.getEventStartDate().toString(), eventStartDate.toString());
        assertEquals(externallyListedEvent.getEventEndDate().toString(), eventEndDate.toString());
        assertEquals(externallyListedEvent.getEventLogo(), eventLogo);
        assertEquals(externallyListedEvent.getEventDescription(), eventDescription);
        assertEquals(externallyListedEvent.getEventType(), eventType);
        assertEquals(externallyListedEvent.getLatitude(), latitude);
        assertEquals(externallyListedEvent.getLongitude(), longitude);
        assertEquals(externallyListedEvent.getEventTicketingLink(), eventTicketingLink);
        assertEquals(externallyListedEvent.getEventContactEmailAddress(), user.getEmail());
    }

    @Test
    void test_saveExternallyListedEventsInDb() {

        //Execution
        externallyListedEventServiceImpl.saveExternallyListedEventsInDb(externallyListedEventsDto, user, Optional.of(whiteLabel));

        //Assertion
        ArgumentCaptor<ExternallyListedEvent> externallyListedEventArgumentCaptor = ArgumentCaptor.forClass(ExternallyListedEvent.class);
        verify(externalEventRepository).save(externallyListedEventArgumentCaptor.capture());

        ExternallyListedEvent externallyListedEvent = externallyListedEventArgumentCaptor.getValue();
        assertEquals(externallyListedEvent.getUserId().getUserId(), user.getUserId());
        assertEquals(externallyListedEvent.getWhiteLabelId().getId(), whiteLabel.getId());
        assertEquals(externallyListedEvent.getEventName(), externallyListedEventsDto.getEventName());
        assertEquals(externallyListedEvent.getEventStartDate().toString(), DateUtils.getFormattedDate(externallyListedEventsDto.getStartDate(), LOCAL_DATE_FORMAT).toString());
        assertEquals(externallyListedEvent.getEventEndDate().toString(), DateUtils.getFormattedDate(externallyListedEventsDto.getEndDate(), LOCAL_DATE_FORMAT).toString());
        assertEquals(externallyListedEvent.getEventLogo(), externallyListedEventsDto.getEventLogo());
        assertEquals(externallyListedEvent.getEventDescription(), externallyListedEventsDto.getEventDescription());
        assertEquals(externallyListedEvent.getEventType(), externallyListedEventsDto.getEventType());
        assertEquals(externallyListedEvent.getLatitude(), externallyListedEventsDto.getLatitude());
        assertEquals(externallyListedEvent.getLongitude(), externallyListedEventsDto.getLongitude());
        assertEquals(externallyListedEvent.getEventTicketingLink(), externallyListedEventsDto.getEventTicketingLink());
        assertEquals(externallyListedEvent.getEventContactEmailAddress(), user.getEmail());
    }

    @Test
    void test_saveExternallyListedEventsInDb_withWhitelabelNull() {

        //Execution
        externallyListedEventServiceImpl.saveExternallyListedEventsInDb(externallyListedEventsDto, user, Optional.empty());

        //Assertion
        ArgumentCaptor<ExternallyListedEvent> externallyListedEventArgumentCaptor = ArgumentCaptor.forClass(ExternallyListedEvent.class);
        verify(externalEventRepository).save(externallyListedEventArgumentCaptor.capture());

        ExternallyListedEvent externallyListedEvent = externallyListedEventArgumentCaptor.getValue();
        assertEquals(externallyListedEvent.getUserId().getUserId(), user.getUserId());
        assertNull(externallyListedEvent.getWhiteLabelId());
        assertEquals(externallyListedEvent.getEventName(), externallyListedEventsDto.getEventName());
        assertEquals(externallyListedEvent.getEventStartDate().toString(), DateUtils.getFormattedDate(externallyListedEventsDto.getStartDate(), LOCAL_DATE_FORMAT).toString());
        assertEquals(externallyListedEvent.getEventEndDate().toString(), DateUtils.getFormattedDate(externallyListedEventsDto.getEndDate(), LOCAL_DATE_FORMAT).toString());
        assertEquals(externallyListedEvent.getEventLogo(), externallyListedEventsDto.getEventLogo());
        assertEquals(externallyListedEvent.getEventDescription(), externallyListedEventsDto.getEventDescription());
        assertEquals(externallyListedEvent.getEventType(), externallyListedEventsDto.getEventType());
        assertEquals(externallyListedEvent.getLatitude(), externallyListedEventsDto.getLatitude());
        assertEquals(externallyListedEvent.getLongitude(), externallyListedEventsDto.getLongitude());
        assertEquals(externallyListedEvent.getEventTicketingLink(), externallyListedEventsDto.getEventTicketingLink());
        assertEquals(externallyListedEvent.getEventContactEmailAddress(), user.getEmail());
    }
}