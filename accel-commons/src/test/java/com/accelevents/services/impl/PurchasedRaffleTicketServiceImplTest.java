package com.accelevents.services.impl;

import com.accelevents.domain.PurchasedRaffleTicket;
import com.accelevents.domain.User;
import com.accelevents.repositories.PurchaseRaffleTicketRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PurchasedRaffleTicketServiceImplTest {

	@Mock
	PurchaseRaffleTicketRepository purchaseRaffleTicketRepository;
	
	@InjectMocks
	@Spy
	PurchasedRaffleTicketServiceImpl purchasedRaffleTicketServiceImpl;
	
	@Test
	public void testIsComplimentaryTicketUsed() {
		
		User user = new User();
		user.setUserId(1L);
		user.setFirstName("user");
		user.setEmail("<EMAIL>");
		user.setFirstName("Normal");
		user.setLastName("User");
		user.setMostRecentEventId(1);
		
		long raffleId = 1;
		
		PurchasedRaffleTicket value = new PurchasedRaffleTicket();
		when(purchaseRaffleTicketRepository.findByUserAndRaffleIdAndIncludesCompTicket(user, raffleId, true)).thenReturn(value);
		boolean result = purchasedRaffleTicketServiceImpl.isComplimentaryTicketUsed(user, raffleId);
		assertTrue(result);
		
		when(purchaseRaffleTicketRepository.findByUserAndRaffleIdAndIncludesCompTicket(user, raffleId, true)).thenReturn(null);
		boolean resultFalse = purchasedRaffleTicketServiceImpl.isComplimentaryTicketUsed(user, raffleId);
		assertFalse(resultFalse);
	}
}
