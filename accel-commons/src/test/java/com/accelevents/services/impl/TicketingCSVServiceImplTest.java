//package com.accelevents.services.impl;
//
//import com.accelevents.domain.*;
//import com.accelevents.domain.enums.*;
//import com.accelevents.dto.AttributeKeyValueDto;
//import com.accelevents.dto.StripeDTO;
//import com.accelevents.dto.TicketAttributeValueDto;
//import com.accelevents.dto.ValueDto;
//import com.accelevents.messages.TicketType;
//import com.accelevents.services.*;
//import com.accelevents.services.repo.helper.EventCommonRepoService;
//import com.accelevents.services.repo.helper.EventTicketsRepoService;
//import com.accelevents.session_speakers.repo.UserSessionRepo;
//import com.accelevents.ticketing.dto.TicketingModuleDTO;
//import com.accelevents.utils.Constants;
//import com.accelevents.dto.TicketHolderHelper;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.rules.ExpectedException;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.Spy;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import javax.xml.bind.JAXBException;
//import javax.xml.bind.Unmarshaller;
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.doReturn;
//import static org.mockito.Mockito.when;
//

//@ExtendWith(MockitoExtension.class)
//public class TicketingCSVServiceImplTest {
//
//    @Spy
//    @InjectMocks
//    private TicketingCSVServiceImpl ticketingCSVServiceImpl = new TicketingCSVServiceImpl();
//
//    @Mock
//    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
//
//    @Mock
//    private EventTicketsRepoService eventTicketsRepoService;
//
//    @Mock
//    private EventCommonRepoService eventCommonRepoService;
//
//    @Mock
//    private TicketingOrderManagerService ticketingOrderManagerService;
//
//    @Mock
//    private TicketingHelperService ticketingHelperService;
//
//    @Mock
//    private StripeService stripeService;
//
//    @Mock
//    private RecurringEventsScheduleService recurringEventsScheduleService;
//
//    @Mock
//    private AttendeeSequenceNumberService attendeeSequenceNumberService;
//
//    @Mock
//    private EventDesignDetailService eventDesignDetailService;
//
//    @Mock
//    private UserSessionRepo userSessionRepo;
//
//    private Event event;
//    private User user;
//    private Ticketing ticketing;
//    private EventTickets eventTickets, eventTickets1;
//    private TicketingOrder ticketingOrder, ticketingOrder1;
//    private TicketHolderAttributes ticketHolderAttributes, ticketHolderAttributes1;
//    private TicketAttributeValueDto ticketAttributeValueDto;
//    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes1;
//	private TicketHolderRequiredAttributes ticketHolderRequiredAttributes2;
//	private TicketHolderRequiredAttributes ticketHolderRequiredAttributes3;
//	private TicketHolderRequiredAttributes ticketHolderRequiredAttributes4;
//	private AttributeKeyValueDto holderAttributeKeyValueDtoQuestion;
//	private StripeDTO stripeDTO;
//    private ValueDto valueDtoHolder;
//	private Unmarshaller unmarshaller;
//    private TicketingCoupon ticketingCoupon;
//    private TicketingTable ticketingTable;
//    private TrackingLinks trackingLinks;
//    private TicketingOrderManager ticketingOrderManager;
//    private TicketingType ticketingType;
//    private TicketHolderHelper ticketHolderHelper;
//    private EventDesignDetail eventDesignDetail;
//    private AttendeeSequenceNumber attendeeSequenceNumber;
//    //private TicketStatus ticketStatus, ticketStatus1;
//
//    private Long id = 1L;
//    private Long recurringEventId = 1L;
//	private String email = "<EMAIL>";
//	private double price = 100d;
//    private Long phoneNumber = 4844554242L;
//	private String date = "2019/04/04 05:30";
//    private Date orderDate = new Date(date);
//    private String couponCode = "Discount50";
//    private  Long totalTicketsInOrder = 10L;
//    private Long tableNoSequence = 1L;
//	private double refundedAmount = 10d;
//    private TicketingModuleDTO ticketingModuleDTO;
//
//    @BeforeEach
//    public void setUp() throws Exception {
//
//        event = EventDataUtil.getEvent();
//        eventTickets = EventDataUtil.getEventTickets();
//        ticketingOrder = EventDataUtil.getTicketingOrder();
//        user = EventDataUtil.getUser();
//        ticketing = EventDataUtil.getTicketing(event);
//        ticketAttributeValueDto =  getTicketAttributeValueDTO();
//
//        ticketHolderAttributes = new TicketHolderAttributes();
//        ticketHolderAttributes.setId(id);
//        ticketHolderAttributes.setValue(EventDataUtil.getSampleXML());
//        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue());
//
//        stripeDTO = new StripeDTO();
//		double ccPercentageFee = 2.9d;
//		stripeDTO.setCCPercentageFee(ccPercentageFee);
//		double ccFlat = 0.3d;
//		stripeDTO.setCCFlatFee(ccFlat);
//
//        trackingLinks = new TrackingLinks();
//        trackingLinks.setLinkUrl("TestEvent");
//
//        ticketingOrderManager = new TicketingOrderManager();
//
//        ticketingType = new TicketingType();
//
//        ticketingCoupon = new TicketingCoupon();
//        ticketingCoupon.setName(couponCode);
//		double amount = 50d;
//		ticketingCoupon.setAmount(amount);
//        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
//        eventDesignDetail = EventDataUtil.getEventDesignDetail(event);
//        attendeeSequenceNumber=EventDataUtil.getAttendeeSequenceNumber(eventTickets);
//
//        ticketingModuleDTO = new TicketingModuleDTO();
//
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_success() throws JAXBException {
//
//        //setup
//        ticketingOrder = getOrder();
//
//        user.setPhoneNumber(phoneNumber);
//
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets = getEventTicket();
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingType.setPassfeetobuyer(true);
//
//        ticketingOrderManager.setTicketType(ticketingType);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//        ticketingOrderManagerList.add(ticketingOrderManager);
//
//        Map<TicketingOrder, List<TicketingOrderManager>> ticketingOrderManagersMap = new HashMap<>();
//        ticketingOrderManagersMap.put(ticketingOrder,ticketingOrderManagerList);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//
//        //mock
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
//        when(eventCommonRepoService.findByEventIdAndTicketingStatusJoinFetch(event, Arrays.asList(TicketingOrder.TicketingOrderStatus.PAID,
//                TicketingOrder.TicketingOrderStatus.PAID_DELETE), recurringEventId, DataType.TICKET)).thenReturn(eventTicketsList);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingOrderManagerService.getAllOrdersByOrderId(anyList())).thenReturn(ticketingOrderManagersMap);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    private TicketingOrder getOrder() {
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//        return ticketingOrder;
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_successWithIsRecurringFalseAndIsEnableAutoAssignedSequenceFalseAndIsPassfeetobuyerFalse() throws JAXBException {
//
//        //setup
//        ticketingModuleDTO.setRecurringEvent(false);
//
//        ticketingOrder = getOrder();
//
//        user.setPhoneNumber(phoneNumber);
//
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets = getEventTicket();
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingType.setPassfeetobuyer(false);
//        ticketingOrderManager.setTicketType(ticketingType);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//        ticketingOrderManagerList.add(ticketingOrderManager);
//
//        Map<TicketingOrder, List<TicketingOrderManager>> ticketingOrderManagersMap = new HashMap<>();
//        ticketingOrderManagersMap.put(ticketingOrder,ticketingOrderManagerList);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdAndTicketingStatusJoinFetch(event, Arrays.asList(TicketingOrder.TicketingOrderStatus.PAID,
//                TicketingOrder.TicketingOrderStatus.PAID_DELETE), recurringEventId, DataType.TICKET)).thenReturn(eventTicketsList);
//        when(ticketingOrderManagerService.getAllOrdersByOrderId(anyList())).thenReturn(ticketingOrderManagersMap);
//        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(false);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    private EventTickets getEventTicket() {
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setTicketingOrder(ticketingOrder);
//        return eventTickets;
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_success_with_ticketHolderRequiredAttributes_null() throws JAXBException {
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_success_with_ticketHolderRequiredAttributes_empty() throws JAXBException {
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(Collections.emptyList());
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_success_with_discountType_PERCENTAGE() throws JAXBException {
//
//        //setup
//
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//
//        user.setPhoneNumber(phoneNumber);
//
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setTicketingOrder(ticketingOrder);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.EMAIL);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes2.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//        ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes3.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes3.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes3.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
//
//        ticketHolderRequiredAttributes4 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes4.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes4.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes4.setAttributeValueType(AttributeValueType.IMAGE);
//        ticketHolderRequiredAttributes4.setAttribute(true);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes4);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getAllAttributesOrderByAttributeOrder(any())).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdAndTicketingStatusJoinFetch(any(), anyList(), anyLong(), any())).thenReturn(eventTicketsList);
//        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
//        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(any())).thenReturn(attendeeSequenceNumber);
//        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketBuyerCSVDataForEvent_success1_with_discountType_PERCENTAGE() throws JAXBException {
//
//        //setup
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//
//        user.setPhoneNumber(phoneNumber);
//
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setTicketingOrder(ticketingOrder);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.EMAIL);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes2.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//        ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes3.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes3.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes3.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
//
//        ticketHolderRequiredAttributes4 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes4.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes4.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes4.setAttributeValueType(AttributeValueType.TEXT);
//        ticketHolderRequiredAttributes4.setAttribute(true);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes4);
//
//        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getAllAttributesOrderByAttributeOrder(event)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdAndTicketingStatusJoinFetch(event, Arrays.asList(TicketingOrder.TicketingOrderStatus.PAID,
//                TicketingOrder.TicketingOrderStatus.PAID_DELETE), recurringEventId, DataType.TICKET)).thenReturn(eventTicketsList);
//        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
//        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketBuyerCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_ticketHolderAttributes_null() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_ticketHolderAttributesvalueBlank() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(new TicketHolderAttributes());
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_ticketHolderRequiredAttributesList_empty() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketingTypeId(ticketingType);
//        eventTickets.setTicketStatus(TicketStatus.REFUNDED);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingCoupon.setEventTicketTypeId("1");
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//
//        ticketingModuleDTO.setRecurringEvent(true);
//
//        //mock
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, null,eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    //@Test
//    public void test_setBuyerDataByTicketingType_success_with_ticketAttributeValueDtoNull() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        //doReturn(null).when(ticketingCSVServiceImpl).unmashler(any(),any());
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_eventTicketStatus_Delete_ticketStatus_TICKETING_STATUS_CHECKED_IN() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setRecurringEventId(null);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.IMAGE);
//        ticketHolderRequiredAttributes2.setName(Constants.IMAGE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//
//        attendeeSequenceNumber.setNumber(null);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmarshler(any(),any());
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//        when(ticketingOrderManagerService.getNumberOfDiscountedTicket(any(),any())).thenReturn(1);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_TicketingCouponPerTicketAndTicketingCouponDiscountTypeFlat() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setRecurringEventId(null);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
//        ticketingCoupon.setDiscountType(DiscountType.FLAT);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//
//        attendeeSequenceNumber.setNumber(null);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//        when(ticketingOrderManagerService.getNumberOfDiscountedTicket(any(),any())).thenReturn(1);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_userPhoneNumber_zero() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setPhoneNumber(0L);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.LAST_NAME);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_attributeValueType_IMAGE() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setPhoneNumber(0L);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.LAST_NAME);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.IMAGE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
//        holderAttributeKeyValueDtoQuestion.setValue("kazarian");
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
//        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);
//
//        valueDtoHolder = new ValueDto();
//        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);
//
//        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
//        ticketAttributeValueDto.setPurchaser(valueDtoHolder);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(),stripeDTO );
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_userPhoneNumber() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setPhoneNumber(9898989898L);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_eventTicketStatus_Delete_ticketStatus_() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.REFUNDED);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_couponCode() throws JAXBException{
//
//        //setup
//
//        ticketingCoupon.setName(couponCode);
//        ticketingCoupon.setDiscountType(DiscountType.FLAT);
//        ticketingCoupon.setAmount(200d);
//        ticketingCoupon.setEventTicketTypeId("12,123,12,121");
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setCountryCode(CountryCode.US);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setNote("Note");
//
//        EventTickets eventTickets = new EventTickets();
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus("");
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setId(id);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setHolderPhoneNumber(9898989898L);
//        eventTickets.setHolderCountryCode("US");
//        eventTickets.setHolderEmail(Constants.STRING_EMAIL_SPACE);
//        eventTickets.setHolderFirstName(Constants.STRING_FIRST_SPACE_NAME);
//        eventTickets.setTicketingOrder(EventDataUtil.getTicketingOrder());
//        eventTickets.setHolderLastName(Constants.STRING_LAST_SPACE_NAME);
//        eventTickets.setTicketingTypeId(EventDataUtil.getTicketingType(EventDataUtil.getEvent()));
//        eventTickets.setPaidAmount(50d);
//        eventTickets.setRefundedAmount(10d);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_recurringIdAndDateMap_null() throws JAXBException{
//
//        //setup
//
//        ticketingCoupon.setName(couponCode);
//        ticketingCoupon.setDiscountType(DiscountType.FLAT);
//        ticketingCoupon.setAmount(200d);
//        ticketingCoupon.setEventTicketTypeId("12,123,12,121");
//
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setCountryCode(CountryCode.US);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setNote("Note");
//
//        EventTickets eventTickets = new EventTickets();
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus("");
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setId(id);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setHolderPhoneNumber(9898989898L);
//        eventTickets.setHolderCountryCode("US");
//        eventTickets.setHolderEmail(Constants.STRING_EMAIL_SPACE);
//        eventTickets.setHolderFirstName(Constants.STRING_FIRST_SPACE_NAME);
//        eventTickets.setTicketingOrder(EventDataUtil.getTicketingOrder());
//        eventTickets.setHolderLastName(Constants.STRING_LAST_SPACE_NAME);
//        eventTickets.setTicketingTypeId(EventDataUtil.getTicketingType(EventDataUtil.getEvent()));
//        eventTickets.setPaidAmount(50d);
//        eventTickets.setRefundedAmount(10d);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//        when(ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(any(),any())).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, null, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_success_with_eventTicketStatus_Refunded_ticketStatus_() throws JAXBException{
//
//        //setup
//        unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//
//        user.setPhoneNumber(0L);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.REFUNDED);
//        eventTickets.setTicketStatus(TicketStatus.REFUNDED);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(stripeService.getStripeFeesByEvent(ticketingOrder.getEventid())).thenReturn(stripeDTO);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, unmarshaller, ticketingOrder, eventTicketsList, totalTicketsInOrder,
//                ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(), stripeDTO);
//    }
//
//    @Test
//    public void test_getUnmashler_success() throws JAXBException{
//
//        //Execution
//        Unmarshaller unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//        assertNotNull(unmarshaller);
//    }
//
//    @Test
//    public void test_getCellNumber_success_with_ticketHolderCountryCode_empty() {
//
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(phoneNumber);
//
//        //Execution
//        String cellNumber = ticketingCSVServiceImpl.getCellNumber(eventTickets1, true);
//        assertEquals(cellNumber, eventTickets1.getHolderPhoneNumber().toString());
//    }
//
//    @Test
//    public void test_getCellNumber_success_with_ticketHolderCountryCode() {
//
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(phoneNumber);
//        eventTickets1.setHolderCountryCode(CountryCode.US.toString());
//
//        String returnValue = " +" + CountryCode
//                .valueOf(StringUtils
//                        .upperCase(eventTickets1.getHolderCountryCode().substring(0, 2))).getIntCode() + "-" + eventTickets1.getHolderPhoneNumber();
//
//        //Execution
//        String cellNumber = ticketingCSVServiceImpl.getCellNumber(eventTickets1, true);
//        assertEquals(cellNumber, returnValue);
//    }
//
//    @Test
//    public void test_getCellNumber_success_with_ticketHolderCountryCodeAndHolderPhoneNumberNull() {
//
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(null);
//        eventTickets1.setHolderCountryCode(CountryCode.US.toString());
//
//        //Execution
//        String cellNumber = ticketingCSVServiceImpl.getCellNumber(eventTickets1, true);
//        assertEquals(cellNumber, null);
//    }
//
//    @Test
//    public void test_getCellNumber_success_with_holder_false() {
//
//        //setup
//        user.setCountryCode(CountryCode.IN);
//
//        ticketingOrder.setPurchaser(user);
//
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(phoneNumber);
//        eventTickets1.setTicketingOrder(ticketingOrder);
//        eventTickets1.setHolderCountryCode(CountryCode.US.name());
//
//        String returnValue = " +" + CountryCode
//                .valueOf(StringUtils
//                        .upperCase(eventTickets1.getTicketingOrder().getPurchaser().getCountryCode().toString().substring(0, 2))).getIntCode() + "-" + eventTickets1.getTicketingOrder().getPurchaser().getPhoneNumber();
//
//        //Execution
//        String cellNumber = ticketingCSVServiceImpl.getCellNumber(eventTickets1, false);
//        assertEquals(cellNumber, returnValue);
//    }
//
//    @Test
//    public void test_getCellNumber_throwException() throws Exception{
//
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(phoneNumber);
//        //"OK" is not country code, if we pass wrong country code then it will throw exception.
//        eventTickets1.setHolderCountryCode("OK");
//
//        //Execution
//        String cellNumber = ticketingCSVServiceImpl.getCellNumber(eventTickets1, true);
//        assertEquals(cellNumber, eventTickets1.getHolderPhoneNumber().toString());
//    }
//
//    @Test
//    public void test_getHolderPhoneNumber_success_with_ticketHolderPhoneNumber_null() {
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(null);
//
//        //Execution
//        String ticketHolderPhoneNumber = ticketingCSVServiceImpl.getHolderPhoneNumber(eventTickets1);
//        assertNull(ticketHolderPhoneNumber);
//    }
//
//    @Test
//    public void test_getHolderPhoneNumber_success_with_ticketHolderPhoneNumber_zero() {
//        //setup
//        EventTickets eventTickets1 = new EventTickets();
//        eventTickets1.setHolderPhoneNumber(0L);
//
//        //Execution
//        String ticketHolderPhoneNumber = ticketingCSVServiceImpl.getHolderPhoneNumber(eventTickets1);
//        assertNull(ticketHolderPhoneNumber);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder = getOrder();
//        eventTickets = getEventTicket();
//
//        ticketingOrder1 = getOrder1();
//
//        eventTickets1 = getEventTicket1();
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//        eventTicketsList.add(eventTickets1);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(false);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    private EventTickets getEventTicket1() {
//        eventTickets1 = new EventTickets();
//        eventTickets1.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets1.setTicketPrice(price);
//        eventTickets1.setTicketPurchaserId(user);
//        eventTickets1.setStatus(Constants.DELETE);
//        eventTickets1.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets1.setTicketingOrder(ticketingOrder);
//        eventTickets1.setTicketingTable(ticketingTable);
//		String displaySeatNumber = "A-1";
//		eventTickets1.setSeatNumberDisplay(displaySeatNumber);
//        eventTickets1.setTicketingTypeId(EventDataUtil.getTicketingType(EventDataUtil.getEvent()));
//        return eventTickets1;
//    }
//
//    private TicketingOrder getOrder1() {
//        ticketingOrder1 = new TicketingOrder();
//        ticketingOrder1.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder1.setOrderDate(orderDate);
//        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder1.setTrackingLinks(trackingLinks);
//        return ticketingOrder1;
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_TICKETING_STATUS_REFUNDED_IsRecurringTrue() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder = getOrder();
//        ticketingOrder.setStaffUserId(null);
//
//        eventTickets = getEventTicket();
//        eventTickets.setStatus(null);
//
//        ticketingOrder1 = getOrder1();
//
//        eventTickets1 = getEventTicket1();
//        eventTickets1.setTicketStatus(TicketStatus.REFUNDED);
//        eventTickets1.setStatus(null);
//        eventTickets1.setCheckInDate(new Date());
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//        eventTicketsList.add(eventTickets1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//        ticketingModuleDTO.setChartKey("Key");
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_TICKETING_STATUS_CHECKED_IN() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder = getOrder();
//        ticketingOrder.setStaffUserId(null);
//
//        eventTickets = getEventTicket();
//        eventTickets.setStatus(null);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setRefundedAmount(0);
//
//        ticketingOrder1 = getOrder1();
//
//        eventTickets1 = getEventTicket1();
//        eventTickets1.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets1.setCheckInDate(new Date());
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//        eventTicketsList.add(eventTickets1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//        ticketingModuleDTO.setChartKey("Key");
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    /*@Test
//    public void test_setAllTicketHolderCSVDataForEvent_success1() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketStatus(TicketStatus.REFUNDED);
//        eventTickets.setTicketingOrder(ticketingOrder);
//        eventTickets.setRefundedAmount(refundedAmount);
//
//        ticketingOrder1 = new TicketingOrder();
//        ticketingOrder1.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder1.setOrderDate(orderDate);
//        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder1.setTrackingLinks(trackingLinks);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//
//        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoQuestion.setKey(Constants.STAFF_NAME);
//        holderAttributeKeyValueDtoQuestion.setValue("");
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
//        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);
//
//        valueDtoHolder = new ValueDto();
//        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);
//
//        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
//        ticketAttributeValueDto.setHolder(valueDtoHolder);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getAllAttributesOrderByAttributeOrder(event)).thenReturn(ticketHolderRequiredAttributesList);
//        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
//        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(recurringIdAndDateMap);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }*/
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_AttributeValueType_BILLING_ADDRESS() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes2.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//
//        ticketingOrder = getOrder();
//
//        eventTickets = getEventTicket();
//        eventTickets.setStatus("CREATED");
//        eventTickets.setRecurringEventId(2L);
//        eventTickets.setRefundedAmount(refundedAmount);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoQuestion.setKey(Constants.FIRST_NAME);
//        holderAttributeKeyValueDtoQuestion.setValue("Address");
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
//        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);
//
//        valueDtoHolder = new ValueDto();
//        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);
//
//        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
//        ticketAttributeValueDto.setHolder(valueDtoHolder);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//        ticketingModuleDTO.setChartKey("Key");
//
//        attendeeSequenceNumber.setNumber(null);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_AttributeValueType_IMAGEWithUrlblank() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes2.setName(Constants.EMAIL);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.IMAGE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//
//        ticketingOrder = getOrder();
//
//        eventTickets = getEventTicket();
//        eventTickets.setStatus("CREATED");
//        eventTickets.setRecurringEventId(2L);
//        eventTickets.setRefundedAmount(refundedAmount);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoQuestion.setKey(Constants.FIRST_NAME);
//        holderAttributeKeyValueDtoQuestion.setValue("Address");
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
//        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);
//
//        valueDtoHolder = new ValueDto();
//        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);
//
//        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
//        ticketAttributeValueDto.setHolder(valueDtoHolder);
//
//        ticketingModuleDTO.setRecurringEvent(true);
//        ticketingModuleDTO.setChartKey("Key");
//
//        attendeeSequenceNumber.setNumber(null);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    /*@Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_eventTicketsStatus_blank() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//        eventTickets.setTicketingOrder(ticketingOrder);
//        eventTickets.setRefundedAmount(refundedAmount);
//        eventTickets.setStatus("");
//
//        ticketingOrder1 = new TicketingOrder();
//        ticketingOrder1.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder1.setOrderDate(orderDate);
//        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder1.setTrackingLinks(trackingLinks);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getAllAttributesOrderByAttributeOrder(event)).thenReturn(ticketHolderRequiredAttributesList);
//        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
//        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(any())).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }*/
//
//    /*@Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_enabledForTicketHolder_false() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketingTable = new TicketingTable();
//        ticketingTable.setTableNoSequence(tableNoSequence);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketHolder(false);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        ticketingOrder = getOrder();
//
//        eventTickets = getEventTicket();
//        eventTickets.setStatus("CREATED");
//        eventTickets.setTicketStatus(TicketStatus.CHECKED_IN);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingModuleDTO.setRecurringEvent(false);
//        ticketingModuleDTO.setChartKey("");
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(anySet())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        when(attendeeSequenceNumberService.getAssignedSequenceByEventTicket(eventTickets)).thenReturn(attendeeSequenceNumber);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }*/
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_ticketAttributeValueDto_null() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketHolder(false);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        ticketingOrder = getOrder();
//
//        eventTickets = getEventTicket();
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingModuleDTO.setRecurringEvent(false);
//        ticketingModuleDTO.setChartKey("");
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(anySet())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//        doReturn(null).when(ticketingCSVServiceImpl).unmashler(any(), any());
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_eventTicketList_empty() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketHolder(false);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(Collections.emptyList());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(anySet())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_ticketHolderRequiredAttributesList_empty() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(Collections.emptyList());
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_ticketHolderRequiredAttributesList_null() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        //mock
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(null);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_success_with_ticketHolderAttributesNullAndBlank() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        eventTickets = getEventTicket();
//        eventTickets.setTicketHolderAttributesId(null);
//
//        ticketHolderAttributes.setValue(null);
//
//        eventTickets1 = getEventTicket1();
//        eventTickets1.setTicketHolderAttributesId(ticketHolderAttributes);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//        eventTicketsList.add(eventTickets1);
//
//        //mock
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        doReturn(ticketAttributeValueDto).when(ticketingCSVServiceImpl).unmashler(any(),any());
//        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event,recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(anySet())).thenReturn(null);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(false);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    //@Test
//    public void test_addTicketHolderRequiredAttributesToHeader_success_with_attributeValueType_BILLING_ADDRESS() {
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//        ticketHolderRequiredAttributes.setName("");
//
//        //Execution
//      //  ticketingCSVServiceImpl.addRequiredAttributesToHeader(headerList, ticketHolderRequiredAttributes);
//    }
//
//    //@Test
//    public void test_addTicketHolderRequiredAttributesToHeader_success_with_attributeValueType_SHIPPING_ADDRESS() {
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
//        ticketHolderRequiredAttributes.setName("");
//
//        //Execution
//       // ticketingCSVServiceImpl.addRequiredAttributesToHeader(headerList, ticketHolderRequiredAttributes);
//    }
//
//    @Test
//    public void test_getTicketAttributeValue_success() {
//
//        //setup
//        ticketHolderRequiredAttributes1 = new  TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setName(ticketAttributeValueDto.getHolder().getQuestions().iterator().next().getKey());
//
//        //Execution
//        String ticketAttributeValue = ticketingCSVServiceImpl.getTicketAttributeValue(eventTickets, ticketAttributeValueDto, ticketHolderRequiredAttributes1);
//        assertEquals(ticketAttributeValue, ticketAttributeValueDto.getHolder().getQuestions().iterator().next().getValue());
//    }
//
//
//    @Test
//    public void test_getTicketHolderAttributesValues_success_with_email() {
//
//        //setup
//        String attributeKey = Constants.STRING_EMAIL_SPACE;
//
//        user.setEmail(email);
//        eventTickets.setHolderEmail(user.getEmail());
//
//        //Execution
//        String holderAttribute = ticketingCSVServiceImpl.getTicketHolderAttributesValues(eventTickets, attributeKey);
//
//        assertEquals(user.getEmail(), holderAttribute);
//    }
//
//    @Test
//    public void test_getTicketHolderAttributesValues_success_with_firstName() {
//
//        //setup
//        String attributeKey = Constants.STRING_FIRST_SPACE_NAME;
//
//        eventTickets.setHolderFirstName(user.getFirstName());
//
//        //Execution
//        String holderAttribute = ticketingCSVServiceImpl.getTicketHolderAttributesValues(eventTickets, attributeKey);
//
//        assertEquals(user.getFirstName(), holderAttribute);
//    }
//
//    @Test
//    public void test_getTicketHolderAttributesValues_success_with_lastName() {
//
//        //setup
//        String attributeKey = Constants.STRING_LAST_SPACE_NAME;
//
//        eventTickets.setHolderLastName(user.getLastName());
//
//        //Execution
//        String holderAttribute = ticketingCSVServiceImpl.getTicketHolderAttributesValues(eventTickets, attributeKey);
//
//        assertEquals(user.getLastName(), holderAttribute);
//    }
//
//    @Test
//    public void test_getTicketHolderAttributesValues_success() {
//
//        //setup
//        String attributeKey = Constants.BIRTHDAY;
//
//        //Execution
//        String holderAttribute = ticketingCSVServiceImpl.getTicketHolderAttributesValues(eventTickets, attributeKey);
//
//        assertNull(holderAttribute);
//    }
//
//    @Test
//    public void test_getTicketingTypeOfTicketingOrderRefundData_success_with_eventTicketList_empty(){
//
//        //setup
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//
//        ticketingOrder = getOrder();
//
//        //Execution
//        Map<String, Object> refundData = ticketingCSVServiceImpl.getTicketingTypeOfTicketingOrderRefundData(ticketingOrder, eventTicketsList, 1L, stripeDTO);
//
//        //assert
//        assertNotNull(refundData);
//    }
//
//    @Test
//    public void test_getTicketingTypeOfTicketingOrderRefundData_success_with_eventTicketList_null(){
//
//        //setup
//        ticketingOrder = getOrder();
//
//        //Execution
//        Map<String, Object> refundData = ticketingCSVServiceImpl.getTicketingTypeOfTicketingOrderRefundData(ticketingOrder, null, 1L, stripeDTO);
//
//        //assert
//        assertNotNull(refundData);
//    }
//
//    private TicketAttributeValueDto getTicketAttributeValueDTO() {
//		AttributeKeyValueDto holderAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
//		String firstName = "Jon";
//		holderAttributeKeyValueDtoAttribute.setValue(firstName);
//
//        holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        holderAttributeKeyValueDtoQuestion.setKey(Constants.STAFF_NAME);
//        holderAttributeKeyValueDtoQuestion.setValue(firstName);
//
//		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute1 = new AttributeKeyValueDto();
//        purchaserAttributeKeyValueDtoAttribute1.setKey(Constants.FIRST_NAME);
//        purchaserAttributeKeyValueDtoAttribute1.setValue(firstName);
//
//		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute2 = new AttributeKeyValueDto();
//        purchaserAttributeKeyValueDtoAttribute2.setKey(Constants.LAST_NAME);
//		String lastName = "Kaz";
//		purchaserAttributeKeyValueDtoAttribute2.setValue(lastName);
//
//		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute3 = new AttributeKeyValueDto();
//        purchaserAttributeKeyValueDtoAttribute3.setKey(Constants.EMAIL);
//        purchaserAttributeKeyValueDtoAttribute3.setValue(email);
//
//		AttributeKeyValueDto purchaserAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
//        purchaserAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
//        purchaserAttributeKeyValueDtoQuestion.setValue(lastName);
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
//        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosHolderAttribute = new ArrayList<>();
//        attributeKeyValueDtosHolderAttribute.add(holderAttributeKeyValueDtoAttribute);
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
//        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute1);
//        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute2);
//        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute3);
//
//        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserQuestion = new ArrayList<>();
//        attributeKeyValueDtosPurchaserQuestion.add(purchaserAttributeKeyValueDtoQuestion);
//
//        valueDtoHolder = new ValueDto();
//        valueDtoHolder.setAttributes(attributeKeyValueDtosHolderAttribute);
//        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);
//
//		ValueDto valueDtoPurchaser = new ValueDto();
//        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);
//        valueDtoPurchaser.setQuestions(attributeKeyValueDtosPurchaserQuestion);
//
//        ticketAttributeValueDto = new TicketAttributeValueDto();
//        ticketAttributeValueDto.setHolder(valueDtoHolder);
//        ticketAttributeValueDto.setPurchaser(valueDtoPurchaser);
//
//        return ticketAttributeValueDto;
//    }
//
//    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes() {
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.EMAIL);
//
//        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes2.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//        ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes3.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes3.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes3.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes3.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
//
//        ticketHolderRequiredAttributes4 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes4.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes4.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes4.setAttributeValueType(AttributeValueType.IMAGE);
//        ticketHolderRequiredAttributes4.setAttribute(true);
//
//		TicketHolderRequiredAttributes ticketHolderRequiredAttributes5 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes5.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes5.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes5.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes5.setAttributeValueType(AttributeValueType.EMAIL);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes4);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes5);
//        return ticketHolderRequiredAttributesList;
//    }
//
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNull(){
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(null, ticketingType);
//
//        //Assert
//        assertEquals(false,applyCoupon);
//
//    }
//
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNotNull_TicketingTypeDonation(){
//
//        //setup
//        ticketingType.setTicketType(TicketType.DONATION);
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(ticketingCoupon, ticketingType);
//
//        //Assert
//        assertEquals(false,applyCoupon);
//
//    }
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNotNull_TicketingTypeFree(){
//
//        //setup
//        ticketingType.setTicketType(TicketType.FREE);
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(ticketingCoupon, ticketingType);
//
//        //Assert
//        assertEquals(false,applyCoupon);
//
//    }
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNotNull(){
//
//        //setup
//        ticketingCoupon.setEventTicketTypeId("1");
//        ticketingType.setId(1L);
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(ticketingCoupon, ticketingType);
//
//        //Assert
//        assertEquals(true,applyCoupon);
//
//    }
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNotNull_TicketingCouponNotContainsTicketingType(){
//
//        //setup
//        ticketingCoupon.setEventTicketTypeId("1");
//        ticketingType.setId(2L);
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(ticketingCoupon, ticketingType);
//
//        //Assert
//        assertEquals(false,applyCoupon);
//
//    }
//    @Test
//    public void test_isTicketCouponExitsForParticularTicketType_TicketingCouponNotNull_TicketingCouponContainsNullTicketingType_case1(){
//
//        //setup
//        ticketingCoupon.setEventTicketTypeId("");
//        ticketingType.setId(2L);
//
//        //Execution
//        boolean applyCoupon = ticketingCSVServiceImpl.isTicketCouponExitsForParticularTicketType(ticketingCoupon, ticketingType);
//
//        //Assert
//        assertEquals(false,applyCoupon);
//
//    }
//
//    @Test
//    public void test_calculateDiscountedAmountPerOrder() {
//
//        //Execution
//        double amountPerOrder = ticketingCSVServiceImpl.calculateDiscountedAmountPerOrder(100,100,50,1);
//
//        //assert
//        assertEquals(amountPerOrder,50,0);
//    }
//
//    @Test
//    public void test_calculateDiscountedAmountPerTicket() {
//
//        //Execution
//        double amountPerTicket = ticketingCSVServiceImpl.calculateDiscountedAmountPerTicket(100,100,1,50);
//
//        //assert
//        assertEquals(amountPerTicket,50,0);
//    }
//}