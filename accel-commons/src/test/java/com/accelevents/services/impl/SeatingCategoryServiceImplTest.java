package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.domain.Event;
import com.accelevents.domain.Integration;
import com.accelevents.domain.SeatingCategories;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.SeatingCategoryDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.SeatingCategoryRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.EventCategoryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import seatsio.charts.Category;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SeatingCategoryServiceImplTest {

    @Spy
    @InjectMocks
    private SeatingCategoryServiceImpl seatingCategoryServiceImpl = new SeatingCategoryServiceImpl();
    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;

    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Mock
    private CommonEventService commonEventService;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private CartAbandonmentService cartAbandonmentService;

    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private ChargebeeService chargebeeService;

    @Mock
    private EventService eventService;

    @Mock
    private TicketingManageService ticketingManageService;
    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private IntegrationService integrationService;

    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    private Event event;
    private Ticketing ticketing;
    private SeatingCategoryDto seatingCategoryDto;
	private SeatingCategories seatingCategories;

    private double sequence = 1000;
    private Long id = 1L;
    private Long topCategoryId = 2L;
    private Long topBottomCategoryId = 3L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = new Event();
        event.setEventId(1L);



        seatingCategoryDto = new SeatingCategoryDto();
        seatingCategoryDto.setColor("#8C2856");
        seatingCategoryDto.setHavingVariations(true);
        seatingCategoryDto.setImageUrl("b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        seatingCategoryDto.setName("T-shirt");
        seatingCategoryDto.setQuantity(100);

        seatingCategories = new SeatingCategories(event.getEventId(), seatingCategoryDto);

		EventCategoryDto eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setEventId(seatingCategories.getEventId());
        eventCategoryDto.setName(seatingCategories.getName());
        eventCategoryDto.setId(seatingCategories.getId());
        eventCategoryDto.setHavingVariations(seatingCategories.isHavingVariations());
        eventCategoryDto.setColor(seatingCategories.getColor());
        eventCategoryDto.setQuantity(seatingCategories.getQuantity());
        eventCategoryDto.setImageUrl(seatingCategories.getImageUrl());
    }

    @Test
    void test_save_withSeatingCategoriesId() {

        //setup
        seatingCategories.setId(1L);

        //Execution
        seatingCategoryServiceImpl.save(seatingCategories);

        //Assertion
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());

        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();
        assertEquals(seatingCategoriesData, seatingCategories);
    }

    public static Object[] getSeatingCategories(){
        return new Object[]{
                new Object[]{null},
                new Object[]{new SeatingCategories()},
        };
    }

    @ParameterizedTest
    @MethodSource("getSeatingCategories")
    void test_save_NoSeatingCategoriesId(SeatingCategories lastItem) {

        //mock
        when(seatingCategoryRepository.findFirstByEventIdOrderByPositionDesc(seatingCategories.getEventId())).thenReturn(lastItem);

        //Execution
        seatingCategoryServiceImpl.save(seatingCategories);

        //Assertion
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());

        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();
        assertEquals(seatingCategoriesData.getPosition(), sequence,0);

        verify(seatingCategoryRepository).findFirstByEventIdOrderByPositionDesc(seatingCategories.getEventId());
    }

    @Test
    void test_addCategory() {
        List<Long> longs=new ArrayList<>();
        longs.add(1L);

        //mock
        ticketingTypeService.findAllIdByEventId(event);

        doReturn(seatingCategories).when(seatingCategoryServiceImpl).save(any());
        doNothing().when(chargebeeService).validateTicketTypesByEventPlan(isA(Event.class),isA(Long.class));

        //Execution
        EventCategoryDto eventCategoryDto = seatingCategoryServiceImpl.addCategory(event,seatingCategoryDto,false);

        //Assertion
        assertEquals(eventCategoryDto.getId(),seatingCategories.getId(),0);
        assertEquals(eventCategoryDto.getName(),seatingCategories.getName());
        assertEquals(eventCategoryDto.getColor(),seatingCategories.getColor());
        assertEquals(eventCategoryDto.getImageUrl(),seatingCategories.getImageUrl());
        assertEquals(eventCategoryDto.isHavingVariations(),seatingCategories.isHavingVariations());
        assertEquals(eventCategoryDto.getEventId(),seatingCategories.getEventId(),0);
        assertEquals(eventCategoryDto.getQuantity(),seatingCategories.getQuantity());

        verify(seatingCategoryServiceImpl).save(any());
    }

    @Test
    void test_getSeatingCategoryById() {

        //mock
        when(seatingCategoryRepository.findCategoryById(id)).thenReturn(null);

        //Execution
        EventCategoryDto eventCategoryDto = seatingCategoryServiceImpl.getSeatingCategoryById(id);

        //Assertion
        assertNull(eventCategoryDto);

        verify(seatingCategoryRepository).findCategoryById(id);
    }

    @Test
    void test_deleteCategories_throwExceptionCategoryAlreadyInUse() {

        //setup
        List<Long> eventTicketsIds = new ArrayList<>();
        eventTicketsIds.add(id);

        //mock
        when(ticketingTypeTicketService.findByCategoryId(id)).thenReturn(Collections.emptyList());
        when(commonEventService.findByTypeId(any())).thenReturn(eventTicketsIds);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> seatingCategoryServiceImpl.deleteCategories(id,event));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CATEGORY_ALREADY_IN_USE.getDeveloperMessage(), exception.getMessage());

        verify(ticketingTypeTicketService).findByCategoryId(id);
        verify(commonEventService).findByTypeId(any());
    }

    @Test
    void test_deleteCategories_success() {
        ticketing = new Ticketing();
        ticketing.setChartKey("chart_key");

        Integration integration = new Integration();
        integration.setId(1L);
        //mock

        when(ticketingTypeTicketService.findByCategoryId(id)).thenReturn(Collections.emptyList());
        when(commonEventService.findByTypeId(any())).thenReturn(Collections.emptyList());


        when(ticketingOrderManagerService.getOrderIdsAndDeleteTicketingOrderManagerByTicketingTypes(anyList())).thenReturn(Arrays.asList(1L));
        doNothing().when(cartAbandonmentService).deleteByOrderIds(anyList());
        //doNothing().when(ticketingOrderRepoService).deleteByIdIn(anyList());
        doNothing().when(ticketingOrderManagerService).deleteTicketingOrderManagerByListOfOrderIds(anyList());
        when(integrationService.getAllIntegrationDetailsListByListOfTicketingTypeIds(List.of())).thenReturn(Collections.singletonList(integration));

        //Execution
        seatingCategoryServiceImpl.deleteCategories(id,event);

        verify(ticketingTypeTicketService).findByCategoryId(id);
        verify(commonEventService).findByTypeId(any());
        verify(ticketingTypeService).updateTicketingTypeRecordStatusToCancel(anyList());
        verify(seatingCategoryRepository).updateSeatingCategoriesToDELETE(id);
        verify(ticketingOrderManagerService).getOrderIdsAndDeleteTicketingOrderManagerByTicketingTypes(anyList());
        verify(cartAbandonmentService).deleteByOrderIds(anyList());
        verify(ticketingOrderManagerService).deleteTicketingOrderManagerByListOfOrderIds(anyList());
    }

    @Test
    void test_updateCategoryDetails() {

        //mock
        when(seatingCategoryRepository.findById(1L)).thenReturn(Optional.of(seatingCategories));
        doReturn(seatingCategories).when(seatingCategoryServiceImpl).save(any());

        //Execution
        EventCategoryDto eventCategoryDto = seatingCategoryServiceImpl.updateCategoryDetails(1L,seatingCategoryDto,event);

        //assert
        assertEquals(eventCategoryDto.getId(),seatingCategories.getId(),0);
        assertEquals(eventCategoryDto.getName(),seatingCategories.getName());
        assertEquals(eventCategoryDto.getColor(),seatingCategories.getColor());
        assertEquals(eventCategoryDto.getImageUrl(),seatingCategories.getImageUrl());
        assertEquals(eventCategoryDto.isHavingVariations(),seatingCategories.isHavingVariations());
        assertEquals(eventCategoryDto.getEventId(),seatingCategories.getEventId(),0);
        assertEquals(eventCategoryDto.getQuantity(),seatingCategories.getQuantity());

        verify(seatingCategoryRepository,times(1)).findById(1L);
        verify(seatingCategoryServiceImpl).save(any());
    }

    @Test
    void test_getDefaultCategories() {

        //mock
        when(seatingCategoryRepository.findByEventIdAndTicketTypeCreateFromIsNull(event, DataType.TICKET)).thenReturn(Collections.emptyList());

        //Execution
        List<Category> categories = seatingCategoryServiceImpl.getDefaultCategories(event, DataType.TICKET);

        //assert
        assertTrue(categories.isEmpty());
        verify(seatingCategoryRepository).findByEventIdAndTicketTypeCreateFromIsNull(event, DataType.TICKET);
    }

    @Test
    void test_getDefaultCategoriesDto() {
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);
        //mock
        when(seatingCategoryRepository.findByEventIdAndFindTicketTypeCreateFromIsNull(event, DataType.TICKET)).thenReturn(Collections.emptyList());
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        //Execution
        List<EventCategoryDto> defaultCategoriesDto = seatingCategoryServiceImpl.getDefaultCategoriesDto(event, DataType.TICKET);

        //assert
        assertTrue(defaultCategoriesDto.isEmpty());
        verify(seatingCategoryRepository).findByEventIdAndFindTicketTypeCreateFromIsNull(event, DataType.TICKET);
    }

    @Test
    void test_updateCategorySequencewithTopAndTopBottomSeatingCategorieNull() {

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(null);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(null);

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
    }

    @Test
    void test_updateCategorySequencewithTopAndTopNextSeatingCategorieNotNullAndPositionOne() {

        //setup
        seatingCategories.setPosition(1);

        List<SeatingCategories> nextAndPrePositionItemList = new ArrayList<>();
        nextAndPrePositionItemList.add(seatingCategories);

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.nextPositionSeatingCategories(seatingCategories.getId(),event.getEventId(),seatingCategories.getPosition())).thenReturn(nextAndPrePositionItemList);
        when(seatingCategoryRepository.previousPositionSeatingCategories(seatingCategories.getId(),event.getEventId(),seatingCategories.getPosition())).thenReturn(nextAndPrePositionItemList);
        doNothing().when(seatingCategoryRepository).updatePositionSeatingCategories(anyLong(),anyDouble(),anyDouble(),anyDouble());

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());
        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();

        assertEquals(seatingCategoriesData.getPosition(),1,0); //NOSONAR

        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
        verify(seatingCategoryRepository).nextPositionSeatingCategories(seatingCategories.getId(),event.getEventId(),seatingCategories.getPosition());
        verify(seatingCategoryRepository).previousPositionSeatingCategories(seatingCategories.getId(),event.getEventId(),seatingCategories.getPosition());
        verify(seatingCategoryRepository).updatePositionSeatingCategories(anyLong(),anyDouble(),anyDouble(),anyDouble());
    }

    @Test
    void test_updateCategorySequencewithTopAndTopNextSeatingCategorieNotNullAndPositionNotOne() {

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(seatingCategories);

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());
        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();

        assertEquals(seatingCategoriesData.getPosition(),0,0); //NOSONAR

        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
    }

    @Test
    void test_updateCategorySequencewithTopSeatingCategorieNull() {

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(null);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(seatingCategories);

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());
        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();

        assertEquals(seatingCategoriesData.getPosition(),sequence,0);

        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
    }

    @Test
    void test_updateCategorySequencewithTopNextSeatingCategorieNullAndPosDiffLessThenAndEqOne() {

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(null);
        doNothing().when(seatingCategoryRepository).updatePositionForAllSeatingCategories(sequence, event.getEventId());

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());
        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();

        assertEquals(seatingCategoriesData.getPosition(),sequence,0);

        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
        verify(seatingCategoryRepository).updatePositionForAllSeatingCategories(sequence, event.getEventId());
    }

    @Test
    void test_updateCategorySequencewithTopNextSeatingCategorieNullAndPosDiffNotLessThenAndEqOne() {

        //setup
        seatingCategories.setPosition(2000);

        //mock
        when(seatingCategoryRepository.findOneById(id)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topCategoryId)).thenReturn(seatingCategories);
        when(seatingCategoryRepository.findOneById(topBottomCategoryId)).thenReturn(null);

        //Execution
        seatingCategoryServiceImpl.updateCategorySequence(id,topCategoryId,topBottomCategoryId,event);

        //assert
        ArgumentCaptor<SeatingCategories> seatingCategoriesArgumentCaptor = ArgumentCaptor.forClass(SeatingCategories.class);
        verify(seatingCategoryRepository).save(seatingCategoriesArgumentCaptor.capture());
        SeatingCategories seatingCategoriesData = seatingCategoriesArgumentCaptor.getValue();

        assertEquals(seatingCategoriesData.getPosition(),sequence,0);

        verify(seatingCategoryRepository,times(3)).findOneById(anyLong());
    }

    @Test
    void test_getCategoriesByEventWithDataType() {

        //mock
        when(seatingCategoryRepository.findCategoriesByEventWithDataType(event)).thenReturn(Collections.emptyList());

        //Execution
        List<EventCategoryDto> eventCategoryDtos = seatingCategoryServiceImpl.getCategoriesByEventWithDataType(event);

        //assert
        assertTrue(eventCategoryDtos.isEmpty());
        verify(seatingCategoryRepository).findCategoriesByEventWithDataType(event);
    }
}