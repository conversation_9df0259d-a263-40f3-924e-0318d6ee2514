package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.ChargeBeeCustomerInvoiceDTO;
import com.accelevents.billing.chargebee.repo.impl.ChargeBeeCustomerInvoicesServiceImpl;
import com.accelevents.billing.chargebee.repositories.ChargeBeeCustomerInvoicesRepository;
import com.accelevents.billing.chargebee.service.ChargebeePaymentService;
import com.accelevents.domain.ChargeBeeCustomerInvoices;
import com.accelevents.domain.Organizer;
import com.accelevents.services.OrganizerService;
import com.chargebee.models.Invoice;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ChargeBeeCustomerInvoiceServiceTest {
    @Spy
    @InjectMocks
    private ChargeBeeCustomerInvoicesServiceImpl chargeBeeCustomerInvoicesServiceImpl;
    @Mock
    private ChargeBeeCustomerInvoicesRepository chargeBeeCustomerInvoicesRepository;
    @Mock
    private ChargebeePaymentService chargebeePaymentService;
    @Mock
    private OrganizerService organizerService;

    @BeforeEach
    void setUp() {
        chargeBeeCustomerInvoicesServiceImpl = new ChargeBeeCustomerInvoicesServiceImpl(chargeBeeCustomerInvoicesRepository, chargebeePaymentService, organizerService);
    }

    @Test
    void testSaveCustomerInvoice() {
        // Arrange
        Invoice invoice = mock(Invoice.class);
        Invoice.LineItem lineItem1 = mock(Invoice.LineItem.class);
        Invoice.LineItem lineItem2 = mock(Invoice.LineItem.class);
        List<Invoice.LineItem> lineItems = Arrays.asList(lineItem1, lineItem2);

        when(invoice.lineItems()).thenReturn(lineItems);
        when(invoice.status()).thenReturn(Invoice.Status.PAID);
        when(lineItem1.description()).thenReturn("Item 1");
        when(lineItem1.amount()).thenReturn(1000L);
        when(lineItem2.description()).thenReturn("Item 2");
        when(lineItem2.amount()).thenReturn(2000L);

        // Act
        chargeBeeCustomerInvoicesServiceImpl.saveCustomerInvoice(invoice);

        // Assert
        verify(chargeBeeCustomerInvoicesRepository, times(1)).saveAll(anyList());
    }

    @Test
    void testGetAllCustomerInvoices() throws Exception {
        // Arrange
        String organizerURL = "testOrganizerURL";
        String search = "testSearch";
        Organizer organizer = mock(Organizer.class);
        when(organizerService.getOrganizerByURL(organizerURL)).thenReturn(organizer);
        when(organizer.getChargebeeCustomerId()).thenReturn("testCustomerId");
        List<ChargeBeeCustomerInvoices> invoicesList = new ArrayList<>();
        when(chargeBeeCustomerInvoicesRepository.findAllByChargeBeeCustomerIdAndSearchString(anyString(), anyString())).thenReturn(invoicesList);
        // Act
        List<ChargeBeeCustomerInvoiceDTO> invoiceDTOs = chargeBeeCustomerInvoicesServiceImpl.getAllCustomerInvoices(organizerURL, search);

        // Assert
        assertEquals(0, invoiceDTOs.size());
    }

    @Test
    void testGetAllCustomerInvoicesWithSearch() throws Exception {
        // Arrange
        String organizerURL = "testOrganizerURL";
        String search = "testSearch";
        Organizer organizer = mock(Organizer.class);
        when(organizerService.getOrganizerByURL(organizerURL)).thenReturn(organizer);
        when(organizer.getChargebeeCustomerId()).thenReturn("testCustomerId");

        List<ChargeBeeCustomerInvoices> invoicesList = new ArrayList<>();
        when(chargeBeeCustomerInvoicesRepository.findAllByChargeBeeCustomerIdAndSearchString(anyString(), anyString())).thenReturn(invoicesList);
        // Act
        List<ChargeBeeCustomerInvoiceDTO> invoiceDTOs = chargeBeeCustomerInvoicesServiceImpl.getAllCustomerInvoices(organizerURL, search);

        // Assert
        assertEquals(0, invoiceDTOs.size());
    }
}
