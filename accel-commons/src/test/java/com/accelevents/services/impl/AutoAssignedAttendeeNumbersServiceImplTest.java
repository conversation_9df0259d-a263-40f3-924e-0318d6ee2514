package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.*;
import com.accelevents.repositories.require.attributes.TicketRequiresAttributesRepo;
import com.accelevents.services.AttendeeSequenceNumberService;
import com.accelevents.services.EventTicketsService;
import com.accelevents.services.TicketHolderRequiredAttributesService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.DisplayAttributeDto;
import com.accelevents.ticketing.dto.TicketSettingGetDto;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.JAXBException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class AutoAssignedAttendeeNumbersServiceImplTest {

    @Spy
    @InjectMocks
    private AutoAssignedAttendeeNumbersServiceImpl autoAssignedAttendeeNumbersServiceImpl = new AutoAssignedAttendeeNumbersServiceImpl();
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;
    @Mock
    private AutoAssignedAttendeeNumbersRepository autoAssignedAttendeeNumbersRepository;
    @Mock
    private AttendeeSequenceNumberService attendeeSequenceNumberService;
    @Mock
    private EventTicketsRepository eventTicketsRepository;
    @Mock
    private TicketHolderEditAttributesServiceImpl ticketHolderEditAttributesServiceImpl;
    @Mock
    private TicketRequiresAttributesRepo ticketRequiresAttributesRepo;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private CommonEventService commonEventService;

    private Event event;
    private EventTickets eventTickets;
    private Ticketing ticketing;
    private TicketingType ticketingType;
    private Long sequenceId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketing = EventDataUtil.getTicketing(event);
        eventTickets = EventDataUtil.getEventTickets();
        ticketingType = EventDataUtil.getTicketingType(event);
        sequenceId = 1L;
    }
    private List<TicketHolderRequiredAttributes> getAllAttributes() {
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributeList = new ArrayList<>();
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName(Constants.GENDER);
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName(Constants.AGE);
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName(Constants.FIRST_NAME);
        ticketHolderRequiredAttributeList.add(ticketHolderRequiredAttributes);
        ticketHolderRequiredAttributeList.add(ticketHolderRequiredAttributes1);
        ticketHolderRequiredAttributeList.add(ticketHolderRequiredAttributes2);
        return ticketHolderRequiredAttributeList;
    }

    static Object[] getCreateSequenceParameters() {
        return new Object[]{
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, null, "1", "Male", null, null), true, getHolderDisplayAttributes("Male", "25", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, null, "1", null, 1, null), true, getHolderDisplayAttributes("Male", "25", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, null, "1", null, 25, null), true, getHolderDisplayAttributes("Male", "20", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, null, "1", null, 1, 50), true, getHolderDisplayAttributes("Male", null, null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,9", "1", "Male", 1, 50), true, getHolderDisplayAttributes("FEMALE", "25A", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,9", "1", "Male", 1, 50), true, getHolderDisplayAttributes("Male", "25", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,9", "1", "Male", 25, 50), true, getHolderDisplayAttributes("Male", "55", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,8", "1", null, null, 50), true, getHolderDisplayAttributes(null, "25", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,9", "1", "Male", null, 50), true, getHolderDisplayAttributes("Male", "55", null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3,9", "1", null, null, null), true, getHolderDisplayAttributes("Male", "55", null)},
        };
    }

    @ParameterizedTest
    @MethodSource("getCreateSequenceParameters")
    void test_createSequence_success(AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto, boolean isholderEnable, List<DisplayAttributeDto> editTicketHoldersAttribute) throws JAXBException {
        // Set up
        List<EventTickets> eventTicketList = new ArrayList<>();

        eventTicketList.add(eventTickets);
        if (!isholderEnable) {
            ticketing.setCollectTicketHolderAttributes(false);
        }
        //Mock



        //Execution
        autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto);

        ArgumentCaptor<AutoAssignedAttendeeNumbers> argumentCaptor = ArgumentCaptor.forClass(AutoAssignedAttendeeNumbers.class);
        verify(autoAssignedAttendeeNumbersRepository, Mockito.times(1)).save(argumentCaptor.capture());
        AutoAssignedAttendeeNumbers actualStatus = argumentCaptor.getValue();
        assertNotNull(actualStatus.getId());
    }

    private static DisplayAttributeDto getAttributes(String name, String value) {
        DisplayAttributeDto displayAttributeDto = new DisplayAttributeDto();
        displayAttributeDto.setName(name);
        displayAttributeDto.setValue(value);
        return displayAttributeDto;
    }

    private AutoAssignedAttendeeNumbersSettingsDto getAutoAssignedAttendeeNumbersSettingsDto() {
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = new AutoAssignedAttendeeNumbersSettingsDto();
        AutoAssignedAttendeeNumbersSettingsDto.setSequenceName("FIRST-SEQUENCE");
        AutoAssignedAttendeeNumbersSettingsDto.setAppliesToTicketType("1");
        AutoAssignedAttendeeNumbersSettingsDto.setNumbersToExclude("1,2,5-10");
        AutoAssignedAttendeeNumbersSettingsDto.setMinAge(1);
        AutoAssignedAttendeeNumbersSettingsDto.setMaxAge(50);
        AutoAssignedAttendeeNumbersSettingsDto.setGender("Male");
        return AutoAssignedAttendeeNumbersSettingsDto;
    }


    private AttendeeSequenceNumbersDto getAttendeeSequenceNumbersDto() {
        AttendeeSequenceNumbersDto attendeeSequenceNumbersDto = new AttendeeSequenceNumbersDto();
        attendeeSequenceNumbersDto.setSequenceName("FIRST-SEQUENCE");
        attendeeSequenceNumbersDto.setEventTicketId(1L);
        attendeeSequenceNumbersDto.setFirstName("FIRST-NAME");
        attendeeSequenceNumbersDto.setLastName("LAST-NAME");
        attendeeSequenceNumbersDto.setTicketType("Ticket-types-name");
        return attendeeSequenceNumbersDto;
    }

    @Test
    void test_createAttendeeSequence_success() {
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        autoAssignedAttendeeNumbers.setId(1L);
        AttendeeSequenceNumber attendeeSequenceNumber = new AttendeeSequenceNumber();
        attendeeSequenceNumber.setModified(false);
        attendeeSequenceNumber.setNumber(1);
        attendeeSequenceNumber.setEventTicketId(eventTickets);

        // Execution
        autoAssignedAttendeeNumbersServiceImpl.createSequence(event, null, eventTickets, autoAssignedAttendeeNumbers);

        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberService, Mockito.times(1)).save(argumentCaptor.capture());

        ArgumentCaptor<EventTickets> argumentCaptor1 = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(argumentCaptor1.capture());
    }

    @Test
    void test_createAttendeeSequence_withSameSequence_success() {
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        eventTickets.setAutoAssignedAttendeeNumber(autoAssignedAttendeeNumbers);
        autoAssignedAttendeeNumbers.setId(1L);
        AttendeeSequenceNumber attendeeSequenceNumber = new AttendeeSequenceNumber();
        attendeeSequenceNumber.setModified(false);
        attendeeSequenceNumber.setNumber(1);
        attendeeSequenceNumber.setEventTicketId(eventTickets);
        // Execution
        autoAssignedAttendeeNumbersServiceImpl.createSequence(event, 1, eventTickets, autoAssignedAttendeeNumbers);
    }

    public static Object[] getCreateSequenceInvalidParameters() {
        return new Object[]{
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "2-1", "1", "Male", null, null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "-10", "1", "Male", 1, null)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "8-3,8", "1", "Male", null, 1)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1a-3,-9", "1", "Male", 1, 50)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-3-5,9", "1", "Male", 1, 50)},
                new Object[]{new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1-5sd,9", "1", null, null, null)},
        };
    }

    @ParameterizedTest
    @MethodSource("getCreateSequenceInvalidParameters")
    void test_createSequence_throwInvalidExcludeNumber(AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto){

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.INVALID_NUMBER_TO_EXCLUD.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throwTicketTypeNotEmpty() {
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = new AutoAssignedAttendeeNumbersSettingsDto(null, "FIRST-SEQUENCE", 1, 1, "1,2,45-50", null, "Male", null, null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.TICKET_TYPE_NOT_EMPTY.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throwSequenceNameNotNull(){
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = new AutoAssignedAttendeeNumbersSettingsDto(null, null, 1, 1, "1,2,45-50", "1", "Male", null, null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.SEQUENCE_NAME_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throw_INVALID_AGE_Exception(){
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = getAutoAssignedAttendeeNumbersSettingsDto();
        AutoAssignedAttendeeNumbersSettingsDto.setMinAge(2);
        AutoAssignedAttendeeNumbersSettingsDto.setMaxAge(1);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.INVALID_AGE.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throw_INVALID_MIN_STARTING_NUMBER_Exception(){
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = getAutoAssignedAttendeeNumbersSettingsDto();
        AutoAssignedAttendeeNumbersSettingsDto.setMinStartingNumber(0);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));
        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.INVALID_MIN_STARTING_NUMBER.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throw_INVALID_ENDING_NUMBER_Exception() {
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = getAutoAssignedAttendeeNumbersSettingsDto();
        AutoAssignedAttendeeNumbersSettingsDto.setEndingNumber(0);
        AutoAssignedAttendeeNumbersSettingsDto.setMinStartingNumber(1);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.INVALID_ENDING_NUMBER.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createSequence_throw_SEQUENCE_NAME_NOT_EXIST_name_blank(){
        //Set up
        AutoAssignedAttendeeNumbersSettingsDto AutoAssignedAttendeeNumbersSettingsDto = getAutoAssignedAttendeeNumbersSettingsDto();
        AutoAssignedAttendeeNumbersSettingsDto.setSequenceName("");

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.createOrUpdateAutoAssignedAttendeeNumber(event, AutoAssignedAttendeeNumbersSettingsDto));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.SEQUENCE_NAME_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }


    public static Object[] assignAttendeeNumberForPurchaseTicketForPurchaser() {
        return new Object[]{
                new Object[]{"Male", "25"},
                new Object[]{null, null},
                new Object[]{"Male", null},
                new Object[]{null, "25"},
                new Object[]{null, "25A"},
        };
    }

    public static Object[] assignEndingAndStartingNumber() {
        return new Object[] {
                new Object[]{"Male", "25", 10, 1, 1},
                new Object[]{"Male", "25", 10, 1, 1},
//                new Object[]{null, null},
//                new Object[]{"Male", null},
//                new Object[]{null, "25"},
//                new Object[]{null, "25A"},
        };
    }

    @ParameterizedTest
    @MethodSource("assignAttendeeNumberForPurchaseTicketForPurchaser")
    void test_assignAttendeeNumberForPurchaseTicketForPurchaser_success(String gender, String age) {
        //Set up
        List<AttributeKeyValueDto> attributes = new ArrayList<>();
        attributes.add(getPurchaseAttributes("GENDER", gender));
        attributes.add(getPurchaseAttributes("AGE", age));
        PurchaserBookingDto purchaserBookingDto = new PurchaserBookingDto();
        purchaserBookingDto.setAttributes(attributes);
        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setHasholderattributes(false);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, null, "1", null, null, null);
        //Mock
        when(autoAssignedAttendeeNumbersRepository.getSequencesByTicketType(event, ticketingType.getId(), StringUtils.isNoneEmpty(age) && NumberUtils.isDigits(age) ? Integer.parseInt(age) : 9999999, gender)).thenReturn(Collections.singletonList(autoAssignedAttendeeNumbers));

        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));

        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberService, Mockito.times(1)).save(argumentCaptor.capture());

        ArgumentCaptor<EventTickets> argumentCaptor1 = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(argumentCaptor1.capture());
    }

    @ParameterizedTest
    @MethodSource("assignEndingAndStartingNumber")
    void test_assignAttendeeNumberForPurchaseTicketForPurchaser_success_Ending_Number_Not_Null(String gender, String age, Integer endingNumber, Integer startingNumber, Integer assignedAttendeeToSequence) {
        //Set up
        List<AttributeKeyValueDto> attributes = new ArrayList<>();
        attributes.add(getPurchaseAttributes("GENDER", gender));
        attributes.add(getPurchaseAttributes("AGE", age));
        PurchaserBookingDto purchaserBookingDto = new PurchaserBookingDto();
        purchaserBookingDto.setAttributes(attributes);
        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setHasholderattributes(false);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, startingNumber, null, "1", null, null, null, false, endingNumber);
        //Mock
        when(autoAssignedAttendeeNumbersRepository.getSequencesByTicketType(event, ticketingType.getId(), StringUtils.isNoneEmpty(age) && NumberUtils.isDigits(age) ? Integer.parseInt(age) : 9999999, gender)).thenReturn(Collections.singletonList(autoAssignedAttendeeNumbers));

        when(commonEventService.countAttendeeAssignedToSequence(any())).thenReturn(assignedAttendeeToSequence);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));

        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberService, Mockito.times(1)).save(argumentCaptor.capture());

        ArgumentCaptor<EventTickets> argumentCaptor1 = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(argumentCaptor1.capture());
    }

    @Test
    void test_assignAttendeeNumberForPurchaseTicketForPurchaserNull_success() {
        //Set up
        List<AttributeKeyValueDto> attributes = new ArrayList<>();
        attributes.add(getPurchaseAttributes("FIRST_NAME", "TEST"));
        PurchaserBookingDto purchaserBookingDto = new PurchaserBookingDto();
        purchaserBookingDto.setAttributes(attributes);
        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setPurchaser(purchaserBookingDto);
        ticketBookingDto.setHasholderattributes(false);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, null, "1", null, null, null);
        //Mock
        when(autoAssignedAttendeeNumbersRepository.getSequencesByTicketType(event, ticketingType.getId(), 9999999, null)).thenReturn(Collections.singletonList(autoAssignedAttendeeNumbers));

        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));
    }

    public static Object[][] assignAttendeeNumberForPurchaseTicketForHolder() {
        return new Object[][]{
                {"Male", "25"}
        };
    }

    @ParameterizedTest
    @MethodSource("assignAttendeeNumberForPurchaseTicketForHolder")
    void test_assignAttendeeNumberForPurchaseTicketForHolder_success(String gender, String age) throws JAXBException {
        //Set up
        List<DisplayAttributeDto> editTicketHoldersAttribute = getHolderDisplayAttributes(gender, age, null);

        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setHasholderattributes(true);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 25);
        //Mock
        when(ticketHolderRequiredAttributesService.getAllAttributes(event)).thenReturn(getAllAttributes());
        when(ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(any(),any(),any())).thenReturn(editTicketHoldersAttribute);
        when(autoAssignedAttendeeNumbersRepository.getSequencesByTicketType(event, ticketingType.getId(), StringUtils.isNoneEmpty(age) && NumberUtils.isDigits(age) ? Integer.parseInt(age) : 9999999, gender)).thenReturn(Collections.singletonList(autoAssignedAttendeeNumbers));

        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));

        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberService, Mockito.times(1)).save(argumentCaptor.capture());

        ArgumentCaptor<EventTickets> argumentCaptor1 = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(argumentCaptor1.capture());
    }

    public static Object[] assignAttendeeNumberForPurchaseTicketForHolderNull() {
        return new Object[]{
                new Object[]{"Male", "25"},
                new Object[]{null, null},
                new Object[]{"Male", null},
                new Object[]{null, "25"},
                new Object[]{null, "25A"},
                new Object[]{"TEST", "TEST"},
        };
    }

    //TODO: mockito fix stub test case
    /*@ParameterizedTest
    @MethodSource("assignAttendeeNumberForPurchaseTicketForHolderNull")
    void test_assignAttendeeNumberForPurchaseTicketForHolderNull_success(String gender, String age) throws JAXBException {
        //Set up
        List<DisplayAttributeDto> editTicketHoldersAttribute = getHolderDisplayAttributes(gender, age, "Test");

        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setHasholderattributes(true);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", null, null, null);
        //Mock
        when(autoAssignedAttendeeNumbersRepository.getSequencesByTicketType(event, ticketingType.getId(), StringUtils.isNoneEmpty(age) && NumberUtils.isDigits(age) ? Integer.parseInt(age) : 9999999, gender)).thenReturn(Collections.emptyList());
        doReturn(Collections.emptyList()).when(autoAssignedAttendeeNumbersRepository).getSequencesByTicketType(event, ticketingType.getId(), StringUtils.isNoneEmpty(age) && NumberUtils.isDigits(age) ? Integer.parseInt(age) : 9999999, gender);
        when(ticketHolderRequiredAttributesService.getAllAttributes(event)).thenReturn(getAllAttributes());
        when(ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(any(), anyList(), anyBoolean())).thenReturn(editTicketHoldersAttribute);
//        when(ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(eventTickets,getAllAttributes(),ticketBookingDto.getHasholderattributes())).thenReturn(editTicketHoldersAttribute);
//        when(attendeeSequenceNumberService.getMaxSequenceNumber(event)).thenReturn(1);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));

        ArgumentCaptor<AttendeeSequenceNumber> argumentCaptor = ArgumentCaptor.forClass(AttendeeSequenceNumber.class);
        verify(attendeeSequenceNumberService, Mockito.times(0)).save(argumentCaptor.capture());

        ArgumentCaptor<EventTickets> argumentCaptor1 = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(0)).save(argumentCaptor1.capture());
    }*/

    private static List<DisplayAttributeDto> getHolderDisplayAttributes(String gender, String age, String firstName) {
        List<DisplayAttributeDto> displayAttributeDtoList = new ArrayList<>();
        if (StringUtils.isBlank(firstName)) {
            displayAttributeDtoList.add(getAttributes("GENDER", gender));
            displayAttributeDtoList.add(getAttributes("AGE", age));
        } else {
            displayAttributeDtoList.add(getAttributes("FIRST_NAME", firstName));
        }
        return displayAttributeDtoList;
    }


    @Test
    void test_assignAttendeeNumberForPurchaseTicketForPurchaser_throwExeption() throws JAXBException {
        //Set up
        TicketBookingDto ticketBookingDto = new TicketBookingDto();
        ticketBookingDto.setHasholderattributes(true);
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        //Mock

        when(ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(any(),any(),any())).thenThrow(JAXBException.class);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.assignAttendeeNumberForPurchaseTicket(event, ticketBookingDto, Collections.singletonList(eventTickets));
    }

    private AttributeKeyValueDto getPurchaseAttributes(String key, String value) {
        AttributeKeyValueDto attributeKeyValueDto = new AttributeKeyValueDto();
        attributeKeyValueDto.setKey(key);
        attributeKeyValueDto.setValue(value);
        return attributeKeyValueDto;
    }

    @Test
    void test_deleteSequence_success() {
        //Setup
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        autoAssignedAttendeeNumbers.setId(sequenceId);
        //Mock
        when(eventTicketsCommonRepo.getEventTicketIdsByAutoAssignedAttendeeNumberId(sequenceId)).thenReturn(Collections.singletonList(1L));
        doNothing().when(attendeeSequenceNumberService).deleteByEventTicketIds(Collections.singletonList(1L));
        doNothing().when(eventTicketsCommonRepo).updateEventTicketForAutoAssignedAttendeeNumber(autoAssignedAttendeeNumbers.getId());
        doNothing().when(autoAssignedAttendeeNumbersRepository).deleteById(autoAssignedAttendeeNumbers.getId());
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.deleteAutoAssignedAttendeeNumber(sequenceId);
    }
    @Test
    void test_deleteSequenceWithNullEventTickets_success() {
        //Setup
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        autoAssignedAttendeeNumbers.setId(sequenceId);
        //Mock
        when(eventTicketsCommonRepo.getEventTicketIdsByAutoAssignedAttendeeNumberId(sequenceId)).thenReturn(Collections.emptyList());
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.deleteAutoAssignedAttendeeNumber(sequenceId);
    }

    @Test
    void test_unAssignSequence_throwSequenceNotExist() {
        //Mock
        doReturn(Optional.empty()).when(eventTicketsRepository).findById(any());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.unAssignSequence(eventTickets.getId(), event));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.SEQUENCE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_unAssignSequence_success() {
        //Mock
        doReturn(Optional.of(eventTickets)).when(eventTicketsRepository).findById(any());
        doNothing().when(attendeeSequenceNumberService).updateSequenceSetModified(eventTickets, null);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.unAssignSequence(eventTickets.getId(), event);
    }

    //TODO: Mockito verify
    /*@Test
    void test_getCustomAttributeForSequenceForSingleTicketType_success() {
        //Setup
        TicketSettingGetDto ticketSettingDto = new TicketSettingGetDto();
        ticketSettingDto.setHolderAttribute(ticketing.getCollectTicketHolderAttributes());
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderEventTicketTypeId("1");
        ticketHolderRequiredAttributes.setAttributeValueType();
        List<String> requiredAttributes = new ArrayList<>();
        requiredAttributes.add(Constants.AGE);
        requiredAttributes.add(Constants.GENDER);

        //Mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketRequiresAttributesRepo.customAttributeForSequence(event, ticketSettingDto.getHolderAttribute(), requiredAttributes)).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.getCustomAttributeForSequence("1", event);
    }*/

    @Test
    void test_getCustomAttributeForSequenceForMultiTicketType_success() {
        //Setup
        TicketSettingGetDto ticketSettingDto = new TicketSettingGetDto();
        ticketSettingDto.setHolderAttribute(ticketing.getCollectTicketHolderAttributes());
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId("1");
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId("1");
        List<String> requiredAttributes = new ArrayList<>();
        requiredAttributes.add(Constants.AGE);
        requiredAttributes.add(Constants.GENDER);
        //Mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketRequiresAttributesRepo.customAttributeForSequence(event, ticketSettingDto.getHolderAttribute(), requiredAttributes)).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.getCustomAttributeForSequence("1,2,5,4", event);
    }

    @Test
    void test_getAttendeeSequences_success() {
        //Setup
        List<AttendeeSequenceNumbersDto> attendeeSequences = new ArrayList<>();
        attendeeSequences.add(getAttendeeSequenceNumbersDto());
        AttendeeSequenceNumbersDto assignedAttendeeNumbersDto = new AttendeeSequenceNumbersDto(1L, 1L,"TEST","LAST","AUTO",1,"FIRST-SEQUENCE",  0);

        attendeeSequences.add(assignedAttendeeNumbersDto);
        AttendeeSequenceNumbersDto assignedAttendeeNumbersDto1 = new AttendeeSequenceNumbersDto(1L, 1L,"TEST","LAST","AUTO",1,"FIRST-SEQUENCE",  1);
        attendeeSequences.add(assignedAttendeeNumbersDto1);
        attendeeSequences.add(new AttendeeSequenceNumbersDto(1L, 1L,"TEST","LAST","AUTO",1,"FIRST-SEQUENCE",  1));
        attendeeSequences.add(new AttendeeSequenceNumbersDto(1L, 1L,"TEST","LAST","AUTO",null,"FIRST-SEQUENCE",  1));
        //Mock
        when(eventTicketsRepository.getAttendeeWithAssignedSequences(event, TicketStatus.CANCELED)).thenReturn(attendeeSequences);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.getAttendeeWithAssignedSequences(event);
        assertEquals(5, attendeeSequences.size());
    }

    public static Object[] getEditSequenceParameters() {
        return new Object[]{
                new Object[]{1L,null},
                new Object[]{null,4}
        };
    }

    @ParameterizedTest
    @MethodSource("getEditSequenceParameters")
    void test_editManuallySequenceNumber_throwSequenceNotExist(Long eventTicketId,Integer number ) {
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.editManuallySequenceNumber(eventTicketId,number,1L, event));

        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.SEQUENCE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_editManuallySequenceNumber_success() {
        //Setup
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        autoAssignedAttendeeNumbers.setId(sequenceId);
        //Mock
        when(attendeeSequenceNumberService.isSequenceNumberExist(Collections.singletonList(4), event)).thenReturn(false);
        doReturn(Optional.of(eventTickets)).when(eventTicketsRepository).findById(any());
        doReturn(Optional.of(autoAssignedAttendeeNumbers)).when(autoAssignedAttendeeNumbersRepository).findById(any());
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.editManuallySequenceNumber(1L,4, 1L,event);
    }
//
    @Test
    void test_editManuallySequenceNumber_throwSequenceNumberAlreadyExist() {
        //Setup
        AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers(event, "FIRST-SEQUENCE", 1, 1, "1,2,5-10", "1", "Male", 1, 50);
        //Mock
        when(attendeeSequenceNumberService.isSequenceNumberExist(Collections.singletonList(4), event)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> autoAssignedAttendeeNumbersServiceImpl.editManuallySequenceNumber(1L,4,1L, event));
        assertEquals(NotAcceptableException.AttendeesSequenceExceptionMsg.SEQUENCE_NUMBER_ALREADY_ASSIGNED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getSequences_success() {
        List<AutoAssignedAttendeeNumbersSettingsDto> AutoAssignedAttendeeNumbersSettingsDtoList = new ArrayList<>();
        AutoAssignedAttendeeNumbersSettingsDtoList.add(getAutoAssignedAttendeeNumbersSettingsDto());
        when(ticketingTypeCommonRepo.getTicketTypeNameByIds(Collections.singletonList(1L))).thenReturn(Collections.singletonList("NEW-TICKET"));
        when(autoAssignedAttendeeNumbersRepository.getAutoAssignedAttendeeNumbers(event)).thenReturn(AutoAssignedAttendeeNumbersSettingsDtoList);
        //Execution
        autoAssignedAttendeeNumbersServiceImpl.getAutoAssignedAttendeeNumbers(event);
    }
}