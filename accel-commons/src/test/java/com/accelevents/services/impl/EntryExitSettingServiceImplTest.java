package com.accelevents.services.impl;

import com.accelevents.common.dto.EntryExitSettingsDTO;
import com.accelevents.domain.EntryExitSettings;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EntryExitSettingsRepository;
import com.accelevents.ro.entryexit.ROEntryExitSettingsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class EntryExitSettingServiceImplTest {

    @Mock
    private ROEntryExitSettingsRepository roRepo;

    @Mock
    private EntryExitSettingsRepository settingsRepo;

    @InjectMocks
    private EntryExitSettingServiceImpl service;

    private User user;
    private Event event;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        user = new User();
        user.setUserId(1L);
        event = new Event();
        event.setEventId(100L);
    }

    @Test
    void testGetEntryExitSettings_WhenExists() {
        EntryExitSettings existing = new EntryExitSettings();
        existing.setEventId(event.getEventId());

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(existing));

        EntryExitSettingsDTO result = service.getEntryExitSettings(user, event);

        assertNotNull(result);
    }

    @Test
    void testGetEntryExitSettings_WhenNotExists() {
        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.empty());
        when(settingsRepo.save(any(EntryExitSettings.class))).thenAnswer(i -> i.getArgument(0));

        EntryExitSettingsDTO result = service.getEntryExitSettings(user, event);

        assertNotNull(result);

    }

    @Test
    void testUpsertEntryExitSettings_CreateNew() {
        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.empty());
        when(settingsRepo.save(any(EntryExitSettings.class))).thenAnswer(i -> i.getArgument(0));

        EntryExitSettingsDTO input = new EntryExitSettingsDTO();
        input.setEntryErrorMessage("entry error");
        input.setExitErrorMessage("exit error");

        EntryExitSettingsDTO result = service.upsertEntryExitSettings(input, user, event);

        assertNotNull(result);
    }

    @Test
    void testDeleteEntryExitSettings() {
        EntryExitSettings setting = new EntryExitSettings();
        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(setting));

        service.deleteEntryExitSettings(user, event);

        assertEquals(RecordStatus.DELETE, setting.getRecStatus());
    }

    @Test
    void testValidation_ThrowsInvalidPinException() {
        EntryExitSettingsDTO dto = new EntryExitSettingsDTO();
        dto.setRequiredPin(true);
        dto.setPassword("12a"); // Invalid PIN
        dto.setEntryErrorMessage("Entry error");
        dto.setExitErrorMessage("Exit error");

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });
    }

    @Test
    void shouldThrowIfPinRequiredButMissing() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setPassword(null);

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.PIN_REQUIRED_TO_ENABLE_PIN.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldThrowIfPinLengthIsNot4() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setPassword("123");

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_ENTRY_EXIT_PIN_LENGTH.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldThrowIfPinHasNonNumericChars() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setPassword("12a4");

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_ENTRY_EXIT_PIN.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldThrowIfPinIsSetButRequiredPinIsFalse() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setRequiredPin(false);

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.ENABLE_PIN_REQUIRED_TO_SET_PIN.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldThrowIfEntryErrorMessageIsBlank() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setEntryErrorMessage("");

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.ENTRY_ERROR_MESSAGE_IS_REQUIRED.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldThrowIfExitErrorMessageIsBlank() {
        EntryExitSettingsDTO dto = baseValidDto();
        dto.setExitErrorMessage("");

        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(new EntryExitSettings()));

        NotAcceptableException ex = assertThrows(NotAcceptableException.class, () -> {
            service.upsertEntryExitSettings(dto, user, event);
        });

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.EXIT_ERROR_MESSAGE_IS_REQUIRED.getErrorMessage(), ex.getMessage());
    }

    @Test
    void shouldPassValidationWithCorrectData() {
        EntryExitSettingsDTO dto = baseValidDto();

        EntryExitSettings savedEntity = new EntryExitSettings();
        when(roRepo.findByEventId(event.getEventId())).thenReturn(Optional.of(savedEntity));
        when(settingsRepo.save(any(EntryExitSettings.class))).thenReturn(savedEntity);

        assertDoesNotThrow(() -> service.upsertEntryExitSettings(dto, user, event));
    }

    private EntryExitSettingsDTO baseValidDto() {
        EntryExitSettingsDTO dto = new EntryExitSettingsDTO();
        dto.setRequiredPin(true);
        dto.setPassword("1234");
        dto.setEntryErrorMessage("Entry message");
        dto.setExitErrorMessage("Exit message");
        return dto;
    }

}
