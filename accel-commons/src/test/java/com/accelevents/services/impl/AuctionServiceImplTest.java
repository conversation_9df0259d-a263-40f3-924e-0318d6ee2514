package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.*;
import com.accelevents.common.dto.ItemInstructionDto;
import com.accelevents.common.dto.ItemResponseContainer;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.event.services.EventEndingService;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.perfomance.dto.AuctionBidAndItemAndBidderDto;
import com.accelevents.perfomance.dto.AuctionBidData;
import com.accelevents.perfomance.dto.AuctionItemWrapper;
import com.accelevents.perfomance.dto.ItemAuctionBidDto;
import com.accelevents.repositories.AuctionBidRepository;
import com.accelevents.repositories.AuctionRepository;
import com.accelevents.repositories.ItemDistributionDetailsRepository;
import com.accelevents.repositories.ItemRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.TimeZoneUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AuctionServiceImplTest {

	@Spy
	@InjectMocks
	private AuctionServiceImpl auctionServiceImpl;

	@Mock
	private ItemCategoryService itemCategoryService;
	@Mock
	private ItemService itemService;
	@Mock
    private EventDesignDetailService eventDesignDetailService;
	@Mock
	private AuctionBidRepository auctionBidRepository;
	@Mock
    private PhoneNumberService phoneNumberService;
	@Mock
	private EventChecklistService eventChecklistService;
	@Mock
	private ItemImgLocationsService itemImgLocationsService;
	@Mock
	private AuctionService auctionService;
	@Mock
	private UserService userService;
    @Mock
    private ROUserService roUserService;
	@Mock
	private TimeZoneService timeZoneService;
	@Mock
    private ItemRepository itemRepository;
	@Mock
	private EventService eventService;
    @Mock
    private ROEventService roEventService;
	@Mock
	private TextMessageUtils textMessageUtils;
	@Mock
	private TwilioTextMessagePrepareService twilioTextMessagePrepareService;
	@Mock
	private PaymentService paymentService;
	@Mock
	private AuctionBidService auctionBidService;
	@Mock
    private EventEndingService eventEndingService;
	@Mock
	private AuctionRepository auctionRepository;
	@Mock
	private WinnerService winnerService;
	@Mock
	private FavoriteItemService favoriteItemService;
	@Mock
	private ItemDistributionDetailsRepository itemDistributionDetailsRepository;
	@Mock
	private ItemDistributionService itemDistributionService;
	@Mock
	private ClearAPIGatewayCache clearAPIGatewayCache;
	@Mock
    private EventRepoService eventRepoService;
	@Mock
    private JoinPaymentItemService joinPaymentItemService;
	@Mock
    private ROStripeService roStripeService;
	@Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private ImageConfiguration imageConfiguration;
    @Mock
    private ItemOutbidGraphQLHandler itemOutbidGraphQLHandler;

	private Event event;
	private Auction auction;
	private User user;
	private Winner winner;
    private Double bidIncrement = 75d;
    private long auctionId = 1;
    private String category="category";
    List<Winner> winnerList=null;
    private WhiteLabel whiteLabel;

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);

		auction=new Auction();
		user = new User();
		user.setUserId(1L);
		user.setLastName("jon");
		user.setLastName("kaz");
		user.setEmail("<EMAIL>");
		user.setCountry("INDIA");
		user.setCityOrProvidence("Ahmedabad");
		user.setState("Gujarat");
		user.setPhoneNumber(9898989898L);
		event = EventDataUtil.getEvent();
		winner = new Winner();
		winner.setUserId(user.getUserId());

        winnerList=new ArrayList<>();
        winner.setBidId(1L);
        winner.setEventId(event.getEventId());
        winner.setHasPaid(false);
        winner.setItemId(1L);
        winner.setModuleType(ModuleType.AUCTION);
        winner.setUserId(user.getUserId());
        winner.setWinnerId(1L);
        winner.setWinningTime(new Date());
        winnerList.add(winner);

        event = EventDataUtil.getEvent();
        event.setWhiteLabelId(1L);
        whiteLabel = new WhiteLabel();
        whiteLabel.setDefaultItemImage(imageConfiguration.getDefaultItem());
        event.setWhiteLabel(whiteLabel);

	}

	@Test
	void testItemCheckForAuctionNotActivated() {
		doReturn(null).when(auctionServiceImpl).findByEvent(any(Event.class));
		//when(auctionServiceImpl.findByEvent(any(Event.class))).thenReturn(null);

		AccelEventsPhoneNumber aePhoneNumber = null;
		Event event = new Event();
		Item item = null;
		boolean isStaff = false;

		String messageTobeSend = "MESSAGE";

		when(textMessageUtils.getAuctionModuleNotActiveMessage(any(Event.class))).thenReturn(messageTobeSend);

		String message = auctionServiceImpl.itemCheck(null, event, null, false);

		assertEquals(messageTobeSend, message);
	}

	@Test
	void testItemCheckForEndedAuction() {

		Auction auction = new Auction();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, -1);

		auction.setEndDate(calendar.getTime());

		doReturn(auction).when(auctionServiceImpl).findByEvent(any(Event.class));

		String messageTobeSend = "MESSAGE";
		when(textMessageUtils.getAuctionModuleEndedMessage(any(Event.class))).thenReturn(messageTobeSend);

		AccelEventsPhoneNumber aePhoneNumber = null;
		Event event = new Event();
		Item item = null;
		boolean isStaff = false;
		String message = auctionServiceImpl.itemCheck(null, event, null, false);

		assertEquals(messageTobeSend, message);
	}

	@Test
	void testItemCheckForAuctionNotActivatedAndNotStaff() {

		Auction auction = new Auction();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);

		auction.setEndDate(calendar.getTime());
		auction.setActivated(false);

		doReturn(auction).when(auctionServiceImpl).findByEvent(any(Event.class));

		String messageTobeSend = "MESSAGE";
		when(textMessageUtils.getAuctionModuleNotActiveMessage(any(Event.class))).thenReturn(messageTobeSend);

		AccelEventsPhoneNumber aePhoneNumber = null;
		Event event = new Event();
		Item item = null;
		boolean isStaff = false;
		String message = auctionServiceImpl.itemCheck(null, event, null, false);

		assertEquals(messageTobeSend, message);
	}

	@Test
	void testItemCheckForAuctionNotActivatedAndIsStaff() {

		Auction auction = new Auction();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);

		auction.setEndDate(calendar.getTime());
		auction.setActivated(false);

		doReturn(auction).when(auctionServiceImpl).findByEvent(any(Event.class));

		AccelEventsPhoneNumber aePhoneNumber = null;
		Event event = new Event();
		event.setCurrency(Currency.USD);
		Item item = null;
		boolean isStaff = true;

		String messageTobe = "MESSAGE";

		when(itemService.getBidIncrement(null, auction)).thenReturn(1d);
		when(textMessageUtils.getHighestBidderPriceCheckMessage(null, 1, null, event.getCurrency().getSymbol(),
				event)).thenReturn(messageTobe);

		String message = auctionServiceImpl.itemCheck(null, event, null, true);
		assertEquals(messageTobe, message);
	}

	@Test
	void testItemCheckForAuctionActivatedAndIsStaff() {

		Auction auction = new Auction();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);

		auction.setEndDate(calendar.getTime());
		auction.setActivated(true);

		doReturn(auction).when(auctionServiceImpl).findByEvent(any(Event.class));

		AccelEventsPhoneNumber aePhoneNumber = null;
		Event event = new Event();
		event.setCurrency(Currency.USD);
		Item item = null;
		boolean isStaff = true;

		String messageTobe = "MESSAGE";

		when(itemService.getBidIncrement(null, auction)).thenReturn(1d);
		when(textMessageUtils.getHighestBidderPriceCheckMessage(null, 1, null, event.getCurrency().getSymbol(),
				event)).thenReturn(messageTobe);

		String message = auctionServiceImpl.itemCheck(null, event, null, true);
		assertEquals(messageTobe, message);
	}

	@Test
	void testAuctionBidForSilenceAunctionDisable() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(false);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		User user = new User();
		boolean isStaff = false;
		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);

		verify(twilioTextMessagePrepareService, never()).sendOutBidNotification(any(Event.class), any(Item.class),
				any(User.class), anyInt(), anyBoolean());
	}

	@Test
	void testAuctionBidForAunctionEnded() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		User user = new User();
		boolean isStaff = false;

		Auction auction = new Auction();

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, -1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);
		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);

		verify(textMessageUtils).getAuctionModuleEndedMessage(event);
	}

	@Test
	void testAuctionBidForAunctionNotActivate() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		item.setBuyItNowPrice(1);
		item.setCurrentBid(1d);
		User user = new User();
		boolean isStaff = false;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);
		verify(textMessageUtils).getAuctionModuleNotActiveMessage(event);

	}

	@Test
	void testAuctionBidForAunctionActivated() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		item.setBuyItNowPrice(1);
		item.setCurrentBid(1d);
		User user = new User();
		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		List<Winner> winners= new ArrayList<>();
		Winner winner = new Winner();
		winners.add(winner);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);
		when(winnerService.findByModuleTypeAndItemId(any(),anyLong())).thenReturn(winners);

		auction.setActivated(true);
		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		verify(textMessageUtils, never()).getAuctionModuleNotActiveMessage(null);

		auction.setActivated(false);
		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		verify(textMessageUtils, never()).getAuctionModuleNotActiveMessage(null);
	}

	@Test
	void testAuctionBidForItemAlreadyPurchased() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		item.setBuyItNowPrice(1);
		item.setCurrentBid(1d);

		User user = new User();
		boolean isStaff = false;

		Auction auction = new Auction();
		auction.setActivated(true);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		List<Winner> winners= new ArrayList<>();
		Winner winner = new Winner();
		winners.add(winner);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);
		when(winnerService.findByModuleTypeAndItemId(any(),anyLong())).thenReturn(winners);

		auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);
		verify(textMessageUtils).getItemAlreadyPurchasedMessage(event);

	}

	@Test
	void testAuctionBidBelowAuctionBid() {

		String body = "";
		Event event = new Event();
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		Item item = new Item();
		item.setBuyItNowPrice(0);
		item.setCurrentBid(1d);

		User user = new User();
		boolean isStaff = false;

		Auction auction = new Auction();
		auction.setActivated(true);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String noneEmptyMessage = "MESSAGE";
		when(textMessageUtils.getBelowAuctionBidMessage(twilioMessage.getEvent(), item, messageAction.getAmount(),
				bidIncrement)).thenReturn(noneEmptyMessage);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);
		assertEquals(noneEmptyMessage, message);

	}

	@Test
	void testAuctionBidConfirmationOrSuccessBidMessage() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(2);

		Item item = new Item();
		item.setBuyItNowPrice(0);
		item.setCurrentBid(1d);

		User user = new User();
		user.setUserId(1L);

		item.setCurrentHighBidder(user);

		boolean isStaff = false;

		Auction auction = new Auction();
		auction.setActivated(true);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);
		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String emptyMessage = "";
		when(textMessageUtils.getBelowAuctionBidMessage(twilioMessage.getEvent(), item, messageAction.getAmount(),
				bidIncrement)).thenReturn(emptyMessage);

		Payment payment = new Payment();
		Optional<Payment> optPayment = Optional.ofNullable(payment);
		when(paymentService.findByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId()))
				.thenReturn(optPayment);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidConfirmationOrSuccessBidMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);
		when(roEventService.getLanguageCodeByUserOrEvent(user,null)).thenReturn("EN");

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);
		assertEquals(messageToBeSend, message);

		verify(twilioTextMessagePrepareService).sendOutBidNotification(twilioMessage.getEvent(), item, user,
				messageAction.getAmount(), false);

		item.setBuyItNowPrice(3);
		message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, false);
		assertEquals(messageToBeSend, message);

		verify(twilioTextMessagePrepareService, atLeast(2)).sendOutBidNotification(twilioMessage.getEvent(), item, user,
				messageAction.getAmount(), false);

	}

	@Test
	void testAuctionBidBuyItNowFirBidConfirmed() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("First Name");

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
        assertTrue(captor.getValue().isConfirmed());
	}

	@Test
	void getAllAuctionBidsReturnNumberOfWinnersWithGreenCheckMarkNextToTheTopNumberOfBiddersWhoCanWinTheItemWithRightParameters(){
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");
		event.setAuctionId(1);

		Item item = new Item();
		item.setId(1L);
		item.setItemShortName("ST");
		item.setNumberOfWinners(3);

		User user1= new User();
		user1.setUserId(1L);

		AuctionBid bid = new AuctionBid(user1,1,item,100d, BiddingSource.SMS,true);

		List<AuctionBid> bids = new ArrayList<>();
		bids.add(bid);

		List<AllBidderDto> actualBidDetails = new ArrayList<>();
		actualBidDetails.add(new AllBidderDto(bid.getAmount(),bid.getUser(), TimeZoneUtil.getDateInLocal(new Date(), event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM),true));

		when(itemService.getItemByModuleType(event, "WAT", ModuleType.AUCTION)).thenReturn(Optional.of(item));
		when(auctionBidRepository.getAllBidsByItemAndIsDeletedOrderByAmountDesc(item, false)).thenReturn(bids);

		List<AllBidderDto> expactedbidDetails = auctionServiceImpl.getAllAuctionBids(event,"WAT");

		assertEquals(expactedbidDetails.get(0).isEligibleWinner(),actualBidDetails.get(0).isEligibleWinner());
	}

	@Test
	void getAuctionItemDtoReturnNumber_of_winnersWithRightParameters(){
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");
		event.setAuctionId(1);

		Item item = new Item();
		item.setId(1L);
		item.setItemShortName("ST");
		item.setNumberOfWinners(10);

		User user = new User();
		user.setUserId(1L);

		Auction auction = new Auction();
		auction.setActivated(false);
		auction.setId(1L);
		auction.setHighestBidderShow(true);

		AuctionBid highestBid = new AuctionBid(user,1,item,100d,BiddingSource.SMS,true);

		FavoriteItem favoriteItem = new FavoriteItem(1L, 1L);



		when(itemService.getItemByModuleType(any(),any(),any())).thenReturn(Optional.of(item));
		when(auctionBidRepository.findFirstByAuctionIdAndItemAndIsDeletedOrderByAmountDesc(auction.getId(),item, false)).thenReturn(highestBid);
		when(itemImgLocationsService.findByItem(any())).thenReturn(new ArrayList<ItemImgLocations>());
		when(auctionRepository.findAuctionByEventId(event.getEventId())).thenReturn(auction);
		when(itemService.getBidIncrement(any(),any())).thenReturn(75d);
		when(favoriteItemService.getFavItemByUserIdAndItemId(any(),any())).thenReturn(Optional.of(favoriteItem));
		AuctionItemBidderDto dto = auctionServiceImpl.getAuctionItemDto(event,"WAT", user);
		assertEquals(10,dto.getNumberOfWinners());


	}

	@Test
	void addAuctionItemSaveAuctionItemWithRightParameters(){
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");
		event.setAuctionId(1);

		String body = "";

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		Item item = new Item();
		item.setId(1L);
		item.setItemShortName("ST");

		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1,ModuleType.AUCTION,"cat")).thenReturn(Optional.empty());
		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);
		when(itemService.getItemFromItemDto(any(),any(),any())).thenReturn(item);
		when(itemService.savewithsequence(event,item)).thenReturn(item);
		when(itemService.savewithsequence(event,item)).thenReturn(item);
		when(eventChecklistService.findByEvent(event)).thenReturn(new EventChecklist());

		AuctionItemUpdateDto auctionItemDto = new AuctionItemUpdateDto();
		auctionItemDto.setBidIncrement(1d);
		auctionItemDto.setNumberOfWinners(2);
		auctionItemDto.setCategory("cat");
        auctionItemDto.setBuyItNowPrice(2);
        auctionItemDto.setStartingBid(1);
		Item item1 = auctionServiceImpl.addAuctionItem(event,auctionItemDto);
		assertEquals(2,item1.getNumberOfWinners());

	}
	//@Test
	void getAuctionPerformanceDataAllbidsPaidPayment(){
		Set<AuctionItemBids> resultSet = new HashSet<>();

		long auctionId = 1;
		AuctionBidAndItemAndBidderDto auctionBidAndItemAndBidderDto = new AuctionBidAndItemAndBidderDto(1L,
				"ITEM-NAME","IAC",1,1000,500,1L,50d,true,new Date("2018/01/01 12:00"),1L,"F-Name","L-Name");
		ItemAuctionBidDto itemAuctionBidDto = new ItemAuctionBidDto(1L, 1,1L,1L,true);
		Event event = new Event();
		event.setEventId(10L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");
		event.setAuctionId(auctionId);
		event.setCurrency(Currency.USD);

		Auction auction = new Auction();
		when(auctionRepository.findAuctionByEventId(event.getEventId())).thenReturn(auction);
		when(itemDistributionDetailsRepository.countItemDistributionBySourceIdsAndStatus(Collections.singletonList(1L), ModuleType.AUCTION, true)).thenReturn(BigInteger.ONE);
		when(auctionBidRepository.findItemIdAndAuctionBidIdByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(anyLong(),any(),anyBoolean())).thenReturn(Collections.singletonList(itemAuctionBidDto));
		when(auctionBidRepository.findAuctionBidAndItemAndBidderByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(Collections.singletonList(1L)))
				.thenReturn(Collections.singletonList(auctionBidAndItemAndBidderDto));
		AuctionItemWrapper auctionItemWrapper = auctionServiceImpl.getAuctionPerformanceData(event,false);
		assertTrue(auctionItemWrapper.getAuctionItems().get(0).getPaid());
		assertTrue(auctionItemWrapper.getAuctionItems().get(0).isItemDistributedToAllWinner());

	}
	@Test
	void updateHostSettingsForSuccessfullSave(){
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");

		String body = "";

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		User user = new User();
		AuctionHostSettingsDto auctionHostSettingsDto = new AuctionHostSettingsDto();
		auctionHostSettingsDto.setDefaultBidIncrement(1);
		auctionHostSettingsDto.setAllowMultipleWinnersPerItem(true);
		auctionHostSettingsDto.setUserTime("2018/01/01 12:00");
		auctionHostSettingsDto.setEventTimeZone("Etc/UCT");

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);
		when(roUserService.isSuperAdminUser(user)).thenReturn(true);
		when(eventChecklistService.findByEvent(event)).thenReturn(new EventChecklist());
		ArgumentCaptor<Auction> captor = ArgumentCaptor.forClass(Auction.class);

		auctionServiceImpl.updateHostSettings(auctionHostSettingsDto,event,user);
        verify(auctionServiceImpl).saveAndCacheClear(captor.capture(), eq(event));
        assertTrue(captor.getValue().isAllowMultipleWinnersPerItem());

	}

	@Test
	void testgetHostPageSettingsGetAllowMultipleWinnersPerItemForAuctionPage(){
		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setTimezoneId("Australia Eastern Time (AET)");

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);


		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();

		Auction auction = new Auction();
		auction.setActivated(false);
		auction.setAllowMultipleWinnersPerItem(true);
		auction.setEndDate(auctionEndDate);
		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
		auction.setId(1L);
		auction.setEventId(1L);

		AuctionHostSettingsGetDto settings = new AuctionHostSettingsGetDto();
		settings.setAllowMultipleWinnersPerItem(auction.isAllowMultipleWinnersPerItem());

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		AuctionHostSettingsGetDto auctionHostSettingsGetDto = auctionServiceImpl.getHostPageSettings(event);

		assertEquals(auctionHostSettingsGetDto.isAllowMultipleWinnersPerItem(),settings.isAllowMultipleWinnersPerItem());

	}

	@Test
	void testAuctionBidBuyItNowFirBidNotConfirmedForNullName() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
        assertFalse(captor.getValue().isConfirmed());

	}

	@Test
	void testAuctionBidBuyItNowFirBidNotConfirmedForEmptyName() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("");

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
        assertFalse(captor.getValue().isConfirmed());

	}

	@Test
	void testAuctionBidBuyItNowFirBidNotConfirmedForCreditEnabledAndCCNotRequired() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setCreditCardEnabled(true);

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("");

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
        assertTrue(captor.getValue().isConfirmed());

	}

	@Test
	void testAuctionBidBuyItNowFirBidNotConfirmedForCreditEnabledAndCCRequiredAndPaymentNotAvailable() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(true);

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("");

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		String emptyMessage = "";

		Optional<Payment> optPayment = Optional.ofNullable(null);
		when(paymentService.findByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId()))
				.thenReturn(optPayment);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
		assertEquals( false,captor.getValue().isConfirmed());

	}

	@Test
	void testAuctionBidBuyItNowFirBidNotConfirmedForCreditEnabledAndCCRequiredAndPaymentAvailable() {

		String body = "";
		Event event = new Event();
		event.setEventId(1L);
		event.setSilentAuctionEnabled(true);
		event.setCreditCardEnabled(true);
		event.setCcRequiredForBidConfirm(true);

		AccelEventsPhoneNumber from = new AccelEventsPhoneNumber(event);
		TwilioMessage twilioMessage = new TwilioMessage(body, from, event);

		MessageAction messageAction = new MessageAction();
		messageAction.setAmount(6);

		Item item = new Item();
		item.setBuyItNowPrice(3);
		item.setCurrentBid(2d);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("");

		item.setCurrentHighBidder(user);

		boolean isStaff = true;

		Auction auction = new Auction();
		auction.setActivated(false);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		Date auctionEndDate = calendar.getTime();
		auction.setEndDate(auctionEndDate);

		when(auctionServiceImpl.findByEvent(twilioMessage.getEvent())).thenReturn(auction);

		Double bidIncrement = 1d;
		when(itemService.getBidIncrement(item, auction)).thenReturn(bidIncrement);

		Payment payment = new Payment();
		Optional<Payment> optPayment = Optional.ofNullable(payment);
		when(paymentService.findByUserIdAndEventId(user.getUserId(), twilioMessage.getEvent().getEventId()))
				.thenReturn(optPayment);

		String messageToBeSend = "MESSAGE";
		when(textMessageUtils.getAuctionBidBuyItNowItemMessage(twilioMessage.getEvent(), item, user,
				messageAction.getAmount())).thenReturn(messageToBeSend);

		ArgumentCaptor<AuctionBid> captor = ArgumentCaptor.forClass(AuctionBid.class);

		String message = auctionServiceImpl.auctionBid(twilioMessage, messageAction, item, user, true);
		assertEquals(messageToBeSend, message);

		verify(auctionBidService).save(captor.capture(),eq(event.getEventURL()));
		assertEquals( true,captor.getValue().isConfirmed());

	}

	@Test
	void test_updateInstructionForAuctionItemInstructionNOTFound(){

		Event event = new Event();
		event.setAuctionId(1);

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = null;
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
		//mock
		when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getAuctionId(), ModuleType.AUCTION)).thenReturn(null);

		//execution
		String actualResult = auctionServiceImpl.updateInstructionForAuction(event, ph,languageMap,user);
		assertTrue(Constants.STRING_EMPTY.equalsIgnoreCase(actualResult));
	}

	@Test
	void test_updateInstructionForAuction_ItemInstructionFound(){

		Event event = new Event();
		event.setAuctionId(1);
		event.setCurrency(Currency.USD);

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = new ItemInstructionDto();
		itemInstructionDto.setCode("AAA");
		itemInstructionDto.setStartingBid(50);
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
		//mock
		when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getAuctionId(), ModuleType.AUCTION)).thenReturn(itemInstructionDto);

		//execution
		String actualResult = auctionServiceImpl.updateInstructionForAuction(event, ph,languageMap,user);
		assertTrue("Bid here or text Your Bid To: 123123123 with the item's three letter code and bid amount ex. AAA50".equalsIgnoreCase(actualResult));
	}

	@Test
	void test_saveItemsDistributionDetails()
	{
		//setup
		List<Long> bidIds = new ArrayList<>();
		bidIds.add(1L);
		boolean distributed = true;

		Event event = new Event();
		event.setAuctionId(1);
		event.setCurrency(Currency.USD);

		User user = new User();
		user.setUserId(1L);
		user.setFirstName("jon");
		user.setLastName("kaz");
		user.setEmail("<EMAIL>");

		//mock
		Mockito.doNothing().when(itemDistributionService).saveItemsDistributionDetails(anyList(), any(), anyBoolean(), anyLong());

		//Execution
		auctionServiceImpl.saveItemsDistributionDetails(bidIds, true, event, user);

		//Assertion
		verify(itemDistributionService).saveItemsDistributionDetails(anyList(), any(), anyBoolean(), anyLong());
	}

	@Test
	void test_getListOfWinnersForEvent_withAllItemsPaidTrueAndHasPaidFalse(){
		//setup
		boolean isItemDistributed = false;
		double totalAmountPaid = 100d;
		int totalItemsWon = 1;
		boolean allItemsPaid = true;
		String winnerName = user.getFirstName() + " " + user.getLastName();
		Set<Long> userIds =new HashSet<>();
		userIds.add(user.getUserId());
		AuctionBid auctionBid = new AuctionBid();
		auctionBid.setAmount(100d);
		auctionBid.setHasPaid(false);

		WinnerDto winnerDto = new WinnerDto(user.getUserId(), totalAmountPaid, totalItemsWon, winnerName, true, false, user.getEmail(),
				user.getPhoneNumber(), user.getAddress1(), user.getAddress2(), user.getCityOrProvidence(), user.getState(), user.getCountry());

		//mock
		when(winnerService.findUserIdByEventIdAndModuleType(anyLong(), any())).thenReturn(userIds);
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
		when(winnerService.findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong())).thenReturn(Collections.singletonList(winner));
		when(auctionBidService.getAuctionBidByIdAndNotRefunded(anyLong())).thenReturn(Optional.of(auctionBid));
		when(itemDistributionService.isAllItemDistributed(anyList(), any())).thenReturn(false);

		//Execution
		List<WinnerDto> winnerDtos = auctionServiceImpl.getListOfWinnersForEvent(event);

		//Assertion
		verify(winnerService).findUserIdByEventIdAndModuleType(anyLong(), any());
		verify(roUserService).getUserById(anyLong());
		verify(winnerService).findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong());
		verify(auctionBidService).getAuctionBidByIdAndNotRefunded(anyLong());
		verify(itemDistributionService).isAllItemDistributed(anyList(), any());

		assertFalse(winnerDtos.get(0).isAllItemsPaid());
		verifyWinnerDto(winnerDto, winnerDtos);
	}

	@Test
	void test_getListOfWinnersForEvent_withAllItemsPaidTrueAndHasPaidTrue(){
		//setup
		User user = new User();
		user.setUserId(1L);
		user.setLastName("jon");
		user.setLastName("kaz");
		user.setEmail("<EMAIL>");
		user.setCountry("INDIA");
		user.setCityOrProvidence("Ahmedabad");
		user.setState("Gujarat");
		user.setPhoneNumber(9898989898L);
		Event event = EventDataUtil.getEvent();
		Winner winner = new Winner();
		winner.setUserId(user.getUserId());
		boolean isItemDistributed = false;
		double totalAmountPaid = 100d;
		int totalItemsWon = 1;
		boolean allItemsPaid = true;
		String winnerName = user.getFirstName() + " " + user.getLastName();
		Set<Long> userIds =new HashSet<>();
		userIds.add(user.getUserId());
		AuctionBid auctionBid = new AuctionBid();
		auctionBid.setAmount(100d);
		auctionBid.setHasPaid(true);

		WinnerDto winnerDto = new WinnerDto(user.getUserId(), totalAmountPaid, totalItemsWon, winnerName, true, false, user.getEmail(),
				user.getPhoneNumber(), user.getAddress1(), user.getAddress2(), user.getCityOrProvidence(), user.getState(), user.getCountry());

		//mock
		when(winnerService.findUserIdByEventIdAndModuleType(anyLong(), any())).thenReturn(userIds);
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
		when(winnerService.findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong())).thenReturn(Collections.singletonList(winner));
		when(auctionBidService.getAuctionBidByIdAndNotRefunded(anyLong())).thenReturn(Optional.of(auctionBid));
		when(itemDistributionService.isAllItemDistributed(anyList(), any())).thenReturn(false);

		//Execution
		List<WinnerDto> winnerDtos = auctionServiceImpl.getListOfWinnersForEvent(event);

		//Assertion
		verify(winnerService).findUserIdByEventIdAndModuleType(anyLong(), any());
		verify(roUserService).getUserById(anyLong());
		verify(winnerService).findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong());
		verify(auctionBidService).getAuctionBidByIdAndNotRefunded(anyLong());
		verify(itemDistributionService).isAllItemDistributed(anyList(), any());

		assertTrue(winnerDtos.get(0).isAllItemsPaid());
		verifyWinnerDto(winnerDto, winnerDtos);
	}

	private void verifyWinnerDto(WinnerDto winnerDto, List<WinnerDto> winnerDtos) {
		assertEquals(winnerDtos.get(0).getWinnerId(), winnerDto.getWinnerId());
		assertEquals(Double.valueOf(winnerDtos.get(0).getTotalPaidAmount()),Double.valueOf(winnerDto.getTotalPaidAmount()));
		assertEquals(winnerDtos.get(0).getTotalItemsWon(), winnerDto.getTotalItemsWon());
		assertEquals(winnerDtos.get(0).getWinnerName(), winnerDto.getWinnerName());
		assertEquals(winnerDtos.get(0).isAllItemsDistributed(), winnerDto.isAllItemsDistributed());
		assertEquals(winnerDtos.get(0).getWinnerEmail(), winnerDto.getWinnerEmail());
		assertEquals(winnerDtos.get(0).getWinnerPhoneNumber(), winnerDto.getWinnerPhoneNumber());
		assertEquals(winnerDtos.get(0).getWinnerAddress1(), winnerDto.getWinnerAddress1());
		assertEquals(winnerDtos.get(0).getWinnerAddress2(), winnerDto.getWinnerAddress2());
		assertEquals(winnerDtos.get(0).getCityOrProvidence(), winnerDto.getCityOrProvidence());
		assertEquals(winnerDtos.get(0).getState(), winnerDto.getState());
		assertEquals(winnerDtos.get(0).getCountry(), winnerDto.getCountry());
	}

	@Test
	void test_getListOfWinnersForEvent_withUserIsNotPresent(){
		//setup
		User user = new User();
		user.setUserId(1L);
		Event event = EventDataUtil.getEvent();
		Set<Long> userIds =new HashSet<>();
		userIds.add(user.getUserId());

		//mock
		when(winnerService.findUserIdByEventIdAndModuleType(anyLong(), any())).thenReturn(userIds);
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.empty());

		//Execution
		List<WinnerDto> winnerDtos = auctionServiceImpl.getListOfWinnersForEvent(event);

		//Assertion
		verify(winnerService).findUserIdByEventIdAndModuleType(anyLong(), any());
		verify(roUserService).getUserById(anyLong());

		assertTrue(winnerDtos.isEmpty());
	}
	public static Object[] getAllowMultipleWinnersPerItem() {
		return new Object[]{
				new Object[] {true},
				new Object[] {false},
		};
	}

	@Test
	void test_getActiveItems_ItemAndUserAndHighestAuctionBidNull(){
		//setup

		String searchString = "search";
		List<Item> itemList = new ArrayList<>();
		Item item = new Item();
		item.setNumberOfWinners(0);
		itemList.add(item);
		Page<Item> items = new PageImpl<>(itemList);

		Auction auction = new Auction();
		auction.setHighestBidderShow(true);
		auction.setAllowMultipleWinnersPerItem(true);

		User user = EventDataUtil.getUser();

		//mock
		when(itemService.getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any())).thenReturn(items);
		doReturn(auction).when(auctionServiceImpl).find(anyLong());
		when(itemService.getBidIncrement(any(),any())).thenReturn(bidIncrement);
		when(auctionBidRepository.findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean())).thenReturn(Collections.emptyList());
		when(itemImgLocationsService.findByListItem(anyList())).thenReturn(Collections.emptyList());
		when(auctionBidRepository.countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList())).thenReturn(Collections.emptyList());
		when(winnerService.findByModuleTypeAndListItemId(any(),anyList())).thenReturn(Collections.emptyList());
		when(favoriteItemService.getFavItemByUserIdAndItemIdList(anyLong(),anyList())).thenReturn(Collections.emptyList());

		//execution
		ItemResponseContainer itemResponseContainer = auctionServiceImpl.getActiveItems(auctionId, searchString,category,10,1, user);

		//assert
		assertFalse(itemResponseContainer.getItems().isEmpty());
		assertEquals(itemResponseContainer.getTotalItems(),items.getTotalElements());
		assertTrue(itemResponseContainer.getItems().get(0).isPurchased());
		assertTrue(itemResponseContainer.getItems().get(0).isActive());
		assertFalse(itemResponseContainer.getItems().get(0).isFavoriteItem());
		assertFalse(itemResponseContainer.getItems().get(0).isLiveAuctionItem());

		verify(itemService).getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any());
		verify(auctionServiceImpl).find(anyLong());
		verify(itemService).getBidIncrement(any(),any());
		verify(auctionBidRepository).findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean());
		verify(itemImgLocationsService).findByListItem(anyList());
		verify(auctionBidRepository).countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList());
		verify(winnerService).findByModuleTypeAndListItemId(any(),anyList());
		verify(favoriteItemService).getFavItemByUserIdAndItemIdList(anyLong(),anyList());
	}

	@ParameterizedTest
	@MethodSource("getAllowMultipleWinnersPerItem")
	void test_getActiveItems_ItemAndUserAndHighestAuctionBidAndMultipleWinnersPerItem(boolean isAllowMultipleWinnersPerItem){
		//setup

		String searchString = "search";
		List<Item> itemList = new ArrayList<>();
		Item item = new Item();
		itemList.add(item);
		Page<Item> items = new PageImpl<>(itemList);

		User user = EventDataUtil.getUser();

		AuctionBid auctionBid = new AuctionBid();
		auctionBid.setItem(item);
		auctionBid.setAmount(100d);
		auctionBid.setUser(user);
		auctionBid.setRefunded(true);

		List<AuctionBid> totalAuctionBids = new ArrayList<>();
		totalAuctionBids.add(auctionBid);

		Auction auction = new Auction();
		auction.setHighestBidderShow(true);
		auction.setAllowMultipleWinnersPerItem(isAllowMultipleWinnersPerItem);

		//mock
		when(itemService.getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any())).thenReturn(items);
		doReturn(auction).when(auctionServiceImpl).find(anyLong());
		when(itemService.getBidIncrement(any(),any())).thenReturn(bidIncrement);
		when(auctionBidRepository.findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean())).thenReturn(totalAuctionBids);
		when(itemImgLocationsService.findByListItem(anyList())).thenReturn(Collections.emptyList());
		when(auctionBidRepository.countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList())).thenReturn(Collections.emptyList());
		when(winnerService.findByModuleTypeAndListItemId(any(),anyList())).thenReturn(Collections.emptyList());
		when(favoriteItemService.getFavItemByUserIdAndItemIdList(anyLong(),anyList())).thenReturn(Collections.emptyList());

		//execution
		ItemResponseContainer itemResponseContainer = auctionServiceImpl.getActiveItems(auctionId, searchString,category,10,1, user);

		//assert
		assertFalse(itemResponseContainer.getItems().isEmpty());
		assertEquals(itemResponseContainer.getTotalItems(),items.getTotalElements());
		assertTrue(itemResponseContainer.getItems().get(0).isActive());
		assertFalse(itemResponseContainer.getItems().get(0).isFavoriteItem());
		assertFalse(itemResponseContainer.getItems().get(0).isLiveAuctionItem());

		AuctionItemBidderDto auctionItemBidderDto = (AuctionItemBidderDto) itemResponseContainer.getItems().get(0);
		assertFalse(auctionItemBidderDto.isPurchased());
		assertEquals(auctionItemBidderDto.getNumberOfWinners(),isAllowMultipleWinnersPerItem ? item.getNumberOfWinners()-1 : 0);

		verify(itemService).getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any());
		verify(auctionServiceImpl).find(anyLong());
		verify(itemService).getBidIncrement(any(),any());
		verify(auctionBidRepository).findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean());
		verify(itemImgLocationsService).findByListItem(anyList());
		verify(auctionBidRepository).countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList());
		verify(winnerService).findByModuleTypeAndListItemId(any(),anyList());
		verify(favoriteItemService).getFavItemByUserIdAndItemIdList(anyLong(),anyList());
	}

	@Test
	void test_getActiveItems_ItemAndUserAndHighestAuctionBidAndMultipleWinnersPerItemAndRefundedFalse(){
		//setup

		String searchString = "search";
		List<Item> itemList = new ArrayList<>();
		Item item = new Item();
		itemList.add(item);
		Page<Item> items = new PageImpl<>(itemList);

		User user = EventDataUtil.getUser();

		AuctionBid auctionBid = new AuctionBid();
		auctionBid.setItem(item);
		auctionBid.setAmount(100d);
		auctionBid.setUser(user);
		auctionBid.setRefunded(false);

		List<AuctionBid> totalAuctionBids = new ArrayList<>();
		totalAuctionBids.add(auctionBid);

		Auction auction = new Auction();
		auction.setHighestBidderShow(true);
		auction.setAllowMultipleWinnersPerItem(true);

		//mock
		when(itemService.getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any())).thenReturn(items);
		doReturn(auction).when(auctionServiceImpl).find(anyLong());
		when(itemService.getBidIncrement(any(),any())).thenReturn(bidIncrement);
		when(auctionBidRepository.findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean())).thenReturn(totalAuctionBids);
		when(itemImgLocationsService.findByListItem(anyList())).thenReturn(Collections.emptyList());
		when(auctionBidRepository.countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList())).thenReturn(Collections.emptyList());
		when(winnerService.findByModuleTypeAndListItemId(any(),anyList())).thenReturn(Collections.emptyList());
		when(favoriteItemService.getFavItemByUserIdAndItemIdList(anyLong(),anyList())).thenReturn(Collections.emptyList());

		//execution
		ItemResponseContainer itemResponseContainer = auctionServiceImpl.getActiveItems(auctionId, searchString,category,10,1, user);

		//assert
		assertFalse(itemResponseContainer.getItems().isEmpty());
		assertEquals(itemResponseContainer.getTotalItems(),items.getTotalElements());
		assertTrue(itemResponseContainer.getItems().get(0).isActive());
		assertFalse(itemResponseContainer.getItems().get(0).isFavoriteItem());
		assertFalse(itemResponseContainer.getItems().get(0).isLiveAuctionItem());

		AuctionItemBidderDto auctionItemBidderDto = (AuctionItemBidderDto) itemResponseContainer.getItems().get(0);
		assertFalse(auctionItemBidderDto.isPurchased());

		verify(itemService).getActiveItems(anyLong(),any(),anyString(),any(),anyInt(),anyInt(),any());
		verify(auctionServiceImpl).find(anyLong());
		verify(itemService).getBidIncrement(any(),any());
		verify(auctionBidRepository).findAllBidsByAuctionIdAndItemListAndIsDeletedOrderByAmountDesc(anyLong(),anyList(),anyBoolean());
		verify(itemImgLocationsService).findByListItem(anyList());
		verify(auctionBidRepository).countAuctionBidIdsByAuctionIdAndUserAndItemList(anyLong(),any(),anyList());
		verify(winnerService).findByModuleTypeAndListItemId(any(),anyList());
		verify(favoriteItemService).getFavItemByUserIdAndItemIdList(anyLong(),anyList());
	}

    @Test
    void test_getByAuctionIdAndEventPayoutStatus_PayoutStatusCompleted_AuctionStatusWINNER_ANNOUNED() {
        //setup
        List<Long> auctionsIds  = new ArrayList<>();
        auctionsIds.add(1L);

        auction.setId(1L);
        auction.setEventId(10L);
        auction.setActivated(true);
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
        auction.setCategoryEnabled(false);
        auction.setHighestBidderShow(true);
        auction.setDefaultBidIncrement(1);
        auction.setEventPayoutStatus(EnumEventPayoutStatus.COMPLETED);
        auction.setExtendedBiddingWaitTime(1L);
        auction.setAllowMultipleWinnersPerItem(true);

        List<Auction> auctions=new ArrayList<>();
        auctions.add(auction);

        //mock
        when(auctionRepository.getByAuctionIdAndEventPayoutStatus(anyList(),any())).thenReturn(auctions);

        //execution
        List<Auction> auctionList=auctionServiceImpl.getByAuctionIdAndEventPayoutStatus(auctionsIds,EnumEventPayoutStatus.HOLD);

        //assert
        assertEquals(auctions,auctionList);
    }


    @Test
    void test_getBidNetSale_WithRightParam() {
	    //setup
	    List<Long> bidIds=new ArrayList<>();
	    bidIds.add(1L);

        BigDecimal bigDecimal=new BigDecimal(1);

	    //mock
        when(auctionBidRepository.sumBidNetSale(anyList())).thenReturn(bigDecimal);

        //execution
        double bidNetSale  =auctionServiceImpl.getBidNetSale(bidIds);

        //assert
        assertEquals(bidNetSale,bigDecimal.doubleValue(),bidNetSale);
    }


    @Test
    void test_createDuplicateAuctionItem() {
        //setup
	    Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        item.setImgLocations(new ArrayList<>());
        AuctionAllItemDto auctionAllItemDto=new AuctionAllItemDto( item.getId(), item.getName(), item.getCode(),
                item.getStartingBid(), item.getBuyItNowPrice(),
                item.getPosition(), StringUtils.isNotBlank(item.getDescription()),
                !item.getImgLocations().isEmpty());


	    //mock
        when(itemService.addDuplicateItem(1L, event, Constants.DUPLICATE_AUCTION_ITEM_DEFAULT_CODE)).thenReturn(item);

        //execution
        AuctionAllItemDto allItemDto=auctionServiceImpl.createDuplicateAuctionItem(1L,event);

        //assert
        assertEquals(auctionAllItemDto.getBN(),allItemDto.getBN());
        assertEquals(auctionAllItemDto.getCD(),allItemDto.getCD());
        assertEquals(auctionAllItemDto.isDS(),allItemDto.isDS());
        assertEquals(auctionAllItemDto.getNM(),allItemDto.getNM());
        assertEquals(auctionAllItemDto.getPN(),allItemDto.getPN(),allItemDto.getPN());
        assertEquals(auctionAllItemDto.getSB(),allItemDto.getSB());
        assertEquals(auctionAllItemDto.isIA(),allItemDto.isIA());
        assertEquals(auctionAllItemDto.isII(),allItemDto.isII());

    }


    @Test
    void test_getListOfWinnerBidsForEvent_AllBidsByAuctionIdAndItemAndIsRefundedOrder() {
        //setup
        List<Winner> winners= new ArrayList<>();
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        Winner winner = new Winner();
        winner.setBidId(1L);
        winners.add(winner);
        List<AuctionBid> auctionBids=new ArrayList<>();
        AuctionBid auctionBid=new AuctionBid();
        auctionBid.setUser(user);
        auctionBid.setAmount(12d);
        auctionBid.setAuctionId(1L);
        auctionBid.setItem(item);
        auctionBid.setConfirmed(true);
        auctionBid.setHasPaid(true);
        auctionBids.add(auctionBid);

        ItemDistributionDetails itemDistributionDetails=new ItemDistributionDetails();
        itemDistributionDetails.setDistributed(true);
        itemDistributionDetails.setModuleType(ModuleType.AUCTION);
        itemDistributionDetails.setNotes("notes");
        itemDistributionDetails.setSourceId(1L);
        itemDistributionDetails.setStaffUserId(1L);

        Payment payment=new Payment();
        payment.setStripeToken("test");
        payment.setStaffUserId(1L);
        payment.setCardIdOrPaymentMethodId("abc");
        payment.setPaymentId(1L);
        payment.setUserId(user.getUserId());
        payment.setEventId(event.getEventId());


        //mock
        when(winnerService.findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong())).thenReturn(winners);
        when(auctionBidService.getAuctionBidByIdAndNotRefunded(anyLong())).thenReturn(Optional.of(auctionBid));
        when(itemDistributionService.getItemDistributionDetails(anyLong(),any())).thenReturn(itemDistributionDetails);
        when(auctionBidRepository.getAllBidsByAuctionIdAndItemAndIsRefundedOrderByBidTimeDesc(1L,item,false)).thenReturn(auctionBids);
        when(phoneNumberService.getDisplayNumber(any())).thenReturn("test");
        doReturn(auction).when(auctionServiceImpl).find(1L);
        when(paymentService.hasCardLinked(anyLong(),anyLong())).thenReturn(true);
        when(paymentService.findByUserIdAndEventId(anyLong(),anyLong())).thenReturn(Optional.of(payment));
        when(joinPaymentItemService.checkTransactionType(any(),any(),anyString())).thenReturn(true);

        //execution
        List<WinnerAuctionBidData> winnerAuctionBidData=auctionServiceImpl.getListOfWinnerBidsForEvent(event,1L);

        //assert
        assertEquals(user.getEmail(),winnerAuctionBidData.get(0).getBidderEmail());
        assertEquals(auctionBid.getAmount().doubleValue(),winnerAuctionBidData.get(0).getBidAmount(),winnerAuctionBidData.get(0).getBidAmount());
        assertEquals(item.getCode(),winnerAuctionBidData.get(0).getItemCode());
        assertEquals(itemDistributionDetails.getNotes(),winnerAuctionBidData.get(0).getNotes());
        assertTrue(winnerAuctionBidData.get(0).isPaid());
        assertTrue(winnerAuctionBidData.get(0).isDistributed());
        assertTrue(winnerAuctionBidData.get(0).isPaid());

        verify(winnerService).findByEventIdAndModuleTypeAndUserId(anyLong(), any(), anyLong());
        verify(auctionBidService).getAuctionBidByIdAndNotRefunded(anyLong());
        verify(itemDistributionService,atLeast(2)).getItemDistributionDetails(anyLong(),any());
        verify(auctionBidRepository).getAllBidsByAuctionIdAndItemAndIsRefundedOrderByBidTimeDesc(anyLong(),any(),anyBoolean());
        verify(phoneNumberService).getDisplayNumber(any());
        verify(auctionServiceImpl,atLeastOnce()).find(1L);
        verify(paymentService).hasCardLinked(anyLong(),anyLong());
        verify(paymentService).findByUserIdAndEventId(anyLong(),anyLong());
        verify(joinPaymentItemService).checkTransactionType(any(),any(),anyString());

    }

    @Test
    void test_sendBuyerReceipt_ProcessingFeesToPurchaserTrue_SuccessSendBuyerReceipt() throws IOException {
	    //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        AuctionBid auctionBid=new AuctionBid();
        auctionBid.setUser(user);
        auctionBid.setAmount(12d);
        auctionBid.setAuctionId(1L);
        auctionBid.setItem(item);
        auctionBid.setConfirmed(true);
        auctionBid.setHasPaid(true);

        Stripe stripe=new Stripe();
        stripe.setProcessingFeesToPurchaser(true);
        stripe.setEvent(event);

        //mock
        when(auctionBidService.getAuctionBidByIdAndNotRefunded(anyLong())).thenReturn(Optional.of(auctionBid));
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        doReturn(auction).when(auctionServiceImpl).find(1L);

        ArgumentCaptor<Stripe> captorStripe = ArgumentCaptor.forClass(Stripe.class);
        //execution
        auctionServiceImpl.sendBuyerReceipt(1L,event);
        //assert
        verify(eventService).sendBuyerReciept(any(),any(),any(),any(),anyBoolean(),anyDouble(),captorStripe.capture());
        assertTrue(captorStripe.getValue().isProcessingFeesToPurchaser());
    }

    @Test
    void test_getItemDetail_WithRigtParam() {
	    //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        List<String> strings=new ArrayList<>();
        String test="test";
        strings.add(test);
        List<ItemImgLocations> itemImgLocations=new ArrayList<>();
        ItemImgLocations itemImgLocation=new ItemImgLocations();
        itemImgLocation.setItem(item);
        itemImgLocation.setPosition(100L);
        itemImgLocation.setImageLocations("test");
        itemImgLocations.add(itemImgLocation);

        Event event1 =  new Event();
        event1.setWhiteLabel(null);

        AuctionItemDetail auctionItemDetail=new AuctionItemDetail(item, strings, itemImgLocations, auction.isAllowMultipleWinnersPerItem());

        //mock
        when(itemService.getItemByModuleIdAndTypeAndCode(anyLong(),any(),anyString())).thenReturn(Optional.of(item));
        doReturn(auction).when(auctionServiceImpl).find(anyLong());
        when(itemCategoryService.findNameByModuleIdAndType(anyLong(),any())).thenReturn(strings);
        when(itemImgLocationsService.findByItem(any())).thenReturn(itemImgLocations);

        //execution
        AuctionItemDetail auctionItemDetail1=auctionServiceImpl.getItemDetail(1L , "Test",event1);

        //assert
        assertEquals(auctionItemDetail.getBidIncrement(),auctionItemDetail1.getBidIncrement());
        assertEquals(auctionItemDetail.getMarketValue(),auctionItemDetail1.getMarketValue());
        assertEquals(auctionItemDetail.getNumberOfWinners(),auctionItemDetail1.getNumberOfWinners());
        assertEquals(auctionItemDetail.isLiveAuctionItem(),auctionItemDetail.isLiveAuctionItem());

        verify(itemService).getItemByModuleIdAndTypeAndCode(anyLong(),any(),anyString());
        verify(auctionServiceImpl).find(anyLong());
        verify(itemCategoryService).findNameByModuleIdAndType(anyLong(),any());
        verify(itemImgLocationsService).findByItem(any());

    }


    @Test
    void test_getAllAuctionItems() {
	    //setup
        List<AuctionAllItemDto> auctionAllItemDtos=new ArrayList<>();
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        AuctionAllItemDto auctionAllItemDto=new AuctionAllItemDto( item.getId(), item.getName(), item.getCode(),
                item.getStartingBid(), item.getBuyItNowPrice(),
                item.getPosition(), StringUtils.isNotBlank(item.getDescription()),false);
        auctionAllItemDtos.add(auctionAllItemDto);
        Event event1 =  new Event();
        event1.setWhiteLabel(null);
        int page = 0, size = 10;
        Page<AuctionAllItemDto> auctionAllItemDtos1 = new PageImpl<>(auctionAllItemDtos);

        //mock
        when(itemService.getAllAuctionItemsDto(anyLong(),anyString(), any())).thenReturn(auctionAllItemDtos1);
        //execution
        DataTableResponse auctionAllItemResponseContainer=auctionServiceImpl.getAllAuctionItems(1L,"test",event1, page, size);
        //assert
        assertEquals(auctionAllItemDtos.size(),auctionAllItemResponseContainer.getRecordsTotal());
        assertEquals(auctionAllItemDtos,auctionAllItemResponseContainer.getData());

    }

    @Test
    void test_getAuctionItemDetailForPerformancePage() {
	    //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        List<AuctionBid> auctionBids=new ArrayList<>();
        AuctionBid auctionBid=new AuctionBid();
        auctionBid.setUser(user);
        auctionBid.setAmount(12d);
        auctionBid.setAuctionId(1L);
        auctionBid.setItem(item);
        auctionBid.setConfirmed(true);
        auctionBid.setHasPaid(true);
        auctionBids.add(auctionBid);
        AuctionItemBids auctionItemBids=new AuctionItemBids(item,auctionBids,auctionBid);
        ItemDistributionDetails itemDistributionDetails=new ItemDistributionDetails();
        itemDistributionDetails.setDistributed(true);
        itemDistributionDetails.setModuleType(ModuleType.AUCTION);
        itemDistributionDetails.setNotes("notes");
        itemDistributionDetails.setSourceId(1L);
        itemDistributionDetails.setStaffUserId(1L);

        Payment payment=new Payment();
        payment.setStripeToken("test");
        payment.setStaffUserId(1L);
        payment.setCardIdOrPaymentMethodId("abc");
        payment.setPaymentId(1L);
        payment.setUserId(user.getUserId());
        payment.setEventId(event.getEventId());
        //mock
        when(itemService.getItemByModuleIdAndTypeAndCode(anyLong(),any(),anyString())).thenReturn(Optional.of(item));
        doReturn(auction).when(auctionServiceImpl).find(anyLong());
        when(phoneNumberService.getDisplayNumber(any())).thenReturn("test");
        when(paymentService.hasCardLinked(anyLong(),anyLong())).thenReturn(true);
        when(paymentService.findByUserIdAndEventId(anyLong(),anyLong())).thenReturn(Optional.of(payment));
        when(joinPaymentItemService.checkTransactionType(any(),any(),anyString())).thenReturn(true);
        when(auctionBidRepository.getAllBidsByAuctionIdAndItemAndIsDeletedOrderByBidTimeDesc(anyLong(),any(),anyBoolean())).thenReturn(auctionBids);
        when(auctionBidRepository.findFirstByAuctionIdAndItemAndIsDeletedOrderByAmountDesc(anyLong(),any(),anyBoolean())).thenReturn(auctionBid);

        when(itemDistributionService.getItemDistributionDetails(anyLong(),any())).thenReturn(itemDistributionDetails);

        //execution
        List<AuctionBidData> auctionBidData=auctionServiceImpl.getAuctionItemDetailForPerformancePage(event,"test");

        //assert
        assertEquals(auctionBid.getAmount().doubleValue(),auctionBidData.get(0).getBidAmount(),auctionBidData.get(0).getBidAmount());
        assertTrue(auctionBidData.get(0).isPaid());
        assertTrue(auctionBidData.get(0).isDistributed());

    }


    @Test
    void test_getAllItems() {
	    //setup
        List<AuctionItemDto> auctionItemDtos=new ArrayList<>();
        AuctionItemDto auctionItemDto=new AuctionItemDto();
        auctionItemDto.setBoolCurrentBid(true);
        auctionItemDto.setHighestBidder("test");
        auctionItemDto.setNumberOfWinners(5);
        auctionItemDto.setBidId(1L);
        auctionItemDto.setImageId(1L);
        auctionItemDto.setImagePosition(1000.0);
        auctionItemDto.setImageUrl("test");
        auctionItemDtos.add(auctionItemDto);
        List<String> strings=new ArrayList<>();
        strings.add("test");
        //mock
        when(itemService.getAllIAuctiontemsWithImage(anyLong(),anyString())).thenReturn(auctionItemDtos);
        when(itemCategoryService.findNameByModuleIdAndType(anyLong(),any())).thenReturn(strings);

        //execution
        ItemResponseContainer itemResponseContainer=auctionServiceImpl.getAllItems(anyLong(),anyString());

        //assert
        assertEquals(auctionItemDtos.size(),itemResponseContainer.getTotalItems());
        assertEquals(auctionItemDtos,itemResponseContainer.getItems());
        assertEquals(strings,itemResponseContainer.getItemCategorys());
    }


    @Test
    void test_getFundRisingGoal_TotalFundRaisedShowTrue_SetEndDate() {
        //setup
	    ItemInstructionDto itemInstructionDto=new ItemInstructionDto();
	    itemInstructionDto.setCode("test");
	    itemInstructionDto.setStartingBid(11);
        Pageable pageable = PageRequest.of(0, 1);
        Page<ItemInstructionDto> itemInstructionDtos=new PageImpl<>(Arrays.asList(itemInstructionDto));
        AuctionBidDto auctionBidDto=new AuctionBidDto(1L,1,1L,1d);
        auction.setId(1L);
        auction.setEndDate(new GregorianCalendar(2014, Calendar.FEBRUARY, 11).getTime());
        auction.setLastExtendedEndTime(new GregorianCalendar(2014, Calendar.FEBRUARY, 11).getTime());
        SimpleDateFormat fmt = new SimpleDateFormat(Constants.UTC_DATE_FORMAT);


        //mock
        when(auctionServiceImpl.findByEvent(event)).thenReturn(auction);
        when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getAuctionId(),ModuleType.AUCTION)).thenReturn(itemInstructionDto);

        when(phoneNumberService.getDisplayNumber(event)).thenReturn("9913318414");
        when(eventDesignDetailService.isTotalFundRaisedShow(event)).thenReturn(true);
        when(auctionBidService.findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong())).thenReturn(Collections.singletonList(auctionBidDto));
        when(auctionBidService.getTotalProceedsAsPerItem(Collections.singletonList(auctionBidDto))).thenReturn(1d);
        when(eventDesignDetailService.getViewScrollSpeedByEvent(event)).thenReturn(1L);

        //execution
        GoalDto goalDto=auctionServiceImpl.getFundRisingGoal(event);

        //assert
        assertTrue(goalDto.isShowTotalFundRaised());
        assertEquals(fmt.format(auction.getActualEndDate()),goalDto.getModuleEndDate());
        assertEquals(new Long(1L),goalDto.getViewScrollSpeed());

    }


    @Test
    void test_getScrollData_auctionScrollViewDataNotNull_ItemCodeAndStartingBidOfItemWithLowestPosition() {
        //setup
        auction.setId(1L);
        auction.setEndDate(new GregorianCalendar(2014, Calendar.FEBRUARY, 11).getTime());
        auction.setLastExtendedEndTime(new GregorianCalendar(2014, Calendar.FEBRUARY, 11).getTime());
        EventDesignDetail eventDesignDetail=new EventDesignDetail();
        eventDesignDetail.setTotalFundRaisedShow(true);
        ItemAuctionBidDto itemAuctionBidDto = new ItemAuctionBidDto(1L, 1,1L,1L,true);
        AuctionBidAndItemAndBidderDto auctionBidAndItemAndBidderDto = new AuctionBidAndItemAndBidderDto(1L,
                "ITEM-NAME","IAC",1,1000,500,1L,50d,true,new Date("2018/01/01 12:00"),1L,"F-Name","L-Name");
        AuctionBidDto auctionBidDto=new AuctionBidDto(1L,1,1L,1d);
        ItemInstructionDto itemInstructionDto = new ItemInstructionDto();
        itemInstructionDto.setCode("AAA");
        itemInstructionDto.setStartingBid(50);
        SimpleDateFormat fmt = new SimpleDateFormat(Constants.UTC_DATE_FORMAT);


        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(auctionBidRepository.findItemIdAndAuctionBidIdByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(auctionId,ModuleType.AUCTION,true)).thenReturn(Collections.singletonList(itemAuctionBidDto));
        when(auctionBidRepository.findAuctionBidAndItemAndBidderByAuctionIdAndIsDeletedFalseAndIsRefundedFalseAndItemIsActive(anyList())).thenReturn(Collections.singletonList(auctionBidAndItemAndBidderDto));
        doReturn(10.0).when(auctionServiceImpl).getSumOfHighestBidAmountForEachItem(auction.getId());
        doReturn(auction).when(auctionServiceImpl).findByEvent(event);
        when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(auction.getId(),ModuleType.AUCTION)).thenReturn(itemInstructionDto);

        when(phoneNumberService.getDisplayNumber(any())).thenReturn("test");

        //execution
        AuctionScrollViewData auctionScrollViewData=auctionServiceImpl.getScrollData(event);

        //assert
        assertTrue(auctionScrollViewData.isShowTotalFundRaised());
        assertEquals(fmt.format(auction.getActualEndDate()),auctionScrollViewData.getModuleEndDate());
        assertEquals(event.getCurrency().getSymbol(),auctionScrollViewData.getCurrencySymbol());
        assertNotNull(auctionScrollViewData.getItems());

    }

    @Test
    void test_updateAuctionItem_SuccessUpdateAuctionItemWithRightParameters() {
	    //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        AuctionBid bid = new AuctionBid(null,1,item,100d, BiddingSource.SMS,true);
        auction.setId(1L);
        auction.setEventId(10L);
        auction.setActivated(true);
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
        auction.setCategoryEnabled(false);
        auction.setHighestBidderShow(true);
        auction.setDefaultBidIncrement(1);
        auction.setEventPayoutStatus(EnumEventPayoutStatus.HOLD);
        auction.setExtendedBiddingWaitTime(1L);
        auction.setAllowMultipleWinnersPerItem(true);
        AuctionItemUpdateDto auctionItemDto = new AuctionItemUpdateDto();
        auctionItemDto.setBidIncrement(1d);
        auctionItemDto.setNumberOfWinners(2);
        auctionItemDto.setCategory("cat");
        auctionItemDto.setLiveAuctionItem(false);
        auctionItemDto.setBuyItNowPrice(2);
        auctionItemDto.setItemShortName("test");
        auctionItemDto.setMarketValue(1);
        auctionItemDto.setStartingBid(1);

        //mock
        when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
        when(auctionBidRepository.findFirstByAuctionIdAndItemOrderByAmountDesc(anyLong(),any())).thenReturn(bid);

        when(itemService.getItemByModuleIdAndTypeAndCode(anyLong(),any(),anyString())).thenReturn(Optional.empty());
        when(itemCategoryService.findByModuleIdAndModuleTypeAndName(anyLong(),any(),anyString())).thenReturn(Optional.empty());

        //execution
        Item item1=auctionServiceImpl.updateAuctionItem(item.getId(),event,auctionItemDto);

        //assert
        assertNotNull(item1);
        assertFalse(item1.isLiveAuctionItem());
        assertEquals(auctionItemDto.getMarketValue(),item1.getMarketValue());
        assertEquals(0, Double.compare(auctionItemDto.getBuyItNowPrice(),item1.getBuyItNowPrice()));
    }

    @Test
    void test_getAuctionCheckoutOrConfirmBidData_GetCheckoutOrConfirmBidDataWithRightParameters() {
        //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        item.setNumberOfWinners(2);
        AuctionBid highestBid = new AuctionBid(user,1,item,100d,BiddingSource.SMS,false);
        highestBid.setId(1L);
        List<ItemImgLocations> itemImgLocations=new ArrayList<>();
        ItemImgLocations itemImgLocation=new ItemImgLocations();
        itemImgLocation.setItem(item);
        itemImgLocation.setPosition(100L);
        itemImgLocation.setImageLocations("test");
        itemImgLocations.add(itemImgLocation);
        Stripe stripe=new Stripe();
        stripe.setProcessingFeesToPurchaser(true);
        stripe.setEvent(event);
        EventUserInfoDto eventUserInfoDto=new EventUserInfoDto(user);
        StripeCreditCardDto stripeCreditCardDto =new StripeCreditCardDto("123","4444",1,1,"ABC",true);
        Winner winner1=new Winner();
        winner1.setBidId(1L);
        winner1.setEventId(event.getEventId());
        winner1.setHasPaid(true);
        winner1.setItemId(item.getId());
        winner1.setModuleType(ModuleType.AUCTION);
        winner1.setUserId(user.getUserId());
        winner1.setWinnerId(1L);
        winner1.setWinningTime(new Date());
        winnerList.add(winner1);

        //mock
        when(roEventService.getEventByURL(anyString())).thenReturn(event);
        when(itemService.getItem(anyLong(), anyString())).thenReturn(Optional.of(item));
        when(auctionBidService.findByUserAndAuctionAndItem(any(),anyLong(),any())).thenReturn(highestBid);
        when(winnerService.findByModuleTypeAndItemId(any(),anyLong())).thenReturn(winnerList);
        when(itemImgLocationsService.findByItem(any())).thenReturn(itemImgLocations);
        when(auctionServiceImpl.findByEvent(event)).thenReturn(auction);
        when(itemService.getBidIncrement(any(),any())).thenReturn(1d);
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(eventService.getLinkedCreditCard(any(),any())).thenReturn(Collections.singletonList(stripeCreditCardDto));
        when(userService.isCCRequired(any(),any(),any(),anyBoolean())).thenReturn(true);
        when(userService.extractUserInfoForEvent(user,event, true,ModuleType.AUCTION)).thenReturn(eventUserInfoDto);

        //execution
        AuctionSmsCheckout auctionSmsCheckout=auctionServiceImpl.getAuctionCheckoutOrConfirmBidData(event.getEventURL(),user,item.getCode(),true);

        //assert
        assertNotNull(auctionSmsCheckout);
        assertEquals(eventUserInfoDto,auctionSmsCheckout.getUserInfo());
        assertTrue(auctionSmsCheckout.isCreditCardRequired());
        assertTrue(auctionSmsCheckout.isProcessingFeesToPurchaser());

    }


    @Test
    void resetAuctionEvent_SuccessResetWithForceResetTrue() {
        //setup
        auction.setId(1L);
        auction.setActivated(false);

        //mock
        when(auctionServiceImpl.findByEvent(event)).thenReturn(auction);
        ArgumentCaptor<Auction> captor = ArgumentCaptor.forClass(Auction.class);

        //execution
        auctionServiceImpl.resetAuctionEvent(event,true);

        //assert
        verify(auctionServiceImpl).saveAndCacheClear(captor.capture(),eq(event));
        assertEquals(null,captor.getValue().getLastExtendedEndTime());
        assertFalse(captor.getValue().isActivated());

    }

    @Test
    void resetAuctionEvent_SuccessResetWithForceResetFalse_AndSetAuctionActivatedTrue() {
        //setup
        auction.setId(1L);
        auction.setActivated(true);

        //mock
        when(auctionServiceImpl.findByEvent(event)).thenReturn(auction);
        ArgumentCaptor<Auction> captor = ArgumentCaptor.forClass(Auction.class);

        //execution
        auctionServiceImpl.resetAuctionEvent(event,false);

        //assert
        verify(auctionServiceImpl).saveAndCacheClear(captor.capture(), eq(event));
        assertEquals(null,captor.getValue().getLastExtendedEndTime());
        assertTrue(captor.getValue().isActivated());

    }


    @Test
    void getUnPaidItems_MoreThenOneItemsWonByUser() {
        //setup
        Item item=new Item("test","test",ModuleType.AUCTION,"test",1L,1);
        AuctionBid bid = new AuctionBid(user,1,item,100d, BiddingSource.SMS,true);
        Stripe stripe=new Stripe();
        stripe.setProcessingFeesToPurchaser(true);
        stripe.setStripePublishableKey("key");
        stripe.setEvent(event);
        StripeCreditCardDto stripeCreditCardDto =new StripeCreditCardDto("123","4444",1,1,"ABC",true);
        EventUserInfoDto eventUserInfoDto=new EventUserInfoDto(user);
        Winner winner1=new Winner();
        winner1.setBidId(1L);
        winner1.setEventId(event.getEventId());
        winner1.setHasPaid(true);
        winner1.setItemId(1L);
        winner1.setModuleType(ModuleType.AUCTION);
        winner1.setUserId(user.getUserId());
        winner1.setWinnerId(1L);
        winner1.setWinningTime(new Date());
        winnerList.add(winner1);

        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(eventService.getLinkedCreditCard(any(),any())).thenReturn(Collections.singletonList(stripeCreditCardDto));
        when(userService.isCCRequired(any(),any(),any(),anyBoolean())).thenReturn(true);
        when(userService.extractUserInfoForEvent(user,event, true,ModuleType.AUCTION)).thenReturn(eventUserInfoDto);
        when(winnerService.getAllItemsWonByUser(anyLong(),any(),any())).thenReturn(winnerList);
        when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(bid));
        when(itemImgLocationsService.findByItem(any())).thenReturn(new ArrayList<ItemImgLocations>());
        doReturn(auction).when(auctionServiceImpl).findByEvent(event);
        when(itemService.getBidIncrement(item, auction)).thenReturn(1d);

        //execution
        AuctionSmsCheckout auctionSmsCheckout = auctionServiceImpl.getUnPaidItems(event, user);

        //assert
        assertNotNull(auctionSmsCheckout);
        assertTrue(winnerList.size()>=2);
        assertNotNull(auctionSmsCheckout.getItems());

    }

}
