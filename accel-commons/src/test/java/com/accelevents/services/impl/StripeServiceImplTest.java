package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.dto.StripeDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.repositories.StripeRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.services.*;
import com.accelevents.utils.DateUtils;
import com.stripe.exception.ApiConnectionException;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StripeServiceImplTest{

    @Spy
    @InjectMocks
    private StripeServiceImpl stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private ROPayFlowConfigService roPayFlowConfigService;
    @Mock
    private StripeRepository stripeRepository;
    @Mock
    private AllPaymentService allPaymentService;
    @Mock
    private StripePaymentService stripePaymentService;
    @Mock
    private ROEventService roEventService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private DonationSettingsService donationSettingsService;
    @Spy
    private StripeConfiguration stripeConfiguration = new StripeConfiguration();
    @Mock
    private EventChecklistService eventChecklistService;
    @Mock
    private PayFlowConfigServiceImpl payFlowConfigService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    Event event;
    Stripe stripe;
    @BeforeEach
    void setUp() {
        // Prepare data
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(1L);
        event.setCurrency(Currency.AUD);
        event.setEventURL("eventURL");

        ReflectionTestUtils.setField(stripeConfiguration, "API_KEY", "sk_test_VdWjJ3utvEVEMNvIojtMYMWn");
        ReflectionTestUtils.setField(stripeConfiguration, "auctionProductId", "prod_Cen5qwPvDrUYv9");
        ReflectionTestUtils.setField(stripeConfiguration, "raffleProductId", "prod_Cen5RURzoolzB6");
        ReflectionTestUtils.setField(stripeConfiguration, "fundANeedProductId", "prod_Cen5hDjNygMuzg");

        stripe = new Stripe();
        stripe.setStripePublishableKey("publishableKey");
    }

    @Test
    void test_unSubscribeStripePlan() throws StripeException {

        String stringReturn = stripeService.unSubscribeStripePlan("subscriptionId");
        assertNull(stringReturn);
    }

    /* Below code unused , because in Stripe java sdk version 22 remove SKU class.
    @Test
    void test_getSilentAuctionSKU(){

        Sku sku = stripeService.getSilentAuctionSKU(event);
        assertNotNull(sku);
    }

    @Test
    void test_getRaffleSKU(){

        Sku sku = stripeService.getRaffleSKU(event);
        assertNotNull(sku);
    }

    @Test
    void test_getFundANeedSKU(){
        Sku sku = stripeService.getFundANeedSKU(event);
        assertNotNull(sku);
    }

     */

    @Test
    void test_getCouponByCouponCode(){

        Coupon coupon = stripeService.getCouponByCouponCode(stripeConfiguration.getAPI_KEY(),"coupon_code");
        assertNull(coupon);
    }

    @Test
    void test_getSecretTerminalConnection() throws JSONException {
        Stripe stripe = new Stripe();
        stripe.setAccessToken("sk_test_VdWjJ3utvEVEMNvIojtMYMWn");

        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        String value = stripeService.getSecretTerminalConnection(event);
        assertNotNull(value);
    }

    @Test
    void test_findByEvent() throws StripeException {

        Stripe stripe = new Stripe();
        stripe.setStripeUserId("stripe_user_id");
        stripe.setStripePublishableKey("publish_key");

        List<Stripe> stripeAccounts = new ArrayList<>();
        stripeAccounts.add(stripe);
        Account account = new Account();
        Account.Settings settings=new Account.Settings();
        Account.Settings.Dashboard dashboard =new Account.Settings.Dashboard();
        dashboard.setDisplayName("Account-Name");
        settings.setDashboard(dashboard);
        account.setEmail("<EMAIL>");
        account.setSettings(settings);

        //Mock
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(stripeAccounts);
        when(allPaymentService.retrieveAccount(stripe.getStripeUserId(), stripe)).thenReturn(account);

        //execute
        Stripe stripeObj = stripeService.findByEventWithEmailDisplayName(event);
        assertNotNull(stripeObj);
        assertEquals(stripeObj.getEmail(),account.getEmail());
        assertEquals(stripeObj.getAccountDisplayName(),account.getSettings().getDashboard().getDisplayName());
    }

    private List<Stripe> getStripes() {
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);

        event.setWhiteLabel(whiteLabel);
        List<Stripe> stripeAccounts = new ArrayList<>();
        Stripe stripe = new Stripe();
        stripe.setStripeUserId("stripe_user_id");
        stripe.setStripePublishableKey("publish_key");
        stripe.setEvent(event);
        stripeAccounts.add(stripe);
        return stripeAccounts;
    }

    @Test
    void test_isStripeConnected_true(){

        List<Stripe> stripeAccounts = getStripes();

        //mock
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(stripeAccounts);

        //execute
        Boolean values = stripeService.isStripeConnected(event);
        assertTrue(values);
    }

    @Test
    void test_isStripeConnected_false(){

        List<Stripe> stripeAccounts = new ArrayList<>();

        //mock
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(stripeAccounts);

        //execute
        Boolean values = stripeService.isStripeConnected(event);
        assertFalse(values);
    }

    @Test
    void test_isStripeConnected_false_stripe_list_null(){

        List<Stripe> stripeAccounts = null;

        //mock
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(stripeAccounts);

        //execute
        Boolean values = stripeService.isStripeConnected(event);
        assertFalse(values);
    }

    @Test
    void test_findByEventUserEmail(){

        List<Stripe> stripeAccounts = getStripes();

        when(stripeRepository.findByEventUserEmail("email")).thenReturn(stripeAccounts);

        List<Stripe> value = stripeService.findByEventUserEmail("email");
        assertTrue(value.size() > 0);
    }

    @Test
    void test_stripeUserId(){


        stripeService.activeStripe("stripe_user_id");
    }

    @Test
    void test_findAllByEvent(){
        List<Stripe> stripeAccounts = getStripes();
        when(this.stripeRepository.findAllByEvent(event)).thenReturn(stripeAccounts);
        List<Stripe> value = stripeService.findAllByEvent(event);
        assertTrue(value.size() > 0);
    }

    @Test
    void test_getDisconnectedAccount(){
        List<Stripe> stripeAccounts = getStripes();
        when(stripeRepository.findByEventAndDefaultAccount(event, false)).thenReturn(stripeAccounts);
        List<Stripe> value = stripeService.getDisconnectedAccount(event);
        assertTrue(value.size() > 0);
    }

    @Test
    void test_activateStripeAccountFromWebHook() throws StripeException {

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();
        Account account = Account.retrieve();
        //Mock
        when(stripePaymentService.getAccountByEventType("json")).thenReturn(account);
        //execute
        stripeService.activateStripeAccountFromWebHook("json");
    }

//    @Test   commented this test case need to re-evaluate logic
    void test_checkTransferEnable() throws StripeException {

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();
        Account account = Account.retrieve();
        when(stripeRepository.findByStripeUser(account.getId())).thenReturn(getStripes());
        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> stripeService.checkTransferEnable(account));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.STRIPE_WEBHOOK_UPDATE_ERROR_NOT_ENABLED_TRANSFER.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_checkTransferEnable_Wl_null() throws StripeException {

        List<Stripe>  stripeList = new ArrayList<>();
        Stripe stripe = new Stripe();
        event.setWhiteLabel(null);
        stripe.setEvent(event);
        stripeList.add(stripe);

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();

        Account account = Account.retrieve();


        //execute

    }

    @Test
    void test_updateEventCheckList() throws StripeException {

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();
        Account account = Account.retrieve();
        EventChecklist eventChecklist = new EventChecklist();
        eventChecklist.setId(1L);

        doNothing().when(this.stripeRepository).activeStripe(account.getId());
        when(this.eventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
        doNothing().when(this.stripeService).activeStripe(account.getId());

        //Execute
        stripeService.updateEventCheckList(account, getStripes().get(0));

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService, Mockito.times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklist1 = eventChecklistArgumentCaptor.getValue();

        assertTrue(eventChecklist1.isActivatePaymentProcessing());

    }

//    @Test commented this test case need to re-evaluate logic
    void test_checkAccountChargesAndPayoutsEnable() throws StripeException {

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();
        Account account = Account.retrieve();
        
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> stripeService.checkAccountChargesAndPayoutsEnable(account));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.STRIPE_WEBHOOK_UPDATE_ERROR_NOT_ENABLED_CHARGES.getErrorMessage(), exception.getMessage());
    }


    @Test
    void test_findStripeCustomerIdByEvent(){

        //mock
        when(stripeRepository.findStripeCustomerIdByEvent(event)).thenReturn("string");
        //execute
        String value = stripeService.findStripeCustomerIdByEvent(event);
        assertNotNull(value);
    }

    @Test
    void test_findByWhiteLabel(){
        Stripe stripe = getStripes().get(0);
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);
        //mock
        when(stripeRepository.findByWhiteLabelId(1L)).thenReturn(stripe);
        //execute
        Stripe value = stripeService.findByWhiteLabel(whiteLabel);
        assertNotNull(value);
    }

    @Test
    void test_getByEventId(){

        //mock
        when(stripeRepository.findByEventIdAndDefaultAccount(event.getEventId(),true)).thenReturn(getStripes());

        //execute
        Stripe stripe = stripeService.getByEventId(event.getEventId());
        assertNotNull(stripe);
    }

    @Test
    void test_getByEventId_stripe_null(){

        //mock
        when(stripeRepository.findByEventIdAndDefaultAccount(event.getEventId(),true)).thenReturn(Collections.EMPTY_LIST);

        //execute
        Stripe stripe = stripeService.getByEventId(event.getEventId());
        assertNull(stripe);
    }

    @Test
    void test_handleStripeWebHook(){
        //setup
        Plan plan = new Plan();
        plan.setNickname("Text To Give");

        Subscription subscription = new Subscription();
        SubscriptionItem subscriptionItem = new SubscriptionItem();
        subscriptionItem.setPlan(plan);
        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
        subscriptionItemCollection.setData(List.of(subscriptionItem));
        subscription.setItems(subscriptionItemCollection);
        subscription.setStatus("canceled");
        subscription.setId("id");

        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setEvent(event);

        DonationSettings donationSettings = new DonationSettings();
        //mock
        when(stripeTransactionService.getStripeTransactionForStripeWebHook(StripeTransactionSource.MODULE_ACTIVE, subscription.getId())).thenReturn(stripeTransaction);
        when(donationSettingsService.getByEventId(stripeTransaction.getEvent().getEventId())).thenReturn(donationSettings);

        //execute3
        stripeService.handleStripeWebHook(subscription);

        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction stripeTransaction1 = stripeTransactionArgumentCaptor.getValue();
        assertNotNull(stripeTransaction1);

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService, Mockito.times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings DonationSettings = donationSettingsArgumentCaptor.getValue();
        assertNotNull(DonationSettings);
    }

    @Test
    void test_getCCProcessingDetails_stripe_account(){

        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        doReturn(stripe).when(roStripeService).findByEvent(event);
        //execute
        CreditCardChargesDto chargesDto = stripeService.getCCProcessingDetails(event);
        assertNotNull(chargesDto);
    }

    @Test
    void test_getCCProcessingDetails_stripe_null(){
        //execute
        doReturn(null).when(roStripeService).findByEvent(event);
        CreditCardChargesDto chargesDto = stripeService.getCCProcessingDetails(event);
        assertNotNull(chargesDto);
    }

    @Test
    void test_getCCProcessingDetails_square_accoount(){
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
        //execute
        doReturn(stripe).when(roStripeService).findByEvent(event);
        CreditCardChargesDto chargesDto = stripeService.getCCProcessingDetails(event);
        assertNotNull(chargesDto);
    }

    @Test
    void test_findByPaymentGatewayAndTokenExpiracyDateLessThan(){

        Date futureDate = DateUtils.getCurrentDate();

        when(stripeRepository.findByPaymentGatewayAndTokenExpiracyDateLessThan(any(), any(), any())).thenReturn(getStripes());
        List<Stripe> stripeList = stripeService.findByPaymentGatewayAndTokenExpiracyDateLessThan(EnumPaymentGateway.SQUARE, futureDate);
        assertFalse(stripeList.isEmpty());
    }

    @Test
    void test_findByPaymentGatewayAndDefaultAccountTrue(){

        when(stripeRepository.findByPaymentGatewayAndDefaultAccountTrue(EnumPaymentGateway.STRIPE.toString())).thenReturn(getStripes());
        List<Stripe> stripeList = stripeService.findByPaymentGatewayAndDefaultAccountTrue(EnumPaymentGateway.STRIPE);
        assertFalse(stripeList.isEmpty());
    }

    @Test
    void test_findByEventAndDefaultAccount(){
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(getStripes());
        Stripe stripe = stripeService.findByEventAndDefaultAccount(event, true);
        assertNotNull(stripe);
    }

    @Test
    void test_findByEventAndDefaultAccount_return_null(){
        when(stripeRepository.findByEventAndDefaultAccount(event, true)).thenReturn(Collections.EMPTY_LIST);
        Stripe stripe = stripeService.findByEventAndDefaultAccount(event, true);
        assertNull(stripe);
    }

    @Test
    void test_getStripeFeesByEvent(){
        when(stripeRepository.getStripeFeesByEvent(event)).thenReturn(null);
        StripeDTO stripeDto =stripeService.getStripeFeesByEvent(event);
        assertNotNull(stripeDto);
    }

    @Test
    void test_getStripeFeesByEvent_stripeDTO_not_null(){
        when(stripeRepository.getStripeFeesByEvent(event)).thenReturn(new StripeDTO());
        StripeDTO stripeDto =stripeService.getStripeFeesByEvent(event);
        assertNotNull(stripeDto);
    }

    @Test
    void test_getAllByPaymentGatewayDefaultAccount(){
        when(stripeRepository.findAllByPaymentGateway(EnumPaymentGateway.STRIPE.toString(), 1, 5)).thenReturn(getStripes());
        List<Stripe> stripeList = stripeService.getAllByPaymentGatewayDefaultAccount(EnumPaymentGateway.STRIPE.toString(), 1, 5);
        assertFalse(stripeList.isEmpty());
    }

    @Test
    void test_getByEventIdAndStaffId(){
        Staff staff = new Staff();
        when(stripeRepository.findByEventAndStaff(event, staff)).thenReturn(getStripes().get(0));
        Stripe stripe = stripeService.getByEventIdAndStaffId(event, staff);
        assertNotNull(stripe);
    }

    @Test
    void test_findByCode(){
        when(stripeRepository.findByCode("code")).thenReturn(getStripes().get(0));
        Stripe stripe = stripeService.findByCode("code");
        assertNotNull(stripe);
    }

    @Test
    void test_save(){
        stripeService.save(getStripes().get(0));
        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeRepository, Mockito.times(1)).save(stripeArgumentCaptor.capture());
        Stripe stripe = stripeArgumentCaptor.getValue();
        assertNotNull(stripe);
    }

    @Test
    void test_getStringObjectMap() throws StripeException {

        Map<String, Object> map = stripeService.getStringObjectMap(event, stripeConfiguration.getAuctionProductId());
        assertNotNull(map);
    }

    @Test
    void test_activateStripeAccountFromWebHook_catch() throws StripeException {

        com.stripe.Stripe.apiKey = stripeConfiguration.getAPI_KEY();
        Account account = Account.retrieve();
        StripeException stripeException = new ApiConnectionException("Exception");
        //Mock
        when(stripePaymentService.getAccountByEventType("json")).thenThrow(stripeException);
        //execute
        stripeService.activateStripeAccountFromWebHook("json");
    }

    @Test
    void test_findByWhiteLabelWithConnectedAccountEmail() throws StripeException {
        Stripe stripe = getStripes().get(0);
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);
        Account account = new Account();
        Account.Settings settings=new Account.Settings();
        Account.Settings.Dashboard dashboard =new Account.Settings.Dashboard();
        dashboard.setDisplayName("Account-Name");
        settings.setDashboard(dashboard);
        account.setEmail("<EMAIL>");
        account.setSettings(settings);
        //mock
        when(stripeRepository.findByWhiteLabelIdAndDefaultAccount(1L, true)).thenReturn(List.of(stripe));
        when(allPaymentService.retrieveAccount(stripe.getStripeUserId(), stripe)).thenReturn(account);
        //execute
        Stripe value = stripeService.findByWhiteLabelWithConnectedAccountEmail(whiteLabel);
        assertNotNull(value);
        assertEquals(value.getEmail(),account.getEmail());
        assertEquals(value.getAccountDisplayName(),account.getSettings().getDashboard().getDisplayName());
    }

    @Test
    void test_findByWhiteLabelWithConnectedAccountEmailNull() throws StripeException {
        Stripe stripe = getStripes().get(0);
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);
        Account account = null;
        //mock
        when(stripeRepository.findByWhiteLabelIdAndDefaultAccount(1L, true)).thenReturn(List.of(stripe));
        when(allPaymentService.retrieveAccount(stripe.getStripeUserId(), stripe)).thenReturn(account);
        //execute
        Stripe value = stripeService.findByWhiteLabelWithConnectedAccountEmail(whiteLabel);
        assertNotNull(value);
        assertNull(value.getEmail());
        assertNull(value.getAccountDisplayName());
    }

}

