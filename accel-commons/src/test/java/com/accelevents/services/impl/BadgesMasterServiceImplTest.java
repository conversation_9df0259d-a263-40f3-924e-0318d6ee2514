package com.accelevents.services.impl;

import com.accelevents.badges.BadgesMaster;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.dto.BadgesMasterDto;
import com.accelevents.repositories.BadgesMasterRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BadgesMasterServiceImplTest {

    @Spy
    @InjectMocks
    private BadgesMasterServiceImpl badgesMasterService = new BadgesMasterServiceImpl();

    @Mock
    private BadgesMasterRepository badgesRepository;

    private Event event;
    private User user;
    private BadgesMasterDto badgesMasterDto;
    private BadgesMaster badgesMaster;

    @BeforeEach
    public void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        badgesMasterDto = new BadgesMasterDto();
        badgesMasterDto.setBadgeTitle("Test Badge Save And Update");
        badgesMasterDto.setCustom(true);
        badgesMasterDto.setWidth(10.0);
        badgesMasterDto.setHeight(10.0);
        badgesMasterDto.setBleedAreaHeight(5.0);
        badgesMasterDto.setBleedAreaWidth(5.0);
        badgesMasterDto.setSafeAreaHeight(5.0);
        badgesMasterDto.setSafeAreaWidth(5.0);
        badgesMasterDto.setNumberOfView(1);
        badgesMasterDto.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        badgesMaster = new BadgesMaster();
        badgesMaster.setTitle("Test Badge Save And Update");
        badgesMaster.setCustom(true);
        badgesMaster.setWidth(10.0);
        badgesMaster.setHeight(10.0);
        badgesMaster.setBleedAreaHeight(5.0);
        badgesMaster.setBleedAreaWidth(5.0);
        badgesMaster.setSafeAreaHeight(5.0);
        badgesMaster.setSafeAreaWidth(5.0);
        badgesMaster.setNumberOfView(1);
        badgesMaster.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");
    }

    @Test
    public void test_getAllBadgesForEvent(){

        List<BadgesMasterDto> badgesMstDtos = new ArrayList<>();
        badgesMstDtos.add(badgesMasterDto);

        String languageCode = "EN";

        when( badgesRepository.getBadgesByLanguageCode(languageCode)).thenReturn(badgesMstDtos);

        //Execution
        List<BadgesMasterDto> allBadgesMstForEvent = badgesMasterService.getCommonBadges(languageCode);
        allBadgesMstForEvent.forEach(badges -> {
            assertEquals(badges.getBadgeTitle(), badgesMasterDto.getBadgeTitle());
            assertEquals(badges.getBadgeId(), badgesMasterDto.getBadgeId());
            assertEquals(badges.getWidth(), badgesMasterDto.getWidth());
            assertEquals(badges.getHeight(), badgesMasterDto.getHeight());
            assertEquals(badges.getBleedAreaWidth(), badgesMaster.getBleedAreaWidth());
            assertEquals(badges.getSafeAreaWidth(), badgesMaster.getSafeAreaWidth());
            assertEquals(badges.getBleedAreaHeight(), badgesMaster.getBleedAreaHeight());
            assertEquals(badges.getSafeAreaHeight(), badgesMaster.getSafeAreaHeight());
        });
    }

    @Test
    public void test_getFiledIsCustom(){

        Boolean filed = true;
        when(badgesRepository.getIsCustomById(anyLong())).thenReturn(true);

        //Execution
        boolean filedIsCustom = badgesMasterService.getFiledIsCustom(anyLong());
        assertEquals(filedIsCustom, filed);
    }

    @Test
    public void test_getBadgeById(){

        badgesMaster.setId(1L);

        when( badgesRepository.findBadgesMasterById(anyLong())).thenReturn(badgesMaster);

        //Execution
        BadgesMaster badgeById = badgesMasterService.getBadgeById(anyLong());

        assertEquals(badgeById.getTitle(), badgesMaster.getTitle());
        assertEquals(badgeById.getId(), badgesMaster.getId());
        assertEquals(badgeById.getWidth(), badgesMaster.getWidth());
        assertEquals(badgeById.getHeight(), badgesMaster.getHeight());
        assertEquals(badgeById.getBleedAreaWidth(), badgesMaster.getBleedAreaWidth());
        assertEquals(badgeById.getSafeAreaWidth(), badgesMaster.getSafeAreaWidth());
        assertEquals(badgeById.getBleedAreaHeight(), badgesMaster.getBleedAreaHeight());
        assertEquals(badgeById.getSafeAreaHeight(), badgesMaster.getSafeAreaHeight());
    }
}
