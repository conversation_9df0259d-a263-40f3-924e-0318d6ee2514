package com.accelevents.services.impl;

import com.accelevents.common.dto.SurveyQuestionsKeyValueDto;
import com.accelevents.common.dto.SurveyResponseDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.enums.UserRole;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.ro.event.service.ROEventLevelSettingService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.EventTaskService;
import com.accelevents.services.UserService;
import com.accelevents.services.repo.helper.SurveyConfigRepoService;
import com.accelevents.services.repo.helper.SurveyQuestionsRepoService;
import com.accelevents.services.repo.helper.SurveyResponseRepoService;
import com.accelevents.services.tray.io.tracking.TrayTrackingService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SurveyServiceImplTest {

    @Spy
    @InjectMocks
    private SurveyServiceImpl surveyServiceImpl;

    @Mock
    private SurveyConfigRepoService surveyConfigRepoService;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private UserService userService;
    @Mock
    private SurveyResponseRepoService surveyResponseRepoService;
    @Mock
    private SurveyQuestionsRepoService surveyQuestionsRepoService;
    @Mock
    private TrayTrackingService trayTrackingService;

    @Mock
    private ROEventLevelSettingService roEventLevelSettingService;

    @Mock
    private EventTaskService eventTaskService;

    private Event event = EventDataUtil.getEvent();
    private User user = EventDataUtil.getUser();
    private List<String> userRoles = List.of(UserRole.ROLE_USER.name());
    private Long surveyId = 1L;
    private SurveyConfiguration surveyConfiguration;
    private List<SurveyQuestions> surveyQuestionsList;
    private List<SurveyQuestions> quizableQuestionList;
    private SurveyResponseDto surveyResponseDto;

    SurveyServiceImplTest() {
        surveyConfiguration = new SurveyConfiguration();
        surveyConfiguration.setSurveyId(surveyId);
        surveyConfiguration.setSurveyName("Test Survey");
        surveyConfiguration.setEventId(event.getEventId());
        surveyConfiguration.setLoginRequired(true);
        surveyConfiguration.setSurveyQuizEnabled(true);

        surveyQuestionsList = getListOfSurveyQuestion();
        quizableQuestionList = getListOfQuizableQuestion();

        surveyResponseDto = new SurveyResponseDto();
        surveyResponseDto.setSurveyId(surveyId);
        surveyResponseDto.setEventId(event.getEventId());
        surveyResponseDto.setUserId(user.getUserId());
        surveyResponseDto.setSurveyQuestionsKeyValueDtos(getSurveyQuestionsKeyValueDtos());

    }

    private List<SurveyQuestionsKeyValueDto> getSurveyQuestionsKeyValueDtos() {
        SurveyQuestionsKeyValueDto surveyQuestionsKeyValueDto1 = new SurveyQuestionsKeyValueDto();
        surveyQuestionsKeyValueDto1.setType(AttributeValueType.SHORT_TEXT.toString());
        surveyQuestionsKeyValueDto1.setKey("Short Text");
        surveyQuestionsKeyValueDto1.setValue("ABC");
        surveyQuestionsKeyValueDto1.setQuestionId(1L);

        SurveyQuestionsKeyValueDto surveyQuestionsKeyValueDto2 = new SurveyQuestionsKeyValueDto();
        surveyQuestionsKeyValueDto2.setType(AttributeValueType.DROPDOWN.toString());
        surveyQuestionsKeyValueDto2.setKey("First DropDown");
        surveyQuestionsKeyValueDto2.setValue("A");
        surveyQuestionsKeyValueDto2.setQuestionId(2L);

        SurveyQuestionsKeyValueDto surveyQuestionsKeyValueDto3 = new SurveyQuestionsKeyValueDto();
        surveyQuestionsKeyValueDto3.setType(AttributeValueType.DROPDOWN.toString());
        surveyQuestionsKeyValueDto3.setKey("Second DropDown");
        surveyQuestionsKeyValueDto3.setValue("B");
        surveyQuestionsKeyValueDto3.setQuestionId(3L);

        SurveyQuestionsKeyValueDto surveyQuestionsKeyValueDto4 = new SurveyQuestionsKeyValueDto();
        surveyQuestionsKeyValueDto4.setType(AttributeValueType.DROPDOWN.toString());
        surveyQuestionsKeyValueDto4.setKey("Second DropDown");
        surveyQuestionsKeyValueDto4.setValue("B");
        surveyQuestionsKeyValueDto4.setQuestionId(4L);

        List<SurveyQuestionsKeyValueDto> surveyQuestionsKeyValueDtoList = new ArrayList<>();
        surveyQuestionsKeyValueDtoList.add(surveyQuestionsKeyValueDto1);
        surveyQuestionsKeyValueDtoList.add(surveyQuestionsKeyValueDto2);
        surveyQuestionsKeyValueDtoList.add(surveyQuestionsKeyValueDto3);
        surveyQuestionsKeyValueDtoList.add(surveyQuestionsKeyValueDto4);
        return surveyQuestionsKeyValueDtoList;
    }


    private List<SurveyQuestions> getListOfSurveyQuestion() {
        List<SurveyQuestions>  questionsList = new ArrayList<>();
        SurveyQuestions shortText = new SurveyQuestions();
        shortText.setId(1L);
        shortText.setQuestionTitle("Short Text");
        shortText.setType(AttributeValueType.SHORT_TEXT);
        shortText.setSurveyId(surveyId);
        shortText.setEventId(event.getEventId());
        shortText.setRequired(true);


        // Quizable question but not count in score
        SurveyQuestions dropDownQuestion2 = new SurveyQuestions();
        dropDownQuestion2.setId(3L);
        dropDownQuestion2.setQuestionTitle("Second DropDown");
        dropDownQuestion2.setType(AttributeValueType.DROPDOWN);
        dropDownQuestion2.setSurveyId(surveyId);
        dropDownQuestion2.setEventId(event.getEventId());
        dropDownQuestion2.setRequired(true);
        dropDownQuestion2.setUnscored(true);
        dropDownQuestion2.setOptions("[{\"optionId\":1,\"value\":\"A\",\"isCorrectAnswer\": true },{\"optionId\":2,\"value\":\"B\",\"isCorrectAnswer\": false }]");

        questionsList.add(shortText);
        questionsList.add(dropDownQuestion2);
        questionsList.addAll(getListOfQuizableQuestion());

        return questionsList;
    }

    private List<SurveyQuestions> getListOfQuizableQuestion() {
        List<SurveyQuestions> surveyAllQuestionsList = new ArrayList<>();
        // quizable Question
        SurveyQuestions dropDownQuestion1 = new SurveyQuestions();
        dropDownQuestion1.setId(2L);
        dropDownQuestion1.setQuestionTitle("First DropDown");
        dropDownQuestion1.setType(AttributeValueType.DROPDOWN);
        dropDownQuestion1.setSurveyId(surveyId);
        dropDownQuestion1.setEventId(event.getEventId());
        dropDownQuestion1.setRequired(true);
        dropDownQuestion1.setUnscored(false);
        dropDownQuestion1.setOptions("[{\"optionId\":1,\"value\":\"A\",\"isCorrectAnswer\": true },{\"optionId\":2,\"value\":\"B\",\"isCorrectAnswer\": false }]");

        // Quizable Question
        SurveyQuestions dropDownQuestion3 = new SurveyQuestions();
        dropDownQuestion3.setId(4L);
        dropDownQuestion3.setQuestionTitle("Third DropDown");
        dropDownQuestion3.setType(AttributeValueType.DROPDOWN);
        dropDownQuestion3.setSurveyId(surveyId);
        dropDownQuestion3.setEventId(event.getEventId());
        dropDownQuestion3.setRequired(true);
        dropDownQuestion3.setUnscored(false);
        dropDownQuestion3.setOptions("[{\"optionId\":1,\"value\":\"A\",\"isCorrectAnswer\": true },{\"optionId\":2,\"value\":\"B\",\"isCorrectAnswer\": false }]");

        surveyAllQuestionsList.add(dropDownQuestion1);
        surveyAllQuestionsList.add(dropDownQuestion3);
        return surveyAllQuestionsList;
    }


    @Test
    void testSubmitSurveyWithQuizAbleQuestion() {

        // Mock
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(userRoles);
        when(surveyConfigRepoService.isSurveyExist(surveyId, event.getEventId())).thenReturn(Boolean.TRUE);
        when(surveyResponseRepoService.findBySurveyIdAndUserId(surveyId, user.getUserId())).thenReturn(Boolean.FALSE);
        when(surveyConfigRepoService.findById(surveyId)).thenReturn(Optional.of(surveyConfiguration));
        when(surveyQuestionsRepoService.findBySurveyIdAndIsShowFalse(surveyId)).thenReturn(surveyQuestionsList);
        when(surveyQuestionsRepoService.findDropDownSurveyQuestionsBySurveyIdAndIsUnScoredFalse(surveyId)).thenReturn(quizableQuestionList);
        when(userService.findUserByIdWithoutCache(user.getUserId())).thenReturn(user);
        doNothing().when(trayTrackingService).submitSurvey(event, user,surveyResponseDto);

        // execution
        surveyServiceImpl.submitSurvey(surveyId, user, event, surveyResponseDto, UserRole.ROLE_USER.name());

        // verify
        ArgumentCaptor<SurveyResponse> surveyResponseArgumentCaptor = ArgumentCaptor.forClass(SurveyResponse.class);
        verify(surveyResponseRepoService, times(1)).save(surveyResponseArgumentCaptor.capture());

        SurveyResponse surveyResponse = surveyResponseArgumentCaptor.getValue();

        assertEquals(surveyId, surveyResponse.getSurveyId());
        assertEquals(50, surveyResponse.getUserQuizScore());
    }

    @Test
    void testSubmitSurveyWithSurveyWithSurveyAlreadySubmittedThrowException() {

        // Mock
        when(surveyConfigRepoService.findById(surveyId)).thenReturn(Optional.of(surveyConfiguration));
        when(roStaffService.getAllUserRoles(event.getEventId(), user.getUserId())).thenReturn(userRoles);
        when(surveyConfigRepoService.isSurveyExist(surveyId, event.getEventId())).thenReturn(Boolean.TRUE);
        when(surveyResponseRepoService.findBySurveyIdAndUserId(surveyId, user.getUserId())).thenReturn(Boolean.TRUE);
        when(roEventLevelSettingService.isSurveyRequiresCheckin(event.getEventId())).thenReturn(Boolean.FALSE);

        // execution
        String userRole = UserRole.ROLE_USER.toString();
        Exception exception = assertThrows(ConflictException.class,
                () -> surveyServiceImpl.submitSurvey(surveyId, user, event, surveyResponseDto, userRole)); //NOSONAR

        assertEquals("Survey response already submitted.", exception.getMessage());

    }
}
