package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.badges.Badges;
import com.accelevents.badges.BadgesMaster;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.common.dto.UserEventOrderDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.enums.BillingType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.perfomance.dto.TicketingBuyerDataWithAmount;
import com.accelevents.registration.approval.services.RegistrationRequestsService;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.session_speakers.services.KeyValueService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingServiceImpl ticketingServiceImpl;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private AllRequiresAttributesService allRequiresAttributesService;
    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private CommonEventService commonEventService;
    @Mock
    private EventTicketsRepository eventTicketsRepository;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private TicketingOrderService ticketingOrderService;
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private StripeService stripeService;
    @Mock
    private EventService eventService;
    @Mock
    private StaffService staffService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    private CustomEmailService customEmailService;
    @Mock
    private UserRepository userRepository;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private PaymentHandlerService paymentHandlerService;
    @Mock
    private SeatsIoService seatsIoService;
    @Mock
    private TicketHolderAttributesAuditService ticketHolderAttributesAuditService;
    @Mock
    private RecurringEventsRepository recurringEventsRepository;
    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private TableRefundService tableRefundService;
    @Mock
    private SalesTaxService salesTaxService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private UserService userService;
    @Mock
    private EventChecklistService eventChecklistService;
    @Mock
    private SessionRepoService sessionRepoService;

    @Mock
    private EventLimitRegistrationDomainService eventLimitRegistrationDomainService;
    @Mock
    private SessionService sessionService;
    @Mock
    private ChallengeConfigService challengeConfigService;
    @Mock
    private ChargebeePlanService chargebeePlanService;
    @Mock
    private SeatingCategoryService seatingCategoryService;
    @Mock
    private TransactionFeeConditionalLogicRepository transactionFeeConditionalLogicRepository;
    @Mock
    private TicketingTypeTagAndTrackRepo ticketingTypeTagAndTrackRepo;
    @Mock
    private KeyValueService keyValueService;
    @Mock
    private BadgesService badgesService;
    @Mock
    private VatTaxService vatTaxService;
    @Mock
    private IntegrationService integrationService;
    @Mock
    private RegistrationRequestsService registrationRequestsService;
    @Mock
    private  TicketingManageService ticketingManageService;
    @Mock
    private JoinBadgeTicketTypesService joinBadgeTicketTypesService;
    @Mock
    private TicketExchangeRuleService ticketExchangeRuleService;

    @Mock
    private TicketTypeTrackSessionLimitsService ticketTypeTrackSessionLimitsService;


    private Ticketing ticketing;
    private Unmarshaller unmarshaller;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private Event event;
    private PageSizeSearchObj pageSizeSearchObj;
    private TicketingBuyerDataFromDB ticketingBuyerDataFromDB;
    private PurchaserInfo purchaserInfo;
    private Map<Long, PurchaserInfo> purchaserInfoByOrderId = new HashMap<>();
    private StripeDTO stripeDTO;
    private TicketingType ticketingType;
    private EventTickets eventTickets;
    private TicketHolderAttributes ticketHolderAttributes;
    private AttendeeDto attendeeDto;
    private TicketAttributeValueDto1 ticketAttributeValueDto1;
    private TransactionFeeConditionalLogic transactionFeeConditionalLogic;
    private CreditCardChargesDto creditCardChargesDto;
    private RecurringEvents recurringEvents;
    private SeatingCategories seatingCategories;
    private TicketingDto ticketingDto;
    private TicketSettingAttributeDto ticketSettingAttributeDto;
    private User user;
    private CustomEmail customEmail;
    private ResendEventEmailBodyDto resendEventEmailBodyDto;
    private TicketingOrder ticketingOrder;
    private EventDesignDetail eventDesignDetail;
    private TicketingOrderManager ticketingOrderManager;
    private JAXBContext jaxbContext;
    private Pageable pageable;
    private EventCategoryDto eventCategoryDto;
    private EmailMessage emailMessage;
    private CategoryDto categoryDto;
    private SalesTaxFeeDto salesTaxFeeDto;
    private TicketSettingDto ticketSettingDto;

    private String firstName = "Jon";
    private String lastName = "Kaz";
    private String email = "<EMAIL>";
    private String search = "Search Data";
    private String date = "2019/04/04 05:30";
    private String endDate = "2019/04/04 09:30";
    private String eventAddress = "event address";
    private String xml;
    private String longitude = "-71.08655420000002";
    private String latitude = "42.3439447";
    private String categories = "1";
    private String categoryName = "seating category";
    private String chartKey = "chartKey";
    private String resendTicketOrderText = "Resend Ticket Order Text";
    private String resendTicketSubject = "Resend Ticket Subject";
    private String label = "label";
    private String color = "#D1D1D1";
    String logoImage = "6367449b-7adb-488b-b0a7-432f22c10029_tufdekwaterproofvinyldeckingpvcoutdoorvinylflooringwithregardtodimensions1900x500jpg";
    String seatNumber = "A1";
    Date eventEndDate = new Date("2019/09/04 05:30");
    private Date orderDate = new Date(date);
    private Date endDate1 = new Date(endDate);
    private Date startDate1 = new Date(date);
    private Long id = 1L;
    private Long recurringEventId = id;
    private Long key = 1L;
    private long orderId = id;
    private long ticketingTypeId = 1L;
    private long countOfIndividualTickets = 5;
    private long countOfNonIndividualTickets = 1;
    private long refundCount = 1;

    private long cancelCount = 1;
    private double orderRefundedAmount = 10d;
    private double orderPaidAmount = 100d;
    private double orderAEFee = AE_FLAT_FEE_ONE;
    private double orderAmount = 100d;
    private double orderWLFee = WL_FEE_FLAT;
    private double refundedTotalWLFee = 0d;
    private double refundedAEFee = 0d;
    private double salesTaxFee = 10.850185768332095;
    private double refundedSalestaxFee = 0d;
    private double vatTaxFee = 10.850185768332095;
    private double refundedVatTaxFee = 0d;
    private double price = 100d;
    private BadgesResponseData badgesResponseData;
    private List<Long> ids = new ArrayList<>();
    private BadgesMaster badgesMaster;
    private Badges customeBadges;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);
        ticketing = EventDataUtil.getTicketing(event);
        ticketingType = EventDataUtil.getTicketingType(event);
        eventTickets = EventDataUtil.getEventTickets();
        TicketTypeSettingDto ticketTypeSettingDto = EventDataUtil.getTicketTypeSettingDtoData(event);

        TicketDisplayPageDto displayPageDto = new TicketDisplayPageDto();
        displayPageDto.setStartDate(ticketing.getEventStartDate());
        displayPageDto.setEndDate(ticketing.getEventEndDate());

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        purchaserInfo = new PurchaserInfo(user);
        purchaserInfoByOrderId.put(orderId, purchaserInfo);

        stripeDTO = new StripeDTO();
        stripeDTO.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripeDTO.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);

        xml = EventDataUtil.getSampleXML(firstName, lastName, email);

        pageable = PageRequest.of(1, Integer.MAX_VALUE);

        pageSizeSearchObj = new PageSizeSearchObj();
        int size = 8;
        pageSizeSearchObj.setSize(size);
        int page = 1;
        pageSizeSearchObj.setPage(page);
        pageSizeSearchObj.setSortDirection(Sort.Direction.ASC);
        pageSizeSearchObj.setSortBy("name");

        eventCategoryDto = new EventCategoryDto();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName(firstName);
        ticketHolderRequiredAttributes.setId(id);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setAttribute(true);

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setId(id);
        ticketingOrder.setEventid(event);
        ticketingOrder.setOrderDate(orderDate);

        seatingCategories = new SeatingCategories();
        seatingCategories.setName(categoryName);
        seatingCategories.setHavingVariations(true);
        seatingCategories.setId(id);
        seatingCategories.setEvent(event);

        long totalTicketCount = 6;
        ticketingBuyerDataFromDB = new TicketingBuyerDataFromDB(orderId, firstName, lastName,
                orderDate, orderRefundedAmount, orderPaidAmount, orderAEFee, orderAmount, countOfIndividualTickets,
                countOfNonIndividualTickets, totalTicketCount, cancelCount,refundCount, TicketingOrder.TicketingOrderStatus.PAID,
                orderWLFee, refundedTotalWLFee, refundedAEFee, salesTaxFee, refundedSalestaxFee, vatTaxFee, refundedVatTaxFee,1l);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue(firstName, lastName, email));

        double creditCardPercentage = CREDIT_CARD_PROCESSING_PERCENTAGE;
        double creditCardFlat = CREDIT_CARD_PROCESSING_FLAT;
        creditCardChargesDto = new CreditCardChargesDto(creditCardFlat, creditCardPercentage);

        attendeeDto = new AttendeeDto();

        recurringEvents = new RecurringEvents();
        recurringEvents.setId(recurringEventId);
        recurringEvents.setRecurringEventEndDate(endDate1);
        recurringEvents.setShowRemainingTicketsForRecurringEvent(RecurringEvents.ShowRemainingTicketsForRecurringEvent.TRUE);

        String firstName = Constants.STRING_FIRST_SPACE_NAME;
        String lastName = Constants.STRING_LAST_SPACE_NAME;
        String attributes = "attributes";

        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(firstName, Constants.STRING_FIRST_SPACE_NAME);
        searchMap.put(lastName, Constants.STRING_FIRST_SPACE_NAME);

        Map<String, Map<String, String>> attributesMap = new HashMap<>();
        attributesMap.put(attributes, searchMap);

        ticketAttributeValueDto1 = new TicketAttributeValueDto1();
        ticketAttributeValueDto1.setHolder(attributesMap);

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setEvent(event);
        transactionFeeConditionalLogic.setAeFeeFlat(AE_FLAT_FEE_ONE);
        transactionFeeConditionalLogic.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
        transactionFeeConditionalLogic.setWlAFeeFlat(WL_FEE_FLAT);
        transactionFeeConditionalLogic.setWlAFeePercentage(WL_FEE_PERCENTAGE);

        ticketingDto = new TicketingDto();
        ticketingDto.setAllowEditingOfDisclaimer(true);
        String timeZoneId = "India Standard Time (IST)";
        ticketingDto.setTimezoneId(timeZoneId);
        ticketingDto.setShowRemainingTickets(true);
        ticketingDto.setEventStartDate(date);
        ticketingDto.setEventEndDate(endDate);
        ticketingDto.setLongitude(longitude);
        ticketingDto.setLatitude(latitude);
        ticketingDto.setEventAddress(eventAddress);
        ticketingDto.setAllowEditingOfDisclaimer(true);
        String customDisclaimer = "Custom Disclaimer";
        ticketingDto.setCustomDisclaimer(customDisclaimer);
        ticketingDto.setSeating(true);

        categoryDto = new CategoryDto(key, label, price, true);

        ticketSettingAttributeDto = new TicketSettingAttributeDto();
        ticketSettingAttributeDto.setEnabledForTicketHolder(false);
        ticketSettingAttributeDto.setEnabledForTicketPurchaser(false);
        ticketSettingAttributeDto.setRequiredForTicketHolder(false);
        ticketSettingAttributeDto.setRequiredForTicketPurchaser(false);
        ticketSettingAttributeDto.setFieldName(firstName);
        ticketSettingAttributeDto.setId(id);

        salesTaxFeeDto = new SalesTaxFeeDto(true, 10d, "1");

        eventDesignDetail = new EventDesignDetail();

        ticketSettingDto = new TicketSettingDto();
        ticketSettingDto.addAtrtibute(ticketSettingAttributeDto);

        badgesResponseData = new BadgesResponseData();
        badgesResponseData.setBadgeId(1L);
        ids.add(1L);
        ids.add(2L);
        badgesResponseData.setTicketingTypeIds(ids);

        badgesMaster = new BadgesMaster();
        badgesMaster.setTitle("Test Badge Save And Update");
        badgesMaster.setCustom(true);
        badgesMaster.setWidth(10.0);
        badgesMaster.setHeight(10.0);
        badgesMaster.setBleedAreaHeight(5.0);
        badgesMaster.setBleedAreaWidth(5.0);
        badgesMaster.setSafeAreaHeight(5.0);
        badgesMaster.setSafeAreaWidth(5.0);
        badgesMaster.setNumberOfView(1);
        badgesMaster.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        customeBadges = new Badges();
        customeBadges.setEventId(event.getEventId());
        customeBadges.setBadgeMasterId(1L);
        customeBadges.setBadgesMaster(badgesMaster);
        customeBadges.setBadgeName("Test Badge Save And Update");
        customeBadges.setWidth(10.0);
        customeBadges.setHeight(10.0);
        customeBadges.setBleedAreaHeight(5.0);
        customeBadges.setBleedAreaWidth(5.0);
        customeBadges.setSafeAreaHeight(5.0);
        customeBadges.setSafeAreaWidth(5.0);
        customeBadges.setNumberOfView(1);
        customeBadges.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

    }

    @Test
    void test_save_success() {

        //Execution
        ticketingServiceImpl.save(ticketing);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, Mockito.times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getChartKey(), ticketing.getChartKey());
        assertEquals(ticketingData.getEventid(), ticketing.getEventid());
        assertEquals(ticketingData.getEventAddress(), ticketing.getEventAddress());
    }

    @Test
    void test_unmashler_success() throws JAXBException {

        //setup
        jaxbContext = JAXBContext.newInstance(TicketAttributeValueDto.class);
        unmarshaller = jaxbContext.createUnmarshaller();

        //Execution
        TicketAttributeValueDto actualData = ticketingServiceImpl.unmashler(xml, unmarshaller);

        assertNotNull(actualData);
        assertTrue(actualData.getHolder().getAttributes().listIterator(0).next().getValue().contains(firstName));
        assertTrue(actualData.getHolder().getAttributes().listIterator(1).next().getValue().contains(lastName));
        assertTrue(actualData.getHolder().getAttributes().listIterator(2).next().getValue().contains(email));
        assertTrue(actualData.getHolder().getQuestions().listIterator(0).next().getValue().contains(lastName));
        assertTrue(actualData.getPurchaser().getAttributes().listIterator(0).next().getValue().contains(firstName));
        assertTrue(actualData.getPurchaser().getAttributes().listIterator(1).next().getValue().contains(lastName));
        assertTrue(actualData.getPurchaser().getAttributes().listIterator(2).next().getValue().contains(email));
        assertTrue(actualData.getPurchaser().getQuestions().listIterator(0).next().getValue().contains(lastName));
    }

    @Test
    void test_getAllTicketBuyers_success_with_recurringEventId() {

        //setup
        pageSizeSearchObj.setSearch(search);
        ticketingBuyerDataFromDB.setCancelCount(0L);
        ticketingBuyerDataFromDB.setRefundCount(0);
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        //mock
        when(ticketingHelperService.getPurchaserInfoyByOrderIds(any(), any())).thenReturn(purchaserInfoByOrderId);
        when(eventCommonRepoService.getTicketBuyerDataSearch(anyLong(),
                any(), any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);

        //Execution
        TicketingBuyerDataWithAmount allTicketBuyersData = ticketingServiceImpl.getAllTicketBuyers(event, pageSizeSearchObj, recurringEventId, DataType.TICKET);

        assertNotNull(allTicketBuyersData);
        assertEquals(orderId, allTicketBuyersData.getBuyerData().iterator().next().getOrderNo());
        assertEquals(firstName + " " + lastName, allTicketBuyersData.getBuyerData().iterator().next().getTicketBuyerName());
        assertEquals(ticketingBuyerDataFromDB.getOrderStatus().toString(), allTicketBuyersData.getBuyerData().iterator().next().getPaymentMode());
        assertEquals(ticketingBuyerDataFromDB.getTotalTicketCount(), allTicketBuyersData.getBuyerData().iterator().next().getQuantity());
        assertTrue(ticketingBuyerDataFromDB.getOrderPaidAmount() == allTicketBuyersData.getBuyerData().iterator().next().getOrderAmount());
        assertTrue(ticketingBuyerDataFromDB.getOrderRefundedAmount() == allTicketBuyersData.getBuyerData().iterator().next().getRefundedAmount());
        assertEquals(ticketingBuyerDataFromDBPage.getNumberOfElements(), allTicketBuyersData.getRecordsFiltered());
    }

    private Page<TicketingBuyerDataFromDB> getAllTicketBuyers(TicketingBuyerDataFromDB ticketingBuyerDataFromDB, Pageable pageable, long l) {
        return new PageImpl<TicketingBuyerDataFromDB>(Collections.singletonList(ticketingBuyerDataFromDB), pageable, l);
    }

    @Test
    void test_getAllTicketBuyers_success_with_recurringEventId_null() {

        //setup
        pageSizeSearchObj.setSearch(search);
        ticketingBuyerDataFromDB.setCancelCount(0L);
        ticketingBuyerDataFromDB.setRefundCount(0);
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        //mock
        when(ticketingHelperService.getPurchaserInfoyByOrderIds(any(), any())).thenReturn(purchaserInfoByOrderId);
        when(eventCommonRepoService.getTicketBuyerDataSearch(anyLong(),
                any(), any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);

        //Execution
        TicketingBuyerDataWithAmount allTicketBuyersData = ticketingServiceImpl.getAllTicketBuyers(event, pageSizeSearchObj, null, DataType.TICKET);

        assertNotNull(allTicketBuyersData);
        assertEquals(orderId, allTicketBuyersData.getBuyerData().iterator().next().getOrderNo());
        assertEquals(firstName + " " + lastName, allTicketBuyersData.getBuyerData().iterator().next().getTicketBuyerName());
        assertEquals(ticketingBuyerDataFromDB.getOrderStatus().toString(), allTicketBuyersData.getBuyerData().iterator().next().getPaymentMode());
        assertEquals(ticketingBuyerDataFromDB.getTotalTicketCount(), allTicketBuyersData.getBuyerData().iterator().next().getQuantity());
        assertEquals(ticketingBuyerDataFromDB.getOrderPaidAmount(), allTicketBuyersData.getBuyerData().iterator().next().getOrderAmount());
        assertEquals(ticketingBuyerDataFromDB.getOrderRefundedAmount(), allTicketBuyersData.getBuyerData().iterator().next().getRefundedAmount());
        assertEquals(ticketingBuyerDataFromDBPage.getNumberOfElements(), allTicketBuyersData.getRecordsFiltered());
    }

    @Test
    void testGetAllTicketBuyersSuccessfullyWithPaymentModePartial() {

        //setup
        pageSizeSearchObj.setSearch(search);
        ticketingBuyerDataFromDB.setCancelCount(1L);
        ticketingBuyerDataFromDB.setRefundCount(1);
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        //mock
        when(ticketingHelperService.getPurchaserInfoyByOrderIds(any(), any())).thenReturn(purchaserInfoByOrderId);
        when(eventCommonRepoService.getTicketBuyerDataSearch(anyLong(),
                any(), any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);

        //Execution
        TicketingBuyerDataWithAmount allTicketBuyersData = ticketingServiceImpl.getAllTicketBuyers(event, pageSizeSearchObj, null, DataType.TICKET);

        assertNotNull(allTicketBuyersData);
        assertEquals(orderId, allTicketBuyersData.getBuyerData().iterator().next().getOrderNo());
        assertEquals(firstName + " " + lastName, allTicketBuyersData.getBuyerData().iterator().next().getTicketBuyerName());
        assertEquals(PARTIALLY_REFUNDED, allTicketBuyersData.getBuyerData().iterator().next().getPaymentMode());
        assertEquals(ticketingBuyerDataFromDB.getTotalTicketCount(), allTicketBuyersData.getBuyerData().iterator().next().getQuantity());
        assertEquals(ticketingBuyerDataFromDB.getOrderPaidAmount(), allTicketBuyersData.getBuyerData().iterator().next().getOrderAmount(), 0.0);
        assertEquals(ticketingBuyerDataFromDB.getOrderRefundedAmount(), allTicketBuyersData.getBuyerData().iterator().next().getRefundedAmount(), 0.0);
        assertEquals(ticketingBuyerDataFromDBPage.getNumberOfElements(), allTicketBuyersData.getRecordsFiltered());
    }

    @Test
    void testGetAllTicketBuyersSuccessfullyWithPaymentModeCanceled() {

        //setup
        pageSizeSearchObj.setSearch(search);
        ticketingBuyerDataFromDB.setCancelCount(6L);
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        //mock
        when(ticketingHelperService.getPurchaserInfoyByOrderIds(any(), any())).thenReturn(purchaserInfoByOrderId);
        when(eventCommonRepoService.getTicketBuyerDataSearch(anyLong(),
                any(), any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);

        //Execution
        TicketingBuyerDataWithAmount allTicketBuyersData = ticketingServiceImpl.getAllTicketBuyers(event, pageSizeSearchObj, null, DataType.TICKET);

        assertNotNull(allTicketBuyersData);
        assertEquals(orderId, allTicketBuyersData.getBuyerData().iterator().next().getOrderNo());
        assertEquals(firstName + " " + lastName, allTicketBuyersData.getBuyerData().iterator().next().getTicketBuyerName());
        assertEquals(CANCELED, allTicketBuyersData.getBuyerData().iterator().next().getPaymentMode());
        assertEquals(ticketingBuyerDataFromDB.getTotalTicketCount(), allTicketBuyersData.getBuyerData().iterator().next().getQuantity());
        assertEquals(ticketingBuyerDataFromDB.getOrderPaidAmount(), allTicketBuyersData.getBuyerData().iterator().next().getOrderAmount(), 0.0);
        assertEquals(ticketingBuyerDataFromDB.getOrderRefundedAmount(), allTicketBuyersData.getBuyerData().iterator().next().getRefundedAmount(), 0.0);
        assertEquals(ticketingBuyerDataFromDBPage.getNumberOfElements(), allTicketBuyersData.getRecordsFiltered());
    }


    @Test
    void testPrepareUserEventOrderDtoSuccessWithStatusCanceled() {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        ticketingOrder.setEventid(event);


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
           when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
           when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
           when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(new ArrayList<>());
           when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(eventTicketsList);

        //Execution
        UserEventOrderDto userEventOrderDto = ticketingServiceImpl.prepareUserEventOrderDto(ticketingOrder,user);
        assertEquals(TICKETING_STATUS_CANCELED, userEventOrderDto.getStatus());


    }


    @Test
    void testPrepareUserEventOrderDtoSuccessWithStatusRefunded() {

        //setup
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        ticketingOrder.setEventid(event);


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(new ArrayList<>());
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(eventTicketsList);

        //Execution
        UserEventOrderDto userEventOrderDto = ticketingServiceImpl.prepareUserEventOrderDto(ticketingOrder,user);
        assertEquals(TICKETING_STATUS_REFUNDED, userEventOrderDto.getStatus());

    }

    @Test
    void testPrepareUserEventOrderDtoSuccessWithStatusPartialRefunded() {

        //setup
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTickets.setRefundedAmount(3.0);
        ticketingOrder.setEventid(event);


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(new ArrayList<>());
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(eventTicketsList);

        //Execution
        UserEventOrderDto userEventOrderDto = ticketingServiceImpl.prepareUserEventOrderDto(ticketingOrder,user);
        assertEquals(STATUS_TO_DISPLAY_PARTIALLY_REFUNDED, userEventOrderDto.getStatus());

    }

    @Test
    void test_getAllTicketBuyers_success_with_pageSearch_empty() {

        //setup
        long totalTicketCount = refundCount;
        ticketingBuyerDataFromDB = new TicketingBuyerDataFromDB(orderId, firstName, lastName,
                orderDate, orderRefundedAmount, orderPaidAmount, orderAEFee, orderAmount, countOfIndividualTickets,
                countOfNonIndividualTickets, totalTicketCount, 0,refundCount, TicketingOrder.TicketingOrderStatus.PAID, orderWLFee, refundedTotalWLFee, refundedAEFee, salesTaxFee, refundedSalestaxFee, vatTaxFee, refundedVatTaxFee,1l);

        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        //mock
        when(ticketingHelperService.getPurchaserInfoyByOrderIds(any(), any())).thenReturn(purchaserInfoByOrderId);
        when(commonEventService.getBuyerData(anyLong(),
                any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);
        when(commonEventService.getBuyerData(anyLong(),
                any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);
        //when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        //Execution
        TicketingBuyerDataWithAmount allTicketBuyersData = ticketingServiceImpl.getAllTicketBuyers(event, pageSizeSearchObj, recurringEventId, DataType.TICKET);

        assertNotNull(allTicketBuyersData);
        assertEquals(orderId, allTicketBuyersData.getBuyerData().iterator().next().getOrderNo());
        assertEquals(firstName + " " + lastName, allTicketBuyersData.getBuyerData().iterator().next().getTicketBuyerName());
        assertEquals(REFUNDED, allTicketBuyersData.getBuyerData().iterator().next().getPaymentMode());
        assertEquals(ticketingBuyerDataFromDB.getTotalTicketCount(), allTicketBuyersData.getBuyerData().iterator().next().getQuantity());
        assertEquals(ticketingBuyerDataFromDB.getOrderPaidAmount(), allTicketBuyersData.getBuyerData().iterator().next().getOrderAmount());
        assertEquals(ticketingBuyerDataFromDB.getOrderRefundedAmount(), allTicketBuyersData.getBuyerData().iterator().next().getRefundedAmount());
        assertEquals(ticketingBuyerDataFromDBPage.getNumberOfElements(), allTicketBuyersData.getRecordsFiltered());
    }

    @Test
    void test_setGrossSaleAndNetSale_success_with_orderGrossAmount_zero() {

        //setup
        long totalTicketCount = refundCount;
        ticketingBuyerDataFromDB = new TicketingBuyerDataFromDB(orderId, firstName, lastName,
                orderDate, 0, 0, orderAEFee, orderAmount, countOfIndividualTickets,
                countOfNonIndividualTickets, totalTicketCount, cancelCount,refundCount, TicketingOrder.TicketingOrderStatus.PAID, orderWLFee, refundedTotalWLFee, refundedAEFee, salesTaxFee, refundedSalestaxFee, vatTaxFee, refundedVatTaxFee, 1l);

        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBPage = getAllTicketBuyers(ticketingBuyerDataFromDB, pageable, 1L);

        TicketingBuyerDataWithAmount buyerDataWithAmount = new TicketingBuyerDataWithAmount();

        //mock
        when(commonEventService.getBuyerData(anyLong(), any(), any(), any())).thenReturn(ticketingBuyerDataFromDBPage);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        //Execution
        ticketingServiceImpl.setGrossSaleAndNetSale(buyerDataWithAmount, event, recurringEventId, DataType.TICKET);
    }

    @Test
    void test_getFee_success_with_orderAeFee_greaterThan_zero() {

        //setup
        double ccFee = ((orderPaidAmount * stripeDTO.getCCPercentageFee()) / 100) + stripeDTO.getCCFlatFee();
        double refundedCCFee = (orderRefundedAmount * ccFee) / orderPaidAmount;

        double totalFee = (ticketingBuyerDataFromDB.getOrderAEFee() - ticketingBuyerDataFromDB.getRefundedAEFee()) + (ccFee - refundedCCFee)
                + (ticketingBuyerDataFromDB.getOrderWLFee() - ticketingBuyerDataFromDB.getTotalRefundedWLFee())
                + (ticketingBuyerDataFromDB.getOrderSalesTaxFee() - ticketingBuyerDataFromDB.getRefundedSalesTaxFee());

        //Execution
        double fee = ticketingServiceImpl.getFee(ticketingBuyerDataFromDB, stripeDTO);

        assertEquals(fee, totalFee);
    }

    @Test
    void test_getFee_success_with_orderAeFee_lessThan_zero() {

        //setup
        double ccFee = 0d;
        double refundedCCFee = 0d;
        double orderAEFee = 0d;

        ticketingBuyerDataFromDB = new TicketingBuyerDataFromDB(orderRefundedAmount, orderPaidAmount, orderAEFee, orderWLFee, refundedTotalWLFee, refundedAEFee, salesTaxFee, refundedSalestaxFee, vatTaxFee, refundedVatTaxFee, 0);

        double totalFee = (ticketingBuyerDataFromDB.getOrderAEFee() - ticketingBuyerDataFromDB.getRefundedAEFee()) + (ccFee - refundedCCFee)
                + (ticketingBuyerDataFromDB.getOrderWLFee() - ticketingBuyerDataFromDB.getTotalRefundedWLFee())
                + (ticketingBuyerDataFromDB.getOrderSalesTaxFee() - ticketingBuyerDataFromDB.getRefundedSalesTaxFee());

        //Execution
        double fee = ticketingServiceImpl.getFee(ticketingBuyerDataFromDB, stripeDTO);

        assertEquals(fee, totalFee);
    }


    @Test
    void test_deleteEventType_success() {

        //setup
        ticketingType.setTicketing(ticketing);
        ticketingType.setPrice(0d);
        //ticketingType.setCategories(categories);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        List<Long> ticketingTypeIdsList = new ArrayList<>();
        ticketingTypeIdsList.add(id);

        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        //mock
        when(ticketingTypeService.getTicketTypeByCreateFrom(ticketingTypeId)).thenReturn(ticketingTypeList);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(false);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(ticketingTypeService.findByidAndEvent(ticketingTypeId, event)).thenReturn(ticketingType);
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);
        doReturn(null).when(eventChecklistService).findByEvent(any());
        when(sessionRepoService.getAllSessionByEventId(event, null, true)).thenReturn(Arrays.asList());
        SeatingCategories seatingCategories = new SeatingCategories();
        seatingCategories.setQuantity(10);

        //Execution
        ticketingServiceImpl.deleteEventType(ticketingTypeId, event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(2)).save(ticketingArgumentCaptor.capture());

        Ticketing actualData = ticketingArgumentCaptor.getValue();
        assertTrue(actualData.getActivated());

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).delete(ticketingTypeArgumentCaptor.capture());
    }

    @Test
    void test_deleteEventType_success_with_ticketingTypeList_empty() {

        //setup
        ticketingType.setTicketing(ticketing);
        ticketingType.setPrice(0d);
        //ticketingType.setCategories(categories);

        Integration integration = new Integration();
        integration.setTicketingType(ticketingType);
        integration.setId(1L);

        List<TicketingType> ticketingTypeList = new ArrayList<>();

        List<Long> ticketingTypeIdsList = new ArrayList<>();
        ticketingTypeIdsList.add(id);

        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        //mock
        when(ticketingTypeService.getTicketTypeByCreateFrom(ticketingTypeId)).thenReturn(ticketingTypeList);
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(ticketingTypeService.findByidAndEvent(ticketingTypeId, event)).thenReturn(ticketingType);
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);
        Mockito.doNothing().when(sessionService).updateTicketTypesThatCanBeRegistered(ticketingType.getId(), event);
        when(integrationService.getAllIntegrationDetailsListByTicketingTypeId(ticketingTypeId)).thenReturn(Collections.singletonList(integration));
        SeatingCategories seatingCategories = new SeatingCategories();
        seatingCategories.setQuantity(10);

        //Execution
        ticketingServiceImpl.deleteEventType(ticketingTypeId, event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actualData = ticketingArgumentCaptor.getValue();
        assertTrue(actualData.getActivated());

        verify(ticketingTypeService).updateTicketingTypeRecordStatusToCancel(any());
        verify(integrationService).saveAll(anyList());
    }


    @Test
    void test_deleteRecurringEventTicketingTypes_success() {

        //setup
        ticketingType.setTicketing(ticketing);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);


        //Execution
        ticketingServiceImpl.deleteRecurringEventTicketingTypes(event, ticketingType, ticketing);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(-1, (long) actualData.getCreatedFrom());
    }

    @Test
    void test_deleteRecurringEventTicketingTypes_success_ticketingOrderManagerService_isTicketingTypeExist_true() {

        //setup
        ticketingType.setTicketing(ticketing);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(ticketingType)).thenReturn(true);


        //Execution
        ticketingServiceImpl.deleteRecurringEventTicketingTypes(event, ticketingType, ticketing);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType actualData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(-1, (long) actualData.getCreatedFrom());
    }

    @Test
    void test_deleteTicketingType_throwException_TICKET_ALREADY_PURCHASED() {

        //setup
        ticketingType.setTicketing(ticketing);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingServiceImpl.deleteTicketingType(event, ticketingType));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_ALREADY_PURCHASED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteTicketingType_throwException_TICKET_ALREADY_PURCHASED1() {

        //setup
        ticketingType.setTicketing(ticketing);

        //mock
        when(eventCommonRepoService.isTypePurchased(ticketingType)).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExistWithNotDeletedOrder(ticketingType)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingServiceImpl.deleteTicketingType(event, ticketingType));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_ALREADY_PURCHASED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getTicketType_success_() {

        //setup
        ticketingType.setPrice(10d);
        ticketingType.setTicketing(ticketing);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        ticketingServiceImpl.checkFreeTicketingTypesAvailableThenSetModuleActivated(event, DataType.TICKET);
    }

    @Test
    void test_getTicketType_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        //mock
        when(ticketingTypeTicketService.getAllTicketingTypesByRecuurringEvent(recurringEventId)).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketTypeData = ticketingServiceImpl.getTicketType(event, recurringEventId, true);

        assertEquals(ticketTypeData.iterator().next().getCreatedFrom(), ticketingType.getCreatedFrom());
        assertEquals(ticketingTypeList.iterator().next().getRecurringEventId(), ticketingType.getRecurringEventId());
        assertEquals(ticketTypeData.iterator().next().isHidden(), ticketingType.isHidden());
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo_success() {

        //setup
        ticketingType.setId(2L);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock
        when(ticketingTypeTicketService.getAllTicketingTypes(event.getEventId())).thenReturn(categoryDtoList);
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);
        Mockito.doNothing().when(ticketingTypeService).delete(any());

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketing, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo_success_with_chartKey_empty() {

        //setup
        ticketingType.setId(2L);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        ticketing.setChartKey("");

        //mock
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);
        Mockito.doNothing().when(ticketingTypeService).delete(any());

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketing, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo1_success_categories_null() {

        //setup
        ticketing.setChartKey("");
        ticketingType.setTicketing(ticketing);
        ticketingType.setId(2L);
        //ticketingType.setCategories(null);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo1_success_with_chartKey_and_categoryColor() {

        //setup
        ticketing.setChartKey("ChartKey");
        ticketingType.setTicketing(ticketing);
        ticketingType.setId(2L);
        //ticketingType.setCategories("2,3");

        List<TicketingType> categoryDtos = new ArrayList<>();
        categoryDtos.add(ticketingType);

        seatingCategories.setColor("#D1D1D1");

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo1_success_with_chartKey_and_seatingCategory_empty() {

        //setup
        ticketing.setChartKey("ChartKey");
        ticketingType.setTicketing(ticketing);
        ticketingType.setId(2L);
        //ticketingType.setCategories("1");

        List<TicketingType> categoryDtos = new ArrayList<>();
        categoryDtos.add(ticketingType);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo1_success_with_chartKey_and_seatingCategory() {

        //setup
        ticketing.setChartKey("ChartKey");
        ticketingType.setTicketing(ticketing);
        ticketingType.setId(2L);
        //ticketingType.setCategories("1");

        List<TicketingType> categoryDtos = new ArrayList<>();
        categoryDtos.add(ticketingType);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock

        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
    }

    @Test
    void test_deleteTicketingTypesAndReleaseSeatsIo1_success_with_chartKey_and_categories_empty() {

        //setup
        ticketing.setChartKey("ChartKey");
        ticketingType.setTicketing(ticketing);
        ticketingType.setId(2L);
        //ticketingType.setCategories("");

        List<TicketingType> categoryDtos = new ArrayList<>();
        categoryDtos.add(ticketingType);

        CategoryDto categoryDto = new CategoryDto(key, label, price, color, true, recurringEventId);

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock
        Mockito.doNothing().when(ticketingOrderManagerService).deleteByTicketType(ticketingType);

        //Execution
        ticketingServiceImpl.deleteTicketingTypesAndReleaseSeatsIo(event, ticketingType);
    }

    @Test
    void test_getBuyer_success() {

        //setup
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketingOrder(ticketingOrder);

        //Execution
        User userData = ticketingServiceImpl.getBuyer(eventTickets);

        assertEquals(userData.getFirstName(), user.getFirstName());
        assertEquals(userData.getLastName(), user.getLastName());
        assertEquals(userData.getEmail(), user.getEmail());
    }

    @Test
    void test_handleAttendeeAttribute_success() {

        //setup
        String search = firstName;

        //Execution
        boolean userData = ticketingServiceImpl.handleAttendeeAttribute(search, attendeeDto, ticketHolderAttributes);

        assertTrue(userData);
    }

    @Test
    void test_handleAttendeeAttribute_success_with_search_empty_and_holderMap_empty() {

        //setup
        String search = "search";

        Map<String, String> attributesMap = new HashMap<>();

        ticketAttributeValueDto1.setHolder(attributesMap);

        //        doReturn(true).when(ticketingServiceImpl).searchMapBuyerHolderMap(anyMap(), anyString());

        //Execution
        boolean userData = ticketingServiceImpl.handleAttendeeAttribute(search, attendeeDto, ticketHolderAttributes);

        assertFalse(userData);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_buyer_firstName() {

        //setup
        String searchKey = Constants.STRING_FIRST_SPACE_NAME;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, Constants.STRING_FIRST_SPACE_NAME);

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertTrue(searchMapValue);
    }


    @Test
    void test_searchMapBuyerHolderMap_with_buyer_lastName() {

        //setup
        String searchKey = Constants.STRING_LAST_SPACE_NAME;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, Constants.STRING_LAST_SPACE_NAME);

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertTrue(searchMapValue);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_buyer_email() {

        //setup
        String searchKey = Constants.STRING_EMAIL_SPACE;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, Constants.STRING_EMAIL_SPACE);

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertTrue(searchMapValue);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_buyer_email1() {

        //setup
        String searchKey = Constants.STRING_EMAIL_SPACE;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, Constants.STRING_FIRST_SPACE_NAME);
        searchMap.put(searchKey, Constants.STRING_LAST_SPACE_NAME);
        searchMap.put(searchKey, Constants.STRING_EMAIL_SPACE);
        searchMap.put(searchKey, Constants.STRING_PHONE_NUMBER);

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertFalse(searchMapValue);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_buyer_phoneNumber() {

        //setup
        String searchKey = Constants.STRING_PHONE_NUMBER;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, Constants.STRING_PHONE_NUMBER);

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertTrue(searchMapValue);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_buyer_phoneNumber1() {

        //setup
        String searchKey = STRING_PHONE_NUMBER;
        Map<String, String> searchMap = new HashMap<>();
        searchMap.put(searchKey, "");

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(searchMap, searchKey);

        assertFalse(searchMapValue);
    }

    @Test
    void test_searchMapBuyerHolderMap_with_searchMap_null() {

        //setup
        String searchKey = STRING_CELL;

        //Execution
        boolean searchMapValue = ticketingServiceImpl.searchMapBuyerHolderMap(null, searchKey);

        assertFalse(searchMapValue);
    }

    @Test
    void test_update_success() {

        //Execution
        ticketingServiceImpl.save(ticketing);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actualData = ticketingArgumentCaptor.getValue();
        assertEquals(actualData.getId(), ticketing.getId());
        assertEquals(actualData.getActivated(), ticketing.getActivated());
        assertEquals(actualData.getChartKey(), ticketing.getChartKey());
        assertEquals(actualData.getEventAddress(), ticketing.getEventAddress());
    }

    @Test
    void test_getTicketFeesLogic_success_with_transactionFeeConditionalLogic_empty() {

        //setup
        PlanConfig planConfig = new PlanConfig();
        planConfig.setId(4);
        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setId(id);
        whiteLabel.setPlanConfig(planConfig);

        event.setWhiteLabel(whiteLabel);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();

        //mock
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);
        when(transactionFeeConditionalLogicService.getAeFeePercentage(true, planConfig, GREATER_THAN_EQUAL_TO, BillingType.Paid, event)).thenReturn(AE_FEE_PERCENTAGE_THREE);

        //Execution
        List<TicketingFeeDto> ticketFeesLogicData = ticketingServiceImpl.getTicketFeesLogic(event, Collections.emptyList());

        assertEquals(0, ticketFeesLogicData.iterator().next().getAeFeeFlat());
        assertEquals(AE_FEE_PERCENTAGE_THREE, ticketFeesLogicData.iterator().next().getAeFeePercentage());
        assertEquals(ticketFeesLogicData.iterator().next().getCreditCardProcessingFlat(), creditCardChargesDto.getCreditCardFlat());
        assertEquals(ticketFeesLogicData.iterator().next().getCreditCardProcessingPercentage(), creditCardChargesDto.getCreditCardPercentage());
        assertEquals(WL_FEE_FLAT, ticketFeesLogicData.iterator().next().getWlFeeFlat());
        assertEquals(WL_FEE_PERCENTAGE, ticketFeesLogicData.iterator().next().getWlFeePercentage());
    }

    @Test
    void test_getTicketFeesLogic_success_with_transactionFeeConditionalLogic() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByEventAndIsVirtualEventAndIsAddon(event,
                EventFormat.VIRTUAL.equals(event.getEventFormat()) || EventFormat.HYBRID.equals(event.getEventFormat()), false)).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);

        //Execution
        List<TicketingFeeDto> ticketFeesLogicData = ticketingServiceImpl.getTicketFeesLogic(event, Collections.emptyList());

        assertEquals(ticketFeesLogicData.iterator().next().getAeFeeFlat(), transactionFeeConditionalLogic.getAeFeeFlat());
        assertEquals(ticketFeesLogicData.iterator().next().getAeFeePercentage(), transactionFeeConditionalLogic.getAeFeePercentage());
        assertEquals(ticketFeesLogicData.iterator().next().getCreditCardProcessingFlat(), creditCardChargesDto.getCreditCardFlat());
        assertEquals(ticketFeesLogicData.iterator().next().getCreditCardProcessingPercentage(), creditCardChargesDto.getCreditCardPercentage());
        assertEquals(ticketFeesLogicData.iterator().next().getWlFeeFlat(), transactionFeeConditionalLogic.getWlAFeeFlat());
        assertEquals(ticketFeesLogicData.iterator().next().getWlFeePercentage(), transactionFeeConditionalLogic.getWlAFeePercentage());
    }

    @Test
    void test_getTicketing_sussecc_with_recurringEvent() {

        //setup
        Boolean fetchHiddenTicketsOnly = true;

        String eventKey = event.getEventId() + STRING_DASH + recurringEvents.getId();

        ticketing.setLongitude(longitude);
        ticketing.setLatitude(latitude);

        //ticketingType.setCategories(categories);

        List<TicketingType> ticketingTypesList = new ArrayList<>();
        ticketingTypesList.add(ticketingType);

        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();

        eventCategoryDto.setName(categoryName);
        eventCategoryDto.setHavingVariations(true);
        eventCategoryDto.setId(id);

        //mock
        when(recurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEvents));
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        when(eventService.getEventEndDate(event)).thenReturn(endDate1);


        when(stripeService.getCCProcessingDetails(event)).thenReturn(creditCardChargesDto);
        doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(event.getEventId());
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(badgesService.getTicketingIdAndBadgesIdByEventId(anyLong())).thenReturn(Collections.singletonList(badgesResponseData));
        //Execution
        CreateTicketingDto ticketingDtoData = ticketingServiceImpl.getTicketing(event, fetchHiddenTicketsOnly, recurringEventId, List.of(DataType.TICKET), true);

        assertNotNull(ticketingDtoData);
        assertEquals(ticketingDtoData.getEventAddress(), ticketing.getEventAddress());
        assertEquals(ticketingDtoData.getLatitude(), ticketing.getLatitude());
        assertEquals(ticketingDtoData.getLongitude(), ticketing.getLongitude());
        assertEquals(ticketingDtoData.getEventVenueType(), EnumEventVenue.VENUE.name());
        assertEquals(ticketingDtoData.getShowRemainingTickets(), ticketing.isShowRemainingTickets());
        assertEquals(ticketingDtoData.getEventKey(), eventKey);
        assertEquals(ticketingDtoData.getSeatingChartKey(), ticketing.getChartKey());
        assertEquals(ticketingDtoData.getTimezoneId(), event.getTimezoneId());
//        assertEquals(ticketingDtoData.getTicketTypes().iterator().next().getEventCategories().iterator().next().getName(), eventCategoryDto.getName());
    }

    @Test
    void test_updateAttributesByToggle() {

        ticketingType.setTicketing(ticketing);
        ticketingType.setPrice(0d);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        when(ticketHolderRequiredAttributesService.findBynameAndEventidRecurringEventId(anyString(), any(), anyLong(), any())).thenReturn(ticketHolderRequiredAttributes);

        //Execution
        ticketingServiceImpl.updateAttributesByToggle(ticketSettingDto, event, user, false, 1L, ticketingTypeList, false);

        assertNotNull(ticketHolderRequiredAttributes);
        ticketingTypeList.forEach(e -> {
            assertEquals(ticketingType, e);
        });
    }

    @Test
    void test_updateAttributesByToggle_recurringEventIdLessThenZero() {

        ticketingType.setTicketing(ticketing);
        ticketingType.setPrice(0d);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);


        when(allRequiresAttributesService.findById(anyLong())).thenReturn(ticketHolderRequiredAttributes);

        //Execution
        ticketingServiceImpl.updateAttributesByToggle(ticketSettingDto, event, user, false, 0L, ticketingTypeList, false);

        assertNotNull(ticketHolderRequiredAttributes);
        ticketingTypeList.forEach(e -> {
            assertEquals(ticketingType, e);
        });
    }

    @Test
    void getCustomAttribute() {
        //setup
        long attributeId = 1L;
        boolean isForBuyer = true;

        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(attributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        //Execution
        List<CustomAttribute> customAttributeList = ticketingServiceImpl.getCustomAttribute(attributeId, isForBuyer, event);

        assertNotNull(customAttributeList);
    }

    @Test
    void getCustomAttribute_conditionalQuestion() {
        //setup
        long attributeId = 1L;
        boolean isForBuyer = true;
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.CONDITIONAL_QUE);

        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(attributeId)).thenReturn(Optional.of(ticketHolderRequiredAttributes));

        //Execution
        List<CustomAttribute> customAttributeList = ticketingServiceImpl.getCustomAttribute(attributeId, isForBuyer, event);

        assertNotNull(customAttributeList);
    }

    @Test
    void getCustomAttribute_ATTRIBUTE_NOT_EXISTS() {
        //setup
        long attributeId = 1L;
        boolean isForBuyer = true;
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.CONDITIONAL_QUE);

        //mock
        when(ticketHolderRequiredAttributesService.findCustomAttributeById(attributeId)).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingServiceImpl.getCustomAttribute(attributeId, isForBuyer, event));

        assertEquals(NotAcceptableException.TicketHolderAttributesMsg.ATTRIBUTE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());

        verify(ticketHolderRequiredAttributesService).findCustomAttributeById(anyLong());
    }
}