package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.repositories.TicketingOrderManagerRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingOrderManagerServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingOrderManagerServiceImpl ticketingOrderManagerServiceImpl;
    @Mock
    private TicketingOrderManagerRepository ticketingOrderManagerRepository;

    private Event event;
    private TicketingOrderManager ticketingOrderManager;
    private TicketingOrder ticketingOrder;
    private TicketingType ticketingType;
    private TicketingCoupon ticketingCoupon;
    private User user;

    private Long id = 1L;
    private Long recurringEventId = 1L;


    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        ticketingType = EventDataUtil.getTicketingType(event);
        ticketingOrderManager = EventDataUtil.getTicketingOrderManager();
        ticketingCoupon = EventDataUtil.getTicketingCoupon();
        user = EventDataUtil.getUser();
    }

    @Test
    void test_save_success() {

        //Execution
        ticketingOrderManagerServiceImpl.save(ticketingOrderManager);

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerRepository, times(1)).save(ticketingOrderManagerArgumentCaptor.capture());

        TicketingOrderManager actual = ticketingOrderManagerArgumentCaptor.getValue();
        assertEquals(ticketingOrderManager.getId(), actual.getId());
        assertEquals(ticketingOrderManager.getSeats(), actual.getSeats());
        assertEquals(ticketingOrderManager.getTicketType().getTicketType(), actual.getTicketType().getTicketType());
        assertEquals(ticketingOrderManager.getOrderId().getId(), actual.getOrderId().getId());
    }

    @Test
    void test_getAllByOrderId_success() {

        //setup
        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManagers.add(ticketingOrderManager);

        //mock
        when(ticketingOrderManagerRepository.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);

        //Execution
        List<TicketingOrderManager> ticketingOrderManagerData = ticketingOrderManagerServiceImpl.getAllByOrderId(ticketingOrder);

        //assert
        assertNotNull(ticketingOrderManagerData);
        assertEquals(ticketingOrderManagers.get(0).getId(),ticketingOrderManagerData.get(0).getId());
        assertEquals(ticketingOrderManagers.get(0).getSeats(),ticketingOrderManagerData.get(0).getSeats());
        assertEquals(ticketingOrderManagers.get(0).getTicketType().getTicketType(),ticketingOrderManagerData.get(0).getTicketType().getTicketType());
        assertEquals(ticketingOrderManagers.get(0).getOrderId().getId(),ticketingOrderManagerData.get(0).getOrderId().getId());
    }

    @Test
    void test_getCreateNotExpiredTicket_success() {

        //setup
        Date currentdate = new Date();
        BigInteger createNotExpiredTickets = BigInteger.valueOf(54);

        //mock
        when(ticketingOrderManagerRepository.getCreateNotExpiredTicket(ticketingType, currentdate,
                TicketingOrder.TicketingOrderStatus.CREATE)).thenReturn(createNotExpiredTickets);

        //Execution
        BigInteger CreateNotExpiredTicketData = ticketingOrderManagerServiceImpl.getCreateNotExpiredTicket(ticketingType, currentdate);
        assertNotNull(CreateNotExpiredTicketData);
        assertEquals(createNotExpiredTickets, CreateNotExpiredTicketData);
    }

    @Test
    void test_isTicketingTypeExist_success() {

        //mock
        when(ticketingOrderManagerRepository.isTicketingTypeExist(isA(TicketingType.class), isA(Date.class))).thenReturn(false);

        //Execution
        boolean ticketingTypeExist = ticketingOrderManagerServiceImpl.isTicketingTypeExist(ticketingType);
        assertFalse(ticketingTypeExist);
    }

    @Test
    void test_getTicketTypeByOrderId_success() {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //mock
        when(ticketingOrderManagerRepository.getTicketTypeByOrderId(ticketingOrder)).thenReturn(ticketingTypes);

        //Execution
        List<TicketingType> ticketTypeByOrderIdData = ticketingOrderManagerServiceImpl.getTicketTypeByOrderId(ticketingOrder);
        assertNotNull(ticketTypeByOrderIdData);
        for (TicketingType actualData : ticketTypeByOrderIdData) {
            assertEquals(ticketingType.getTicketType(), actualData.getTicketType());
            assertEquals(ticketingType.getId(), actualData.getId());
        }
    }

    @Test
    void test_deleteByTicketType_success() {

        //Execution
        ticketingOrderManagerServiceImpl.deleteByTicketType(ticketingType);

        verify(ticketingOrderManagerRepository, times(1)).deleteByTicketType(ticketingType);
    }

    @Test
    void test_deleteByTicketingOrder_success() {

        //Execution
        ticketingOrderManagerServiceImpl.deleteByTicketingOrder(ticketingOrder);

        verify(ticketingOrderManagerRepository, times(1)).deleteByTicketingOrder(ticketingOrder);
    }

    @Test
    void test_getTicketTypeByOrderIdAndTicketTypeId_success() {

        //mock
        when(ticketingOrderManagerRepository.findByTicketingOrderAndTicketingOrderManager(ticketingType, ticketingOrder)).thenReturn(ticketingOrderManager);

        //Execution
        TicketingOrderManager actualData = ticketingOrderManagerServiceImpl.getTicketTypeByOrderIdAndTicketTypeId(ticketingType, ticketingOrder);
        assertNotNull(actualData);
        assertEquals(ticketingType.getId(), actualData.getTicketType().getId());
    }

    @Test
    void test_getSoldRecurringEventIdsByScheduleId_success() {

        //setup
        Long scheduleId = id;

        List<Long> soldRecurringEventIds = new ArrayList<>();
        soldRecurringEventIds.add(scheduleId);

        //mock
        when(ticketingOrderManagerRepository.findSoldRecurringEventIdsByScheduleId(any())).thenReturn(soldRecurringEventIds);

        //Execution
        List<Long> soldRecurringEventIdsData = ticketingOrderManagerServiceImpl.getSoldRecurringEventIdsByScheduleId(scheduleId);
        for (Long actualData : soldRecurringEventIdsData) {
            assertEquals(scheduleId, actualData);
        }
    }

    @Test
    void test_getOrderIdsAreInProgressByRecurringEventId_success() {

        //setup
        Long recurringEventId = id;

        List<Long> orderIdsAreInProgress = new ArrayList<>();
        orderIdsAreInProgress.add(recurringEventId);

        //mock
        when(ticketingOrderManagerRepository.findOrderIdsAreInProgressByRecurringEventId(isA(Long.class), isA(Date.class), isA(TicketingOrder.TicketingOrderStatus.class))).thenReturn(orderIdsAreInProgress);

        //Execution
        ticketingOrderManagerServiceImpl.getOrderIdsAreInProgressByRecurringEventId(recurringEventId);
        verify(ticketingOrderManagerRepository).findOrderIdsAreInProgressByRecurringEventId(isA(Long.class), isA(Date.class), isA(TicketingOrder.TicketingOrderStatus.class));
    }

    @Test
    void test_getSoldOrderIdsByRecurringEventId_success() {

        //setup
        Long recurringEventId = id;

        List<Long> soldOrderIds = new ArrayList<>();
        soldOrderIds.add(recurringEventId);

        //mock
        when(ticketingOrderManagerRepository.findSoldOrderIdsByRecurringEventId(recurringEventId)).thenReturn(soldOrderIds);

        //Execution
        List<Long> soldOrderIdsData = ticketingOrderManagerServiceImpl.getSoldOrderIdsByRecurringEventId(recurringEventId);
        for (Long actualData : soldOrderIdsData) {
            assertEquals(recurringEventId, actualData);
        }
    }

    @Test
    void test_getAllOrdersByOrderId_emptyList(){
        List<Long> ids = new ArrayList<>();
        Map<TicketingOrder, List<TicketingOrderManager>> ticketingOrderListMap = new HashMap<>();
        ticketingOrderListMap = ticketingOrderManagerServiceImpl.getAllOrdersByOrderId(ids);
        assertEquals(true, ticketingOrderListMap.isEmpty());
    }

    @Test
    void test_getAllOrdersByOrderId_notEmptyList(){
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        Map<TicketingOrder, List<TicketingOrderManager>> ticketingOrderListMap = new HashMap<>();

        TicketingOrder ticketingOrder = new TicketingOrder();

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager ticketingOrderManager1 = new TicketingOrderManager();
        ticketingOrderManager1.setId(1L);
        ticketingOrderManager1.setOrderId(ticketingOrder);
        ticketingOrderManagers.add(ticketingOrderManager1);
        when(ticketingOrderManagerRepository.getAllOrdersByOrderId(ids)).thenReturn(ticketingOrderManagers);

        ticketingOrderListMap = ticketingOrderManagerServiceImpl.getAllOrdersByOrderId(ids);

        assertEquals(1, ticketingOrderListMap.size());
    }

    public static Object[] getRecurringEventId(){

        return new Object[]{
                new Object[]{0L},
                new Object[]{1L},
        };
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_findByTicketingCouponAndEventidAndPurchaserAndRecurringEventId_success(Long recurringEventId) {

        //mock
        when(ticketingOrderManagerRepository.getListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),isA(Event.class),isA(User.class),nullable(Long.class))).thenReturn(Collections.emptyList());

        //Execution
        List<Long> orderList = ticketingOrderManagerServiceImpl.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(ticketingCoupon,event,user,recurringEventId);

        //assert
        assertTrue(orderList.isEmpty());
        verify(ticketingOrderManagerRepository).getListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(isA(TicketingCoupon.class),isA(Event.class),isA(User.class),nullable(Long.class));
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_findByTicketingCouponAndEventidAndRecurringEventId_success(Long recurringEventId) {

        //mock
        when(ticketingOrderManagerRepository.getListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(isA(TicketingCoupon.class),isA(Event.class),isNull(),nullable(Long.class))).thenReturn(Collections.emptyList());

        //Execution
        List<Long> orderList = ticketingOrderManagerServiceImpl.findListByTicketingCouponAndEventidAndRecurringEventId(ticketingCoupon,event,recurringEventId);

        //assert
        assertTrue(orderList.isEmpty());
        verify(ticketingOrderManagerRepository).getListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),isNull(),nullable(Long.class));
    }

    @Test
    void test_getNumberOfDiscountedTicket_success() {

        //mock
        when(ticketingOrderManagerRepository.findNumberOfDiscountedTicket(ticketingType,ticketingOrder)).thenReturn(1);

        //Execution
        int discountedTicket = ticketingOrderManagerServiceImpl.getNumberOfDiscountedTicket(ticketingType,ticketingOrder);

        //assert
        assertEquals(discountedTicket,1);
        verify(ticketingOrderManagerRepository).findNumberOfDiscountedTicket(ticketingType,ticketingOrder);
    }

    @Test
    void test_getSoldRecurringEventsByScheduleId_success() {

        //mock
        when(ticketingOrderManagerRepository.findSoldRecurringEventsByScheduleId(1L)).thenReturn(Collections.emptyList());

        //Execution
        List<RecurringEvents> recurringEvents = ticketingOrderManagerServiceImpl.getSoldRecurringEventsByScheduleId(1L);

        //assert
        assertTrue(recurringEvents.isEmpty());
        verify(ticketingOrderManagerRepository).findSoldRecurringEventsByScheduleId(1L);
    }

    @Test
    void test_getTicketingOrderManagerByListOfOrderIds_success() {

        //setup
        List<Long> ids = new ArrayList<>();
        ids.add(1L);

        //mock
        when(ticketingOrderManagerRepository.getAllOrdersByOrderId(ids)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingOrderManager> ticketingOrderManagers = ticketingOrderManagerServiceImpl.getTicketingOrderManagerByListOfOrderIds(ids);

        //assert
        assertTrue(ticketingOrderManagers.isEmpty());
        verify(ticketingOrderManagerRepository).getAllOrdersByOrderId(ids);
    }

    @Test
    void test_getOrderIdsAreInProgressByRecurringEventScheduleId_success() {

        //mock
        when(ticketingOrderManagerRepository.getOrderIdsAreInProgressByRecurringEventScheduleId(anyLong(),any(),any())).thenReturn(Collections.emptyList());

        //Execution
        List<Long> orderIds = ticketingOrderManagerServiceImpl.getOrderIdsAreInProgressByRecurringEventScheduleId(1L,new Date(), TicketingOrder.TicketingOrderStatus.PAID);

        //assert
        assertTrue(orderIds.isEmpty());
        verify(ticketingOrderManagerRepository).getOrderIdsAreInProgressByRecurringEventScheduleId(anyLong(),any(),any());
    }

    @Test
    void test_getAllOrderIdsByRecurringEventIds_success() {

        //setup
        List<Long> recurringEventIds = new ArrayList<>();
        recurringEventIds.add(recurringEventId);

        //mock
        when(ticketingOrderManagerRepository.getAllOrderIdsByRecurringEventIds(recurringEventIds)).thenReturn(Collections.emptyList());

        //Execution
        List<Long> orderIds = ticketingOrderManagerServiceImpl.getAllOrderIdsByRecurringEventIds(recurringEventIds);

        //assert
        assertTrue(orderIds.isEmpty());
        verify(ticketingOrderManagerRepository).getAllOrderIdsByRecurringEventIds(recurringEventIds);
    }

    @Test
    void test_updateTicketingOrderManagerRecStatusByRecurringEventIds_success() {

        //setup
        List<Long> recurringEventIds = new ArrayList<>();
        recurringEventIds.add(recurringEventId);

        //mock
        doNothing().when(ticketingOrderManagerRepository).updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);

        //Execution
        ticketingOrderManagerServiceImpl.updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds,RecordStatus.CANCEL);

        //assert
        verify(ticketingOrderManagerRepository).updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds,RecordStatus.CANCEL);
    }

}