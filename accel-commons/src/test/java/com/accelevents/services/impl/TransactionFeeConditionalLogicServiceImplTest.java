package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.ChargebeePaymentService;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.WLTypeEnum;
import com.accelevents.repositories.EventDesignDetailRepository;
import com.accelevents.repositories.TransactionFeeConditionalLogicRepository;
import com.accelevents.services.StripeService;
import com.accelevents.services.repo.helper.EventRepoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.accelevents.utils.FeeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TransactionFeeConditionalLogicServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(WhiteLabelServiceImpl.class);

    @Spy
    @InjectMocks
    private TransactionFeeConditionalLogicServiceImpl transactionFeeConditionalLogicServiceImpl;

    @Spy
    @InjectMocks
    private TransactionFeeConditionalLogic transactionFeeConditionalLogics;
    @Mock
    TransactionFeeConditionalLogicRepository transactionFeeConditionalLogicRepository;

    @Mock
    StripeService stripeService;

    @Mock
    private EventDesignDetailRepository eventDesignDetailRepository;

    @Mock
    private Event event;

    @Mock
    private ChargebeePlanService chargebeePlanService;

    @Mock
    private EventChargeUsagesRepoService eventChargeUsagesRepoService;
    @Mock
    private ChargebeeTransactionRepository chargebeeTransactionRepository;
    @Mock
    private ChargebeePaymentService chargebeePaymentService;
    @Mock
    private ChargeConfigRepoService chargeConfigRepoService;
    @Mock
    private OrganizerRepoService organizerRepoService;
    @Mock
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Mock
    private EventPlanConfigRepoService eventPlanConfigRepoService;

    @Mock
    private EventRepoService eventRepoService;

    private WhiteLabel whiteLabel;

    private TicketingType ticketingType;

    private TransactionFeeConditionalLogic transactionFeeConditionalLogic;

    private double thresholdPrice = new Double(40);
    private double aeFlat1 = new Double(1);
    private double aeFlatPercentage1 = new Double(1.9);
    private double aeFlat2 = new Double(1);
    private double aeFlatPercentage2 = new Double(2.9);

    private double wlFlat1 = new Double(1.10);
    private double wlFlatPercentage1 = new Double(0);
    private double wlFlat2 = new Double(1);
    private double wlFlatPercentage2 = new Double(0.5);
    private String operator = LESS_THAN;

    @BeforeEach
    void setUpData() {
        long whiteLabelId = 1L;
        whiteLabel = new WhiteLabel(whiteLabelId,"testWhiteLabelUrl");

        event = new Event();
        long eventId = 11L;
        event.setEventId(eventId);
        event.setEventURL("testEvent");
        event.setWhiteLabel(whiteLabel);

        ticketingType = EventDataUtil.getTicketingType(event);
    }

    /*@Test
    void test_callConditionalLogicToCreateRecord_success() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogic = getDefaultConfigs();

        //mock
        when(transactionFeeConditionalLogicRepository.findByWhiteLabelAndEventIsNullAndIsVirtualEvent(whiteLabel, false)).thenReturn(transactionFeeConditionalLogic);

        //Execution
        transactionFeeConditionalLogicServiceImpl.callConditionalLogicToCreateRecord(event);

        Class<ArrayList<TransactionFeeConditionalLogic>> listClass = (Class<ArrayList<TransactionFeeConditionalLogic>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TransactionFeeConditionalLogic>> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(transactionFeeConditionalLogicRepository, times(1)).saveAll(transactionFeeConditionalLogicArgumentCaptor.capture());

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        for (TransactionFeeConditionalLogic actualData : transactionFeeConditionalLogicData) {
            assertEquals(eventId, actualData.getEvent().getEventId());
            assertTrue(actualData.getId() == 0);
        }
    }*/

    @Test
    void test_callConditionalLogicToCreateRecord_throwException_ConfigMissing() {

        //mock


        NotFoundException exception = null;

        //Execution
//        try{
//            transactionFeeConditionalLogicServiceImpl.callConditionalLogicToCreateRecord(event,false);
//        } catch (NotFoundException e){
//            exception = e;
//        }
//
//        assertEquals(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND.getStatusCode() , exception.getErrorCode());
//        assertEquals(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND.getErrorMessage() , exception.getErrorMessage());
    }

    @Test
    void test_callConditionalLogicToCreateRecord_NotWhiteLabelEvent() {

        //setup
        event.setWhiteLabel(null);
        event.setEventFormat(EventFormat.VIRTUAL);

        //Execution
        //transactionFeeConditionalLogicServiceImpl.callConditionalLogicToCreateRecord(event,false);
    }

    /*@Test
    void test_ConditionalLogicToCopyRecords_Success() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogic = getDefaultConfigs();

        //mock
        when(transactionFeeConditionalLogicRepository.findByWhiteLabelAndEventIsNullAndIsVirtualEvent(whiteLabel,false)).thenReturn(transactionFeeConditionalLogic);

        //Execution
        transactionFeeConditionalLogicServiceImpl.callConditionalLogicToCreateRecord(event);

        Class<ArrayList<TransactionFeeConditionalLogic>> listClass = (Class<ArrayList<TransactionFeeConditionalLogic>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<TransactionFeeConditionalLogic>> argument = ArgumentCaptor.forClass(listClass);
        verify(transactionFeeConditionalLogicRepository, times(1)).saveAll(argument.capture());

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicData = argument.getValue();
        assertEquals(transactionFeeConditionalLogicData.listIterator(0).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicData.listIterator(0).next().getEvent().getEventId(), event.getEventId());
        assertEquals(transactionFeeConditionalLogicData.listIterator(0).next().getOperator(), getDefaultConfigs().listIterator(0).next().getOperator());
        assertTrue(transactionFeeConditionalLogicData.listIterator(0).next().getAeFeeFlat() == getDefaultConfigs().listIterator(0).next().getAeFeeFlat());
        assertTrue(transactionFeeConditionalLogicData.listIterator(0).next().getWlAFeeFlat() == getDefaultConfigs().listIterator(0).next().getWlAFeeFlat());

        assertEquals(transactionFeeConditionalLogicData.listIterator(1).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicData.listIterator(1).next().getEvent().getEventId(), event.getEventId());
        assertEquals(transactionFeeConditionalLogicData.listIterator(1).next().getOperator(), getDefaultConfigs().listIterator(1).next().getOperator());
        assertTrue(transactionFeeConditionalLogicData.listIterator(1).next().getAeFeeFlat() == getDefaultConfigs().listIterator(1).next().getAeFeeFlat());
        assertTrue(transactionFeeConditionalLogicData.listIterator(1).next().getWlAFeeFlat() == getDefaultConfigs().listIterator(1).next().getWlAFeeFlat());
    }*/

    @Test
    void test_AddConditionalFeeInTicketTypes_success(){

        //setup
        TicketingType ticketingType30 = new TicketingType();
        ticketingType30.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType30.setPrice(new Double(30));
        event.getWhiteLabel().setPlanConfig(chargebeePlanService.getFreePlanConfig());

        //case -1
        //mock
        when(transactionFeeConditionalLogicRepository.findByEventAndIsVirtualEventAndIsAddon(event, false, false)).thenReturn(getDefaultConfigs());



        //Execution
        transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType30, false, false);

        assertEquals(ticketingType30.getAeFeeFlat(), AE_FLAT_FEE_ONE, 0);
        assertEquals(ticketingType30.getAeFeePercentage(), AE_FEE_PERCENTAGE_THREE, 0);
        assertEquals(ticketingType30.getWlAFeeFlat(), wlFlat1, 0);
        assertEquals(ticketingType30.getWlAFeePercentage(), wlFlatPercentage2, 0);

        //case -2
        //setup
        TicketingType ticketingType40 = new TicketingType();
        ticketingType40.setPrice(new Double(40));

        //mock


        //Execution
        transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType40, false, false);

        assertEquals(ticketingType40.getAeFeeFlat(), AE_FLAT_FEE_ONE, 0);
        assertEquals(ticketingType40.getWlAFeeFlat(), wlFlat2, 0);
        assertEquals(ticketingType40.getWlAFeePercentage(), wlFlatPercentage2, 0);

        //case -3
        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = getDefaultConfigs();
        transactionFeeConditionalLogics.get(0).setCustomPricing(true);
        transactionFeeConditionalLogics.get(1).setCustomPricing(true);

        TicketingType ticketingType50 = new TicketingType();
        ticketingType50.setPrice(new Double(50));

        //mock


        //Execution
        transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType50, false, false);

        assertEquals(ticketingType50.getAeFeeFlat(), AE_FLAT_FEE_ONE, 0);
        assertEquals(ticketingType50.getWlAFeeFlat(), wlFlat2, 0);
        assertEquals(ticketingType50.getWlAFeePercentage(), wlFlatPercentage2, 0);
    }

    @Test
    void test_AddConditionalFeeInTicketTypes_success1(){

        //setup
        double ticketTypePrice = 10d;
        TicketingType ticketingType50 = new TicketingType();
        ticketingType50.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType50.setPrice(new Double(30));

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setAeFeeFlat(aeFlat1);
        transactionFeeConditionalLogic.setAeFeePercentage(aeFlatPercentage1);

        event.setWhiteLabel(null);

        //mock


        //Execution
        transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType50, false, false);
    }

    @Test
    void test_AddConditionalFeeInTicketTypes_success2(){

        //setup
        TicketingType ticketingType50 = new TicketingType();
        ticketingType50.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType50.setPrice(new Double(30));

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setAeFeeFlat(aeFlat1);
        transactionFeeConditionalLogic.setAeFeePercentage(aeFlatPercentage1);

        event.setWhiteLabel(null);

        //mock
        Mockito.doReturn(transactionFeeConditionalLogic).when(transactionFeeConditionalLogicServiceImpl).getTransactionFeeConditionLogic(anyDouble(), any(), anyBoolean(),anyBoolean());

        //Execution
        transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType50, false, false);
    }

    @Test
    void test_AddConditionalFeeInTicketTypes_throwException_TRANSACTION_CONFIG_NOT_FOUND(){

        //setup
        double ticketTypePrice = 1L;
        TicketingType ticketingType30 = new TicketingType();
        ticketingType30.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType30.setPrice(new Double(30));

        //mock



        //Execution
        try {
            transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType30, false, false);
        } catch (NotFoundException e){

        }
    }

    @Test
    void test_AddConditionalFeeInTicketTypes_throwException1_TRANSACTION_CONFIG_NOT_FOUND(){

        //setup
        double ticketTypePrice = 1L;
        TicketingType ticketingType30 = new TicketingType();
        ticketingType30.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType30.setPrice(new Double(30));

        //mock



        //Execution
        try {
            transactionFeeConditionalLogicServiceImpl.applyFeeInTicketType(event,ticketingType30, false, false);
        } catch (NotFoundException e){

        }
    }


    public List<TransactionFeeConditionalLogic> getDefaultConfigs(){
        TransactionFeeConditionalLogic entity1 = new TransactionFeeConditionalLogic();
        entity1.setWhiteLabel(whiteLabel);
        entity1.setEvent(null);
        entity1.setAeFeeFlat(AE_FLAT_FEE_ONE);
        entity1.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
        entity1.setWlAFeeFlat(wlFlat1);
        entity1.setWlAFeePercentage(wlFlatPercentage1);
        entity1.setFromTicketPriceThreshold(thresholdPrice);
        entity1.setOperator(LESS_THAN);
        entity1.setToTicketPriceThreshold(-1);


        TransactionFeeConditionalLogic entity2 = new TransactionFeeConditionalLogic();
        entity2.setWhiteLabel(whiteLabel);
        entity2.setEvent(null);
        entity2.setAeFeeFlat(AE_FLAT_FEE_ONE);
        entity2.setAeFeePercentage(aeFlatPercentage2);
        entity2.setWlAFeeFlat(wlFlat2);
        entity2.setWlAFeePercentage(wlFlatPercentage2);
        entity2.setFromTicketPriceThreshold(thresholdPrice);
        entity2.setOperator(GREATER_THAN_EQUAL_TO);
        entity2.setToTicketPriceThreshold(-1);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();
        transactionFeeConditionalLogics.add(entity1);
        transactionFeeConditionalLogics.add(entity2);

        return transactionFeeConditionalLogics;
    }


    @Test
    void test_saveStripeUserIdForWLA_success() {

        //setup
        String wlAStripeUserId = "1";
        double wlAFeeFlat = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setWlAStripeUserId(wlAStripeUserId);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);
        transactionFeeConditionalLogic.setWhiteLabelId(1L);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByWhiteLabel(whiteLabel)).thenReturn(transactionFeeConditionalLogicList);
        when(eventRepoService.findAllByActiveAndUpcomingEventsByWhiteLabel(whiteLabel.getId())).thenReturn(new ArrayList<>());

        //Execution
        transactionFeeConditionalLogicServiceImpl.saveStripeUserIdForWLA(wlAStripeUserId, whiteLabel);

        Class<ArrayList<TransactionFeeConditionalLogic>> listClass = (Class<ArrayList<TransactionFeeConditionalLogic>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TransactionFeeConditionalLogic>> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(transactionFeeConditionalLogicRepository).saveAll(transactionFeeConditionalLogicArgumentCaptor.capture());

        ArrayList<TransactionFeeConditionalLogic> actualData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(wlAStripeUserId, actualData.get(0).getWlAStripeUserId());
    }

    @Test
    void test_getStripeUserId_success_with_WlTypeEnum_A() {

        //setup
        String wlAStripeUserId = "1";
        double wlAFeeFlat = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setWlAStripeUserId(wlAStripeUserId);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        Mockito.doReturn(transactionFeeConditionalLogicList).when(transactionFeeConditionalLogicServiceImpl).getRecordByEvent(event);

        //Execution
        Optional<String> stripeUserId = transactionFeeConditionalLogicServiceImpl.getStripeUserId(event, WLTypeEnum.A);
        assertEquals(wlAStripeUserId, stripeUserId.get());
    }

    @Test
    void test_getStripeUserId_success_with_WlTypeEnum_B() {

        //setup
        String wlBStripeUserId = "1";
        double wlBFeeFlat = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setWlBStripeUserId(wlBStripeUserId);
        transactionFeeConditionalLogic.setWlBFeeFlat(wlBFeeFlat);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        Mockito.doReturn(transactionFeeConditionalLogicList).when(transactionFeeConditionalLogicServiceImpl).getRecordByEvent(event);

        //Execution
        Optional<String> stripeUserId = transactionFeeConditionalLogicServiceImpl.getStripeUserId(event, WLTypeEnum.B);
        assertEquals(wlBStripeUserId, stripeUserId.get());
    }

    @Test
    void test_getTransactionFeeConditionLogic_throwException_TRANSACTION_CONFIG_NOT_FOUND() {

        //setup
        double wlAFeeFlat = 1d;
        double price = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> transactionFeeConditionalLogicServiceImpl.getTransactionFeeConditionLogic(price, event, true,false));

        assertEquals(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_isWlAFeeExists_success() {

        //setup
        double wlAFeeFlat = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByEvent(event)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        boolean wlAFeeExists = transactionFeeConditionalLogicServiceImpl.isWlAFeeExists(event);
        assertTrue(wlAFeeExists);
    }

    @Test
    void test_isWlAFeeExists_success1() {

        //setup
        double wlAFeeFlat = 1d;
        double wlAFeePercentage = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);
        transactionFeeConditionalLogic.setWlAFeePercentage(wlAFeePercentage);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByEvent(event)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        boolean wlAFeeExists = transactionFeeConditionalLogicServiceImpl.isWlAFeeExists(event);
        assertTrue(wlAFeeExists);
    }

    @Test
    void test_isWlAFeeExists_success2() {

        //setup
        double wlAFeeFlat = 0d;
        double wlAFeePercentage = 0d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);
        transactionFeeConditionalLogic.setWlAFeeFlat(wlAFeeFlat);
        transactionFeeConditionalLogic.setWlAFeePercentage(wlAFeePercentage);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByEvent(event)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        boolean wlAFeeExists = transactionFeeConditionalLogicServiceImpl.isWlAFeeExists(event);
        assertFalse(wlAFeeExists);
    }

    @Test
    void test_isWlAFeeExists_success3() {

        //setup
        double wlAFeePercentage = 1d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);
        transactionFeeConditionalLogic.setWlAFeePercentage(wlAFeePercentage);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        //mock
        when(transactionFeeConditionalLogicRepository.findByEvent(event)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        boolean wlAFeeExists = transactionFeeConditionalLogicServiceImpl.isWlAFeeExists(event);
        assertTrue(wlAFeeExists);
    }


    @Test
    void test_save_success() {

        //setup
        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setOperator(operator);

        //Execution
        transactionFeeConditionalLogicServiceImpl.save(transactionFeeConditionalLogic);

        ArgumentCaptor<TransactionFeeConditionalLogic> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(TransactionFeeConditionalLogic.class);
        verify(transactionFeeConditionalLogicRepository, times(1)).save(transactionFeeConditionalLogicArgumentCaptor.capture());

        TransactionFeeConditionalLogic actualData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(transactionFeeConditionalLogic.getOperator(), actualData.getOperator());
    }

    @Test
    void test_filterTransactionalLogicRecord_success() {

        //setup
        double price = 10d;

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        List<TransactionFeeConditionalLogic> transactionFeeConfigList = new ArrayList<>();
        transactionFeeConfigList.add(transactionFeeConditionalLogic);

        //Execution
        TransactionFeeConditionalLogic TransactionFeeConditionalLogicData = transactionFeeConditionalLogicServiceImpl.filterTransactionalLogicRecord(price, transactionFeeConfigList);
        assertNull(TransactionFeeConditionalLogicData);
    }

    @Test
    void test_setDefaultAeFee_success() {

        //Execution
        transactionFeeConditionalLogicServiceImpl.setDefaultAeFee(ticketingType, false, event, false);
    }

    @Test
    void test_setDefaultWLFee_success() {

        //Execution
        transactionFeeConditionalLogicServiceImpl.setDefaultWLFee(ticketingType);
    }

    @Test
    void test_getBaseRecordByWhiteLabel_success() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = getDefaultConfigs();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogics);

        //mock
        when(transactionFeeConditionalLogicRepository.findByWhiteLabelAndEventIsNullAndOrganizerIsNull(whiteLabel)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicsData = transactionFeeConditionalLogicServiceImpl.getBaseRecordByWhiteLabel(whiteLabel);
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getOperator(), getDefaultConfigs().listIterator(0).next().getOperator());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getAeFeeFlat(), getDefaultConfigs().listIterator(0).next().getAeFeeFlat());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getWlAFeeFlat(), getDefaultConfigs().listIterator(0).next().getWlAFeeFlat());


        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getOperator(), getDefaultConfigs().listIterator(1).next().getOperator());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getAeFeeFlat(), getDefaultConfigs().listIterator(1).next().getAeFeeFlat());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getWlAFeeFlat(), getDefaultConfigs().listIterator(1).next().getWlAFeeFlat());

    }

    @Test
    void test_getRecordByEvent_success() {

        //setup
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = getDefaultConfigs();

        //mock
        when(transactionFeeConditionalLogicRepository.findByEvent(event)).thenReturn(transactionFeeConditionalLogicList);

        //Execution
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicsData = transactionFeeConditionalLogicServiceImpl.getRecordByEvent(event);
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getOperator(), getDefaultConfigs().listIterator(0).next().getOperator());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getAeFeeFlat(), getDefaultConfigs().listIterator(0).next().getAeFeeFlat());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(0).next().getWlAFeeFlat(), getDefaultConfigs().listIterator(0).next().getWlAFeeFlat());

        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getWhiteLabel().getWhiteLabelUrl(), whiteLabel.getWhiteLabelUrl());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getOperator(), getDefaultConfigs().listIterator(1).next().getOperator());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getAeFeeFlat(), getDefaultConfigs().listIterator(1).next().getAeFeeFlat());
        assertEquals(transactionFeeConditionalLogicsData.listIterator(1).next().getWlAFeeFlat(), getDefaultConfigs().listIterator(1).next().getWlAFeeFlat());
    }

    @Test
    void test_updateCustomPricingByEvent_success() {

        //mock
        doNothing().when(transactionFeeConditionalLogicRepository).updateCustomPricingByEvent(event,true);

        //Execution
        transactionFeeConditionalLogicServiceImpl.updateCustomPricingByEvent(event,true);

        //assert
        verify(transactionFeeConditionalLogicRepository).updateCustomPricingByEvent(event,true);

    }

    @Test
    void test_isCustomPricingByEvent_success() {

        //mock
        doReturn(null).when(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);

        //Execution
        boolean isCustomPricing = transactionFeeConditionalLogicServiceImpl.isCustomPricingByEvent(event);

        //assert
        assertFalse(isCustomPricing);
        verify(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);
    }

    @Test
    void test_isCustomPricingByEvent_successWithIsCustomPricingTrue() {

        //mock
        doReturn(true).when(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);

        //Execution
        boolean isCustomPricing = transactionFeeConditionalLogicServiceImpl.isCustomPricingByEvent(event);

        //assert
        assertTrue(isCustomPricing);
        verify(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);
    }

    @Test
    void test_isCustomPricingByEvent_successWithIsCustomPricingNull() {

        //mock
        doReturn(null).when(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);

        //Execution
        boolean isCustomPricing = transactionFeeConditionalLogicServiceImpl.isCustomPricingByEvent(event);

        //assert
        assertFalse(isCustomPricing);
        verify(transactionFeeConditionalLogicRepository).isCustomPricingByEvent(event);
    }
}
