package com.accelevents.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.repositories.WinnerRepository;
import com.accelevents.services.ItemService;
import com.accelevents.services.TextMessageService;
import com.accelevents.utils.SecurityUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.accelevents.utils.GeneralUtils.getCheckoutPath;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WinnerServiceImplTest {

	@Spy
	@InjectMocks
	private WinnerServiceImpl winnerServiceImpl=new WinnerServiceImpl();

	@Mock
	private TextMessageUtils textMessageUtils;
	@Mock
	private TextMessageService textMessageService;

	@Mock
	private ItemService itemService;

	@Mock
	private WinnerRepository winnerRepository;
    @Mock
    private ImageConfiguration imageConfiguration;
    @Mock
    private EmailImageHelper emailImageHelper;

	private String apiBaseUrl = "https://www.stagingaccel.com";
	private Winner winner;
	@BeforeEach
	void setup(){
		winnerServiceImpl.setUiBaseurl(apiBaseUrl);
		winner = new Winner();
		winner.setItemId(1L);
		winner.setBidId(1L);
		winner.setHasPaid(true);
		winner.setEventId(1L);
		winner.setUserId(1L);
		winner.setModuleType(ModuleType.AUCTION);
	}
	@Test
	void testSendRaffleItemsWinnerText() {
		User winner = new User();
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		List<Item> items = new ArrayList<>();
		doReturn("testMessage").when(textMessageUtils).getRaffleItemWinnerTextMessage(items, event);

		winnerServiceImpl.sendRaffleItemsWinnerText(items, winner, event);

		verify(textMessageUtils).getRaffleItemWinnerTextMessage(items, event);

		verify(textMessageService).sendText(event.getAePhoneNumber(), winner.getAePhoneNumber(), "testMessage");
	}

	@Test
	void testSendAuctionSingleItemWinnerTextForNoWinningItem() {
		User winner = new User();
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		Item item = new Item();

		when(itemService.isBuyItNowItemAndHasBeenPaid(any(Item.class))).thenReturn(true);

		winnerServiceImpl.sendAuctionSingleItemWinnerText(item, winner, event);
		verify(textMessageUtils, never()).getSingleAuctionItemWinnerTextMessage(anyString(), anyBoolean(), anyString(),
				any());
		verify(textMessageService, never()).sendText(any(AccelEventsPhoneNumber.class),
				any(AccelEventsPhoneNumber.class), anyString());
	}

	@Test
	void testSendAuctionSingleItemWinnerTextForWinningItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		Item item = new Item();
		item.setCode("ITEM_CODE");
		item.setItemShortName("Short_name");

		when(itemService.isBuyItNowItemAndHasBeenPaid(item)).thenReturn(false);

		String auctionItemWinnerCheckoutLink = apiBaseUrl + getCheckoutPath() + event.getEventURL() + "/A/" + SecurityUtils.encodeUserid(winner.getUserId());
		String message = "MESSAGE_TO_BE_SEND";
		when(textMessageUtils.getSingleAuctionItemWinnerTextMessage(item.getItemShortName(), event.isCreditCardEnabled(),
				auctionItemWinnerCheckoutLink, event)).thenReturn(message);

		winnerServiceImpl.sendAuctionSingleItemWinnerText(item, winner, event);

		verify(textMessageUtils).getSingleAuctionItemWinnerTextMessage(item.getItemShortName(), event.isCreditCardEnabled(),
				auctionItemWinnerCheckoutLink, event);
		verify(textMessageService).sendText(event.getAePhoneNumber(), winner.getAePhoneNumber(), message);
	}

	@Test
	void testSendAuctionItemsWinnerTextForSingleItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		Item item = new Item();
		item.setCode("ITEM_CODE");
		List<Item> items = new ArrayList<Item>();
		items.add(item);

		winnerServiceImpl.sendAuctionItemsWinnerText(items, winner, event);

		verify(winnerServiceImpl).sendAuctionSingleItemWinnerText(item, winner, event);
	}

	@Test
	void testSendAuctionItemsWinnerTextForZeroItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		List<Item> items = new ArrayList<Item>(0);
		winnerServiceImpl.sendAuctionItemsWinnerText(items, winner, event);

		verify(winnerServiceImpl, never()).sendAuctionSingleItemWinnerText(any(Item.class), Mockito.eq(winner),
				Mockito.eq(event));
		verify(textMessageUtils, never()).getMultiAuctionItemWinnerTextMessage(anyBoolean(), anyString(), any());
		verify(textMessageUtils, never()).getSingleAuctionItemWinnerTextMessage(anyString(), anyBoolean(), anyString(),
				any());
		verify(textMessageService, never()).sendText(any(AccelEventsPhoneNumber.class),
				any(AccelEventsPhoneNumber.class), anyString());
	}

	@Test
	void testSendAuctionItemsWinnerTextForAllNonWinningItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		Item item1 = new Item();
		item1.setCode("ITEM_CODE_1");
		Item item2 = new Item();
		item2.setCode("ITEM_CODE_2");
		List<Item> items = new ArrayList<Item>();
		items.add(item1);
		items.add(item2);

		when(itemService.isBuyItNowItemAndHasBeenPaid(any(Item.class))).thenReturn(true);

		winnerServiceImpl.sendAuctionItemsWinnerText(items, winner, event);
		verify(textMessageUtils, never()).getMultiAuctionItemWinnerTextMessage(anyBoolean(), anyString(), any());
		verify(textMessageUtils, never()).getSingleAuctionItemWinnerTextMessage(anyString(), anyBoolean(), anyString(),
				any());
		verify(textMessageService, never()).sendText(any(AccelEventsPhoneNumber.class),
				any(AccelEventsPhoneNumber.class), anyString());

	}

	@Test
	void testSendAuctionItemsWinnerTextForOneWinningItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		Item winningItem = new Item();
		winningItem.setId(11);
		winningItem.setCode("ITEM_CODE_1");
		winningItem.setItemShortName("I1_shortName");
		Item nonWinningItem = new Item();
		nonWinningItem.setId(12);
		nonWinningItem.setCode("ITEM_CODE_2");
		List<Item> items = new ArrayList<Item>();
		items.add(winningItem);
		items.add(nonWinningItem);

		when(itemService.isBuyItNowItemAndHasBeenPaid(winningItem)).thenReturn(false);
		when(itemService.isBuyItNowItemAndHasBeenPaid(nonWinningItem)).thenReturn(true);
		String auctionItemWinnerCheckoutLink = apiBaseUrl + getCheckoutPath() + event.getEventURL() + "/A/" + SecurityUtils.encodeUserid(winner.getUserId());
		String message = "MESSAGE_TO_BE_SEND";
		when(textMessageUtils.getSingleAuctionItemWinnerTextMessage(winningItem.getItemShortName(), event.isCreditCardEnabled(),
				auctionItemWinnerCheckoutLink, event)).thenReturn(message);

		winnerServiceImpl.sendAuctionItemsWinnerText(items, winner, event);
		verify(textMessageUtils, never()).getMultiAuctionItemWinnerTextMessage(anyBoolean(), anyString(), any());
		verify(textMessageUtils, never()).getSingleAuctionItemWinnerTextMessage(nonWinningItem.getItemShortName(),
				event.isCreditCardEnabled(), auctionItemWinnerCheckoutLink, event);
		verify(textMessageUtils).getSingleAuctionItemWinnerTextMessage(winningItem.getItemShortName(),
				event.isCreditCardEnabled(), auctionItemWinnerCheckoutLink, event);
		verify(textMessageService).sendText(event.getAePhoneNumber(), winner.getAePhoneNumber(), message);

	}

	@Test
	void testSendAuctionItemsWinnerTextForMoreThanOneWinningItem() {
		User winner = new User();
		winner.setUserId(1L);
		winner.setPhoneNumber(8888888888L);
		winner.setCountryCode(CountryCode.US);
		Event event = new Event();
		event.setPhoneNumber(9999999999L);
		event.setCountryCode(CountryCode.IN);
		event.setCreditCardEnabled(true);
		event.setEventURL("EVENT_URL");

		Item winningItem1 = new Item();
		winningItem1.setId(11);
		winningItem1.setCode("ITEM_CODE_1");
		Item winningItem2 = new Item();
		winningItem2.setId(12);
		winningItem2.setCode("ITEM_CODE_2");
		Item nonWinningItem = new Item();
		nonWinningItem.setId(13);
		nonWinningItem.setCode("ITEM_CODE_3");
		List<Item> items = new ArrayList<Item>();
		items.add(winningItem1);
		items.add(winningItem2);
		items.add(nonWinningItem);

		when(itemService.isBuyItNowItemAndHasBeenPaid(winningItem1)).thenReturn(false);
		when(itemService.isBuyItNowItemAndHasBeenPaid(winningItem2)).thenReturn(false);
		when(itemService.isBuyItNowItemAndHasBeenPaid(nonWinningItem)).thenReturn(true);
		String auctionItemWinnerCheckoutLink = apiBaseUrl +  getCheckoutPath() + event.getEventURL() + "/A/" + SecurityUtils.encodeUserid(winner.getUserId());
		String message = "MESSAGE_TO_BE_SEND";
		when(textMessageUtils.getMultiAuctionItemWinnerTextMessage(event.isCreditCardEnabled(),
				auctionItemWinnerCheckoutLink, event)).thenReturn(message);

		winnerServiceImpl.sendAuctionItemsWinnerText(items, winner, event);
		verify(textMessageUtils).getMultiAuctionItemWinnerTextMessage(event.isCreditCardEnabled(),
				auctionItemWinnerCheckoutLink, event);
		verify(textMessageUtils, never()).getSingleAuctionItemWinnerTextMessage(anyString(), anyBoolean(), anyString(),
				any());
		verify(textMessageService).sendText(event.getAePhoneNumber(), winner.getAePhoneNumber(), message);

	}

	@Test
	void test_findAllWinnerByBidIds(){

		//setup
		List<Winner> winnerList = new ArrayList<>();
		winnerList.add(winner);

		//mock
		when(winnerRepository.findAllWinnerByBidIds(anyList())).thenReturn(winnerList);

		//Execution
		List<Winner> winners = winnerServiceImpl.findAllWinnerByBidIds(Collections.singletonList(1L));

		//Assertion
		verify(winnerRepository).findAllWinnerByBidIds(anyList());

		assertEquals(winners.get(0).getUserId(), winner.getUserId());
		assertEquals(winners.get(0).getBidId(), winner.getBidId());
		assertEquals(winners.get(0).isHasPaid(), winner.isHasPaid());
		assertEquals(winners.get(0).getEventId(), winner.getEventId());
		assertEquals(winners.get(0).getModuleType(), winner.getModuleType());
	}

	@Test
	void test_saveAllWinner(){
		List<Winner> winnerList = new ArrayList<>();
		winnerList.add(winner);

		//Execution
		winnerServiceImpl.saveAllWinner(winnerList);

		//Assertion
		Class<ArrayList<Winner>> listClass = (Class<ArrayList<Winner>>)(Class)ArrayList.class;
		ArgumentCaptor<ArrayList<Winner>> winnerArgumentCaptor = ArgumentCaptor.forClass(listClass);
		verify(winnerRepository, times(1)).saveAll(winnerArgumentCaptor.capture());

		List<Winner> winners = winnerArgumentCaptor.getValue();
		assertEquals(winners.get(0).getUserId(), winner.getUserId());
		assertEquals(winners.get(0).getBidId(), winner.getBidId());
		assertEquals(winners.get(0).isHasPaid(), winner.isHasPaid());
		assertEquals(winners.get(0).getEventId(), winner.getEventId());
		assertEquals(winners.get(0).getModuleType(), winner.getModuleType());
	}
}
