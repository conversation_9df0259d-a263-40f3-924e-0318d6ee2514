package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.DiscountType;
import com.accelevents.dto.SalesTaxFeeDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.TicketPriceDetails;
import com.accelevents.services.SalesTaxService;
import com.accelevents.utils.FeeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;

//@ExtendWith(MockitoExtension.class)
public class TicketingCouponLogicTest{

	@Spy
	@InjectMocks
	private TicketingHelperServiceImpl ticketingHelperService=new TicketingHelperServiceImpl();
	@Mock
	private TicketingType ticketingType;
	@Mock
	private TicketingOrderManager ticketingOrderManager;

	@Mock
	private SalesTaxService salesTaxService;
	private TicketingCoupon ticketingCoupon;
	private StripeDTO stripeDTO;
	private List<TicketPriceDetails> ticketPriceDetailsList;
	private SalesTaxFeeDto salesTaxFeeDto;
	private TicketingOrder ticketingOrder;

	@BeforeEach
	public void setUp(){
		// Prepare data
		Event event=new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
		event.setEventId(1L);

		Ticketing ticketing=new Ticketing();
		ticketing.setId(1L);
		ticketing.setActivated(true);
		ticketing.setEventid(event);
		ticketingOrder=EventDataUtil.getTicketingOrder();

		ticketingCoupon=new TicketingCoupon();
		ticketingCoupon.setDiscountType(DiscountType.FLAT);
		ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
		ticketingCoupon.setAmount(150);
		ticketingType=EventDataUtil.getTicketingType(event);

		ticketingOrderManager=new TicketingOrderManager();

		stripeDTO=new StripeDTO();
		stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
		stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

		salesTaxFeeDto=new SalesTaxFeeDto(true, 10d, "1");
		ticketPriceDetailsList=new ArrayList<>();

	}


	//@Test
	public void getDiscountPerTicketForOneTicketTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(1);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 1, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice(), 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(1, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(150, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(50.0, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscountPerTicketForFiveTicketTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(5);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 5, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice() * 5, 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(3, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(150, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(50, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscountPerTicketForOneTicketPerOrderTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(1);
		ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 1, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice(),0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(1, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(150, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(50, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscountPerTicketForFiveTicketPerOrderTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(5);
		ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 5, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice() * 5, 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(5, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(30, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(170, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscount100PercentagePerTicketForFiveTicketPerOrderTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(5);
		ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
		ticketingCoupon.setAmount(100);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 5, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice() * 5, 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(3, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(200, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(0, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscountInPercentagePerTicketForFiveTicketPerOrderTest(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(5);
		ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
		ticketingCoupon.setAmount(50);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 5, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice() * 5, 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(3, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(100, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(100, ticketPriceDetails.get().getPriceWithFee(), 0);
	}

	//@Test
	public void getDiscountInPercentagePerTicketForFiveTicketPerOrderTestPassFeeToBuyer(){
		int numberOfUsageLeft=3;
		ticketingOrderManager.setNumberofticket(5);
		ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
		ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
		ticketingCoupon.setAmount(50);
		ticketingType.setPassfeetobuyer(true);
		doReturn(salesTaxFeeDto).when(salesTaxService).getTaxFeeAndTicketTypeId(1L);
		ticketPriceDetailsList=ticketingHelperService.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, true, ticketingCoupon, ticketingType, 1, 5, ticketingOrder, stripeDTO, false, false, salesTaxFeeDto, ticketingType.getPrice() * 5, 0);
		// Run checks
		Optional<TicketPriceDetails> ticketPriceDetails=ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).findFirst();
		assertEquals(3, ticketPriceDetailsList.stream().filter(TicketPriceDetails::isDiscounted).count());
		assertEquals(100, ticketPriceDetails.get().getDiscountedAmount(), 0);
		assertEquals(105.64, ticketPriceDetails.get().getPriceWithFee(), 0);

	}

}
