//package com.accelevents.services.impl;
//
//import com.accelevents.domain.*;
//import com.accelevents.domain.enums.AttributeValueType;
//import com.accelevents.domain.enums.DataType;
//import com.accelevents.domain.enums.DiscountType;
//import com.accelevents.domain.enums.TicketStatus;
//import com.accelevents.dto.StripeDTO;
//import com.accelevents.dto.TicketAttributeValueDto;
//import com.accelevents.services.EventDesignDetailService;
//import com.accelevents.services.RecurringEventsScheduleService;
//import com.accelevents.services.TicketHolderRequiredAttributesService;
//import com.accelevents.services.TicketingHelperService;
//import com.accelevents.services.repo.helper.EventCommonRepoService;
//import com.accelevents.ticketing.dto.TicketingModuleDTO;
//import com.accelevents.utils.Constants;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.Spy;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import javax.xml.bind.JAXBContext;
//import javax.xml.bind.JAXBException;
//import javax.xml.bind.Unmarshaller;
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.assertNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anySet;
//import static org.mockito.Mockito.when;
//
//@ExtendWith(MockitoExtension.class)
//public class TicketingCSVServiceImplTestWithPowerMockito {
//
//    @Spy
//    @InjectMocks
//    private TicketingCSVServiceImpl ticketingCSVServiceImpl = new TicketingCSVServiceImpl();
//
//
//    @Mock
//    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
//
//    @Mock
//    private EventCommonRepoService eventCommonRepoService;
//
//    @Mock
//    private TicketingHelperService ticketingHelperService;
//
//    @Mock
//    private RecurringEventsScheduleService recurringEventsScheduleService;
//
//    @Mock
//    private EventDesignDetailService eventDesignDetailService;
//
//    private Event event;
//    private User user;
//	private EventTickets eventTickets;
//    private TicketingOrder ticketingOrder;
//    private TicketHolderAttributes ticketHolderAttributes;
//    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes1;
//	private StripeDTO stripeDTO;
//	private TrackingLinks trackingLinks;
//	private EventDesignDetail eventDesignDetail;
//
//	private double price = 100d;
//	private String date = "2019/04/04 05:30";
//    private Date orderDate = new Date(date);
//	private TicketingModuleDTO ticketingModuleDTO;
//
//    @BeforeEach
//    public void setUp() throws Exception {
//
//        event = EventDataUtil.getEvent();
//        eventTickets = EventDataUtil.getEventTickets();
//        ticketingOrder = EventDataUtil.getTicketingOrder();
//        user = EventDataUtil.getUser();
//		Ticketing ticketing = EventDataUtil.getTicketing(event);
//
//        ticketHolderAttributes = new TicketHolderAttributes();
//		Long id = 1L;
//		ticketHolderAttributes.setId(id);
//        ticketHolderAttributes.setValue(EventDataUtil.getSampleXML());
//        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue());
//
//        stripeDTO = new StripeDTO();
//		double ccPercentageFee = 2.9d;
//		stripeDTO.setCCPercentageFee(ccPercentageFee);
//		double ccFlat = 0.3d;
//		stripeDTO.setCCFlatFee(ccFlat);
//
//        trackingLinks = new TrackingLinks();
//        trackingLinks.setLinkUrl("TestEvent");
//
//		TicketingOrderManager ticketingOrderManager = new TicketingOrderManager();
//
//		TicketingType ticketingType = new TicketingType();
//        eventDesignDetail = EventDataUtil.getEventDesignDetail(event);
//        ticketingModuleDTO = new TicketingModuleDTO();
//
//    }
//
//    @Test
//    public void test_setBuyerDataByTicketingType_throw_JAXBException() throws JAXBException{
//
//        PowerMockito.mockStatic(JAXBContext.class);
//
//        //setup
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.REFUNDED);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setStaffUserId(user);
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
//        ticketHolderRequiredAttributes1.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.IMAGE);
//        ticketHolderRequiredAttributes1.setAttribute(true);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//
//        Map<Long, String> recurringIdAndDateMap = new HashMap<>();
//        recurringIdAndDateMap.put(1L, date);
//
//        //mock
//        Mockito.doThrow(JAXBException.class).when(ticketingCSVServiceImpl).unmashler(EventDataUtil.getSampleXML(), null);
//
//        //Execution
//		Long totalTicketsInOrder = 10L;
//		ticketingCSVServiceImpl.setBuyerDataByTicketingType(event, allTicketPurchaserData, ticketHolderRequiredAttributesList, null, ticketingOrder, eventTicketsList, totalTicketsInOrder, ticketingModuleDTO, recurringIdAndDateMap, eventDesignDetail.isEnableAutoAssignedSequence(),stripeDTO);
//    }
//
//    @Test
//    public void test_getUnmashler_throwJAXBException() throws JAXBException{
//        PowerMockito.mockStatic(JAXBContext.class);
//
//        //mock
//        when(JAXBContext.newInstance(TicketAttributeValueDto.class)).thenThrow(JAXBException.class);
//
//        //Execution
//        Unmarshaller unmarshaller = ticketingCSVServiceImpl.getUnmashler();
//        assertNull(unmarshaller);
//    }
//
//    @Test
//    public void test_unmashler_throwJAXBException() throws JAXBException {
//
//        //setup
//        String xml = EventDataUtil.getSampleXML();
//
//        PowerMockito.mockStatic(JAXBContext.class);
//
//        //mock
//        //Execution
//        ticketingCSVServiceImpl.unmashler(xml, null);
//    }
//
//    @Test
//    public void test_setAllTicketHolderCSVDataForEvent_throwException() throws JAXBException{
//
//        //setup
//        List<String> headerList = new ArrayList<>();
//
//        List<String> ticketPurchaserData = new ArrayList<>();
//
//        List<List<String>> allTicketPurchaserData = new ArrayList<>();
//        allTicketPurchaserData.add(ticketPurchaserData);
//
//		TicketingCoupon ticketingCoupon = new TicketingCoupon();
//		String couponCode = "Discount50";
//		ticketingCoupon.setName(couponCode);
//		double amount = 50d;
//		ticketingCoupon.setAmount(amount);
//        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//        ticketingOrder.setOrderDate(orderDate);
//        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
//        ticketingOrder.setTrackingLinks(trackingLinks);
//        ticketingOrder.setStaffUserId(user);
//
//        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
//        eventTickets.setTicketPrice(price);
//        eventTickets.setTicketPurchaserId(user);
//        eventTickets.setStatus(Constants.DELETE);
//        eventTickets.setTicketStatus(TicketStatus.BOOKED);
//        eventTickets.setTicketingOrder(ticketingOrder);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        //mock
//		Long recurringEventId = 1L;
//		when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributesOrderByAttributeOrder(event, recurringEventId,DataType.TICKET)).thenReturn(ticketHolderRequiredAttributesList);
//        when(ticketingHelperService.findTicketingByEventId(event)).thenReturn(ticketingModuleDTO);
//        when(eventCommonRepoService.findByEventIdJoinFetch(event, recurringEventId, false, DataType.TICKET)).thenReturn(eventTicketsList);
//        Mockito.doThrow(JAXBException.class).when(ticketingCSVServiceImpl).unmashler(any(), any());
//        when(recurringEventsScheduleService.getRecurringEventByIdsIn(anySet())).thenReturn(null);
//        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(false);
//
//        //Execution
//        ticketingCSVServiceImpl.setAllTicketHolderCSVDataForEvent(event, headerList, allTicketPurchaserData, recurringEventId, DataType.TICKET);
//    }
//
//    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes() {
//
//        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes1.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
//        ticketHolderRequiredAttributes1.setAttributeValueType(AttributeValueType.EMAIL);
//
//		TicketHolderRequiredAttributes ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes2.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes2.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes2.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
//
//		TicketHolderRequiredAttributes ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes3.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes3.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes3.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
//
//		TicketHolderRequiredAttributes ticketHolderRequiredAttributes4 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes4.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes4.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes4.setAttributeValueType(AttributeValueType.IMAGE);
//        ticketHolderRequiredAttributes4.setAttribute(true);
//
//		TicketHolderRequiredAttributes ticketHolderRequiredAttributes5 = new TicketHolderRequiredAttributes();
//        ticketHolderRequiredAttributes5.setEnabledForTicketHolder(true);
//        ticketHolderRequiredAttributes5.setName(Constants.FIRST_NAME);
//        ticketHolderRequiredAttributes5.setAttributeValueType(AttributeValueType.EMAIL);
//
//        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes4);
//        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes5);
//        return ticketHolderRequiredAttributesList;
//    }
//
//}