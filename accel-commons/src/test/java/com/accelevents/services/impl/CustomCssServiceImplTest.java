package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.services.S3Wrapper;
import com.accelevents.services.customcss.CustomCSSRepo;
import com.accelevents.services.customcss.CustomCss;
import com.accelevents.services.customcss.CustomCssDto;
import com.accelevents.utils.Constants;
import com.amazonaws.services.cloudfront.AmazonCloudFront;
import com.amazonaws.services.cloudfront.model.CreateInvalidationResult;
import com.amazonaws.services.cloudfront.model.Invalidation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CustomCssServiceImplTest {

    @Spy
    @InjectMocks
    private CustomCssServiceImpl contactServiceImpl;
    @Mock
    S3Wrapper s3client;
    @Mock
    CustomCSSRepo customCSSRepo;
    @Mock
    AmazonCloudFront amazonCloudFrontClient;

    private Event event;
	private User user;
	private CustomCss customCss;
	private CustomCssDto customCssDto;
    String uploadFile="file content";
    CreateInvalidationResult invalidationResult;

    @BeforeEach
    void setUp() {
//        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        customCss = new CustomCss();
        customCss.setId(0L);
        customCss.setEventId(event.getEventId());
        customCss.setTargetType(Constants.CustomCSSTargetType.LANDING_PAGE);
        customCss.setUserId(user.getUserId());
        customCss.setUser(user);
        customCss.setUploadDate(new Date(1699694330573L));

        customCssDto = new CustomCssDto();
        customCssDto.setContent(uploadFile);
        customCssDto.setTargetType(Constants.CustomCSSTargetType.LANDING_PAGE);

        invalidationResult = new CreateInvalidationResult();
        invalidationResult.setInvalidation(new Invalidation().withStatus("Success"));
    }

    @Test
    void test_createACustomCss() {

        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
        when(customCSSRepo.save(any())).thenReturn(customCss);

        contactServiceImpl.uploadCustomCss(customCssDto,user,event);
        assertEquals(customCss.getEventId().longValue(),event.getEventId());
        assertEquals(customCss.getUserId(),user.getUserId());
        assertEquals(Constants.CustomCSSTargetType.LANDING_PAGE, customCss.getTargetType());
    }

    @Test
    void test_createACustomCssWrongExtension(){

        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
        when(customCSSRepo.save(any())).thenReturn(customCss);

        contactServiceImpl.uploadCustomCss(customCssDto,user,event);

    }

    @Test
    void test_createACustomCssNullFile(){

        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
        when(customCSSRepo.save(any())).thenReturn(customCss);


        contactServiceImpl.uploadCustomCss(customCssDto,user,event);

    }

    //TODO: Mockito ReWrite
//    @Test
//    void test_createACustomCssNullPointer(){
//
//        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
//        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
//        when(customCSSRepo.save(isA(CustomCss.class))).thenReturn(customCss);
//
//        Matcher<String> nullMatcher = new IsNull<>();
//
//        contactServiceImpl.uploadCustomCss(customCssDto,user,event);
//    }

    @Test
    void test_createACustomCssWithExists(){


        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.of(customCss));


        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.uploadCustomCss(customCssDto, user, event));
        
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CUSTOM_CSS_ALREADY_EXIST.getDeveloperMessage().replace("{$target}",Constants.CustomCSSTargetType.LANDING_PAGE.getTargetType()), exception.getMessage());
    }

    @Test
    void test_updateACustomCss(){

        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
        when(customCSSRepo.findById(any())).thenReturn(Optional.of(customCss));
        when(customCSSRepo.save(any())).thenReturn(customCss);

        contactServiceImpl.updateCustomCss(customCssDto, user, event, 1L);

        assertEquals(customCss.getEventId().longValue(),event.getEventId());
        assertEquals(customCss.getUserId(),user.getUserId());
        assertEquals(Constants.CustomCSSTargetType.LANDING_PAGE, customCss.getTargetType());
    }

    @Test
    void test_updateACustomCssNonExists(){

        when(customCSSRepo.findByEventIdAndTargetType(any(), any())).thenReturn(Optional.empty());
        when(customCSSRepo.findById(any())).thenReturn(Optional.empty());

        
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.updateCustomCss(customCssDto,user,event, 1L));
        
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CUSTOM_CSS_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteACustomCss(){

        when(amazonCloudFrontClient.createInvalidation(any())).thenReturn(invalidationResult);
        when(customCSSRepo.findById(any())).thenReturn(Optional.of(customCss));


        contactServiceImpl.deleteCustomCss(1L);

        verify(contactServiceImpl,times(1)).deleteCustomCss(1L);
    }

    @Test
    void test_deleteACustomCssNonExists(){

        when(customCSSRepo.findById(any())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.deleteCustomCss(1L));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CUSTOM_CSS_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }


    @Test
    void test_getAllCustomCssInAEvent(){
        when(customCSSRepo.findByEventId(any())).thenReturn(Collections.singletonList(customCss));
        contactServiceImpl.getCustomCss(event);
        verify(contactServiceImpl,times(1)).getCustomCss(event);
    }

    @Test
    void test_getCustomCssInAEvent(){
        when(customCSSRepo.findById(any())).thenReturn(Optional.of(customCss));
        contactServiceImpl.getCustomCss(customCss.getId());
        verify(contactServiceImpl,times(1)).getCustomCss(customCss.getId());
    }

    @Test
    void test_getCustomCssInAEventWithNonExistsRecord(){
        when(customCSSRepo.findById(any())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.getCustomCss(customCss.getId()));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.CUSTOM_CSS_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }
}