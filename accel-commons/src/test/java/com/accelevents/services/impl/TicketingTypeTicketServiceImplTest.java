package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.RecurringEvents;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.CategoryDto;
import com.accelevents.ticketing.dto.EventCategoryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static com.accelevents.utils.Constants.GENERAL_ADMISSION;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingTypeTicketServiceImplTest {

    @InjectMocks
    @Spy
    TicketingTypeTicketServiceImpl ticketingTypeTicketServiceImpl;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingManageService ticketingManageService;
    @Mock
    private SeatingCategoryService seatingCategoryService;

    private Event event;
    private TicketingType ticketingType;
    private Ticketing ticketing;
    private RecurringEvents recurringEvent;

	private Long id = 1L;
	private Long recurringEventId = 1L;
    private double sequence = 1000;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketingType = new TicketingType();
        ticketingType.setId(id);
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketingType.setTicketing(ticketing);
        ticketingType.setEventId(event.getEventId());
        recurringEvent = new RecurringEvents();
		EventCategoryDto eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setId(id);

    }

    @Test
    void test_setPositionForTicketingType_success() {

        //Execution
        TicketingType ticketType = ticketingTypeTicketServiceImpl.setPositionForTicketingType(ticketingType);

        //Assertion
        assertEquals(ticketType,ticketingType);
    }

    @Test
    void test_setPositionForTicketingType_successTicketingTypeIdZeroAndRecurringEventsIdNull() {
        //Setup
        ticketingType.setId(0L);
        TicketingType lastItem = new TicketingType();

        //Mock
        when(ticketingTypeRepository.findFirstByEventIdAndRecurringEventIdIsNullOrderByPositionDesc(ticketingType.getEventId())).thenReturn(lastItem);

        //Execution
        TicketingType ticketType = ticketingTypeTicketServiceImpl.setPositionForTicketingType(ticketingType);

        //Assertion
        assertEquals(ticketType.getPosition(),lastItem.getPosition()+sequence,0);

        verify(ticketingTypeRepository).findFirstByEventIdAndRecurringEventIdIsNullOrderByPositionDesc(ticketingType.getEventId());
    }

    @Test
    void test_setPositionForTicketingType_successTicketingTypeIdZeroAndRecurringEventsId() {
        //Setup
        ticketingType.setId(0L);
        ticketingType.setRecurringEventId(id);

        //Mock
        when(ticketingTypeRepository.findFirstByEventAndRecurringEventIdOrderByPositionDesc(event.getEventId(),ticketingType.getRecurringEventId())).thenReturn(null);

        //Execution
        TicketingType ticketType = ticketingTypeTicketServiceImpl.setPositionForTicketingType(ticketingType);

        //Assertion
        assertEquals(ticketType.getPosition(),sequence,0);

        verify(ticketingTypeRepository).findFirstByEventAndRecurringEventIdOrderByPositionDesc(event.getEventId(),ticketingType.getRecurringEventId());
    }

    public static Object[] getTicketingId(){
        return new Object[]{
                new Object[]{1L,true} ,
                new Object[]{0L,false} ,
        };
    }

    @Test
    void test_isAnyTicketExistsWithDonationType() {

        //Mock
        when(ticketingTypeRepository.findTicketTypeAsCategoryByEvent(event, TicketType.DONATION)).thenReturn(Collections.emptyList());

        //Execution
        boolean response = ticketingTypeTicketServiceImpl.isAnyTicketExistsWithDonationType(event);

        //Assertion
        assertFalse(response);
        verify(ticketingTypeRepository).findTicketTypeAsCategoryByEvent(event, TicketType.DONATION);
    }

    @Test
    void test_isAnyTicketExistsWithDonationType_responseSizeGreaterThanZero() {

        //Setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //Mock
        when(ticketingTypeRepository.findTicketTypeAsCategoryByEvent(event, TicketType.DONATION)).thenReturn(ticketingTypes);

        //Execution
        boolean response = ticketingTypeTicketServiceImpl.isAnyTicketExistsWithDonationType(event);

        //Assertion
        assertTrue(response);
        verify(ticketingTypeRepository).findTicketTypeAsCategoryByEvent(event, TicketType.DONATION);
    }

    public static Object[] getFetchHiddenTicketsOnly(){
        return new Object[]{
                new Object[]{true} ,
                new Object[]{false} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getFetchHiddenTicketsOnly")
    void test_getByListOfRecurringEvents_success(boolean fetchHiddenTicketsOnly) {

        //Setup
        recurringEvent.setId(id);
        List<RecurringEvents> recurringEvents = new ArrayList<>();
        recurringEvents.add(recurringEvent);

        //Mock
        when(ticketingTypeRepository.findByListOfRecurringEvents(anyList(), nullable(Boolean.class), isNull())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.getByListOfRecurringEvents(recurringEvents,fetchHiddenTicketsOnly);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findByListOfRecurringEvents(anyList(), nullable(Boolean.class),isNull());
    }

    @Test
    void test_getAllByTicketingAndCreatedFromNullOnlyPaidOrderByPosition() {
        //Mock
        when(ticketingTypeRepository.findAllByEventIdRecurringIdNullOnlyPaid(event, TicketType.PAID)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.getAllByTicketingAndCreatedFromNullOnlyPaidOrderByPosition(event);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());

    }

    @Test
    void test_getAllTicketingTypes() {
        //Mock
		Long eventId = 1L;
		when(ticketingTypeRepository.findAllTicketingTypes(eventId, TicketType.PAID)).thenReturn(Collections.emptyList());

        //Execution
        List<CategoryDto> categoryDtos = ticketingTypeTicketServiceImpl.getAllTicketingTypes(eventId);

        //Assertion
        assertTrue(categoryDtos.isEmpty());
    }

    @Test
    void test_getNumberOfTotalTickets_eventIdNull() {

        //Execution
        List<Object[]> objects = ticketingTypeTicketServiceImpl.getNumberOfTotalTickets(null);

        //Assertion
        assertTrue(objects.isEmpty());
    }

    @Test
    void test_getNumberOfTotalTickets_eventIdEmpty() {
        //Setup
        Set<Long> set = new HashSet<>();

        //Execution
        List<Object[]> objects = ticketingTypeTicketServiceImpl.getNumberOfTotalTickets(set);

        //Assertion
        assertTrue(objects.isEmpty());
    }

    @Test
    void test_getNumberOfTotalTickets_successEventId() {
        //Setup
        Set<Long> set = new HashSet<>();
        set.add(1L);

        //Mock
        when(ticketingTypeRepository.findNumberOfTotalTickets(set)).thenReturn(Collections.emptyList());

        //Execution
        List<Object[]> objects = ticketingTypeTicketServiceImpl.getNumberOfTotalTickets(set);

        //Assertion
        assertTrue(objects.isEmpty());

    }

    @Test
    void test_getAllTicketingTypesByRecuurringEvent() {
        //Mock
        when(ticketingTypeRepository.findAllTicketingTypesByRecuurringEvent(recurringEventId, TicketType.PAID)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.getAllTicketingTypesByRecuurringEvent(recurringEventId);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());

        verify(ticketingTypeRepository).findAllTicketingTypesByRecuurringEvent(recurringEventId, TicketType.PAID);
    }


    //TODO: Junit5 review test and code for null vs false
    /*@ParameterizedTest
    @MethodSource("getFetchHiddenTicketsOnly")
    void test_findAllByRecurringEventsDisplay(Boolean excludeHidden) {
        //Mock
        when(recurringEventsScheduleService.getRecurringEvents(recurringEventId)).thenReturn(recurringEvent);
        when(ticketingTypeRepository.findByListOfRecurringEventsOrAddOns(Collections.singletonList(recurringEvent.getId()),excludeHidden, DataType.TICKET)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findAllByRecurringEventsDisplay(recurringEventId,excludeHidden, DataType.TICKET);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());

        verify(recurringEventsScheduleService).getRecurringEvents(recurringEventId);
        verify(ticketingTypeRepository).findByListOfRecurringEventsOrAddOns(anyList(),nullable(Boolean.class),isA(DataType.class));
    }*/

    //TODO: Junit5 review test and code for null vs false
    /*@ParameterizedTest
    @MethodSource("getFetchHiddenTicketsOnly")
    void test_findAllByRecurringEvents(Boolean fetchHiddenTicketsOnly) {
        //Mock
        when(recurringEventsScheduleService.getRecurringEvents(recurringEventId)).thenReturn(recurringEvent);
        when(ticketingTypeRepository.findByListOfRecurringEvents(Collections.singletonList(recurringEvent.getId()),fetchHiddenTicketsOnly, DataType.TICKET)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findAllByRecurringEvents(recurringEventId,fetchHiddenTicketsOnly, DataType.TICKET);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());

        verify(recurringEventsScheduleService).getRecurringEvents(recurringEventId);
        verify(ticketingTypeRepository).findByListOfRecurringEvents(anyList(),nullable(Boolean.class),isA(DataType.class));
    }*/

    @Test
    void test_getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates() {

        //Mock
        when(ticketingTypeRepository.findTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(anyLong(),any())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.getTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(1L,event);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findTicketTypeByCreateFromAndBelongToOnlyFutureRecurringDates(anyLong(),any());
    }

    @Test
    void test_findAllByTicketingAndRecuringEvent() {

        //Mock
        when(ticketingTypeRepository.findAllByEventIdRecurring(event, recurringEventId)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findAllByTicketingAndRecuringEvent(recurringEventId,event);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());

        verify(ticketingTypeRepository).findAllByEventIdRecurring(event, recurringEventId);
    }

    @Test
    void test_findAllByEventIdAndRecurringEventIdPresent() {

        //Mock
        when(ticketingTypeRepository.findAllByEventIdAndRecurringIdIsNotNullAndHidden(event, true, List.of(DataType.TICKET, DataType.ADDON))).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findAllByEventIdAndRecurringEventIdPresent(event,true);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findAllByEventIdAndRecurringIdIsNotNullAndHidden(event, true, Arrays.asList(DataType.TICKET, DataType.ADDON));
    }

    @Test
    void test_findAllByEvent() {

        //Mock
        when(ticketingTypeRepository.findAllByEventId(isA(Event.class), isA(Date.class),anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findAllByEvent(event);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findAllByEventId(isA(Event.class),isA(Date.class),anyList());
    }

    @Test
    void test_findTicketingByEventIdList() {
        //Setup
        List<Long> eventIds = new ArrayList<>();
        eventIds.add(1L);

        //Mock
        when(ticketingTypeRepository.findTicketingByEventIdList(eventIds)).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findTicketingByEventIdList(eventIds);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findTicketingByEventIdList(eventIds);
    }

    @Test
    void test_findTicketingByEventIdList_eventIdsEmpty() {

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findTicketingByEventIdList(Collections.emptyList());

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
    }

    @Test
    void test_findAllIdByEventIdAndRecurringEvent() {

        //Mock
        when(ticketingTypeRepository.findAllIdByEventIdAndRecurringEvent(event,recurringEventId)).thenReturn(Collections.emptyList());

        //Execution
        List<Long> ticketingTypeIds = ticketingTypeTicketServiceImpl.findAllIdByEventIdAndRecurringEvent(event,recurringEventId);

        //Assertion
        assertTrue(ticketingTypeIds.isEmpty());
        verify(ticketingTypeRepository).findAllIdByEventIdAndRecurringEvent(event,recurringEventId);
    }

    @Test
    void test_getTicketTypeIdsByCreatedFromsAndInRecurringEventId_createdFromsEmpty() {

        //Execution
        List<Long> ticketingTypeIds = ticketingTypeTicketServiceImpl.getTicketTypeIdsByCreatedFromsAndInRecurringEventId(Collections.emptyList(),recurringEventId);

        //Assertion
        assertTrue(ticketingTypeIds.isEmpty());
    }

    @Test
    void test_getTicketTypeIdsByCreatedFromsAndInRecurringEventId_success() {

        //Setup
        List<Long> createdFroms = new ArrayList<>();
        createdFroms.add(1L);

        //Mock
        when(ticketingTypeRepository.getTicketTypeIdsByCreatedFromsAndInRecurringEventId(createdFroms, recurringEventId)).thenReturn(Collections.emptyList());

        //Execution
        List<Long> ticketingTypeIds = ticketingTypeTicketServiceImpl.getTicketTypeIdsByCreatedFromsAndInRecurringEventId(createdFroms,recurringEventId);

        //Assertion
        assertTrue(ticketingTypeIds.isEmpty());
        verify(ticketingTypeRepository).getTicketTypeIdsByCreatedFromsAndInRecurringEventId(createdFroms, recurringEventId);
    }

    @Test
    void test_markAsHiddenByTicketTypeIds() {
        //Mock
        doNothing().when(ticketingTypeRepository).markAsHiddenByTicketTypeIds(anyList(),anyBoolean());

        //Execution
        ticketingTypeTicketServiceImpl.markAsHiddenByTicketTypeIds(Collections.emptyList());

        //Assertion
        verify(ticketingTypeRepository).markAsHiddenByTicketTypeIds(anyList(),anyBoolean());
    }

    @Test
    void test_getTicketingTypesByRecurringList() {
        //Mock
        when(ticketingTypeRepository.findTicketingTypesByRecurringList(anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.getTicketingTypesByRecurringList(Collections.emptyList());

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findTicketingTypesByRecurringList(anyList());
    }

    @Test
    void test_findRecurringEventTypeIdByEventAndCreatedFrom() {
        //Mock
        when(ticketingTypeRepository.findRecurringEventTypeIdByEventAndCreatedFrom(event,1L)).thenReturn(Collections.emptyList());

        //Execution
        List<Long> ticketingTypeIds = ticketingTypeTicketServiceImpl.findRecurringEventTypeIdByEventAndCreatedFrom(event,1L);

        //Assertion
        assertTrue(ticketingTypeIds.isEmpty());
        verify(ticketingTypeRepository).findRecurringEventTypeIdByEventAndCreatedFrom(event,1L);
    }

    @Test
    void test_getTicketTypeIdsByCreatedFroms() {
        //Mock
        when(ticketingTypeRepository.findTicketTypeIdsByCreatedFroms(anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<Long> ticketingTypeIds = ticketingTypeTicketServiceImpl.getTicketTypeIdsByCreatedFroms(Collections.emptyList());

        //Assertion
        assertTrue(ticketingTypeIds.isEmpty());
        verify(ticketingTypeRepository).findTicketTypeIdsByCreatedFroms(anyList());
    }

    @Test
    void test_findByCategoryId() {
        //Mock
        when(ticketingTypeRepository.findByCategoryId(1L, Arrays.asList(DataType.TICKET, DataType.ADDON))).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketServiceImpl.findByCategoryId(1L);

        //Assertion
        assertTrue(ticketingTypes.isEmpty());
        verify(ticketingTypeRepository).findByCategoryId(1L, Arrays.asList(DataType.TICKET, DataType.ADDON));

    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_ticketingTypesNotEmpty() {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //Mock
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypes);

        //Execution
        ticketingTypeTicketServiceImpl.createGeneralAdmissionTicketIfNotExists(ticketing,event);

        //Assertion
        verify(ticketingTypeRepository).findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId());
    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_ticketingTypesEmpty() {

        //setup
        event.setEventFormat(EventFormat.VIRTUAL);

        //Mock
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(Collections.emptyList());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(isA(Event.class),isA(TicketingType.class),anyBoolean() , anyBoolean());
        when(ticketingTypeService.save(isA(TicketingType.class))).thenReturn(ticketingType);
        doNothing().when(ticketingManageService).updateHolderRequiredAttributesForNewTicket(event,ticketingType);

        //Execution
        ticketingTypeTicketServiceImpl.createGeneralAdmissionTicketIfNotExists(ticketing,event);

        //Assertion
        ArgumentCaptor<TicketingType> argumentCaptorSchedule = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, Mockito.times(1)).save(argumentCaptorSchedule.capture());
        TicketingType ticketingTypeValue = argumentCaptorSchedule.getValue();

        assertEquals(ticketingTypeValue.getTicketTypeName(),GENERAL_ADMISSION);
        assertEquals(ticketingTypeValue.getTicketType(),TicketType.PAID);
        assertEquals(ticketingTypeValue.getPrice(),50,0);
        assertEquals(ticketingTypeValue.getDataType(),DataType.TICKET);
        assertEquals(ticketingTypeValue.getEndDate(),ticketing.getEventEndDate());
        assertEquals(ticketingTypeValue.getStartDate().toString(),new Date().toString());
        assertEquals(ticketingTypeValue.getRecurringEventSalesEndStatus(),TicketingType.RecurringEventSalesEndStatus.START);
        assertEquals(ticketingTypeValue.getRecurringEventSalesEndTime(),60,0);
        assertEquals(ticketingTypeValue.getNumberOfTickets(),100);
        assertEquals(ticketingTypeValue.getMaxTicketsPerBuyer(),10);
        assertEquals(ticketingTypeValue.getBundleType(), TicketBundleType.INDIVIDUAL_TICKET);
        assertEquals(ticketingTypeValue.getTicketing(),ticketing);

        verify(ticketingTypeRepository).findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId());
        verify(transactionFeeConditionalLogicService).applyFeeInTicketType(any(),any(), anyBoolean(), anyBoolean());
        verify(ticketingManageService).updateHolderRequiredAttributesForNewTicket(any(),any());
    }

    @Test
    void test_updateTicketingTypeRecStatusByRecurringEventIds(){

        doNothing().when(ticketingTypeRepository).updateTicketingTypeRecStatusByRecurringEventIds(anyList(),any());
        ticketingTypeTicketServiceImpl.updateTicketingTypeRecStatusByRecurringEventIds(Collections.singletonList(id), RecordStatus.CREATE);

        assertTrue(true);

    }

    @Test
    void test_createDefaultCategory(){
        try {
            Method method = TicketingTypeTicketServiceImpl.class.getDeclaredMethod("createDefaultCategory", Event.class, String.class,Double.class,Integer.class);
            method.setAccessible(true);
            method.invoke(ticketingTypeTicketServiceImpl, event, "test",100D,10);
        }
        catch (Exception ex){
            //ignored
        }
        assertTrue(true);
    }

    @Test
    void test_createFreeAdmissionTicketIfNotExists(){

        ticketing = new Ticketing();

        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(anyString(), anyLong())).thenReturn(Collections.emptyList());
        doNothing().when(ticketingManageService).updateHolderRequiredAttributesForNewTicket(any(),any());

        TicketingType freeAdmissionTicketIfNotExists = ticketingTypeTicketServiceImpl.createFreeAdmissionTicketIfNotExists(ticketing, event);

        assertNull(freeAdmissionTicketIfNotExists);

    }
}