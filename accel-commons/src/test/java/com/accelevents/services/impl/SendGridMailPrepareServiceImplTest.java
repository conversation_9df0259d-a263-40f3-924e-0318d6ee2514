package com.accelevents.services.impl;

import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.dto.EmailMessage;
import com.accelevents.dto.TicketHolderEmailDto;
import com.accelevents.helpers.EmailImageHelper;
import com.accelevents.helpers.ServiceHelper;
import com.accelevents.helpers.TemplateId;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.impl.SendGridMailPrepareServiceImpl;
import com.accelevents.repositories.BeeFreeRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SendGridMailPrepareServiceImplTest extends SendGridMailPrepareServiceImpl {

    @Spy
    @InjectMocks
    private SendGridMailPrepareServiceImpl sendGridMailPrepareServiceImpl = new SendGridMailPrepareServiceImpl();
    @Mock
     private  ServiceHelper serviceHelper;

    @Mock
    private BeeFreeRepository beeFreeRepository;

    @Mock
    private AutoLoginService autoLoginService;

    @Mock
    private SendGridMailService sendGridMailService;

    @Mock
    EventDesignDetailService eventDesignDetailService;

    @Mock
    EmailImageHelper emailImageHelper;

    @Mock
    EmailMessage emailMessage ;

    @Mock
    private URLShortnerService urlShortnerService;

    @Mock
    private TextMessageUtils textMessageUtils;

    @Mock
    private TextMessageService textMessageService;

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private TicketingService ticketingService;
    @Mock
    private ImageConfiguration imageConfiguration;

    private EventTickets eventTickets;
	private String SequenceName = "Men's Team";
    private Ticketing ticketing;
    private Event event;
    private User user;
    private  EventDesignDetail eventDesignDetail;
    private TicketingOrder ticketingOrder;


    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

		TicketingType ticketingType = new TicketingType();
		Long id = 1L;
		ticketingType.setId(id);
        eventTickets = new EventTickets();
        eventTickets.setId(id);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setNumberoftickets(1L);
        eventTickets.setTicketingTypeId(ticketingType);
        ticketing = EventDataUtil.getTicketing(event);
        emailMessage.setSubject("test");
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        eventDesignDetail = EventDataUtil.getEventDesignDetail(event);
        ticketingOrder = EventDataUtil.getTicketingOrder();
    }

    @Test
    void test_updateBuyerNameWithSequenceNameAndNumber_isHolderTrue() {

        //Setup
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();
        //Execute
        sendGridMailPrepareServiceImpl.updateBuyerNameWithSequenceNameAndNumber(true, ticketing, ticketHolderEmailMap, item, dataForTicketFtl);
        verify(sendGridMailPrepareServiceImpl).updateHolderNameWithAutoSequenceNameAndNumber(any(), any(), any());
    }

    @Test
    void test_updateBuyerNameWithSequenceNameAndNumber_isHolderFALSEAndAssignedNumberNotEmptyAndTicketTypeFREE() {

        //Setup
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoSequeneceName(SequenceName);
        item.setAutoAssignedNumber("12");
        item.setPurchaserName("Jonathan Kazarian");
        item.setHolderName("Jonathan Kazarian");
        item.setTicketType(TicketType.FREE);
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        //Mock
        doReturn("12,13,14").when(sendGridMailPrepareServiceImpl).getSequenceNumberWithComaSep(ticketHolderEmailMap, item);

        //Execute
        sendGridMailPrepareServiceImpl.updateBuyerNameWithSequenceNameAndNumber(false, ticketing, ticketHolderEmailMap, item, dataForTicketFtl);
        verify(sendGridMailPrepareServiceImpl).getSequenceNumberWithComaSep(ticketHolderEmailMap, item);

        assertEquals("Jonathan Kazarian - Men's Team: 12,13,14",dataForTicketFtl.getPurchaserName());
    }

    @Test
    void test_updateBuyerNameWithSequenceNameAndNumber_isHolderFalseAndAssignedNumberNotEmptyAndTicketTypePAID(){

        //SetUp
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoSequeneceName(SequenceName);
        item.setAutoAssignedNumber("12");
        item.setPurchaserName("Jonathan Kazarian");
        item.setTicketType(TicketType.PAID);
        item.setTicketingTypePrice(10);

        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        //Mock
        doReturn("12,13,14").when(sendGridMailPrepareServiceImpl).getSequenceNumberWithComaSep(ticketHolderEmailMap, item);

        //Execute
        sendGridMailPrepareServiceImpl.updateBuyerNameWithSequenceNameAndNumber(false, ticketing, ticketHolderEmailMap, item, dataForTicketFtl);
        verify(sendGridMailPrepareServiceImpl).getSequenceNumberWithComaSep(ticketHolderEmailMap, item);

        assertEquals(" - Men's Team: 12,13,14", dataForTicketFtl.getAutoAssignedNumber());
    }

    @Test
    void test_updateBuyerNameWithSequenceNameAndNumber_isHolderFalseAndAssignedNumberNotPresent(){

        //SetUp
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setHolderName("Jon Kazarian");
        item.setAutoAssignedNumber(Constants.STRING_EMPTY);
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        //Execute
        sendGridMailPrepareServiceImpl.updateBuyerNameWithSequenceNameAndNumber(false, ticketing, ticketHolderEmailMap, item, dataForTicketFtl);
    }


    @Test
    void test_updateHolderNameWithAutoSequenceNameAndNumber_holderName(){

        //SetUp
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoSequeneceName(SequenceName);
        item.setAutoAssignedNumber("12");
        item.setHolderName("Jon Kazarian");
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        ticketing.setCollectTicketHolderAttributes(true);

        //Execute
        sendGridMailPrepareServiceImpl.updateHolderNameWithAutoSequenceNameAndNumber(ticketing, item, dataForTicketFtl);
        assertEquals("Jon Kazarian - Men's Team: 12", dataForTicketFtl.getHolderName());

    }

    @Test
    void test_updateHolderNameWithAutoSequenceNameAndNumber_CollectTicketHolderAttributesFalse_PurchaserNameISPresent(){

        //SetUp
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoSequeneceName(SequenceName);
        item.setAutoAssignedNumber("12");
        item.setPurchaserName("Jon Kazarian");
        item.setHolderName("Jon Kazarian");
        item.setTicketType(TicketType.FREE);
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        ticketing.setCollectTicketHolderAttributes(false);

        //Execute
        sendGridMailPrepareServiceImpl.updateHolderNameWithAutoSequenceNameAndNumber(ticketing, item, dataForTicketFtl);
        assertEquals("Jon Kazarian - Men's Team: 12", dataForTicketFtl.getPurchaserName());
    }

    @Test
    void test_updateHolderNameWithAutoSequenceNameAndNumber_CollectTicketHolderAttributesFalse_PurchaserNameIsNOTPresent(){

        //SetUp
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoSequeneceName(SequenceName);
        item.setAutoAssignedNumber("12");
        item.setHolderName("Jon Kazarian");
        item.setTicketType(TicketType.FREE);
        TicketHolderEmailDto dataForTicketFtl = new TicketHolderEmailDto();

        ticketing.setCollectTicketHolderAttributes(false);

        //Execute
        sendGridMailPrepareServiceImpl.updateHolderNameWithAutoSequenceNameAndNumber(ticketing, item, dataForTicketFtl);
        assertEquals(Constants.STRING_DASH, dataForTicketFtl.getPurchaserName());
    }

    @Test
    void test_getHolderNameWithColonAndDashForEmail(){
        //SetUp
        String holderName = "Jon Kazarian";
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoAssignedNumber("12");
        item.setAutoSequeneceName(SequenceName);

        //Execute
        String actualResult = sendGridMailPrepareServiceImpl.getHolderNameWithColonAndDashForEmail(holderName, Constants.STRING_BLANK, Constants.STRING_DASH, Constants.STRING_BLANK, item.getAutoSequeneceName(), Constants.STRING_COLON, Constants.STRING_BLANK, item.getAutoAssignedNumber());
        assertTrue(actualResult.equalsIgnoreCase("Jon Kazarian - Men's Team: 12"));
    }

    @Test
    void test_getSequenceNumberWithComaSepSuccess(){

        //SetUp
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setAutoAssignedNumber("11");
        item.setTicketingTypeId(1L);
        ticketHolderEmailMap.put(1L, Collections.singletonList(item));

        //Execute
        String actualResult = sendGridMailPrepareServiceImpl.getSequenceNumberWithComaSep(ticketHolderEmailMap, item);
        assertTrue(actualResult.equalsIgnoreCase("11"));
    }

    @Test
    void test_getSequenceNumberWithComaSep_EmptyList(){

        //SetUp
        Map<Long, List<TicketHolderEmailDto>> ticketHolderEmailMap = new HashMap<>();
        TicketHolderEmailDto item = new TicketHolderEmailDto();
        item.setTicketingTypeId(1L);
        ticketHolderEmailMap.put(1L, Collections.singletonList(item));

        //Execute
        String actualResult = sendGridMailPrepareServiceImpl.getSequenceNumberWithComaSep(ticketHolderEmailMap, item);
        assertTrue(actualResult.equalsIgnoreCase(Constants.STRING_EMPTY));
    }

    @Test
    void test_setSequenceNameInTicketHolderEmailDto_SequenceNamePresent(){

        //SetUp
        AutoAssignedAttendeeNumbers attendeeNumbers = new AutoAssignedAttendeeNumbers();
        attendeeNumbers.setSequenceName(SequenceName);
        eventTickets.setAutoAssignedAttendeeNumber(attendeeNumbers);
        //Execute
        String actualResult = sendGridMailPrepareServiceImpl.getAutoAssignSeqName("11", eventTickets);
        assertTrue(actualResult.equalsIgnoreCase(SequenceName));
    }

    @Test
    void test_setSequenceNameInTicketHolderEmailDto_SequenceNameNotPresent(){

        //Execute
        String actualResult = sendGridMailPrepareServiceImpl.getAutoAssignSeqName(Constants.STRING_EMPTY, eventTickets);
        assertTrue(actualResult.equalsIgnoreCase(Constants.STRING_EMPTY));
    }

    @Test
    void test_sendMagicLinkTogivenEmail(){

        //setup
        String email = "<EMAIL>";
        Long userId = 1L;

        //mock
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);

        //Execute
        sendGridMailPrepareServiceImpl.sendEventLevelMagicLinkEmailToUser(email, event, "magiclink");



    }

    @Test
    void test_sendAttendeeCheckInEmail(){

        //setup
        String ticketHolderFirstName = "test";
        String ticketHolderEmail = "<EMAIL>";
        Long userId = 1L;
        emailMessage  = new EmailMessage(TemplateId.ATTENDEE_CHECKIN);
        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        //mock

        //Execute
        sendGridMailPrepareServiceImpl.sendAttendeeCheckInEmail(anyString(),anyString(),any(),any(), any());
    }

    @Test
    void test_sendBuyerReceiptDonation(){

        //setup
        Double amount = 23.98;
        Double paidAmount = 21.67;
        emailMessage  = new EmailMessage(TemplateId.BUYER_RECEIPT_DONATION);
        Map<String, Object> substitutionMap = emailMessage.getSubstitutionData();

        //mock
        doReturn("").when(serviceHelper).getEventBaseUrl((Event) any());
        when(autoLoginService.getOrCreateEventLevelMagicLinkToken(any(), anyLong(), anyLong(), anyString())).thenReturn("magicLinkToken");
        doReturn(eventDesignDetail).when(eventDesignDetailService).findByEvent(event);


        when(emailImageHelper.getHeaderImageForEmail(eventDesignDetailService.getEventOrWhiteLabelHeaderLogoLocation(event))).thenReturn(String.valueOf(substitutionMap));

        //Execute
        sendGridMailPrepareServiceImpl.sendBuyerReceiptDonation(user,event,amount,paidAmount,true,true,false,"",new Date(),"");



    }
}
