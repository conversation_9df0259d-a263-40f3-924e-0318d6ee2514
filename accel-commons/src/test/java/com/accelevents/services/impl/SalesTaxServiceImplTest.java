package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.*;
import com.accelevents.dto.SalesTaxDto;
import com.accelevents.dto.SalesTaxFeeDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.SalesTaxRepository;
import com.accelevents.repositories.SeatingCategoryRepository;
import com.accelevents.services.TicketingOrderManagerService;
import com.accelevents.services.TicketingService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.services.TicketingTypeTicketService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.SalesTaxDetailsDto;
import com.accelevents.ticketing.dto.TicketTypesDto;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SalesTaxServiceImplTest {

    @Spy
    @InjectMocks
    private SalesTaxServiceImpl salesTaxServiceImpl = new SalesTaxServiceImpl();
    @Mock
    private SalesTaxRepository salesTaxRepository;
    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private TicketingService ticketingService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;
    @Mock
    private EventTicketsRepository eventTicketsRepository;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    private Event event;
    private SalesTaxFeeDto salesTaxFeeDto;
    private SalesTaxDto salesTaxDto;
    private  SalesTax salesTax;
    private TicketingType ticketingType;
    private Ticketing ticketing;

    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        salesTaxDto = new SalesTaxDto();
        salesTaxDto.setCountry("India");
        salesTaxDto.setSalesTaxId("874512L");
        salesTaxDto.setTicketingTypeIds(Arrays.asList(16017L, 16018L, 16019L));
        salesTaxDto.setSalesTaxName("GST");
        salesTaxDto.setSalesTaxRate(10.0);
        salesTaxDto.setAbsorbTax(false);
        salesTaxFeeDto = new SalesTaxFeeDto();
        salesTaxFeeDto.setSalesTaxRate(10.0);
        salesTaxFeeDto.setAbsorbTax(true);
        salesTaxFeeDto.setTicketingTypeIds("16016");

        salesTax = SalesTax.toEntity(salesTaxDto, event.getEventId(), Arrays.asList(16022L, 16023L, 16024L));
        salesTax.setAllTickeType(true);
        salesTax.setAbsorbSalesTax(false);
        salesTax.setSalesTaxRate(10.0);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);
    }

    @Test
    void test_save() {

        //Execution
        salesTaxServiceImpl.save(salesTax,event);

        //Assertion
        ArgumentCaptor<SalesTax> salesTaxArgumentCaptor = ArgumentCaptor.forClass(SalesTax.class);
        verify(salesTaxRepository).save(salesTaxArgumentCaptor.capture());

        SalesTax salesTaxData =  salesTaxArgumentCaptor.getValue();
        assertEquals(salesTax.getAbsorbSalesTax(), salesTaxData.getAbsorbSalesTax());
        assertEquals(salesTax.getAllTickeType(), salesTaxData.getAllTickeType());
        assertEquals(salesTax.getCountry(), salesTaxData.getCountry());
        assertEquals(salesTax.getEventId(), salesTaxData.getEventId());
        assertEquals(salesTax.getSalesTaxId(), salesTaxData.getSalesTaxId());
        assertEquals(salesTax.getSalesTaxName(), salesTaxData.getSalesTaxName());
        assertEquals(salesTax.getSalesTaxRate(), salesTaxData.getSalesTaxRate());
        assertEquals(salesTax.getTicketingTypeId(), salesTaxData.getTicketingTypeId());
    }

    @Test
    void test_saveSalesTaxDetails_DetailsAlreadyExist() {

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.of(salesTax));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> salesTaxServiceImpl.saveSalesTaxDetails(salesTaxDto,event));

        //Assertion
        verify(salesTaxRepository).findByEventId(anyLong());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.SALES_TAX_DETAILS_ALREADY_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    static Object isAllTicket(){
        return new Object[]{
          new Object[]{true, Collections.singletonList(16017L)},
          new Object[]{false, Arrays.asList(16017L, 16018L, 16019L)}
        };
    }
    @ParameterizedTest
    @MethodSource("isAllTicket")
    void test_saveSalesTaxDetails_AllTicketFalse(boolean isAllTicket, List<Long> TicketingTypeIds) {

        //setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        salesTaxDto.setTicketingTypeIds(TicketingTypeIds);
        salesTaxDto.setAllTicketType(isAllTicket);

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.empty());
        when(ticketingTypeService.findAllByTicketTypeIdsAndCreatedFrom(anyList())).thenReturn(ticketingTypes);

        //Execution
        salesTaxServiceImpl.saveSalesTaxDetails(salesTaxDto,event);

        //Assertion
        verify(salesTaxRepository).findByEventId(anyLong());
        verify(ticketingTypeService).findAllByTicketTypeIdsAndCreatedFrom(anyList());

        ArgumentCaptor<SalesTax> salesTaxArgumentCaptor = ArgumentCaptor.forClass(SalesTax.class);
        verify(salesTaxRepository).save(salesTaxArgumentCaptor.capture());

        SalesTax salesTaxData =  salesTaxArgumentCaptor.getValue();
        assertEquals(isAllTicket, salesTaxData.getAllTickeType());
    }

    @Test
    void test_setTicketTypeIds() {

        //setup
        ticketingType.setId(1L);
        ticketing.setRecurringEvent(true);
        RecurringEvents recurringEvents = EventDataUtil.getRecurringEvents();
        ticketingType.setEndDate(DateUtils.getCurrentDate());
        ticketingType.setStartDate(DateUtils.getCurrentDate());
        //ticketingType.setCategories("1");
        ticketingType.setRecurringEvent(recurringEvents);
        ticketingType.setCreatedFrom(1L);
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(1);

        SeatingCategories seatingCategories = new SeatingCategories();
        seatingCategories.setId(1L);
        seatingCategories.setName("MUSIC");
        seatingCategories.setHavingVariations(true);
        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        //mock

        //Execution
       // SalesTaxDetailsDto salesTaxDetailsDto = salesTaxServiceImpl.getTicketTypeBaseDtoSalesTax(event);

        //Assertions
//        verify(ticketingService).findByEvent(any());
 //       verify(eventTicketsRepoService).isTicketingTypeExist(any());
  //      verify(seatingCategoryRepository).findByIdIn(anyList());
//        verify(ticketingTypeTicketService).findAllByEvent(any());
//
//        assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getTicketingTypeIds(), salesTax.getTicketingTypeId());
//        assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getRecurringEventSalesEndStatus(), ticketingType.getRecurringEventSalesEndStatus());
 //       assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getEventCategories().get(0).getName(), seatingCategories.getName());
  //      assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getRecurringEventId().longValue(), ticketingType.getRecurringEvent().getId());
   //     assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getCreatedFrom(), ticketingType.getCreatedFrom());
    }

    @Test
    void test_setTicketTypeIds_withRecurringEventNull() {

        //setup
        ticketing.setRecurringEvent(true);
        ticketingType.setEndDate(DateUtils.getCurrentDate());
        ticketingType.setStartDate(DateUtils.getCurrentDate());
        //ticketingType.setCategories("1");
        ticketingType.setTicketTypeName("Shirt");

        SeatingCategories seatingCategories = new SeatingCategories();
        seatingCategories.setId(1L);
        seatingCategories.setName("MUSIC");
        seatingCategories.setHavingVariations(true);
        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        //mock


//        //Execution
//        SalesTaxDetailsDto salesTaxDetailsDto = salesTaxServiceImpl.getTicketTypeBaseDtoSalesTax(event);
//
//        //Assertions
//        verify(ticketingTypeTicketService).findAllByEvent(any());
//
//        assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getTicketingTypeIds(), salesTax.getTicketingTypeId());
   //     assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getRecurringEventSalesEndStatus(), ticketingType.getRecurringEventSalesEndStatus());
    //    assertEquals(salesTaxDetailsDto.getTicketTypes().get(0).getEventCategories().get(0).getName(), seatingCategories.getName());
    }

    @Test
    void test_setTicketTypeIds_withRecurringEventFalseAndCategoriesEmpty() {

        //setup
        SalesTax salesTax = new SalesTax();
        salesTax.setTicketingTypeId("16016");

        ticketing.setRecurringEvent(false);
        ticketingType.setEndDate(DateUtils.getCurrentDate());
        ticketingType.setStartDate(DateUtils.getCurrentDate());
        //ticketingType.setCategories("");
        ticketingType.setCreatedFrom(1L);
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(1);

        SeatingCategories seatingCategories = new SeatingCategories();
        seatingCategories.setId(1L);
        seatingCategories.setName("MUSIC");
        seatingCategories.setHavingVariations(true);
        List<SeatingCategories> seatingCategoriesList = new ArrayList<>();
        seatingCategoriesList.add(seatingCategories);

        //mock



        when(ticketingTypeTicketService.findAllByEvent(any())).thenReturn(Collections.singletonList(ticketingType));

        //Execution
        List<TicketTypesDto> ticketTypesDtos = salesTaxServiceImpl.getTicketTypeBaseDtoSalesTax(event);

        //Assertions
        verify(ticketingTypeTicketService).findAllByEvent(any());

        assertEquals(ticketTypesDtos.get(0).getTypeId().longValue(), ticketingType.getId());
        assertEquals(ticketTypesDtos.get(0).getCategoryId(), ticketingType.getCategoryId());
        assertEquals(ticketTypesDtos.get(0).getName(), ticketingType.getTicketTypeName());
    }

  //  @Test
    void test_setTicketTypeIds_withTicketingTypeListEmpty(){

        //mock
        when(ticketingService.findByEvent(any())).thenReturn(ticketing);
        when(ticketingTypeTicketService.findAllByEvent(any())).thenReturn(Collections.emptyList());

//        //Execution
//        SalesTaxDetailsDto salesTaxDetailsDto = salesTaxServiceImpl.getTicketTypeBaseDtoSalesTax(event);
//
//        //Assertion
//        assertTrue(salesTaxDetailsDto.getTicketTypes().isEmpty());
    }

    //@Test
    void test_getSalesTaxDetailsByEventId_SalesTaxIsNotPresent() {

        //setup
        TicketTypesDto ticketTypesDto = new TicketTypesDto();
        ticketTypesDto.setTypeId(16017L);
        SalesTaxDetailsDto salesTaxDetailsDto = new SalesTaxDetailsDto();
        salesTaxDetailsDto.setTicketTypes(Collections.singletonList(ticketTypesDto));

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.empty());
        doReturn(salesTaxDetailsDto).when(salesTaxServiceImpl).getTicketTypeBaseDtoSalesTax(any());

        //Execution
        SalesTaxDetailsDto salesTaxDetailsDtoData = salesTaxServiceImpl.getSalesTaxDetailsByEventId(event);

        //Assertions
        verify(salesTaxRepository).findByEventId(anyLong());
        verify(salesTaxServiceImpl).getTicketTypeBaseDtoSalesTax(any());

        assertEquals(salesTaxDetailsDtoData.getTicketTypes().get(0).getTypeId(), ticketTypesDto.getTypeId());
        assertFalse(salesTaxDetailsDtoData.getAbsorbTax());
        assertNull(salesTaxDetailsDtoData.getCountry());
        assertNull(salesTaxDetailsDtoData.getSalesTaxId());
        assertNull(salesTaxDetailsDtoData.getSalesTaxName());
        assertNull(salesTaxDetailsDtoData.getSalesTaxRate());
    }

    //@Test
    void test_getSalesTaxDetailsByEventId_SalesTaxIstPresent() {

        //setup
        TicketTypesDto ticketTypesDto = new TicketTypesDto();
        ticketTypesDto.setTypeId(16017L);
        SalesTaxDetailsDto salesTaxDetailsDto = new SalesTaxDetailsDto();
        salesTaxDetailsDto.setTicketTypes(Collections.singletonList(ticketTypesDto));

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.of(salesTax));
        doReturn(salesTaxDetailsDto).when(salesTaxServiceImpl).getTicketTypeBaseDtoSalesTax(any());

        //Execution
        SalesTaxDetailsDto salesTaxDetailsDtoData = salesTaxServiceImpl.getSalesTaxDetailsByEventId(event);

        //Assertions
        verify(salesTaxRepository).findByEventId(anyLong());
        verify(salesTaxServiceImpl).getTicketTypeBaseDtoSalesTax(any());

        assertEquals(salesTaxDetailsDtoData.getTicketTypes().get(0).getTypeId(), ticketTypesDto.getTypeId());
        assertEquals(salesTaxDetailsDtoData.getAbsorbTax(), salesTax.getAbsorbSalesTax());
        assertEquals(salesTaxDetailsDtoData.getCountry(), salesTax.getCountry());
        assertEquals(salesTaxDetailsDtoData.getSalesTaxId(), salesTax.getSalesTaxId());
        assertEquals(salesTaxDetailsDtoData.getSalesTaxName(), salesTax.getSalesTaxName());
        assertEquals(salesTaxDetailsDtoData.getSalesTaxRate(), salesTax.getSalesTaxRate());
    }

    @Test
    void test_updateSalesTaxDetails_salesTaxDetailsNotPresent() {

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.empty());

        //Execution
        salesTaxServiceImpl.updateSalesTaxDetails(salesTaxDto, event);

        //assertion
        ArgumentCaptor<SalesTax> salesTaxArgumentCaptor = ArgumentCaptor.forClass(SalesTax.class);
        verify(salesTaxRepository).save(salesTaxArgumentCaptor.capture());

        SalesTax salesTaxData =  salesTaxArgumentCaptor.getValue();
        assertEquals(salesTaxDto.getSalesTaxRate(), salesTaxData.getSalesTaxRate());
    }

    @Test
    void test_updateSalesTaxDetails_salesTaxDetailsPresent() {

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        salesTax.setTicketingTypeId("16017,16018");
        salesTaxDto.setAllTicketType(false);

        //mock
        when(salesTaxRepository.findByEventId(event.getEventId())).thenReturn(Optional.of(salesTax));
        when(ticketingTypeService.findAllByTicketTypeIdsAndCreatedFrom(anyList())).thenReturn(ticketingTypes);

        //Execution
        salesTaxServiceImpl.updateSalesTaxDetails(salesTaxDto, event);

        //assertion
        ArgumentCaptor<SalesTax> salesTaxArgumentCaptor = ArgumentCaptor.forClass(SalesTax.class);
        verify(salesTaxRepository).save(salesTaxArgumentCaptor.capture());

        SalesTax salesTaxData =  salesTaxArgumentCaptor.getValue();
        assertEquals(salesTaxDto.getSalesTaxRate(), salesTaxData.getSalesTaxRate());
        assertFalse(salesTaxData.getAllTickeType());
    }

    @Test
    void test_deleteSalesTaxDetailsByEventId_throwException() {
        //Mock
        when( salesTaxRepository.findByEventId(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> salesTaxServiceImpl.deleteSalesTaxDetailsByEventId(event));

        //Assertion
        verify(salesTaxRepository).findByEventId(anyLong());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.SALES_TAX_DETAILS_NOT_PRESENT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteSalesTaxDetailsByEventId() {

        //Mock
        when( salesTaxRepository.findByEventId(anyLong())).thenReturn(Optional.of(salesTax));

        //Execution
        salesTaxServiceImpl.deleteSalesTaxDetailsByEventId(event);

        //Assertion
        verify(salesTaxRepository).findByEventId(anyLong());
        verify(salesTaxRepository).delete(any());
    }

    @Test
    void test_getTaxFeeAndTicketTypeId() {

        //mock
        when(salesTaxRepository.getTaxFeeAndTicketTypeId(anyLong())).thenReturn(salesTaxFeeDto);

        //Execution
        SalesTaxFeeDto salesTaxFeeData = salesTaxServiceImpl.getTaxFeeAndTicketTypeId(event.getEventId());

        //Assertion
        verify(salesTaxRepository).getTaxFeeAndTicketTypeId(anyLong());
        assertEquals(salesTaxFeeDto.getSalesTaxRate(), salesTaxFeeData.getSalesTaxRate(), 0);
        assertEquals(salesTaxFeeDto.getAbsorbTax(), salesTaxFeeData.getAbsorbTax());
        assertEquals(salesTaxFeeDto.getTicketingTypeIds(), salesTaxFeeData.getTicketingTypeIds());
    }

    @Test
    void test_getSalesTaxByEvent(){

        //mock
        when(salesTaxRepository.findByEventId(anyLong())).thenReturn(Optional.of(salesTax));

        //Execution
        Optional<SalesTax> salesTaxDetails = salesTaxServiceImpl.getSalesTaxByEvent(event);

        //Assertion
        assertEquals(salesTaxDetails.get().getSalesTaxRate(), salesTax.getSalesTaxRate());
        assertEquals(salesTaxDetails.get().getAbsorbSalesTax(), salesTax.getAbsorbSalesTax());
        assertEquals(salesTaxDetails.get().getAllTickeType(), salesTax.getAllTickeType());

        verify(salesTaxRepository).findByEventId(anyLong());
    }

    @Test
    void test_getTotalSalesTax_ticketingTypeIdListEmpty(){

        //setup
        salesTaxDto.setTicketingTypeIds(Collections.emptyList());
        double totalSTax = 0D;

        //mock
        when(salesTaxRepository.getSalesTaxDetails(anyLong())).thenReturn(Optional.of(salesTaxDto));

        //Execution
        double totalSalesTax = salesTaxServiceImpl.getTotalSalesTax(event.getEventId());

        //Assertion
        verify(salesTaxRepository).getSalesTaxDetails(anyLong());
        assertEquals(totalSalesTax, totalSTax, 0);
    }

    @Test
    void test_getTotalSalesTax_salesTaxDtoNotPresent(){

        //setup
        double totalSTax = 0D;

        //mock
        when(salesTaxRepository.getSalesTaxDetails(anyLong())).thenReturn(Optional.empty());

        //Execution
        double totalSalesTax = salesTaxServiceImpl.getTotalSalesTax(event.getEventId());

        //Assertion
        verify(salesTaxRepository).getSalesTaxDetails(anyLong());
        assertEquals(totalSalesTax, totalSTax, 0);
    }

    @Test
    void test_getTotalSalesTax(){

        //mock
        EventTickets eventTickets = new EventTickets();
        eventTickets.setSalesTaxFee(10.520082389289394);
        eventTickets.setRefundedSalesTaxFee(05.26);

        double totalSTax = eventTickets.getSalesTaxFee() - eventTickets.getRefundedSalesTaxFee();
        when(salesTaxRepository.getSalesTaxDetails(anyLong())).thenReturn(Optional.of(salesTaxDto));
        when(eventTicketsCommonRepo.findTotalSalesTax(anyList())).thenReturn(totalSTax);

        //Execution
        double totalSalesTax = salesTaxServiceImpl.getTotalSalesTax(event.getEventId());

        //Assertion
        verify(salesTaxRepository).getSalesTaxDetails(anyLong());

        assertEquals(totalSalesTax, totalSTax, 0);
    }
}