package com.accelevents.services.impl;

import com.accelevents.auction.dto.ContactDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.session_speakers.CustomFormAttribute;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.ContactsListMappingRepository;
import com.accelevents.repositories.ContactsListRepository;
import com.accelevents.repositories.ContactsRepository;
import com.accelevents.repositories.MessageToContactsRepository;
import com.accelevents.services.*;
import com.accelevents.session_speakers.dto.CustomAttributeDetailsDto;
import com.accelevents.session_speakers.dto.CustomAttributesResponseDto;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Stream;

import static com.accelevents.exceptions.ConflictException.ContactExceptionMsg.CONTACT_ALREADY_ADDED;
import static com.accelevents.exceptions.ConflictException.UserExceptionConflictMsg.EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER;
import static com.accelevents.utils.GeneralUtils.isValidEmailAddress;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ContactServiceImplTest {

    @Mock
    private ContactsRepository contactsRepository;


    @Mock
    private EmailSuppressionService emailSuppressionService;

    @Mock
    private ContactModuleSettingsService contactModuleSettingsService;

    @Mock
    private ContactsListMappingRepository contactsListMappingRepository;

    @Mock
    private ContactsCommonRepoService contactsCommonRepoService;
    @Mock
    private ContactsListRepository contactsListRepository;
     @Mock
     private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;

    @Mock
    private MessageToContactsRepository messageToContactsRepository;

    @Mock
    private CustomAttributesResponseDto c;

    @Spy
    @InjectMocks
    private ContactServiceImpl contactServiceImpl = new ContactServiceImpl();
    private Event event;
	private Contacts contacts;
    private ContactDto contactDto;

    private  ContactsList contactsList;
    private ContactsListMapping contactsListMapping;
    private String email = "<EMAIL>";
    private long phoneNumber = 9898989898L;
    private long id = 1L;

    private long contactsListId = 1L;


    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        contactDto = new ContactDto();
        contacts = new Contacts();
        contactsList = new ContactsList();
        contactsListMapping = new ContactsListMapping();
    }
    static Object[] getFilePath(){
        return new Object[]{
                new Object[]{"csvFile/Exported Contacts2.csv"} ,
                new Object[]{"csvFile/Exported Contacts1.csv"} ,
                new Object[]{"csvFile/Exported Contacts.csv"} ,
        };
    }

    //TODO: Junit wrong parameter file, it's not throwing exception with given data
    /*@ParameterizedTest
    @MethodSource("getFilePath")
    void test_uploadContactsCSV_withIsValidHeaderFalseThrowExceptionUPLOAD_FILE_HEADER_NOT_CORRECT(String fileName) throws IOException {

        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream(fileName);
        //setup
        MockMultipartFile file = new MockMultipartFile("file", fileName, "multipart/form-data", inputFile);
        
        //mock

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.uploadContactsCSV(file, event));
        
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.UPLOAD_FILE_HEADER_NOT_CORRECT.getDeveloperMessage(), exception.getMessage());
    }*/

    @Test
    void test_uploadContactsCSV_withErrorAndNextItem3() throws IOException {

        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/Exported Contacts1.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/Exported Contacts1.csv", "multipart/form-data", inputFile);

        // Mocking valid headers and max contact upload validation
        Mockito.doNothing()
                .when(contactServiceImpl)
                .isValidHeaders(Mockito.any(String[].class), Mockito.anyList(), Mockito.anyMap());

        doAnswer(invocation -> {
            List<ContactDto> validContacts = invocation.getArgument(1);

            ContactDto contactDto = new ContactDto();
            contactDto.setEmail("<EMAIL>");
            contactDto.setId(0L);
            contactDto.setContactId("0");

            CustomAttributeDetailsDto detailsDto = new CustomAttributeDetailsDto();
            detailsDto.setAttributes(new ArrayList<>()); // prevent NPE
            contactDto.setContactAttributeDetailsDto(detailsDto);

            validContacts.add(contactDto);

            // return null since saveValidContacts is void
            return null;
        }).when(contactServiceImpl).saveValidContacts(
                any(Event.class),
                anyList(),
                any(ContactsList.class),
                anyList(),
                anyList(),
                anySet(),
                anyList(),
                anySet()
        );


        // Executing the method under test
        contactServiceImpl.createContactsUsingCSV(event, file, contactsList);

        ArgumentCaptor<Event> eventCaptor = ArgumentCaptor.forClass(Event.class);
        ArgumentCaptor<List<ContactDto>> validContactsCaptor = ArgumentCaptor.forClass((Class) List.class);
        ArgumentCaptor<ContactsList> contactsListCaptor = ArgumentCaptor.forClass(ContactsList.class);
        ArgumentCaptor<List<Contacts>> contactsToSaveCaptor = ArgumentCaptor.forClass((Class) List.class);
        ArgumentCaptor<List<ContactDto>> invalidContactsCaptor = ArgumentCaptor.forClass((Class) List.class);
        ArgumentCaptor<Set<Long>> existingContactIdsCaptor = ArgumentCaptor.forClass((Class) Set.class);
        ArgumentCaptor<List<CustomFormAttribute>> customFormAttributesCaptor = ArgumentCaptor.forClass((Class) List.class);
        ArgumentCaptor<Set<String>> existingEmailsCaptor = ArgumentCaptor.forClass((Class) Set.class);

        verify(contactServiceImpl).saveValidContacts(
                eventCaptor.capture(),
                validContactsCaptor.capture(),
                contactsListCaptor.capture(),
                contactsToSaveCaptor.capture(),
                invalidContactsCaptor.capture(),
                existingContactIdsCaptor.capture(),
                customFormAttributesCaptor.capture(),
                existingEmailsCaptor.capture()
        );

        // Assertions
        assertEquals(event, eventCaptor.getValue());
        assertEquals(contactsList, contactsListCaptor.getValue());
        assertNotNull(validContactsCaptor.getValue());
        assertNotNull(contactsToSaveCaptor.getValue());
        assertNotNull(invalidContactsCaptor.getValue());
        assertNotNull(existingContactIdsCaptor.getValue());
        assertNotNull(customFormAttributesCaptor.getValue());
        assertNotNull(existingEmailsCaptor.getValue());
    }

    @Test
    void test_uploadContactsCSV_withErrorEmptyAndNextItem3Empty() throws IOException {

        //setup
        InputStream inputFile = this.getClass().getClassLoader().getResourceAsStream("csvFile/Exported Contacts.csv");
        MockMultipartFile file = new MockMultipartFile("file", "csvFile/Exported Contacts1.csv", "multipart/form-data", inputFile);

        //mock
        Mockito.doNothing()
                .when(contactServiceImpl)
                .isValidHeaders(Mockito.any(String[].class), Mockito.anyList(), Mockito.anyMap());

        //Execution
        contactServiceImpl.createContactsUsingCSV(event,file,contactsList);

        //Assertion
        //verify(contactsRepository, times(1)).findByEmailAndEventId(anyString(), anyLong());
    }

    @Test
    void test_isValidEmailAddress_withValidEmailAddress(){

        //Execution
        boolean validEmeailAddress = isValidEmailAddress(email);

        //Assertion
        assertTrue(validEmeailAddress);
    }

    @Test
    void test_isValidEmailAddress_withInvalidEmailAddress(){

        //setup
        String email = "Jon.com@jonvalid";

        //Execution
        boolean validEmeailAddress = isValidEmailAddress(email);

        //Assertion
        assertFalse(validEmeailAddress);
    }

    static Stream<Arguments> getHeader() {
        String[] validHeader = {Constants.CONTACT_SPACE_ID, Constants.FIRST_NAME, Constants.LAST_NAME, Constants.EMAIL};
        String[] invalidHeaderWrongOrder = {Constants.FIRST_NAME, Constants.CONTACT_SPACE_ID, Constants.LAST_NAME, Constants.EMAIL};
        String[] invalidHeaderAnotherOrder = {Constants.LAST_NAME, Constants.FIRST_NAME, Constants.CONTACT_SPACE_ID, Constants.EMAIL};
        String[] invalidHeaderYetAnotherOrder = {Constants.FIRST_NAME, Constants.LAST_NAME, Constants.CONTACT_SPACE_ID, Constants.EMAIL};
        String[] invalidHeaderMissingField = {Constants.FIRST_NAME, Constants.LAST_NAME, Constants.EMAIL, "Number"};

        return Stream.of(
                Arguments.of(validHeader, true),
                Arguments.of(invalidHeaderWrongOrder, false),
                Arguments.of(invalidHeaderAnotherOrder, false),
                Arguments.of(invalidHeaderYetAnotherOrder, false),
                Arguments.of(invalidHeaderMissingField, false)
        );
    }


    @ParameterizedTest
    @MethodSource("getHeader")
    void isValidHeaders(String[] header, boolean shouldPass) {
        if (shouldPass) {
            assertDoesNotThrow(() ->
                    contactServiceImpl.isValidHeaders(header, new ArrayList<>(), new HashMap<>())
            );
        } else {
            assertThrows(NotAcceptableException.class, () ->
                    contactServiceImpl.isValidHeaders(header, new ArrayList<>(), new HashMap<>())
            );
        }
    }


    @Test
    void test_isValidHeaders_withValidHeader() {
        String[] header = {Constants.CONTACT_SPACE_ID, Constants.FIRST_NAME, Constants.LAST_NAME, Constants.EMAIL};
        assertDoesNotThrow(() ->
                contactServiceImpl.isValidHeaders(header, new ArrayList<>(), new HashMap<>())
        );
    }

    @Test
    void test_isValidHeaders_withValiHeader() {
        //setup
        String[] header = {Constants.CONTACT_SPACE_ID,Constants.FIRST_NAME, Constants.LAST_NAME, Constants.EMAIL};
        contactServiceImpl.isValidHeaders(header,new ArrayList<>(),new HashMap<>());

    }

    @Test
    void test_validate_withEmailEmptyAndContactsPresent() {

        //setup
        contactDto.setEmail("");

        //mock
        //Mockito.doNothing().when(contactServiceImpl).validateMaxContactUpload(any());
        //when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.of(contacts));
        //Execution
        String validate = contactServiceImpl.validate(contactDto);

        //Assertion
        //assertEquals( "Contact already present with same email",validate);
    }

    public static Object[] getContactDtoEmailAndErrorMessageAndIsValidEmaiAddress(){
        return new Object[]{
                new Object[]{"", "Email must be not null OR Email must be less than 75 characters.", true} ,
                new Object[]{"JonKaz.com@gmail", "This is not valid email address.", false} ,
                new Object[]{"<EMAIL>", "", true} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getContactDtoEmailAndErrorMessageAndIsValidEmaiAddress")
    void test_validate_withEmailEmptyAndContactsNotPresent(String email, String error, boolean validEmailAddress) {

        //setup
        contactDto.setEmail(email);
        contactDto.setFirstName("FirstName");
        contactDto.setLastName("LastName");

        //mock
        //Mockito.doNothing().when(contactServiceImpl).validateMaxContactUpload(any());
        //when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.empty());


        //Execution
        String validate = contactServiceImpl.validate(contactDto);

        //Assertion
        assertEquals(validate, error);
    }

    @Test
    void test_addContact() {

        //mock
        contactDto = new ContactDto("Jon", "Kaz", email, id, CountryCode.US.name(), phoneNumber, false);

        //Execution
        when(contactsCommonRepoService.findContactsListByIdAndEventId(eq(contactsListId), anyLong())).thenReturn(contactsList);
        when(contactsCommonRepoService.findContactsByEmailAndEventId(eq(contactDto.getEmail()), anyLong())).thenReturn(null);
        when(contactsRepository.save(any(Contacts.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Execution
        Contacts savedContact = contactServiceImpl.addContact(contactDto, event, contactsListId);

        // Verify interactions
        verify(contactsCommonRepoService, times(1)).findContactsListByIdAndEventId(eq(contactsListId), anyLong());
        verify(contactsCommonRepoService, times(1)).findContactsByEmailAndEventId(eq(contactDto.getEmail()), anyLong());
        verify(contactsRepository, times(1)).save(any(Contacts.class));

        // Assertion
        assertNotNull(savedContact);
        assertEquals(contactDto.getCountryCode(), savedContact.getCountryCode().toString());
        assertEquals(contactDto.getFirstName(), savedContact.getFirstName());
        assertEquals(contactDto.getLastName(), savedContact.getLastName());
        assertEquals(contactDto.getEmail(), savedContact.getEmail());
        assertEquals(contactDto.getPhoneNumber(), savedContact.getPhoneNumber());

    }

    @Test
    void test_validateMaxContactUpload_withContactLimitAndThrowExceptionCONTACT_UPLOAD_MAX_LIMIT_REACHED() {

        //setup
        int currentContactUploaded = 1;
		ContactModuleSettings contactModuleSettings = new ContactModuleSettings();
        contactModuleSettings.setContactLimit(1);

        NotAcceptableException.ContactExceptionMsg exceptionMsg = NotAcceptableException.ContactExceptionMsg.CONTACT_UPLOAD_MAX_LIMIT_REACHED;
        exceptionMsg.setErrorMessage(exceptionMsg.getErrorMessage().replace("{MAX_CONTACTS}", String.valueOf(contactModuleSettings.getContactLimit())));
        exceptionMsg.setDeveloperMessage(
                exceptionMsg.getDeveloperMessage().replace("{MAX_CONTACTS}", String.valueOf(contactModuleSettings.getContactLimit())));

        //mock
        when(contactsListRepository.getContactsCountByEventIdAndId(event.getEventId(),contactsListId)).thenReturn(currentContactUploaded);
        when(contactModuleSettingsService.findContactModuleSettingsByEvent(any())).thenReturn(Optional.of(contactModuleSettings));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.validateMaxContactUploadPerContactsList(event,contactsListId));

        //Assertion
        assertEquals(exceptionMsg.getDeveloperMessage(), exception.getMessage());
        verify(contactsListRepository, times(1)).getContactsCountByEventIdAndId(anyLong(),anyLong());
        verify(contactModuleSettingsService, times(1)).findContactModuleSettingsByEvent(any());
    }

    @Test
    void test_validateMaxContactUpload_withoutContactLimit() {

        //setup
        int currentContactUploaded = 1;

        //mock
        when(contactsListRepository.getContactsCountByEventIdAndId(event.getEventId(),contactsListId)).thenReturn(currentContactUploaded);
        when(contactModuleSettingsService.findContactModuleSettingsByEvent(any())).thenReturn(Optional.empty());

        //Execution
        contactServiceImpl.validateMaxContactUploadPerContactsList(event,contactsListId);

        //Assertion
        verify(contactsListRepository, times(1)).getContactsCountByEventIdAndId(anyLong(),anyLong());
        verify(contactModuleSettingsService, times(1)).findContactModuleSettingsByEvent(any());
    }

    @Test
    void test_isEmailRegistered_withContactsEmailAndEventId() {
        //setup
        contacts.setEventId(event.getEventId());
        contacts.setEmail(email);

        //mock
        when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.of(contacts));

        //Execution
        boolean emailRegistered = contactServiceImpl.isEmailRegistered(email, event);

        //Assertion
        assertTrue(emailRegistered);
    }

    @Test
    void test_isEmailRegistered_withoutContactsEmailAndEventId() {

        //mock
        when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.empty());

        //Execution
        boolean emailRegistered = contactServiceImpl.isEmailRegistered(email, event);

        //Assertion
        assertFalse(emailRegistered);
    }

    @Test
    void test_isPhoneRegistered_withContactsPhoneNumberAndEventId() {
        //setup
        contacts.setPhoneNumber(phoneNumber);
        contacts.setEventId(event.getEventId());

        //mock
        when(contactsRepository.findByPhoneNumberAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(contacts));

        //Execution
        boolean phoneRegistered = contactServiceImpl.isPhoneRegistered(phoneNumber, event);

        //Assertion
        assertTrue(phoneRegistered);
    }

    @Test
    void test_isPhoneRegistered_withoutContactsPhoneNumberAndEventId() {

        //mock
        when(contactsRepository.findByPhoneNumberAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

        //Execution
        boolean phoneRegistered = contactServiceImpl.isPhoneRegistered(phoneNumber, event);

        //Assertion
        assertFalse(phoneRegistered);
    }

    @Test
    void test_deleteContact_withContacts() {

        //setup
        Long contactId = id;
        when(contactsRepository.findById(anyLong())).thenReturn(Optional.of(contacts));
        when(contactsCommonRepoService.findContactsListByIdAndEventId(anyLong(), anyLong())).thenReturn(contactsList);
        when(contactsListMappingRepository.findByContactsAndContactsList(any(Contacts.class), any(ContactsList.class))).thenReturn(Optional.of(contactsListMapping));

        // Execution
        contactServiceImpl.removeContactFromContactsList(contactId, event, contactsListId);

        // Verification
        verify(contactsRepository, times(1)).findById(anyLong());
        verify(contactsListMappingRepository, times(1)).findByContactsAndContactsList(any(Contacts.class), any(ContactsList.class));
        verify(contactsListMappingRepository, times(1)).save(any(ContactsListMapping.class));

    }

    @Test
    void test_deleteContact_withContactsEmptyAndThrowExceptionCONTACT_NOT_EXISTS() {

        //setup
        Long contactId = id;

        //mock
        when(contactsRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.removeContactFromContactsList(contactId, event,contactsListId));

        //Assertion
        assertEquals(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
        verify(contactsRepository, times(1)).findById(anyLong());
    }

    @Test
    void test_deleteContacts_ContactIdListEmptyAndDeleteAllFalseAndThrowExceptionCONTACT_ID_OR_PARAMETERS_NOT_PROVIDED() {

        //setup
        List<Long> contactIdList = new ArrayList<>();

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.removeContactsFromContactsList(contactIdList, event, false,contactsListId));
        assertEquals(NotAcceptableException.ContactExceptionMsg.CONTACT_ID_OR_PARAMETERS_NOT_PROVIDED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteContacts_ContactIdListAndDeleteAllFalse() {

        //setup
        List<Long> contactIdList = new ArrayList<>();
        contactIdList.add(id);

        // Mock behavior for the method call on contactsListMappingRepository
        when(contactsListMappingRepository.findByContactsIdAndEventIdAndContactsListId(
                contactIdList, event.getEventId(), contactsListId))
                .thenReturn(Collections.emptyList());

        // Execution
        contactServiceImpl.removeContactsFromContactsList(contactIdList, event, false, contactsListId);

        // Assertion
    }

    @Test
    void test_deleteContacts_ContactIdListNUllAndDeleteAllFalse() {
        Assertions.assertThrows(NotAcceptableException.class, () -> {
            contactServiceImpl.removeContactsFromContactsList(null, event, false, contactsListId);
        });
    }

    @Test
    void test_deleteContacts_ContactIdListAndDeleteAllTrue() {


        List<Long> contactIds = Arrays.asList(1L, 2L); // Provide valid contactIds
        boolean deleteAll = true; // Set deleteAll to true

        // Stub the contactsListMappingRepository.findByEventIdAndContactsListId method
        when(contactsListMappingRepository.findByEventIdAndContactsListId(anyLong(), anyLong()))
                .thenReturn(Collections.singletonList(new ContactsListMapping()));

        // When
        assertDoesNotThrow(() -> contactServiceImpl.removeContactsFromContactsList(contactIds, event, deleteAll, contactsListId));

        // Then
        verify(contactsListMappingRepository, times(1)).findByEventIdAndContactsListId(anyLong(), anyLong());
        verify(contactsListMappingRepository, times(1)).saveAll(any());
    }

    @Test
    void test_updateContact_ThrowExceptionCONTACT_NOT_EXISTS() {

        //mock
        when(contactsRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> contactServiceImpl.updateContact(contactDto, event));

        //Assertion
        assertEquals(NotAcceptableException.ContactExceptionMsg.CONTACT_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
        verify(contactsRepository, times(1)).findById(anyLong());
    }

    public static Object[] getContactDtoEmaiAndExceptionMessage(){
        return new Object[]{
                new Object[]{"<EMAIL>", EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER.getDeveloperMessage()}
        };
    }

    @ParameterizedTest
    @MethodSource("getContactDtoEmaiAndExceptionMessage")
    void test_updateContact_ThrowException(String email1, String ExceptionMessage) {

        //setup
        contacts.setId(id);
        contacts.setCountryCode(CountryCode.US);
        contacts.setPhoneNumber(phoneNumber);
        contacts.setEmail(email);

        contactDto.setEmail(email1);
        contactDto.setPhoneNumber(9898989899L);

        //mock
        when(contactsRepository.findById(anyLong())).thenReturn(Optional.of(contacts));
        when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.of(contacts));


        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> contactServiceImpl.updateContact(contactDto, event));
        assertEquals(ExceptionMessage, exception.getMessage());

        //Assertion
        verify(contactsRepository, times(1)).findById(anyLong());
        verify(contactsRepository, times(1)).findByEmailAndEventId(anyString(), anyLong());
    }

    public static Object[] getContactDtoparams(){
        return new Object[]{
                new Object[]{9898989898L, 9898989898L, "<EMAIL>", "<EMAIL>"} ,
                new Object[]{9898989899L, 9898989898L, "<EMAIL>", "<EMAIL>"} ,
                new Object[]{0L,0L,"", ""} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getContactDtoparams")
    void test_updateContact_save(long phoneNumber, long dtoPhoneNumber, String email, String dtoEmail) {

        //setup
        contacts.setId(id);
        contacts.setCountryCode(CountryCode.US);
        contacts.setPhoneNumber(phoneNumber);
        contacts.setEmail(email);

        contactDto.setEmail(dtoEmail);
        contactDto.setPhoneNumber(dtoPhoneNumber);

        //mock
        when(contactsRepository.findById(anyLong())).thenReturn(Optional.of(contacts));



        //Execution
        contactServiceImpl.updateContact(contactDto, event);

        //Assertion
        verify(contactsRepository, times(1)).findById(anyLong());

        ArgumentCaptor<Contacts> contactDtoArgumentCaptor = ArgumentCaptor.forClass(Contacts.class);
        verify(contactsRepository, times(1)).save(contactDtoArgumentCaptor.capture());

        Contacts contactsData = contactDtoArgumentCaptor.getValue();
        assertEquals(contactsData.getEmail(), contacts.getEmail());
        assertEquals(contactsData.getPhoneNumber(), contacts.getPhoneNumber());
        assertEquals(contactsData.getId(), contacts.getId());
        assertEquals(contactsData.getCountryCode().name(), contacts.getCountryCode().name());
    }

    @Test
    void test_getAllContactsData_withSearchEmpty() {
        // given
        int size       = 1;
        int page       = 1;
        String search  = "";
        String sortBy  = "id";
        boolean isAsc  = false;

        ContactDto expected = new ContactDto(
                "John",                           // firstName
                "Doe",                            // lastName
                "<EMAIL>",           // email
                42L,                              // id
                CountryCode.US,                   // countryCode
                1234567890L,                      // phoneNumber
                "Unregistered"                    // ticketStatus
        );

        // build raw row as the native query would return
        Object[] row = new Object[] {
                expected.getFirstName(),
                expected.getLastName(),
                expected.getEmail(),
                expected.getId(),
                expected.getCountryCode(),
                expected.getPhoneNumber(),
                expected.getTicketStatus()
        };

        Pageable pageable = PageRequest.of(
                page - 1,
                size,
                Sort.by(isAsc ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)
        );
        Page<Object[]> rawPage = new PageImpl<>(
                Collections.singletonList(row),
                pageable,
                1L
        );

        when(contactsRepository.findContactsByEventIdAndContactsListId(
                eq(event.getEventId()),
                eq(contactsListId),
                eq(search),
                any(Pageable.class))
        ).thenReturn(rawPage);

        // when
        DataTableResponse response = contactServiceImpl.getAllContactsDataByContactsListId(
                event,
                null,
                contactsListId,
                new PageSizeSearchObj(size, page, search, sortBy,
                        isAsc ? Sort.Direction.ASC : Sort.Direction.DESC)
        );

        // then
        verify(contactsRepository, times(1))
                .findContactsByEventIdAndContactsListId(
                        eq(event.getEventId()),
                        eq(contactsListId),
                        eq(search),
                        any(Pageable.class)
                );

        assertNotNull(response);
        assertEquals(1, response.getRecordsTotal());
        assertEquals(1, response.getRecordsFiltered());

        ContactDto actual = (ContactDto) response.getData().get(0);
        assertEquals(expected.getFirstName(),    actual.getFirstName());
        assertEquals(expected.getLastName(),     actual.getLastName());
        assertEquals(expected.getEmail(),        actual.getEmail());
        assertEquals(expected.getId(),           actual.getId());
        assertEquals(expected.getCountryCode(),  actual.getCountryCode());
        assertEquals(expected.getPhoneNumber(),  actual.getPhoneNumber());
    }


    @Test
    void test_getAllContactsData_withSearch() {
        // given
        int size       = 1;
        int page       = 1;
        String search  = "FirstName";
        String sortBy  = "id";
        boolean isAsc  = true;

        // the DTO we expect after mapping
        ContactDto expected = new ContactDto(
                "John",                           // firstName
                "Doe",                            // lastName
                "<EMAIL>",           // email
                42L,                              // id
                CountryCode.US,                   // countryCode
                1234567890L,                      // phoneNumber
                "Unregistered"                    // ticketStatus
        );

        // mimic the native-query row order
        Object[] row = new Object[] {
                expected.getFirstName(),
                expected.getLastName(),
                expected.getEmail(),
                expected.getId(),
                expected.getCountryCode(),    // this is "US"
                expected.getPhoneNumber(),
                expected.getTicketStatus()
        };

        Pageable pageable = PageRequest.of(
                page - 1,
                size,
                Sort.by(isAsc ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)
        );
        Page<Object[]> rawPage = new PageImpl<>(
                Collections.singletonList(row),
                pageable,
                1  // total elements
        );

        when(contactsRepository.findContactsByEventIdAndContactsListId(
                eq(event.getEventId()),
                eq(contactsListId),
                eq(search),
                any(Pageable.class))
        ).thenReturn(rawPage);

        // when
        DataTableResponse response = contactServiceImpl.getAllContactsDataByContactsListId(
                event,
                null,
                contactsListId,
                new PageSizeSearchObj(size, page, search, sortBy,
                        isAsc ? Sort.Direction.ASC : Sort.Direction.DESC)
        );

        // then
        verify(contactsRepository, times(1)).findContactsByEventIdAndContactsListId(
                eq(event.getEventId()),
                eq(contactsListId),
                eq(search),
                any(Pageable.class)
        );

        assertNotNull(response);
        assertEquals(1, response.getRecordsTotal());
        assertEquals(1, response.getRecordsFiltered());

        ContactDto actual = (ContactDto) response.getData().get(0);
        assertEquals(expected.getFirstName(),    actual.getFirstName());
        assertEquals(expected.getLastName(),     actual.getLastName());
        assertEquals(expected.getEmail(),        actual.getEmail());
        assertEquals(expected.getId(),           actual.getId());
        assertEquals(expected.getCountryCode(),  actual.getCountryCode());
        assertEquals(expected.getPhoneNumber(),  actual.getPhoneNumber());
        assertEquals(expected.getTicketStatus(), actual.getTicketStatus());
    }


    @Test
    void test_Validate_withEmailAndEmailRegisteredTrueAndThrowExceptionCONTACT_ALREADY_ADDED() {
        //setup
        contactDto.setEmail(email);

        //mock
        when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.of(contacts));

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> contactServiceImpl.validate(contactDto, event));
        assertEquals(CONTACT_ALREADY_ADDED.getDeveloperMessage(), exception.getMessage());

        //Assertion
        verify(contactsRepository, times(1)).findByEmailAndEventId(anyString(), anyLong());
    }

    @Test
    void test_Validate_withEmailAndEmailRegisteredFalse() {
        //setup
        contactDto.setEmail(email);

        //mock
        when(contactsRepository.findByEmailAndEventId(anyString(), anyLong())).thenReturn(Optional.empty());

        //Execution
        contactServiceImpl.validate(contactDto, event);

        //Assertion
        verify(contactsRepository, times(1)).findByEmailAndEventId(anyString(), anyLong());
    }

    @Test
    void test_Validate_withEmailAndEmailEmptyRegisteredTrue() {
        //setup
        contactDto.setEmail("");

        //Execution
        contactServiceImpl.validate(contactDto, event);
    }
}