package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.EmbedWidgetSettings;
import com.accelevents.domain.Event;
import com.accelevents.domain.RecurringEvents;
import com.accelevents.domain.enums.WidgetType;
import com.accelevents.dto.EmbedWidgetSettingsDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EmbedWidgetSettingsRepository;
import com.accelevents.utils.CustomHashMap;
import com.google.gson.JsonSyntaxException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EmbedWidgetSettingServiceImplTest {

    @Spy
    @InjectMocks
    private  EmbedWidgetSettingServiceImpl embedWidgetSettingServiceImpl;

    @Mock
    private EmbedWidgetSettingsRepository embedWidgetSettingsRepository;

    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    private EmbedWidgetSettingsDto embedWidgetSettingsDto;
    private EmbedWidgetSettings embedWidgetSettings;
    private Event event;
    private RecurringEvents recurringEvents;

    private final Long id = 1L;
	private final String buyTicketButtonJsonString = "{\"buyTicketsButton\":\"buyTicketsButton\"}";
    private final String calenderJsonString = "{\"Calender\":\"Calender\"}";
    private final String countDownJsonString = "{\"CountDown\":\"CountDown\"}";
    private final String ticketingWidgetJsonString = "{\"Ticketing Widget\":\"Ticketing Widget\"}";
    private final String donationJsonString = "{\"Donation\":\"Donation\"}";
    private final String singlePageRegistrationString = "{\"singlePageRegistration\":\"singlePageRegistration\"}";
    private final String agendaSettingString = "{\"agendaSetting\":\"agendaSetting\"}";
    private final String speakerSettingString = "{\"speakerSetting\":\"speakerSetting\"}";
    private final Long eventId = 1L;
    private final Long organizerId = 1L;
	private CustomHashMap<String, Object> buyTicketButtonMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> calenderMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> countDownMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> ticketingWidgetMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> donationMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> singlePageRegistrationMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> agendaSettingMap = new CustomHashMap<>();
    private CustomHashMap<String, Object> speakerSettingMap = new CustomHashMap<>();
    private Long recurringEventId = 1L;



    @BeforeEach
    public void setUp() throws Exception {

        buyTicketButtonMap.put("buyTicketsButton", "buyTicketsButton");
        calenderMap.put("Calender", "Calender");
        countDownMap.put("CountDown","CountDown");
        ticketingWidgetMap.put("Ticketing Widget","Ticketing Widget");
        donationMap.put("Donation", "Donation");
        singlePageRegistrationMap.put("singlePageRegistration","singlePageRegistration");

        embedWidgetSettingsDto = new EmbedWidgetSettingsDto();
        embedWidgetSettingsDto.setId(id);
		String eventPage = "Event page";
		embedWidgetSettingsDto.setEventpage(eventPage);
		String organizerPage = "Organizer Page";
		embedWidgetSettingsDto.setOrganizerPage(organizerPage);
        embedWidgetSettingsDto.setBuyTicketsButton(buyTicketButtonMap);
        embedWidgetSettingsDto.setCalender(calenderMap);
        embedWidgetSettingsDto.setCountdown(countDownMap);
        embedWidgetSettingsDto.setDonation(donationMap);
        embedWidgetSettingsDto.setTicketingWidget(ticketingWidgetMap);
        embedWidgetSettingsDto.setSinglePageRegistration(singlePageRegistrationMap);
        embedWidgetSettingsDto.setAgendaSetting(agendaSettingMap);
        embedWidgetSettingsDto.setSpeakerSetting(speakerSettingMap);
        embedWidgetSettings = new EmbedWidgetSettings();
        embedWidgetSettings.setId(id);
        embedWidgetSettings.setBuyTicketsButton(buyTicketButtonJsonString);
        embedWidgetSettings.setCalender(calenderJsonString);
        embedWidgetSettings.setCountdown(countDownJsonString);
        embedWidgetSettings.setDonation(donationJsonString);
        embedWidgetSettings.setTicketingWidget(ticketingWidgetJsonString);
        embedWidgetSettings.setSinglePageRegistration(singlePageRegistrationString);
        embedWidgetSettings.setAgendaSetting(agendaSettingString);
        embedWidgetSettings.setSpeakerSetting(speakerSettingString);
        embedWidgetSettings.setEventPage(eventPage);
        embedWidgetSettings.setOrganizerPage(organizerPage);
        embedWidgetSettings.setEventId(eventId);
        embedWidgetSettings.setOrganizerId(organizerId);

        event = new Event();
        event.setEventId(eventId);

        recurringEvents = new RecurringEvents();

    }


    @Test
    public void test_save_throwException_WIDGET_SETTING_TYPE_NOT_EXISTS() {

        //setup
        embedWidgetSettingsDto.setId(id);
        embedWidgetSettingsDto.setType(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId));
        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.WIDGET_SETTING_TYPE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_save_throwException_ORGANIZER_OR_EVENT_ID_NOT_EXIST() {

        //setup
        embedWidgetSettingsDto.setId(null);
        embedWidgetSettingsDto.setRecurringEventId(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId));
        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.ORGANIZER_OR_EVENT_ID_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_save_throwException_WIDGET_SETTING_TYPE_NOT_EXISTS1() {

        //setup
        embedWidgetSettingsDto.setId(1L);
        embedWidgetSettingsDto.setType(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId));
        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.WIDGET_SETTING_TYPE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_save_success_with_widgetType_EVENT_recurringEventIdNotNull() {

        //setup
        embedWidgetSettingsDto.setType(WidgetType.EVENT.name());
        recurringEventId = 1L;
        embedWidgetSettingsDto.setRecurringEventId(recurringEventId);
        event.setEventId(eventId);
        recurringEvents.setId(recurringEventId);
        recurringEvents.setEventId(event);
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).save(embedWidgetSettingsArgumentCaptor.capture());

        EmbedWidgetSettings actualData = embedWidgetSettingsArgumentCaptor.getValue();
        assertEquals(actualData.getEventId(), embedWidgetSettingsDto.getId());
        assertEquals(actualData.getEventPage(), embedWidgetSettingsDto.getEventpage());
        assertEquals(actualData.getOrganizerPage(), embedWidgetSettingsDto.getOrganizerPage());
        assertEquals(actualData.getBuyTicketsButton(), buyTicketButtonJsonString);
        assertEquals(actualData.getCalender(), calenderJsonString);
        assertEquals(actualData.getCountdown(), countDownJsonString);
        assertEquals(actualData.getDonation(), donationJsonString);
        assertEquals(actualData.getTicketingWidget(), ticketingWidgetJsonString);
        assertEquals(actualData.getSinglePageRegistration(), singlePageRegistrationString);
    }

    @Test
    public void test_save_success_with_widgetType_EVENT_recurringEventIdNull() {

        //setup
        embedWidgetSettingsDto.setType(WidgetType.EVENT.name());
        recurringEventId = null;
        embedWidgetSettingsDto.setRecurringEventId(null);
        event.setEventId(eventId);
        recurringEvents.setEventId(event);
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).save(embedWidgetSettingsArgumentCaptor.capture());

        EmbedWidgetSettings actualData = embedWidgetSettingsArgumentCaptor.getValue();
        assertEquals(actualData.getEventId(), embedWidgetSettingsDto.getId());
        assertEquals(actualData.getEventPage(), embedWidgetSettingsDto.getEventpage());
        assertEquals(actualData.getOrganizerPage(), embedWidgetSettingsDto.getOrganizerPage());
        assertEquals(actualData.getBuyTicketsButton(), buyTicketButtonJsonString);
        assertEquals(actualData.getCalender(), calenderJsonString);
        assertEquals(actualData.getCountdown(), countDownJsonString);
        assertEquals(actualData.getDonation(), donationJsonString);
        assertEquals(actualData.getTicketingWidget(), ticketingWidgetJsonString);
        assertEquals(actualData.getSinglePageRegistration(), singlePageRegistrationString);
    }

    @Test
    public void test_save_success_with_widgetType_OrganizerName() {

        //setup
        embedWidgetSettingsDto.setType(WidgetType.ORGANIZER.name());
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).save(embedWidgetSettingsArgumentCaptor.capture());

        EmbedWidgetSettings actualData = embedWidgetSettingsArgumentCaptor.getValue();
        assertEquals(actualData.getOrganizerId(), embedWidgetSettingsDto.getId());
        assertEquals(actualData.getEventPage(), embedWidgetSettingsDto.getEventpage());
        assertEquals(actualData.getOrganizerPage(), embedWidgetSettingsDto.getOrganizerPage());
        assertEquals(actualData.getBuyTicketsButton(), buyTicketButtonJsonString);
        assertEquals(actualData.getCalender(), calenderJsonString);
        assertEquals(actualData.getCountdown(), countDownJsonString);
        assertEquals(actualData.getDonation(), donationJsonString);
        assertEquals(actualData.getTicketingWidget(), ticketingWidgetJsonString);
        assertEquals(actualData.getSinglePageRegistration(), singlePageRegistrationString);
    }

    @Test
    public void test_save_throwException_WIDGET_SETTING_TYPE_NOT_EXISTS2() {

        //setup
        String widgetType = "Other";
        embedWidgetSettingsDto.setType(widgetType);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId));

        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.WIDGET_SETTING_TYPE_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_save_throwException_WIDGET_SETTING_ALREADY_EXISTS() {

        //setup
        embedWidgetSettingsDto.setType("Event");
        event.setEventId(eventId);

        when(embedWidgetSettingsRepository.checkEmbedWidgetSettingExistsByRecurringEventIdAndEventId(any(), any(), anyBoolean())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.saveEmbedWidgetSettings(embedWidgetSettingsDto, event, recurringEventId));

        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.WIDGET_SETTING_ALREADY_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_getById_success() {

        //mock
		Long widgetSettingId = 1L;
		when(embedWidgetSettingsRepository.getById(widgetSettingId)).thenReturn(embedWidgetSettings);

        //Execution
        EmbedWidgetSettings embedgeWidgetData = embedWidgetSettingServiceImpl.getById(widgetSettingId);

        assertEquals(embedgeWidgetData.getEventId(), embedWidgetSettings.getEventId());
        assertEquals(embedgeWidgetData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedgeWidgetData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedgeWidgetData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedgeWidgetData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedgeWidgetData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedgeWidgetData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedgeWidgetData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_getByEventId_success() {

        //mock
        when(embedWidgetSettingsRepository.getByEventIdAndRecurringEventIdNull(eventId)).thenReturn(embedWidgetSettings);

        //Execution
        EmbedWidgetSettings embedgeWidgetData = embedWidgetSettingServiceImpl.getByEventIdAndRecurringEventIdNull(eventId);

        assertEquals(embedgeWidgetData.getEventId(), embedWidgetSettings.getEventId());
        assertEquals(embedgeWidgetData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedgeWidgetData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedgeWidgetData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedgeWidgetData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedgeWidgetData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedgeWidgetData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedgeWidgetData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_getByOrganizerId_success() {

        //mock
        when(embedWidgetSettingsRepository.getByOrganizerId(organizerId)).thenReturn(embedWidgetSettings);

        //Execution
        EmbedWidgetSettings embedgeWidgetData = embedWidgetSettingServiceImpl.getByOrganizerId(organizerId);

        assertEquals(embedgeWidgetData, embedWidgetSettings);
        assertEquals(embedgeWidgetData.getEventId(), embedWidgetSettings.getEventId());
        assertEquals(embedgeWidgetData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedgeWidgetData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedgeWidgetData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedgeWidgetData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedgeWidgetData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedgeWidgetData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedgeWidgetData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_delete_success() {

        //setup

        when(embedWidgetSettingsRepository.getById(any())).thenReturn(embedWidgetSettings);
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.deleteWidgetSettingsById(event,embedWidgetSettings.getId());

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).delete(embedWidgetSettingsArgumentCaptor.capture());
    }

    @Test
    public void test_updateEmbedWidgetSettings_success_widgetTypeEvent() {

        //setup
        embedWidgetSettingsDto.setType(WidgetType.EVENT.name());
        embedWidgetSettings.setEventId(id);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getById(any());
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.updateEmbedWidgetSettings(event,id, embedWidgetSettingsDto, recurringEventId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).save(embedWidgetSettingsArgumentCaptor.capture());

        EmbedWidgetSettings embedgeWidgetData = embedWidgetSettingsArgumentCaptor.getValue();
        assertEquals(embedgeWidgetData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedgeWidgetData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedgeWidgetData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedgeWidgetData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedgeWidgetData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedgeWidgetData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedgeWidgetData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedgeWidgetData.getEventId(), embedWidgetSettings.getEventId());
    }

    @Test
    public void test_updateEmbedWidgetSettings_success_widgetTypeOrganizer() {

        //setup
        embedWidgetSettingsDto.setType(WidgetType.ORGANIZER.name());
        embedWidgetSettings.setOrganizerId(id);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getById(any());
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.updateEmbedWidgetSettings(event,id, embedWidgetSettingsDto, recurringEventId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).save(embedWidgetSettingsArgumentCaptor.capture());

        EmbedWidgetSettings embedgeWidgetData = embedWidgetSettingsArgumentCaptor.getValue();
        assertEquals(embedgeWidgetData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedgeWidgetData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedgeWidgetData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedgeWidgetData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedgeWidgetData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedgeWidgetData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedgeWidgetData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedgeWidgetData.getOrganizerId(), embedWidgetSettings.getOrganizerId());
    }

    @Test
    public void test_getEmbedWidgetSettingsDto_success_widgetType_EVENT_recurringEventIdNull() {

        //setup
        String widgetType = WidgetType.EVENT.name();

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getEmbedWidgetSettings(any(), any(), any());

        //Execution
        EmbedWidgetSettingsDto embedWidgetSettingsDtoData = embedWidgetSettingServiceImpl.getEmbedWidgetSettingsDto(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsDtoData.getEventpage(), embedWidgetSettings.getEventPage());
        assertEquals(embedWidgetSettingsDtoData.getCalender(), calenderMap);
        assertEquals(embedWidgetSettingsDtoData.getDonation(), donationMap);
        assertEquals(embedWidgetSettingsDtoData.getCountdown(), countDownMap);
        assertEquals(embedWidgetSettingsDtoData.getBuyTicketsButton(), buyTicketButtonMap);
        assertEquals(embedWidgetSettingsDtoData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedWidgetSettingsDtoData.getSinglePageRegistration(), singlePageRegistrationMap);
    }

    @Test
    public void test_getEmbedWidgetSettingsDto_success_widgetType_EVENT_recurringEventIdNotNull() {

        //setup
        String widgetType = WidgetType.EVENT.name();
        recurringEventId = 1L;
        embedWidgetSettings.setRecurringEventId(recurringEventId);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getEmbedWidgetSettings(any(), any(), any());

        //Execution
        EmbedWidgetSettingsDto embedWidgetSettingsDtoData = embedWidgetSettingServiceImpl.getEmbedWidgetSettingsDto(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsDtoData.getEventpage(), embedWidgetSettings.getEventPage());
        assertEquals(embedWidgetSettingsDtoData.getCalender(), calenderMap);
        assertEquals(embedWidgetSettingsDtoData.getDonation(), donationMap);
        assertEquals(embedWidgetSettingsDtoData.getCountdown(), countDownMap);
        assertEquals(embedWidgetSettingsDtoData.getBuyTicketsButton(), buyTicketButtonMap);
        assertEquals(embedWidgetSettingsDtoData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedWidgetSettingsDtoData.getSinglePageRegistration(), singlePageRegistrationMap);
    }

    @Test
    public void test_getEmbedWidgetSettingsDto_success_widgetType_ORGANIZER() {

        //setup
        String widgetType = WidgetType.ORGANIZER.name();
        recurringEventId = null;
        embedWidgetSettings.setRecurringEventId(null);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getEmbedWidgetSettings(any(), any(), any());

        //Execution
        EmbedWidgetSettingsDto embedWidgetSettingsDtoData = embedWidgetSettingServiceImpl.getEmbedWidgetSettingsDto(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsDtoData.getEventpage(), embedWidgetSettings.getEventPage());
        assertEquals(embedWidgetSettingsDtoData.getCalender(), calenderMap);
        assertEquals(embedWidgetSettingsDtoData.getDonation(), donationMap);
        assertEquals(embedWidgetSettingsDtoData.getCountdown(), countDownMap);
        assertEquals(embedWidgetSettingsDtoData.getBuyTicketsButton(), buyTicketButtonMap);
        assertEquals(embedWidgetSettingsDtoData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedWidgetSettingsDtoData.getSinglePageRegistration(), singlePageRegistrationMap);
    }

    @Test
    public void test_getEmbedWidgetSettingsDto_success_widgetType_OTHER() {

        //setup
        String widgetType = "Other";
        recurringEventId = null;
        embedWidgetSettings.setRecurringEventId(null);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getEmbedWidgetSettings(any(), any(), any());

        //Execution
        EmbedWidgetSettingsDto embedWidgetSettingsDtoData = embedWidgetSettingServiceImpl.getEmbedWidgetSettingsDto(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsDtoData.getEventpage(), embedWidgetSettings.getEventPage());
        assertEquals(embedWidgetSettingsDtoData.getCalender(), calenderMap);
        assertEquals(embedWidgetSettingsDtoData.getDonation(), donationMap);
        assertEquals(embedWidgetSettingsDtoData.getCountdown(), countDownMap);
        assertEquals(embedWidgetSettingsDtoData.getBuyTicketsButton(), buyTicketButtonMap);
        assertEquals(embedWidgetSettingsDtoData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
        assertEquals(embedWidgetSettingsDtoData.getSinglePageRegistration(), singlePageRegistrationMap);
    }

    @Test
    public void test_getEmbedWidgetSettings_success_with_id_and_widgetType_null() {

      doReturn(null).when(embedWidgetSettingServiceImpl).getEmbedWidgetSettings(any(), any(), any());

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(null, null, recurringEventId);

        assertNull(embedWidgetSettingsData);
    }

    @Test
    public void test_getEmbedWidgetSettings_with_id_null() {

        //setup
        String widgetType = WidgetType.ORGANIZER.name();

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(null, widgetType, recurringEventId);

        assertNull(embedWidgetSettingsData);
    }

    @Test
    public void test_getEmbedWidgetSettings_with_widgetType_null() {

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(id, null, recurringEventId);

        assertNull(embedWidgetSettingsData);
    }

    @Test
    public void test_getEmbedWidgetSettings_success_with_WidgetType_ORGANIZER() {

        //setup
        String widgetType = WidgetType.ORGANIZER.name();

        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getByOrganizerId(any());

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsData.getEventPage(), embedWidgetSettings.getEventPage());
        assertEquals(embedWidgetSettingsData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedWidgetSettingsData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedWidgetSettingsData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedWidgetSettingsData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedWidgetSettingsData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedWidgetSettingsData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_getEmbedWidgetSettings_success_with_WidgetType_other() {

        //setup
        String widgetType = "Other";

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(id, widgetType, recurringEventId);

        assertNull(embedWidgetSettingsData);
    }

    @Test
    public void test_getEmbedWidgetSettings_success_with_WidgetType_EVENT_recurringIdNull() {

        //setup
        String widgetType = WidgetType.EVENT.name();
        recurringEventId = null;
        embedWidgetSettings.setRecurringEventId(null);

        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getByEventIdAndRecurringEventIdNull(any());

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(id, widgetType, recurringEventId);

        assertEquals(embedWidgetSettingsData, embedWidgetSettings);
        assertEquals(embedWidgetSettingsData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedWidgetSettingsData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedWidgetSettingsData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedWidgetSettingsData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedWidgetSettingsData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedWidgetSettingsData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_getEmbedWidgetSettings_success_with_WidgetType_EVENT_recurringIdNotNull() {

        //setup
        recurringEventId = 1L;
        embedWidgetSettings.setRecurringEventId(recurringEventId);

        when(embedWidgetSettingsRepository.getByRecurringEventIdAndEventId(anyLong(), anyLong())).thenReturn(embedWidgetSettings);

        //Execution
        EmbedWidgetSettings embedWidgetSettingsData = embedWidgetSettingServiceImpl.getEmbedWidgetSettings(id, WidgetType.EVENT.name(), recurringEventId);

        assertEquals(embedWidgetSettingsData, embedWidgetSettings);
        assertEquals(embedWidgetSettingsData.getRecurringEventId(), recurringEventId);
        assertEquals(embedWidgetSettingsData.getTicketingWidget(), embedWidgetSettings.getTicketingWidget());
        assertEquals(embedWidgetSettingsData.getCalender(), embedWidgetSettings.getCalender());
        assertEquals(embedWidgetSettingsData.getDonation(), embedWidgetSettings.getDonation());
        assertEquals(embedWidgetSettingsData.getCountdown(), embedWidgetSettings.getCountdown());
        assertEquals(embedWidgetSettingsData.getBuyTicketsButton(), embedWidgetSettings.getBuyTicketsButton());
        assertEquals(embedWidgetSettingsData.getOrganizerPage(), embedWidgetSettings.getOrganizerPage());
    }

    @Test
    public void test_deleteWidgetSettingByWidgetId_success() {

        //setup
        Long widgetSettingId = 1L;

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getById(any());
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.deleteWidgetSettingsById(event,widgetSettingId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).delete(embedWidgetSettingsArgumentCaptor.capture());
    }

    @Test
    public void test_deleteWidgetSettingsByIdAndType_throwException_WIDGET_SETTING_NOT_FOUND() {

        //setup
        Long widgetSettingId = 1L;
        String widgetType = null;

        //mock
        Mockito.doReturn(null).when(embedWidgetSettingServiceImpl).getById(any());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.deleteWidgetSettingsById(event,widgetSettingId));

        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.WIDGET_SETTING_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_deleteWidgetSettingsById() {

        //setup
        Long widgetSettingId = 1L;
        embedWidgetSettings.setRecurringEventId(null);

        //mock
        Mockito.doReturn(embedWidgetSettings).when(embedWidgetSettingServiceImpl).getById(any());
        Mockito.doNothing().when(clearAPIGatewayCache).clearAPIGwEmbedWidgetsSetting(any(),any(),anyLong());
        //Execution
        embedWidgetSettingServiceImpl.deleteWidgetSettingsById(event,widgetSettingId);

        ArgumentCaptor<EmbedWidgetSettings> embedWidgetSettingsArgumentCaptor = ArgumentCaptor.forClass(EmbedWidgetSettings.class);
        verify(embedWidgetSettingsRepository, times(1)).delete(embedWidgetSettingsArgumentCaptor.capture());
    }

    @Test
    public void test_convertMapToJsonString_throw_Exception_NOT_VALID_JSON_FORMAT() {

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> embedWidgetSettingServiceImpl.convertMapToJsonString(null));

        assertEquals(NotAcceptableException.EmbedWidgeSettingExceptionMsg.NOT_VALID_JSON_FORMAT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_convertJsonStringToMap_throw_Exception_JsonSyntaxException() {

        //Execution
        assertThrows(JsonSyntaxException.class,
                () -> embedWidgetSettingServiceImpl.convertJsonStringToMap("Json String"));
    }

    @Test
    public void test_checkWidgetSettingExists_idAndWidgetTypeBlank() {

        //Execution
        boolean widgetSettingExists = embedWidgetSettingServiceImpl.checkWidgetSettingExists(null, "", null);
        assertFalse(widgetSettingExists);
    }
}