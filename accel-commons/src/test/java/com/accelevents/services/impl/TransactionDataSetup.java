package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.TransactionFeeConditionalLogic;
import com.accelevents.domain.WhiteLabel;

import java.util.ArrayList;
import java.util.List;

import static com.accelevents.utils.FeeConstants.GREATER_THAN_EQUAL_TO;
import static com.accelevents.utils.FeeConstants.LESS_THAN;

/**
 * Created by VK on 2/6/2018.
 */
public class TransactionDataSetup {

    private static double thresholdPrice = new Double(40);
    private static double aeFlat1 = new Double(1);
    private static double aeFlatPercentage1 = new Double(1.9);
    private static double aeFlat2 = new Double(1);
    private static double aeFlatPercentage2 = new Double(2.9);

    private static double wlFlat1 = new Double(1.10);
    private static double wlFlatPercentage1 = new Double(0);
    private static double wlFlat2 = new Double(1);
    private static double wlFlatPercentage2 = new Double(0.5);


    public static List<TransactionFeeConditionalLogic> getDefaultConfigs(WhiteLabel whiteLabel){
        return getDefaultConfigs(null,whiteLabel);
    }

    public static List<TransactionFeeConditionalLogic> getDefaultConfigs(Event event, WhiteLabel whiteLabel){

        TransactionFeeConditionalLogic entity1 = new TransactionFeeConditionalLogic();
        entity1.setWhiteLabel(whiteLabel);
        entity1.setEvent(event);
        entity1.setAeFeeFlat(aeFlat1);
        entity1.setAeFeePercentage(aeFlatPercentage1);
        entity1.setWlAFeeFlat(wlFlat1);
        entity1.setWlAFeePercentage(wlFlatPercentage1);
        entity1.setFromTicketPriceThreshold(thresholdPrice);
        entity1.setOperator(LESS_THAN);

        TransactionFeeConditionalLogic entity2 = new TransactionFeeConditionalLogic();
        entity2.setWhiteLabel(whiteLabel);
        entity2.setEvent(event);
        entity2.setAeFeeFlat(aeFlat2);
        entity2.setAeFeePercentage(aeFlatPercentage2);
        entity2.setWlAFeeFlat(wlFlat2);
        entity2.setWlAFeePercentage(wlFlatPercentage2);
        entity2.setFromTicketPriceThreshold(thresholdPrice);
        entity2.setOperator(GREATER_THAN_EQUAL_TO);

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogics = new ArrayList<>();
        transactionFeeConditionalLogics.add(entity1);
        transactionFeeConditionalLogics.add(entity2);

        return transactionFeeConditionalLogics;

    }
}
