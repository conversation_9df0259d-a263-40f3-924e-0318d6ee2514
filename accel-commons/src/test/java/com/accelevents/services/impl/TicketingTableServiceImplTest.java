package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingTable;
import com.accelevents.repositories.TicketingTableRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingTableServiceImplTest {

    @Spy
    @InjectMocks TicketingTableServiceImpl ticketingTableServiceImpl;

    @Mock
    private TicketingTableRepository ticketingTableRepository;

    private Event event;
	private TicketingTable ticketingTable;

    long id = 1L;
    long tableNumberSequence = 1L;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
		Ticketing ticketing = EventDataUtil.getTicketing(event);

        ticketingTable = new TicketingTable();
        ticketingTable.setEventId(event);
        ticketingTable.setId(id);
        ticketingTable.setTableNoSequence(tableNumberSequence);
    }

    @Test
    void test_save_success() {

        //Execution
        ticketingTableServiceImpl.save(ticketingTable);

        ArgumentCaptor<TicketingTable> ticketingTableArgumentCaptor = ArgumentCaptor.forClass(TicketingTable.class);
        verify(ticketingTableRepository,times(1)).save(ticketingTableArgumentCaptor.capture());

        TicketingTable actual = ticketingTableArgumentCaptor.getValue();
        assertNotNull(actual);
    }

    @Test
    void test_findOneByEventIdDesc_success() {

        //mock
        when(ticketingTableRepository.findTop1ByEventIdOrderByIdDesc(any())).thenReturn(ticketingTable);

        //Execution
        TicketingTable ticket = ticketingTableServiceImpl.findOneByEventIdDesc(event);
        assertNotNull(ticket);
    }
}