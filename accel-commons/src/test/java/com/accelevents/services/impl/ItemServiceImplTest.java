package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.AuctionAllItemDto;
import com.accelevents.auction.dto.AuctionItemDto;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.ItemCategoryDetails;
import com.accelevents.dto.ItemImageDto;
import com.accelevents.dto.ItemParticipants;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.fundaneed.dto.FundANeedAllItemDto;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.perfomance.dto.ItemNameCodeDto;
import com.accelevents.raffle.dto.RaffleAllItemDto;
import com.accelevents.repositories.AuctionBidRepository;
import com.accelevents.repositories.ItemRepository;
import com.accelevents.repositories.PledgeRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.itextpdf.text.BadElementException;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static com.accelevents.domain.enums.AccountActivatedTriggerStatus.INITIAL;
import static com.accelevents.utils.Constants.*;
import static java.util.Optional.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ItemServiceImplTest {

	@Spy
	@InjectMocks
	private ItemServiceImpl itemServiceImpl;
	@Mock
	private JoinPaymentItemService joinPaymentItemService;
	@Mock
	private ItemRepository itemRepository;
	@Mock
	private ItemImgLocationsService itemImgLocationsService;
    @Mock
    ROEventService roEventService;
	@Mock
    AuctionBidRepository bidRepository;
	@Mock
    TwilioTextMessagePrepareService twilioTextMessagePrepareService;
	@Mock
    AuctionBidService auctionBidService;

	@Mock
    PledgeRepository pledgeRepository;

	@Mock
    EventDesignDetailService eventDesignDetailService;

	@Mock
    ImageConfiguration imageConfiguration;

	@Mock
    private AuctionServiceImpl auctionService;

	@Mock
	private FavoriteItemService favoriteItemService;

	@Mock
	private ItemCategoryService itemCategoryService;

    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

	private Event event;
	private User user;
	private Item item;


	private String itemCode = "itemCode";
	public final double sequence = 1000;

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);
		event = EventDataUtil.getEvent();
		user = EventDataUtil.getUser();
		item = new Item();
	}

	@Test
	void testIsBuyItNowItemAndHasBeenPaidForNonAuctionItem(){
		Item item = new Item();
		item.setModuleType(ModuleType.RAFFLE);
		boolean result = itemServiceImpl.isBuyItNowItemAndHasBeenPaid(item);
		assertFalse(result);
	}

	@Test
	void testAddDuplicateitemWithNextPositionNotNull(){

		long itemId = 1;
		Item item = new Item();
		item.setId( itemId);
		item.setName( "item1" );
		item.setCode( "TWE" );
		item.setModuleType(ModuleType.AUCTION);
		item.setPosition( 1000 );
		item.setBuyItNowPrice(0);

		long itemId2 = 2;
		Item item2 = new Item();
		item2.setId( itemId2);
		item.setName( "item2" );
		item.setCode( "TWR" );
		item2.setModuleType(ModuleType.AUCTION);
		item2.setBuyItNowPrice(0);
		item2.setPosition( 2000 );

		ItemImgLocations itemImgLocations1 = new ItemImgLocations(  );
		itemImgLocations1.setItem( item );

		List<ItemImgLocations> clonedItemImgLocations = new ArrayList<>(  );
		clonedItemImgLocations.add(itemImgLocations1  );


		Event event = new Event("test event",true,true,true,
				true, INITIAL);
		event.setEventId( 994 );

		when( itemRepository.findItemById(  itemId) ).thenReturn( of( item ) );

		when( itemImgLocationsService.findByItem( any() ) ).thenReturn( clonedItemImgLocations );
		when( roEventService.getEventById( event.getEventId() ) ).thenReturn(  event  );
		when( itemRepository.findFirstByModuleIdAndModuleTypeAndPositionGreaterThan( anyLong(),any(),anyDouble()  )).thenReturn( of( item2 ) );

		Item clonedItem = itemServiceImpl.addDuplicateItem( itemId,event,"@@A" );

		assertEquals( "@@A",clonedItem.getCode() );
		assertEquals( 0,clonedItem.getStartingBid(),0.01 );
		assertEquals( clonedItem.getName(),item.getName() );
		assertEquals( clonedItem.getPosition(),(item.getPosition()+item2.getPosition())/2 ,0);
		assertEquals( clonedItem.getBuyItNowPrice(), item.getBuyItNowPrice(), 0.01 );
		assertEquals( clonedItem.getDescription(), item.getDescription() );
		assertEquals( clonedItem.getItemCategory(), item.getItemCategory() );

	}

	@Test
	void testAddDuplicateitemWithNextPositionNull(){

		long itemId = 1;
		Item item = new Item();
		item.setId(itemId);
		item.setName( "item1" );
		item.setCode( "TWE" );
		item.setModuleType(ModuleType.AUCTION);
		item.setPosition( 1000 );
		item.setBuyItNowPrice(0);


		ItemImgLocations itemImgLocations = new ItemImgLocations(  );
		itemImgLocations.setItem( item );

		List<ItemImgLocations> clonedItemImgLocations = new ArrayList<>(  );
		clonedItemImgLocations.add(itemImgLocations  );


		Event event = new Event("test event",true,true,true,
				true, INITIAL);
		event.setEventId( 1000 );

		when( itemRepository.findItemById(  itemId) ).thenReturn( of( item ) );

		when( itemImgLocationsService.findByItem( any() ) ).thenReturn( clonedItemImgLocations );
		when( roEventService.getEventById( event.getEventId() ) ).thenReturn(  event  );
		when( itemRepository.findFirstByModuleIdAndModuleTypeAndPositionGreaterThan( anyLong(),any(),anyDouble()  )).thenReturn( empty());

		Item clonedItem = itemServiceImpl.addDuplicateItem( itemId,event,"@@A" );

		assertEquals( "@@A",clonedItem.getCode() );
		assertEquals( clonedItem.getPosition(),item.getPosition()+1000 ,0);
		assertEquals( 0,clonedItem.getStartingBid(),0.01);
		assertEquals( clonedItem.getName(),item.getName() );
		assertEquals( clonedItem.getBuyItNowPrice(), item.getBuyItNowPrice(),0.01 );
		assertEquals( clonedItem.getDescription(), item.getDescription() );
		assertEquals( clonedItem.getItemCategory(), item.getItemCategory() );

	}

	@Test
	void test_addDuplicateItem_throwExceptionItemNotFound(){

		//mock
		when(itemRepository.findItemById(1L)).thenReturn(empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> itemServiceImpl.addDuplicateItem( 1L,event,"@@A" ));

		//assert
        assertEquals(NotFoundException.ItemNotFound.ITEM_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
		verify(itemRepository).findItemById(1L);

	}


	@Test
	void testIsBuyItNowItemAndHasBeenPaidForBuyItNowEqZero(){
		Item item = new Item();
		item.setModuleType(ModuleType.AUCTION);
		item.setBuyItNowPrice(0);
		boolean result = itemServiceImpl.isBuyItNowItemAndHasBeenPaid(item);
		assertFalse(result);
	}

	@Test
	void testIsBuyItNowItemAndHasBeenPaidForCurrentItemPriseLessThanBuyItNowPrice(){
		Item item = new Item();
		item.setModuleType(ModuleType.AUCTION);
		item.setBuyItNowPrice(5);
		item.setCurrentBid(4d);
		boolean result = itemServiceImpl.isBuyItNowItemAndHasBeenPaid(item);
		assertFalse(result);
	}

	@Test
	void testIsBuyItNowItemAndHasBeenPaidForNonPaidItem(){
		Item item = new Item();
		item.setModuleType(ModuleType.AUCTION);
		item.setBuyItNowPrice(5);
		item.setCurrentBid(6d);

		doReturn(false).when(joinPaymentItemService).hasPayment(item);

		boolean result = itemServiceImpl.isBuyItNowItemAndHasBeenPaid(item);
		assertFalse(result);
	}

	@Test
	void testIsBuyItNowItemAndHasBeenPaidForPaidItem(){
		Item item = new Item();
		item.setModuleType(ModuleType.AUCTION);
		item.setBuyItNowPrice(5);
		item.setCurrentBid(6d);

		doReturn(true).when(joinPaymentItemService).hasPayment(item);

		boolean result = itemServiceImpl.isBuyItNowItemAndHasBeenPaid(item);
		assertTrue(result);
	}

	@Test
	void testGetBidIncrementWhenItemBidIncrementIsZero(){
		Item item = new Item();
		item.setBidIncrement(0d);
		Auction auction = new Auction();
		auction.setDefaultBidIncrement(10);

		double result = itemServiceImpl.getBidIncrement(item, auction);

		assertEquals(10, result,0.01);
	}

	@Test
	void testGetBidIncrementWhenItemBidIncrementIsNonZero(){
		Item item = new Item();
		item.setBidIncrement(5d);
		Auction auction = new Auction();
		auction.setDefaultBidIncrement(10);

		double result = itemServiceImpl.getBidIncrement(item, auction);

		assertEquals(5, result,0.01);
	}

	@Test
	void test_getActiveItems_searchStringEmptyAndCategoryFavorites(){

		//SetUp
		List<Long> favouriteItemListIds = new ArrayList<>();
		favouriteItemListIds.add(1L);

		item.setId(1L);
		item.setName("BOOK");

		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);


		//Mock Data
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION,FAVORITES)).thenReturn(empty());
		when(favoriteItemService.findFavoriteItemListByUserId(user)).thenReturn(favouriteItemListIds);
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemOrderByPositionDesc(
				1L, ModuleType.AUCTION,true,favouriteItemListIds, PageRequest.of(0, 10))).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(
				1,ModuleType.AUCTION,"",FAVORITES,10,0,user);

		//Verify
		assertEquals(expectedItemsList.getContent().get(0).getName(),pageImpl.getContent().get(0).getName());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION,FAVORITES);
		verify(favoriteItemService).findFavoriteItemListByUserId(user);
		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemOrderByPositionDesc(
				1L, ModuleType.AUCTION,true,favouriteItemListIds, PageRequest.of(0, 10));
	}

	@Test
	void test_getActiveItems_searchStringEmptyAndReturnEmptyCollectionList(){

		//Mock Data
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION,FAVORITES)).thenReturn(empty());
		when(favoriteItemService.findFavoriteItemListByUserId(user)).thenReturn(Collections.emptyList());

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"",FAVORITES,10,1,user);

		//Verify
		assertTrue(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION,FAVORITES);
		verify(favoriteItemService).findFavoriteItemListByUserId(user);
	}

	public static Object[] getItemCategory(){

		return new Object[]{
				new Object[]{empty()},
				new Object[]{of(new ItemCategory())},
		};
	}

	@ParameterizedTest
	@MethodSource("getItemCategory")
	void test_getActiveItems_searchStringEmptyAndItemCategoryPresentOrNonDbCategoriesContainCategory(Optional<ItemCategory> itemCategory){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, BUYITNOW)).thenReturn(itemCategory);
		doReturn(pageImpl).when(itemServiceImpl).getItemsForSearchWithCategory(anyLong(),any(),anyString(),any(),any());

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"",BUYITNOW,10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, BUYITNOW);
		verify(itemServiceImpl).getItemsForSearchWithCategory(anyLong(),any(),anyString(),any(),any());
	}

	@Test
	void test_getActiveItems_searchStringEmptyAndItemWithUserBid(){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, ITEMS_WITH_USER_BID)).thenReturn(empty());
		when(itemRepository.findAllItemByModuleIdAndActiveAndUserOrderByPositionDesc(anyLong(),anyBoolean(),any(),any())).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"",ITEMS_WITH_USER_BID,10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, ITEMS_WITH_USER_BID);
		verify(itemRepository).findAllItemByModuleIdAndActiveAndUserOrderByPositionDesc(anyLong(),anyBoolean(),any(),any());
	}

	public static Object[] getCategory(){

		return new Object[]{
				new Object[]{ITEMS_WITH_USER_BID},
				new Object[]{FAVORITES},
		};
	}

	@ParameterizedTest
	@MethodSource("getCategory")
	void test_getActiveItems_searchStringEmptyAndUserNull(String category){

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category)).thenReturn(empty());

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"",category,10,1,null);

		//Verify
		assertTrue(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category);
	}

	@Test
	void test_getActiveItems_searchStringEmptyAndCategoryBlank(){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveOrderByPosition(anyLong(),any(),anyBoolean(),any())).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"","",10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveOrderByPosition(anyLong(),any(),anyBoolean(),any());
	}

	@Test
	void test_getActiveItems_searchStringAndCategoryBlank(){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndCodeContainingOrderByPositionDesc(anyLong(),any(),anyBoolean(),anyString(),any())).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item","",10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveAndCodeContainingOrderByPositionDesc(anyLong(),any(),anyBoolean(),anyString(),any());
	}

	@ParameterizedTest
	@MethodSource("getItemCategory")
	void test_getActiveItems_searchStringAndItemCategoryPresentOrNonDbCategoriesContainCategory(Optional<ItemCategory> itemCategory){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, UNCATEGORIZED)).thenReturn(itemCategory);
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(anyLong(),isA(ModuleType.class),anyBoolean(),anyLong(),anyString(),
                isA(Pageable.class), nullable(Boolean.class),nullable(Integer.class),nullable(Double.class),nullable(Boolean.class))).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item",UNCATEGORIZED,10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, UNCATEGORIZED);
		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(anyLong(),isA(ModuleType.class),anyBoolean(),anyLong(),anyString(),
                isA(Pageable.class), nullable(Boolean.class),nullable(Integer.class),nullable(Double.class),nullable(Boolean.class));
	}

	public static Object[] getDiffCategory(){

		return new Object[]{
				new Object[]{LIVE},
				new Object[]{SILENT},
				new Object[]{NOBIDS},
				new Object[]{BUYITNOW},
		};
	}

	@ParameterizedTest
	@MethodSource("getDiffCategory")
	void test_getActiveItems_searchStringAndContainDifferentCategory(String category){

		//setup
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category)).thenReturn(empty());
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(anyLong(),isA(ModuleType.class),anyBoolean(),nullable(Long.class),anyString(),
                isA(Pageable.class), nullable(Boolean.class),nullable(Integer.class),nullable(Double.class),nullable(Boolean.class))).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item",category,10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category);
		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryAndCodeContainingOrderByPositionDesc(anyLong(),isA(ModuleType.class),anyBoolean(),nullable(Long.class),anyString(),
                isA(Pageable.class), nullable(Boolean.class),nullable(Integer.class),nullable(Double.class),nullable(Boolean.class));
	}

	@Test
	void test_getActiveItems_searchStringAndFavoriteCategoryFavouriteItemListIdsEmpty(){

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, FAVORITES)).thenReturn(empty());
		when(favoriteItemService.findFavoriteItemListByUserId(user)).thenReturn(Collections.emptyList());

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item",FAVORITES,10,1,user);

		//Verify
		assertTrue(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, FAVORITES);
		verify(favoriteItemService).findFavoriteItemListByUserId(user);
	}

	@Test
	void test_getActiveItems_searchStringAndFavoriteCategory(){

		//setup
		List<Long> favouriteItemListIds = new ArrayList<>();
		favouriteItemListIds.add(1L);

		List<Item> itemList = new ArrayList<>();
		itemList.add(item);
		Page<Item> pageImpl = new PageImpl<>(itemList);

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, FAVORITES)).thenReturn(empty());
		when(favoriteItemService.findFavoriteItemListByUserId(user)).thenReturn(favouriteItemListIds);
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemAndSearchStringOrderByPositionDesc(anyLong(),any(),anyString(),anyBoolean(),any(),any())).thenReturn(pageImpl);

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item",FAVORITES,10,1,user);

		//Verify
		assertFalse(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, FAVORITES);
		verify(favoriteItemService).findFavoriteItemListByUserId(user);
		verify(itemRepository).findAllItemByModuleIdAndModuleTypeAndActiveAndFavoriteItemAndSearchStringOrderByPositionDesc(anyLong(),any(),anyString(),anyBoolean(),any(),any());
	}

	@ParameterizedTest
	@MethodSource("getCategory")
	void test_getActiveItems_searchStringAndUserNull(String category){

		//Mock
		when(itemCategoryService.findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category)).thenReturn(empty());

		//Execute
		Page<Item> expectedItemsList = itemServiceImpl.getActiveItems(1,ModuleType.AUCTION,"item",category,10,1,null);

		//Verify
		assertTrue(expectedItemsList.getContent().isEmpty());

		verify(itemCategoryService).findByModuleIdAndModuleTypeAndName(1L, ModuleType.AUCTION, category);
	}

	@Test
	void test_getItemCodeAndStartingBidOfItemWithLowestPosition(){

		//SetUp
		Pageable pageable = PageRequest.of(0, 1);

		//Mock
		ItemInstructionDto itemInstructionDto1 = new ItemInstructionDto("AUC", 50);
		Page<ItemInstructionDto> itemInstructionDto = new PageImpl<>(Collections.singletonList(itemInstructionDto1));
		when(itemRepository.getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable)).thenReturn(itemInstructionDto);

		//Execute
		ItemInstructionDto expectedItem = itemServiceImpl.getItemCodeAndStartingBidOfItemWithLowestPosition(1L,ModuleType.AUCTION);

		//Verify
		assertEquals("AUC",expectedItem.getCode());

		verify(itemRepository).getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable);
	}

	@Test
	void test_getItemCodeAndStartingBidOfItemWithLowestPosition_itemInstructionDtoNull(){

		//SetUp
		Pageable pageable = PageRequest.of(0, 1);

		//Mock
		when(itemRepository.getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable)).thenReturn(null);

		//Execute
		ItemInstructionDto expectedItem = itemServiceImpl.getItemCodeAndStartingBidOfItemWithLowestPosition(1L,ModuleType.AUCTION);

		//Verify
		assertNull(expectedItem);

		verify(itemRepository).getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable);
	}

	@Test
	void test_getItemCodeAndStartingBidOfItemWithLowestPosition_itemInstructionDtoEmpty(){

		//SetUp
		Pageable pageable = PageRequest.of(0, 1);

		//Mock
		Page<ItemInstructionDto> itemInstructionDto = new PageImpl<>(Collections.emptyList());
		when(itemRepository.getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable)).thenReturn(itemInstructionDto);

		//Execute
		ItemInstructionDto expectedItem = itemServiceImpl.getItemCodeAndStartingBidOfItemWithLowestPosition(1L,ModuleType.AUCTION);

		//Verify
		assertNull(expectedItem);

		verify(itemRepository).getItemCodeAndStartingBidOfItemWithLowestPosition(1L, ModuleType.AUCTION,true,pageable);
	}

	private ItemImgLocations getItemImageLocation(Item item, String imgSrc, double position) {
		ItemImgLocations itemImgLocations = new ItemImgLocations();
		itemImgLocations.setItem(item);
		itemImgLocations.setImageLocations(imgSrc);
		itemImgLocations.setPosition(position);
		itemImgLocations.setId(RandomUtils.nextInt(1,100));

		return itemImgLocations;
	}

	private Item getItem(long id, String code) {
		Item item = new Item();
		item.setCode(code);
		item.setId(id);
		item.setPosition(RandomUtils.nextInt(1000,5000));
		return item;
	}

	@Test
	void test_findAllItemNameByModuleIdAndItemCodeOrderByPositionDesc(){

		//setup
		String itemCode = "CAU";
		String itemName = "my first causeAuction";
		ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto(itemName, itemCode);
		List<ItemNameCodeDto> itemNameCodeDtoList = new ArrayList<>();
		itemNameCodeDtoList.add(itemNameCodeDto);

		List<String> itemCodes = new ArrayList<>();
		itemCodes.add(itemCode);

		event.setAuctionId(1L);
		event.setRaffleId(1L);
		event.setCauseAuctionId(1L);

		//mock
		when(itemRepository.findAllItemNameByModuleIdAndItemCodeOrderByPositionDesc(any(), anyLong(), anyLong(), anyLong())).thenReturn(itemNameCodeDtoList);

		//Execution
		List<ItemNameCodeDto> itemNameCodeDtos = itemServiceImpl.findAllItemNameByModuleIdAndItemCodeOrderByPositionDesc(itemCodes, event.getAuctionId(), event.getRaffleId(), event.getCauseAuctionId());

		//Assertion
		assertEquals(itemNameCodeDtos.get(0).getItemName(), itemNameCodeDto.getItemName());
		assertEquals(itemNameCodeDtos.get(0).getItemCode(), itemNameCodeDto.getItemCode());

		verify(itemRepository).findAllItemNameByModuleIdAndItemCodeOrderByPositionDesc(any(), anyLong(), anyLong(), anyLong());
	}

	@Test
	void test_getItem_success(){

		//setup
		item.setCode(itemCode);
		List<Item> itemList = new ArrayList<>();
		itemList.add(item);

		//mock
		when(roEventService.getEventById(1L)).thenReturn(event);
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndCode(anyLong(),any(),anyString())).thenReturn(itemList);

		//Execution
		Optional<Item> itemOptional = itemServiceImpl.getItem(1L,itemCode);

		//assert
		itemOptional.ifPresent(value -> assertEquals(item, value));

		verify(roEventService).getEventById(1L);
	}

	@Test
	void test_getItem_ItemEmpty(){

		//setup
		item.setCode("item");

		List<Item> itemList = new ArrayList<>();
		itemList.add(item);

		//mock
		when(roEventService.getEventById(1L)).thenReturn(event);
		when(itemRepository.findAllItemByModuleIdAndModuleTypeAndCode(anyLong(),any(),anyString())).thenReturn(itemList);

		//Execution
		Optional<Item> itemOptional = itemServiceImpl.getItem(1L,itemCode);

		//assert
		assertEquals(itemOptional, empty());

		verify(roEventService).getEventById(1L);
	}

	@Test
	void test_update_success(){

		//Execution
		itemServiceImpl.update(event,item);

		//assert
		ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
		verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());

		Item itemData = itemArgumentCaptor.getValue();
		assertEquals(itemData,item);
	}

	public static Object[] getImgLocations(){

		return new Object[]{
				new Object[]{null},
				new Object[]{Collections.emptyList()},
		};
	}

	@ParameterizedTest
	@MethodSource("getImgLocations")
	void test_updateItemImage_imgLocationsNullOrEmpty(List<ItemImgLocations> imgLocations){

		//setup
		item.setImgLocations(imgLocations);

		//Execution
		itemServiceImpl.updateItemImage(item);

		assertTrue(true);
	}

	@Test
	void test_updateItemImage_success(){

		//setup
		List<ItemImgLocations> itemImgLocationsList = new ArrayList<>();
		itemImgLocationsList.add(new ItemImgLocations());

		item.setImgLocations(itemImgLocationsList);

		//Execution
		itemServiceImpl.updateItemImage(item);

		//assert
		ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
		verify(itemImgLocationsService, times(1)).save(itemImgLocationsArgumentCaptor.capture());

		ItemImgLocations itemImgLocations = itemImgLocationsArgumentCaptor.getValue();
		assertEquals(itemImgLocations,itemImgLocationsList.get(0));
	}

	public static Object[] getItem(){
		return new Object[]{
				new Object[]{null},
				new Object[]{new Item()},
		};
	}

	@ParameterizedTest
	@MethodSource("getItem")
	void test_savewithsequence_success(Item lastItem){

		//mock
		when(itemRepository.findFirstByModuleIdAndModuleTypeOrderByPositionDesc(item.getModuleId(),item.getModuleType())).thenReturn(lastItem);

		//Execution
		itemServiceImpl.savewithsequence(event,item);

		//assert
		ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
		verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());

		Item itemData = itemArgumentCaptor.getValue();

		assertEquals(itemData.getPosition(),sequence,0);
		verify(itemRepository).findFirstByModuleIdAndModuleTypeOrderByPositionDesc(item.getModuleId(),item.getModuleType());
	}

	@ParameterizedTest
	@MethodSource("getImgLocations")
	void test_saveItemImagewithsequence_imgLocationsNullOrEmpty(List<ItemImgLocations> imgLocations){

		//setup
		item.setImgLocations(imgLocations);

		//Execution
		itemServiceImpl.saveItemImagewithsequence(item);

		assertTrue(true);
	}

	@Test
	void test_saveItemImagewithsequence_success(){

		//setup
		List<ItemImgLocations> itemImgLocationsList = new ArrayList<>();
		itemImgLocationsList.add(new ItemImgLocations());

		item.setImgLocations(itemImgLocationsList);

		//Execution
		itemServiceImpl.saveItemImagewithsequence(item);

		//assert
		ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
		verify(itemImgLocationsService, times(1)).save(itemImgLocationsArgumentCaptor.capture());

		ItemImgLocations itemImgLocations = itemImgLocationsArgumentCaptor.getValue();
		assertEquals(itemImgLocations,itemImgLocationsList.get(0));
	}

	@Test
	void test_updateWithSequence_both_position_one(){

		item.setPosition(1);
		when(itemRepository.findItemById(anyLong())).thenReturn(ofNullable(item));
		when(itemRepository.nextPositionItem(anyLong(), any(), anyDouble())).thenReturn(Collections.singletonList(item));
		when(itemRepository.previousPositionItem(anyLong(), any(), anyDouble())).thenReturn(Collections.singletonList(item));

		itemServiceImpl.updatewithsequence(item,1L,1L,event,user);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository).save(itemArgumentCaptor.capture());

        assertEquals(item,itemArgumentCaptor.getValue());
	}

	@Test
	void test_updateWithSequence_both_position_zero(){

		item.setPosition(0);
		when(itemRepository.findItemById(anyLong())).thenReturn(ofNullable(item));



		itemServiceImpl.updatewithsequence(item,1L,1L,event,user);

		ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
		verify(itemRepository).save(itemArgumentCaptor.capture());

		assertEquals(item,itemArgumentCaptor.getValue());
	}

	@Test
	void test_updateWithSequence_topItem_null(){

		item.setPosition(1);
		when(itemRepository.findItemById(1L)).thenReturn(ofNullable(null));

		when(itemRepository.findItemById(2L)).thenReturn(ofNullable(item));


		itemServiceImpl.updatewithsequence(item,1L,2L,event,user);

		ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
		verify(itemRepository).save(itemArgumentCaptor.capture());

		assertEquals(item,itemArgumentCaptor.getValue());
	}

	@Test
	void test_updateWithSequence_bottom_null(){

		item.setPosition(1);
        item.setModuleType(ModuleType.AUCTION);
		when(itemRepository.findItemById(1L)).thenReturn(ofNullable(item));
		when(itemRepository.findItemById(2L)).thenReturn(ofNullable(null));

		doNothing().when(itemRepository).updatePositionForAllItem(anyLong(), any(), anyDouble());

		itemServiceImpl.updatewithsequence(item,1L,2L,event,user);

		ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
		verify(itemRepository).save(itemArgumentCaptor.capture());

		assertEquals(item,itemArgumentCaptor.getValue());
	}

	@Test
	void test_updateItemImageWithSequence_notNull_both_with_position_not_equal_to_one(){

		item.setId(1);
		item.setName( "item" );
		item.setCode( "TWE" );
		item.setModuleType(ModuleType.AUCTION);
		item.setPosition( 1000 );
		item.setBuyItNowPrice(0);


		ItemImgLocations itemImgLocations = new ItemImgLocations();
		itemImgLocations.setId(1L);
		itemImgLocations.setItem( item );

		when(itemImgLocationsService.findByItem(any())).thenReturn((Collections.singletonList(itemImgLocations)));



		itemServiceImpl.updateItemImagewithsequence(item,itemImgLocations,1L,1L);

		ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
		verify(itemImgLocationsService).save(itemImgLocationsArgumentCaptor.capture());

		assertEquals(itemImgLocations,itemImgLocationsArgumentCaptor.getValue());

	}

    @Test
    void test_updateItemImageWithSequence_notNull_both_with_position_equal_to_one(){

        item.setId(1);
        item.setName( "item" );
        item.setCode( "TWE" );
        item.setModuleType(ModuleType.AUCTION);
        item.setPosition( 1001 );
        item.setBuyItNowPrice(0);


        ItemImgLocations itemImgLocations = new ItemImgLocations();
        itemImgLocations.setId(1L);
        itemImgLocations.setPosition(1);
        itemImgLocations.setItem( item );

        when(itemImgLocationsService.findByItem(any())).thenReturn((Collections.singletonList(itemImgLocations)));

        when(itemImgLocationsService.nextPositionItemImage(anyLong(), anyDouble())).thenReturn(Collections.singletonList(itemImgLocations));
        when(itemImgLocationsService.previousPositionItemImage(anyLong(), anyDouble())).thenReturn(Collections.singletonList(itemImgLocations));
        doNothing().when(itemImgLocationsService).updatePositionItem(anyDouble(), anyDouble(), anyDouble(), anyLong());
        itemServiceImpl.updateItemImagewithsequence(item,itemImgLocations,1L,1L);

        ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
        verify(itemImgLocationsService).save(itemImgLocationsArgumentCaptor.capture());

        assertEquals(itemImgLocations,itemImgLocationsArgumentCaptor.getValue());

    }

    @Test
    void test_updateItemImageWithSequence_null_topItem(){

        item.setId(1);
        item.setName( "item" );
        item.setCode( "TWE" );
        item.setModuleType(ModuleType.AUCTION);
        item.setPosition( 1001 );
        item.setBuyItNowPrice(0);


        ItemImgLocations itemImgLocations = new ItemImgLocations();
        itemImgLocations.setId(1L);
        itemImgLocations.setPosition(1);
        itemImgLocations.setItem( item );

        when(itemImgLocationsService.findByItem(any())).thenReturn((Collections.singletonList(itemImgLocations)));
        doNothing().when(itemImgLocationsService).updatePositionForAllItemImages(anyDouble(), anyLong());



        itemServiceImpl.updateItemImagewithsequence(item,itemImgLocations,0L,1L);

        ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
        verify(itemImgLocationsService).save(itemImgLocationsArgumentCaptor.capture());

        assertEquals(itemImgLocations,itemImgLocationsArgumentCaptor.getValue());

    }

    @Test
    void test_updateItemImageWithSequence_null_topNextItem(){

        item.setId(1);
        item.setName( "item" );
        item.setCode( "TWE" );
        item.setModuleType(ModuleType.AUCTION);
        item.setPosition( 1001 );
        item.setBuyItNowPrice(0);


        ItemImgLocations itemImgLocations = new ItemImgLocations();
        itemImgLocations.setId(1L);
        itemImgLocations.setPosition(1);
        itemImgLocations.setItem( item );

        when(itemImgLocationsService.findByItem(any())).thenReturn((Collections.singletonList(itemImgLocations)));




        itemServiceImpl.updateItemImagewithsequence(item,itemImgLocations,1L,0L);

        ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
        verify(itemImgLocationsService).save(itemImgLocationsArgumentCaptor.capture());

        assertEquals(itemImgLocations,itemImgLocationsArgumentCaptor.getValue());

    }

    @Test
    void test_delete(){
        itemServiceImpl.delete(item);
        assertTrue(true);
    }

    @Test
    void test_deleteById(){
	    when(itemRepository.findItemById(anyLong())).thenReturn(ofNullable(item));
        itemServiceImpl.deleteById(1L);
        assertTrue(true);

    }

    @Test
    void test_deleteById_throw_exception_notFound(){

        when(itemRepository.findItemById(anyLong())).thenReturn(ofNullable(null));
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.deleteById(1L));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_getAllItems(){

	    when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(Collections.singletonList(item));
        List<Item> allItems = itemServiceImpl.getAllItems(1L, ModuleType.AUCTION);
        assertEquals(Collections.singletonList(item),allItems);

    }

    @Test
    void test_countItemByModuleIdAndModuleType(){

        when(itemRepository.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(1);
        int allItems = itemServiceImpl.countItemByModuleIdAndModuleType(1L, ModuleType.AUCTION);
        assertEquals(1,allItems);

    }

    @Test
    void test_getAllItemsAndAllParticipants(){

        AuctionBid auctionBid = new AuctionBid();

	    when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(Collections.singletonList(item));
	    when(bidRepository.getAllBidsByItemAndIsRefunded(item, false)).thenReturn(Collections.singletonList(auctionBid));

        Set<ItemParticipants> allItemsAndAllParticipants = itemServiceImpl.getAllItemsAndAllParticipants(1L, ModuleType.AUCTION, true);

        for (ItemParticipants itemParticipants:allItemsAndAllParticipants) {
            assertEquals(item,itemParticipants.getItem());
            assertEquals(Collections.singletonList(null),itemParticipants.getUsers());
        }
    }

    @Test
    void test_updateItemVisibility(){

	    when(itemRepository.findItemByIdAndModuleType(anyLong(),any())).thenReturn(ofNullable(item));

	    itemServiceImpl.updateItemVisibility(event,1L,true,ModuleType.AUCTION);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());

        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(itemData,item);

    }

    @Test
    void test_updateItemVisibility_item_not_found(){

        when(itemRepository.findItemByIdAndModuleType(anyLong(),any())).thenReturn(ofNullable(null));

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.updateItemVisibility(event,1L,true,ModuleType.AUCTION));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getAllItemsByHighestBidder(){

	    when(itemRepository.findAllItemByModuleIdAndAndModuleTypeAndCurrentHighBidder(1L, ModuleType.AUCTION,
                user)).thenReturn(Collections.singletonList(item));

        List<Item> allItemsByHighestBidder = itemServiceImpl.getAllItemsByHighestBidder(1L, ModuleType.AUCTION, user);

        assertEquals(Collections.singletonList(item),allItemsByHighestBidder);

    }

    @Test
    void test_updateUserUnconfirmedBidsAndItems(){

	    User user1 = new User();
	    user1.setEmail("<EMAIL>");
	    user1.setUserId(2L);
        AuctionBid auctionBid = new AuctionBid();
        item.setCurrentBid(1000D);
        item.setCurrentHighBidder(user1);
        auctionBid.setAmount(10000D);
        auctionBid.setItem(item);


	    when(bidRepository.getAllBidsByUserAndAuctionIdAndIsConfirmedAndIsRefunded(any(), anyLong(),
                anyBoolean(), anyBoolean())).thenReturn(Collections.singletonList(auctionBid));
	    when(auctionBidService.getLastBidderLesserThanBuyItNow(any())).thenReturn(user);
	    doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyDouble(), anyBoolean());

	    itemServiceImpl.updateUserUnconfirmedBidsAndItems(user,event);

        //assert
        verify(bidRepository).save(auctionBid);
        verify(itemRepository).save(item);


    }

    @Test
    void test_updateUserUnconfirmedPledgeAndItems(){

	    Pledge pledge = new Pledge();

	    when(pledgeRepository.findByCauseAuctionIdAndIsConfirmed(anyLong(), anyBoolean())).thenReturn((Collections.singletonList(pledge)));

	    itemServiceImpl.updateUserUnconfirmedPledgeAndItems(user,event);

        ArgumentCaptor<Pledge> itemArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
        verify(pledgeRepository).save(itemArgumentCaptor.capture());

        Pledge pledge1 = itemArgumentCaptor.getValue();
        assertEquals(pledge,pledge1);

    }

    @Test
    void test_updateCurrentBidForAllItemOfModule() {

        doNothing().when(itemRepository).updateCurrentBidForAllItemOfModule(anyLong(), any(), anyDouble());

        itemServiceImpl.updateCurrentBidForAllItemOfModule(1L,ModuleType.AUCTION, 100D);

        assertTrue(true);
    }

    @Test
    void test_resetCurrentHighBidderForAllItemOfModule() {

        doNothing().when(itemRepository).updateCurrentHighBidderForAllItemOfModule(anyLong(), any());

        itemServiceImpl.resetCurrentHighBidderForAllItemOfModule(1L,ModuleType.AUCTION);

        assertTrue(true);
    }

    @Test
    void test_updateNumberOfTicketsForAllItemOfModule() {

        doNothing().when(itemRepository).updateNumberOfTicketsForAllItemOfModule(anyLong(), any(), anyInt());

        itemServiceImpl.updateNumberOfTicketsForAllItemOfModule(1L,ModuleType.AUCTION, 100);

        assertTrue(true);
    }

    @Test
    void updateByNewUser() {
        doNothing().when(itemRepository).updateByNewUser(user, user);
        itemServiceImpl.updateByNewUser(user, user);
        assertTrue(true);
    }

    @Test
    void getActiveItems() {
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveOrderByPositionDesc(anyLong(), any(),
                anyBoolean())).thenReturn(Collections.singletonList(item));

        List<Item> activeItems = itemServiceImpl.getActiveItems(1L, ModuleType.AUCTION);

        assertEquals(Collections.singletonList(item),activeItems);
    }

    @Test
    void getActiveItemIdAndCategories() {

	    ItemCategoryDetails itemCategoryDetails = new ItemCategoryDetails();

        when(itemRepository.findAllItemAndCatByModuleIdAndModuleTypeAndActiveOrderByPositionDesc(anyLong(), any(),anyBoolean()))
                .thenReturn(Collections.singletonList(itemCategoryDetails));
        List<ItemCategoryDetails> activeItemIdAndCategories = itemServiceImpl.getActiveItemIdAndCategories(1L, ModuleType.AUCTION);

        assertEquals(Collections.singletonList(itemCategoryDetails),activeItemIdAndCategories);
    }

    @Test
    void getItemsForSearchWithCategory() {

        ItemCategory itemCategory = new ItemCategory();

        Pageable pageable = PageRequest.of(0, 1);
        PageImpl pageImpl = new PageImpl(Collections.singletonList(item));

        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndActiveAndItemCategoryOrderByPositionDesc(
                anyLong(), any(), anyBoolean(), anyLong(), any(), nullable(Boolean.class), nullable(Integer.class), nullable(Double.class), nullable(Boolean.class))).thenReturn(pageImpl);

        Page<Item> itemsForSearchWithCategory = itemServiceImpl.getItemsForSearchWithCategory(1L, ModuleType.AUCTION, null, pageable, ofNullable(itemCategory));

        assertEquals(pageImpl.getTotalElements(),itemsForSearchWithCategory.getTotalElements());
    }

    @Test
    void test_getItemWithLowestPosition(){

	    when(itemRepository.findFirstItemByModuleIdAndModuleTypeAndActiveOrderByPositionAsc(anyLong(),any(),anyBoolean())).thenReturn(ofNullable(item));
        Optional<Item> itemWithLowestPosition = itemServiceImpl.getItemWithLowestPosition(1L, ModuleType.AUCTION);

        assertEquals(item,itemWithLowestPosition.get());
    }

    @Test
    void test_getItemByModuleType(){

	    item.setCode("test");
	    when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(),
                any())).thenReturn(Collections.singletonList(item));

        Optional<Item> test = itemServiceImpl.getItemByModuleType(event, "test", ModuleType.RAFFLE);
        Optional<Item> test1 = itemServiceImpl.getItemByModuleType(event, "test", ModuleType.AUCTION);
        Optional<Item> test2 = itemServiceImpl.getItemByModuleType(event, "test", ModuleType.CAUSEAUCTION);

        assertEquals(item,test.get());
        assertEquals(item,test1.get());
        assertEquals(item,test2.get());
    }

    @Test
    void test_getItemByModuleType_optionalEmpty(){

        item.setCode("test");
        when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(),
                any())).thenReturn(Collections.emptyList());

        Optional<Item> test = itemServiceImpl.getItemByModuleType(event, "test", ModuleType.RAFFLE);

        assertEquals(empty(),test);
    }

    @Test
    void test_getItemByModuleTypeAndSearchString(){

        item.setCode("test");
        when(itemRepository.findItemsByModuleIdAndModuleTypeAndSearchStringOrderByPositionDesc(anyLong(),
                any(),anyString())).thenReturn(Collections.singletonList(item));

        List<Item> test = itemServiceImpl.getItemByModuleTypeAndSearchString(event, "test", ModuleType.RAFFLE);
        List<Item> test1 = itemServiceImpl.getItemByModuleTypeAndSearchString(event, "test", ModuleType.AUCTION);
        List<Item> test2 = itemServiceImpl.getItemByModuleTypeAndSearchString(event, "test", ModuleType.CAUSEAUCTION);

        assertEquals(Collections.singletonList(item),test);
        assertEquals(Collections.singletonList(item),test1);
        assertEquals(Collections.singletonList(item),test2);
    }

    @Test
    void test_validateAndCheckItemCodeAlreadyPresentForOtherItemInSameEvent(){

	    item.setId(1L);
	    doReturn(ofNullable(item)).when(itemServiceImpl).getItem(anyLong(), anyString());
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.validateAndCheckItemCodeAlreadyPresentForOtherItemInSameEvent(1L,2L,"test"));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_ALREADY_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getItemFromItemDto(){

        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan");
        itemDto.setCode("test");


	    doNothing().when(itemServiceImpl).saveItemFromItemDto(any(),any(),any(),anyString());
        Item test = itemServiceImpl.getItemFromItemDto(itemDto, event, "test");

        assertEquals(item,test);
    }

    @Test
    void test_validateAndCheckItemShortNameAlreadyPresentForOtherItemInSameEvent(){

	    item.setItemShortName("Milan");
	    item.setId(1);

        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndItemShortName(
                anyLong(), any(), anyString())).thenReturn(Collections.singletonList(item));

        try {
        Method checkSpeakerAlreadyExistByEmail = ItemServiceImpl.class
                .getDeclaredMethod("validateAndCheckItemShortNameAlreadyPresentForOtherItemInSameEvent", long.class,long.class,String.class);
        checkSpeakerAlreadyExistByEmail.setAccessible(true);
        checkSpeakerAlreadyExistByEmail.invoke(itemServiceImpl,event.getEventId(), 2L, "Milan");
        } catch (InvocationTargetException e) {
            assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_SHORT_NAME_EXISTS.getDeveloperMessage(),e.getTargetException().getMessage());
        } catch (Exception e){
            //Ignore
        }

    }

    @Test
    void test_validateAndCheckItemShortNameAlreadyPresentForOtherItemInSameEvent_null(){

        item.setItemShortName("Milan");
        item.setId(1);

        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndItemShortName(
                anyLong(), any(), anyString())).thenReturn(Collections.emptyList());

        try {
            Method checkSpeakerAlreadyExistByEmail = ItemServiceImpl.class
                    .getDeclaredMethod("validateAndCheckItemShortNameAlreadyPresentForOtherItemInSameEvent", long.class, long.class, String.class);
            checkSpeakerAlreadyExistByEmail.setAccessible(true);
            checkSpeakerAlreadyExistByEmail.invoke(itemServiceImpl, event.getEventId(), 1L, "Milan");
            assertTrue(true);
        }catch (Exception e){
            //Ignore
        }

    }

    @Test
    void test_getItemsByCategoryId(){

	    when(itemRepository.findByItemCategoryId(any(), anyLong())).thenReturn(1L);

        Long itemsByCategoryId = itemServiceImpl.getItemsByCategoryId(ModuleType.AUCTION, 1L);

        assertEquals(Long.valueOf(1),itemsByCategoryId);

    }

    @Test
    void test_updateItemFromItemDto(){

        //setup
        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan");
        itemDto.setCode("test");
        item.setCode(itemCode);
        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        //mock
        when(roEventService.getEventById(1L)).thenReturn(event);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndCode(anyLong(),any(),anyString())).thenReturn(itemList);

        itemServiceImpl.updateItemFromItemDto(item,itemDto,event,true,"Milan");
        assertTrue(true);

    }

    @Test
    void test_updateItemFromItemDto_updateItem_false(){

        //setup
        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan");
        itemDto.setCode("test");
        item.setCode(itemCode);
        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        //mock
        when(roEventService.getEventById(1L)).thenReturn(event);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndCode(anyLong(),any(),anyString())).thenReturn(itemList);

        itemServiceImpl.updateItemFromItemDto(item,itemDto,event,false,"");
        assertTrue(true);

    }

    @Test
    void test_updateItemFromItemDto_updateItem_false_with_itemName_with_space(){

        //setup
        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan Dobariya");
        itemDto.setCode("test");
        item.setCode(itemCode);
        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        //mock
        when(roEventService.getEventById(1L)).thenReturn(event);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndCode(anyLong(),any(),anyString())).thenReturn(itemList);

        itemServiceImpl.updateItemFromItemDto(item,itemDto,event,false,"");
        assertTrue(true);

    }

    @Test
    void test_updateItemFromItemDto_updateItem_exception(){

        //setup
        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan Dobariya");
        item.setCode(itemCode);
        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.updateItemFromItemDto(item,itemDto,event,false,""));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_CODE_IS_REQUIRED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getUpdateItemImageLocationFromDto(){

        item.setId(1);
        item.setName( "item" );
        item.setCode( "TWE" );
        item.setModuleType(ModuleType.AUCTION);
        item.setPosition( 1001 );
        item.setBuyItNowPrice(0);

        ItemImageDto itemImageDto = new ItemImageDto();
        itemImageDto.setId(1L);
        itemImageDto.setImageUrl("test");
        itemImageDto.setPosition(1D);

        ItemImageDto itemImageDto2 = new ItemImageDto();
        itemImageDto2.setId(2L);
        itemImageDto2.setImageUrl("test");
        itemImageDto2.setPosition(1D);

        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan Dobariya");
        itemDto.setCode("test");
        itemDto.setImages(Collections.singletonList(itemImageDto));

        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setDefaultItemImage("test");
        event.setWhiteLabel(whiteLabel);


        ItemImgLocations itemImgLocations = new ItemImgLocations();
        itemImgLocations.setId(1L);
        itemImgLocations.setPosition(1);
        itemImgLocations.setItem( item );

	    when(itemImgLocationsService.findByItem(any())).thenReturn(Collections.singletonList(itemImgLocations));


        itemServiceImpl.getUpdateItemImageLocationFromDto(item,itemDto,event);

        assertTrue(true);

    }

    @Test
    void test_getUpdateItemImageLocationFromDto_whiteLable_null(){

        item.setId(1);
        item.setName( "item" );
        item.setCode( "TWE" );
        item.setModuleType(ModuleType.AUCTION);
        item.setPosition( 1001 );
        item.setBuyItNowPrice(0);

        ItemImageDto itemImageDto = new ItemImageDto();
        itemImageDto.setId(1L);
        itemImageDto.setImageUrl("test");
        itemImageDto.setPosition(1D);

        ItemUpdateDto itemDto = new ItemUpdateDto();
        itemDto.setName("Milan Dobariya");
        itemDto.setCode("test");
        itemDto.setImages(Collections.singletonList(itemImageDto));

        WhiteLabel whiteLabel = new WhiteLabel();
        whiteLabel.setDefaultItemImage("test");
        event.setWhiteLabel(whiteLabel);


        ItemImgLocations itemImgLocations = new ItemImgLocations();
        itemImgLocations.setId(1L);
        itemImgLocations.setPosition(1);
        itemImgLocations.setItem( item );

        when(itemImgLocationsService.findByItem(any())).thenReturn(null);


        itemServiceImpl.getUpdateItemImageLocationFromDto(item,itemDto,event);

        assertTrue(true);

    }

    @Test
    void test_getAllPageItems(){

        item.setId(1L);
        item.setName("BOOK");

        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        Page<Item> pageImpl = new PageImpl<>(itemList);

        when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any(), anyString(), any())).thenReturn(pageImpl);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any(ModuleType.class), any(Pageable.class))).thenReturn(pageImpl);

        Page<Item> test = itemServiceImpl.getAllItems(1L, ModuleType.AUCTION, "test", 10, 0);
        Page<Item> test2 = itemServiceImpl.getAllItems(1L, ModuleType.AUCTION, "", 10, 0);

        assertEquals(pageImpl,test);
        assertEquals(pageImpl,test2);

    }

    @Test
    void test_getAllLiveItems(){

        item.setId(1L);
        item.setName("BOOK");

        List<Item> itemList = new ArrayList<>();
        itemList.add(item);

        Page<Item> pageImpl = new PageImpl<>(itemList);

        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndIsLiveAuctionItemOrderByPositionDesc(anyLong(), any(),
                anyString(), any())).thenReturn(pageImpl);
        when(itemRepository.findAllItemByModuleIdAndModuleTypeAndIsLiveAuctionItemOrderByPositionDesc(anyLong(), any(),anyBoolean(),
                any())).thenReturn(pageImpl);

        Page<Item> test = itemServiceImpl.getAllLiveItems(1L, ModuleType.AUCTION, "test", 10, 0);
        Page<Item> test2 = itemServiceImpl.getAllLiveItems(1L, ModuleType.AUCTION, "", 10, 0);

        assertEquals(pageImpl,test);
        assertEquals(pageImpl,test2);

    }

    @Test
    void test_getAllItemsCount(){

        when(itemRepository.countByModuleIdAndModuleTypeAndCodeContainingOrderByPositionDesc(anyLong(), any(),
                anyString())).thenReturn(10L);
        when(itemRepository.countByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(10L);

        long test = itemServiceImpl.getAllItemsCount(1L, ModuleType.AUCTION, "test");
        long test2 = itemServiceImpl.getAllItemsCount(1L, ModuleType.AUCTION, "");

        assertEquals(10L,test);
        assertEquals(10L,test2);

    }

    @Test
    void test_getAllIAuctiontemsWithImage(){

        AuctionItemDto auctionItemDto = new AuctionItemDto();
        auctionItemDto.setId(1L);
        auctionItemDto.setImageId(1L);
        auctionItemDto.setImageUrl("test");
        auctionItemDto.setPosition(1001);

        when(itemRepository.findAllIAuctionItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any(),
                anyString())).thenReturn(Collections.singletonList(auctionItemDto));
        when(itemRepository.findAllAuctionItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(Collections.singletonList(auctionItemDto));

        List<AuctionItemDto> test = itemServiceImpl.getAllIAuctiontemsWithImage(1L, "test");
        List<AuctionItemDto> allIAuctiontemsWithImage = itemServiceImpl.getAllIAuctiontemsWithImage(1L, "");

        assertEquals(Collections.singletonList(auctionItemDto),test);
        assertEquals(Collections.singletonList(auctionItemDto),allIAuctiontemsWithImage);

    }

    @Test
    void test_getItemByModuleIdAndTypeAndCode(){

        when(itemRepository.findItemByModuleIdAndModuleTypeAndCode(anyLong(), any(),
                anyString())).thenReturn(ofNullable(item));

        Optional<Item> test = itemServiceImpl.getItemByModuleIdAndTypeAndCode(1L, ModuleType.AUCTION, "test");

        assertEquals(ofNullable(item),test);

    }

    @Test
    void test_getAllAuctionItemsDto(){

        AuctionAllItemDto auctionAllItemDto = new AuctionAllItemDto();
        auctionAllItemDto.setId(1L);
        Pageable pageable = PageRequest.of(1, 10);
        Page<AuctionAllItemDto> auctionAllItemDtos = Mockito.mock(Page.class);
        when(itemRepository.findAllAuctionItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(anyLong(),
                anyString(), any())).thenReturn(auctionAllItemDtos);
        when(itemRepository.findAllAuctionItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any()))
                .thenReturn(auctionAllItemDtos);

        Page<AuctionAllItemDto> test = itemServiceImpl.getAllAuctionItemsDto(1L, "test", pageable);
        Page<AuctionAllItemDto> test2 =itemServiceImpl.getAllAuctionItemsDto(1L, "", pageable);

        assertEquals(auctionAllItemDtos,test);
        assertEquals(auctionAllItemDtos,test2);

    }

    @Test
    void test_getAllFundANeedItemsDto(){

        FundANeedAllItemDto auctionAllItemDto = new FundANeedAllItemDto();
        auctionAllItemDto.setId(1L);

        Pageable pageable = PageRequest.of(1, 10);
        Page<FundANeedAllItemDto> fundANeedAllItemDtos = Mockito.mock(Page.class);
        when(itemRepository.findAllFundANeedItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(anyLong(),
                anyString(), any())).thenReturn(fundANeedAllItemDtos);
        when(itemRepository.findAllFundANeedItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any()))
                .thenReturn(fundANeedAllItemDtos);

        Page<FundANeedAllItemDto> test = itemServiceImpl.getAllFundANeedItemsDto(1L, "test", pageable);
        Page<FundANeedAllItemDto> test2 =itemServiceImpl.getAllFundANeedItemsDto(1L, "", pageable);

        assertEquals(fundANeedAllItemDtos,test);
        assertEquals(fundANeedAllItemDtos,test2);

    }

    @Test
    void test_getAllRaffleItemsDto(){

       Pageable pageable = PageRequest.of(0, 10);
        Page<RaffleAllItemDto> raffleAllItemDtos = Mockito.mock(Page.class);
        when(itemRepository.findAllRaffleItemDtoByModuleIdAndModuleTypeWithSearchStringOrderByPositionDesc(anyLong(),
                anyString(), any())).thenReturn(raffleAllItemDtos);
        when(itemRepository.findAllRaffleItemDtoByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any()))
                .thenReturn(raffleAllItemDtos);

        Page<RaffleAllItemDto> test = itemServiceImpl.getAllRaffleItemsDto(1L, "test", pageable);
        Page<RaffleAllItemDto> test2 =itemServiceImpl.getAllRaffleItemsDto(1L, "", pageable);

        assertEquals(raffleAllItemDtos,test);
        assertEquals(raffleAllItemDtos,test2);

    }

    @Test
    void test_getAuctionUnsoldItemCount(){

        when(itemRepository.countByModuleIdAndModuleTypeAndCurrentBid(anyLong(), any(),
                anyDouble())).thenReturn(12);

        int test = itemServiceImpl.getAuctionUnsoldItemCount(1L);
        assertEquals(12,test);
    }

    @Test
    void test_generateItemShortName(){

	    item.setItemShortName("Milan Dobariya abcabd ab");
        when(roEventService.getEventById(anyLong())).thenReturn(event);
	    when(itemRepository.findAllItemByModuleIdAndModuleTypeAndItemShortName(
                anyLong(), any(), anyString())).thenReturn(Collections.singletonList(item));

        String test = itemServiceImpl.generateItemShortName(event,null);
        String test1 = itemServiceImpl.generateItemShortName(event,"Milan Dobariya abcabd abcd abcd abcd");


        assertNull(test);
        assertEquals("Milan Dobariya abcabd ab1",test1);
    }

    @Test
    void test_getPdfItemCatalogDesc() throws IOException, BadElementException {

        List<Long> favouriteItemListIds = new ArrayList<>();
        favouriteItemListIds.add(1L);

        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setId(1L);
        eventDesignDetail.setEvent(event);

        Auction auction = new Auction();
        auction.setEnableMarketValue(true);

        item.setModuleType(ModuleType.AUCTION);
        item.setMarketValue(12);

        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        when(imageConfiguration.getBlackLogo()).thenReturn("test_image");
        when(imageConfiguration.getItemCatalogPdfEventLogoImageFullUrl(any(), anyString())).thenReturn("test_logo_full_path");
        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.ofNullable(item));
        when(auctionService.findByEvent(any())).thenReturn(auction);

        PdfItemCatalogDto pdfItemCatalogDesc = itemServiceImpl.getPdfItemCatalogDesc(favouriteItemListIds, event);

        assertEquals(event.getName(),pdfItemCatalogDesc.getEventName());


    }

    @Test
    void test_getPdfItemSheetDto() throws IOException, BadElementException {

        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setId(1L);
        eventDesignDetail.setEvent(event);

        Auction auction = new Auction();
        auction.setEnableMarketValue(true);

        item.setModuleType(ModuleType.AUCTION);
        item.setMarketValue(12);

        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        when(imageConfiguration.getBlackLogo()).thenReturn("test_image");

        when(itemRepository.findItemById(1L)).thenReturn(Optional.ofNullable(item));
        when(auctionService.findByEvent(any())).thenReturn(auction);
        PdfItemSheetDto pdfItemCatalogDesc = itemServiceImpl.getPdfItemSheetDto(1L, event);

        assertEquals(item.getMarketValue(),pdfItemCatalogDesc.getMarketValue());


    }

    @Test
    void test_getPdfItemSheetDto_trows_notExpected() throws IOException, BadElementException {

	    item.setModuleId(120);

        when(itemRepository.findItemById(0L)).thenReturn(Optional.empty());
        when(itemRepository.findItemById(1L)).thenReturn(Optional.ofNullable(item));

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.getPdfItemSheetDto(1L, event));


        assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.getPdfItemSheetDto(0L, event));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_copyPdfItemDescAndGetItemIds(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");

	    when(itemRepository.findAllItemByModuleIdAndModuleTypeAndSearchStringOrderByPositionDesc(anyLong(), any(), anyString()))
                .thenReturn(Collections.singletonList(item));
        doNothing().when(clearAPIGatewayCache).clearAPIGwAuctionItemsByEventId(any());
        List<Long> test = itemServiceImpl.copyPdfItemDescAndGetItemIds(event,ModuleType.AUCTION, 1L, "test");

        assertEquals(Collections.singletonList(0L),test);

    }

    @Test
    void test_updatePdfItemSheetDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");
        PdfItemSheetDescDto pdfItemSheetDescDto = new PdfItemSheetDescDto();
        pdfItemSheetDescDto.setItemSheetDescription("Milan");

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.ofNullable(item));

        itemServiceImpl.updatePdfItemSheetDesc(event,1L, pdfItemSheetDescDto);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());
        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(item,itemData);

    }

    @Test
    void test_updatePdfItemSheetDesc_throws_exception(){

        PdfItemSheetDescDto pdfItemSheetDescDto = new PdfItemSheetDescDto();
        pdfItemSheetDescDto.setItemSheetDescription("Milan");

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.updatePdfItemSheetDesc(event,1L, pdfItemSheetDescDto));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_updatePdfItemCatalogDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");
        PdfItemCatalogDescDto pdfItemSheetDescDto = new PdfItemCatalogDescDto();

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.ofNullable(item));

        itemServiceImpl.updatePdfItemCatalogDesc(event,1L, pdfItemSheetDescDto);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());
        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(item,itemData);

    }

    @Test
    void test_updatePdfItemCatalogDesc_throws_exception(){

        PdfItemCatalogDescDto pdfItemSheetDescDto = new PdfItemCatalogDescDto();

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.updatePdfItemCatalogDesc(event,1L, pdfItemSheetDescDto));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_resetPdfItemSheetDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.ofNullable(item));

        itemServiceImpl.resetPdfItemSheetDesc(event,1L);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());
        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(item,itemData);

    }

    @Test
    void test_resetPdfItemSheetDesc_throws_exception(){

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.resetPdfItemSheetDesc(event,1L));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_resetPdfItemCatalogDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");
        item.setItemCatalogDesc("test");

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.ofNullable(item));
        when(itemRepository.save(any())).thenReturn(item);

        String s = itemServiceImpl.resetPdfItemCatalogDesc(event,1L);

        assertEquals(s,"Dobariya");

    }

    @Test
    void test_resetPdfItemCatalogDesc_throws_exception(){

        when(itemRepository.findItemById(anyLong())).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemServiceImpl.resetPdfItemCatalogDesc(event,1L));

        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_NOT_EXISTS.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_resetPdfItemsSheetDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");

        when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(Collections.singletonList(item));

        itemServiceImpl.resetPdfItemsSheetDesc(event,ModuleType.AUCTION,1L);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());
        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(item,itemData);

    }

    @Test
    void test_resetPdfItemsCatalogDesc(){

        item.setItemSheetDesc("Milan");
        item.setDescription("Dobariya");
        item.setItemCatalogDesc("Test");

        when(itemRepository.findAllItemByModuleIdAndModuleTypeOrderByPositionDesc(anyLong(), any())).thenReturn(Collections.singletonList(item));

        itemServiceImpl.resetPdfItemsCatalogDesc(event,ModuleType.AUCTION,1L);

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemRepository, times(1)).save(itemArgumentCaptor.capture());
        Item itemData = itemArgumentCaptor.getValue();
        assertEquals(item,itemData);

    }

}
