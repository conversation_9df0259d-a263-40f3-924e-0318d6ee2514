package com.accelevents.services.impl;

import com.accelevents.common.dto.VirtualPortalImageDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.VirtualPortalImagesType;
import com.accelevents.domain.exhibitors.VirtualPortalImage;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.VirtualPortalImageRepository;
import com.accelevents.services.VirtualPortalImageServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class VirtualPortalImageServiceImplTest {

    @Spy
    @InjectMocks
    private VirtualPortalImageServiceImpl virtualPortalImageService = new VirtualPortalImageServiceImpl();

    @Mock
    VirtualPortalImageRepository virtualPortalImageRepository;

    private Event event;
    private User user;
    private VirtualPortalImage virtualPortalImage;
    private VirtualPortalImageDto virtualPortalImageDto;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        virtualPortalImage = new VirtualPortalImage();
        virtualPortalImage.setCreatedAt(new Date());
        virtualPortalImage.setImageId("fdf19a42-07e6-48a3-b2d3-65155a3b1ea2");
        virtualPortalImage.setFileName("test");
        virtualPortalImage.setEventId(1L);
        virtualPortalImage.setImagePosition(VirtualPortalImagesType.getNameByValue("lobby"));
        virtualPortalImage.setDefaultImage(true);
        virtualPortalImage.setLastDefaultAt(new Date());

        virtualPortalImageDto = new VirtualPortalImageDto();
        virtualPortalImageDto.setCreatedAt(new Date());
        virtualPortalImageDto.setImageId("fdf19a42-07e6-48a3-b2d3-65155a3b1ea2");
        virtualPortalImageDto.setFileName("test");
        virtualPortalImageDto.setImagePosition(VirtualPortalImagesType.getNameByValue("lobby"));
        virtualPortalImageDto.setDefaultImage(true);

    }

    @Test
    void test_saveVirtualPortalImageWhileUpload(){


        doNothing().when(virtualPortalImageRepository).updateIsDefaultPastExpoImage(anyLong(),anyBoolean(),any());

        //Execution
        virtualPortalImageService.saveVirtualPortalImageWhileUpload(event, "fdf19a42-07e6-48a3-b2d3-65155a3b1ea2",
                "test", "lobby");

        ArgumentCaptor<VirtualPortalImage> virtualPortalImageArgumentCaptor = ArgumentCaptor.forClass(VirtualPortalImage.class);
        verify(virtualPortalImageRepository).save(virtualPortalImageArgumentCaptor.capture());

        VirtualPortalImage saveVirtualPortalImage = virtualPortalImageArgumentCaptor.getValue();
        assertEquals(saveVirtualPortalImage.getImageId(),virtualPortalImage.getImageId());
        assertEquals(saveVirtualPortalImage.getFileName(),virtualPortalImage.getFileName());
        assertEquals(saveVirtualPortalImage.getEventId(),virtualPortalImage.getEventId());
        assertEquals(saveVirtualPortalImage.getImagePosition(),virtualPortalImage.getImagePosition());
        assertEquals(saveVirtualPortalImage.isDefaultImage(),virtualPortalImage.isDefaultImage());
    }

    @Test
    void test_getVirtualPortalImageByEventId(){

        List<VirtualPortalImageDto> virtualPortalImageDtoList = new ArrayList<>();
        virtualPortalImageDtoList.add(virtualPortalImageDto);

        when(virtualPortalImageRepository.getVirtualPortalImageByEventIdAndImagePosition(anyLong(),any())).thenReturn(virtualPortalImageDtoList);

        //Execution
        List<VirtualPortalImageDto> allVirtualPortalImageForEvent = virtualPortalImageService.getVirtualPortalImageByEventId(1L, "lobby");
        allVirtualPortalImageForEvent.forEach(virtualPortaldto -> {
            assertEquals(virtualPortaldto.getImageId(),virtualPortalImage.getImageId());
            assertEquals(virtualPortaldto.getFileName(),virtualPortalImage.getFileName());
            assertEquals(virtualPortaldto.getImagePosition(),virtualPortalImage.getImagePosition());
            assertEquals(virtualPortaldto.isDefaultImage(),virtualPortalImage.isDefaultImage());
        });

    }

    @Test
    void test_getDefaultVirtualPortalImageByEventId(){

        when(virtualPortalImageRepository.getDefaultVirtualPortalImageByEventIdAndImagePosition(anyLong(), anyBoolean(),any())).thenReturn(virtualPortalImageDto);

        //Execution
        VirtualPortalImageDto defaultVirtualPortalImageDto = virtualPortalImageService.getDefaultVirtualPortalImageByEventId(1L,"lobby");
        assertEquals(defaultVirtualPortalImageDto.getImageId(),virtualPortalImage.getImageId());
        assertEquals(defaultVirtualPortalImageDto.getFileName(),virtualPortalImage.getFileName());
        assertEquals(defaultVirtualPortalImageDto.getImagePosition(),virtualPortalImage.getImagePosition());
        assertEquals(defaultVirtualPortalImageDto.isDefaultImage(),virtualPortalImage.isDefaultImage());

    }

    @Test
    void test_removePastVirtualPortalImage(){

        virtualPortalImage.setId(1L);

        List<VirtualPortalImage> virtualPortalImageList = new ArrayList<>();
        virtualPortalImageList.add(virtualPortalImage);

        when(virtualPortalImageRepository.findByIdAndImagePosition(anyLong(),any())).thenReturn(Optional.ofNullable(virtualPortalImage));
        when(virtualPortalImageRepository.findLastByEventIdAndImagePosition(anyLong(),any())).thenReturn(virtualPortalImageList);

        //Execution
        virtualPortalImageService.removePastVirtualPortalImage(1L, true, "lobby");

        assertEquals(1L,virtualPortalImage.getId());

        virtualPortalImageList.forEach(virtualPortalImage1 -> {
            assertEquals(virtualPortalImage1.getEventId(),virtualPortalImage.getEventId());
        });

    }

    @Test
    void test_removePastVirtualPortalImage_virtualPortalImageNotFound(){

        //mock
        when(virtualPortalImageRepository.findByIdAndImagePosition(anyLong(),any())).thenReturn(Optional.ofNullable(null));

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> virtualPortalImageService.removePastVirtualPortalImage(1L, true, "lobby"));

        assertEquals(NotFoundException.NotFound.VIRTUAL_PORTAL_IMAGE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_setVirtualPortalImageById(){

        virtualPortalImage.setId(1L);
        virtualPortalImage.setDefaultImage(false);


        List<VirtualPortalImage> virtualPortalImageList = new ArrayList<>();
        virtualPortalImageList.add(virtualPortalImage);

        when(virtualPortalImageRepository.findByIdAndImagePosition(anyLong(),any())).thenReturn(Optional.ofNullable(virtualPortalImage));
        doNothing().when(virtualPortalImageRepository).updateIsDefaultPastExpoImage(anyLong(),anyBoolean(),any());

        //Execution
        VirtualPortalImage updatedVirtualPortalImage = virtualPortalImageService.setVirtualPortalImageById(1L, "lobby");

        virtualPortalImage.setDefaultImage(true);
        assertEquals(updatedVirtualPortalImage.isDefaultImage(),virtualPortalImage.isDefaultImage());

    }

    @Test
    void test_setVirtualPortalImageById_virtualPortalImageNotFound(){

        //mock
        when(virtualPortalImageRepository.findByIdAndImagePosition(anyLong(),any())).thenReturn(Optional.ofNullable(null));

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> virtualPortalImageService.setVirtualPortalImageById(1L, "lobby"));

        assertEquals(NotFoundException.NotFound.VIRTUAL_PORTAL_IMAGE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

    }



}
