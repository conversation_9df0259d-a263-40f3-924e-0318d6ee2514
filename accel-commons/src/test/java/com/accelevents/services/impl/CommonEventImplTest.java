package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.TicketingOrderRepository;
import com.accelevents.services.*;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.ticketing.dto.TicketingBuyerDataFromDB;
import com.accelevents.utils.Constants;
import com.itextpdf.text.DocumentException;
import com.stripe.exception.ApiException;
import com.stripe.exception.StripeException;
import freemarker.template.TemplateException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.*;

import static com.accelevents.utils.Constants.CREATED;
import static com.accelevents.utils.Constants.DELETE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CommonEventImplTest {

    @InjectMocks
    @Spy
    CommonEventImpl commonEventImpl = new CommonEventImpl();

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private TicketingEmailService ticketingEmailService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private UserService userService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private TicketingRefundService ticketingRefundService;

    @Mock
    private EventTicketTransactionService eventTicketTransactionService;

    @Mock
    private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;

    @Mock
    private TrayIntegrationService trayIntegrationService;

    @Mock
    private AttendeeProfileService attendeeProfileService;

    @Mock
    private TicketingPurchaseService ticketingPurchaseService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private LeadRetriverDataRepoService leadRetriverDataRepository;

    @Mock
    private PayFlowConfigServiceImpl payFlowConfigService;

    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;

    private User user;
    private TicketingOrder ticketingOrder;
    private EventTickets eventTickets;
    private Event event;
    private AutoAssignedAttendeeNumbers autoAssignedAttendeeNumbers;

    private Long orderId = 1L;
    private Long eventId = 1L;
    private Long recurringEventId = 1L;

	@BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        eventTickets = EventDataUtil.getEventTickets();
        autoAssignedAttendeeNumbers = new AutoAssignedAttendeeNumbers();
    }

    @Test
    void test_updateOrderStatus_eventTicketsEmpty() throws TemplateException, IOException, JAXBException, DocumentException {

        //mock
        when(ticketingOrderRepository.findById(orderId)).thenReturn(Optional.of(ticketingOrder));
        when(eventTicketsCommonRepo.findByTicketingOrder(ticketingOrder.getId())).thenReturn(Collections.emptyList());
        when(ticketingHelperService.isEnableOrderConfirmationEmail(eventId)).thenReturn(true);
        doNothing().when(ticketingEmailService).reSendTicketingPurchaseOrderEmail(ticketingOrder.getEventid(), ticketingOrder, Collections.emptyList(),true,false);
        doNothing().when(commonEventImpl).orderAuditLog(any(),any(),any(),any(),any(),any());
        //Execution
        commonEventImpl.updateOrderStatus(orderId,user, null);

        //assert
        verify(ticketingOrderRepository).findById(orderId);
        verify(eventTicketsCommonRepo).findByTicketingOrder(ticketingOrder.getId());
        verify(ticketingEmailService).reSendTicketingPurchaseOrderEmail(any(),any(),anyList(),anyBoolean(),anyBoolean());
    }

    public static Object[] getOrderTypeAndStatuAndPaidAmount(){
        return new Object[]{
                new Object[]{TicketingOrder.OrderType.COMPLIMENTARY, "CREATED",0} ,
                new Object[]{TicketingOrder.OrderType.CASH,Constants.PAID,50} ,
        };
    }

    //TODO: Mockito ReValidate
    /*@ParameterizedTest
    @MethodSource("getOrderTypeAndStatuAndPaidAmount")
    void test_updateOrderStatus_eventTickets(TicketingOrder.OrderType orderType,String status,double paidAmount) throws TemplateException, IOException, JAXBException, DocumentException {

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPrice(paidAmount);
        eventTicketsList.add(eventTickets);

        ticketingOrder.setOrderType(orderType);

        //mock
        when(ticketingOrderRepository.findById(orderId)).thenReturn(Optional.of(ticketingOrder));
        doReturn(eventTicketsList).when(eventTicketsCommonRepo).findByTicketingOrder(ticketingOrder.getId());
        doNothing().when(ticketingEmailService).reSendTicketingPurchaseOrderEmail(ticketingOrder.getEventid(), ticketingOrder, eventTicketsList,true);
        doNothing().when(commonEventImpl).orderAuditLog(any(),any(),any(),any(),any(),anyString());
        //Execution
        commonEventImpl.updateOrderStatus(orderId,user, null);

        //assert
        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets actual = eventTicketsArgumentCaptor.getValue();
        assertEquals(actual.getPaidAmount(),paidAmount,0);
        assertEquals(actual.getStatus(),status);

        verify(ticketingOrderRepository).findById(orderId);
        verify(eventTicketsCommonRepo).findByTicketingOrder(ticketingOrder.getId());
        verify(ticketingEmailService).reSendTicketingPurchaseOrderEmail(any(),any(),anyList(),anyBoolean());
    }*/

    @Test
    void test_getBuyerData_withRecurringEventId() {

        //mock
        when(eventTicketsCommonRepo.getTicketingBuyerData(anyLong(),any(),anyList(),anyLong(),anyBoolean(),any(),anyBoolean(),any())).thenReturn(new PageImpl<>(Collections.emptyList()));

        //Execution
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS = commonEventImpl.getBuyerData(eventId, PageRequest.of(0, Integer.MAX_VALUE),recurringEventId,DataType.TICKET);

        //assert

        assertTrue(ticketingBuyerDataFromDBS.getContent().isEmpty());
        verify(eventTicketsCommonRepo).getTicketingBuyerData(anyLong(),any(),anyList(),anyLong(),anyBoolean(),any(),anyBoolean(),any());
    }

    public static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{null} ,
                new Object[]{-1L} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_getBuyerData_success(Long recurringEventId) {

        //mock
        when(eventTicketsCommonRepo.getTicketingBuyerData(anyLong(),any(),anyList(),anyLong(),anyBoolean(),any(),anyBoolean(),any())).thenReturn(new PageImpl<>(Collections.emptyList()));

        //Execution
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS = commonEventImpl.getBuyerData(eventId, PageRequest.of(0, Integer.MAX_VALUE),false,recurringEventId,true,true,DataType.TICKET);

        //assert

        assertTrue(ticketingBuyerDataFromDBS.getContent().isEmpty());
        verify(eventTicketsCommonRepo).getTicketingBuyerData(anyLong(),any(),anyList(),anyLong(),anyBoolean(),any(),anyBoolean(),any());
    }

    public static Object[] getEventTicketingIdAndTicketsToProcessSize(){
        return new Object[]{
                new Object[]{1L,1} ,
                new Object[]{2L,0} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getEventTicketingIdAndTicketsToProcessSize")
    void test_updateEventTicketsOrAddOnToDelete_successEventTicketingIdGreaterThanZero(Long eventTicketingId,int ticketsToProcessSize) throws StripeException, com.squareup.square.exceptions.ApiException {

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setHolderUserId(user);
        eventTicketsList.add(eventTickets);

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED)).thenReturn(eventTicketsList);
        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(false);



        when(userService.findById(any())).thenReturn(user);
        //Execution
        commonEventImpl.updateEventTicketsOrAddOnToDelete(event,orderId,eventTicketingId, true, 1L);

        //assert
        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor= ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder actual = ticketingOrderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID_DELETE,actual.getStatus());

        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> eventTicketsToProcess = argument.getValue();
        assertEquals(eventTicketsToProcess.size() , ticketsToProcessSize);

        verify(ticketingOrderService).findByidAndEventid(orderId, event);
        verify(eventTicketsCommonRepo,times(2)).findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED);
        verify(eventDesignDetailService).isEnableAutoAssignedSequence(event);
    }

    @Test
    void test_updateEventTicketsOrAddOnToDelete_throwExceptionStripeException() throws StripeException, com.squareup.square.exceptions.ApiException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        EventTickets eventTickets1 = new EventTickets();
        eventTickets1.setId(2L);
        eventTicketsList.add(eventTickets1);

        //mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED)).thenReturn(eventTicketsList);
        doThrow(ApiException.class).when(ticketingRefundService).refund(anyLong(), any(),anyList(),any(), anyBoolean(), anyBoolean(),any(), anyBoolean(),anyString(),anyBoolean(),anyBoolean(), anyBoolean());
        doNothing().when(leadRetriverDataRepository).deleteByEventTicketId(List.of(eventTickets.getId(), eventTickets1.getId()), RecordStatus.DELETE);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> commonEventImpl.updateEventTicketsOrAddOnToDelete(event,orderId,-1L, true, 1L));

        //assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_CAN_NOT_DELETED_REFUND_FAILED.getDeveloperMessage(), exception.getMessage());
        verify(ticketingOrderService).findByidAndEventid(orderId, event);
        verify(eventTicketsCommonRepo,times(1)).findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED);
        verify(ticketingRefundService).refund(anyLong(), any(),anyList(),any(), anyBoolean(), anyBoolean(),any(), anyBoolean(),anyString(),anyBoolean(),anyBoolean(), anyBoolean());
    }

    @Test
    void test_updateEventTicketsOrAddOnToDelete_successEventTicketingIdLessThanZeroAndUnPaidOrder(){

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setHolderUserId(user);
        EventTickets eventTickets1 = new EventTickets();
        eventTickets1.setId(2L);
        eventTickets1.setHolderUserId(user);
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED)).thenReturn(eventTicketsList);
        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);

        doNothing().when(autoAssignedAttendeeNumbersService).deleteAssignedNumbersByEventTicketId(anyList());
        when(eventTicketsRepository.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(leadRetriverDataRepository).deleteByEventTicketId(List.of(eventTickets.getId(), eventTickets1.getId()), RecordStatus.DELETE);

        //Execution
        commonEventImpl.updateEventTicketsOrAddOnToDelete(event,orderId,-1L, true, 1L);

        //assert
        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor= ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder actual = ticketingOrderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID_DELETE,actual.getStatus());
        assertEquals(TicketingOrder.OrderType.UNPAID,actual.getOrderType());

        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> eventTicketsToProcess = argument.getValue();
        assertEquals(TicketStatus.DELETED,eventTicketsToProcess.get(0).getTicketStatus());
        assertEquals(TicketPaymentStatus.PAID,eventTicketsToProcess.get(0).getTicketPaymentStatus());
        assertEquals(TicketPaymentStatus.UNPAID,eventTicketsToProcess.get(1).getTicketPaymentStatus());

        verify(ticketingOrderService).findByidAndEventid(orderId, event);
        verify(eventTicketsCommonRepo,times(2)).findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED);
        verify(eventDesignDetailService).isEnableAutoAssignedSequence(event);
        //verify(ticketingRefundService).handleSeatsRelease(any(),anyList(),any());
        verify(autoAssignedAttendeeNumbersService).deleteAssignedNumbersByEventTicketId(anyList());
        verify(eventTicketsRepository).checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any());
    }

    @Test
    void test_updateEventTicketsOrAddOnToDelete_successIsLastTicketOrFullOrderIsDeletedFalse() throws StripeException, com.squareup.square.exceptions.ApiException {

        //setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets.setHolderUserId(user);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(new EventTickets());

        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);

        //mock
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(eventTicketsCommonRepo.findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED)).thenReturn(eventTicketsList);
        when(eventDesignDetailService.isEnableAutoAssignedSequence(event)).thenReturn(true);
        when(eventTicketsRepository.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(List.of());
        doNothing().when(autoAssignedAttendeeNumbersService).deleteAssignedNumbersByEventTicketId(anyList());
        doNothing().when(leadRetriverDataRepository).deleteByEventTicketId(List.of(eventTickets.getId()), RecordStatus.DELETE);



        //Execution
		Long eventTicketingId = 1L;
		commonEventImpl.updateEventTicketsOrAddOnToDelete(event,orderId, eventTicketingId, true, 1L);

        //assert
        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> argument = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository).saveAll(argument.capture());

        List<EventTickets> eventTicketsToProcess = argument.getValue();
        assertEquals(TicketPaymentStatus.PAID,eventTicketsToProcess.get(0).getTicketPaymentStatus());

        verify(ticketingOrderService).findByidAndEventid(orderId, event);
        verify(eventTicketsCommonRepo,times(2)).findByOrderIdAndStatusNotIn(orderId, TicketStatus.DELETED);
        verify(eventDesignDetailService).isEnableAutoAssignedSequence(event);
        verify(autoAssignedAttendeeNumbersService).deleteAssignedNumbersByEventTicketId(anyList());
        verify(eventTicketsRepository).checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any());
        verify(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
    }

    @Test
    void test_updateEventTicketsOrAddOnWithDbRecurringEvent() {

        //mock
        doNothing().when(eventTicketsCommonRepo).updateRecurringId(orderId, 2L);

        //Execution
        commonEventImpl.updateEventTicketsOrAddOnWithDbRecurringEvent(orderId,2L);

        //assertion
        verify(eventTicketsCommonRepo).updateRecurringId(orderId, 2L);
    }

    public static Object[] getAssignedAttendeeToSequence(){
        return new Object[]{
                new Object[]{null,0} ,
                new Object[]{1,1} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getAssignedAttendeeToSequence")
    void test_countAttendeeAssignedToSequence(Integer seq,Integer response) {

        //mock
        when(eventTicketsCommonRepo.countAttendeeAssignedToSequence(autoAssignedAttendeeNumbers)).thenReturn(seq);

        //Execution
        Integer countSeq = commonEventImpl.countAttendeeAssignedToSequence(autoAssignedAttendeeNumbers);

        //assertion

        assertEquals(countSeq,response,0);
        verify(eventTicketsCommonRepo).countAttendeeAssignedToSequence(autoAssignedAttendeeNumbers);
    }

    @Test
    void test_findByTypeId() {

        //setup
        List<TicketingType> ticketTypes = new ArrayList<>();
        ticketTypes.add(new TicketingType());

        //mock
        when(eventTicketsCommonRepo.getEventTicketIdsByTicketTypeIds(ticketTypes)).thenReturn(Collections.emptyList());

        //Execution
        List<Long> ids = commonEventImpl.findByTypeId(ticketTypes);

        //assertion
        assertTrue(ids.isEmpty());
        verify(eventTicketsCommonRepo).getEventTicketIdsByTicketTypeIds(ticketTypes);
    }

    public static Object[] isIncludeDonationTicketType(){
        return new Object[]{
                new Object[]{true} ,
                new Object[]{false} ,
        };
    }

    @ParameterizedTest
    @MethodSource("isIncludeDonationTicketType")
    void test_getBuyerDataBetweenDates(boolean isIncludeDonationTicketType) {

        //mock
        when(eventTicketsCommonRepo.getBuyerDataBetweenDates(anyLong(),any(),anyList(),any(),any(),anyLong(),any(),any())).thenReturn(new PageImpl(Collections.emptyList()));

        //Execution
        Page<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS = commonEventImpl.getBuyerDataBetweenDates(eventId,PageRequest.of(0, Integer.MAX_VALUE),isIncludeDonationTicketType,new Date(),new Date(),recurringEventId,DataType.ADDON);

        //assertion
        assertTrue(ticketingBuyerDataFromDBS.getContent().isEmpty());
        verify(eventTicketsCommonRepo).getBuyerDataBetweenDates(anyLong(),any(),anyList(),any(),any(),anyLong(),any(),any());
    }
}