package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.OrderAuditLogRepo;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.ticketing.dto.AttendeeDto;
import com.accelevents.ticketing.dto.PurchaserInfo;
import com.accelevents.ticketing.dto.RefundDto;
import com.accelevents.ticketing.dto.RefundInfo;
import com.accelevents.utils.Constants;
import com.accelevents.utils.GeneralUtils;
import com.google.gson.Gson;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.OngoingStubbing;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingRefundServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingRefundServiceImpl ticketingRefundService;
    @Mock
    private TicketingOrderService ticketingOrderService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private TableRefundService tableRefundService;
    @Mock
    private PaymentHandlerService paymentHandlerService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private SeatsIoService seatsIoService;
    @Mock
    private RefundTransactionService refundTransactionService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private StripeService stripeService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private TicketingOrderDetailsService ticketingOrderDetailsService;
    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Mock
    private TrayIntegrationService trayIntegrationService;
    @Mock
    private OrderAuditLogRepo orderAuditLogRepo;
    @Mock
    private AttendeeProfileService attendeeProfileService;
    @Mock
    private EventTicketTransactionService eventTicketTransactionService;

    @Mock
    private SalesTaxService salesTaxService;
    
    Event event;
    Ticketing ticketing;
    User user, purchaser, purchaser1;
    TicketHolderAttributes ticketHolderAttributes;
    Unmarshaller unmarshaller;
    EventTickets eventTickets, eventTickets1, eventTickets2;
    TicketingTable ticketingTable;
    RefundInfo refundInfo, refundInfo1, refundInfo2;
    TicketingType ticketingTypePaid, ticketingTypeFree;
    PurchaserInfo purchaserInfo;
    TicketingOrder ticketingOrder;
    StripeTransaction stripeTransaction;
    AttendeeDto attendeeDto, attendeeDto1;
    TicketingOrderManager ticketingOrderManager;
    TicketHolderEmailDto ticketHolderEmailDto;
    EventTicketRefundTracker eventTicketRefundTracker;
    Stripe stripe ;
    StripeDTO stripeDTO;
    PurchaserInfo updatedPurchaser;

    SalesTaxFeeDto salesTaxFeeDto;

	private long numberOfTicketsWhichArePaid=1;

    @BeforeEach
    void setUp() {

        // Prepare data
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
		Long id = 1L;
		event.setEventId(id);
        event.setCurrency(Currency.AUD);
        event.setEventURL("asd");

        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setActivated(true);
        ticketing.setEventid(event);
        ticketing.setEventAddress("testAddress");
        ticketing.setShowRemainingTickets(false);
        ticketing.setEventStartDate(new Date());
        ticketing.setEventEndDate(LocalDate.now().plusDays(10).toDate());
        ticketing.setCheckoutminutes(10);

        user = new User();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setFirstName("Normal");
        user.setLastName("User");
        user.setMostRecentEventId(id);

        purchaser = new User();
        purchaser.setEmail("<EMAIL>");
        purchaser.setFirstName("buyer");
        purchaser.setLastName("user");

        purchaser1 = new User();
        purchaser1.setEmail("<EMAIL>");
        purchaser1.setFirstName("buyer");
        purchaser1.setLastName("user1");

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);

        unmarshaller = getUnmashler();

        eventTickets = EventDataUtil.getEventTickets();
        eventTickets1 = EventDataUtil.getEventTickets();
        eventTickets2 = EventDataUtil.getEventTickets();

        ticketingTable = new TicketingTable();
        ticketingTable.setId(1L);

        refundInfo = new RefundInfo();
        refundInfo1 = new RefundInfo();
        refundInfo2 = new RefundInfo();

        ticketingTypePaid = new TicketingType();
        ticketingTypePaid.setTicketType(TicketType.PAID);
        ticketingTypePaid.setId(1);

        ticketingTypeFree = new TicketingType();
        ticketingTypeFree.setId(2);
        ticketingTypeFree.setTicketType(TicketType.FREE);

        purchaserInfo = new PurchaserInfo();

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(id);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder.setOrderDate(new Date());

        stripeTransaction = new StripeTransaction();
		String cardType = "visa";
		stripeTransaction.setCardType(cardType);
		String lastFour = "1111";
		stripeTransaction.setLastFour(lastFour);

        attendeeDto = new AttendeeDto();
        attendeeDto1 = new AttendeeDto();

        ticketingOrderManager = new TicketingOrderManager();

        ticketHolderEmailDto = new TicketHolderEmailDto();

        eventTicketRefundTracker = new EventTicketRefundTracker();

        stripe = new Stripe();
        stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US",EnumPaymentGateway.STRIPE.name());

        updatedPurchaser = new PurchaserInfo(user);

        salesTaxFeeDto = new SalesTaxFeeDto(false, 10.0, "1");
    }

    private Unmarshaller getUnmashler() {
        JAXBContext jaxbContext = null;
        try {
            jaxbContext = JAXBContext.newInstance(TicketAttributeValueDto.class);
            return jaxbContext.createUnmarshaller();
        } catch (JAXBException e) {
        }
        return null;
    }

    private TicketAttributeValueDto1 parseJsonToObject(String jsonString){
        Gson gson = new Gson();
        try {
            TicketAttributeValueDto1 dto = gson.fromJson(jsonString, TicketAttributeValueDto1.class);
            if(dto == null){
                dto = new TicketAttributeValueDto1();
            }
            return dto;
        } catch (Exception e){
            return new TicketAttributeValueDto1();
        }

    }

    @Test
    void test_getRefundData_success() {

        //mock
        mockTicketingOrder(ticketingOrder);
        doReturn(new RefundDto()).when(ticketingRefundService).getRefundDto(any(), any());

        //execute
        RefundDto refundDto = ticketingRefundService.getRefundData(anyLong(), any());

        //verify
        assertNotNull(refundDto);
    }

    @Test
    void test_getRefundDto_success() {
        // setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(any());

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertEquals(refundDto.getTicketsRefunded(), 2);
    }

    @Test
    void test_getRefundDto_success_numberOfTicketTypeExcludingFreeType_grater_than_one() {
        // setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(any());

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertEquals(refundDto.getTicketsRefunded(), 2);
        verify(stripeService).getStripeFeesByEvent(event);
    }


    @Test
    void test_getRefundDtoWithAllRefunded_success() {
        // setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(null).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        //for all ticket refund
        assertEquals(refundDto.getTicketsRefunded(), eventTicketsList.size());
    }


    @Test
    void whenRefundAndCancelOrderThenStatusCanceled() {
        // setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets1.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(null).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        //for all ticket refund
        assertEquals(Constants.TICKETING_STATUS_CANCELED, refundDto.getStatus());
    }

    @Test
    void getRefundDtoWithAllRefundedWithCount() {
        // setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTickets1.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(null).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        //for all ticket refund
        assertEquals(1, refundDto.getTicketsRefunded());
    }

    @Test
    void test_getRefundDtoWithAttendee_success() {
        // setup
        user.setEmail("");

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets1.setTicketStatus(TicketStatus.REGISTERED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        attendeeDto.setPaid(50.00);
        attendeeDto.setRefundedAmount(0.00);

        attendeeDto1.setPaid(50.00);
        attendeeDto1.setRefundedAmount(10.00);

        List<AttendeeDto> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(attendeeDto);
        attendeeDtos.add(attendeeDto1);

        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(attendeeDtos).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertEquals(refundDto.getAttendee().size(), attendeeDtos.size());
    }

    @Test
    void test_getRefundDtoWithPurchaserInfo_success() {
        // setup
        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(updatedPurchaser).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertNotNull(refundDto.getPurchaser());
        assertEquals(refundDto.getPurchaser().getEmail(), user.getEmail());
        assertEquals(refundDto.getPurchaser().getFirstName(), user.getFirstName());
        assertEquals(refundDto.getPurchaser().getLastName(), user.getLastName());
    }

    @Test
    void test_getRefundDto_With_PurchaserInfo_purchaser_and_ticketingorder_purchaser_lastName_different() {
        // setup
        PurchaserInfo updatedPurchaser = new PurchaserInfo(purchaser);

        ticketingOrder.setPurchaser(purchaser1);

        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(updatedPurchaser).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertNotNull(refundDto.getPurchaser());
        assertEquals(refundDto.getPurchaser().getEmail(), purchaser.getEmail());
        assertEquals(refundDto.getPurchaser().getFirstName(), purchaser.getFirstName());
        assertEquals(refundDto.getPurchaser().getLastName(), purchaser.getLastName());
    }

    @Test
    void test_getRefundDto_With_PurchaserInfo_purchaser_and_ticketingorder_purchaser_firstName_different() {
        // setup
        purchaser1.setFirstName("buyer1");
        purchaser1.setLastName("user");

        PurchaserInfo updatedPurchaser = new PurchaserInfo(purchaser);

        ticketingOrder.setPurchaser(purchaser1);

        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(stripeTransaction);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(updatedPurchaser).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertNotNull(refundDto.getPurchaser());
        assertEquals(refundDto.getPurchaser().getEmail(), purchaser.getEmail());
        assertEquals(refundDto.getPurchaser().getFirstName(), purchaser.getFirstName());
        assertEquals(refundDto.getPurchaser().getLastName(), purchaser.getLastName());
    }

    @Test
    void test_getRefundDtoWithPurchaserInfo_stripeTransaction_null() {
        // setup
        purchaser.setFirstName("");
        purchaser.setLastName("user");

        PurchaserInfo updatedPurchaser = new PurchaserInfo(purchaser);

        ticketing.setChartKey("ChartKey");

        ticketingOrder.setPurchaser(purchaser);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        EventTickets eventTickets = new EventTickets();
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets = new EventTickets();
        eventTickets.setTicketStatus(TicketStatus.REGISTERED);
        eventTickets.setTicketingTable(ticketingTable);

        List<EventTickets> eventTicketsOnlyBundleTypeList = new ArrayList<>();
        eventTicketsOnlyBundleTypeList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsOnlyBundleTypeList.stream().map(eventTicket -> new EventTicketFeesDto(eventTicket.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        mockStripeTransaction(null);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(updatedPurchaser).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);
        when(eventCommonRepoService.findByTicketsHaveOnlyBundleTypeTicketByOrderId(ticketingOrder)).thenReturn(eventTicketsOnlyBundleTypeList);

        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertNotNull(refundDto.getPurchaser());
        assertEquals(refundDto.getPurchaser().getEmail(), purchaser.getEmail());
        assertEquals(refundDto.getPurchaser().getFirstName(), purchaser.getFirstName());
        assertEquals(refundDto.getPurchaser().getLastName(), purchaser.getLastName());
    }

    @Test
    void test_getRefundDtoWithPurchaserInfo_stripeTransaction_eventTicketsOnlyBundleTypeList_empty() {
        // setup
        purchaser.setLastName("");

        PurchaserInfo updatedPurchaser = new PurchaserInfo(purchaser);

        ticketing.setChartKey("ChartKey");

        ticketingOrder.setPurchaser(purchaser);

        eventTickets1.setTicketingTable(ticketingTable);

        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        List<EventTickets> eventTicketsOnlyBundleTypeList = new ArrayList<>();

        //mock
        mockStripeTransaction(null);
        mockListOngoingStubbing(eventTicketsList, any());
        doReturn(updatedPurchaser).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        doReturn(new ArrayList<AttendeeDto>()).when(ticketingRefundService).getAllTicketAttendee(anyList(), anyLong(), any(), anyList());
        mockTicketingOngoingStubbing(event);
        when(eventCommonRepoService.findByTicketsHaveOnlyBundleTypeTicketByOrderId(ticketingOrder)).thenReturn(eventTicketsOnlyBundleTypeList);


        // execution
        RefundDto refundDto = ticketingRefundService.getRefundDto(ticketingOrder, event);
        assertNotNull(refundDto);
        assertNotNull(refundDto.getPurchaser());
        assertEquals(refundDto.getPurchaser().getEmail(), purchaser.getEmail());
        assertEquals(refundDto.getPurchaser().getFirstName(), purchaser.getFirstName());
        assertEquals(refundDto.getPurchaser().getLastName(), purchaser.getLastName());
    }

    @Test
    void test_getRefundDtoWithThrow_REFUND_ORDER_NOT_PAID() {
        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);

        //execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingRefundService.getRefundDto(ticketingOrder, event));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.REFUND_ORDER_NOT_PAID.getDeveloperMessage(), exception.getMessage());
    }

    //@Test
    void test_getPurchaserInfo() {

        //setup
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = getDefaultTicketHolderRequireAttributes("1", false);

        //mock
        mockListOngoingStubbing(eventTicketsList, any());
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);
        when(ticketHolderRequiredAttributesService.getAllAttributes(any())).thenReturn(holderRequiredAttributes);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());


        //execute
        PurchaserInfo purchaserInfo = ticketingRefundService.getPurchaserInfo(ticketingOrder, event);
        assertNull(purchaserInfo.getFirstName());
        assertNull(purchaserInfo.getLastName());
        assertNull(purchaserInfo.getEmail());
        assertNotNull(purchaserInfo);
    }

    //@Test
    void test_getPurchaserInfoWithEmptyEventTicketList() {

        //setup
        ticketingOrder.setPurchaser(purchaser);

        //mock
        mockListOngoingStubbing(new ArrayList<>(), any());

        //execute
        PurchaserInfo purchaserInfo = ticketingRefundService.getPurchaserInfo(ticketingOrder, event);
        assertEquals(purchaserInfo.getFirstName(), ticketingOrder.getPurchaser().getFirstName());
        assertEquals(purchaserInfo.getLastName(), ticketingOrder.getPurchaser().getLastName());
        assertEquals(purchaserInfo.getEmail(), ticketingOrder.getPurchaser().getEmail());
        assertNotNull(purchaserInfo);
    }

    @Test
    void test_getAllTicketAttendeeWithNoEventList_success_with_eventTickets_null() {
        //execution
        List<AttendeeDto> attendeeDtos = ticketingRefundService.getAllTicketAttendee(null, numberOfTicketsWhichArePaid, stripeDTO, Collections.emptyList());
        assertTrue(attendeeDtos.isEmpty());
    }

    @Test
    void test_getAllTicketAttendeeWithNoEventList_success() {

        //setup
        eventTickets.setTicketingTable(ticketingTable);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        Mockito.doNothing().when(ticketingRefundService).setTicketType(any(), any(), any());

        //execution
        List<AttendeeDto> attendeeDtoData = ticketingRefundService.getAllTicketAttendeeForBundleTicketType(eventTicketsList);
        assertEquals(attendeeDtoData.get(0).getFirstName(), eventTickets.getHolderFirstName());
        assertEquals(attendeeDtoData.get(0).getLastName(), eventTickets.getHolderLastName());
    }

    @Test
    void test_updateAttendeeDtosWithBundleTypeTickets_success_with_eventTickets_ticketingTypeId_bundleType_not_INDIVIDUAL_TICKET() {

        //setup
        TicketingTable ticketingTableForEventTicket = new TicketingTable();
        ticketingTableForEventTicket.setId(1L);

        ticketingTypePaid.setBundleType(TicketBundleType.TABLE);
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketingTable(ticketingTableForEventTicket);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        refundInfo.setQty(1);
        refundInfo.setRefundAmount(50D);
        refundInfo.setEventTicketingId(1L);
        refundInfo.setBundleType(TicketBundleType.TABLE.toString());

        List<RefundInfo> refundInfoList = new ArrayList<>();
        refundInfoList.add(refundInfo);

        //execution
        List<RefundInfo> refundDtoData = mockRefundInfos(ticketingRefundService, refundInfoList, eventTicketsList);

        assertEquals(refundDtoData.listIterator(0).next().getEventTicketingId().longValue(), eventTickets.getId());
        assertEquals(refundDtoData.listIterator(0).next().getQty().intValue(), 1);
        assertEquals(refundDtoData.listIterator(0).next().getRefundAmount(), eventTickets.getPaidAmount());
    }

    @Test
    void test_updateAttendeeDtosWithBundleTypeTickets_success_with_eventTickets_ticketingTypeId_bundleType_INDIVIDUAL_TICKET() {

        //setup
        ticketingTypePaid.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        eventTickets.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        refundInfo.setBundleType(TicketBundleType.INDIVIDUAL_TICKET.toString());

        List<RefundInfo> refundInfoList = new ArrayList<>();
        refundInfoList.add(refundInfo);

        //execution
        List<RefundInfo> refundDtoData = mockRefundInfos(ticketingRefundService, refundInfoList, eventTicketsList);

        assertEquals(refundDtoData.listIterator(0).next().getBundleType(), refundInfo.getBundleType());
    }

    @Test
    void test_updateAttendeeDtosWithBundleTypeTickets_success_with_refundInfo_bundleType_null() {

        //setup
        ticketingTypePaid.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        eventTickets.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        refundInfo.setBundleType(null);

        List<RefundInfo> refundInfoList = new ArrayList<>();
        refundInfoList.add(refundInfo);

        //execution
        List<RefundInfo> refundDtoData = mockRefundInfos(ticketingRefundService, refundInfoList, eventTicketsList);

        assertNull(refundDtoData.listIterator(0).next().getBundleType());
    }

    @Test
    void test_getAllTicketAttendeeWithEventList_success() {

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        //mock
        doNothing().when(ticketingRefundService).setTicketType(any(), any(), any());

        //execution
        List<AttendeeDto> attendeeDtos = ticketingRefundService.getAllTicketAttendee(eventTicketsList, numberOfTicketsWhichArePaid, stripeDTO, eventTicketFees);
        assertNotNull(attendeeDtos);
        assertEquals(attendeeDtos.size(), eventTicketsList.size());
        assertEquals(attendeeDtos.iterator().next().getFirstName(), eventTickets.getHolderFirstName());
        assertEquals(attendeeDtos.iterator().next().getLastName(), eventTickets.getHolderLastName());
        assertEquals(attendeeDtos.iterator().next().getBarcode(), eventTickets.getBarcodeId());
    }

    @Test
    void test_getAllTicketAttendeeWithEventList_success_verify() {

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        //mock
        doNothing().when(ticketingRefundService).setTicketType(any(), any(), any());
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        //execution
        List<AttendeeDto> attendeeDtos = ticketingRefundService.getAllTicketAttendee(eventTicketsList, numberOfTicketsWhichArePaid, stripeDTO, eventTicketFees);
        assertNotNull(attendeeDtos);
        assertEquals(attendeeDtos.size(), eventTicketsList.size());
        assertEquals(attendeeDtos.iterator().next().getFirstName(), eventTickets.getHolderFirstName());
        assertEquals(attendeeDtos.iterator().next().getLastName(), eventTickets.getHolderLastName());
        assertEquals(attendeeDtos.iterator().next().getBarcode(), eventTickets.getBarcodeId());
        verify(ticketingOrderDetailsService).setCCfeeAndTicketingFees(any(), anyLong(), any(), any(), any());
    }

    @Test
    void test_setTicketTypeWithTicketingTable_success() {
        //setup
        ticketingTypePaid.setBundleType(TicketBundleType.TABLE);
        ticketingTypePaid.setTicketTypeName("NEW TICKET");

        eventTickets.setTicketingTypeId(ticketingTypePaid);

        ticketingTable.setTableNoSequence(1);

        //execution
        ticketingRefundService.setTicketType(eventTickets, attendeeDto, ticketingTable);
        assertNotNull(attendeeDto.getBundleType());
        assertNotNull(attendeeDto.getTicketType());
        assertTrue(attendeeDto.isTable());
        assertNotNull(attendeeDto.getTicketTypeId());
    }

    @Test
    void test_setTicketTypeWithoutTicketingTable_success() {
        //setup
        ticketingTypePaid.setTicketTypeName("NEW TICKET");

        eventTickets.setTicketingTypeId(ticketingTypePaid);

        //execution
        ticketingRefundService.setTicketType(eventTickets, attendeeDto, null);
        assertNotNull(attendeeDto.getTicketType());
        assertFalse(attendeeDto.isTable());
        assertNotNull(attendeeDto.getTicketTypeId());
    }

    @Test
    void test_refund_full_order() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);

        ticketingTypePaid.setRecurringEventId(1L);

        eventTickets.setRefundedAmount(0.00);
        eventTickets.setAeFeeAmount(1.00);
        eventTickets.setSeatNumber("A-1");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(50.00);
        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setTicketType(ticketingTypePaid);
        orderManager.setSeats("A-1");
        orderManager.setSelectTable("T1");
        ticketingOrderManagers.add(orderManager);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());


        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
        assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.REFUNDED);
    }



    @Test
    void test_refund_partial_cash_order() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder.setOrderDate(new Date());

        eventTickets.setRefundedAmount(25.00);
        eventTickets.setPaidAmount(53.35);
        eventTickets.setTicketPrice(50.00);
        eventTickets.setAeFeeAmount(1.00);
        eventTickets.setSeatNumber("A-1");
        eventTickets.setChargeId(null);
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(28.35);
        attendeeDtos.add(refundInfo);

        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets.getId());
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(53.35);

        List<TicketingOrderManager> ticketingOrderManagerList= new ArrayList<>();
        ticketingOrderManager.setTicketType(ticketingTypePaid);
        ticketingOrderManager.setSeats("A-1");
        ticketingOrderManager.setSelectTable("T1");
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<User> userList = new ArrayList<>();
        userList.add(user);
        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManagers.add(ticketingOrderManager);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketsList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        stripeDTO.setPaymentGateway(EnumPaymentGateway.CASH.name());
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        mockEventTicketTransactions(Collections.singletonList(eventTicketTransactions));
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);
        when(eventTicketsRepoService.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(ticketingRefundService).orderAuditLog(any(),any(),any(),any(),any(),any());
        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,purchaser, false,Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<RefundTransaction> refundTransactionArgumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService, Mockito.times(1)).save(refundTransactionArgumentCaptor.capture());

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        EventTickets resEventTickets = ticketsArgumentCaptor.getValue().get(0);

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());

        assertEquals(resEventTickets.getRefundedAmount(), eventTickets.getRefundedAmount(), 1);
    }

    @Test
    void test_refund_partial_cash_order_success() throws StripeException, ApiException, IOException {
        // setup
        eventTickets.setRefundedAmount(25.00);
        eventTickets.setAeFeeAmount(0d);
        eventTickets.setSeatNumber("A-1");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets1.setId(2);
        eventTickets1.setPaidAmount(10.00);
        eventTickets1.setRefundedAmount(2.00);
        eventTickets1.setAeFeeAmount(0d);
        eventTickets1.setSeatNumber("");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);
        eventTickets1.setHolderUserId(user);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets.getId());
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(100);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(25.00);
        attendeeDtos.add(refundInfo);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManager.setTicketType(ticketingTypePaid);
        ticketingOrderManager.setSeats("A-1");
        ticketingOrderManager.setSelectTable("T1");
        ticketingOrderManagers.add(ticketingOrderManager);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        ticketing.setChartKey("ChartKey");

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketsList, ticketingOrder);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());

        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());

        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();
        mockTicketingOngoingStubbing(any());
        mockEventTicketTransactions(Collections.singletonList(eventTicketTransactions));
        mockRefundInfos(doReturn(attendeeDtos).when(ticketingRefundService), any(), any());
        when(eventTicketsRepoService.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(ticketingRefundService).orderAuditLog(any(),any(),any(),any(),any(),any());
        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false,Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        EventTickets resEventTickets = ticketsArgumentCaptor.getValue().get(0);

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());

        assertEquals(resEventTickets.getRefundedAmount(), eventTickets.getRefundedAmount(), 1);
    }

    @Test
    void test_refund_partial_cash_order_success1() throws StripeException, ApiException, IOException {
        // setup
        eventTickets.setRefundedAmount(25.00);
        eventTickets.setAeFeeAmount(0d);
        eventTickets.setSeatNumber("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        eventTickets1.setId(2);
        eventTickets1.setPaidAmount(10.00);
        eventTickets1.setRefundedAmount(2.00);
        eventTickets1.setAeFeeAmount(0d);
        eventTickets1.setSeatNumber("");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);
        eventTickets1.setHolderUserId(user);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(25.00);
        attendeeDtos.add(refundInfo);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setTicketType(ticketingTypePaid);
        orderManager.setSeats("A-1");
        orderManager.setSelectTable("T1");
        ticketingOrderManagers.add(orderManager);

        ticketing.setChartKey("ChartKey");

        purchaserInfo.setFirstName("jon");
        purchaserInfo.setEmail("<EMAIL>");

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketsList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);




        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());



        mockFindByEvent();

        mockTicketingOngoingStubbing(any());
        mockSaleTax(salesTaxFeeDto);


        mockRefundInfos(doReturn(attendeeDtos).when(ticketingRefundService), any(), any());

        doReturn(purchaserInfo).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketsRepoService.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(ticketingRefundService).orderAuditLog(any(),any(),any(),any(),any(),any());
        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false,Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll((List<EventTicketRefundTracker>) refundTrackerArgumentCaptor.capture());

    }

    @Test
    void test_refund_partial_cash_order_success2() throws StripeException, ApiException, IOException {
        // setup
        eventTickets.setRefundedAmount(25.00);
        eventTickets.setAeFeeAmount(0d);
        eventTickets.setSeatNumber("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(25.00);
        attendeeDtos.add(refundInfo);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManager.setTicketType(ticketingTypePaid);
        ticketingOrderManager.setSeats("A-1");
        ticketingOrderManager.setSelectTable("T1");
        ticketingOrderManagers.add(ticketingOrderManager);

        ticketing.setChartKey("ChartKey");

        purchaserInfo.setFirstName("jon");
        purchaserInfo.setEmail("<EMAIL>");

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        mockTicketingOrder(ticketingOrder);
	    when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockListOngoingStubbing(eventTicketsList, ticketingOrder);
//        doReturn(true).when(ticketingRefundService).setRefundAmount(any(), anyBoolean(),
//                anyList(), anyBoolean(), any(), any(), any(), any(),anyDouble() );




        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());




        mockFindByEvent();

        mockTicketingOngoingStubbing(any());



        mockRefundInfos(doReturn(attendeeDtos).when(ticketingRefundService), any(), any());

        doReturn(purchaserInfo).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketsRepoService.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(ticketingRefundService).orderAuditLog(any(),any(),any(),any(),any(),any());
        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false,Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

    }

    @Test
    void test_refund_partial_cash_order_success3() throws StripeException, ApiException, IOException {
        // setup
        ticketingTypePaid.setId(3);
        ticketingTypePaid.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        TicketingType ticketingType1 = new TicketingType();
        ticketingType1.setId(1);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        eventTickets.setRefundedAmount(25.00);
        eventTickets.setAeFeeAmount(0d);
        eventTickets.setSeatNumber("A-1");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setHolderUserId(user);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(25.00);
        attendeeDtos.add(refundInfo);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManager.setTicketType(ticketingType1);
        ticketingOrderManager.setSeats("A-1");
        ticketingOrderManager.setSelectTable("T1");
        ticketingOrderManagers.add(ticketingOrderManager);

        ticketing.setChartKey("ChartKey");

        purchaserInfo.setFirstName("jon");
        purchaserInfo.setEmail("<EMAIL>");

        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketsList, ticketingOrder);
        /*doReturn(true).when(ticketingRefundService).setRefundAmount(any(), anyInt(),
                anyList(), anyBoolean(), any(), any(), any(), any());*/
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());

        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());




        mockFindByEvent();

	    when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockTicketingOngoingStubbing(any());


        mockRefundInfos(doReturn(attendeeDtos).when(ticketingRefundService), any(), any());

        doReturn(purchaserInfo).when(ticketingRefundService).getPurchaserInfo(any(), any());
        when(eventTicketsRepoService.checkUserHadPurchasedVirtualTypeTicketInEventAndHolderUsers(any(),anySet(),any())).thenReturn(userList);
        doNothing().when(ticketingRefundService).orderAuditLog(any(),any(),any(),any(),any(),any());
        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false,Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

    }

    @Test
    void test_refundWithThrowREFUND_ORDER_NOT_PAID() throws StripeException, ApiException {
        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);

        //mock
        mockTicketingOrder(ticketingOrder);

        //execute
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingRefundService.refund(anyLong(), any(), anyList(), any(), anyBoolean(), anyBoolean(),any(), anyBoolean(),anyString(),anyBoolean(),anyBoolean(), anyBoolean()));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.REFUND_ORDER_NOT_PAID.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_prepareTicketHolderEmailDto_success() {
        //setup
        ticketingTypePaid.setTicketTypeName("General");
        ticketingTypePaid.setTicketTypeDescription("No Description");
        EventTicketRefundTracker eventTicketRefundTracker1 = new EventTicketRefundTracker();
        eventTicketRefundTracker1.setRefundAmount(0.00);
        eventTicketRefundTracker1.setEventTicketId(1L);
        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue());

        eventTickets.setHolderFirstName("Normal");
        eventTickets.setHolderLastName("User");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        TicketAttributeValueDto1 dto1 = parseJsonToObject(ticketHolderAttributes.getJsonValue());

        //execute
        TicketHolderEmailDto ticketHolderEmailDto = ticketingRefundService.createTicketHolderEmailDto(eventTickets, Collections.singletonList(eventTicketRefundTracker1));
        assertNotNull(ticketHolderEmailDto.getTicketTypeName());
        assertNotNull(ticketHolderEmailDto.getTicketingTypeDesc());
        assertNotNull(ticketHolderEmailDto.getHolderName());
        assertNotNull(ticketHolderEmailDto.getPrice());
        assertNotNull(ticketHolderEmailDto.getRefundedAmount());
    }

    @Test
    void test_prepareTicketHolderEmailDto_success_with_eventTickets_holderLastName_empty() {
        //setup
        ticketingTypePaid.setTicketTypeName("General");
        ticketingTypePaid.setTicketTypeDescription("No Description");
        EventTicketRefundTracker eventTicketRefundTracker1 = new EventTicketRefundTracker();
        eventTicketRefundTracker1.setRefundAmount(0.00);
        eventTicketRefundTracker1.setEventTicketId(1L);
        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue());

        eventTickets.setHolderFirstName("Normal");
        eventTickets.setHolderLastName("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        TicketAttributeValueDto1 dto1 = parseJsonToObject(ticketHolderAttributes.getJsonValue());

        //execute
        TicketHolderEmailDto ticketHolderEmailDto = ticketingRefundService.createTicketHolderEmailDto(eventTickets, Collections.singletonList(eventTicketRefundTracker1));
        assertNotNull(ticketHolderEmailDto.getTicketTypeName());
        assertNotNull(ticketHolderEmailDto.getTicketingTypeDesc());
        assertNotNull(ticketHolderEmailDto.getHolderName());
        assertNotNull(ticketHolderEmailDto.getPrice());
        assertNotNull(ticketHolderEmailDto.getRefundedAmount());
    }

    @Test
    void test_prepareTicketHolderEmailDto_success_with_ticketAttributeValueDto_null() throws JAXBException {
        //setup
        EventTicketRefundTracker eventTicketRefundTracker1 = new EventTicketRefundTracker();
        eventTicketRefundTracker1.setRefundAmount(0.00);
        eventTicketRefundTracker1.setEventTicketId(1L);
        ticketingTypePaid.setTicketTypeName("General");
        ticketingTypePaid.setTicketTypeDescription("No Description");
        eventTickets.setHolderFirstName("Normal");
        eventTickets.setHolderLastName("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock

        //execute
        TicketHolderEmailDto ticketHolderEmailDto = ticketingRefundService.createTicketHolderEmailDto(eventTickets, Collections.singletonList(eventTicketRefundTracker1));
        assertNotNull(ticketHolderEmailDto.getTicketTypeName());
        assertNotNull(ticketHolderEmailDto.getTicketingTypeDesc());
        assertNull(ticketHolderEmailDto.getHolderName());
        assertNotNull(ticketHolderEmailDto.getPrice());
        assertNotNull(ticketHolderEmailDto.getRefundedAmount());
    }

    @Test
    void test_prepareTicketHolderEmailDto_success_with_eventTickets_ticketHolderAttributesId_null() {
        //setup
        ticketingTypePaid.setTicketTypeName("General");
        ticketingTypePaid.setTicketTypeDescription("No Description");
        EventTicketRefundTracker eventTicketRefundTracker1 = new EventTicketRefundTracker();
        eventTicketRefundTracker1.setRefundAmount(0.00);
        eventTicketRefundTracker1.setEventTicketId(1L);
        eventTickets.setHolderFirstName("Normal");
        eventTickets.setHolderLastName("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketHolderAttributesId(null);

        //mock


        //execute
        TicketHolderEmailDto ticketHolderEmailDto = ticketingRefundService.createTicketHolderEmailDto(eventTickets, Collections.singletonList(eventTicketRefundTracker1));
        assertNotNull(ticketHolderEmailDto.getTicketTypeName());
        assertNotNull(ticketHolderEmailDto.getTicketingTypeDesc());
        assertNull(ticketHolderEmailDto.getHolderName());
        assertNotNull(ticketHolderEmailDto.getPrice());
        assertNotNull(ticketHolderEmailDto.getRefundedAmount());
    }

    @Test
    void test_prepareTicketHolderEmailDto_success_with_ticketHolderAttributes_value_empty() throws JAXBException {
        //setup
        ticketingTypePaid.setTicketTypeName("General");
        ticketingTypePaid.setTicketTypeDescription("No Description");
        eventTickets.setHolderFirstName("Normal");
        eventTickets.setHolderLastName("");
        eventTickets.setTicketingTypeId(ticketingTypePaid);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        EventTicketRefundTracker eventTicketRefundTracker1 = new EventTicketRefundTracker();
        eventTicketRefundTracker1.setRefundAmount(0.00);
        eventTicketRefundTracker1.setEventTicketId(1L);

        //mock

        //execute
        TicketHolderEmailDto ticketHolderEmailDto = ticketingRefundService.createTicketHolderEmailDto(eventTickets, Collections.singletonList(eventTicketRefundTracker1));
        assertNotNull(ticketHolderEmailDto.getTicketTypeName());
        assertNotNull(ticketHolderEmailDto.getTicketingTypeDesc());
        assertNull(ticketHolderEmailDto.getHolderName());
        assertNotNull(ticketHolderEmailDto.getPrice());
        assertNotNull(ticketHolderEmailDto.getRefundedAmount());
    }

    @Test
    void test_getFullRefundTicketCount() {
        //setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        //execution
        ticketingRefundService.setFullRefundEventTicketIds(new ArrayList<>(), TicketPaymentStatus.REFUNDED, new HashMap<>(), fullRefundEventTicketIds, eventTickets, ticketingOrder, false);
        assertEquals(fullRefundEventTicketIds.size(), 1);
    }

    @Test
    void test_getFullRefundTicketCountWithotTicketStatus() {
        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        eventTickets.setPaidAmount(60.00);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(50.00);
        attendeeDtos.add(refundInfo);

        //execution
         ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder,  false);
        assertEquals(fullRefundEventTicketIds.size(), 1);
        assertFalse(mapEventTickets.isEmpty());
    }

    @Test
    void test_getFullRefundTicketCount_success() {
        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        eventTickets.setPaidAmount(100.00);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(50.00);
        attendeeDtos.add(refundInfo);

        //execution
         ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder, false);
        assertEquals(fullRefundEventTicketIds.size(), 0);
        assertFalse(mapEventTickets.isEmpty());
    }

    @Test
    void test_getFullRefundTicketCount_success_with_eventTicketsId_and_refundInfoEventTicketingId_different() {
        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();

        eventTickets.setPaidAmount(100.00);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        List<RefundInfo> attendeeDtos = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(2L);
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(50.00);
        attendeeDtos.add(refundInfo);

        //execution
        ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder, false);
        assertEquals(fullRefundEventTicketIds.size(), 0);
        assertTrue(mapEventTickets.isEmpty());
    }

    @Test
    void test_getFullRefundTicketCount_success_with_refundInfo_qty_zero() {
        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        eventTickets.setPaidAmount(100.00);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(0);
        refundInfo.setRefundAmount(50.00);

        RefundInfo refundInfo1 = new RefundInfo();
        refundInfo1.setEventTicketingId(eventTickets.getId());
        refundInfo1.setQty(0);
        refundInfo1.setRefundAmount(0d);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);
        attendeeDtos.add(refundInfo1);

        //execution
         ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder, false);
        assertEquals(fullRefundEventTicketIds.size(), 0);
        assertTrue(mapEventTickets.isEmpty());
    }

    @Test
    void test_getFullRefundTicketCount_success_with_eventTickets_paidAmount_zero() {

        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();

        eventTickets.setPaidAmount(00);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(0);
        refundInfo.setRefundAmount(0d);

        RefundInfo refundInfo1 = new RefundInfo();
        refundInfo1.setEventTicketingId(eventTickets.getId());
        refundInfo1.setQty(0);
        refundInfo1.setRefundAmount(1d);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);
        attendeeDtos.add(refundInfo1);

        //execution
         ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder, false);
        assertEquals(fullRefundEventTicketIds.size(), 0);
        assertTrue(mapEventTickets.isEmpty());
    }

    @Test
    void test_getFullRefundTicketCountWithThrowREFUND_AMOUNT_SHOULD_NOT_GREATER_THEN_PAID() {
        //setup
        Map<RefundInfo, EventTickets> mapEventTickets = new HashMap<>();
        List <Long> fullRefundEventTicketIds = new ArrayList<>();
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(60.00);

        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingRefundService.setFullRefundEventTicketIds(attendeeDtos, TicketPaymentStatus.REFUNDED, mapEventTickets, fullRefundEventTicketIds, eventTickets, ticketingOrder, false));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.REFUND_AMOUNT_SHOULD_NOT_GREATER_THEN_PAID.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_unmashler_throwJAXBException() throws JAXBException {
        //setup
        String xml = EventDataUtil.getSampleXML();

        //Execution
        Exception exception = assertThrows(JAXBException.class,
                () -> ticketingRefundService.unmashler(xml, null));

        assertEquals("unmarshaller not found!", exception.getMessage());
    }

    @Test
    void test_unmashler_success() throws JAXBException {
        //setup
        String xml = EventDataUtil.getSampleXML();

        //Execution
        TicketAttributeValueDto actualData = ticketingRefundService.unmashler(xml, unmarshaller);

        assertEquals(actualData.getPurchaser().getAttributes().listIterator(0).next().getValue(), "Jon");
        assertEquals(actualData.getPurchaser().getAttributes().listIterator(1).next().getValue(), "Kaz");
        assertEquals(actualData.getPurchaser().getAttributes().listIterator(2).next().getValue(), "<EMAIL>");
        assertNotNull(actualData);
    }



    @Test
    void test_getTotalRefundedAmount(){
        //setup
        eventTickets.setRefundedAmount(10.00);

        refundInfo.setEventTicketingId(eventTickets.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(40.00);

        //execution
        double refAmt = ticketingRefundService.getTotalRefundedAmount(refundInfo, eventTickets);
        assertEquals(refAmt, 50.0, 1);
    }

    @Test
    void test_getRefundAmount(){
        //setup
        eventTickets.setRefundedAmount(0.00);

        //execution
        double refAmt = ticketingRefundService.getRefundAmount(eventTickets, 1, stripeDTO, false);
        assertEquals(refAmt, 49.41, 2);
    }

    @Test
    void testCancelTicket(){
       //mock
        when(eventTicketsRepoService.findByEventTicketIdWithAllFetch(any())).thenReturn(Optional.ofNullable(eventTickets));

        //execution
        ticketingRefundService.cancelTicket(eventTickets.getId(), event,user, false);

        verify( eventCommonRepoService, Mockito.times(1)).save(eventTickets);
    }

    @Test
    void testCancelTicketThrowTicketNotFoundException(){
        //mock
        when(eventTicketsRepoService.findByEventTicketIdWithAllFetch(any())).thenReturn(Optional.ofNullable(null));


        //execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingRefundService.cancelTicket(eventTickets.getId(), event,user, false));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.TICKET_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }


    @Test
    void testCancelOrder(){
        //setup
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);

        //mock
        when(ticketingOrderService.findByidAndEventid(ticketingOrder.getId(), event)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(any())).thenReturn(eventTicketsList);

        //execution
        ticketingRefundService.cancelOrder(ticketingOrder.getId(), event,user, false);
        verify( eventTicketsRepoService, Mockito.times(1)).saveAll(anyList());

    }



    @Test
    void test_getRefundAmount_success_with_paidAmount_lessThan_zero(){
        //setup
        eventTickets.setPaidAmount(0);

        //execution
        double refAmt = ticketingRefundService.getRefundAmount(eventTickets, 1,
                stripeDTO, false);
        assertEquals(refAmt, 0, 1);
    }

    @Test
    void test_getRefundAmountWithSquarePayment(){
        //setup
        eventTickets.setRefundedAmount(0.00);

        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        //execution
        double refAmt = ticketingRefundService.getRefundAmount(eventTickets, 1,
                stripeDTO, false);
        assertEquals(refAmt, 49.41, 2);
    }

    @Test
    void test_getEventKey_success_with_key_null(){
        //setup
        Long eventId =1L;
        boolean isRecurringEvent = true;

        //execution
        String eventKey = ticketingRefundService.getEventKey(eventId, isRecurringEvent, null);
        assertEquals(eventKey, String.valueOf(eventId));
    }


    private List<TicketHolderRequiredAttributes> getDefaultTicketHolderRequireAttributes(String eventTicketTypeId, Boolean withQuestion) {
        List<TicketHolderRequiredAttributes> ticketHolderAttributes = new ArrayList<>();

        TicketHolderRequiredAttributes prefix = new TicketHolderRequiredAttributes();
        prefix.setName("Prefix");
        prefix.setAttributeValueType(AttributeValueType.DROPDOWN);
        prefix.setEventid(event);
        prefix.setBuyerAttributeOrder(1000);
        prefix.setAttribute(true);
        ticketHolderAttributes.add(prefix);

        TicketHolderRequiredAttributes firstName = new TicketHolderRequiredAttributes();
        firstName.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        firstName.setHolderRequiredTicketTypeId(eventTicketTypeId);
        firstName.setName("First Name");
        firstName.setAttributeValueType(AttributeValueType.TEXT);
        firstName.setEnabledForTicketPurchaser(true);
        firstName.setEnabledForTicketHolder(true);
        firstName.setEventid(event);
        firstName.setBuyerAttributeOrder(2000);
        firstName.setAttribute(true);
        ticketHolderAttributes.add(firstName);

        TicketHolderRequiredAttributes lastname = new TicketHolderRequiredAttributes();
        lastname.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        lastname.setHolderRequiredTicketTypeId(eventTicketTypeId);
        lastname.setName("Last Name");
        lastname.setAttributeValueType(AttributeValueType.TEXT);
        lastname.setEnabledForTicketHolder(true);
        lastname.setEnabledForTicketPurchaser(true);
        lastname.setEventid(event);
        lastname.setBuyerAttributeOrder(3000);
        lastname.setAttribute(true);
        ticketHolderAttributes.add(lastname);

        TicketHolderRequiredAttributes emailAddress = new TicketHolderRequiredAttributes();
        emailAddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        emailAddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        emailAddress.setName("Email");
        emailAddress.setAttributeValueType(AttributeValueType.EMAIL);
        emailAddress.setEnabledForTicketHolder(true);
        emailAddress.setEnabledForTicketPurchaser(true);
        emailAddress.setEventid(event);
        emailAddress.setBuyerAttributeOrder(4000);
        emailAddress.setAttribute(true);
        ticketHolderAttributes.add(emailAddress);

        TicketHolderRequiredAttributes cellphone = new TicketHolderRequiredAttributes();
        cellphone.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        cellphone.setHolderRequiredTicketTypeId(eventTicketTypeId);
        cellphone.setName("Cell Phone");
        cellphone.setAttributeValueType(AttributeValueType.NUMBER);
        cellphone.setEnabledForTicketPurchaser(true);
        cellphone.setEventid(event);
        cellphone.setBuyerAttributeOrder(5000);
        cellphone.setAttribute(true);
        ticketHolderAttributes.add(cellphone);

        TicketHolderRequiredAttributes billingaddress = new TicketHolderRequiredAttributes();
        billingaddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        billingaddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        billingaddress.setName(Constants.BILLING_ADDRESS);
        billingaddress.setAttributeValueType(AttributeValueType.BILLING_ADDRESS);
        billingaddress.setEnabledForTicketHolder(false);
        billingaddress.setEnabledForTicketPurchaser(false);
        billingaddress.setEventid(event);
        billingaddress.setBuyerAttributeOrder(6000);
        billingaddress.setAttribute(true);
        ticketHolderAttributes.add(billingaddress);

        TicketHolderRequiredAttributes shippingaddress = new TicketHolderRequiredAttributes();
        shippingaddress.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        shippingaddress.setHolderRequiredTicketTypeId(eventTicketTypeId);
        shippingaddress.setName(Constants.SHIPPING_ADDRESS);
        shippingaddress.setAttributeValueType(AttributeValueType.SHIPPING_ADDRESS);
        shippingaddress.setEventid(event);
        shippingaddress.setBuyerAttributeOrder(7000);
        shippingaddress.setAttribute(true);
        ticketHolderAttributes.add(shippingaddress);

        TicketHolderRequiredAttributes gender = new TicketHolderRequiredAttributes();
        gender.setName("Gender");
        gender.setAttributeValueType(AttributeValueType.DROPDOWN);
        gender.setEventid(event);
        gender.setBuyerAttributeOrder(8000);
        gender.setAttribute(true);
        ticketHolderAttributes.add(gender);

        TicketHolderRequiredAttributes birthday = new TicketHolderRequiredAttributes();
        birthday.setName("Birthday");
        birthday.setAttributeValueType(AttributeValueType.DATE);
        birthday.setEventid(event);
        birthday.setBuyerAttributeOrder(9000);
        birthday.setAttribute(true);
        ticketHolderAttributes.add(birthday);

        TicketHolderRequiredAttributes age = new TicketHolderRequiredAttributes();
        age.setName("Age");
        age.setAttributeValueType(AttributeValueType.NUMBER);
        age.setEventid(event);
        age.setBuyerAttributeOrder(10000);
        age.setAttribute(true);
        ticketHolderAttributes.add(age);

        TicketHolderRequiredAttributes organization = new TicketHolderRequiredAttributes();
        organization.setName("Organization");
        organization.setAttributeValueType(AttributeValueType.TEXT);
        organization.setEventid(event);
        organization.setAttribute(true);
        organization.setBuyerAttributeOrder(11000);
        ticketHolderAttributes.add(organization);

        TicketHolderRequiredAttributes jobtitle = new TicketHolderRequiredAttributes();
        jobtitle.setName("Job title");
        jobtitle.setAttributeValueType(AttributeValueType.TEXT);
        jobtitle.setEventid(event);
        jobtitle.setBuyerAttributeOrder(12000);
        jobtitle.setAttribute(true);
        ticketHolderAttributes.add(jobtitle);

        TicketHolderRequiredAttributes image = new TicketHolderRequiredAttributes();
        image.setName("Image");
        image.setAttributeValueType(AttributeValueType.IMAGE);
        image.setEventid(event);
        image.setBuyerAttributeOrder(13000);
        image.setAttribute(true);
        ticketHolderAttributes.add(image);

        if (withQuestion) {
            TicketHolderRequiredAttributes question = new TicketHolderRequiredAttributes();
            question.setName("Question");
            question.setAttributeValueType(AttributeValueType.TEXT);
            question.setEventid(event);
            question.setBuyerAttributeOrder(14000);
            question.setAttribute(false);
            question.setEnabledForTicketHolder(true);
            question.setEnabledForTicketPurchaser(true);
            question.setBuyerRequiredTicketTypeId(eventTicketTypeId);
            question.setHolderRequiredTicketTypeId(eventTicketTypeId);
            ticketHolderAttributes.add(question);
        }
        return ticketHolderAttributes;
    }

    private OngoingStubbing<List<EventTickets>> mockListOngoingStubbing(List<EventTickets> eventTicketsList, TicketingOrder any) {
        return when(eventCommonRepoService.findByOrder(any)).thenReturn(eventTicketsList);
    }

    private Object mockStripeTransaction(StripeTransaction stripeTransaction) {
        return when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, ticketingOrder.getId())).thenReturn(stripeTransaction);
    }

    private Object mockTicketingOngoingStubbing(Event event) {
        return when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
    }

    private EventDesignDetail mockFindByEvent() {
        return doReturn(new EventDesignDetail()).when(eventDesignDetailService).findByEvent(any());
    }

    private List<RefundInfo> mockRefundInfos(TicketingRefundServiceImpl when, List<RefundInfo> o, List<EventTickets> o2) {
        return when.updateAttendeeDtosWithBundleTypeTickets(o, o2);
    }

    private TicketingOrder mockTicketingOrder(TicketingOrder ticketingOrder) {
        when(ticketingOrderService.findByidAndEventid(anyLong(), any())).thenReturn(ticketingOrder);
        return ticketingOrder;
    }
    private SalesTaxFeeDto mockSaleTax(SalesTaxFeeDto salesTaxFeeDto) {
        when(salesTaxService.getTaxFeeAndTicketTypeId(anyLong())).thenReturn(salesTaxFeeDto);
        return salesTaxFeeDto;
    }

    private List<EventTicketTransaction> mockEventTicketTransactions(List<EventTicketTransaction>  eventTicketTransactions) {
        when(eventTicketTransactionService.getByTicketingOrderIdAndChargeStatus(anyList(),any())).thenReturn(eventTicketTransactions);
        return eventTicketTransactions;
    }

    @Test
    void testSingleTicketWithFullRefundFromSingleCharge() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);

        ticketingTypePaid.setRecurringEventId(1L);


        eventTickets1.setId(2L);
        eventTickets1.setPaidAmount(105.88);
        eventTickets1.setTicketPrice(100.00);
        eventTickets1.setRefundedAmount(0.00);
        eventTickets1.setAeFeeAmount(2.5);
        eventTickets1.setChargeId("ch_1");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets1);

        //For refund Paid ticket
        refundInfo.setEventTicketingId(eventTickets1.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(105.88);


        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        List <EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(105.88);
        eventTicketTransactions.setChargeId("ch_1");
        eventTicketTransactions.setCcFeeAmount(3.37);
        eventTicketTransactions.setAeFeeAmount(2.5);
        eventTicketTransactions.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setNumberofticket(1);
        orderManager.setTicketType(eventTickets.getTicketingTypeId());
        ticketingOrderManagers.add(orderManager);

        TicketingOrderManager orderManager2 = new TicketingOrderManager();
        orderManager2.setTicketType(eventTickets1.getTicketingTypeId());
        orderManager2.setNumberofticket(1);
        ticketingOrderManagers.add(orderManager2);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockEventTicketTransactions(eventTicketTransactionList);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doNothing().when(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
        when(paymentHandlerService.startRefund(anyLong(), any(), any(), anyBoolean(), anyBoolean(), anyList(), anyMap(), anyDouble(), anyDouble(), anyDouble(), anyDouble(), anyString())).thenReturn(stripeTransaction);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        List<EventTickets> allRefundedTicket = ticketsArgumentCaptor.getValue();

        ArgumentCaptor<List<EventTicketTransaction>> ticketsTransactionArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionService, Mockito.times(1)).saveAll(ticketsTransactionArgumentCaptor.capture());
        List<EventTicketTransaction> allValues = ticketsTransactionArgumentCaptor.getAllValues().get(0);

        allValues.forEach(eventTicketTransaction -> {
            assertEquals(eventTicketTransaction.getChargeStatus(), EventTicketTransaction.TicketTransactionChargeStatus.REFUNDED);
        });

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
        allRefundedTicket.forEach(eventTickets -> {
            assertEquals(eventTickets1.getPaidAmount(), eventTickets.getRefundedAmount());
            assertEquals(eventTickets.getRefundedAEFee(), eventTickets1.getAeFeeAmount());
            assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.REFUNDED);
        });
    }

    @Test
    void testSingleTicketWithFullRefundFromMultipleCharge() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);

        ticketingTypePaid.setRecurringEventId(1L);


        eventTickets1.setId(2L);
        eventTickets1.setPaidAmount(158.45);
        eventTickets1.setTicketPrice(150.00);
        eventTickets1.setRefundedAmount(0.00);
        eventTickets1.setAeFeeAmount(3.24985);
        eventTickets1.setChargeId("ch_1");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets1);

        //For refund Paid ticket
        refundInfo.setEventTicketingId(eventTickets1.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(158.45);


        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        List <EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions.setTicketingOrderId(1L);
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(105.88);
        eventTicketTransactions.setChargeId("ch_1");
        eventTicketTransactions.setCcFeeAmount(3.37);
        eventTicketTransactions.setAeFeeAmount(2.5);
        eventTicketTransactions.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions);

        EventTicketTransaction eventTicketTransactions1 = new EventTicketTransaction();
        eventTicketTransactions1.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions1.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions1.setTicketingOrderId(1L);
        eventTicketTransactions1.setPaidAmount(52.57);
        eventTicketTransactions1.setChargeId("ch_2");
        eventTicketTransactions1.setCcFeeAmount(1.82);
        eventTicketTransactions1.setAeFeeAmount(0.74985);
        eventTicketTransactions1.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions1);


        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setNumberofticket(1);
        orderManager.setTicketType(eventTickets.getTicketingTypeId());
        ticketingOrderManagers.add(orderManager);

        TicketingOrderManager orderManager2 = new TicketingOrderManager();
        orderManager2.setTicketType(eventTickets1.getTicketingTypeId());
        orderManager2.setNumberofticket(1);
        ticketingOrderManagers.add(orderManager2);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockEventTicketTransactions(eventTicketTransactionList);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doNothing().when(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
        when(paymentHandlerService.startRefund(anyLong(), any(), any(), anyBoolean(), anyBoolean(), anyList(), anyMap(), anyDouble(), anyDouble(), anyDouble(), anyDouble(), anyString())).thenReturn(stripeTransaction);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        List<EventTickets> allRefundedTicket = ticketsArgumentCaptor.getValue();

        ArgumentCaptor<List<EventTicketTransaction>> ticketsTransactionArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionService, Mockito.times(1)).saveAll(ticketsTransactionArgumentCaptor.capture());
        List<EventTicketTransaction> allValues = ticketsTransactionArgumentCaptor.getAllValues().get(0);

        allValues.forEach(eventTicketTransaction -> {
            assertEquals(eventTicketTransaction.getChargeStatus(), EventTicketTransaction.TicketTransactionChargeStatus.REFUNDED);
        });


        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
        allRefundedTicket.forEach(eventTickets -> {
            assertEquals(eventTickets1.getPaidAmount(), eventTickets.getRefundedAmount());
            assertEquals(eventTickets.getRefundedAEFee(), eventTickets1.getAeFeeAmount());
            assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.REFUNDED);
        });
    }


    @Test
    void testSingleTicketWithPartialRefundFromMultipleCharge() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);

        ticketingTypePaid.setRecurringEventId(1L);


        eventTickets1.setId(2L);
        eventTickets1.setPaidAmount(158.45);
        eventTickets1.setTicketPrice(150.00);
        eventTickets1.setRefundedAmount(0.00);
        eventTickets1.setAeFeeAmount(3.24985);
        eventTickets1.setChargeId("ch_1");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets1);

        //For refund Paid ticket
        refundInfo.setEventTicketingId(eventTickets1.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(125.00);


        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        List <EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions.setTicketingOrderId(1L);
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(105.88);
        eventTicketTransactions.setChargeId("ch_1");
        eventTicketTransactions.setCcFeeAmount(3.37);
        eventTicketTransactions.setAeFeeAmount(2.5);
        eventTicketTransactions.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions);

        EventTicketTransaction eventTicketTransactions1 = new EventTicketTransaction();
        eventTicketTransactions1.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions1.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions1.setTicketingOrderId(1L);
        eventTicketTransactions1.setPaidAmount(52.57);
        eventTicketTransactions1.setChargeId("ch_2");
        eventTicketTransactions1.setCcFeeAmount(1.82);
        eventTicketTransactions1.setAeFeeAmount(0.74985);
        eventTicketTransactions1.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions1);


        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setNumberofticket(1);
        orderManager.setTicketType(eventTickets.getTicketingTypeId());
        ticketingOrderManagers.add(orderManager);

        TicketingOrderManager orderManager2 = new TicketingOrderManager();
        orderManager2.setTicketType(eventTickets1.getTicketingTypeId());
        orderManager2.setNumberofticket(1);
        ticketingOrderManagers.add(orderManager2);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockEventTicketTransactions(eventTicketTransactionList);
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doNothing().when(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
        when(paymentHandlerService.startRefund(anyLong(), any(), any(), anyBoolean(), anyBoolean(), anyList(), anyMap(), anyDouble(), anyDouble(), anyDouble(), anyDouble(), anyString())).thenReturn(stripeTransaction);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        List<EventTickets> allRefundedTicket = ticketsArgumentCaptor.getValue();

        ArgumentCaptor<List<EventTicketTransaction>> ticketsTransactionArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionService, Mockito.times(1)).saveAll(ticketsTransactionArgumentCaptor.capture());
        List<EventTicketTransaction> allValues = ticketsTransactionArgumentCaptor.getAllValues().get(0);

        allValues.forEach(eventTicketTransaction -> {
            if(eventTicketTransaction.getChargeId().equals("ch_1"))
                assertEquals(eventTicketTransaction.getChargeStatus(), EventTicketTransaction.TicketTransactionChargeStatus.REFUNDED);
            else
                assertEquals(eventTicketTransaction.getChargeStatus(), EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        });


        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        allRefundedTicket.forEach(eventTickets -> {
            assertEquals(125.0, eventTickets.getRefundedAmount());
            assertEquals(GeneralUtils.getRoundValue(eventTickets.getRefundedAEFee()), GeneralUtils.getRoundValue(2.91));
            assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.PARTIALLY_REFUNDED);
        });
    }


    @Test
    void testRefundMultipleTicketsWithFullRefundFromSingleCharge() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);

        ticketingTypePaid.setRecurringEventId(1L);


        eventTickets1.setId(2L);
        eventTickets1.setPaidAmount(105.72);
        eventTickets1.setTicketPrice(100.00);
        eventTickets1.setRefundedAmount(0.00);
        eventTickets1.setAeFeeAmount(2.5);
        eventTickets1.setChargeId("ch_1");
        eventTickets1.setTicketingTypeId(ticketingTypePaid);

        eventTickets2.setId(3L);
        eventTickets2.setPaidAmount(105.72);
        eventTickets2.setTicketPrice(100.00);
        eventTickets2.setRefundedAmount(0.00);
        eventTickets2.setAeFeeAmount(2.5);
        eventTickets2.setChargeId("ch_1");
        eventTickets2.setTicketingTypeId(ticketingTypePaid);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets1);
        eventTicketList.add(eventTickets2);

        //For refund Paid ticket
        refundInfo.setEventTicketingId(eventTickets1.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(105.72);

        refundInfo1.setEventTicketingId(eventTickets2.getId());
        refundInfo1.setQty(1);
        refundInfo1.setRefundAmount(105.72);

        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);

        List <EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(105.72);
        eventTicketTransactions.setChargeId("ch_1");
        eventTicketTransactions.setCcFeeAmount(3.22);
        eventTicketTransactions.setAeFeeAmount(2.5);
        eventTicketTransactions.setPaymentType(EventTicketTransaction.PaymentType.CARD);

        eventTicketTransactionList.add(eventTicketTransactions);


        EventTicketTransaction eventTicketTransactions1 = new EventTicketTransaction();
        eventTicketTransactions1.setEventTicketId(eventTickets2.getId());
        eventTicketTransactions1.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions1.setPaidAmount(105.72);
        eventTicketTransactions1.setChargeId("ch_1");
        eventTicketTransactions1.setCcFeeAmount(3.22);
        eventTicketTransactions1.setAeFeeAmount(2.5);
        eventTicketTransactions1.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions1);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();

        TicketingOrderManager orderManager2 = new TicketingOrderManager();
        orderManager2.setTicketType(eventTickets1.getTicketingTypeId());
        orderManager2.setNumberofticket(2);
        ticketingOrderManagers.add(orderManager2);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockEventTicketTransactions(eventTicketTransactionList);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doNothing().when(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
        when(paymentHandlerService.startRefund(anyLong(), any(), any(), anyBoolean(), anyBoolean(), anyList(), anyMap(), anyDouble(), anyDouble(), anyDouble(), anyDouble(), anyString())).thenReturn(stripeTransaction);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        List<EventTickets> allRefundedTicket = ticketsArgumentCaptor.getValue();

        ArgumentCaptor<List<EventTicketTransaction>> ticketsTransactionArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionService, Mockito.times(1)).saveAll(ticketsTransactionArgumentCaptor.capture());
        List<EventTicketTransaction> allValues = ticketsTransactionArgumentCaptor.getAllValues().get(0);

        allValues.forEach(eventTicketTransaction -> {
            assertEquals( EventTicketTransaction.TicketTransactionChargeStatus.REFUNDED,eventTicketTransaction.getChargeStatus());
        });

        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
        allRefundedTicket.forEach(eventTickets -> {
            assertEquals(eventTickets.getPaidAmount(), eventTickets.getRefundedAmount());
            assertEquals(eventTickets.getRefundedAEFee(), eventTickets.getAeFeeAmount());
            assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.REFUNDED);
        });
    }

    @Test
    void testRefundWithMultipleTicketWithMultipleChargeCashAndCard() throws StripeException, ApiException, IOException {
        // setup
        ticketingOrder.setPurchaser(purchaser);
        eventTickets1.setId(2L);
        eventTickets1.setRefundedAmount(0.00);
        eventTickets1.setAeFeeAmount(2.5);
        eventTickets1.setChargeId("ch_1");
        eventTickets1.setPaidAmount(105.88);
        eventTickets1.setTicketPrice(100);
        eventTickets1.setTicketingTypeId(ticketingTypePaid);

        eventTickets2.setId(3L);
        eventTickets2.setRefundedAmount(0.00);
        eventTickets2.setAeFeeAmount(0.00);
        eventTickets2.setTicketPrice(100.00);
        eventTickets2.setPaidAmount(100.00);
        eventTickets2.setChargeId(null);
        eventTickets2.setTicketingTypeId(ticketingTypePaid);


        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets1);
        eventTicketList.add(eventTickets2);
        //For refund free ticket
        refundInfo1.setEventTicketingId(eventTickets1.getId());
        refundInfo1.setQty(1);
        refundInfo1.setRefundAmount(105.88);
        //For refund Paid ticket
        refundInfo.setEventTicketingId(eventTickets2.getId());
        refundInfo.setQty(1);
        refundInfo.setRefundAmount(100.00);


        List<RefundInfo> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(refundInfo);
        attendeeDtos.add(refundInfo1);

        List <EventTicketTransaction> eventTicketTransactionList = new ArrayList<>();
        EventTicketTransaction eventTicketTransactions = new EventTicketTransaction();
        eventTicketTransactions.setEventTicketId(eventTickets1.getId());
        eventTicketTransactions.setTicketingOrderId(1L);
        eventTicketTransactions.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransactions.setPaidAmount(105.88);
        eventTicketTransactions.setChargeId("ch_1");
        eventTicketTransactions.setAeFeeAmount(2.5);
        eventTicketTransactions.setCcFeeAmount(3.37);
        eventTicketTransactions.setPaymentType(EventTicketTransaction.PaymentType.CARD);
        eventTicketTransactionList.add(eventTicketTransactions);

        EventTicketTransaction eventTicketTransaction1 = new EventTicketTransaction();
        eventTicketTransaction1.setEventTicketId(eventTickets2.getId());
        eventTicketTransaction1.setTicketingOrderId(1L);
        eventTicketTransaction1.setChargeStatus(EventTicketTransaction.TicketTransactionChargeStatus.PAID);
        eventTicketTransaction1.setPaidAmount(100.00);
        eventTicketTransaction1.setChargeId(Constants.STRING_EMPTY);
        eventTicketTransaction1.setPaymentType(EventTicketTransaction.PaymentType.CASH);
        eventTicketTransactionList.add(eventTicketTransaction1);

        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        TicketingOrderManager orderManager = new TicketingOrderManager();
        orderManager.setNumberofticket(2);
        orderManager.setTicketType(eventTickets1.getTicketingTypeId());
        ticketingOrderManagers.add(orderManager);

        //mock
        mockTicketingOrder(ticketingOrder);
        mockListOngoingStubbing(eventTicketList, ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        mockEventTicketTransactions(eventTicketTransactionList);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagers);
        when(ticketingTypeCommonRepo.findByid(anyLong())).thenReturn(ticketingTypePaid);
        doNothing().when(seatsIoService).changeStatus(anyString(), anyList(), anyString());
        doCallRealMethod().when(ticketingRefundService).createTicketHolderEmailDto(any(), any());
        doNothing().when(attendeeProfileService).deleteAttendeeProfile(anyString(),anyString());
        when(paymentHandlerService.startRefund(anyLong(), any(), any(), anyBoolean(), anyBoolean(), anyList(), anyMap(), anyDouble(), anyDouble(), anyDouble(), anyDouble(), anyString())).thenReturn(stripeTransaction);
        doReturn(new PurchaserInfo()).when(ticketingRefundService).getPurchaserInfo(any(), any());
        mockFindByEvent();

        mockTicketingOngoingStubbing(event);

        //execution
        ticketingRefundService.refund(1, event, attendeeDtos, new Date(), true, true,user, false, Constants.STRING_EMPTY,false,true, true);

        ArgumentCaptor<List<EventTickets>> ticketsArgumentCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(eventCommonRepoService, Mockito.times(1)).saveAll(ticketsArgumentCaptor.capture());
        List<EventTickets> allRefundedTicket = ticketsArgumentCaptor.getValue();


        ArgumentCaptor<EventTicketRefundTracker> refundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundService, Mockito.times(1)).saveAll(Collections.singletonList(refundTrackerArgumentCaptor.capture()));

        ArgumentCaptor<List<EventTicketTransaction>> ticketsTransactionArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(eventTicketTransactionService, Mockito.times(2)).saveAll(ticketsTransactionArgumentCaptor.capture());
        List<EventTicketTransaction> allValues = ticketsTransactionArgumentCaptor.getAllValues().get(0);

        allValues.forEach(eventTicketTransaction -> {
            assertEquals( EventTicketTransaction.TicketTransactionChargeStatus.REFUNDED,eventTicketTransaction.getChargeStatus());
        });

        ArgumentCaptor<RefundTransaction> refundTransactionArgumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService, Mockito.times(1)).save(refundTransactionArgumentCaptor.capture());

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
        allRefundedTicket.forEach(eventTickets -> {
            assertEquals(eventTickets.getTicketPaymentStatus(), TicketPaymentStatus.REFUNDED);
        });
    }

}
