package com.accelevents.services.impl;

import com.accelevents.common.dto.SubcriptionPlanDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.dto.DonationPurchaseDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.StripeIntervalEnum;
import com.accelevents.services.StripePaymentService;
import com.accelevents.services.StripeService;
import com.accelevents.utils.Constants;
import com.stripe.exception.StripeException;
import com.stripe.model.Plan;
import com.stripe.model.Product;
import com.stripe.model.Subscription;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class StripePaymentHandlerServiceImplTest {

    @Spy
    @InjectMocks
    private StripePaymentHandlerServiceImpl stripePaymentHandlerServiceImpl = new StripePaymentHandlerServiceImpl();
    @Mock
    private StripePaymentService stripePaymentService;

    @Mock
    private StripeConfiguration stripeConfiguration;

    @Mock
    private StripeService stripeService;

    private Stripe stripe;
    private StripeTransaction stripeTransaction;
    private Event event;
    private DonationPurchaseDto donationPurchaseDto;
    private DonationSettings donationSettings;
    private Donation donation;
    private Plan plan;
    private StripeDTO stripeDTO;
    private User user;
    private BiddingSource biddingSource;
    private Subscription subscription;
    private Product product;

    private String customerId = "cus_FSjuTT9VAXQn8x";
    private String stripeAccessToken = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private String subscriptionId = "sub_FVRS57a8mj0MJ7";
	private String currency = "USD";
    private String note = "note";
    private double amount = 100d;
    private double ccPercentageFee = CREDIT_CARD_PROCESSING_PERCENTAGE;
    private double ccFlatFee = CREDIT_CARD_PROCESSING_FLAT;
	private Date date = new Date();

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();

        stripe = new Stripe();

        user = EventDataUtil.getUser();

        stripeTransaction = new StripeTransaction();

        donationPurchaseDto = new DonationPurchaseDto();

        donationSettings = new DonationSettings();

        plan = new Plan();

        stripeDTO = new StripeDTO(ccFlatFee, ccPercentageFee, "US", EnumPaymentGateway.STRIPE.name());

        subscription = new Subscription();

        product = new Product();
    }

    @Test
    void createRecurringPlanAndSubscribeToPlan() throws StripeException{

        //setup
        String planId = "plan_FVRMR4BM5GQTTA";
        donationPurchaseDto.setRecurringDonation(true);
        donationPurchaseDto.setAmount(amount);

        stripe.setAccessToken(stripeAccessToken);
        stripe.setCCFlatFee(ccFlatFee);
        stripe.setCCPercentageFee(ccPercentageFee);

        stripeTransaction.setStripecustomerid(customerId);

        donationSettings.setAbsorbFee(false);

        donation = new Donation(user, event.getEventId(), amount, BiddingSource.ONLINE, date, true, true, amount, note, user, true);

		Long planAmount = 29L;
		plan.setAmount(planAmount);
        plan.setId(planId);

        subscription.setId(subscriptionId);

		String productId = "prod_FVRZhck5T6nI9X";
		product.setId(productId);

        //mock
        when(stripeConfiguration.getAPI_KEY()).thenReturn(stripeAccessToken);
        when(stripeConfiguration.getTextToGiveProductId()).thenReturn(productId);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(stripePaymentService.createRecurringStripePlan(stripeConfiguration.getAPI_KEY(), Constants.TEXT_TO_GIVE, StripeIntervalEnum.MONTH, event.getCurrency(), 29, false, product.getId(), stripe.getCCPercentageFee(), stripe.getCCFlatFee())).thenReturn(plan);
        when(stripePaymentService.subscribeStripePlanToCustomer(stripe.getAccessToken(), stripeTransaction.getStripecustomerid(), plan.getId(), "pm_card_visa",event.getEventURL())).thenReturn(subscription);

        //Execution
        SubcriptionPlanDto planData = stripePaymentHandlerServiceImpl.createRecurringPlanAndSubscribeToPlan(customerId, event, "pm_card_visa");
        assertEquals(planData.getPlanAmount(), planAmount.longValue());
        assertEquals(planData.getSubscriptionId(), subscriptionId);
        assertNotNull(planData);
    }

    @Test
    void test_handleStripeForRecurringDonation_success() throws StripeException {

        //setup
        String planId = "plan_FVRMR4BM5GQTTA";
        donationPurchaseDto.setRecurringDonation(true);
        donationPurchaseDto.setAmount(amount);

        stripe.setAccessToken(stripeAccessToken);
        stripe.setCCFlatFee(ccFlatFee);
        stripe.setCCPercentageFee(ccPercentageFee);
        stripe.setProcessingFeesToPurchaser(true);
        stripeTransaction.setStripecustomerid(customerId);

        //donationSettings.setAbsorbFee(false);

        donation = new Donation(user, event.getEventId(), amount, BiddingSource.ONLINE, date, true, true, amount, note, user, true);

        plan.setAmount(29L);
        plan.setId(planId);

        subscription.setId(subscriptionId);

        String planName = ( stripe.isProcessingFeesToPurchaser() ? Constants.TEXT_TO_GIVE_WITH_FEE_PREFIX : Constants.TEXT_TO_GIVE_WITHOUT_FEE_PREFIX ) + donationPurchaseDto.getAmount() + "_" + event.getCurrency().getISOCode();

        //mock
        when(stripePaymentService.createRecurringStripePlan(stripe.getAccessToken(), planName, StripeIntervalEnum.MONTH, event.getCurrency(), donationPurchaseDto.getAmount(), true, null, stripe.getCCPercentageFee(), stripe.getCCFlatFee())).thenReturn(plan);
        when(stripePaymentService.subscribeStripePlanToCustomer(stripe.getAccessToken(), stripeTransaction.getStripecustomerid(), plan.getId(), "pm_card_visa",event.getEventURL())).thenReturn(subscription);

        //Execution
        stripePaymentHandlerServiceImpl.handleStripeForRecurringDonation(stripe, stripeTransaction, true, event, donationPurchaseDto, donationSettings, donation, "pm_card_visa");
    }

    @Test
    void test_handleStripeForRecurringDonation_success_with_donationAbsorbFee_true() throws StripeException {

        //setup
        donationPurchaseDto.setRecurringDonation(true);
        donationPurchaseDto.setAmount(amount);

        stripe.setAccessToken(stripeAccessToken);
        stripe.setCCFlatFee(ccFlatFee);
        stripe.setCCPercentageFee(ccPercentageFee);
        stripe.setProcessingFeesToPurchaser(false);
        stripeTransaction.setStripecustomerid(customerId);

        donationSettings.setAbsorbFee(true);

        donation = new Donation(user, event.getEventId(), amount, BiddingSource.ONLINE, date, true, true, amount, note, user, true);

        plan.setAmount(29L);
        plan.setId("");

        subscription.setId(subscriptionId);

        String planName = ( stripe.isProcessingFeesToPurchaser() ? Constants.TEXT_TO_GIVE_WITH_FEE_PREFIX : Constants.TEXT_TO_GIVE_WITHOUT_FEE_PREFIX ) + donationPurchaseDto.getAmount() + "_" + event.getCurrency().getISOCode();

        //mock
        when(stripePaymentService.createRecurringStripePlan(stripe.getAccessToken(), planName, StripeIntervalEnum.MONTH, event.getCurrency(), donationPurchaseDto.getAmount(), true, null, stripe.getCCPercentageFee(), stripe.getCCFlatFee())).thenReturn(plan);

        //Execution
        stripePaymentHandlerServiceImpl.handleStripeForRecurringDonation(stripe, stripeTransaction, true, event, donationPurchaseDto, donationSettings, donation, "pm_card_visa");
    }

    @Test
    void test_handleStripeForRecurringDonation_success_with_recurringDonation_false() throws StripeException {

        //setup
        donationPurchaseDto.setRecurringDonation(false);
        donationPurchaseDto.setAmount(amount);

        //Execution
        stripePaymentHandlerServiceImpl.handleStripeForRecurringDonation(stripe, stripeTransaction, true, event, donationPurchaseDto, donationSettings, donation, "pm_card_visa");
    }
}