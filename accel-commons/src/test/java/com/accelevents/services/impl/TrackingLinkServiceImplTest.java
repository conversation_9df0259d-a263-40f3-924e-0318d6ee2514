package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.TrackingLinks;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.TrackingLinkDto;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.TrackingLinkRepository;
import com.accelevents.services.TicketingOrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TrackingLinkServiceImplTest {

    @Spy
    @InjectMocks
    private TrackingLinkServiceImpl trackingLinkServiceImpl =new TrackingLinkServiceImpl();

    @Mock
    TrackingLinkRepository trackingLinkRepository;

    @Mock
    TicketingOrderService ticketingOrderService;

    Event event;
    User user;

    private String linkUrl="test1";
    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(1L);
        event.setCurrency(Currency.AUD);
        event.setEventURL("asd");
    }

    @Test
    void test_save_success() {
        //setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.save(isA(TrackingLinks.class))).thenReturn(trackingLinks);
        //Execute
        trackingLinkServiceImpl.save(event, linkUrl);
        verify(trackingLinkRepository).save(isA(TrackingLinks.class));
    }

    @Test
    void test_checkDuplicateEventLink_success() {
        //Setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.of(trackingLinks));
        //Execution
        boolean actual= trackingLinkServiceImpl.checkDuplicateEventLink(event, linkUrl);
        //Assertion
        assertTrue(actual);

    }


    @Test
    void test_checkDuplicateEventLink_throwConflictException() {
        //Execution
        try{
            trackingLinkServiceImpl.checkDuplicateEventLink(event, null);
            fail("Test failed");
        } catch (ConflictException e){
            //Assertion
            assertEquals(ConflictException.TrackingLinkExceptionMsg.LINK_CAN_NOT_BE_EMPTY.getErrorMessage(), e.getErrorMessage());
        } catch (Exception e){
            fail("Test failed");
        }

    }

    @Test
    void test_updateLinkUrl_success() {
        //setUp
        Long id=1L;
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findById(id)).thenReturn(Optional.of(trackingLinks));
        trackingLinks.setLinkUrl("test2");
        when( trackingLinkRepository.save(trackingLinks)).thenReturn(trackingLinks);
        //Execute
        trackingLinkServiceImpl.updateLinkUrl(event,id,linkUrl);
        ArgumentCaptor<TrackingLinks> trackingLinksArgumentCaptor=ArgumentCaptor.forClass(TrackingLinks.class);
        verify(trackingLinkRepository, times(1)).save(trackingLinksArgumentCaptor.capture());
        //Verify
        TrackingLinks actual = trackingLinksArgumentCaptor.getValue();
        assertEquals(linkUrl,actual.getLinkUrl());

    }


    @Test
    void test_incrementTrackingLinkVisitCount_throwNotFoundException() {
        //Mock
        when(trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.empty());
        //Execution
        try{
            trackingLinkServiceImpl.incrementTrackingLinkVisitCount(linkUrl,event);
            fail("Test failed");
        } catch (NotFoundException e){
            //Assertion
            assertEquals(NotFoundException.NotFound.TRACKING_URL_NOT_FOUND.getErrorMessage(), e.getErrorMessage());
        } catch (Exception e){
            fail("Test failed");
        }

    }
    @Test
    void test_incrementTrackingLinkVisitCount_success() {
        //Setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.of(trackingLinks));
        //Execution
        trackingLinkServiceImpl.incrementTrackingLinkVisitCount(linkUrl,event);
        ArgumentCaptor<TrackingLinks> trackingLinksArgumentCaptor=ArgumentCaptor.forClass(TrackingLinks.class);
        verify(trackingLinkRepository, times(1)).save(trackingLinksArgumentCaptor.capture());

    }

    @Test
    void test_createTrackingLink_throwConflictException() {
        //Execution
        try{
            trackingLinkServiceImpl.createTrackingLink(event, null);
            fail("Test failed");
        } catch (ConflictException e){
            //Assertion
            assertEquals(ConflictException.TrackingLinkExceptionMsg.LINK_CAN_NOT_BE_EMPTY.getErrorMessage(), e.getErrorMessage());
        } catch (Exception e){
            fail("Test failed");
        }
    }
    @Test
    public  void test_createTrackingLing_throwConflictException(){
        //Setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when( trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.of(trackingLinks));
        //Execute
        try{
            trackingLinkServiceImpl.createTrackingLink(event,linkUrl);
            fail("Test failed");
        } catch (ConflictException e){
            //Assertion
            assertEquals(ConflictException.TrackingLinkExceptionMsg.LINK_EXIST_FOR_EVENT.getErrorMessage(), e.getErrorMessage());
        } catch (Exception e){
            fail("Test failed");
        }
    }

    @Test
    public  void test_createTrackingLing_success(){
        //setUp
        Long id=1L;
        TrackingLinks trackingLinks=new  TrackingLinks();
        trackingLinks.setLinkUrl(linkUrl);
        trackingLinks.setEvent(event);
        //Mock
        when( trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.empty());
        when( trackingLinkRepository.save(any())).thenReturn(trackingLinks);

        //Execute
        String trackingLink = trackingLinkServiceImpl.createTrackingLink(event, linkUrl);
        ArgumentCaptor<TrackingLinks> trackingLinksArgumentCaptor=ArgumentCaptor.forClass(TrackingLinks.class);
        verify(trackingLinkRepository, times(1)).save(trackingLinksArgumentCaptor.capture());
        //Verify
        TrackingLinks actual = trackingLinksArgumentCaptor.getValue();
    }

    @Test
    void test_updateTrackingLink_success() {
        //setUp
        Long id=1L;
        TrackingLinks trackingLinks=new  TrackingLinks();
        trackingLinks.setLinkUrl(linkUrl);
        trackingLinks.setEvent(event);
        //Mock
        when(trackingLinkRepository.findById(id)).thenReturn(Optional.of(trackingLinks));
        when( trackingLinkRepository.save(trackingLinks)).thenReturn(trackingLinks);
        when( trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.empty());
        //Execute
        trackingLinkServiceImpl.updateTrackingLink(event,id,linkUrl);
        ArgumentCaptor<TrackingLinks> trackingLinksArgumentCaptor=ArgumentCaptor.forClass(TrackingLinks.class);
        verify(trackingLinkRepository, times(1)).save(trackingLinksArgumentCaptor.capture());
        //Verify
        TrackingLinks actual = trackingLinksArgumentCaptor.getValue();
        assertEquals(linkUrl,actual.getLinkUrl());
    }
    @Test
    void test_updateTrackingLink_throwConflictException() {
        //setUp
        Long id=1L;
        TrackingLinks trackingLinks=new  TrackingLinks();
        trackingLinks.setLinkUrl(linkUrl);
        trackingLinks.setEvent(event);
        //Mock
        when( trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.of(trackingLinks));
        //Execute
        try{
            trackingLinkServiceImpl.updateTrackingLink(event,id,linkUrl);
            fail("Test failed");
        } catch (ConflictException e){
            //Assertion
            assertEquals(ConflictException.TrackingLinkExceptionMsg.LINK_EXIST_FOR_EVENT.getErrorMessage(), e.getErrorMessage());
        } catch (Exception e){
            fail("Test failed");
        }
    }


    @Test
    void test_disableTrackingLink_success() {
        Long id=1L;
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findById(id)).thenReturn(Optional.of(trackingLinks));
        trackingLinks.setActive(false);
        //Execution
        trackingLinkServiceImpl.disableTrackingLink(id);
        ArgumentCaptor<TrackingLinks> trackingLinksArgumentCaptor=ArgumentCaptor.forClass(TrackingLinks.class);
        verify(trackingLinkRepository, times(1)).save(trackingLinksArgumentCaptor.capture());
        TrackingLinks actual = trackingLinksArgumentCaptor.getValue();
        assertEquals(false,actual.getActive());
    }

    private TrackingLinks createTrackingLinks() {
        TrackingLinks trackingLinks = new TrackingLinks();
        trackingLinks.setLinkUrl(linkUrl);
        trackingLinks.setEvent(event);
        trackingLinks.setVisitCount(0);
        trackingLinks.setActive(true);
        return trackingLinks;
    }

    @Test
    void test_deleteTrackingLink_success(){
        //Setup
        Long id=1L;
        TrackingLinks trackingLinks = createTrackingLinks();
        when(trackingLinkRepository.findById(id)).thenReturn(Optional.of(trackingLinks));
        doNothing().when(ticketingOrderService).setReferencesToNull(trackingLinks);
        //Execution
        trackingLinkServiceImpl.deleteTrackingLink(id);
    }

    @Test
    void test_getAllTrackingLinkForEvent_success(){
        //Setup
        Long id=1L;
        List<TrackingLinkDto> trackingLinkList=new ArrayList<TrackingLinkDto>();
        TrackingLinkDto trackingLinkDto = new TrackingLinkDto();
        trackingLinkDto.setId(1L);
        trackingLinkDto.setLinkUrl(linkUrl);
        trackingLinkDto.setVisitCount(0L);
        trackingLinkList.add(trackingLinkDto);
        List<Long> trackingLinkIds = trackingLinkList.stream().map(TrackingLinkDto::getId).collect(Collectors.toList());

        //Mock
        when(trackingLinkRepository.findAllTrackingLinks(trackingLinkIds,1L)).thenReturn(trackingLinkList);

        //Execution
        trackingLinkServiceImpl.getAllTrackingLinkForEvent(trackingLinkIds,1L);
    }

    @Test
    void test_getTrackingLink_success(){
        //Setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.of(trackingLinks));
        //Execution
        trackingLinkServiceImpl.getTrackingLink(event,linkUrl);
    }

    @Test
    void test_getTrackingLink_success1(){
        //Setup
        TrackingLinks trackingLinks = createTrackingLinks();
        //Mock
        when(trackingLinkRepository.findByEventAndLinkUrl(event,linkUrl)).thenReturn(Optional.empty());
        //Execution
        TrackingLinks obj = trackingLinkServiceImpl.getTrackingLink(event,linkUrl);
        assertNull(obj);
    }
}