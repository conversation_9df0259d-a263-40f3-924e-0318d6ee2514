package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.TicketingTypeCommonRepo;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.CategoryDto;
import com.accelevents.ticketing.dto.EventCategoryDto;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.accelevents.utils.Constants.GENERAL_ADMISSION;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingTypeServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(TicketingTypeServiceImpl.class);

    @Spy
    @InjectMocks
    private TicketingTypeServiceImpl ticketingTypeServiceImpl;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    SeatingCategoryService seatingCategoryService;
    @Mock
    TicketingManageService ticketingManageService;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    private Event event;
	private Ticketing ticketing;
	private TicketingType ticketingType1, ticketingType2;
    private RecurringEvents recurringEvents;
	private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private User user;

    private Long id = 1L;
    private double position1 = 1000d;
	private double sequence = 1000;

    @BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();
		EventTickets eventTickets = EventDataUtil.getEventTickets();
		TicketingOrder ticketingOrder = EventDataUtil.getTicketingOrder();
		user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        ticketingType1 = EventDataUtil.getTicketingType(event);
        ticketingType2 = EventDataUtil.getTicketingType(event);
        recurringEvents = EventDataUtil.getRecurringEvents();
    }

    @Test
    void test_save_success_with_ticketingTypeId_notEqualToZero() {

        //setup
        ticketingType1.setPosition(position1);

        //mock
        when(ticketingTypeCommonRepo.save(ticketingType1)).thenReturn(ticketingType1);

        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        TicketingType ticketingTypeData = ticketingTypeServiceImpl.save(ticketingType1);
        assertEquals(ticketingType1.getId(), ticketingTypeData.getId());
        assertEquals(position1, ticketingTypeData.getPosition());
    }

    //@Test
    void test_save_success_with_ticketingTypeId_equalToZero_with_lastItemNull() {

        //setup
        ticketingType1.setPosition(position1);
        ticketingType1.setId(0L);

        //mock
        Mockito.doReturn(ticketingType1).when(ticketingTypeServiceImpl).save(ticketingType1);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());
        Mockito.doNothing().when(ticketingTypeTicketService).setPositionForTicketingType(any());
        Mockito.doNothing().when(ticketingTypeCommonRepo).save(any());

        //Execution
        TicketingType ticketingTypeData = ticketingTypeServiceImpl.setPositionForTicketingTypeAndsaveTicketingType(ticketingType1);
        assertEquals(ticketingTypeData.getPosition(), position1);
    }

  //  @Test
    void test_save_success_with_ticketingTypeId_equalToZero_with_lastItemNotNull() {

        //setup
        ticketingType1.setId(0L);
        double position = ticketingType1.getPosition() + sequence;

        //mock
        when(ticketingTypeServiceImpl.save(ticketingType1)).thenReturn(ticketingType1);
        Mockito.doReturn(ticketingType1).when(ticketingTypeTicketService).getLastItem(ticketingType1);

        //Execution
        TicketingType ticketingTypeData = ticketingTypeServiceImpl.setPositionForTicketingTypeAndsaveTicketingType(ticketingType1);
        assertEquals(ticketingTypeData.getPosition(), position);
    }

    @Test
    void test_updatewithsequence_success_topTicketingType1_notNull_with_position_notEqualOne() {

        //setup
        long topId = 1L;
        long topBottomId = 2L;

        ticketingType1.setPosition(position1);
		double position2 = 2000d;
		ticketingType2.setPosition(position2);
        double position = (ticketingType1.getPosition() + ticketingType2.getPosition())/2;

        //mock
        when(ticketingTypeCommonRepo.findByid(topId)).thenReturn(ticketingType1);
        when(ticketingTypeCommonRepo.findByid(topBottomId)).thenReturn(ticketingType2);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeServiceImpl.updatewithsequence(ticketingType1, topId, topBottomId, event, user);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeCommonRepo,times(1)).save(ticketingTypeArgumentCaptor.capture());
        TicketingType actualData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(actualData.getPosition(), position);
    }

    @Test
    void test_updatewithsequence_success_topTicketingType1_notNull_with_position_equalOne() {

        //setup
        long topId = 1L;
        long topBottomId = 2L;

        ticketingType1.setPosition(1);
        ticketingType2.setPosition(1);

        //mock
        when(ticketingTypeCommonRepo.findByid(topId)).thenReturn(ticketingType1);
        when(ticketingTypeCommonRepo.findByid(topBottomId)).thenReturn(ticketingType2);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());
        Mockito.doReturn(ticketingType1).when(ticketingTypeServiceImpl).getNextPositionItem(ticketingType1,event);
        Mockito.doReturn(ticketingType2).when(ticketingTypeServiceImpl).getPreviousPositionItem(any(), any());

        //Execution
        ticketingTypeServiceImpl.updatewithsequence(ticketingType1, topId, topBottomId, event, user);

        ArgumentCaptor<TicketingType> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeCommonRepo,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingType actualData = ticketingArgumentCaptor.getValue();
        assertEquals(actualData.getPosition(), ticketingType2.getPosition());
    }

    @Test
    void test_updatewithsequence_success_topTicketingType1_Null() {

        //setup
        long topId = 1L;
        long topBottomId = 2L;

        ticketingType1.setPosition(1);
        ticketingType2.setPosition(1);
        event.setTicketingId(1L);

        //mock
        when(ticketingTypeCommonRepo.findByid(topId)).thenReturn(null);
        when(ticketingTypeCommonRepo.findByid(topBottomId)).thenReturn(ticketingType2);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeServiceImpl.updatewithsequence(ticketingType1, topId, topBottomId, event, user);

        ArgumentCaptor<TicketingType> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeCommonRepo,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingType actualData = ticketingArgumentCaptor.getValue();
        assertEquals(actualData.getPosition() - ticketingType2.getPosition(), sequence);
    }

    @Test
    void test_updatewithsequence_success_topTicketingType2_Null() {

        //setup
        long topId = 1L;
        long topBottomId = 2L;

        ticketingType1.setPosition(1);
        ticketingType2.setPosition(1);
        event.setTicketingId(1L);

        double position = ticketingType1.getPosition() + sequence;

        //mock
        when(ticketingTypeCommonRepo.findByid(topId)).thenReturn(ticketingType1);
        when(ticketingTypeCommonRepo.findByid(topBottomId)).thenReturn(null);
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeServiceImpl.updatewithsequence(ticketingType1, topId, topBottomId, event, user);

        ArgumentCaptor<TicketingType> ticketingArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeCommonRepo,times(1)).save(ticketingArgumentCaptor.capture());
        TicketingType actualData = ticketingArgumentCaptor.getValue();
        assertEquals(actualData.getPosition(), position - ticketingType2.getPosition());
    }

    @Test
    void test_updatewithsequence_success() {

        //setup
        Long topId = 1L;
        Long topBottomId = 2L;

        ticketingType1.setPosition(1);
        ticketingType2.setPosition(1);
        event.setTicketingId(1L);

        //mock
        when(ticketingTypeCommonRepo.findByid(topId)).thenReturn(null);
        when(ticketingTypeCommonRepo.findByid(topBottomId)).thenReturn(null);

        //Execution
        ticketingTypeServiceImpl.updatewithsequence(ticketingType1, topId, topBottomId, event, user);
    }

    @Test
    void test_getPreviousPositionItem_success() {

        //setup
        ticketingType2.setId(id);
        ticketingType2.setPosition(1);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType2);

        //mock
        when(ticketingTypeCommonRepo.previousPositionTicketType(ticketingType2.getId(),event,ticketingType2.getPosition())).thenReturn(ticketingTypeList);

        //Execution
        TicketingType actualData = ticketingTypeServiceImpl.getPreviousPositionItem(ticketingType2, event);
        assertEquals(actualData.getPosition(), ticketingType2.getPosition());
    }

    @Test
    void test_getPreviousPositionItem_success_with_ticketTypeList_empty() {

        //setup
        ticketingType2.setId(id);
        ticketingType2.setPosition(1);

        List<TicketingType> ticketingTypeList = new ArrayList<>();

        //mock
        when(ticketingTypeCommonRepo.previousPositionTicketType(ticketingType2.getId(),event,ticketingType2.getPosition())).thenReturn(ticketingTypeList);

        //Execution
        TicketingType actualData = ticketingTypeServiceImpl.getPreviousPositionItem(ticketingType2, event);
        assertNull(actualData);
    }

    @Test
    void test_getNextPositionItem_success() {

        //setup
        ticketingType1.setId(id);
        ticketingType1.setPosition(1);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.nextPositionTicketType(ticketingType1.getId(),event,ticketingType1.getPosition())).thenReturn(ticketingTypeList);

        //Execution
        TicketingType actualData = ticketingTypeServiceImpl.getNextPositionItem(ticketingType1, event);
        assertEquals(actualData.getPosition(), ticketingType1.getPosition());
    }

    @Test
    void test_getNextPositionItem_success_with_ticketingTypeList_empty() {

        //setup
        ticketingType1.setId(id);
        ticketingType1.setPosition(1);

        List<TicketingType> ticketingTypeList = new ArrayList<>();

        //mock
        when(ticketingTypeCommonRepo.nextPositionTicketType(ticketingType1.getId(),event,ticketingType1.getPosition())).thenReturn(ticketingTypeList);

        //Execution
        TicketingType actualData = ticketingTypeServiceImpl.getNextPositionItem(ticketingType1, event);
        assertNull(actualData);
    }

   // @Test
    void test_getLastItem_success_with_recurringEventId() {
        ticketing.setId(1L);

        //mock
        when(ticketingTypeRepository.findFirstByEventAndRecurringEventIdOrderByPositionDesc(event.getEventId(), ticketingType1.getRecurringEventId())).thenReturn(ticketingType1);

        //Execution
        TicketingType lastItem = ticketingTypeTicketService.getLastItem(ticketingType1);
        assertEquals(ticketingType1.getId(),lastItem.getId());
    }

 //   @Test
    void test_getLastItem_success() {

        //setup
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        //mock
        when(ticketingTypeRepository.findFirstByEventIdAndRecurringEventIdIsNullOrderByPositionDesc(ticketingType.getEventId())).thenReturn(ticketingType);

        //Execution
        TicketingType lastItem = ticketingTypeTicketService.getLastItem(ticketingType);
        assertEquals(ticketingType1.getId(),lastItem.getId());
    }

    @Test
    void test_delete_success() {

        //Execution
        ticketingTypeServiceImpl.delete(ticketingType1);
        verify(ticketingTypeCommonRepo, times(1)).delete(ticketingType1);
    }

    @Test
    void test_getTicketingTypes_success_with_recurring() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        Mockito.doReturn(ticketingTypeList).when(ticketingTypeTicketService).findAllByRecurringEvents(id, true, DataType.TICKET);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypes(event, true, id, ticketing, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypes_success_with_recurringEventId_zero() {

        //setup
        Long recurringEventId = 0L;

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypes(event, true, recurringEventId, ticketing, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypes_success_with_recurringEventId_null() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypes(event, true, null, ticketing, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypes_success() {

        //setup
        ticketing.setRecurringEvent(false);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        Mockito.doReturn(ticketingTypeList).when(ticketingTypeServiceImpl).findAllByEventIdHost(event, true, DataType.TICKET);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypes(event, true, id, ticketing, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypesDisplay_success_recurring() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypesDisplay(event, true, id, ticketing);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypesDisplay_success_with_eventId_null() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypesDisplay(event, true, null, ticketing);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypesDisplay_success_with_eventId_zero() {

        //setup
        Long recurringEventId = 0L;
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypesDisplay(event, true, recurringEventId, ticketing);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getTicketingTypesDisplay_success() {

        //setup
        ticketing.setRecurringEvent(false);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        Mockito.doReturn(ticketingTypeList).when(ticketingTypeServiceImpl).findAllByEventId(event, true);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketingTypesDisplay(event, true, id, ticketing);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }

    }

    @Test
    void test_deleteAll_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //Execution
        ticketingTypeServiceImpl.deleteAll(ticketingTypeList);

        Class<ArrayList<TicketingType>> listClass = (Class<ArrayList<TicketingType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingType>> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketingTypeCommonRepo, times(1)).deleteAll(ticketingTypeArgumentCaptor.capture());
    }

    @Test
    void test_getByListOfRecurringEvents_success() {

        //setup
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<Long> recurringEventsIdList = new ArrayList<>();
        recurringEventsIdList.add(id);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //setup
        List<TicketingType> ticketingTypes = ticketingTypeTicketService.getByListOfRecurringEvents(recurringEventsList, true);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getRecurringEventId(), ticketingType1.getRecurringEventId());
        }
    }

    @Test
    void test_getByListOfRecurringEvents_success1() {

        //setup
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<Long> recurringEventsIdList = new ArrayList<>();
        recurringEventsIdList.add(id);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //setup
        List<TicketingType> ticketingTypes = ticketingTypeTicketService.getByListOfRecurringEvents(recurringEventsList, false);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getRecurringEventId(), ticketingType1.getRecurringEventId());
        }
    }

    @Test
    void test_getAllByTicketingAndCreatedFromNullOrderByPosition_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);

        //setup
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getAllByTicketingAndCreatedFromNullOrderByPosition(event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getCreatedFrom(), ticketingType1.getCreatedFrom());
        }
    }

    @Test
    void test_getAllByEventAndCreatedFromIsNotNull_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findByTicketing_EventidAndCreatedFromNotNullAndDataType(event, DataType.TICKET)).thenReturn(ticketingTypeList);

        //setup
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getAllByEventAndCreatedFromIsNotNull(event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getCreatedFrom(), ticketingType1.getCreatedFrom());
        }
    }

    @Test
    void test_deleteByRecurringEventId_success() {

        //Execution
        ticketingTypeServiceImpl.deleteByRecurringEventId(id);
        verify(ticketingTypeCommonRepo, times(1)).deleteByRecurringEventId(id);
    }

    @Test
    void test_deleteByRecurringEventIdIn_success() {

        //setup
        List<Long> recurringEventsIdList = new ArrayList<>();
        recurringEventsIdList.add(id);

        //Execution
        ticketingTypeServiceImpl.deleteByRecurringEventIdIn(recurringEventsIdList);
        verify(ticketingTypeCommonRepo, times(1)).deleteByRecurringEventIdIn(recurringEventsIdList);
    }

    @Test
    void test_getAllByEventIdAndRecurringId_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdAndRecurringId(event, id, DataType.TICKET)).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getAllByEventIdAndRecurringId(event, id, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_getMainEventTicketTypeIds_success() {

        //setup
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(id);

        Set<Long> mainEventTicketTypeIdsList = new HashSet<>();
        mainEventTicketTypeIdsList.add(id);

        //mock
        when(ticketingTypeCommonRepo.findBaseEventTicketTypeIds(ticketingTypeIds)).thenReturn(mainEventTicketTypeIdsList);

        //Execution
        Set<Long> mainEventTicketTypeIds = ticketingTypeServiceImpl.getMainEventTicketTypeIds(ticketingTypeIds);
        for (Long actualData : mainEventTicketTypeIds) {
            assertEquals(actualData.longValue(), ticketingType1.getId());
        }
    }

    @Test
    void test_getAllTicketingTypes_success() {

        //setup
        double price = 10L;
        long key = 1L;
        String label = "Label";
        boolean isHidden = true;

		CategoryDto categoryDto = new CategoryDto(key, label, price, isHidden);
        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        //mock


        //Execution
        List<CategoryDto> allTicketingTypes = ticketingTypeTicketService.getAllTicketingTypes(event.getEventId());
        for (CategoryDto actualData : allTicketingTypes) {
            assertEquals(actualData.getKey().longValue(), key);
            assertEquals(actualData.getLabel(), label);
            assertEquals(actualData.getPrice(), price);
        }

    }

    @Test
    void test_getNumberOfTotalTickets_success() {

        //setup
        Set<Long> eventIdsList = new HashSet<>();
        eventIdsList.add(id);

        Object[] objectArray = {10};

        List<Object[]> numberOfTotalTicketsList = new ArrayList<>();
        numberOfTotalTicketsList.add(objectArray);

        //mock


        //Execution
        List<Object[]> numberOfTotalTickets = ticketingTypeTicketService.getNumberOfTotalTickets(eventIdsList);
        for (Object actualData : numberOfTotalTickets) {
            assertEquals(actualData.toString().indexOf(0), numberOfTotalTicketsList.indexOf(0));
        }
    }

    @Test
    void test_getNumberOfTotalTickets_success_with_eventIds_IsEmpty() {

        //setup
        Set<Long> eventIdsList = new HashSet<>();

        //Execution
        List<Object[]> numberOfTotalTickets = ticketingTypeTicketService.getNumberOfTotalTickets(eventIdsList);
        assertTrue(numberOfTotalTickets.isEmpty());
    }

    @Test
    void test_getNumberOfTotalTickets_success_with_eventIds_null() {

        //setup
        List<Object[]> numberOfTotalTickets = ticketingTypeTicketService.getNumberOfTotalTickets(null);
        assertTrue(numberOfTotalTickets.isEmpty());
    }

    @Test
    void test_getAllTicketingTypesByRecuurringEvent_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> allTicketingTypes = ticketingTypeTicketService.getAllTicketingTypesByRecuurringEvent(event.getEventId());
        for (TicketingType actualData : allTicketingTypes) {
            assertEquals(actualData.getTicketType(), ticketingType1.getTicketType());
        }
    }

    @Test
    void test_findAllByRecurringEventsDisplay_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock



        //Execution
        List<TicketingType> allTicketingTypes = ticketingTypeTicketService.findAllByRecurringEventsDisplay(recurringEvents.getId(), true, DataType.TICKET);
        for (TicketingType actualData : allTicketingTypes) {
            assertEquals(actualData.getRecurringEventId(), ticketingType1.getRecurringEventId());
        }
    }

    @Test
    void test_findAllByRecurringEventsDisplay_success1() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock



        //Execution
        List<TicketingType> allTicketingTypes = ticketingTypeTicketService.findAllByRecurringEventsDisplay(recurringEvents.getId(), false, DataType.TICKET);
        for (TicketingType actualData : allTicketingTypes) {
            assertEquals(actualData.getRecurringEventId(), ticketingType1.getRecurringEventId());
        }
    }

    @Test
    void test_findAllByRecurringEvents_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock



        //Execution
        List<TicketingType> allTicketingTypes = ticketingTypeTicketService.findAllByRecurringEvents(recurringEvents.getId(), true, DataType.TICKET);
        for (TicketingType actualData : allTicketingTypes) {
            assertEquals(actualData.getRecurringEventSalesEndStatus(), ticketingType1.getRecurringEventSalesEndStatus());
        }
    }

    @Test
    void test_getTicketTypeByCreateFrom_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findByCreatedFrom(id)).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.getTicketTypeByCreateFrom(id);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getCreatedFrom(), ticketingType1.getCreatedFrom());
        }
    }

    @Test
    void test_findAllByTicketingAndRecuringEvent_success() {
        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketService.findAllByTicketingAndRecuringEvent(id, event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getCreatedFrom(), ticketingType1.getCreatedFrom());
        }
    }

    @Test
    void test_findAllByTicketing_success_with_recurringEventId() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock



        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByTicketing(id, event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getTicketing().getId(), ticketingType1.getTicketing().getId());
        }
    }

    @Test
    void test_findAllByTicketing_success_with_recurringEventId_zero() {

        //setup
        Long recurringId = 0L;
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByTicketing(recurringId, event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getTicketing().getId(), ticketingType1.getTicketing().getId());
        }
    }

    @Test
    void test_findAllByTicketing_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByTicketing(null, event);
        for (TicketingType actualData : ticketingTypes) {
            assertNull(actualData.getRecurringEvent());
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_findByidAndEvent_success() {

        //mock
        when(ticketingTypeCommonRepo.findByIdAndEvent(id, event)).thenReturn(ticketingType1);

        //Execution
        TicketingType ticketingType = ticketingTypeServiceImpl.findByidAndEvent(id, event);
        assertEquals(ticketingType.getTicketType(), ticketingType1.getTicketType());
    }

    @Test
    void test_findByidInAndEvent_success() {
        //setup
        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(id);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findByIdInAndEvent(ticketTypeIds, event)).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findByidInAndEvent(ticketTypeIds, event);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getId(), ticketingType1.getId());
        }
    }

    @Test
    void test_findByidInAndEvent_success_ticketType() {
        //setup
        List<Long> tickettypeids = new ArrayList<>();

        //Execution
        List<TicketingType> ticketType = ticketingTypeServiceImpl.findByidInAndEvent(tickettypeids, event);
        assertEquals(0, ticketType.size());
    }

    @Test
    void test_findAllByEventId_success_with_excludeHidden_true() {

        //setup
        ticketingType1.setHidden(true);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByEventId(event, true);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getPrice(), ticketingType1.getPrice());
            assertEquals(actualData.isHidden(), ticketingType1.isHidden());
        }
    }

    @Test
    void test_findAllByEventId_success_with_excludeHidden_false() {

        //setup
        ticketingType1.setHidden(false);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByEventId(event, false);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.isHidden(), ticketingType1.isHidden());
        }
    }

    @Test
    void test_findAllByEventIdHost_success_fetchHiddenOnly_true() {

        //setup
        ticketingType1.setHidden(true);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdAndRecurringIdIsNullAndHidden(event, true, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByEventIdHost(event, true, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.getPrice(), ticketingType1.getPrice());
            assertEquals(actualData.isHidden(), ticketingType1.isHidden());
        }
    }

    @Test
    void test_findAllByEventIdHost_success_fetchHiddenOnly_false() {

        //setup
        ticketingType1.setHidden(false);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeCommonRepo.findAllByEventIdRecurringIdNull(event, Collections.singletonList(DataType.TICKET))).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByEventIdHost(event, false, DataType.TICKET);
        for (TicketingType actualData : ticketingTypes) {
            assertEquals(actualData.isHidden(), ticketingType1.isHidden());
        }
    }

    @Test
    void test_findAllIdByEventId_success() {

        //setup
        List<Long> tickettypeids = new ArrayList<>();
        tickettypeids.add(id);

        //mock


        //Execution
        List<Long> allTicketTypeId = ticketingTypeServiceImpl.findAllIdByEventId(event);
        for (Long actualData : allTicketTypeId) {
            assertEquals(actualData,id);
        }
    }

    //@Test
    void test_createGeneralAdmissionTicketIfNotExists_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(this.ticketingTypeServiceImpl.save(any())).thenReturn(ticketingType);
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypeList);
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(event, ticketingType1, false, false);
        when(ticketHolderRequiredAttributesService.findByEventId(event)).thenReturn(ticketHolderRequiredAttributesList);
        when(seatingCategoryService.addCategory(any(),any(),false)).thenReturn(new EventCategoryDto(1L, GENERAL_ADMISSION, false));
        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);

        ArgumentCaptor<TicketingType> saveTicketType = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeRepository,times(1)).save(saveTicketType.capture());
        TicketingType actualData = saveTicketType.getValue();
        assertEquals(DataType.TICKET, actualData.getDataType());
    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_success2() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(false);
        ticketHolderRequiredAttributes.setName(Constants.STRING_CELL_SPACE_PHONE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock





        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);
    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_success3() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(false);
        ticketHolderRequiredAttributes.setName(Constants.STRING_ITEM_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock




        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);
    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_success4() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_ITEM_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock

        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);
    }

   // @Test
    void test_createGeneralAdmissionTicketIfNotExists_success5() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();


        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_EMAIL_SPACE);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(this.ticketingTypeServiceImpl.save(any())).thenReturn(ticketingType);
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypeList);
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(event, ticketingType1, false, false);
        when(ticketHolderRequiredAttributesService.findByEventId(event)).thenReturn(ticketHolderRequiredAttributesList);
        when(seatingCategoryService.addCategory(any(),any(),false)).thenReturn(new EventCategoryDto(1L, GENERAL_ADMISSION, false));

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);

        ArgumentCaptor<TicketingType> saveTicketType = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeRepository,times(1)).save(saveTicketType.capture());
    }

    // @Test
    void test_createGeneralAdmissionTicketIfNotExists_success6() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_LAST_SPACE_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypeList);
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(event, ticketingType1, false, false);
        when(ticketHolderRequiredAttributesService.findByEventId(event)).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);

    }

   // @Test
    void test_createGeneralAdmissionTicketIfNotExists_success7() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_FIRST_SPACE_NAME);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypeList);
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(event, ticketingType1, false, false);
        when(ticketHolderRequiredAttributesService.findByEventId(event)).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);

    }

   // @Test
    void test_createGeneralAdmissionTicketIfNotExists_success_with_ticketTypeId() {

        //setup
        String eventTicketTypeId = "1";
        String ticketTypeId = eventTicketTypeId + "," + "0";

        List<TicketingType> ticketingTypeList = new ArrayList<>();

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(Constants.STRING_CELL_SPACE_PHONE);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(eventTicketTypeId);
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId(eventTicketTypeId);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        //mock
        when(this.ticketingTypeServiceImpl.save(any())).thenReturn(ticketingType);
        when(ticketingTypeRepository.findByTicketTypeNameAndEventId(GENERAL_ADMISSION, ticketing.getId())).thenReturn(ticketingTypeList);
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(event, ticketingType1, false, false);
        when(ticketHolderRequiredAttributesService.findByEventId(event)).thenReturn(ticketHolderRequiredAttributesList);
        when(seatingCategoryService.addCategory(any(),any(),false)).thenReturn(new EventCategoryDto(1L, GENERAL_ADMISSION, false));

        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeCommonRepo,times(1)).save(ticketingTypeArgumentCaptor.capture());
        TicketingType actualData = ticketingTypeArgumentCaptor.getValue();
        assertTrue(actualData.getPosition() == sequence);
        assertEquals(actualData.getTicketTypeName(), GENERAL_ADMISSION);
        assertEquals(actualData.getTicketType(), TicketType.PAID);
        assertEquals(actualData.getRecurringEventSalesEndStatus(), TicketingType.RecurringEventSalesEndStatus.START);
        assertEquals(actualData.getBundleType(), TicketBundleType.INDIVIDUAL_TICKET);

        ArgumentCaptor<TicketingType> saveTicketType = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeRepository,times(1)).save(saveTicketType.capture());
    }

    @Test
    void test_createGeneralAdmissionTicketIfNotExists_success1() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock


        //Execution
        ticketingTypeTicketService.createGeneralAdmissionTicketIfNotExists(ticketing, event);
    }


    @Test
    void test_saveAll_success() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        //TODO: APIGW_UN
        //Mockito.doNothing().when(clearAPIGatewayCache).clearTicketingTypeCache(anyList());

        //Execution
        ticketingTypeServiceImpl.saveAll(ticketingTypeList);

        Class<ArrayList<TicketingType>> listClass = (Class<ArrayList<TicketingType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingType>> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketingTypeCommonRepo, times(1)).saveAll(ticketingTypeArgumentCaptor.capture());

        List<TicketingType> ticketingTypesData = ticketingTypeArgumentCaptor.getValue();
        for (TicketingType actualData : ticketingTypesData) {
            assertEquals(ticketingType1.getRecurringEventSalesEndStatus(), actualData.getRecurringEventSalesEndStatus());
            assertEquals(actualData.getTicketType(), ticketingType1.getTicketType());
            assertEquals(actualData.getBundleType(), ticketingType1.getBundleType());
        }
    }

    @Test
    void test_saveAll_success1() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //Execution
        ticketingTypeServiceImpl.saveAll(null);

    }

    @Test
    void test_saveAll_success2() {

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();

        //Execution
        ticketingTypeServiceImpl.saveAll(ticketingTypeList);
    }

   // @Test
    void test_findAllByEvent(){

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        //mock
        when(ticketingTypeRepository.findAllByEventId(any(), any(), any())).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeTicketService.findAllByEvent(event);

        //Assertion
        assertEquals(ticketingTypes.get(0).getRecurringEventSalesEndStatus(), TicketingType.RecurringEventSalesEndStatus.START);
        assertEquals(ticketingTypes.get(0).getTicketType(), TicketType.PAID);

        verify(ticketingTypeRepository).findAllByEventId(any(), any(), any());
    }

    @Test
    void test_findAllByTicketTypeIdsAndCreatedFrom(){

        //setup
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType1);

        List<Long> ticketTypeIds = new ArrayList<>();
        ticketTypeIds.add(ticketingType1.getId());

        //mock
        when(ticketingTypeCommonRepo.findAllByTicketTypeIds(anyList())).thenReturn(ticketingTypeList);

        //Execution
        List<TicketingType> ticketingTypes = ticketingTypeServiceImpl.findAllByTicketTypeIdsAndCreatedFrom(ticketTypeIds);

        //Assertion
        assertEquals(ticketingTypes.get(0).getRecurringEventSalesEndStatus(), TicketingType.RecurringEventSalesEndStatus.START);
        assertEquals(ticketingTypes.get(0).getTicketType(), TicketType.PAID);

        verify(ticketingTypeCommonRepo).findAllByTicketTypeIds(anyList());
    }

   // @Test
    void test_findAllIdByEventIdAndRecurringEvent(){

        //setup
        List<Long> ticketTypeIdList = new ArrayList<>();
        ticketTypeIdList.add(ticketingType1.getId());

        //mock
        when(ticketingTypeRepository.findAllIdByEventIdAndRecurringEvent(any(), anyLong())).thenReturn(ticketTypeIdList);

        //Execution
        List<Long> ticketTypeIds = ticketingTypeTicketService.findAllIdByEventIdAndRecurringEvent(event, recurringEvents.getId());

        //Assertion
        assertEquals(ticketTypeIds.get(0).longValue(), ticketingType1.getId());

        verify(ticketingTypeRepository).findAllIdByEventIdAndRecurringEvent(any(), anyLong());
    }

}