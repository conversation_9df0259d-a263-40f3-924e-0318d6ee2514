package com.accelevents.services.impl;

import com.accelevents.domain.ItemDistributionDetails;
import com.accelevents.domain.Winner;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.ItemDistributionDetailsRepository;
import com.accelevents.services.WinnerService;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ItemDistributionServiceImplTest {

    @Spy
    @InjectMocks
    private ItemDistributionServiceImpl itemDistributionServiceImpl = new ItemDistributionServiceImpl();

    @Mock
    private ItemDistributionDetailsRepository itemDistributionDetailsRepository;

    @Mock
    private WinnerService winnerService;

    private ItemDistributionDetails itemDistributionDetails;
    private Winner winner;

    private boolean distributed = false;
    private long staffUserId = 1L;

    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);
        itemDistributionDetails = new ItemDistributionDetails();
        winner = new Winner();
    }

    @Test
    void test_getItemDistributionDetails() {

        //setup
        setItemDistributionDetailsObject(staffUserId, distributed);
        itemDistributionDetails.setNotes("auction note");

        //mock
        when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(itemDistributionDetails);

        //Execution
        ItemDistributionDetails itemDistributionDetailsData = itemDistributionServiceImpl.getItemDistributionDetails(1L, ModuleType.AUCTION);

        //Assertion
        verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        assertEquals(itemDistributionDetailsData.getStaffUserId(), itemDistributionDetails.getStaffUserId());
        assertEquals(itemDistributionDetailsData.getModuleType(), itemDistributionDetails.getModuleType());
        assertEquals(itemDistributionDetailsData.getSourceId(), itemDistributionDetails.getSourceId());
        assertEquals(itemDistributionDetailsData.getNotes(), itemDistributionDetails.getNotes());
    }



    @Test
    void test_saveItemDistributionDetails_withWinnerNullAndModuleTypeCauseAuctionAndItemDistributionDetailsNotNull() {

        //setup
        Long sourceId = 1L;
        ModuleType moduleType = ModuleType.CAUSEAUCTION;
        boolean distributed = false;
        long staffUserId = 1L;
        String notes = "auction note";
        setItemDistributionDetailsObject(staffUserId, distributed);

        //mock
        when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(itemDistributionDetails);

        //Execution
        itemDistributionServiceImpl.saveItemDistributionDetails(sourceId, moduleType, distributed, notes, staffUserId);

        //Assertion
        verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        verify(itemDistributionDetailsRepository).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailsData =itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailsData.getStaffUserId(), itemDistributionDetails.getStaffUserId());
        assertEquals(itemDistributionDetailsData.getModuleType(), itemDistributionDetails.getModuleType());
        assertEquals(itemDistributionDetailsData.getSourceId(), itemDistributionDetails.getSourceId());
        assertEquals(itemDistributionDetailsData.getNotes(), itemDistributionDetails.getNotes());
        assertEquals(itemDistributionDetailsData.getUpdatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    @Test
    void test_saveItemDistributionDetails_withWinnerNotNullAndModuleTypeCauseAuctionAndItemDistributionDetailsNull() {

        //setup
        Long sourceId = 1L;
        boolean distributed = false;
        long staffUserId = 1L;
        String notes = "auction note";
        setItemDistributionDetailsObject(staffUserId, distributed);
        itemDistributionDetails.setModuleType(ModuleType.AUCTION);
        itemDistributionDetails.setNotes(notes);

        //mock
        when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(null);

        //Execution
        itemDistributionServiceImpl.saveItemDistributionDetails(sourceId, ModuleType.AUCTION, distributed, notes, staffUserId);

        //Assertion
        verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        verify(itemDistributionDetailsRepository).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailsData =itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailsData.getStaffUserId(), itemDistributionDetails.getStaffUserId());
        assertEquals(itemDistributionDetailsData.getModuleType(), itemDistributionDetails.getModuleType());
        assertEquals(itemDistributionDetailsData.getSourceId(), itemDistributionDetails.getSourceId());
        assertEquals(itemDistributionDetailsData.getNotes(), itemDistributionDetails.getNotes());
        assertEquals(itemDistributionDetailsData.getCreatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    @Test
    void test_isAllItemDistributed_withSourceIdsEmpty() {

        //setup
        List<Long> sourceIds = new ArrayList<>();

        //Execution
        boolean isAllItemDistributed = itemDistributionServiceImpl.isAllItemDistributed(sourceIds, ModuleType.AUCTION);

        //Assertion
        assertFalse(isAllItemDistributed);
    }

    static Object[] getCountDistributed(){
        return new Object[]{
                new Object[]{BigInteger.ONE, true},
                new Object[]{BigInteger.ZERO, false}
        };
    }
    @ParameterizedTest
    @MethodSource("getCountDistributed")
    void test_isAllItemDistributed_withSourceIds(BigInteger countOfDistributedItem, boolean isAllDistributed) {

        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);

        //mock
        when(itemDistributionDetailsRepository.countItemDistributionBySourceIdsAndStatus(anyList(), any(), anyBoolean())).thenReturn(countOfDistributedItem);

        //Execution
        boolean isAllItemDistributed = itemDistributionServiceImpl.isAllItemDistributed(sourceIds, ModuleType.AUCTION);

        //Assertion
        verify(itemDistributionDetailsRepository).countItemDistributionBySourceIdsAndStatus(anyList(), any(), anyBoolean());

        assertEquals(isAllItemDistributed, isAllDistributed);
    }

    @Test
    void test_saveItemsDistributionDetails_throwException() {
        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);
        ModuleType moduleType = ModuleType.AUCTION;
        boolean distributed = false;
        long staffUserId = 1L;

        //mock
        when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> itemDistributionServiceImpl.saveItemsDistributionDetails(sourceIds, moduleType, distributed, staffUserId));

        //Assertion
        assertEquals(NotAcceptableException.AuctionExceptionMsg.NOT_A_WINNING_BID.getDeveloperMessage(), exception.getMessage());
        verify(winnerService).findAllWinnerByBidIds(anyList());
    }

    @Test
    void test_saveItemsDistributionDetails_withWinneListEmptyAndModuleTypeCauseAuctionAndItemDistributionNull() {
        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);
        sourceIds.add(2L);
        ModuleType moduleType = ModuleType.CAUSEAUCTION;
        boolean distributed = false;
        long staffUserId = 1L;
        itemDistributionDetails.setSourceId(1L);
        List<ItemDistributionDetails> itemDistributionDetailsList = new ArrayList<>();
        itemDistributionDetailsList.add(itemDistributionDetails);

        //mock
        Mockito.when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(Collections.emptyList());
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdsAndModuleType(anyList(), any())).thenReturn(itemDistributionDetailsList);
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(null);

        //Execution
        itemDistributionServiceImpl.saveItemsDistributionDetails(sourceIds, moduleType, distributed, staffUserId);

        //Assertion
        Mockito.verify(winnerService).findAllWinnerByBidIds(anyList());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdsAndModuleType(anyList(), any());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        Mockito.verify(itemDistributionDetailsRepository, Mockito.times(2)).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailData = itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailData.getSourceId().longValue(), 2L);
        assertEquals(itemDistributionDetailData.getModuleType(), moduleType);
        assertEquals(itemDistributionDetailData.isDistributed(), distributed);
        assertNull(itemDistributionDetailData.getNotes());
        assertEquals(itemDistributionDetailData.getStaffUserId(), staffUserId);

        Class<ArrayList<ItemDistributionDetails>> itemDistributionDetails = (Class<ArrayList<ItemDistributionDetails>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<ItemDistributionDetails>> arrayListArgumentCaptor = ArgumentCaptor.forClass(itemDistributionDetails);
        verify(itemDistributionDetailsRepository).saveAll(arrayListArgumentCaptor.capture());

        List<ItemDistributionDetails> itemDistributionDetailstData = arrayListArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailstData.get(0).getUpdatedAt().toString(), DateUtils.getCurrentDate().toString());
        assertEquals(itemDistributionDetailstData.get(1).getCreatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    @Test
    void test_saveItemsDistributionDetails_withWinneListEmptyAndModuleTypeCauseAuctionAndItemDistributionNotNull() {
        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);
        sourceIds.add(2L);
        ModuleType moduleType = ModuleType.CAUSEAUCTION;
        boolean distributed = false;
        long staffUserId = 1L;
        itemDistributionDetails.setSourceId(1L);
        itemDistributionDetails.setCreatedAt(DateUtils.getCurrentDate());
        List<ItemDistributionDetails> itemDistributionDetailsList = new ArrayList<>();
        itemDistributionDetailsList.add(itemDistributionDetails);

        //mock
        Mockito.when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(Collections.emptyList());
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdsAndModuleType(anyList(), any())).thenReturn(itemDistributionDetailsList);
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(itemDistributionDetails);

        //Execution
        itemDistributionServiceImpl.saveItemsDistributionDetails(sourceIds, moduleType, distributed, staffUserId);

        //Assertion
        Mockito.verify(winnerService).findAllWinnerByBidIds(anyList());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdsAndModuleType(anyList(), any());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        Mockito.verify(itemDistributionDetailsRepository, Mockito.times(2)).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailData = itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailData.getSourceId().longValue(), 2L);
        assertEquals(itemDistributionDetailData.getModuleType(), moduleType);
        assertEquals(itemDistributionDetailData.isDistributed(), distributed);
        assertNull(itemDistributionDetailData.getNotes());
        assertEquals(itemDistributionDetailData.getStaffUserId(), staffUserId);

        Class<ArrayList<ItemDistributionDetails>> itemDistributionDetails = (Class<ArrayList<ItemDistributionDetails>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<ItemDistributionDetails>> arrayListArgumentCaptor = ArgumentCaptor.forClass(itemDistributionDetails);
        verify(itemDistributionDetailsRepository).saveAll(arrayListArgumentCaptor.capture());

        List<ItemDistributionDetails> itemDistributionDetailstData = arrayListArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailstData.get(0).getUpdatedAt().toString(), DateUtils.getCurrentDate().toString());
//        assertEquals(itemDistributionDetailstData.get(1).getCreatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    @Test
    void test_saveItemsDistributionDetails_withWinneListNotEmptyAndModuleTypeCauseAuctionAndSourceIdEmpty() {
        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);
        ModuleType moduleType = ModuleType.CAUSEAUCTION;

        setItemDistributionDetailsObject(staffUserId, distributed);
        List<ItemDistributionDetails> itemDistributionDetailsList = new ArrayList<>();
        itemDistributionDetailsList.add(itemDistributionDetails);

        //mock
        Mockito.when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(Collections.singletonList(winner));
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdsAndModuleType(anyList(), any())).thenReturn(itemDistributionDetailsList);

        //Execution
        itemDistributionServiceImpl.saveItemsDistributionDetails(sourceIds, moduleType, distributed, staffUserId);

        //Assertion
        Mockito.verify(winnerService).findAllWinnerByBidIds(anyList());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdsAndModuleType(anyList(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        Mockito.verify(itemDistributionDetailsRepository).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailData = itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailData.getSourceId().longValue(), 1L);
        assertEquals(itemDistributionDetailData.getModuleType(), moduleType);
        assertEquals(itemDistributionDetailData.isDistributed(), distributed);
        assertNull(itemDistributionDetailData.getNotes());
        assertEquals(itemDistributionDetailData.getStaffUserId(), staffUserId);

        Class<ArrayList<ItemDistributionDetails>> itemDistributionDetails = (Class<ArrayList<ItemDistributionDetails>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<ItemDistributionDetails>> arrayListArgumentCaptor = ArgumentCaptor.forClass(itemDistributionDetails);
        verify(itemDistributionDetailsRepository).saveAll(arrayListArgumentCaptor.capture());

        List<ItemDistributionDetails> itemDistributionDetailstData = arrayListArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailstData.get(0).getUpdatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    @Test
    void test_saveItemsDistributionDetails_withWinneListNotEmptyAndModuleTypeCauseAuctionAndItemDistributionListEmpty() {
        //setup
        List<Long> sourceIds = new ArrayList<>();
        sourceIds.add(1L);
        ModuleType moduleType = ModuleType.CAUSEAUCTION;
        setItemDistributionDetailsObject(staffUserId, distributed);
        List<ItemDistributionDetails> itemDistributionDetailsList = new ArrayList<>();

        //mock
        Mockito.when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(Collections.singletonList(winner));
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdsAndModuleType(anyList(), any())).thenReturn(itemDistributionDetailsList);
        Mockito.when(itemDistributionDetailsRepository.findBySourceIdAndModuleType(anyLong(), any())).thenReturn(itemDistributionDetails);

        //Execution
        itemDistributionServiceImpl.saveItemsDistributionDetails(sourceIds, moduleType, distributed, staffUserId);

        //Assertion
        Mockito.verify(winnerService).findAllWinnerByBidIds(anyList());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdsAndModuleType(anyList(), any());
        Mockito.verify(itemDistributionDetailsRepository).findBySourceIdAndModuleType(anyLong(), any());

        ArgumentCaptor<ItemDistributionDetails> itemDistributionDetailsArgumentCaptor = ArgumentCaptor.forClass(ItemDistributionDetails.class);
        Mockito.verify(itemDistributionDetailsRepository).save(itemDistributionDetailsArgumentCaptor.capture());

        ItemDistributionDetails itemDistributionDetailData = itemDistributionDetailsArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailData.getSourceId().longValue(), 1L);
        assertEquals(itemDistributionDetailData.getModuleType(), moduleType);
        assertEquals(itemDistributionDetailData.isDistributed(), distributed);
        assertNull(itemDistributionDetailData.getNotes());
        assertEquals(itemDistributionDetailData.getStaffUserId(), staffUserId);

        Class<ArrayList<ItemDistributionDetails>> itemDistributionDetails = (Class<ArrayList<ItemDistributionDetails>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<ItemDistributionDetails>> arrayListArgumentCaptor = ArgumentCaptor.forClass(itemDistributionDetails);
        verify(itemDistributionDetailsRepository).saveAll(arrayListArgumentCaptor.capture());

        List<ItemDistributionDetails> itemDistributionDetailstData = arrayListArgumentCaptor.getValue();
        assertEquals(itemDistributionDetailstData.get(0).getUpdatedAt().toString(), DateUtils.getCurrentDate().toString());
    }

    private void setItemDistributionDetailsObject(long staffUserId, boolean distributed) {
        itemDistributionDetails.setSourceId(1L);
        itemDistributionDetails.setCreatedAt(DateUtils.getCurrentDate());
        itemDistributionDetails.setUpdatedAt(DateUtils.getCurrentDate());
        itemDistributionDetails.setId(1L);
        itemDistributionDetails.setDistributed(distributed);
        itemDistributionDetails.setModuleType(ModuleType.AUCTION);
        itemDistributionDetails.setStaffUserId(staffUserId);
    }
}

