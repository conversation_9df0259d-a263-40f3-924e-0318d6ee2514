//TODO: PowerMock issue with java17
/*
package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.*;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.itextpdf.text.DocumentException;
import freemarker.template.TemplateException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;

import java.io.IOException;
import java.util.*;
import java.util.jar.JarException;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingServiceImplTestWithPowerMockito {

    @Spy
    @InjectMocks
    private TicketingServiceImpl ticketingServiceImpl = new TicketingServiceImpl();

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private TicketingRepository ticketingRepository;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Mock
    private UserRepository userRepository;

    private Ticketing ticketing;
    private Event event;
    private EventTickets eventTickets;
    private TicketHolderAttributes ticketHolderAttributes;
    private User user;
    private TicketingOrder ticketingOrder;

    private String firstName = "Jon";
    private String lastName = "Kaz";
    private String email = "<EMAIL>";
    private String date = "2019/04/04 05:30";
    private String endDate = "2019/04/04 09:30";
    private Date orderDate = new Date(date);
    private Long id = 1L;

    @BeforeEach
    public void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        ticketing = EventDataUtil.getTicketing(event);
        eventTickets = EventDataUtil.getEventTickets();

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setId(id);
        ticketingOrder.setEventid(event);
        ticketingOrder.setOrderDate(orderDate);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketHolderAttributes.setJsonValue(EventDataUtil.getJsonValue(firstName, lastName, email));
    }

    @Test
    public void test_unmashler_throwJAXBException() throws JAXBException {

        //setup
        String xml = EventDataUtil.getSampleXML();

        PowerMockito.mockStatic(JAXBContext.class);

        //mock

        //Execution
        ticketingServiceImpl.unmashler(xml, null);
    }

    @Test
    public void test_getTicketHoldersAndBuyersContacts_throw_JAXBException() throws JAXBException{

        //setup
        PowerMockito.mockStatic(JAXBContext.class);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderFirstName(firstName);
        eventTickets.setHolderLastName(lastName);
        eventTickets.setHolderEmail(email);
        eventTickets.setTicketingOrder(ticketingOrder);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        //mock
        when(eventCommonRepoService.findByEventIdAndTicketingStatusJoinFetch(
                event, Collections.singletonList(TicketingOrder.TicketingOrderStatus.PAID),0L, DataType.TICKET, orderDate, orderDate)).thenReturn(eventTicketsList);
        doThrow(JAXBException.class).when(ticketingServiceImpl).unmashler(EventDataUtil.getSampleXML(), null);

        //Execution
        ticketingServiceImpl.getTicketHoldersAndBuyersContacts(event);
    }

    //TODO: fix test-case after spring-boot
    */
/*@Test
    public void test_sendTestEmail_throwException_JAXBException() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        user.setEmail(email);

        eventTickets = new EventTickets();
        eventTickets.setId(id);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        //mock
        when(userRepository.findUserByEmail(any())).thenThrow(JarException.class);
        when(eventTicketsCommonRepo.findByTicketingOrder(anyLong())).thenReturn(eventTicketList);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        doThrow(JAXBException.class).when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean());

        //Execution
        try {
            ticketingServiceImpl.sendTestEmail(email, event, Optional.empty(), false);
        } catch (Exception e){

        }
    }*//*

}*/
