package com.accelevents.services.impl;

import com.accelevents.badges.Badges;
import com.accelevents.badges.BadgesImage;
import com.accelevents.badges.BadgesMaster;
import com.accelevents.badges.JoinBadgeTicketTypes;
import com.accelevents.billing.chargebee.enums.ChargebeeEntitlements;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.BadgesImageRepository;
import com.accelevents.repositories.BadgesRepository;
import com.accelevents.services.BadgesMasterService;
import com.accelevents.services.JoinBadgeTicketTypesService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;

import javax.xml.bind.JAXBException;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BadgesServiceImplTest {

    @Spy
    @InjectMocks
    private BadgesServiceImpl badgesService;
    @Mock
    private BadgesRepository badgesRepository;
    @Mock
    private BadgesMasterService badgesMasterService;
    @Mock
    private BadgesImageRepository badgesImageRepository;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Mock
    private TicketHolderEditAttributesServiceImpl ticketHolderEditAttributesService;
    @Mock
    private JoinBadgeTicketTypesService joinBadgeTicketTypesService;

    private Event event;
    private User user;
    private Badges customeBadges;
    private BadgesDto badgesDto;
    private List<Long> badgeIds;
    private List<Long> badgeImageIds;
    private List<Long> ticketingTypeId = new ArrayList<>();
    private BadgesImage badgesImage;
    private BadgesMaster badgesMaster;
    private TicketingType ticketingType;
    private BadgesResponseData badgesResponseData;
    private EventTickets eventTickets;
    private TicketHolderAttributes ticketHolderAttributes;
    private HolderDisplayAttributesDto holderDisplayAttributesDto;
    private JoinBadgeTicketTypes joinBadgeTicketTypes;
    List<JoinBadgeTicketTypes> joinBadgeTicketTypesList = new ArrayList<>();


    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        ticketingType = EventDataUtil.getTicketingType(event);

        badgesMaster = new BadgesMaster();
        badgesMaster.setTitle("Test Badge Save And Update");
        badgesMaster.setCustom(true);
        badgesMaster.setWidth(10.0);
        badgesMaster.setHeight(10.0);
        badgesMaster.setBleedAreaHeight(5.0);
        badgesMaster.setBleedAreaWidth(5.0);
        badgesMaster.setSafeAreaHeight(5.0);
        badgesMaster.setSafeAreaWidth(5.0);
        badgesMaster.setNumberOfView(1);
        badgesMaster.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        customeBadges = new Badges();
        customeBadges.setEventId(event.getEventId());
        customeBadges.setBadgeMasterId(1L);
        customeBadges.setBadgesMaster(badgesMaster);
        customeBadges.setBadgeName("Test Badge Save And Update");
        customeBadges.setWidth(10.0);
        customeBadges.setHeight(10.0);
        customeBadges.setBleedAreaHeight(5.0);
        customeBadges.setBleedAreaWidth(5.0);
        customeBadges.setSafeAreaHeight(5.0);
        customeBadges.setSafeAreaWidth(5.0);
        customeBadges.setNumberOfView(1);
        customeBadges.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        joinBadgeTicketTypes = new JoinBadgeTicketTypes();
        joinBadgeTicketTypes.setId(1L);
        joinBadgeTicketTypes.setTicketTypeId(2L);
        joinBadgeTicketTypes.setBadges(customeBadges);
        joinBadgeTicketTypesList.add(joinBadgeTicketTypes);

        badgesDto = new BadgesDto();
        badgesDto.setEventId(event.getEventId());
        badgesDto.setBadgeMasterId(1L);
        badgesDto.setBadgeName("Test Badge Save And Update");
        badgesDto.setWidth(10.0);
        badgesDto.setHeight(10.0);
        badgesDto.setBleedAreaHeight(5.0);
        badgesDto.setBleedAreaWidth(5.0);
        badgesDto.setSafeAreaHeight(5.0);
        badgesDto.setSafeAreaWidth(5.0);
        badgesDto.setNumberOfView(1);
        badgesDto.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        badgesImage = new BadgesImage();
        badgesImage.setBadgeMasterId(1L);
        badgesImage.setBadgeId(1L);
        badgesImage.setEventId(1L);
        badgesImage.setImageUrl("fdf19a42-07e6-48a3-b2d3-65155a3b1ea2");
        badgesImage.setImageName("test");
        badgesImage.setCreatedBy(1L);

        badgesResponseData = new BadgesResponseData();
        badgesResponseData.setBadgeId(1L);

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(1L);

        eventTickets = new EventTickets();
        eventTickets.setBadgeId(1L);
        eventTickets.setId(1L);
        eventTickets.setTicketingTypeOnlyId(1L);
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setHolderUserId(user);

        holderDisplayAttributesDto = new HolderDisplayAttributesDto();
        holderDisplayAttributesDto.setEventTicketingId(1L);

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_saveTime(){

        ticketingTypeId.add(2L);
        badgesDto.setTicketType(ticketingTypeId);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);

        when( badgesMasterService.getBadgeById(anyLong())).thenReturn(badgesMaster);

        //Execution
        badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event);

        //Assertion
        ArgumentCaptor<Badges> badgesArgumentCaptor = ArgumentCaptor.forClass(Badges.class);
        verify(badgesRepository).save(badgesArgumentCaptor.capture());

        Badges badges = badgesArgumentCaptor.getValue();
        assertEquals(badges.getBadgeName(), badgesDto.getBadgeName());
        assertEquals(badges.getBadgeMasterId(), badgesDto.getBadgeMasterId());
        assertEquals(badges.getEventId(), badgesDto.getEventId());
        assertEquals(badges.getWidth(), badgesDto.getWidth());
        assertEquals(badges.getHeight(), badgesDto.getHeight());
        //assertEquals(badges.getBleedAreaWidth(), badgesDto.getBleedAreaWidth());
        //assertEquals(badges.getSafeAreaWidth(), badgesDto.getSafeAreaWidth());
        //assertEquals(badges.getBleedAreaHeight(), badgesDto.getBleedAreaHeight());
        //assertEquals(badges.getSafeAreaHeight(), badgesDto.getSafeAreaHeight());

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_updateTime_badgeMasterId_null(){

        badgesDto.setBadgeId(1L);
        badgesDto.setBadgeMasterId(2L);
        customeBadges.setId(1L);
        customeBadges.setBadgeMasterId(1L);

        ticketingTypeId.add(2L);
        badgesDto.setTicketType(ticketingTypeId);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);
        when( badgesMasterService.getBadgeById(anyLong())).thenReturn(badgesMaster);
        when( badgesRepository.findById(badgesDto.getBadgeId()) ).thenReturn(Optional.ofNullable(customeBadges));

        //Execution
        badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event);

        //Assertion
        ArgumentCaptor<Badges> badgesArgumentCaptor = ArgumentCaptor.forClass(Badges.class);
        verify(badgesRepository).save(badgesArgumentCaptor.capture());

        Badges badges = badgesArgumentCaptor.getValue();
        assertEquals(badges.getBadgeName(), badgesDto.getBadgeName());
        assertEquals(badges.getId(), badgesDto.getBadgeId());
        assertEquals(badges.getBadgeMasterId(), badgesDto.getBadgeMasterId());
        assertEquals(badges.getEventId(), badgesDto.getEventId());
        assertEquals(badges.getWidth(), badgesDto.getWidth());
        assertEquals(badges.getHeight(), badgesDto.getHeight());
        assertEquals(badges.getBleedAreaWidth(), badgesDto.getBleedAreaWidth());
        assertEquals(badges.getSafeAreaWidth(), badgesDto.getSafeAreaWidth());
        assertEquals(badges.getBleedAreaHeight(), badgesDto.getBleedAreaHeight());
        assertEquals(badges.getSafeAreaHeight(), badgesDto.getSafeAreaHeight());

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_updateTime_TicketingTypeId_Duplicate(){

        badgesDto.setBadgeId(1L);
        badgesDto.setBadgeMasterId(2L);
        customeBadges.setId(1L);
        customeBadges.setBadgeMasterId(1L);

        ticketingTypeId.add(1L);
        ticketingTypeId.add(2L);
        badgesDto.setTicketType(ticketingTypeId);
        Badges anotherBadge = new Badges();
        anotherBadge.setId(2L);
        anotherBadge.setBadgeName("another badge name");


        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);
        when( badgesMasterService.getBadgeById(anyLong())).thenReturn(badgesMaster);
        when( badgesRepository.findById(badgesDto.getBadgeId()) ).thenReturn(Optional.ofNullable(customeBadges));

        when(ticketingTypeService.findByid(anyLong())).thenReturn(ticketingType);
        when(joinBadgeTicketTypesService.findTicketTypeIdsByBadges(customeBadges)).thenReturn(List.of(1L));
        when(joinBadgeTicketTypesService.findByTicketTypeIdIn(List.of(2L))).thenReturn(joinBadgeTicketTypesList);

        String message = Constants.DUPLICATE_TICKETING_TYPE_ID.replace(Constants.TICKETING_TYPE_NAME, ticketingType.getTicketTypeName())
                .replace(Constants.BADGE_NAME,customeBadges.getBadgeName());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event));
        assertEquals(message, exception.getMessage());

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_updateTime(){

        badgesDto.setBadgeId(1L);
        badgesDto.setBadgeName(null);
        customeBadges.setId(1L);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);
        when( badgesRepository.findById(badgesDto.getBadgeId()) ).thenReturn(Optional.ofNullable(customeBadges));


        //Execution
        badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event);

        //Assertion
        ArgumentCaptor<Badges> badgesArgumentCaptor = ArgumentCaptor.forClass(Badges.class);
        verify(badgesRepository).save(badgesArgumentCaptor.capture());

        Badges badges = badgesArgumentCaptor.getValue();
        assertEquals(badges.getId(), badgesDto.getBadgeId());
        assertEquals(badges.getBadgeMasterId(), badgesDto.getBadgeMasterId());
        assertEquals(badges.getEventId(), badgesDto.getEventId());
        assertEquals(badges.getWidth(), badgesDto.getWidth());
        assertEquals(badges.getHeight(), badgesDto.getHeight());
        assertEquals(badges.getBleedAreaWidth(), badgesDto.getBleedAreaWidth());
        assertEquals(badges.getSafeAreaWidth(), badgesDto.getSafeAreaWidth());
        assertEquals(badges.getBleedAreaHeight(), badgesDto.getBleedAreaHeight());
        assertEquals(badges.getSafeAreaHeight(), badgesDto.getSafeAreaHeight());

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_updateTime_duplicateBadgesName(){

        badgesDto.setBadgeId(1L);
        badgesDto.setBadgeName("Test Badge Save And UpdateCOPY1");
        customeBadges.setId(1L);

        when(badgesMasterService.getBadgeById(anyLong())).thenReturn(badgesMaster);
        when( badgesRepository.findById(badgesDto.getBadgeId()) ).thenReturn(Optional.ofNullable(customeBadges));

        when(badgesRepository.badgesNameExist(anyLong(),anyString())).thenReturn(true);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.DUPLICATE_BADGES_NAME.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_saveAndUpdateBadgesForEvent_badgesNotFound(){

        badgesDto.setBadgeId(1L);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);

        //mock
        when( badgesRepository.findById(badgesDto.getBadgeId()) ).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.saveAndUpdateBadgesForEvent(badgesDto,user,event));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_cloneBadgesForEvent_cloneTime(){

        badgeIds = new ArrayList<>();
        badgeIds.add(1L);
        badgeIds.add(2L);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);
        when( badgesRepository.findByIdAndEventId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(customeBadges));
        //Execution
        BadgesResponseDto badgesResponseDto  = badgesService.cloneBadgesForEvent(user, event, badgeIds);

        assertEquals(Constants.SUCCESS,badgesResponseDto.getType());
    }

    @Test
    void test_cloneBadgesForEvent_badgesNotFound(){

        badgeIds = new ArrayList<>();
        badgeIds.add(1L);
        badgeIds.add(2L);

        //setup
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, ChargebeeEntitlements.BADGE_PRINTING)).thenReturn(true);
        
        //mock
        when( badgesRepository.findByIdAndEventId(anyLong(),anyLong()) ).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.cloneBadgesForEvent(user,event,badgeIds));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getAllBadgesForEvent(){

        //Setup
        List<BadgesDto> badgesDtos = new ArrayList<>();
        badgesDtos.add(badgesDto);

        Pageable pageable = PageRequest.of(0, 10, Sort.Direction.DESC, BADGES_ID);
        Page<BadgesDto> pageBadgeDto = new PageImpl(Collections.singletonList(badgesDtos));


        when( badgesService.getSortablePage(0,10,null,null)).thenReturn(pageable);
        when( badgesService.getSortablePage(0,10,"test",null)).thenReturn(pageable);
        doReturn(pageBadgeDto).when(badgesService).getAllBadgesDtoByEventIdAndSearchStrWithPagination(event.getEventId(), null, pageable);

        //Execution

        DataTableResponse allBadgesForEvent = badgesService.getAllBadgesForEvent(event, null, 0, 10, null, null);
        DataTableResponse allBadgesForEventTest = badgesService.getAllBadgesForEvent(event, null, 0, 10, "test", null);

        assertFalse(allBadgesForEvent.getData().isEmpty());
        assertEquals(1,allBadgesForEvent.getRecordsTotal() );

        assertFalse(allBadgesForEventTest.getData().isEmpty());
        assertEquals(1,allBadgesForEventTest.getRecordsTotal() );
    }

    @Test
    void test_getAllBadgesForEvent_sort(){

        //Setup
        List<BadgesDto> badgesDtos = new ArrayList<>();
        badgesDtos.add(badgesDto);

        Pageable pageable = PageRequest.of(0, 10, Sort.Direction.DESC, BADGES_NAME);
        Pageable pageableTest = PageRequest.of(0, 10, Sort.Direction.DESC, BADGES_UPDATED_AT);
        Page<BadgesDto> pageBadgeDto = new PageImpl(Collections.singletonList(badgesDtos));


        when( badgesService.getSortablePage(0,10,BADGES_NAME,"desc")).thenReturn(pageable);
        when( badgesService.getSortablePage(0,10,BADGES_UPDATED_AT,"desc")).thenReturn(pageableTest);
        doReturn(pageBadgeDto).when(badgesService).getAllBadgesDtoByEventIdAndSearchStrWithPagination(event.getEventId(), null, pageable);
        doReturn(pageBadgeDto).when(badgesService).getAllBadgesDtoByEventIdAndSearchStrWithPagination(event.getEventId(), null, pageableTest);

        //Execution

        DataTableResponse allBadgesForEvent = badgesService.getAllBadgesForEvent(event, null, 0, 10, BADGES_NAME, "desc");
        DataTableResponse allBadgesForEventTest = badgesService.getAllBadgesForEvent(event, null, 0, 10, BADGES_UPDATED_AT, "desc");

        assertFalse(allBadgesForEvent.getData().isEmpty());
        assertEquals(1,allBadgesForEvent.getRecordsTotal() );

        assertFalse(allBadgesForEventTest.getData().isEmpty());
        assertEquals(1,allBadgesForEventTest.getRecordsTotal() );
    }

    @Test
    void test_removeBadgesForEvent_cloneTime(){

        badgeIds = new ArrayList<>();
        badgeIds.add(1L);
        badgeIds.add(2L);

        when( badgesRepository.findByIdAndEventId(anyLong(),anyLong())).thenReturn(Optional.ofNullable(customeBadges));
        //Execution
        ResponseDto responseDto  = badgesService.removeBadgesForEvent(user, event, badgeIds);

        assertEquals(Constants.SUCCESS,responseDto.getType());
        assertEquals(badgeIds.toString(),responseDto.getMessage());
    }

    @Test
    void test_removeBadgesForEvent_badgesNotFound(){

        badgeIds = new ArrayList<>();
        badgeIds.add(1L);
        badgeIds.add(2L);

        //mock
        when( badgesRepository.findByIdAndEventId(anyLong(),anyLong()) ).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.removeBadgesForEvent(user,event,badgeIds));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_saveBadgeImageWhileUpload(){

        //Execution
        badgesService.saveBadgeImageWhileUpload(event, "fdf19a42-07e6-48a3-b2d3-65155a3b1ea2",
                                                                          "test", user, 1L, 1L);

        ArgumentCaptor<BadgesImage> badgesImageArgumentCaptor = ArgumentCaptor.forClass(BadgesImage.class);
        verify(badgesImageRepository).save(badgesImageArgumentCaptor.capture());

        BadgesImage saveBadgesImage = badgesImageArgumentCaptor.getValue();
        assertEquals(saveBadgesImage.getBadgeMasterId(),badgesImage.getBadgeMasterId());
        assertEquals(saveBadgesImage.getBadgeId(),badgesImage.getBadgeId());
        assertEquals(saveBadgesImage.getEventId(),badgesImage.getEventId());
        assertEquals(saveBadgesImage.getImageUrl(),badgesImage.getImageUrl());
        assertEquals(saveBadgesImage.getImageName(),badgesImage.getImageName());
        assertEquals(saveBadgesImage.getCreatedBy(),badgesImage.getCreatedBy());
    }

    @Test
    void test_getAllBadgesImagesForEvent(){

        List<BadgesImage> badgesImagesList = new ArrayList<>();
        badgesImagesList.add(badgesImage);



        //Execution
        List<BadgesImage> allBadgesImagesForEvent = badgesService.getAllBadgesImagesForEvent(event, 1L);
        allBadgesImagesForEvent.forEach(saveBadgesImage -> {
            assertEquals(saveBadgesImage.getBadgeMasterId(),badgesImage.getBadgeMasterId());
            assertEquals(saveBadgesImage.getBadgeId(),badgesImage.getBadgeId());
            assertEquals(saveBadgesImage.getEventId(),badgesImage.getEventId());
            assertEquals(saveBadgesImage.getImageUrl(),badgesImage.getImageUrl());
            assertEquals(saveBadgesImage.getImageName(),badgesImage.getImageName());
            assertEquals(saveBadgesImage.getCreatedBy(),badgesImage.getCreatedBy());
        });

    }

    @Test
    void test_getBadgeUsingId(){

        customeBadges.setId(1L);
        badgesDto.setBadgeId(1L);

        when( badgesRepository.findByIdAndEventId(anyLong(),any())).thenReturn(Optional.ofNullable(customeBadges));

        //Execution
        BadgesDto dto = badgesService.getBadgeUsingId(1L,event);

        assertEquals(dto.getBadgeName(), badgesDto.getBadgeName());
        assertEquals(dto.getBadgeId(), badgesDto.getBadgeId());
        assertEquals(dto.getBadgeMasterId(), badgesDto.getBadgeMasterId());
        assertEquals(dto.getEventId(), badgesDto.getEventId());
        assertEquals(dto.getWidth(), badgesDto.getWidth());
        assertEquals(dto.getHeight(), badgesDto.getHeight());
        assertEquals(dto.getBleedAreaWidth(), badgesDto.getBleedAreaWidth());
        assertEquals(dto.getSafeAreaWidth(), badgesDto.getSafeAreaWidth());
        assertEquals(dto.getBleedAreaHeight(), badgesDto.getBleedAreaHeight());
        assertEquals(dto.getSafeAreaHeight(), badgesDto.getSafeAreaHeight());
    }

    @Test
    void test_getBadgeUsingId_Empty(){

        customeBadges.setId(1L);
        badgesDto.setBadgeId(1L);

        when( badgesRepository.findByIdAndEventId(anyLong(),any())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.getBadgeUsingId(1L,event));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_removeBadgesImageUsingBadgesImageId(){

        badgeImageIds = new ArrayList<>();
        badgeImageIds.add(1L);
        badgeImageIds.add(2L);

        when(badgesImageRepository.findByIdAndBadgeIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Optional.ofNullable(badgesImage));

        //Execution
        ResponseDto responseDto = badgesService.removeBadgesImageUsingBadgesImageId(event, 1L, badgeImageIds);

        assertEquals(Constants.SUCCESS,responseDto.getType());
        assertEquals(badgeImageIds.toString(),responseDto.getMessage());

    }

    @Test
    void test_removeBadgesImageUsingBadgesImageId_nullBadgeImage(){

        badgeImageIds = new ArrayList<>();
        badgeImageIds.add(1L);
        badgeImageIds.add(2L);

        when(badgesImageRepository.findByIdAndBadgeIdAndEventId(anyLong(),anyLong(),anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.removeBadgesImageUsingBadgesImageId(event, 1L, badgeImageIds));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES_IMAGE.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_badgeNameDuplicate(){

        when(badgesRepository.badgesNameExist(anyLong(),anyString())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> badgesService.badgeNameDuplicate(1L,"test"));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.DUPLICATE_BADGES_NAME.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getCopyBadgesName(){

        when(badgesRepository.countByEventId(1L,"testCOPY1")).thenReturn(1L);
        when(badgesRepository.countByEventId(1L,"testCOPY2")).thenReturn(0L);
        //Execution
        String test = badgesService.getCopyBadgesName("test", 1L, 1);

        assertEquals( "testCOPY2",test);
    }

    @Test
    void test_getBadgeUsingEventId(){
        ticketingTypeId.add(1L);
        badgesResponseData.setTicketingTypeIds(ticketingTypeId);

        when(badgesRepository.getAllBadgesByEventId(anyLong())).thenReturn(Collections.singletonList(badgesResponseData));

        //Execution
        BadgesResponseDto badgeUsingEventId = badgesService.getBadgeUsingEventId(event);

        assertEquals(SUCCESS,badgeUsingEventId.getType());
    }

    @Test
    void test_getTicketingIdAndBadgesIdByEventId(){
        ticketingTypeId.add(1L);
        badgesResponseData.setTicketingTypeIds(ticketingTypeId);

        //Execution
        List<BadgesResponseData> ticketingIdAndBadgesIdByEventId = badgesService.getTicketingIdAndBadgesIdByEventId(anyLong());

        ticketingIdAndBadgesIdByEventId.forEach(e->{
            assertEquals(badgesResponseData.getBadgeId(),e.getBadgeId());
        });

    }

    @Test
    void test_saveAll(){

        //Execution
        badgesService.saveAll(Collections.singletonList(customeBadges));

        //Assertion
        ArgumentCaptor<List<Badges>> badgesArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(badgesRepository).saveAll(badgesArgumentCaptor.capture());

        List<Badges> badges = badgesArgumentCaptor.getValue();
        badges.forEach(e -> {
            assertEquals(customeBadges,e);
        });

    }

    @Test
    void test_upsertCustomAttribute(){
        customeBadges.setId(1L);
        when(badgesRepository.findByIdAndEventId(customeBadges.getId(),event.getEventId())).thenReturn(Optional.of(customeBadges));

        List<HashMap<String,Object>> customAttributes = new ArrayList<HashMap<String,Object>>(){{
            add(new HashMap<String,Object>(){{
                put("id","420793");
                put("fieldName","Prefix");
            }});

            add(new HashMap<String,Object>(){{
                put("id","420794");
                put("fieldName","First Name");
            }});
        }};

        badgesService.upsertCustomFields(event,customeBadges.getId(),customAttributes);
        verify(badgesRepository).save(any());
    }

    @Test
    void test_upsertCustomAttributeNotFoundBadge(){
        customeBadges.setId(1L);
        when(badgesRepository.findByIdAndEventId(customeBadges.getId(),event.getEventId())).thenReturn(Optional.empty());

        List<HashMap<String,Object>> customAttributes = new ArrayList<HashMap<String,Object>>(){{
            add(new HashMap<String,Object>(){{
                put("id","420793");
                put("fieldName","Prefix");
            }});

            add(new HashMap<String,Object>(){{
                put("id","420794");
                put("fieldName","First Name");
            }});
        }};

        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.upsertCustomFields(event,customeBadges.getId(),customAttributes));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getListOfBadgeUsingListOfBadgeIdAndEventId(){

        List<Badges> listOfBadgeUsingListOfBadgeIdAndEventId = badgesService.getListOfBadgeUsingListOfBadgeIdAndEventId(badgeIds, event.getEventId());

        listOfBadgeUsingListOfBadgeIdAndEventId.forEach(e->{
            assertEquals(customeBadges,e);
        });
    }

    @Test
    void test_getHolderInformation_with_barcode_success() throws JAXBException {

        when(eventCommonRepoService.findByBarcodeId(anyString())).thenReturn(eventTickets);
        when(ticketHolderEditAttributesService.getEditTicketHoldersAttributeDto(any(),any())).thenReturn(holderDisplayAttributesDto);

        HolderDisplayAttributesDto holderDisplayAttributesDtoResult = badgesService.getHolderInformation(event, user, "test_bar_ids");

        assertEquals(holderDisplayAttributesDto,holderDisplayAttributesDtoResult);
    }

    @Test
    void test_getHolderInformation_without_barcode_success() throws JAXBException {

        when(eventCommonRepoService.findTicketRegisterForUser(any(), any())).thenReturn(Collections.singletonList(eventTickets));
        when(ticketHolderEditAttributesService.getEditTicketHoldersAttributeDto(any(),any())).thenReturn(holderDisplayAttributesDto);

        HolderDisplayAttributesDto holderDisplayAttributesDtoResult = badgesService.getHolderInformation(event, user, "");

        assertEquals(holderDisplayAttributesDto,holderDisplayAttributesDtoResult);
    }

    @Test
    void test_getHolderInformation_without_barcode_error() throws JAXBException {

        when(eventCommonRepoService.findTicketRegisterForUser(any(), any())).thenReturn(Collections.emptyList());
        
        Exception exception = assertThrows(NotFoundException.class,
                () -> badgesService.getHolderInformation(event, user, ""));
        assertEquals(NotFoundException.TicketingOrderExceptionMsg.ORDER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getAllEventTicketDetailByBarcodeId() throws JAXBException {

        when(eventCommonRepoService.findByBarcodeIds(anyList())).thenReturn(Collections.singletonList(eventTickets));


        List<EventTickets> allEventTicketDetailByBarcodeId = badgesService.getAllEventTicketDetailByBarcodeId(event, user, new ArrayList<>(Arrays.asList("barcode1", "barcode2")));

        allEventTicketDetailByBarcodeId.forEach(e->{
            assertEquals(eventTickets,e);
        });
    }

}
