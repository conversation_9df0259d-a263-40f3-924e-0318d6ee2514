package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.dto.KioskModeCheckInDetailDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.kioskmodecheckindetail.KioskModeCheckInDetail;
import com.accelevents.repositories.KioskModeCheckInDetailRepository;
import com.accelevents.session_speakers.services.SessionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class KioskModeCheckInDetailServiceImplTest {

    @Spy
    @InjectMocks
    private KioskModeCheckInDetailServiceImpl kioskModeCheckInDetailServiceImpl;

    @Mock
    private KioskModeCheckInDetailRepository kioskModeCheckInDetailRepository;

    @Mock
    private SessionService sessionService;

    private Event event;
    private User user;
    private KioskModeCheckInDetailDTO kioskModeCheckInDetailDTO;
    private KioskModeCheckInDetail kioskModeCheckInDetail;
    private Session session;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        kioskModeCheckInDetailDTO = new KioskModeCheckInDetailDTO();
        kioskModeCheckInDetailDTO.setKioskDetailJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");
        kioskModeCheckInDetailDTO.setEventId(event.getEventId());

        session = new Session();
        session.setId(1L);
        session.setEvent(event);

        kioskModeCheckInDetail = new KioskModeCheckInDetail();
        kioskModeCheckInDetail.setId(1L);

    }

    @Test
    void test_updateCheckAttendeeDetails_save_success(){


        kioskModeCheckInDetailServiceImpl.updateCheckAttendeeDetails(event,user,kioskModeCheckInDetailDTO);

        verify(kioskModeCheckInDetailRepository).save(any());
    }

    @Test
    void test_updateCheckAttendeeDetails_save_error(){

        kioskModeCheckInDetailDTO.setSessionId(1L);
        when(sessionService.getSessionById(anyLong(), any())).thenReturn(session);
        when(kioskModeCheckInDetailRepository.isKioskModeAvailableByEventIdAndSessionId(anyLong(), anyLong())).thenReturn(true);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> kioskModeCheckInDetailServiceImpl.updateCheckAttendeeDetails(event,user,kioskModeCheckInDetailDTO));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.ALREADY_ADDED_KIOSK_DETAIL.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_updateCheckAttendeeDetails_update_error(){

        kioskModeCheckInDetailDTO.setSessionId(1L);
        kioskModeCheckInDetailDTO.setKioskModeCheckInDetailId(1L);

        when(sessionService.getSessionById(anyLong(), any())).thenReturn(session);

        when(kioskModeCheckInDetailRepository.findById(anyLong())).thenReturn(Optional.ofNullable(null));

        Exception exception = assertThrows(NotFoundException.class,
                () -> kioskModeCheckInDetailServiceImpl.updateCheckAttendeeDetails(event,user,kioskModeCheckInDetailDTO));

        assertEquals(NotFoundException.NotFound.NOT_FOUND_KIOSK_MODE_CHECK_IN_DETAIL.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_updateCheckAttendeeDetails_update_success(){

        kioskModeCheckInDetailDTO.setSessionId(1L);
        kioskModeCheckInDetailDTO.setKioskModeCheckInDetailId(1L);

        when(sessionService.getSessionById(anyLong(), any())).thenReturn(session);
        when(kioskModeCheckInDetailRepository.findById(anyLong())).thenReturn(Optional.ofNullable(kioskModeCheckInDetail));
        kioskModeCheckInDetailServiceImpl.updateCheckAttendeeDetails(event,user,kioskModeCheckInDetailDTO);
        assertTrue(true);
    }

    @Test
    void test_getCheckInDetailForKioskMode_using_sessionId(){
        when(kioskModeCheckInDetailRepository.findByEventIdAndSessionId(anyLong(), anyLong())).thenReturn(kioskModeCheckInDetailDTO);

        KioskModeCheckInDetailDTO checkInDetailForKioskMode = kioskModeCheckInDetailServiceImpl.getCheckInDetailForKioskMode(event, 1L);

        assertEquals(kioskModeCheckInDetailDTO,checkInDetailForKioskMode);
    }

    @Test
    void test_getCheckInDetailForKioskMode_using_eventId(){

        when(kioskModeCheckInDetailRepository.findByEventIdAndSessionIdNull(anyLong())).thenReturn(kioskModeCheckInDetailDTO);

        KioskModeCheckInDetailDTO checkInDetailForKioskMode = kioskModeCheckInDetailServiceImpl.getCheckInDetailForKioskMode(event, null);

        assertEquals(kioskModeCheckInDetailDTO,checkInDetailForKioskMode);
    }

    @Test
    void test_getAllCheckInDetailForKioskMode(){

        when(kioskModeCheckInDetailRepository.findByEventId(anyLong())).thenReturn(Collections.singletonList(kioskModeCheckInDetailDTO));

        List<KioskModeCheckInDetailDTO> allCheckInDetailForKioskMode = kioskModeCheckInDetailServiceImpl.getAllCheckInDetailForKioskMode(event);

        allCheckInDetailForKioskMode.forEach(e->{
            assertEquals(kioskModeCheckInDetailDTO,e);
        });
    }



}
