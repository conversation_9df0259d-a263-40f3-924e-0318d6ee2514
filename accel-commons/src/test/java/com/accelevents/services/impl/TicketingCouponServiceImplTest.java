package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.DiscountType;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.RecurringEventsRepository;
import com.accelevents.repositories.TicketingCouponRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.DiscountCouponDto;
import com.accelevents.ticketing.dto.TicketingCouponDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.math.BigInteger;
import java.util.*;

import static com.accelevents.utils.Constants.DATE_FORMAT_MONTH;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingCouponServiceImplTest {

    @Spy
    @InjectMocks
    private  TicketingCouponServiceImpl ticketingCouponServiceImpl;
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private TicketingCouponRepository ticketingCouponRepository;
    @Mock
    private RecurringEventsRepository recurringEventsRepository;
    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    private User user;
    private TicketingOrder ticketingOrder;
    private TicketingOrderManager ticketingOrderManager;
    private TicketingType ticketingType;
    private TicketingCoupon ticketingCoupon;
    private Event event;
    private TicketingCouponDto ticketingCouponDto;
    private Ticketing ticketing;

	private long recurringEventId = 1L;
    private Long eventId = 1L;
    private String startDate = "01/01/2019 00:00:00";
    private String endDate = "04/04/2019 00:00:00";
	private String couponCode = "couponCode";
    private Date startDate1 = new Date(startDate);
    private Date endDate1 = new Date(endDate);
	long count = 0;
    private Date futureExpireDate = DateUtils.addDaysWithoutTime(new Date(), 1);

   private List<Long> ticketOrderIds = new ArrayList<>();

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
		String eventName = "TestEvent";
		event = new Event(eventName, true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(eventId);
        event.setCurrency(Currency.AUD);
		String eventUrl = "asd";
		event.setEventURL(eventUrl);

        user = new User();
        user.setUserId(1L);

        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setAmount(50d);
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
		String eventTicketTypeId = "1";
		ticketingCoupon.setEventTicketTypeId(eventTicketTypeId);
        ticketingCoupon.setCouponEndDate(endDate1);
        ticketingCoupon.setCouponStartDate(startDate1);
		long uses = 1L;
		ticketingCoupon.setUsesPerUser(uses);
		long couponCodeId = 1L;
		ticketingCoupon.setId(couponCodeId);
        ticketingCoupon.setUses(uses);
        ticketingCoupon.setName(couponCode);
        ticketingCoupon.setApplyToHiddenTickets(false);
        ticketingCoupon.setEventId(eventId);


        ticketingCouponDto = new TicketingCouponDto();
        ticketingCouponDto.setAmount(50d);
        ticketingCouponDto.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
        ticketingCouponDto.setCouponCode(couponCode);
        ticketingCouponDto.setCouponEndDate(endDate);
        ticketingCouponDto.setCouponStartDate(startDate);
        ticketingCouponDto.setDiscountType(DiscountType.PERCENTAGE);
        ticketingCouponDto.setEventTicketTypeId(eventTicketTypeId);
        ticketingCouponDto.setUses(uses);
        ticketingCouponDto.setUsesPerUser(uses);
        ticketingCouponDto.setApplicableToHiddenCoupon(true);

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(1L);
        ticketingOrder.setExpirein(endDate1);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
    }

    @Test
    void test_isCouponUsedForAnyRecurringEvent_withTicketingCouponNull_case1() {
        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setExpirein(new Date());
        ticketingOrder.setTicketingCoupon(null);
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when(ticketingOrderRepoService.findByTicketingCouponInAndEventId(anyList(),anyLong())).thenReturn(ticketingOrders);

        //Execution
         ticketingCouponServiceImpl.isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Assert
        verify(ticketingOrderRepoService).saveAll(anyList());
    }
    @Test
    void test_isCouponUsedForAnyRecurringEvent_withTicketingCouponNull_case2() {
        //setup
        ticketingOrder.setExpirein(new Date());
        ticketingOrder.setTicketingCoupon(null);
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when(ticketingOrderRepoService.findByTicketingCouponInAndEventId(anyList(),anyLong())).thenReturn(ticketingOrders);

        //Execution
        ticketingCouponServiceImpl.isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Assert
        verify(ticketingOrderRepoService).saveAll(anyList());
    }
    @Test
    void test_isCouponUsedForAnyRecurringEvent_withTicketingCoupon_case1() {
        //setup
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when(ticketingOrderRepoService.findByTicketingCouponInAndEventId(anyList(),anyLong())).thenReturn(ticketingOrders);

        //Execution
        ticketingCouponServiceImpl.isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Assert
        verify(ticketingOrderRepoService).saveAll(anyList());
    }
    @Test
    void test_isCouponUsedForAnyRecurringEvent_withTicketingCoupon1_case2() {
        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when(ticketingOrderRepoService.findByTicketingCouponInAndEventId(anyList(),anyLong())).thenReturn(ticketingOrders);

        //Execution
        ticketingCouponServiceImpl.isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Assert
        verify(ticketingOrderRepoService).saveAll(anyList());
    }
    @Test
    void test_getByRecurringEventIdAndCouponCode_withoutTicketingCouponNull() {
        //mock
        when(ticketingCouponRepository.findByRecurringEventIdAndName(anyLong(),anyString())).thenReturn(ticketingCoupon);

        //Execution
        TicketingCoupon ticketingCouponData = ticketingCouponServiceImpl.getByRecurringEventIdAndCouponCode(recurringEventId,couponCode);

        //Assertion
        assertNotNull(ticketingCouponData);
    }
    @Test
    void test_deleteByRecurringIdAndEventId_succsess() {
        //Execution
        ticketingCouponServiceImpl.deleteByRecurringIdAndEventId(Collections.singletonList(recurringEventId),event);

        //Assert
        verify(ticketingCouponRepository).deleteAllByRecurringIdAndEventId(anyList(),anyLong());
    }
    @Test
    void test_getAllByEventIdAndRecurringEventIdIsNull_succsess() {

        //setup
        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        //mock
        when(ticketingCouponRepository.getAllByEventIdAndRecurringEventIdIsNull(anyLong())).thenReturn(ticketingCoupons);

        //Execution
        ticketingCouponServiceImpl.getAllByEventIdAndRecurringEventIdIsNull(event);

        //Assert
        verify(ticketingCouponRepository).getAllByEventIdAndRecurringEventIdIsNull(anyLong());
    }
    @Test
    void test_save_succsess() {
        //Execution
        ticketingCouponServiceImpl.save(ticketingCoupon);

        //Assert
        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository).save(ticketingCouponArgumentCaptor.capture());
        TicketingCoupon actual = ticketingCouponArgumentCaptor.getValue();
    }
    @Test
    void test_saveAll_succsess() {
        //Execution
        ticketingCouponServiceImpl.saveAll(Collections.singletonList(ticketingCoupon));

        //Assert
        verify(ticketingCouponRepository).saveAll(anyList());
    }
    @Test
    void test_getByRecurringEventIdAndCouponCode_withTicketingCouponNull_throwExceptionCodeNotFound() throws IOException {
        //setup
        
        //mock
        when(ticketingCouponRepository.findByRecurringEventIdAndName(anyLong(),anyString())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.getByRecurringEventIdAndCouponCode(recurringEventId,couponCode));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }

   @ParameterizedTest
   @MethodSource("getRecurringEventId")
   void test_save_reccuringEventFalseAndWithotRecurringEventId(Long recurringEventId) {
       //setup
       Date startdate = DateUtils.getFormattedDate(ticketingCouponDto.getCouponStartDate(), Constants.DATE_FORMAT);
       Date enddate = DateUtils.getFormattedDate(ticketingCouponDto.getCouponEndDate(), Constants.DATE_FORMAT);

       ticketingCouponDto.setCouponEndDate(endDate);
       ticketingCouponDto.setCouponStartDate(startDate);
       ticketingCouponDto.setRecurringRelativeEndTime(1);
       ticketingCouponDto.setRecurringRelativeStartTime(1);

       ticketing = new Ticketing();
       ticketing.setRecurringEvent(false);

       //mock
       when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

       //Execution
       ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId);


       ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
       verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());
       TicketingCoupon actual = ticketingCouponArgumentCaptor.getValue();

       //assert
       assertEquals(ticketingCouponDto.getCouponCode(),actual.getName());
       assertEquals(ticketingCouponDto.getEventTicketTypeId(),actual.getEventTicketTypeId());
       assertEquals((long) ticketingCouponDto.getUses(), actual.getUses());
       assertEquals(ticketingCouponDto.getAmount(), actual.getAmount());
       assertEquals(ticketingCouponDto.getDiscountType(),actual.getDiscountType());
       assertEquals(ticketingCouponDto.getUsesPerUser(),actual.getUsesPerUser());
       assertEquals(ticketingCouponDto.getApplicableTo(),actual.getApplicableTo());
       assertEquals(ticketingCouponDto.getApplicableToHiddenCoupon(),actual.getApplyToHiddenTickets());
       assertEquals(ticketingCouponDto.getRecurringRelativeStartTime(),actual.getRecurringRelativeStartTime());
       assertEquals(ticketingCouponDto.getRecurringRelativeEndTime(),actual.getRecurringRelativeEndTime());
       assertEquals(startdate,actual.getCouponStartDate());
       assertEquals(enddate,actual.getCouponEndDate());
   }
    @Test
    void test_save__throwExceptionPercentageGreaterThan100() throws IOException {
        //setup
        
        ticketingCouponDto = new TicketingCouponDto();
        ticketingCouponDto.setAmount(105d);
        ticketingCouponDto.setDiscountType(DiscountType.PERCENTAGE);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_PERCENT_GREATER_100.getErrorMessage(), exception.getMessage());
    }
    @Test
    void test_save_throwExceptionCanNotNullRecurringRelativeTime() throws IOException {
        //setup
        
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketingCouponDto.setRecurringRelativeEndTime(null);
        ticketingCouponDto.setRecurringRelativeStartTime(null);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_NULL_XX_RELATIVE_TIME.getErrorMessage(), exception.getMessage());
    }


    @Test
    void test_save_throwExceptionCouponAlreadyExist() throws IOException {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponRepository.isCouponExist(anyString(),anyLong(),anyLong())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_ALREADY_EXIST.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_save_recurringEventTrueAndWithRecurringEventIdIsZero() {
        //setup
        ticketingCouponDto.setCouponEndDate(endDate);
        ticketingCouponDto.setCouponStartDate(startDate);
        ticketingCouponDto.setRecurringRelativeEndTime(1);
        ticketingCouponDto.setRecurringRelativeStartTime(1);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        List<RecurringEvents> recurringEventsList = new ArrayList<RecurringEvents>();
        RecurringEvents recurringEvent=new RecurringEvents();
        recurringEventsList.add(recurringEvent);

        Long recurringEventId = 0L;

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(recurringEventsRepository.findByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(recurringEventsList);
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateCouponCode(anyList(),anyList(), anyBoolean(), anyBoolean(), nullable(Long.class));

        //Execution
       ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId);

        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(recurringEventsRepository).findByEventIdOrderByRecurringEventStartDateAsc(any());
        verify(recurringEventsMainScheduleService).callingExecutorServiceForCreateCouponCode(anyList(),anyList(), anyBoolean(), anyBoolean(), nullable(Long.class));

    }
    @Test
    void test_save_reccuringEventTrueAndWithRecurringEventId() {
        //setup
        ticketingCouponDto.setCouponEndDate(endDate);
        ticketingCouponDto.setCouponStartDate(startDate);
        ticketingCouponDto.setRecurringRelativeEndTime(1);
        ticketingCouponDto.setRecurringRelativeStartTime(1);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        RecurringEvents recurringEvent=new RecurringEvents();

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(recurringEventsRepository.findById(anyLong())).thenReturn(Optional.of(recurringEvent));
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateCouponCode(anyList(),anyList(), anyBoolean(), anyBoolean(), anyLong());

        //Execution
        ticketingCouponServiceImpl.save(ticketingCouponDto,event,recurringEventId);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(recurringEventsRepository).findById(anyLong());
        verify(recurringEventsMainScheduleService).callingExecutorServiceForCreateCouponCode(anyList(),anyList(), anyBoolean(), anyBoolean(), anyLong());

    }
   @Test
   void test_getAllCoupons_withRecurringEventTrueAndRecuringEventId() {
       //setup
       String startdate = DateUtils.formatDate(ticketingCoupon.getCouponStartDate(),DATE_FORMAT_MONTH);
       String enddate = DateUtils.formatDate(ticketingCoupon.getCouponEndDate(),DATE_FORMAT_MONTH);

       ticketingCoupon.setRecurringEventId(recurringEventId);
       ticketingCoupon.setRecurringRelativeStartTime(1);
       ticketingCoupon.setRecurringRelativeEndTime(1);

       ticketing = new Ticketing();
       ticketing.setRecurringEvent(true);

       List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
       ticketingCoupons.add(ticketingCoupon);

       //mock
       when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
       when(ticketingCouponRepository.getAllByEventIdAndRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingCoupons);
       doReturn(5L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

       //Execution
       List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);

       //assert
       assertEquals(discountCouponDtosData.get(0).getCode(), ticketingCoupon.getName());
       assertEquals(discountCouponDtosData.get(0).getAmount(), ticketingCoupon.getAmount());
       assertEquals(discountCouponDtosData.get(0).getCouponUsed(), 5L);
       assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCoupon(), ticketingCoupon.getUses());
       assertEquals(discountCouponDtosData.get(0).getApplicableTo(), ticketingCoupon.getApplicableTo());
       assertEquals(discountCouponDtosData.get(0).getStartDate(), startdate);
       assertEquals(discountCouponDtosData.get(0).getEndDate(), enddate);
       assertEquals(discountCouponDtosData.get(0).getDiscountType(), ticketingCoupon.getDiscountType());
       assertEquals(discountCouponDtosData.get(0).getEventTicketTypeId(), ticketingCoupon.getEventTicketTypeId());
       assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCouponPerUser(), ticketingCoupon.getUsesPerUser());
       assertEquals(discountCouponDtosData.get(0).getRecurringEventId(), ticketingCoupon.getRecurringEventId());
       assertEquals(discountCouponDtosData.get(0).getRecurringRelativeStartTime(), ticketingCoupon.getRecurringRelativeStartTime());
       assertEquals(discountCouponDtosData.get(0).getRecurringRelativeEndTime(), ticketingCoupon.getRecurringRelativeEndTime());

   }
    @Test
    void test_getAllCoupons_withRecurringEventTrueAndRecuringEventIdNull_Success() {
        //setup
        String startdate = DateUtils.formatDate(ticketingCoupon.getCouponStartDate(),DATE_FORMAT_MONTH);
        String enddate = DateUtils.formatDate(ticketingCoupon.getCouponEndDate(),DATE_FORMAT_MONTH);

        Long recurringEventId=0L;

        ticketingCoupon.setRecurringEventId(recurringEventId);
        ticketingCoupon.setRecurringRelativeStartTime(1);
        ticketingCoupon.setRecurringRelativeEndTime(1);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponRepository.getAllByEventIdAndRecurringEventIdIsNull(anyLong())).thenReturn(ticketingCoupons);
        doReturn(5L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        //Execution
        List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);

        //assert
        assertEquals(discountCouponDtosData.get(0).getCode(), ticketingCoupon.getName());
        assertEquals(discountCouponDtosData.get(0).getAmount(), ticketingCoupon.getAmount());
        assertEquals(discountCouponDtosData.get(0).getCouponUsed(), 5L);
        assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCoupon(), ticketingCoupon.getUses());
        assertEquals(discountCouponDtosData.get(0).getApplicableTo(), ticketingCoupon.getApplicableTo());
        assertEquals(discountCouponDtosData.get(0).getStartDate(), startdate);
        assertEquals(discountCouponDtosData.get(0).getEndDate(), enddate);
        assertEquals(discountCouponDtosData.get(0).getDiscountType(), ticketingCoupon.getDiscountType());
        assertEquals(discountCouponDtosData.get(0).getEventTicketTypeId(), ticketingCoupon.getEventTicketTypeId());
        assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCouponPerUser(), ticketingCoupon.getUsesPerUser());
        assertEquals(discountCouponDtosData.get(0).getRecurringEventId(), ticketingCoupon.getRecurringEventId());
        assertEquals(discountCouponDtosData.get(0).getRecurringRelativeStartTime(), ticketingCoupon.getRecurringRelativeStartTime());
        assertEquals(discountCouponDtosData.get(0).getRecurringRelativeEndTime(), ticketingCoupon.getRecurringRelativeEndTime());
    }
    @Test
    void test_getAllCoupons_withRecurringEventFalseAndWithoutRecuringEventId_Success() {
        //setup
        String startdate = DateUtils.formatDate(ticketingCoupon.getCouponStartDate(),DATE_FORMAT_MONTH);
        String enddate = DateUtils.formatDate(ticketingCoupon.getCouponEndDate(),DATE_FORMAT_MONTH);

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);

        List<TicketingCoupon> ticketingCoupons = new ArrayList<>();
        ticketingCoupons.add(ticketingCoupon);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponRepository.findAllByEventId(anyLong())).thenReturn(ticketingCoupons);
        doReturn(5L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        //Execution
        List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);

        //assert
        assertEquals(discountCouponDtosData.get(0).getCode(), ticketingCoupon.getName());
        assertEquals(discountCouponDtosData.get(0).getAmount(), ticketingCoupon.getAmount());
        assertEquals(discountCouponDtosData.get(0).getCouponUsed(), 5L);
        assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCoupon(), ticketingCoupon.getUses());
        assertEquals(discountCouponDtosData.get(0).getApplicableTo(), ticketingCoupon.getApplicableTo());
        assertEquals(discountCouponDtosData.get(0).getStartDate(), startdate);
        assertEquals(discountCouponDtosData.get(0).getEndDate(), enddate);
        assertEquals(discountCouponDtosData.get(0).getDiscountType(), ticketingCoupon.getDiscountType());
        assertEquals(discountCouponDtosData.get(0).getEventTicketTypeId(), ticketingCoupon.getEventTicketTypeId());
        assertEquals(discountCouponDtosData.get(0).getMaximumUseOfCouponPerUser(), ticketingCoupon.getUsesPerUser());
        assertEquals(discountCouponDtosData.get(0).getRecurringEventId(), ticketingCoupon.getRecurringEventId());
        assertEquals(discountCouponDtosData.get(0).getRecurringRelativeStartTime(), ticketingCoupon.getRecurringRelativeStartTime());
        assertEquals(discountCouponDtosData.get(0).getRecurringRelativeEndTime(), ticketingCoupon.getRecurringRelativeEndTime());

    }
    static Object[] getTicketingCoupons(){
        return new Object[]{
                new Object[]{null},
                new Object[]{Collections.EMPTY_LIST},
        };
    }
    @ParameterizedTest
    @MethodSource("getTicketingCoupons")
    void test_getAllCoupons_withRecurringEventTrueAndRecurringEventId_TicketingcouponNullAndEmpty(List<TicketingCoupon> ticketingCoupons) {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        Long recurringEventId=0L;

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);



        //Execution
        List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);
        assertEquals(0,discountCouponDtosData.size());
    }
    @ParameterizedTest
    @MethodSource("getTicketingCoupons")
    void test_getAllCoupons_withRecurringEventFalseAndwithoutRecurringEventId_TicketingcouponNullAndEmpty(List<TicketingCoupon> ticketingCoupons) {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);



        //Execution
        List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);
        assertEquals(0,discountCouponDtosData.size());
    }
    @ParameterizedTest
    @MethodSource("getTicketingCoupons")
    void test_getAllCoupons_withRecurringEventTrueAndRecurringEventIdIsNull_TicketingcouponNullAndEmpty(List<TicketingCoupon> ticketingCoupons) {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);



        //Execution
        List<DiscountCouponDto> discountCouponDtosData = ticketingCouponServiceImpl.getAllCoupons(event,recurringEventId);
        assertEquals(0,discountCouponDtosData.size());
    }

    static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{null},
                new Object[]{0L},
        };
    }
   @ParameterizedTest
   @MethodSource("getRecurringEventId")
   void test_getByEventIdAndCouponCode_withoutRecurringEventIdSucess(Long recurringEventId) {

       //mock
       when(ticketingCouponRepository.findByEventIdAndNameAndCreatedFromIsNull(eventId,couponCode)).thenReturn(ticketingCoupon);

       //Execution
       TicketingCoupon ticketingCouponData = ticketingCouponServiceImpl.getByEventIdAndCouponCode(eventId, couponCode, recurringEventId);

       //Assertion
       assertNotNull(ticketingCouponData);
   }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_getByEventIdAndCouponCode_withoutRecurringEventId_throwExceptionCodeNotFound(Long recurringEventId) throws IOException {
        //setup
        //mock
        when(ticketingCouponRepository.findByEventIdAndNameAndCreatedFromIsNull(eventId,couponCode)).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.getByEventIdAndCouponCode(eventId,couponCode,recurringEventId));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_getByEventIdAndCouponCode_withRecurringEventIdSucess() {
        //mock
        when(ticketingCouponRepository.findByEventIdAndNameAndRecurringEventId(eventId,couponCode,recurringEventId)).thenReturn(ticketingCoupon);
        //Execution
        TicketingCoupon ticketingCouponData= ticketingCouponServiceImpl.getByEventIdAndCouponCode(eventId,couponCode,recurringEventId);
        //Assert
        assertNotNull(ticketingCouponData);
    }
    @Test
    void test_getByEventIdAndCouponCode_withRecurringEventId_throwExceptionCodeNotFound() throws IOException {
       //setup
        
        //mock
        when(ticketingCouponRepository.findByEventIdAndNameAndRecurringEventId(eventId,couponCode,recurringEventId)).thenReturn(null);
        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.getByEventIdAndCouponCode(eventId,couponCode,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }
    @Test
    void test_deleteCouponCode_throwExceptionCouponCodeAlreadyUsed() throws IOException{
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);
        Long recurringEventId=0L;

        ticketingCoupon.setRecurringEventId(recurringEventId);
        ticketingCoupon.setRecurringRelativeStartTime(1);
        ticketingCoupon.setRecurringRelativeEndTime(1);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(1L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.deleteCouponCode(event,1L,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_USED.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl,times(1)).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        verify(ticketingCouponServiceImpl,times(1)).couponUsed(any(), any(), any(), anyLong());

    }
    @Test
    void test_deleteCouponCode_withRecurringEventFalseAndRecurringEventIdIsNotZero(){
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);
        Long recurringEventId=1L;

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(0L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        //Execution
        ticketingCouponServiceImpl.deleteCouponCode(event,1L,recurringEventId);

        //assertion
        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl,times(1)).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        verify(ticketingCouponServiceImpl,times(1)).couponUsed(any(), any(), any(), anyLong());
    }
    @Test
    void test_deleteCouponCode_withRecurringEventTrueAndRecurringEventIdIsZeroWithCouponIsNotUsed(){
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);
        Long recurringEventId=0L;

        List<TicketingCoupon> reCouponCodes = new ArrayList<TicketingCoupon>();
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(0L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());
        when(ticketingCouponRepository.findAllByCreatedFromAndEventId(anyLong(),anyLong())).thenReturn(reCouponCodes);
        Mockito.doReturn(false).when(ticketingCouponServiceImpl).isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Execution
        ticketingCouponServiceImpl.deleteCouponCode(event,1L,recurringEventId);

        //assertion
        verify(ticketingCouponRepository, times(1)).saveAll(reCouponCodes);
        verify(ticketingCouponRepository, times(1)).save(ticketingCoupon);
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl,times(1)).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        verify(ticketingCouponServiceImpl,times(1)).couponUsed(any(), any(), any(), anyLong());
        verify(ticketingCouponRepository).findAllByCreatedFromAndEventId(anyLong(),anyLong());
        verify(ticketingCouponServiceImpl).isCouponUsedForAnyRecurringEvent(anyList(),anyLong());
    }
    @Test
    void test_deleteCouponCode_withRecurringEventTrueAndRecurringEventIdIsZeroWithCouponUsed(){
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);
        Long recurringEventId=0L;

        List<TicketingCoupon> reCouponCodes = new ArrayList<TicketingCoupon>();
        reCouponCodes.add(ticketingCoupon);
        List<TicketingOrder> ticketingOrders = new ArrayList<TicketingOrder>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(0L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());
        when(ticketingCouponRepository.findAllByCreatedFromAndEventId(anyLong(),anyLong())).thenReturn(reCouponCodes);
        Mockito.doReturn(true).when(ticketingCouponServiceImpl).isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.deleteCouponCode(event,1L,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_USED.getErrorMessage(), exception.getMessage());

        //assertion
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl,times(1)).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        verify(ticketingCouponServiceImpl,times(1)).couponUsed(any(), any(), any(), anyLong());
        verify(ticketingCouponRepository).findAllByCreatedFromAndEventId(anyLong(),anyLong());
        verify(ticketingCouponServiceImpl).isCouponUsedForAnyRecurringEvent(anyList(),anyLong());
    }
    @Test
    void test_updateCouponCode_recurringEventTrueAndRecurringEventIdZero() {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);
        Long recurringEventId=0L;

        String newCouponCode = "newCoupon";

        ticketingCouponDto.setUses(-1L);
        ticketingCouponDto.setRecurringRelativeStartTime(1);
        ticketingCouponDto.setRecurringRelativeEndTime(1);
        ticketingCouponDto.setCouponCode(newCouponCode);

        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setId(recurringEventId);
        ticketingCoupon.setRecurringEvents(recurringEvents);

        List<TicketingCoupon> couponsForRec = new ArrayList<TicketingCoupon>();
        couponsForRec.add(ticketingCoupon);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(0L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());
        when(ticketingCouponRepository.findAllByCreatedFromAndEventIdWithFutureDate(anyLong(),anyLong(), any())).thenReturn(couponsForRec);
        doReturn(false).when(ticketingCouponServiceImpl).isCouponUsedForAnyRecurringEvent(anyList(),anyLong());

        //Execution
        ticketingCouponServiceImpl.updateCouponCode(event,1L,ticketingCouponDto,recurringEventId);

        //Assert
        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository).saveAll(couponsForRec);
        verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());

        TicketingCoupon actual = ticketingCouponArgumentCaptor.getValue();
        assertEquals(newCouponCode, actual.getName());
        assertEquals(ticketingCouponDto.getRecurringRelativeEndTime(),actual.getRecurringRelativeEndTime());
        assertEquals(ticketingCouponDto.getRecurringRelativeStartTime(),actual.getRecurringRelativeStartTime());
        assertEquals(ticketingCouponDto.getAmount(), actual.getAmount());
        assertEquals(ticketingCouponDto.getApplicableTo(),ticketingCoupon.getApplicableTo());
        assertEquals((long) ticketingCouponDto.getUses(), ticketingCoupon.getUses());
        assertEquals(ticketingCouponDto.getDiscountType(),ticketingCoupon.getDiscountType());
    }

    @Test
    void test_updateCouponCode_recurringEventTrueAndWithRecurringEventId(){

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);
        Long recurringEventId=1L;

        String newCouponCode = "newCoupon";

        ticketingCouponDto.setUses(-1L);
        ticketingCouponDto.setRecurringRelativeStartTime(1);
        ticketingCouponDto.setRecurringRelativeEndTime(1);

        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setId(recurringEventId);

        ticketingCoupon.setRecurringEvents(recurringEvents);
        ticketingCoupon.setRecurringRelativeStartTime(1);
        ticketingCoupon.setRecurringRelativeEndTime(1);

        List<TicketingCoupon> couponsForRec = new ArrayList<TicketingCoupon>();
        couponsForRec.add(ticketingCoupon);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        List<Long> newTicketIds =new ArrayList<Long>();
        newTicketIds.add(1L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(1L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        doNothing().when(ticketingCouponServiceImpl).updateCreatedFrom(any(), any());

        //Execution
        ticketingCouponServiceImpl.updateCouponCode(event,1L,ticketingCouponDto,recurringEventId);

        //Assert
        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());

        TicketingCoupon actual = ticketingCouponArgumentCaptor.getValue();
        assertEquals(ticketingCouponDto.getCouponCode(), actual.getName());
        assertEquals(ticketingCouponDto.getRecurringRelativeEndTime(),actual.getRecurringRelativeEndTime());
        assertEquals(ticketingCouponDto.getRecurringRelativeStartTime(),actual.getRecurringRelativeStartTime());
        assertEquals(ticketingCouponDto.getAmount(), actual.getAmount());
        assertEquals(ticketingCouponDto.getApplicableTo(),ticketingCoupon.getApplicableTo());
        assertEquals((long) ticketingCouponDto.getUses(), ticketingCoupon.getUses());
        assertEquals(ticketingCouponDto.getDiscountType(),ticketingCoupon.getDiscountType());
    }
    @Test
    void test_updateCouponCode_throwExceptionMoreTicketCouponUsed() throws IOException {
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);
        Long recurringEventId=0L;

        //setup
        String newCouponCode = "newCoupon";

        ticketingCouponDto.setCouponCode(newCouponCode);
        ticketingCouponDto.setDiscountType(DiscountType.FLAT);
        ticketingCouponDto.setUses(0L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        Mockito.doReturn(ticketingCoupon).when(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        doReturn(1L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), any(), anyLong());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.updateCouponCode(event, 1L, ticketingCouponDto,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.MORE_TICKET_COUPON_USED.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl,times(1)).getByEventIdAndCouponCode(anyLong(), anyLong(), anyLong());
        verify(ticketingCouponServiceImpl,times(1)).couponUsed(any(), any(), any(), anyLong());
    }
    @Test
    void test_updateCouponCode__throwExceptionPercentageGreaterThan100() throws IOException {
        //setup
        
        ticketingCouponDto = new TicketingCouponDto();
        ticketingCouponDto.setAmount(105d);
        ticketingCouponDto.setDiscountType(DiscountType.PERCENTAGE);

        String newCouponCode = "newCoupon";

        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.updateCouponCode(event,1L,ticketingCouponDto,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
    }
    @Test
    void test_updateCouponCode_throwExceptionCouponAlreadyExist() throws IOException {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(false);

        String newCouponCode = "newCoupon";

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.updateCouponCode(event,1L,ticketingCouponDto,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());

    }
    @Test
    void test_updateCouponCode_throwExceptionCanNotNullRecurringRelativeTime() throws IOException {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketingCouponDto.setRecurringRelativeEndTime(null);
        ticketingCouponDto.setRecurringRelativeStartTime(null);

        String newCouponCode = "newCoupon";

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponServiceImpl.updateCouponCode(event,1L,ticketingCouponDto,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_COUPON_NOT_FOUND.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService,times(1)).findTicketingByEvent(any());
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNUll_status_PaidOrCreate() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

       ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndRecurringEventId(any(),any(),anyLong())).thenReturn(ticketOrderIds);
        when(ticketingOrderManagerService.getNumberOfDiscountTicketByOrderIdsAndTicketTypes(any(),any(),any())).thenReturn(1L);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNUll_status_PaidOrCreate_PerOrder() {

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndRecurringEventId(any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNUll_status_UnPaid() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndRecurringEventId(any(),any(),anyLong())).thenReturn(ticketOrderIds);
        when(ticketingOrderManagerService.getNumberOfDiscountTicketByOrderIdsAndTicketTypes(any(),any(),any())).thenReturn(1L);

        //Excecution
        long actual = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,recurringEventId);
        assertEquals(1L,actual);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNUll_status_PaidOrCreate() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);

      ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNUll_status_PaidOrCreate_PerOrder() {

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNUll_status_UnPaid() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
       ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        long actual = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1L,actual);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNUll_status_PaidOrCreate_PerOrder() {

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventid(any(),any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,null);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNUll_status_UnPaid() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventid(any(),any())).thenReturn(ticketOrderIds);
        when(ticketingOrderManagerService.getNumberOfDiscountTicketByOrderIdsAndTicketTypes(any(),any(),any())).thenReturn(1L);
        //Excecution
        long actual = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,null);

        assertEquals(1L,actual);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNUll_status_PaidOrCreate() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventid(any(),any())).thenReturn(ticketOrderIds);
        when(ticketingOrderManagerService.getNumberOfDiscountTicketByOrderIdsAndTicketTypes(any(),any(),any())).thenReturn(1L);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, null,null);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNotNUll_status_PaidOrCreate() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(), any(), any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,null);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNotNUll_status_PaidOrCreate_PerOrder() {

        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(), any(), any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,null);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNotNUll_status_UnPaid() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);
        ticketOrderIds.add(ticketingOrder.getId());
        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(), any(), any())).thenReturn(ticketOrderIds);

        //Excecution
        long actual = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,null);

        assertEquals(1L,actual);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNull_status_PaidOrCreate_case1() {

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, user,recurringEventId);
        assertEquals(0,count);
    }
    @Test
    void test_couponUsed_withoutRecurringeventId_purchaserNotNull_status_PaidOrCreate_case1() {

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, user,null);
        assertEquals(0,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNull_status_PaidOrCreate_case2() {

        //setup
        List<TicketingOrder> ticketingOrders = new ArrayList<>();

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, user,recurringEventId);
        assertEquals(0,count);
    }
    @Test
    void test_couponUsed_withoutRecurringEventId_purchaserNotNull_status_PaidOrCreate_case2() {

        //setup

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.couponUsed(ticketingCoupon, event, user,recurringEventId);
        assertEquals(0,count);
    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNUll_status_Create() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketOrderIds.add(ticketingOrder.getId());
        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);

    }
    @Test
    void test_couponUsed_withRecurringEventId_purchaserNotNUll_status_Create_withExpirein() {

        //setup
        ticketingCoupon.setEventTicketTypeId("");

        ticketingOrder.setExpirein(futureExpireDate);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
       ticketOrderIds.add(ticketingOrder.getId());


        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user, recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsedPerUser_withRecurringId() {

        //setup
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_couponUsedPerUser_withoutRecurringId(Long recurringEventId) {

        //setup
       ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when( ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(),any(),any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_couponUsedPerUser_ticketingOrderStatus_unpaid_withoutRecurringEventId(Long recurringEventId) {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when( ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(),any(),any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1L,count);
    }
    @Test
    void test_couponUsedPerUser_ticketingOrderStatus_unpaid_withRecurringEventId() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock
        when( ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(0,count);
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_couponUsedPerUser_ticketingOrderStatus_create_withoutRecurringEventId(Long recurringEventId) {

        //setup
        ticketingOrder.setExpirein(futureExpireDate);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
       ticketOrderIds.add(ticketingOrder.getId());
        //mock
        when( ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(any(),any(),any())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }
    @Test
    void test_couponUsedPerUser_ticketingOrderStatus_create_withRecurringEventId() {

        //setup
        ticketingOrder.setExpirein(futureExpireDate);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketOrderIds.add(ticketingOrder.getId());
        //mock
        when( ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(1,count);
    }

    private static Object[] getTicketingOrder(){
        return new Object[]{
                new Object[]{null},
                new Object[]{Collections.EMPTY_LIST},
        };
    }
    @ParameterizedTest
    @MethodSource("getTicketingOrder")
    void test_couponUsedPerUser_ticketingOrderList_emptyOrNull_withoutRecurringEventId(List<Long> ticketOrderIds) {
        //setUp
        Long recurringEventId=0L;

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, user)).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user ,recurringEventId);
        assertEquals(0,count);
    }

    @ParameterizedTest
    @MethodSource("getTicketingOrder")
    void test_couponUsedPerUser_ticketingOrderList_nullOrEmpty_withRecurringEventId(List<TicketingOrder> ticketingOrders) {

        //mock
        when( ticketingOrderManagerService.findListByTicketingCouponAndEventidAndPurchaserAndRecurringEventId(any(),any(),any(),anyLong())).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.couponUsedPerUser(ticketingCoupon, event, user,recurringEventId);
        assertEquals(0,count);
    }


    @Test
    void test_getRemainingCount_numberOfUsesLeft_0() {

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(null,ticketingOrder);
        assertEquals(0, count);
    }

    @Test
    void test_getRemainingCount_uses_unlimited() {

        //setup
        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setUses(-1L);
        ticketingCoupon.setUsesPerUser(-1L);

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(-1, count);

    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNotNull() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(2L);
        ticketingCoupon.setUsesPerUser(2L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setEventid(event);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, user)).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(1, count);
    }

    @Test
    void test_getRemainingCount_success_greateThanZero_and_purchaserNotNull() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(3L);
        ticketingCoupon.setUsesPerUser(2L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setEventid(event);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, user)).thenReturn(ticketOrderIds);


        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(1, count);
    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNull() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(2L);
        ticketingCoupon.setUsesPerUser(2L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setEventid(event);

        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(2, count);
    }

    @Test
    void test_getRemainingCount_success_greateThanZero_and_purchaserNull() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(5L);
        ticketingCoupon.setUsesPerUser(2L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setEventid(event);

        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock

        Mockito.doReturn(2L).when(ticketingCouponServiceImpl).couponUsed(any(), any(), isNull(),isNull());

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(2, count);
    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNotNull_and_UsesPerCoupon_unlimited() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(-1L);
        ticketingCoupon.setUsesPerUser(2L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setEventid(event);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, user)).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(1, count);
    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNotNull_and_UsesPerUser_unlimited() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(2L);
        ticketingCoupon.setUsesPerUser(-1L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setEventid(event);

        ticketOrderIds.add(ticketingOrder.getId());

        //mock
        when(ticketingOrderRepoService.findListByTicketingCouponAndEventidAndPurchaser(ticketingCoupon, event, user)).thenReturn(ticketOrderIds);

        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(1, count);
    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNull_and_UsesPerCoupon_unlimited() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setId(1L);
        ticketingCoupon.setName(couponCode);
        ticketingCoupon.setEventId(eventId);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(-1L);
        ticketingCoupon.setUsesPerUser(1L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setEventid(event);

        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(1, count);
    }

    @Test
    void test_getRemainingCount_greateThanZero_and_purchaserNull_and_UsesPerUser_unlimited() {
        //setup
        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setId(1L);
        ticketingCoupon.setName(couponCode);
        ticketingCoupon.setEventId(eventId);
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);
        ticketingCoupon.setUses(2L);
        ticketingCoupon.setUsesPerUser(-1L);

        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        ticketingOrder.setEventid(event);

        List<TicketingOrder> ticketingOrders = new ArrayList<>();
        ticketingOrders.add(ticketingOrder);

        //mock


        //Excecution
        count = ticketingCouponServiceImpl.getRemainingCount(ticketingCoupon,ticketingOrder);
        assertEquals(2, count);
    }

    @Test
    void test_countByEventOrRecurringEvent_recurringEventIdNotNull() {

        //mock
        when(ticketingCouponRepository.countByRecurringEventId(recurringEventId)).thenReturn(BigInteger.valueOf(1));

        long count = ticketingCouponServiceImpl.countByEventOrRecurringEvent(event, recurringEventId);
        assertEquals(1L, count);
    }

    @Test
    void test_countByEventOrRecurringEvent_recurringEventIdNull() {

        //mock
        when(ticketingCouponRepository.countByEventId(eventId)).thenReturn(null);

        long count = ticketingCouponServiceImpl.countByEventOrRecurringEvent(event, 0L);
        assertEquals(0L, count);
    }
}
