package com.accelevents.services.impl;


import com.accelevents.common.dto.CategoryCountDto;
import com.accelevents.domain.ItemCategory;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.ItemCategoryDetails;
import com.accelevents.services.FavoriteItemService;
import com.accelevents.services.ItemService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ModuleServiceImplTest {

    @Spy
    @InjectMocks
    private ModuleServiceImpl moduleServiceImpl;

    @Mock
    private ItemService itemService;

    @Mock
    private FavoriteItemService favoriteItemService;

    @Test
    void testGetItemCategories(){
        //SetUp

        User user = new User();
        user.setUserId(1L);

        List<Long> itemIds = new ArrayList<>();
        itemIds.add(1L);
        ItemCategory itemCategory = new ItemCategory();
        itemCategory.setId(1L);
        itemCategory.setName("Uncategorized");
        itemCategory.setPosition(1000);

        ItemCategoryDetails itemCategoryDetails = new ItemCategoryDetails(1L,itemCategory);

        List<CategoryCountDto> actualDtoList = new ArrayList<>();
        actualDtoList.add(new CategoryCountDto("Favorites",1,"Favorites"));
        //Mock Data
        when(favoriteItemService.countFavoriteItemByUserIdAndModuleIdAndActive(user.getUserId(),1L)).thenReturn(BigInteger.ONE);
        when(itemService.getActiveItemIdAndCategories(1L, ModuleType.AUCTION)).thenReturn(Collections.singletonList(itemCategoryDetails));
        //Execution
        List<CategoryCountDto> expected = moduleServiceImpl.getItemCategories(1L,ModuleType.AUCTION,user, false);

        assertEquals(expected.get(0).getName(),actualDtoList.get(0).getName());
        assertEquals(expected.get(0).getCount(),actualDtoList.get(0).getCount());
    }
}
