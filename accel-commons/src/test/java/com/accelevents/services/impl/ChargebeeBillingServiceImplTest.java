package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.ChargebeeChargeConfigDto;
import com.accelevents.billing.chargebee.dto.CustomerCardDetailDto;
import com.accelevents.billing.chargebee.dto.ModuleActivationDto;
import com.accelevents.billing.chargebee.enums.ChargeConfigNames;
import com.accelevents.billing.chargebee.repo.ChargeConfigRepoService;
import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.repositories.ChargebeeTransactionRepository;
import com.accelevents.billing.chargebee.service.ChargeConfigService;
import com.accelevents.billing.chargebee.service.ChargebeePaymentService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.billing.chargebee.service.impl.ChargebeeBillingServiceImpl;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.utils.JsonMapper;
import com.chargebee.Environment;
import com.chargebee.models.Customer;
import com.chargebee.models.Invoice;
import com.chargebee.models.PaymentSource;
import com.chargebee.models.enums.AutoCollection;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.accelevents.utils.Constants.STRING_EMPTY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ChargebeeBillingServiceImplTest {

    @Spy
    @InjectMocks
    private ChargebeeBillingServiceImpl chargebeeBillingServiceImpl;
    
    @Mock
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Mock
    private ChargebeePaymentService chargebeePaymentService;
    @Mock
    private EventService eventService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private AuctionService auctionService;
    @Mock
    private CauseAuctionService causeAuctionService;
    @Mock
    private RaffleService raffleService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private ChargebeeConfiguration chargebeeConfiguration;
    @Mock
    private ChargeConfigService chargeConfigService;
    @Mock
    private DonationSettingsService donationSettingsService;
    @Mock
    private OrganizerRepoService organizerRepoService;
    @Mock
    private EventBillingAddOnsService eventBillingAddOnsService;
    @Mock
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Mock
    private ROVirtualEventService roVirtualEventService;
    @Mock
    private ChargebeeTransactionRepository chargebeeTransactionRepository;
    @Mock
    private ChargeConfigRepoService chargeConfigRepoService;

    private Event event;
    private User user,billingContact;
    private Organizer organizer;
    private ChargeBeeDetails chargeBeeDetails;
    private Ticketing ticketing;
    private Auction auction;
    private CauseAuction causeAuction;
    private Raffle raffle;
    private StripeTransaction stripeTransaction;
    private Customer customer;
    private PaymentSource card;
    private CustomerCardDetailDto customerCardDetailDto;
    private ModuleActivationDto moduleActivationDto,moduleActivationDto1;
    private CountryCode countryCode;
    private ChargebeeTransaction chargebeeTransaction;
    List<ModuleActivationDto> moduleActivationDtos = new ArrayList<>();
    List<ChargebeeChargeConfigDto> chargebeeChargeConfigDtos = new ArrayList<>();
    ChargebeeChargeConfigDto chargebeeChargeConfigDto,chargebeeChargeConfigDto1;
    private Invoice invoiceObj;
    private  DonationSettings donationSettings;
    private EventBillingAddOns eventBillingAddOns;
    private VirtualEventSettings virtualEventSettings;

    private Long id = 1L;
    String token = "tokenOrCardNonce";
    String startDate = "01/01/2019 00:00:00";
    String endDate = "04/04/2019 00:00:00";
    String subscriptionId = "AzyzZ1SyLAKj45yC";

    @BeforeEach
    void setUp() throws Exception{
        Environment.configure("accelevents-test","test_SOMuNJcXBocu1Ij5o4wz5cushw3Lnpuv0p");
        MockitoAnnotations.openMocks(this);
        user = EventDataUtil.getUser();
        auction = new Auction();
        causeAuction = new CauseAuction();
        raffle = new Raffle();
        countryCode = CountryCode.IN;
        chargebeeConfiguration.setChargebeeAPIKey("test_SOMuNJcXBocu1Ij5o4wz5cushw3Lnpuv0p");
        chargebeeConfiguration.setChargebeeSite("accelevents-test");

        customer = Customer.create().firstName("Ruutu")
                .lastName("Kathiriya")
                .email("<EMAIL>")
                .autoCollection(AutoCollection.OFF)
                .request()
                .customer();

        card = PaymentSource.createCard()
                .customerId(customer.id())
                .cardNumber("***************")
                .cardCvv("100")
                .cardExpiryYear(2028)
                .cardExpiryMonth(12)
                .request()
                .paymentSource();
        customerCardDetailDto = new CustomerCardDetailDto(customer,card);

        billingContact = new User();
        billingContact.setUserId(2L);
        billingContact.setFirstName("Rutu");
        billingContact.setLastName("K");

        getOrganizerData();
        getTicketingData();
        getEventData();
        getModuleActivationDtos();
        getChargebeeDetails();

    }

    private void getChargebeeTransaction(){
        chargebeeTransaction = new ChargebeeTransaction(event, 1d, ChargebeeTransactionSource.MODULE_ACTIVATE, subscriptionId,
                customer.id(), card != null ? card.id() : null, card != null ? card.card().last4() : null,
                JsonMapper.convertToString(moduleActivationDtos),
                null != invoiceObj ? invoiceObj.id() : null, null != invoiceObj ? invoiceObj.status().name() : STRING_EMPTY, new Date(),
                null, user, event.getWhiteLabel(), event.getOrganizer());
    }
    private void getOrganizerData(){
        organizer = new Organizer();
        organizer.setId(id);
        organizer.setName("RK");
        organizer.setSubscriptionId(subscriptionId);
    }
    private void getTicketingData(){
        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setEventid(event);
        ticketing.setEventStartDate(new Date(startDate));
        ticketing.setEventEndDate(new Date(endDate));
        ticketing.setRecordStatus(RecordStatus.CREATE);
    }
    private void getModuleActivationDtos(){
        moduleActivationDto = new ModuleActivationDto();
        moduleActivationDto.setNeedToActivate(true);
        moduleActivationDto.setModuleName(ModuleActivationName.PRO_EXHIBITOR);
        moduleActivationDto.setModuleActivationCharge(ChargeConfigNames.EXHIBITOR_PRO_BOOTH_CHARGE);
        moduleActivationDto.setQuantity(1L);
        moduleActivationDtos.add(moduleActivationDto);

        moduleActivationDto1 = new ModuleActivationDto();
        moduleActivationDto.setNeedToActivate(true);
        moduleActivationDto.setModuleName(ModuleActivationName.FUND_A_NEED);
        moduleActivationDto.setModuleActivationCharge(ChargeConfigNames.FUND_A_NEED_BIDDER_CHARGE);
        moduleActivationDto.setQuantity(1L);
        moduleActivationDtos.add(moduleActivationDto);
    }
    private void getEventData(){
        event =  new Event("TestEvent1", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
        event.setEventId(id);
        event.setEventURL("TestEvent1");
        event.setCurrency(Currency.USD);
        event.setTimezoneId("India Time");
        event.setAuctionId(id);
        event.setCreditCardEnabled(true);
        event.setEquivalentTimeZone("Asia/Calcutta");
        event.setTicketingEnabled(true);
        event.setCreditCardEnabled(true);
        event.setTicketingId(id);
        event.setPhoneNumber(9999999999L);
        event.setEventFormat(EventFormat.VIRTUAL);
        event.setOrganizer(organizer);
        event.setOrganizerId(organizer.getId());
    }
    private void getChargebeeDetails(){
        chargeBeeDetails = new ChargeBeeDetails();
        chargeBeeDetails.setSubscriptionId(subscriptionId);
        chargeBeeDetails.setChargebeeCustomerId(customer.id());

    }
    private void getDonationSettingsData(){
        donationSettings = new DonationSettings();
        donationSettings.setId(id);
        donationSettings.setEventId(id);
        donationSettings.setTextToGiveActivated(false);
    }
    private void getEventBillingAddOnsData(){
        eventBillingAddOns = new EventBillingAddOns();
        eventBillingAddOns.setId(id);
        eventBillingAddOns.setEventId(id);
        eventBillingAddOns.setNumberOfBooths(1L);
        eventBillingAddOns.setAddOnType(AddOnType.PRO_BOOTH);
        eventBillingAddOns.setPaymentStatus(PaymentStatus.PAID);
        eventBillingAddOns.setRecordEnteredByEmployeeId(user.getUserId());
        eventBillingAddOns.setNotes("This is added from Activate module page.");
    }
    private void getInvoiceData() throws Exception {
        invoiceObj = Invoice.createForChargeItemsAndCharges()
                .customerId(customer.id())
                .itemPriceItemPriceId(1,"Exhibitor-Pro-Booth-Charge-USD")
                .invoiceNote("notes")
                .itemPriceQuantity(1,1)
                .request()
                .invoice();
    }
    private void getChargebeeChargeConfigDtos(){
        chargebeeChargeConfigDto = new ChargebeeChargeConfigDto();
        chargebeeChargeConfigDto.setId(3);
        chargebeeChargeConfigDto.setChargeId("Exhibitor-Pro-Booth-Charge-USD");
        chargebeeChargeConfigDto.setAmount(99d);
        chargebeeChargeConfigDto.setChargeDisplayName("Exhibitor Pro Booth Charge");
        chargebeeChargeConfigDtos.add(chargebeeChargeConfigDto);

        chargebeeChargeConfigDto1 = new ChargebeeChargeConfigDto();
        chargebeeChargeConfigDto1.setId(12);
        chargebeeChargeConfigDto1.setChargeId("Fund-A-Need-Bidder-Charge-USD");
        chargebeeChargeConfigDto1.setAmount(1d);
        chargebeeChargeConfigDto1.setChargeDisplayName("Fund A Need Bidder Charge");
        chargebeeChargeConfigDtos.add(chargebeeChargeConfigDto1);
    }
    private void getVirtualEventSettingsData(){
        virtualEventSettings = new VirtualEventSettings();
        virtualEventSettings.setId(id);
        virtualEventSettings.setEvent(event);
        virtualEventSettings.setEventId(id);
        virtualEventSettings.setNumberOfLiveStreamingExhibitorsPurchased(0L);
    }

    @Test
    void test_activeEventModulesInChargebee_success() throws Exception {

        //setup
        List<ModuleActivationDto> moduleActivationDtos = new ArrayList<>();

        moduleActivationDto = new ModuleActivationDto();
        moduleActivationDto.setNeedToActivate(true);
        moduleActivationDto.setModuleName(ModuleActivationName.PRO_EXHIBITOR);
        moduleActivationDto.setModuleActivationCharge(ChargeConfigNames.EXHIBITOR_PRO_BOOTH_CHARGE);
        moduleActivationDto.setQuantity(1L);
        moduleActivationDtos.add(moduleActivationDto);

        String countryCode1 = CountryCode.IN.name();
        stripeTransaction = new StripeTransaction();
        Long quantity = 1L;
        List<String> chargeDisplayNameList = new ArrayList<>();
        chargeDisplayNameList.add(ChargeConfigNames.EXHIBITOR_PRO_BOOTH_CHARGE.toString());
        getChargebeeChargeConfigDtos();
        //getInvoiceData();
        getChargebeeTransaction();
        customerCardDetailDto.setCard(card);
        customerCardDetailDto.setCustomer(customer);
        getDonationSettingsData();
        getEventBillingAddOnsData();
        getVirtualEventSettingsData();


        //mock
        when(eventService.getEventByIdWithoutCache(anyLong())).thenReturn(event);
        when(chargebeeBillingServiceImpl.getCountryCode(countryCode1,event)).thenReturn(countryCode);
        when(ticketingHelperService.findByEventId(anyLong())).thenReturn(ticketing);
        when(auctionService.findByEvent(any())).thenReturn(auction);
        when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
        when(raffleService.findByEvent(any())).thenReturn(raffle);
        when(chargeBeePaymentHandler.getOrgOrWLBillingContact(any(),any())).thenReturn(user);
        when(chargeBeePaymentHandler.getOrganizerOrWLChargebeeDetails(event)).thenReturn(chargeBeeDetails);
        when(chargebeePaymentService.createAndRetrieveCardForCustomer(anyString(),any(),any(),anyString(),anyString())).thenReturn(customerCardDetailDto);
        when(donationSettingsService.getByEventIdNotFromCache(anyLong())).thenReturn(donationSettings);
        when(chargebeeBillingServiceImpl.isGivenModuleActive(moduleActivationDtos,ModuleActivationName.PRO_EXHIBITOR)).thenReturn(true);
        when(chargebeeBillingServiceImpl.getQuantityOfGivenModule(moduleActivationDtos,ChargeConfigNames.EXHIBITOR_PRO_BOOTH_CHARGE.getLableName())).thenReturn(quantity);
        when(organizerRepoService.findByIdThrowException(anyLong())).thenReturn(organizer);
        when(roVirtualEventService.findVirtualEventSettingsByEventId(anyLong())).thenReturn(virtualEventSettings);

        //Execution
        Event eventUrlData = chargebeeBillingServiceImpl.activeEventModulesInChargebee(event.getEventId(),moduleActivationDtos,user,token,countryCode1,"Update ChargeBee Billing Setting Test");

        //Assertion
        assertEquals(eventUrlData.getEventURL(), event.getEventURL());
        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, times(1)).save(stripeTransactionArgumentCaptor.capture());

        ArgumentCaptor<ChargebeeTransaction> chargebeeTransactionArgumentCaptor = ArgumentCaptor.forClass(ChargebeeTransaction.class);
        verify(chargebeeBillingServiceImpl, times(1)).save(chargebeeTransactionArgumentCaptor.capture());

        Event eventData = eventArgumentCaptor.getValue();
        assertEquals(eventData.getEventURL(), event.getEventURL());
        assertEquals(eventData.getEventId(), event.getEventId());
        assertEquals(eventData.getName(), event.getName());
        assertEquals(eventData.getOrganizer(),organizer);

        ChargebeeTransaction chargebeeTransactionData = chargebeeTransactionArgumentCaptor.getValue();
        assertEquals(chargebeeTransactionData.getEvent(),event);
        assertEquals(chargebeeTransactionData.getCreatedBy(),user);
        assertEquals(chargebeeTransactionData.getSubscriptionId(),subscriptionId);
        assertEquals(chargebeeTransactionData.getOrganizer(),organizer);

    }

}
