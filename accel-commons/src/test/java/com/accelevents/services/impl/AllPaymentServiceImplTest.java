package com.accelevents.services.impl;

import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.PaymentMethodTypes;
import com.accelevents.dto.CardInfoDto;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.dto.StripeCreditCardDto;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.services.SquarePaymentService;
import com.accelevents.services.StripePaymentService;
import com.accelevents.spreedly.dto.SpreedlyGatewayType;
import com.accelevents.spreedly.dto.transactions.response.TransactionDataDTO;
import com.accelevents.spreedly.service.SpreedlyGatewayService;
import com.accelevents.ticketing.dto.ChargeDto;
import com.accelevents.utils.Constants;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.models.Payment;
import com.squareup.square.models.*;
import com.stripe.exception.StripeException;
import com.stripe.model.Card;
import com.stripe.model.Customer;
import com.stripe.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AllPaymentServiceImplTest {

    @Spy
    @InjectMocks
    private AllPaymentServiceImpl allPaymentServiceImpl;

    @Mock
    private StripePaymentService stripePaymentService;
    @Mock
    private SquarePaymentService squarePaymentService;
    @Mock
    private StripeCustomerService stripeCustomerService;
    @Mock
    private SpreedlyGatewayService spreedlyGatewayService;

    private Stripe stripe;
    private ChargeResponse chargeResponse;
    private User user;
    private Event event;
    private Card card;
    private RefundInfoDto refundInfoDto;
    private FeeRefund feeRefund;
    private Account account;
    private StripeCreditCardDto stripeCreditCardDto;
    private CardInfoDto cardInfoDto;
    private StripeTransaction stripeTransaction;
    private StripeCustomers stripeCustomers;
    private Transaction transaction;
    private Tender tender;
    private Customer customer;
    private com.squareup.square.models.Money money;

    private String customerId = "1";
    private String cardId = "cardId";
    private String currency = "USD";
    private String tenderId = "tender Id";
    private String id = "1";
    private String email = "email";
    private String description = "description";
    private static Integer expMonth = 12;
    private static Integer expYear = 2029;
    private String lastFour = "1111";
    private String accessToken = "accessToken";
    private String chargeId = "1";
    private String stripeUserId = "1";
    private String cardType = "card type";
    private String tokenOrCardNonce = "tokenOrCardNonce";
    private String status = "refunded";
    private String apiKey = "api key";
    private int limit = 1;
    private double amount = 100d;
    private double applicationFeeRefundAmount = 1d;

    @BeforeEach
    void setUp() throws Exception {

        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();

        stripe = new Stripe();

        Charge charge = new Charge();

        ChargeDto chargeDto = new ChargeDto();

      //  chargeResponse = new ChargeResponse();

        //transaction = new Transaction();

     //   tender = new Tender();

        card = new Card();

        customer = new Customer();

        feeRefund = new FeeRefund();

        refundInfoDto = new RefundInfoDto();

        account = new Account();

        stripeCreditCardDto = new StripeCreditCardDto();

        stripeCustomers = new StripeCustomers();
    }

//    @Test
//    public void test_createCharge_success_with_paymentGateWay_stripe() throws StripeException, ApiException {
//
//        //setup
//        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//
//        charge.setId(id);
//
//        Map<String, Object> metadata = new HashMap<>();
//
//        chargeDto.setAmount(10L);
//        chargeDto.setId(id);
//        chargeDto.setLocationId(locationId);
//        chargeDto.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//        chargeDto.setTenderId(tenderId);
//
//        //mock
//        when(stripePaymentService.createCharge(stripe.getAccessToken(), customerId, cardId, amount, currency, metadata, statementDescriptor)).thenReturn(charge);
//
//        //Execution
//        ChargeDto chargeData = allPaymentServiceImpl.createCharge(customerId, cardId, amount, currency, stripe, statementDescriptor, metadata, null);
//
//        assertEquals(chargeData.getId(), charge.getId());
//        assertEquals(chargeData.getPaymentGateway(), chargeDto.getPaymentGateway());
//    }

//    @Test
//    public void test_createCharge_success_with_paymentGateWay_square() throws StripeException, ApiException {
//
//        //setup
//        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
//
//        tender.setId(id);
//
//        List<Tender> tenderList = new ArrayList<>();
//        tenderList.add(tender);
//
//        transaction.setId(id);
//        transaction.setLocationId(locationId);
//        transaction.setTenders(tenderList);
//
//        chargeResponse.setTransaction(transaction);
//
//        Map<String, Object> metadata = new HashMap<>();
//
//        chargeDto.setAmount(10L);
//        chargeDto.setId("1");
//        chargeDto.setLocationId(locationId);
//        chargeDto.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
//        chargeDto.setTenderId(tenderId);
//
//        //mock
//        when(squarePaymentService.createCharge(stripe.getAccessToken(), customerId, cardId, amount, currency, metadata)).thenReturn(chargeResponse);
//
//        //Execution
//        ChargeDto chargeData = allPaymentServiceImpl.createCharge(customerId, cardId, amount, currency, stripe, statementDescriptor, metadata);
//
//        assertEquals(chargeData.getId(), transaction.getId());
//        assertEquals(chargeData.getPaymentGateway(), chargeDto.getPaymentGateway());
//        assertEquals(chargeData.getLocationId(), transaction.getLocationId());
//        assertEquals(chargeData.getTenderId(), tender.getId());
//    }

//    @Test
//    public void test_createCustomer_success_with_paymentGateWay_square() throws StripeException, ApiException{
//
//        //setup
//        Map<String, Object> metadata = new HashMap<>();
//
//        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
//
//        customerCardDto = new CustomerCardDto(customerId, null);
//
//        //mock
//        when(squarePaymentService.createCustomerAndCardToCustomer(stripe.getAccessToken(),token, metadata, email, description)).thenReturn(customerCardDto);
//
//        //Execution
//        CustomerCardDto customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(token, metadata, email, description, stripe);
//
//        assertEquals(customerData.getCustomerId(), customerCardDto.getCustomerId());
//        assertNull(customerData.getCardId());
//    }

//    @Test
//    public void test_createCustomer_success_with_paymentGateWay_stripe() throws StripeException, ApiException{
//
//        //setup
//        Map<String, Object> metadata = new HashMap<>();
//
//        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//
//        customerCardDto = new CustomerCardDto(customerId, null);
//
//        //mock
//        when(stripePaymentService.createCustomerAndCardToCustomer(stripe.getAccessToken(), token, metadata, email, description, false, false, )).thenReturn(customerCardDto);
//
//        //Execution
//        CustomerCardDto customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(token, metadata, email, description, stripe);
//
//        assertEquals(customerData.getCustomerId(), customerCardDto.getCustomerId());
//        assertNull(customerData.getCardId());
//    }

   /* @Test
    public void test_createCustomer1_success_with_paymentGateWay_stripe() throws StripeException, ApiException{

        //setup
        Map<String, Object> metadata = new HashMap<>();

        card.setId(id);
        card.setLast4("1111");
        card.setBrand("brand");
        card.setExpMonth(9l);
        card.setExpYear(19l);

        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        customerCardDto = new CustomerCardDto(customerId, cardId);

        //mock
        when(stripePaymentService.createCustomerAndCardToCustomer(stripe.getAccessToken(), token, metadata, email, description)).thenReturn(customerCardDto);
        when(stripePaymentService.retrieveDefaultPaymentIdOfCustomer(stripe.getAccessToken(), customerCardDto.getCustomerId())).thenReturn(card);
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(card.getId(), user, event)).thenReturn(null);

        //Execution
        CustomerCardDto customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(token, metadata, email, description, stripe, user, event);

        assertEquals(customerData.getCustomerId(), customerCardDto.getCustomerId());
        assertEquals(customerData.getCardId(), customerCardDto.getCardId());

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card.getId());
        assertEquals(actualData.getCardType(), card.getBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(Long.valueOf(actualData.getExpDateMonth()), card.getExpMonth());
        assertEquals(Long.valueOf(actualData.getExpDateYear()), card.getExpYear());
        assertEquals(actualData.getLastFour(), card.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.STRIPE.value());
        assertTrue(actualData.getDefault());
    }*/

    @Test
    public void test_createCustomer1_success_with_paymentGateWay_square() throws StripeException, ApiException{

        //setup
        Map<String, Object> metadata = new HashMap<>();

       stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card = new com.squareup.square.models.Card.Builder().build().toBuilder().id(id).cardBrand("OTHER_BRAND").last4("1111").expMonth(9l).expYear(19l).build();

        CustomerCardDto customerCardDto = new CustomerCardDto(customerId, cardId);

        //mock
        when(squarePaymentService.createCustomer(any(),any(),any(),any(),any())).thenReturn(customerCardDto);
        when(squarePaymentService.retrieveDefaultCardOfCustomer(stripe.getAccessToken(), customerCardDto.getCustomerId(),cardId)).thenReturn(card);
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(card.getId(), user, event)).thenReturn(null);

        //Execution
        String token = "token";
        CustomerCardDto customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(token, stripe, user, event, false, null);

        assertEquals(customerData.getCustomerId(), customerCardDto.getCustomerId());
        assertEquals(customerData.getCardId(), customerCardDto.getCardId());

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card.getId());
        assertEquals(actualData.getCardType(), card.getCardBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth().longValue(), card.getExpMonth().longValue());
        assertEquals(actualData.getExpDateYear().longValue(), card.getExpYear().longValue());
        assertEquals(actualData.getLastFour(), card.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
        assertTrue(actualData.getDefault());
    }

    @Test
    void test_createCustomer1_success_with_paymentGateWay_spreedly_payment_gateway() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(SpreedlyGatewayType.SP_BRAINTREE.name());
        CustomerCardDto customerCardDto = new CustomerCardDto(customerId, cardId);
        customerCardDto.setLastFour(lastFour);
        customerCardDto.setExpDateMonth(expMonth);
        customerCardDto.setExpDateYear(expYear);
        customerCardDto.setCardType(cardType);
        customerCardDto.setFingerPrint(tokenOrCardNonce);

        //mock
        when(spreedlyGatewayService.getCustomerIdFromSpreedlyThirdPartyVault(stripe, cardId, event, user, null)).thenReturn(customerCardDto);
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(cardId, user, event)).thenReturn(null);

        //Execution
        CustomerCardDto customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(cardId, stripe, user, event, false, null);

        assertEquals(customerData.getCustomerId(), customerCardDto.getCustomerId());
        assertEquals(customerData.getCardId(), customerCardDto.getCardId());

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), customerCardDto.getCardId());
        assertEquals(actualData.getCardType(), customerCardDto.getCardType() );
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth(), customerCardDto.getExpDateMonth());
        assertEquals(actualData.getExpDateYear(), customerCardDto.getExpDateYear());
        assertEquals(actualData.getLastFour(), customerCardDto.getLastFour());
        assertEquals(actualData.getPaymentGateway(), SpreedlyGatewayType.SP_BRAINTREE.name());
        assertTrue(actualData.getDefault());
    }

    @Test
    public void test_createCustomer2_success_with__paymentGateWay_stripe() throws StripeException, ApiException {   // this is incomplete

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        customer.setId(id);

        //mock
        when(stripePaymentService.createCustomerAndAddCardToCustomer(any(), any())).thenReturn(customer);

        //Execution
        String customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(email, stripe);

        assertEquals(customerData, customer.getId());
    }

    @Test
    public void test_createCustomer2_success_with_paymentGateWay_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
        stripe.setAccessToken("accessToken");

        com.squareup.square.models.Customer customer = new com.squareup.square.models.Customer(id,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null, null);

        //mock
        when(squarePaymentService.createCustomer(anyString(), anyString())).thenReturn(customer);

        //Execution
        String customerData = allPaymentServiceImpl.createCustomerAndCardToCustomer(email, stripe);

        assertEquals(customerData, customer.getId());
    }

    @Test
    public void test_getLast4Digits_success_with__paymentGateWay_stripe() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setLast4(lastFour);

        //mock
        when(stripePaymentService.getLast4Digits(stripe.getAccessToken(), customerId)).thenReturn(card.getLast4());

        //Execution
        String lastFour = allPaymentServiceImpl.getLast4Digits(customerId, stripe);

        assertEquals(lastFour, card.getLast4());
    }

    @Test
    public void test_getLast4Digits_success_with__paymentGateWay_square() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        card.setLast4(lastFour);

        //mock
        when(squarePaymentService.getLast4Digits(stripe.getAccessToken(), customerId)).thenReturn(card.getLast4());

        //Execution
        String lastFour = allPaymentServiceImpl.getLast4Digits(customerId, stripe);

        assertEquals(lastFour, card.getLast4());
    }

    /*@Test
    public void test_createChargeToCustomer_success_with_stripe() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        Map<String, Object> metadata = new HashMap<>();

        BalanceTransaction balanceTransaction = new BalanceTransaction();
        balanceTransaction.setNet(10L);

        charge.setId(id);
        charge.setAmount(10L);
        charge.setBalanceTransactionObject(balanceTransaction);

        //mock
        doReturn(charge).when(stripePaymentService).createChargeToCustomer(any(), anyString(), anyDouble(), anyString(),
                anyBoolean(), anyDouble(), anyString(), anyMap(), anyString(),anyString(), null);

        //Execution
        ChargeDto chargeToCustomerData = allPaymentServiceImpl.createChargeToCustomer(customerId, amount,
                currency, true, metadata, applicationFee, stripe, cardId, statementDescriptor
            ,"transferGroupA", null);

        assertEquals(chargeToCustomerData.getPaymentGateway(), EnumPaymentGateway.STRIPE.value());
        assertEquals(chargeToCustomerData.getAmount(), charge.getAmount().longValue());
    }*/

    @Test
    public void test_createChargeToCustomer_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        Map<String, Object> metadata = new HashMap<>();

        money = new com.squareup.square.models.Money(10L, "USD");

        TenderCardDetails tenderCardDetails = new TenderCardDetails("CAPTURED",null,null);

        List<Tender> tenderList = new ArrayList<>();
        tenderList.add(tender);

        String locationId = "location id";

        //mock
        double applicationFee = 1d;

        CardPaymentDetails cardDetails = new CardPaymentDetails("status", null, null, "cvvStatus", "avsStatus", "authResultCode", "applicationIdentifier", "applicationName", "applicationCryptogram", "verificationMethod", "verificationResults", "statementDescription", null, null, false, null);

        Payment payment = new Payment("1", "createdAt", "updatedAt", money, money, money, money, money, null, money,
                "status", "delayDuration", "delayAction", "delayedUntil", "sourceType", cardDetails, null, null, null, null, null, null, "locationId", "orderId", "referenceId",
                "customerId", "employeeId", "teamMemberId", null, null, "buyerEmailAddress", null, null, "note",
                "statementDescriptionIdentifier", null, "receiptNumber", "receiptUrl",null, null, "versionToken");
        CreatePaymentResponse createPaymentResponse = new CreatePaymentResponse(null, payment);
        when(squarePaymentService.createChargeToCustomer(stripe, customerId, amount, applicationFee, currency, true, cardId, metadata, null)).thenReturn(createPaymentResponse);

        //Execution
        String statementDescriptor = "statementDescription";
        ChargeDto chargeToCustomerData = allPaymentServiceImpl.createChargeToCustomer(customerId, amount, currency,
                true, metadata, applicationFee, stripe, cardId, statementDescriptor,null, null, false);

        assertEquals(chargeToCustomerData.getId(), "1");
        assertEquals(chargeToCustomerData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
        assertEquals(chargeToCustomerData.getLocationId(), "locationId");
        assertEquals(chargeToCustomerData.getTenderId(), "1");
        assertEquals(chargeToCustomerData.getAmount(), 10.0, 0);
    }

    @Test
    void test_createChargeToCustomer_success_with_spreedly_payment_processor() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(SpreedlyGatewayType.SP_BRAINTREE.name());

        Map<String, Object> metadata = new HashMap<>();
        TransactionDataDTO transactionDataDTO = new TransactionDataDTO();
        transactionDataDTO.setAmount((int) amount);
        transactionDataDTO.setState(Constants.SUCCEEDED);
        transactionDataDTO.setSucceeded(true);
        transactionDataDTO.setToken(chargeId);

        String statementDescriptor = "statementDescription";
        when(spreedlyGatewayService.createChargeFromSpreedly(stripe, customerId, amount, currency, true, statementDescriptor, metadata, cardId)).thenReturn(transactionDataDTO);

        //Execution
        ChargeDto chargeToCustomerData = allPaymentServiceImpl.createChargeToCustomer(customerId, amount, currency,
                true, metadata, 0, stripe, cardId, statementDescriptor,null, null, false);

        assertEquals(chargeId, chargeToCustomerData.getId());
        assertEquals(amount, chargeToCustomerData.getAmount());
        assertEquals(Constants.SUCCEEDED, chargeToCustomerData.getStatus());
        assertEquals(SpreedlyGatewayType.SP_BRAINTREE.name(), chargeToCustomerData.getPaymentGateway());
        assertEquals(PaymentMethodTypes.CARD.name(), chargeToCustomerData.getPaymentMethodType());
    }

    @Test
    public void test_getDefaultSource_success_with_stripe() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        customer.setId(customerId);

        //mock
        when(stripePaymentService.getDefaultSource(stripe.getAccessToken(), customerId)).thenReturn(customer.getId());

        //Execution
        String customerData = allPaymentServiceImpl.getDefaultSource(customerId, stripe);

        assertEquals(customerData, customer.getId());
    }

    @Test
    public void test_getDefaultSource_success_with_square() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        customer.setId(customerId);

        //mock
        when(squarePaymentService.getDefaultSource(stripe.getAccessToken(), customerId)).thenReturn(customer.getId());

        //Execution
        String customerData = allPaymentServiceImpl.getDefaultSource(customerId, stripe);

        assertEquals(customerData, customer.getId());
    }

    @Test
    public void test_createApplicationFeeRefund_success_with_stripe() throws StripeException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        feeRefund.setAmount(10L);

        //mock
        when(stripePaymentService.createApplicationFeeRefund(accessToken, chargeId, applicationFeeRefundAmount, null)).thenReturn(feeRefund);

        //Execution
        long feeRefundAmount = allPaymentServiceImpl.createApplicationFeeRefund(accessToken, chargeId, applicationFeeRefundAmount, stripe, refundInfoDto);

        assertEquals(feeRefundAmount, feeRefund.getAmount().longValue());
    }

    @Test
    public void test_createApplicationFeeRefund_success_with_feeRefund_null() throws StripeException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        feeRefund.setAmount(10L);

        //mock
        when(stripePaymentService.createApplicationFeeRefund(accessToken, chargeId, applicationFeeRefundAmount, null)).thenReturn(null);

        //Execution
        long feeRefundAmount = allPaymentServiceImpl.createApplicationFeeRefund(accessToken, chargeId, applicationFeeRefundAmount, stripe, refundInfoDto);

        assertEquals(feeRefundAmount, 0);
    }

    @Test
    public void test_createApplicationFeeRefund_success_with_square() throws StripeException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        refundInfoDto.setRefundedApplicationFee(10L);

        //Execution
        long feeRefundAmount = allPaymentServiceImpl.createApplicationFeeRefund(accessToken, chargeId, applicationFeeRefundAmount, stripe, refundInfoDto);

        assertEquals(feeRefundAmount, refundInfoDto.getRefundedApplicationFee());
    }

    @Test
    public void test_retrieveAccount_success_with_stripe() throws StripeException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        account.setType("stripe");
        account.setPayoutsEnabled(true);
        Account.Settings.Branding settingsBranding = new Account.Settings.Branding();
        settingsBranding.setPrimaryColor("#D3D3D3");

        Account.Settings settings = new Account.Settings();
        settings.setBranding(settingsBranding);

        Account.Settings.Payouts settingsPayouts = new Account.Settings.Payouts();
        settingsPayouts.setDebitNegativeBalances(false);
        settings.setPayouts(settingsPayouts);

        account.setSettings(settings);

        //mock
        when(stripePaymentService.retrieveAccount(stripeUserId)).thenReturn(account);

        //Execution
        Account accountData = allPaymentServiceImpl.retrieveAccount(stripeUserId, stripe);

        assertEquals(accountData.getType(), account.getType());
        assertEquals(accountData.getPayoutsEnabled(), account.getPayoutsEnabled());
        /*assertEquals(accountData.getBusinessPrimaryColor(), account.getBusinessPrimaryColor());
        assertEquals(accountData.getDebitNegativeBalances(), account.getDebitNegativeBalances());*/
    }

    @Test
    public void test_retrieveAccount_success_with_square() throws StripeException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        //Execution
        Account accountData = allPaymentServiceImpl.retrieveAccount(stripeUserId, stripe);

        assertNull(accountData);
    }

    @Test
    public void test_getAllCardsForCustomer_success_with_stripe() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        stripeCreditCardDto.setCardType(cardType);
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setExpmonth(9);
        stripeCreditCardDto.setExpyear(19);
        stripeCreditCardDto.setId(id);
        stripeCreditCardDto.setLast4(lastFour);

        List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();
        stripeCreditCardDtoList.add(stripeCreditCardDto);

        //mock
        when(stripePaymentService.getAllCardsForCustomer(stripe.getAccessToken(), customerId, limit)).thenReturn(stripeCreditCardDtoList);

        //Execution
        List<StripeCreditCardDto> allCardsForCustomerData = allPaymentServiceImpl.getAllCardsForCustomer(customerId, limit, stripe, user, event);

        for (StripeCreditCardDto actualData : allCardsForCustomerData) {

            assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
            assertEquals(actualData.isDefaultCard(), stripeCreditCardDto.isDefaultCard());
            assertEquals(actualData.getExpmonth(), stripeCreditCardDto.getExpmonth());
            assertEquals(actualData.getExpyear(), stripeCreditCardDto.getExpyear());
            assertEquals(actualData.getId(), stripeCreditCardDto.getId());
            assertEquals(actualData.getLast4(), stripeCreditCardDto.getLast4());
        }

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), stripeCreditCardDto.getId());
        assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth(), stripeCreditCardDto.getExpmonth());
        assertEquals(actualData.getExpDateYear(), stripeCreditCardDto.getExpyear());
        assertEquals(actualData.getLastFour(), stripeCreditCardDto.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.STRIPE.value());
        assertEquals(actualData.getDefault(), stripeCreditCardDto.isDefaultCard());
    }

    @Test
    public void test_getAllCardsForCustomer_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        stripeCreditCardDto.setCardType(cardType);
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setExpmonth(9);
        stripeCreditCardDto.setExpyear(19);
        stripeCreditCardDto.setId(id);
        stripeCreditCardDto.setLast4(lastFour);

        List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();
        stripeCreditCardDtoList.add(stripeCreditCardDto);

        //mock
        when(squarePaymentService.getAllCardsForCustomer(stripe.getAccessToken(), customerId)).thenReturn(stripeCreditCardDtoList);

        //Execution
        List<StripeCreditCardDto> allCardsForCustomerData = allPaymentServiceImpl.getAllCardsForCustomer(customerId, limit, stripe, user, event);

        for (StripeCreditCardDto actualData : allCardsForCustomerData) {

            assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
            assertEquals(actualData.isDefaultCard(), stripeCreditCardDto.isDefaultCard());
            assertEquals(actualData.getExpmonth(), stripeCreditCardDto.getExpmonth());
            assertEquals(actualData.getExpyear(), stripeCreditCardDto.getExpyear());
            assertEquals(actualData.getId(), stripeCreditCardDto.getId());
            assertEquals(actualData.getLast4(), stripeCreditCardDto.getLast4());
        }

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), stripeCreditCardDto.getId());
        assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth(), stripeCreditCardDto.getExpmonth());
        assertEquals(actualData.getExpDateYear(), stripeCreditCardDto.getExpyear());
        assertEquals(actualData.getLastFour(), stripeCreditCardDto.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
        assertEquals(actualData.getDefault(), stripeCreditCardDto.isDefaultCard());
    }

    @Test
    public void test_getAllCardsForCustomer1_success_with_stripe() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        Pageable pageable = PageRequest.of(0, limit);

        stripeCreditCardDto.setCardType(cardType);
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setExpmonth(9);
        stripeCreditCardDto.setExpyear(19);
        stripeCreditCardDto.setId(id);
        stripeCreditCardDto.setLast4(lastFour);

        List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();
        stripeCreditCardDtoList.add(stripeCreditCardDto);

        //mock


        //Execution
        List<StripeCreditCardDto> allCardsForCustomerData = allPaymentServiceImpl.getAllCardsForCustomer(user, event, limit, stripe);

        for (StripeCreditCardDto actualData : allCardsForCustomerData) {

            assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
            assertEquals(actualData.isDefaultCard(), stripeCreditCardDto.isDefaultCard());
            assertEquals(actualData.getExpmonth(), stripeCreditCardDto.getExpmonth());
            assertEquals(actualData.getExpyear(), stripeCreditCardDto.getExpyear());
            assertEquals(actualData.getId(), stripeCreditCardDto.getId());
            assertEquals(actualData.getLast4(), stripeCreditCardDto.getLast4());
        }
    }

    @Test
    public void test_getAllCardsForCustomer1_success_with_square() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        Pageable pageable = PageRequest.of(0, limit);

        stripeCreditCardDto.setCardType(cardType);
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setExpmonth(9);
        stripeCreditCardDto.setExpyear(19);
        stripeCreditCardDto.setId(id);
        stripeCreditCardDto.setLast4(lastFour);

        List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();
        stripeCreditCardDtoList.add(stripeCreditCardDto);

        //mock


        //Execution
        List<StripeCreditCardDto> allCardsForCustomerData = allPaymentServiceImpl.getAllCardsForCustomer(user, event, limit, stripe);

        for (StripeCreditCardDto actualData : allCardsForCustomerData) {

            assertEquals(actualData.getCardType(), stripeCreditCardDto.getCardType());
            assertEquals(actualData.isDefaultCard(), stripeCreditCardDto.isDefaultCard());
            assertEquals(actualData.getExpmonth(), stripeCreditCardDto.getExpmonth());
            assertEquals(actualData.getExpyear(), stripeCreditCardDto.getExpyear());
            assertEquals(actualData.getId(), stripeCreditCardDto.getId());
            assertEquals(actualData.getLast4(), stripeCreditCardDto.getLast4());
        }
    }

    /*@Test
    public void test_addCardToCustomer1_success_with_stripe() throws StripeException, ApiException {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setId(cardId);
        card.setLast4("1111");
        card.setBrand("brand");
        card.setExpMonth(9l);
        card.setExpYear(19l);

        //mock
        when(stripePaymentService.cardToCustomer(stripe.getAccessToken(), customerId, tokenOrCardNonce, true, true)).thenReturn(card);

        //Execution
        String cardIdForCustomer = allPaymentServiceImpl.addCardToCustomer(customerId, tokenOrCardNonce, true, true, stripe, event, user);

        assertEquals(cardIdForCustomer, card.getId());

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card.getId());
        assertEquals(actualData.getCardType(), card.getBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(Long.valueOf(actualData.getExpDateMonth()), card.getExpMonth());
        assertEquals(Long.valueOf(actualData.getExpDateYear()), card.getExpYear());
        assertEquals(actualData.getLastFour(), card.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.STRIPE.value());
        assertTrue(actualData.getDefault());
    }*/

    @Test
    public void test_addCardToCustomer1_success_with_square() throws StripeException, ApiException {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card = new com.squareup.square.models.Card(id, "OTHER_BRAND", "1111", 9l, 19L, null, null, null, null, null, null,true, "CREDIT", "PREPAID", null, null, null);

        //mock
        when(squarePaymentService.addCardToCustomer(stripe.getAccessToken(), customerId, tokenOrCardNonce, true)).thenReturn(card);

        //Execution
        String cardIdForCustomer = allPaymentServiceImpl.addCardToCustomer(customerId, tokenOrCardNonce, stripe, event, user, false, null);

        assertEquals(cardIdForCustomer, card.getId());

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card.getId());
        assertEquals(actualData.getCardType(), card.getCardBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth().longValue(), card.getExpMonth().longValue());
        assertEquals(actualData.getExpDateYear().longValue(), card.getExpYear().longValue());
        assertEquals(actualData.getLastFour(), card.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
        assertTrue(actualData.getDefault());
    }

    /*@Test
    public void test_retrieveDefaultCardOfCustomer_success_with_stripe() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setId(cardId);
        card.setLast4("1111");
        card.setBrand("brand");

        cardInfoDto = new CardInfoDto(card.getId(), card.getLast4(), card.getBrand());

        //mock
        when(stripePaymentService.retrieveDefaultPaymentIdOfCustomer(stripe.getAccessToken(), customerId)).thenReturn(card);

        //Execution
        CardInfoDto retrieveDefaultCardOfCustomerData = allPaymentServiceImpl.retrieveDefaultPaymentIdOfCustomer(customerId, stripe, null);

        assertEquals(retrieveDefaultCardOfCustomerData.getId(), cardInfoDto.getId());
        assertEquals(retrieveDefaultCardOfCustomerData.getLast4(), cardInfoDto.getLast4());
        assertEquals(retrieveDefaultCardOfCustomerData.getBrand(), cardInfoDto.getBrand());
    }*/

    /*@Test
    public void test_retrieveDefaultCardOfCustomer_success_with_stripe_card_null() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setId(cardId);
        card.setLast4("1111");
        card.setBrand("brand");

        cardInfoDto = new CardInfoDto(card.getId(), card.getLast4(), card.getBrand());

        //mock
        when(stripePaymentService.retrieveDefaultPaymentIdOfCustomer(stripe.getAccessToken(), customerId)).thenReturn(null);

        //Execution
        CardInfoDto retrieveDefaultCardOfCustomerData = allPaymentServiceImpl.retrieveDefaultPaymentIdOfCustomer(customerId, stripe, null);

        assertNull(retrieveDefaultCardOfCustomerData);
    }*/

//    @Test
    public void test_retrieveDefaultCardOfCustomer_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card = new com.squareup.square.models.Card.Builder().build().toBuilder().id(id).cardBrand("OTHER_BRAND").last4("1111").expMonth(9l).expYear(19l).build();

        cardInfoDto = new CardInfoDto(card.getId(), card.getLast4(), card.getCardBrand().toString());

        //mock
        when(squarePaymentService.retrieveDefaultCardOfCustomer(stripe.getAccessToken(), customerId, cardId)).thenReturn(card);

        //Execution
        CardInfoDto retrieveDefaultCardOfCustomerData = allPaymentServiceImpl.retrieveDefaultCardOfCustomer(customerId, stripe, null);

        assertEquals(retrieveDefaultCardOfCustomerData.getId(), cardInfoDto.getId());
        assertEquals(retrieveDefaultCardOfCustomerData.getLast4(), cardInfoDto.getLast4());
        assertEquals(retrieveDefaultCardOfCustomerData.getBrand(), cardInfoDto.getBrand());
    }

    @Test
    public void test_retrieveDefaultCardOfCustomer_success_with_square_card_null() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card = new com.squareup.square.models.Card.Builder().build().toBuilder().id(id).cardBrand("OTHER_BRAND").last4("1111").expMonth(9l).expYear(19l).build();

        cardInfoDto = new CardInfoDto(card.getId(), card.getLast4(), card.getCardBrand().toString());

        //mock


        //Execution
        CardInfoDto retrieveDefaultCardOfCustomerData = allPaymentServiceImpl.retrieveDefaultCardOfCustomer(customerId, stripe, null);

        assertNull(retrieveDefaultCardOfCustomerData);
    }

    @Test
    public void test_createRefund_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
        stripe.setId(1l);
        stripe.setEvent(event);

        money = new com.squareup.square.models.Money(10L, "USD");

        Map<String, String> metadata = new HashMap<>();

        PaymentRefund paymentRefund = new PaymentRefund(id, money, "APPROVED", null, null, null, null, null, null, null, null, null, null, null, null);

        Long refundedApplicationFee = paymentRefund.getAmountMoney().getAmount();

        refundInfoDto = new RefundInfoDto(paymentRefund.getId(), paymentRefund.getStatus(), paymentRefund.getAmountMoney().getAmount(), refundedApplicationFee);

        //mock
        String reason = "reason";
        when(squarePaymentService.createRefund(stripe.getAccessToken(), stripeTransaction, currency, this.amount, metadata, reason, true)).thenReturn(paymentRefund);

        //Execution
        RefundInfoDto refundData = allPaymentServiceImpl.createRefund(stripeTransaction, stripe, this.amount, metadata, reason, true, currency);

        assertEquals(refundData.getId(), refundInfoDto.getId());
        assertEquals(refundData.getStatus(), refundInfoDto.getStatus());
        assertEquals(refundData.getRefundedAmount(), refundInfoDto.getRefundedAmount());
        assertEquals(refundData.getRefundedApplicationFee(), refundInfoDto.getRefundedApplicationFee());
    }

    @Test
    public void test_updateDefaultCard_success_with_stripe() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        //mock
        Mockito.doNothing().when(stripePaymentService).updateDefaultCard(apiKey, customerId, cardId);

        //Execution
        allPaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId, stripe);
    }

    @Test
    public void test_updateDefaultCard_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        //mock
        Mockito.doNothing().when(squarePaymentService).updateDefaultCard(apiKey, customerId, cardId);

        //Execution
        allPaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId, stripe);
    }

    @Test
    public void test_updateDefaultCard1_success_with_stripe() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        //mock
        Mockito.doNothing().when(stripePaymentService).updateDefaultCard(apiKey, customerId, cardId);
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(cardId, user, event)).thenReturn(stripeCustomers);
        Mockito.doNothing().when(stripeCustomerService).updateDefaultCardByCustomerId(user, event);

        //Execution
        allPaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId, stripe, event, user);

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertTrue(actualData.getDefault());
    }

    @Test
    public void test_updateDefaultCard1_success_with_square() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        //mock
        Mockito.doNothing().when(squarePaymentService).updateDefaultCard(apiKey, customerId, cardId);
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(cardId, user, event)).thenReturn(stripeCustomers);
        Mockito.doNothing().when(stripeCustomerService).updateDefaultCardByCustomerId(user, event);

        //Execution
        allPaymentServiceImpl.updateDefaultCard(apiKey, customerId, cardId, stripe, event, user);

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertTrue(actualData.getDefault());
    }

    @Test
    public void test_updateDefaultCard_success_stripeCustomer_null() throws StripeException, ApiException{

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        //mock
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(cardId, user, event)).thenReturn(null);

        //Execution
        allPaymentServiceImpl.updateDefaultCard(cardId, event, user);
    }

//    @Test
    public void test_saveStripeCustomer_success() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setId(cardId);
        card.setLast4("1111");
        card.setBrand("brand");
        card.setExpMonth(9l);
        card.setExpYear(19l);

        //mock
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(card.getId(), user, event)).thenReturn(null);

        //Execution
//        allPaymentServiceImpl.saveStripeCustomer(customerId, card, user, event, true);

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card.getId());
        assertEquals(actualData.getCardType(), card.getBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(Long.valueOf(actualData.getExpDateMonth()), card.getExpMonth());
        assertEquals(Long.valueOf(actualData.getExpDateYear()), card.getExpYear());
        assertEquals(actualData.getLastFour(), card.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.STRIPE.value());
        assertTrue(actualData.getDefault());
    }

//    @Test
    public void test_saveStripeCustomer_success_with_stripeCustomer() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        card.setId(cardId);

        //mock
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(card.getId(), user, event)).thenReturn(stripeCustomers);

        //Execution
//        allPaymentServiceImpl.saveStripeCustomer(customerId, card, user, event, true);
    }


    @Test
    public void test_saveSquareCustomer_success() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card1 = new com.squareup.square.models.Card.Builder().build().toBuilder().id(id).cardBrand("OTHER_BRAND").last4("1111").expMonth(9l).expYear(19l).build();

        //mock


        //Execution
        allPaymentServiceImpl.saveSquareCustomer(customerId, card1, user, event,true);

        ArgumentCaptor<StripeCustomers> stripeCustomersArgumentCaptor = ArgumentCaptor.forClass(StripeCustomers.class);
        verify(stripeCustomerService, times(1)).save(stripeCustomersArgumentCaptor.capture());

        StripeCustomers actualData = stripeCustomersArgumentCaptor.getValue();
        assertEquals(actualData.getEvent(), event);
        assertEquals(actualData.getUser(), user);
        assertEquals(actualData.getStripecustomerid(), customerId);
        assertEquals(actualData.getStripecardid(), card1.getId());
        assertEquals(actualData.getCardType(), card1.getCardBrand());
        assertEquals(actualData.getCreatedDate().toString(), new Date().toString());
        assertEquals(actualData.getExpDateMonth().longValue(), card1.getExpMonth().longValue());
        assertEquals(actualData.getExpDateYear().longValue(), card1.getExpYear().longValue());
        assertEquals(actualData.getLastFour(), card1.getLast4());
        assertEquals(actualData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
        assertTrue(actualData.getDefault());
    }

    @Test
    public void test_saveSquareCustomer_success_with_stripeCustomer() {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

        com.squareup.square.models.Card card1 = new com.squareup.square.models.Card.Builder().build().toBuilder().id(id).cardBrand("OTHER_BRAND").last4("1111").expMonth(9l).expYear(19l).build();

        //mock
        when(stripeCustomerService.getByStripecardidAndUserAndEvent(any(), any(), any())).thenReturn(stripeCustomers);

        //Execution
        allPaymentServiceImpl.saveSquareCustomer(customerId, card1, user, event,true);
    }
}