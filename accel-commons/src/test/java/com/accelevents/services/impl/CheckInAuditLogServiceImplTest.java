package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.EventTickets;
import com.accelevents.domain.Staff;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.domain.virtual.CheckInAuditLog;
import com.accelevents.enums.StaffRole;
import com.accelevents.messages.CheckInSource;
import com.accelevents.messages.CheckInUserTracking;
import com.accelevents.services.CheckInAuditLogRepoService;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;


@ExtendWith(MockitoExtension.class)
public class CheckInAuditLogServiceImplTest {

    @Spy
    @InjectMocks
    private CheckInAuditLogServiceImpl checkInAuditLogServiceImpl = new CheckInAuditLogServiceImpl();

    @Mock
    private CheckInAuditLogRepoService checkInAuditLogRepoService;

    private Event event;
    private EventTickets eventTickets;
    private Staff staff;
    private TicketingType ticketingTypeId;
    private CheckInAuditLog checkInAuditLog;
    private Long userId;

    private Date timeStamp = DateUtils.getCurrentDate();

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        eventTickets = EventDataUtil.getEventTickets();
        staff = new Staff();
        staff.setId(1L);
        staff.setEvent(event);
        staff.setRole(StaffRole.staff);
        checkInAuditLog = new CheckInAuditLog();
        ticketingTypeId = new TicketingType();
        ticketingTypeId.setId(1L);
    }

    @Test
    void test_save() {
        //setup
        String device = "MAC_OS_X_IPHONE-MOBILE_SAFARI from Mobile";
        String ticketStatus = TicketStatus.REGISTERED.name();
        setCheckInAuditLogObject(device, ticketStatus);

        //Execution
        checkInAuditLogServiceImpl.save(checkInAuditLog);

        //Assertion
        ArgumentCaptor<CheckInAuditLog> checkInAuditLogArgumentCaptor = ArgumentCaptor.forClass(CheckInAuditLog.class);
        Mockito.verify(checkInAuditLogRepoService).save(checkInAuditLogArgumentCaptor.capture());

        CheckInAuditLog checkInAuditLogData = checkInAuditLogArgumentCaptor.getValue();
        assertCheckInAuditLog(device, ticketStatus, checkInAuditLogData);
    }

    @Test
    void test_setCheckInAuditLogObjectAndSaveInDB() {
        String device = "BlackBerry from Mobile  259";
        String ticketStatus = TicketStatus.CHECKED_IN.name();
        String source = "KIOSK";
        String sourceDescription = "{\"browser\":\"Chrome\",\"browserVersion\":\"*********\",\"os\":\"Linux\",\"osVersion\":\"Unknown\",\"deviceType\":\"Desktop\",\"userAgent\":\"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"platform\":\"Linux x86_64\"}";
        setCheckInAuditLogObject(device, ticketStatus);

        //Execution
        checkInAuditLogServiceImpl.setCheckInAuditLogObjectAndSaveInDB(event, staff, ticketingTypeId, timeStamp, ticketStatus, device, CheckInSource.IN_PERSON, eventTickets.getId(), userId, CheckInUserTracking.LIVE_EVENT_CHECKIN,false,source,sourceDescription);

        //Assertion
        ArgumentCaptor<CheckInAuditLog> checkInAuditLogArgumentCaptor = ArgumentCaptor.forClass(CheckInAuditLog.class);
        Mockito.verify(checkInAuditLogRepoService).save(checkInAuditLogArgumentCaptor.capture());

        CheckInAuditLog checkInAuditLogData = checkInAuditLogArgumentCaptor.getValue();
        assertCheckInAuditLog(device, ticketStatus, checkInAuditLogData);
    }

    @Test
    void test_getByEventIdAndTicketingTypeId() {
        String device = "BlackBerry from Mobile  259";
        String ticketStatus = TicketStatus.CHECKED_IN.name();
        setCheckInAuditLogObject(device, ticketStatus);

        //Execution
        List<CheckInAuditLog> checkInAuditLogList = checkInAuditLogServiceImpl.getByEventIdAndTicketingTypeId(event, ticketingTypeId);
        for (CheckInAuditLog checkInAuditLog :
                checkInAuditLogList) {
            assertCheckInAuditLog(device, ticketStatus, checkInAuditLog);
        }
    }


    private void assertCheckInAuditLog(String device, String ticketStatus, CheckInAuditLog checkInAuditLogData) {
        assertEquals(checkInAuditLogData.getDevice(), device);
        assertEquals(checkInAuditLogData.getEventid().getEventId(), event.getEventId());
        assertEquals(checkInAuditLogData.getStaffId().longValue(), staff.getId());
        assertEquals(checkInAuditLogData.getTicketingTypeId().getId(), ticketingTypeId.getId());
        assertEquals(checkInAuditLogData.getTicketStatus(), ticketStatus);
        assertEquals(checkInAuditLogData.getAuditTime().toString(), timeStamp.toString());
    }

    private void setCheckInAuditLogObject(String device, String ticketStatus) {
        checkInAuditLog.setDevice(device);
        checkInAuditLog.setEventid(event);
        checkInAuditLog.setStaffId(staff.getId());
        checkInAuditLog.setTicketingTypeId(ticketingTypeId);
        checkInAuditLog.setTicketStatus(ticketStatus);
        checkInAuditLog.setAuditTime(timeStamp);
    }
}