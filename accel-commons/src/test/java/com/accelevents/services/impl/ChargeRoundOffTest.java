package com.accelevents.services.impl;


import java.math.BigDecimal;

public class ChargeRoundOffTest {

    public static void main(String[] args) throws Exception {

        double wlFlat = 0.99;
        double wlPercentage = 0.5;
        double aeFlatFee = 1;
        double aePercentage = 0.02;
        double stripeFixed = 0.3;
        double stripePercentage = 2.9;
//        double wlFlat = 0.99;
//        double aeFlatFee = 1;
//        double stripeFixed = 0.3;
//        double stripePercentage = 2.9;

//        for ( int j = 100 ; j < 10000 ; j ++ ) {
//            double wlPercentage = j/100;
//            double aePercentage = 1.25;

            for (int i = 5; i <= 60000; i++) {
                double ticketPrice = i;
                double wlPercentageFee = (wlPercentage * ticketPrice) / 100;
                double aePercentageFee = (aePercentage * ticketPrice) / 100;
                double ticketPriceWithFee = ticketPrice + wlFlat + wlPercentageFee + aeFlatFee + aePercentageFee;
                double ccFee = ((ticketPriceWithFee * stripePercentage) / 100) + stripeFixed;
                double totalFee = ticketPriceWithFee + ccFee;

                double totalFeeR = BigDecimal.valueOf(totalFee).setScale(2, BigDecimal.ROUND_HALF_EVEN).doubleValue();

                if (!BigDecimal.valueOf(totalFee * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN).equals(BigDecimal.valueOf(totalFeeR * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN))) {
                    System.out.println(BigDecimal.valueOf(totalFee * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN));
                    System.out.println(BigDecimal.valueOf(totalFeeR * 100).setScale(0, BigDecimal.ROUND_HALF_EVEN));
                    System.out.println(i);
                    System.out.println(wlPercentage);
                    System.out.println("Fail");
                    throw new Exception("Fail");
                }
            }
        //}

        System.out.println("Success");

    }
}
