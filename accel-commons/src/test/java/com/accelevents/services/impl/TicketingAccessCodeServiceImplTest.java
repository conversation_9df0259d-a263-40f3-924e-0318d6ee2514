package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.AccessCodeDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AccountActivatedTriggerStatus;
import com.accelevents.domain.enums.Currency;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.RecurringEventsRepository;
import com.accelevents.repositories.TicketingAccessCodeRepository;
import com.accelevents.services.RecurringEventsMainScheduleService;
import com.accelevents.services.TicketingHelperService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.services.TicketingTypeTicketService;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.cloudinary.utils.StringUtils;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.io.IOException;
import java.util.*;

import static com.accelevents.utils.Constants.DATE_FORMAT_MONTH;
import static com.accelevents.utils.TimeZoneUtil.getDateInUTC;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingAccessCodeServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingAccessCodeServiceImpl ticketingAccessCodeServiceImpl = new TicketingAccessCodeServiceImpl();
    @Mock
    private TicketingAccessCodeRepository accessCodeRepository;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private TicketingAccessCode ticketingAccessCode;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private RecurringEventsRepository recurringEventsRepository;
    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;

    private TicketingType ticketingType;
    private Event event;
    private AccessCodeDto accessCodeDto;
    private Ticketing ticketing;

	private long accessCodeId1 = 2L;
	private long ticketTypeId = 1L;
    private String startDate = "01/01/2019 00:00:00";
    private String endDate = "04/04/2019 00:00:00";
    private String eventTicketTypeId = "1";
	private String accessCode = "accessCode";
    private Date startDate1 = new Date(startDate);
    private Date endDate1 = new Date(endDate);
    private Long recurringEventId = 1L;

    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);
		String eventName = "TestEvent";
		event = new Event(eventName, true, true, true, true, AccountActivatedTriggerStatus.INITIAL);
		Long eventId = 1L;
		event.setEventId(eventId);
        event.setCurrency(Currency.AUD);
		String eventUrl = "asd";
		event.setEventURL(eventUrl);

        accessCodeDto = new AccessCodeDto();
        accessCodeDto.setCode(accessCode);
		long accessCodeId = 1L;
		accessCodeDto.setId(accessCodeId);
        accessCodeDto.setEventTicketTypeId(eventTicketTypeId);
        accessCodeDto.setEndDate(endDate);
        accessCodeDto.setStartDate(startDate);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setCode(accessCodeDto.getCode());
        ticketingAccessCode.setId(accessCodeDto.getId());
        ticketingAccessCode.setEventId(event);
        ticketingAccessCode.setEventTicketTypeId(accessCodeDto.getEventTicketTypeId());
        ticketingAccessCode.setEndDate(endDate1);
        ticketingAccessCode.setStartDate(startDate1);
    }
    @Test
    void test_updateAccessCode_success_ticketingAccessCode_notPresent(){

        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        TicketingAccessCode ticketingAccessCode1 = new TicketingAccessCode();
        ticketingAccessCode1.setId(accessCodeDto.getId());

        Ticketing ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);
        recurringEventId = 1L;
        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(accessCodeRepository.findById(accessCodeDto.getId())).thenReturn(Optional.of(ticketingAccessCode));

        when(ticketingTypeService.findByidInAndEvent(accessCodeTicketTypeList,event)).thenReturn(ticketingTypeList);
        doReturn(Optional.empty()).when(ticketingAccessCodeServiceImpl).getByCode(accessCodeDto.getCode(), event, recurringEventId);

        //Execution
        ticketingAccessCodeServiceImpl.updateAccessCode(accessCodeDto, event, recurringEventId);

        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodes = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(accessCodeRepository, times(1)).save(ticketingAccessCodes.capture());
        TicketingAccessCode actual = ticketingAccessCodes.getValue();
        assertEquals(accessCode, actual.getCode());
    }

    @Test
    void test_FindByEvent_success(){

        //setup
        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<>();
        ticketingAccessCodes.add(ticketingAccessCode);

        //mock
        when(accessCodeRepository.findByEventId(event)).thenReturn(ticketingAccessCodes);

        //Execution
        ticketingAccessCodeServiceImpl.findByEvent(event);
    }

    @Test
    void test_getByCode_success(){

        //mock


        //Execution
        ticketingAccessCodeServiceImpl.getByCode(accessCodeDto.getCode(),event, recurringEventId);
    }

    @Test
    void test_removeTicketTypeFromAccessCode_success(){

        //setup
        TicketingAccessCode ticketingAccessCode1 = new TicketingAccessCode();
        ticketingAccessCode1.setEventTicketTypeId(eventTicketTypeId);

        List<String> accessCodes = new ArrayList<>();
        accessCodes.add(ticketingAccessCode.getCode());

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCodeList.add(ticketingAccessCode1);

        //mock
        when(accessCodeRepository.findAllByCodeIn(accessCodes)).thenReturn(ticketingAccessCodeList);

        //Execution
        ticketingAccessCodeServiceImpl.removeTicketTypeFromAccessCode(accessCodes, ticketingAccessCode.getId());

        Class<ArrayList<TicketingAccessCode>> listClass = (Class<ArrayList<TicketingAccessCode>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingAccessCode>> argument = ArgumentCaptor.forClass(listClass);
        verify(accessCodeRepository, times(1)).saveAll(argument.capture());

        List<TicketingAccessCode> actualData = argument.getValue();
        for (TicketingAccessCode actual : actualData) {
            assertNotEquals(eventTicketTypeId,actual.getEventTicketTypeId());
        }
    }
    @Test
    void test_deleteAccessCode_withRecurringEventTrue_RecurringEventIdIsZero(){
        //setup
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);
        recurringEventId=0L;

        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(accessCodeRepository.findById(accessCodeDto.getId())).thenReturn(Optional.of(ticketingAccessCode));
        when(accessCodeRepository.findByCreatedFromAndEventId(anyLong(),any())).thenReturn(Collections.singletonList(ticketingAccessCode));

        //Execution
        NotFoundException exception = null;
        try{
            ticketingAccessCodeServiceImpl.deleteAccessCode(accessCodeDto.getId(), event, recurringEventId);
        } catch (NotFoundException e){
            exception = e;
        }

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findById(anyLong());
        verify(accessCodeRepository).findByCreatedFromAndEventId(anyLong(),any());
        verify(ticketingAccessCodeServiceImpl,Mockito.times(2)).markTicketAsHiddenFalseIfAlreadyHidden(anyList(), any());
    }
    @Test
    void test_deleteAccessCode_withRecurringEventFalse_RecurringEventIdIsZero(){
        //setup
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);
        recurringEventId=0L;

        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(accessCodeRepository.findById(accessCodeDto.getId())).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        NotFoundException exception = null;
        try{
            ticketingAccessCodeServiceImpl.deleteAccessCode(accessCodeDto.getId(), event, recurringEventId);
        } catch (NotFoundException e){
            exception = e;
        }

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findById(anyLong());
    }
    @Test
    void test_deleteAccessCode_withRecurringEventTrue_RecurringEventIdIsNotZero(){
        //setup
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(accessCodeRepository.findById(accessCodeDto.getId())).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        NotFoundException exception = null;
        try{
            ticketingAccessCodeServiceImpl.deleteAccessCode(accessCodeDto.getId(), event, recurringEventId);
        } catch (NotFoundException e){
            exception = e;
        }

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findById(anyLong());
    }
    @Test
    void test_deleteAccessCode_throwExceptionAccessCodeNotFound(){
        //setup
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingAccessCodeServiceImpl.deleteAccessCode(1L, event, recurringEventId));

        assertEquals(NotFoundException.NotFound.ACCESS_CODE_NOT_FOUND.getErrorMessage(), exception.getMessage());

        //Assert
        verify(accessCodeRepository).findById(anyLong());
    }

    static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{null},
                new Object[]{0L},
        };
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_getAllAccessCodes_withRecurringEventTrue_RecurringEventIdIsNullOrEmpty(Long recurringEventId){
        //setup
        String startdate = DateUtils.formatDate(ticketingAccessCode.getStartDate(),DATE_FORMAT_MONTH);
        String enddate = DateUtils.formatDate(ticketingAccessCode.getEndDate(),DATE_FORMAT_MONTH);

        ticketingAccessCode.setRecurringRelativeEndTime(1);
        ticketingAccessCode.setRecurringRelativeStartTime(1);
        accessCodeDto.setRecurringRelativeEndTime(1);
        accessCodeDto.setRecurringRelativeStartTime(1);

        List<AccessCodeDto> accessCodeDtoList = new ArrayList<>();
        accessCodeDtoList.add(accessCodeDto);

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCodeList.add(ticketingAccessCode);
        Page<TicketingAccessCode> ticketingAccessCodes= new PageImpl<>(ticketingAccessCodeList);

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findAllByEventIdAndRecurringEventIdisNull(any(),anyString(),any())).thenReturn(ticketingAccessCodes);

        //Execution
        ticketingAccessCodeServiceImpl.getAllAccessCodes(event, recurringEventId,StringUtils.EMPTY,0,10);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findAllByEventIdAndRecurringEventIdisNull(any(),anyString(),any());

        assertEquals(accessCodeDtoList.get(0).getCode(),ticketingAccessCodeList.get(0).getCode());
        assertEquals(accessCodeDtoList.get(0).getStartDate(),startdate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeStartTime(),ticketingAccessCodeList.get(0).getRecurringRelativeStartTime());
        assertEquals(accessCodeDtoList.get(0).getEndDate(),enddate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeEndTime(),ticketingAccessCodeList.get(0).getRecurringRelativeEndTime());
        assertEquals(accessCodeDtoList.get(0).getEventTicketTypeId(),ticketingAccessCodeList.get(0).getEventTicketTypeId());
        assertEquals((long) accessCodeDtoList.get(0).getId(), ticketingAccessCodeList.get(0).getId());
    }
    @Test
    void test_getAllAccessCodes_withRecurringEventTrue_withRecurringEventId(){
        //setup
        String startdate = DateUtils.formatDate(ticketingAccessCode.getStartDate(),DATE_FORMAT_MONTH);
        String enddate = DateUtils.formatDate(ticketingAccessCode.getEndDate(),DATE_FORMAT_MONTH);

        ticketingAccessCode.setRecurringRelativeEndTime(1);
        ticketingAccessCode.setRecurringRelativeStartTime(1);
        accessCodeDto.setRecurringRelativeEndTime(1);
        accessCodeDto.setRecurringRelativeStartTime(1);

        List<AccessCodeDto> accessCodeDtoList = new ArrayList<>();
        accessCodeDtoList.add(accessCodeDto);

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCodeList.add(ticketingAccessCode);
        Page<TicketingAccessCode> ticketingAccessCodes= new PageImpl<>(ticketingAccessCodeList);

                ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findByEventIdAndRecurringEventId(any(),anyLong(),anyString(),any())).thenReturn(ticketingAccessCodes);

        //Execution
        ticketingAccessCodeServiceImpl.getAllAccessCodes(event, recurringEventId,StringUtils.EMPTY,0,10);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findByEventIdAndRecurringEventId(any(),anyLong(),anyString(),any());

        assertEquals(accessCodeDtoList.get(0).getCode(),ticketingAccessCodeList.get(0).getCode());
        assertEquals(accessCodeDtoList.get(0).getStartDate(),startdate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeStartTime(),ticketingAccessCodeList.get(0).getRecurringRelativeStartTime());
        assertEquals(accessCodeDtoList.get(0).getEndDate(),enddate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeEndTime(),ticketingAccessCodeList.get(0).getRecurringRelativeEndTime());
        assertEquals(accessCodeDtoList.get(0).getEventTicketTypeId(),ticketingAccessCodeList.get(0).getEventTicketTypeId());
        assertEquals((long) accessCodeDtoList.get(0).getId(), ticketingAccessCodeList.get(0).getId());
    }
    @Test
    void test_getAllAccessCodes_withRecurringEventFalse(){

        //setup
        String startdate = DateUtils.formatDate(ticketingAccessCode.getStartDate(),DATE_FORMAT_MONTH);
        String enddate = DateUtils.formatDate(ticketingAccessCode.getEndDate(),DATE_FORMAT_MONTH);

        ticketingAccessCode.setRecurringRelativeEndTime(1);
        ticketingAccessCode.setRecurringRelativeStartTime(1);
        accessCodeDto.setRecurringRelativeEndTime(1);
        accessCodeDto.setRecurringRelativeStartTime(1);
        recurringEventId=0L;

        List<AccessCodeDto> accessCodeDtoList = new ArrayList<>();
        accessCodeDtoList.add(accessCodeDto);

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCodeList.add(ticketingAccessCode);
        Page<TicketingAccessCode> ticketingAccessCodes = new PageImpl<>(ticketingAccessCodeList);

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findByEventId(any(),any(),any())).thenReturn(ticketingAccessCodes);

        //Execution
        ticketingAccessCodeServiceImpl.getAllAccessCodes(event, recurringEventId, StringUtils.EMPTY,0,10);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findByEventId(any(),any(),any());

        assertEquals(accessCodeDtoList.get(0).getCode(),ticketingAccessCodeList.get(0).getCode());
        assertEquals(accessCodeDtoList.get(0).getStartDate(),startdate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeStartTime(),ticketingAccessCodeList.get(0).getRecurringRelativeStartTime());
        assertEquals(accessCodeDtoList.get(0).getEndDate(),enddate);
        assertEquals(accessCodeDtoList.get(0).getRecurringRelativeEndTime(),ticketingAccessCodeList.get(0).getRecurringRelativeEndTime());
        assertEquals(accessCodeDtoList.get(0).getEventTicketTypeId(),ticketingAccessCodeList.get(0).getEventTicketTypeId());
        assertEquals((long) accessCodeDtoList.get(0).getId(), ticketingAccessCodeList.get(0).getId());
    }
    @Test
    void test_markTicketAsHiddenIfAlreadyNotHidden_TicketingTypeHidden_True() {
        //setup
        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);
        TicketingType ticketingTypes = new TicketingType();
        ticketingTypes.setHidden(false);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingTypes);

        //mock
        when(ticketingTypeService.findByidInAndEvent(accessCodeTicketTypeList,event)).thenReturn(ticketingTypeList);

        //Execution
        ticketingAccessCodeServiceImpl.markTicketAsHiddenIfAlreadyNotHidden(accessCodeDto,event);

        ArgumentCaptor<TicketingType> ticketingTypesArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypesArgumentCaptor.capture());

        TicketingType actual = ticketingTypesArgumentCaptor.getValue();
        assertEquals(true,actual.isHidden());
    }

    @Test
    void test_markTicketAsHiddenIfAlreadyNotHidden_TicketingTypeHidden_false() {

        //setup
        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);
        TicketingType ticketingTypes = new TicketingType();
        ticketingTypes.setHidden(true);
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingTypes);

        //mock
        when(ticketingTypeService.findByidInAndEvent(accessCodeTicketTypeList,event)).thenReturn(ticketingTypeList);

        //Execution
        ticketingAccessCodeServiceImpl.markTicketAsHiddenIfAlreadyNotHidden(accessCodeDto,event);

        assertEquals(true,ticketingTypes.isHidden());
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_createAccessCode_recurringEventTrue_recurringEventIdNullAndZero_recurringEventNotPresent(Long recurringEventId) {
        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        RecurringEvents recurringEvents = new RecurringEvents();

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        TicketingAccessCode ticketingAccessCode = new TicketingAccessCode();
        accessCodeDto.setId(0L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(ticketingAccessCodeServiceImpl).markTicketAsHiddenIfAlreadyNotHidden(any(),any());
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),nullable(Long.class));
        when(recurringEventsRepository.findById(nullable(Long.class))).thenReturn(Optional.empty());
        when(recurringEventsRepository.findByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(Collections.singletonList(recurringEvents));
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateAccessCode(anyList(),anyList(), anyBoolean(), anyBoolean(), nullable(Long.class) );

        //Execution
        ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto,event, recurringEventId);

        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodes = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(accessCodeRepository, times(1)).save(ticketingAccessCodes.capture());

        TicketingAccessCode actual = ticketingAccessCodes.getValue();
        assertEquals(accessCodeDto.getCode(),actual.getCode());
        assertEquals(accessCodeDto.getEventTicketTypeId(),actual.getEventTicketTypeId());

    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_createAccessCode_recurringEventFalse_recurringEventIdNullAndZero(Long recurringEventId) {
        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        RecurringEvents recurringEvents = new RecurringEvents();

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        ticketingAccessCode.setEventId(event);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(ticketingAccessCodeServiceImpl).markTicketAsHiddenIfAlreadyNotHidden(any(),any());
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),nullable(Long.class));

        //Execution
        ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto,event, recurringEventId);

        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodes = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(accessCodeRepository, times(1)).save(ticketingAccessCodes.capture());

        TicketingAccessCode actual = ticketingAccessCodes.getValue();
        assertEquals(accessCodeDto.getCode(),actual.getCode());
        assertEquals(accessCodeDto.getEventTicketTypeId(),actual.getEventTicketTypeId());

    }
    @Test
    void test_createAccessCode_recurringEventFalse_recurringEventIsNotZero() {
        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        RecurringEvents recurringEvents = new RecurringEvents();

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        ticketingAccessCode.setEventId(event);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(ticketingAccessCodeServiceImpl).markTicketAsHiddenIfAlreadyNotHidden(any(),any());
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),anyLong());

        //Execution
        ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto,event, recurringEventId);
    }
    @Test
    void test_createAccessCode_recurringEventTrue_recurringEventIdIsNotZero_recuringEventNotPresent() {
        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        RecurringEvents recurringEvents = new RecurringEvents();

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        ticketingAccessCode.setEventId(event);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(ticketingAccessCodeServiceImpl).markTicketAsHiddenIfAlreadyNotHidden(any(),any());
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),anyLong());
        when(recurringEventsRepository.findById(anyLong())).thenReturn(Optional.empty());
        when(recurringEventsRepository.findByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(Collections.singletonList(recurringEvents));
        doNothing().when(recurringEventsMainScheduleService).callingExecutorServiceForCreateAccessCode(anyList(),anyList(), anyBoolean(), anyBoolean(), nullable(Long.class));

        //Execution
        ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto,event, recurringEventId);

    }
    @Test
    void test_createAccessCode_recurringEventTrue_recurringEventIdIsNotZero_recuringEventPresent() {
        //setup
        ticketingType = new TicketingType();
        ticketingType.setHidden(false);

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        RecurringEvents recurringEvents = new RecurringEvents();

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        ticketingAccessCode.setEventId(event);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(ticketingAccessCodeServiceImpl).markTicketAsHiddenIfAlreadyNotHidden(any(),any());
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),anyLong());



        //Execution
        ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto,event, recurringEventId);
    }
    @Test
    void test_createAccessCode_throwExceptionCodeAlreadyExist() throws IOException {

        //setup
        Ticketing ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        ticketingAccessCode.setId(2L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findByCodeAndEventIdAndRecurringEventId(anyString(), any(),anyLong())).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> ticketingAccessCodeServiceImpl.createAccessCode(accessCodeDto, event, recurringEventId));

        //assert
        assertEquals(ConflictException.ConflictExceptionMsg.ACCESS_CODE_PRESENT.getErrorMessage(), exception.getMessage());
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(accessCodeRepository).findByCodeAndEventIdAndRecurringEventId(anyString(), any(),anyLong());
    }
    @Test
    void test_getByCode_tickeingAccessCodeIsPresent(){
        //mock
        when(accessCodeRepository.findByCodeAndEventIdAndRecurringEventId(anyString(), any(),anyLong())).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        ticketingAccessCodeServiceImpl.getByCode(accessCode, event, recurringEventId);

        //assert
        verify(accessCodeRepository).findByCodeAndEventIdAndRecurringEventId(anyString(), any(),anyLong());
    }
    @Test
    void test_updateAccessCode_recurringEventTrue_recurringEventIdZero(){

        //setup
        accessCodeDto.setRecurringRelativeEndTime(1);
        accessCodeDto.setRecurringRelativeStartTime(1);
        Long recurringEventId=0L;

        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setId(0L);

        ticketingAccessCode.setRecurringEvents(recurringEvents);
        ticketingAccessCode.setRecurringRelativeStartTime(1);
        ticketingAccessCode.setRecurringRelativeEndTime(1);

        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<TicketingAccessCode>();
        ticketingAccessCodes.add(ticketingAccessCode);

        String startDate = accessCodeDto.getStartDate();
        String endDate = accessCodeDto.getEndDate();
        accessCodeDto.setStartDate(DateUtils.getFormattedDateInString(accessCodeDto.getStartDate(),Constants.DATE_FORMAT));
        accessCodeDto.setEndDate(DateUtils.getFormattedDateInString(accessCodeDto.getEndDate(),Constants.DATE_FORMAT));
        ticketingAccessCode.setStartDate(getDateInUTC(accessCodeDto.getStartDate(), ticketingAccessCode.getEventId().getEquivalentTimeZone()));
        ticketingAccessCode.setEndDate(getDateInUTC(accessCodeDto.getEndDate(), ticketingAccessCode.getEventId().getEquivalentTimeZone()));
        accessCodeDto.setStartDate(startDate);
        accessCodeDto.setEndDate(endDate);
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findById(anyLong())).thenReturn(Optional.of(ticketingAccessCode));
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),anyLong());
        when(accessCodeRepository.findAllByCreatedFromAndEventIdWithFutureDates(anyLong(),any(), any())).thenReturn(ticketingAccessCodes);


        //Execution
        ticketingAccessCodeServiceImpl.updateAccessCode(accessCodeDto, event, recurringEventId);

        //Assert
        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodesArgumentCaptor = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(accessCodeRepository).saveAll(ticketingAccessCodes);
        verify(accessCodeRepository, times(1)).save(ticketingAccessCodesArgumentCaptor.capture());
        TicketingAccessCode actual = ticketingAccessCodesArgumentCaptor.getValue();
        assertEquals(accessCodeDto.getCode(), actual.getCode());
        assertEquals(accessCodeDto.getEventTicketTypeId(), actual.getEventTicketTypeId());
        assertEquals(accessCodeDto.getRecurringRelativeEndTime(), actual.getRecurringRelativeEndTime());
        assertEquals(accessCodeDto.getRecurringRelativeStartTime(), actual.getRecurringRelativeStartTime());
        //removing date comparison because now we are saving UTC format in DB
        verify(ticketingAccessCodeServiceImpl, Mockito.times(2)).markTicketAsHiddenFalseIfAlreadyHidden(anyList(), any());
    }
    @Test
    void test_updateAccessCode_recurringEventTrue_recurringEventIdNotZero(){
        //setup
        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setId(recurringEventId);
        recurringEvents.setRecurringEventStartDate(startDate1);
        recurringEvents.setRecurringEventEndDate(endDate1);

        ticketingAccessCode.setRecurringEvents(recurringEvents);
        ticketingAccessCode.setRecurringRelativeStartTime(1);
        ticketingAccessCode.setRecurringRelativeEndTime(1);
        ticketingAccessCode.setCreatedFrom(-1L);

        accessCodeDto.setRecurringRelativeEndTime(1);
        accessCodeDto.setRecurringRelativeStartTime(1);
        Date startDate = DateUtils.getAddedMinutes(new DateTime(ticketingAccessCode.getRecurringEvents().getRecurringEventStartDate()), -ticketingAccessCode.getRecurringRelativeStartTime());
        Date endDate = DateUtils.getAddedMinutes(new DateTime(ticketingAccessCode.getRecurringEvents().getRecurringEventEndDate()), -ticketingAccessCode.getRecurringRelativeStartTime());

        List<Long> accessCodeTicketTypeList = new ArrayList<>();
        accessCodeTicketTypeList.add(ticketTypeId);

        List<TicketingAccessCode> ticketingAccessCodes = new ArrayList<TicketingAccessCode>();
        ticketingAccessCodes.add(ticketingAccessCode);

        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findById(anyLong())).thenReturn(Optional.of(ticketingAccessCode));
        doNothing().when(ticketingAccessCodeServiceImpl).validationForAccessCode(any(),any(),anyLong());



        //Execution
        ticketingAccessCodeServiceImpl.updateAccessCode(accessCodeDto, event, recurringEventId);

        //Assert
        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodesArgumentCaptor = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(accessCodeRepository, times(1)).save(ticketingAccessCodesArgumentCaptor.capture());
        TicketingAccessCode actual = ticketingAccessCodesArgumentCaptor.getValue();
        assertEquals(accessCodeDto.getCode(), actual.getCode());
        assertEquals(String.valueOf(ticketTypeId), actual.getEventTicketTypeId());
        assertEquals(accessCodeDto.getRecurringRelativeEndTime(), actual.getRecurringRelativeEndTime());
        assertEquals(accessCodeDto.getRecurringRelativeStartTime(), actual.getRecurringRelativeStartTime());
        assertEquals(startDate, actual.getStartDate());
        assertEquals(endDate,actual.getEndDate());
    }
    // Alok: Please write a test case for /makeItCustomAccessCode

    @Test
    void test_updateAccessCode_throwsAccessCodeNotFound(){
        //setup
        ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingAccessCodeServiceImpl.updateAccessCode(accessCodeDto, event, recurringEventId));

        //Assert
        assertEquals(NotFoundException.NotFound.ACCESS_CODE_NOT_FOUND.getErrorMessage(), exception.getMessage());
        verify(accessCodeRepository).findById(anyLong());
    }
    @Test
    void test_updateAccessCode_throwExceptionCodeAlreadyExist(){

        Ticketing ticketing = new Ticketing();
        ticketing.setId(1L);
        ticketing.setRecurringEvent(false);

        ticketingAccessCode.setId(2L);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(accessCodeRepository.findById(anyLong())).thenReturn(Optional.of(ticketingAccessCode));
        when(accessCodeRepository.findByCodeAndEventIdAndRecurringEventId(anyString(), any(),anyLong())).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> ticketingAccessCodeServiceImpl.updateAccessCode(accessCodeDto, event, recurringEventId));

        //Assert
        assertEquals(ConflictException.ConflictExceptionMsg.ACCESS_CODE_PRESENT.getErrorMessage(), exception.getMessage());
        verify(accessCodeRepository).findById(anyLong());
    }
    @Test
    void test_deleteByRecurringIdAndEventId_succsess() {
        //Execution
        ticketingAccessCodeServiceImpl.deleteByRecurringIdAndEventId(Collections.singletonList(recurringEventId),event);

        //Assert
        verify(accessCodeRepository).deleteByRecurringIdAndEventId(anyList(),any());
    }
    @Test
    void test_findByEventIdAndRecurringEventIdNull_succsess() {
        //setup
        List<TicketingAccessCode> ticketingAccessCodes=new ArrayList<TicketingAccessCode>();
        ticketingAccessCodes.add(ticketingAccessCode);

        //Execution
        ticketingAccessCodeServiceImpl.findByEventIdAndRecurringEventIdNull(event);

        //Assert
        verify(accessCodeRepository).findAllByEventIdAndRecurringEventIdisNull(any());
        assertNotNull(ticketingAccessCodes);
    }

    @Test
    void test_markTicketAsHiddenFalseIfAlreadyHiddenWithEmptyTicketTypeIds(){

        //Setup
        List<TicketingAccessCode> reAccessCodes = new ArrayList<>();
        TicketingAccessCode ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId("");
        reAccessCodes.add(ticketingAccessCode);

        //Mock
        when(ticketingTypeService.findByidInAndEvent(Arrays.asList(), event)).thenReturn(Collections.EMPTY_LIST);

        //Execution
        ticketingAccessCodeServiceImpl.markTicketAsHiddenFalseIfAlreadyHidden(reAccessCodes, event);
    }
    @Test
    void testGetDisplayPageSettingDataThrowExceptionTicketAccessCodeIsExpired() {

        ticketingAccessCode.setId(2L);

        //mock
        when(accessCodeRepository.findByCodeAndEventIdAndRecurringEventIdIsNull(accessCode, event)).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingAccessCodeServiceImpl.getByCodeANDCheckEnddate(accessCode, event, 0L));

        //assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_ACCESS_CODE_IS_EXPIRED.getDeveloperMessage(), exception.getMessage());

    }
    @Test
    void testGetDisplayPageSettingDataThrowExceptionTicketAccessCodeInFuture() {

        ticketingAccessCode.setStartDate(DateUtils.addDaysInDate(new Date(),1));
        ticketingAccessCode.setId(2L);

        //mock
        when(accessCodeRepository.findByCodeAndEventIdAndRecurringEventIdIsNull(accessCode, event)).thenReturn(Optional.of(ticketingAccessCode));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingAccessCodeServiceImpl.getByCodeANDCheckEnddate(accessCode, event, 0L));

        //assertion
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_ACCESS_CODE_IN_FUTURE.getErrorMessage(), exception.getMessage());

    }
}