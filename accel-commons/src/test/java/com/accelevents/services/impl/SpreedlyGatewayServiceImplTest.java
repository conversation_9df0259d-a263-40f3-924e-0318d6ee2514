package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.ThirdPartyExceptions;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.EventService;
import com.accelevents.services.StaffService;
import com.accelevents.services.StripeService;
import com.accelevents.services.UserService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.spreedly.dto.SpreedlyGatewayType;
import com.accelevents.spreedly.dto.gateways.CredentialRequestDto;
import com.accelevents.spreedly.dto.gateways.GatewayRequestDto;
import com.accelevents.spreedly.dto.transactions.response.TransactionDataDTO;
import com.accelevents.spreedly.service.SpreedlyApiExecutionService;
import com.accelevents.spreedly.service.SpreedlyGatewayAsyncTaskService;
import com.accelevents.spreedly.service.impl.SpreedlyGatewayServiceImpl;
import com.accelevents.utils.Constants;
import com.accelevents.utils.JsonMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpVersion;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHttpResponse;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.accelevents.enums.PlanConfigNames.*;
import static com.accelevents.utils.Constants.ORDER_ID;
import static com.accelevents.utils.Constants.SUCCEEDED;
import static com.accelevents.utils.Constants.Spreedly.CUSTOMER_ID;
import static com.accelevents.utils.Constants.Spreedly.SPREEDLY_PAYMENT_PROCESSOR;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SpreedlyGatewayServiceImplTest {

    @Spy
    @InjectMocks
    SpreedlyGatewayServiceImpl spreedlyGatewayServiceImpl;

    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private EventService eventService;
    @Mock
    private ROPayFlowConfigService roPayFlowConfigService;
    @Mock
    private SpreedlyGatewayAsyncTaskService spreedlyGatewayAsyncTaskService;
    @Mock
    private SpreedlyApiExecutionService spreedlyApiExecutionService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;


    private static final String PAYMENT_GATEWAY = "SP_BRAINTREE";
    private static final String GATEWAY_TOKEN = "UlSR3GCgSBzryKio8zeIF4aIHqm";
    private static final String PAYMENT_METHOD_TOKEN = "1rpKvP8zOUhj4Y9EDrIoIYQzzD5";
    private static final String FINGERPRINT = "16a02e37e5043919412c58753da12";
    private static final String customerId = "987654321"; //NOSONAR
    private static final String GATEWAY_RESPONSE_STRING;
    private static final String PURCHASE_API_RESPONSE_STRING;
    private static final String PURCHASE_API_RESPONSE_ERROR_STRING;
    private static final HttpResponse<JsonNode> pmTokenApiResponse;
    private static final HttpResponse<JsonNode> pmTokenErrorResponse;
    private static final Map<String, String> spreedlyResponseMap= new HashMap<>();
    private static final Map<String, Object> chargeMetadata = new HashMap<>();
    private static final Event event = EventDataUtil.getEvent();
    private static final String CURRENCY_CODE = event.getCurrency().getISOCode();
    private static final User user = EventDataUtil.getUser();
    private static final Staff staff = EventDataUtil.getUserStaff(user);
    private static final Staff wlStaff;
    private static final Stripe stripe;
    private static final GatewayRequestDto gatewayRequestDto;
    private static final Long ORDERID = 1L;
    private static final Map<String, String> credentialMap =  new HashMap<>();
    private static WhiteLabel whiteLabel;
    private static CredentialRequestDto credentialRequestDto;


    static {

        SpreedlyGatewayServiceImplTest.loadSpreedlyResponseFromFile();
        GATEWAY_RESPONSE_STRING = spreedlyResponseMap.getOrDefault("gateway_response_string", Constants.STRING_EMPTY);
        PURCHASE_API_RESPONSE_STRING = spreedlyResponseMap.getOrDefault("purchase_api_response_string", Constants.STRING_EMPTY);
        PURCHASE_API_RESPONSE_ERROR_STRING = spreedlyResponseMap.getOrDefault("purchase_api_response_error_string", Constants.STRING_EMPTY);


        pmTokenApiResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.getOrDefault("payment_method_token_api_response_string", Constants.STRING_EMPTY));
        pmTokenErrorResponse = getJsonNodeHttpResponse(404,
                spreedlyResponseMap.getOrDefault("payment_method_token_api_error_string", Constants.STRING_EMPTY));

        chargeMetadata.put(Constants.STRING_EMAIL, user.getEmail());
        chargeMetadata.put(Constants.STRING_CELL, user.getPhoneNumber());
        chargeMetadata.put(Constants.EVENTURL, event.getEventURL());
        chargeMetadata.put("EVENT_NAME", event.getName());
        chargeMetadata.put("BUYER_NAME", user.getFirstName() + Constants.STRING_BLANK + user.getLastName());
        chargeMetadata.put(ORDER_ID, String.valueOf(ORDERID));

        wlStaff = EventDataUtil.getUserStaff(user);
        wlStaff.setWhiteLabel(whiteLabel);

        staff.setEvent(event);
        staff.setEventId(event.getEventId());

        stripe = new Stripe();
        stripe.setId(1L);
        stripe.setAccessToken("*********");

        whiteLabel = new WhiteLabel();
        whiteLabel.setId(1L);

        credentialMap.put("merchant_account_id", "abc");
        credentialMap.put("merchant_id", "xyz");
        credentialMap.put("public_key", "123");
        credentialMap.put("private_key", "789");

        gatewayRequestDto = new GatewayRequestDto();
        credentialRequestDto = new CredentialRequestDto();
        credentialRequestDto.setAuthModeType("blue");
        credentialRequestDto.setCredentials(credentialMap);

    }

    private static void loadSpreedlyResponseFromFile() {
        try {
            Path path = Paths.get(Objects.requireNonNull(SpreedlyGatewayServiceImplTest.class.getClassLoader().getResource("jsonFile/spreedly_response_data.json")).getPath());
            String jsonContent = Files.readString(path);
            Map<String, Object> spreedlyResponseObjectMap = JsonMapper.stringToObjectWithTypeReference(jsonContent, new TypeReference<>() {});
            if(!CollectionUtils.isEmpty(spreedlyResponseObjectMap)) {
                spreedlyResponseObjectMap.forEach((key, value)->
                    spreedlyResponseMap.put(key, JsonMapper.parseToJsonString(value))
                );
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @BeforeEach
    public void setup() {
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(ENTERPRISE.getName());
        whiteLabel.setPlanConfig(planConfig);
        stripe.setPaymentGateway(PAYMENT_GATEWAY);
    }

    @NotNull
    private static HttpResponse<JsonNode> getJsonNodeHttpResponse(int statusCode, String responseString){
        org.apache.http.HttpResponse httpResponse = new BasicHttpResponse(HttpVersion.HTTP_1_1, statusCode, Constants.STRING_EMPTY);
        try {
            HttpEntity httpEntity = new StringEntity(responseString);
            httpResponse.setEntity(httpEntity);
        }catch (Exception e) {
            e.printStackTrace(); //NOSONAR
        }
        return new HttpResponse<>(httpResponse, JsonNode.class);
    }

    @NotNull
    private static HttpResponse<String> getStringHttpResponse(){
        org.apache.http.HttpResponse httpResponse = new BasicHttpResponse(HttpVersion.HTTP_1_1, 200, Constants.STRING_EMPTY);
        try {
            HttpEntity httpEntity = new StringEntity(Constants.SUCCESS);
            httpResponse.setEntity(httpEntity);
        }catch (Exception e) {
            e.printStackTrace(); //NOSONAR
        }
        return new HttpResponse<>(httpResponse, String.class);
    }

    // ----------------- WL payment gateway Connection ------------------//
    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithFreePlan() {
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(FREE_PLAN.getName());
        whiteLabel.setPlanConfig(planConfig);

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));

        assertEquals("This feature is not available on your current plan. Please upgrade plan to access it.", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithConnectedOtherPaymentGateway() {

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(stripe);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));

        assertEquals("White label is already connected to other payment gateway.", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithPayFlowIsConnected() {

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(roPayFlowConfigService.isPayFlowConnected(whiteLabel)).thenReturn(true);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));

        assertEquals("White label is already connected to other payment gateway.", exception.getMessage());
    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithInvalidField() throws UnirestException {

        credentialRequestDto = new CredentialRequestDto();
        credentialRequestDto.setAuthModeType("blue");

        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> response = getJsonNodeHttpResponse(400,
                spreedlyResponseMap.get("gateway_api_error_response_string"));

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(roPayFlowConfigService.isPayFlowConnected(whiteLabel)).thenReturn(false);
        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(response);


        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());

        assertEquals("Merchant can't be blank", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithValidCredentialOfLiveAccount() throws UnirestException {

        //setup
        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> gatewayResponse = getJsonNodeHttpResponse(200, GATEWAY_RESPONSE_STRING);
        HttpResponse<JsonNode> purchaseApiResponse = getJsonNodeHttpResponse(400, PURCHASE_API_RESPONSE_STRING);

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(roPayFlowConfigService.isPayFlowConnected(whiteLabel)).thenReturn(false);
        when(stripeService.findByWhiteLabel(whiteLabel)).thenReturn(null);
        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(gatewayResponse);
        when(spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(anyString())).thenReturn(pmTokenApiResponse);
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiResponse);

        //execution
        spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto);

        ArgumentCaptor<Stripe> stripeArgumentCapture = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService, atLeastOnce()).save(stripeArgumentCapture.capture());
        Stripe stripeArgumentCaptureValue = stripeArgumentCapture.getValue();

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreatePaymentMethodTokenApi(anyString());
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());

        assertEquals(whiteLabel.getId(), stripeArgumentCaptureValue.getWhiteLabelId());
        assertEquals(PAYMENT_GATEWAY, stripeArgumentCaptureValue.getPaymentGateway());
        assertEquals(GATEWAY_TOKEN, stripeArgumentCaptureValue.getAccessToken());
        assertEquals(SPREEDLY_PAYMENT_PROCESSOR, stripeArgumentCaptureValue.getAccountSource());
        assertEquals(2.89d, stripeArgumentCaptureValue.getCCPercentageFee());
        assertEquals(0.29d, stripeArgumentCaptureValue.getCCFlatFee());
        assertTrue(stripeArgumentCaptureValue.isDefaultAccount());
    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithInValidCredentialOfLiveAccount() throws UnirestException {

        //setup
        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> gatewayResponse = getJsonNodeHttpResponse(200, GATEWAY_RESPONSE_STRING);
        HttpResponse<JsonNode> purchaseApiResponse = getJsonNodeHttpResponse(400, PURCHASE_API_RESPONSE_ERROR_STRING);

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(roPayFlowConfigService.isPayFlowConnected(whiteLabel)).thenReturn(false);
        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(gatewayResponse);
        when(spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(anyString())).thenReturn(pmTokenApiResponse);
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiResponse);
        when(spreedlyApiExecutionService.executeRedactGatewayConnectionApi(anyString())).thenReturn( getStringHttpResponse());

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreatePaymentMethodTokenApi(anyString());
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeRedactGatewayConnectionApi(anyString());

        assertEquals("Braintree::AuthorizationError", exception.getMessage());
    }

    @Test
    void testCreateSpreedlyPaymentGatewayForWLWithInValidCredentialOfLiveAccountWithInvalidResponse() throws UnirestException {

        //setup
        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> gatewayResponse = getJsonNodeHttpResponse(200, GATEWAY_RESPONSE_STRING);
        HttpResponse<JsonNode> purchaseApiResponse = getJsonNodeHttpResponse(400, Constants.STRING_EMPTY);

        // Mock
        when(roStaffService.findWhiteLabelAdminStaff(any(), any())).thenReturn(Optional.of(wlStaff));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(roPayFlowConfigService.isPayFlowConnected(whiteLabel)).thenReturn(false);
        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(gatewayResponse);
        when(spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(anyString())).thenReturn(pmTokenApiResponse);
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiResponse);
        when(spreedlyApiExecutionService.executeRedactGatewayConnectionApi(anyString())).thenReturn( getStringHttpResponse());

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForWL(whiteLabel, user, gatewayRequestDto));

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreatePaymentMethodTokenApi(anyString());
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeRedactGatewayConnectionApi(anyString());

        assertEquals("Error while connecting payment gateway.", exception.getMessage());
    }

    // ----------------------- Event Level Payment gateway connection -------------

    @Test
    void testCreateSpreedlyPaymentGatewayForEventWithFreePlan() {

        // Mock
        when(eventPlanConfigService.getEventPlanName(anyLong())).thenReturn(FREE_PLAN.getName());

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForEvent(event, user, gatewayRequestDto));

        assertEquals("This feature is not available on your current plan. Please upgrade plan to access it.", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForEventWithOutEventAdminRole() {

        // Mock
        when(eventPlanConfigService.getEventPlanName(anyLong())).thenReturn(PROFESSIONAL.getName());
        when(roUserService.isSuperAdminUser(any())).thenReturn(true);
        when(roStaffService.isEventAdmin(any(), any())).thenReturn(false);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForEvent(event, user, gatewayRequestDto));

        assertEquals("You must be added as an event Admin to connect a payment processing account.", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForEventWithConnectedOtherPaymentGateway() {

        stripe.setPaymentGateway(Constants.STRIPE);

        // Mock
        when(eventPlanConfigService.getEventPlanName(anyLong())).thenReturn(PROFESSIONAL.getName());
        when(roUserService.isSuperAdminUser(any())).thenReturn(false);
        when(stripeService.findByEventAndDefaultAccount(any(), anyBoolean())).thenReturn(stripe);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForEvent(event, user, gatewayRequestDto));

        assertEquals("Event is connected to STRIPE payment gateway.", exception.getMessage());

    }

    @Test
    void testCreateSpreedlyPaymentGatewayForEventWithValidCredentialOfLiveAccount() throws UnirestException {

        //setup
        stripe.setEvent(event);
        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> gatewayResponse = getJsonNodeHttpResponse(200, GATEWAY_RESPONSE_STRING);
        HttpResponse<JsonNode> purchaseApiResponse = getJsonNodeHttpResponse(400, PURCHASE_API_RESPONSE_STRING);

        // Mock
        when(eventPlanConfigService.getEventPlanName(anyLong())).thenReturn(PROFESSIONAL.getName());
        when(roUserService.isSuperAdminUser(any())).thenReturn(false);
        when(stripeService.findByEventAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(staffService.findAdminAndStaffByEvent(any())).thenReturn(List.of(staff));
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        doNothing().when(eventService).enablePaymentProcessing(any(), any());
        doNothing().when(eventRepoService).save(any());
        doNothing().when(eventPlanConfigService).updateAttendeeImportLimitOnceStripeIsConnected(any());
        doNothing().when(eventService).autoActivateAndEnableTicketing(any());

        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(gatewayResponse);
        when(spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(anyString())).thenReturn(pmTokenApiResponse);
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiResponse);

        //execution
        spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForEvent(event, user, gatewayRequestDto);

        ArgumentCaptor<Stripe> stripeArgumentCapture = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService, atLeastOnce()).save(stripeArgumentCapture.capture());
        Stripe stripeArgumentCaptureValue = stripeArgumentCapture.getValue();

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreatePaymentMethodTokenApi(anyString());
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());

        assertEquals(event.getEventId(), stripeArgumentCaptureValue.getEvent().getEventId());
        assertEquals(0L, stripeArgumentCaptureValue.getWhiteLabelId());
        assertEquals(PAYMENT_GATEWAY, stripeArgumentCaptureValue.getPaymentGateway());
        assertEquals(GATEWAY_TOKEN, stripeArgumentCaptureValue.getAccessToken());
        assertEquals(SPREEDLY_PAYMENT_PROCESSOR, stripeArgumentCaptureValue.getAccountSource());
        assertEquals(2.89d, stripeArgumentCaptureValue.getCCPercentageFee());
        assertEquals(0.29d, stripeArgumentCaptureValue.getCCFlatFee());
        assertTrue(stripeArgumentCaptureValue.isDefaultAccount());
    }

    @Test
    void testCreateSpreedlyPaymentGatewayForEventWithValidCredentialAndEventCurrencyIsPound() throws UnirestException {

        //setup
        event.setCurrency(Currency.POUND);
        stripe.setEvent(event);
        gatewayRequestDto.setGatewayType(SpreedlyGatewayType.SP_BRAINTREE);
        gatewayRequestDto.setGatewayData(credentialRequestDto);

        HttpResponse<JsonNode> gatewayResponse = getJsonNodeHttpResponse(200, GATEWAY_RESPONSE_STRING);
        HttpResponse<JsonNode> purchaseApiResponse = getJsonNodeHttpResponse(400, PURCHASE_API_RESPONSE_STRING);

        // Mock
        when(eventPlanConfigService.getEventPlanName(anyLong())).thenReturn(PROFESSIONAL.getName());
        when(roUserService.isSuperAdminUser(any())).thenReturn(false);
        when(stripeService.findByEventAndDefaultAccount(any(), anyBoolean())).thenReturn(null);
        when(staffService.findAdminAndStaffByEvent(any())).thenReturn(List.of(staff));
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        doNothing().when(eventService).enablePaymentProcessing(any(), any());
        doNothing().when(eventRepoService).save(any());
        doNothing().when(eventPlanConfigService).updateAttendeeImportLimitOnceStripeIsConnected(any());
        doNothing().when(eventService).autoActivateAndEnableTicketing(any());

        when(spreedlyApiExecutionService.executeCreateGatewayConnectionApi(any())).thenReturn(gatewayResponse);
        when(spreedlyApiExecutionService.executeCreatePaymentMethodTokenApi(anyString())).thenReturn(pmTokenApiResponse);
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiResponse);

        //execution
        spreedlyGatewayServiceImpl.createSpreedlyPaymentGatewayForEvent(event, user, gatewayRequestDto);

        ArgumentCaptor<Stripe> stripeArgumentCapture = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService, atLeastOnce()).save(stripeArgumentCapture.capture());
        Stripe stripeArgumentCaptureValue = stripeArgumentCapture.getValue();

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreateGatewayConnectionApi(any());
        verify(spreedlyApiExecutionService, atLeastOnce()).executeCreatePaymentMethodTokenApi(anyString());
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());

        assertEquals(event.getEventId(), stripeArgumentCaptureValue.getEvent().getEventId());
        assertEquals(0L, stripeArgumentCaptureValue.getWhiteLabelId());
        assertEquals(PAYMENT_GATEWAY, stripeArgumentCaptureValue.getPaymentGateway());
        assertEquals(GATEWAY_TOKEN, stripeArgumentCaptureValue.getAccessToken());
        assertEquals(SPREEDLY_PAYMENT_PROCESSOR, stripeArgumentCaptureValue.getAccountSource());
        assertEquals(1.9d, stripeArgumentCaptureValue.getCCPercentageFee());
        assertEquals(0.20d, stripeArgumentCaptureValue.getCCFlatFee());
        assertTrue(stripeArgumentCaptureValue.isDefaultAccount());
    }

    // ----------------- End of Event Level Payment gateway connect -----------

    @Test
    void testGetCustomerIdFromSpreedlyThirdPartyVaultWithInvalidPaymentMethodToken() throws UnirestException {

        //mock
        when(spreedlyApiExecutionService.executePaymentMethodStoreApi(anyString(), any())).thenReturn(pmTokenErrorResponse);

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.getCustomerIdFromSpreedlyThirdPartyVault(stripe, null, event, user, null));

        verify(spreedlyApiExecutionService, atLeastOnce()).executePaymentMethodStoreApi(anyString(), any());
        assertEquals("There is no payment method corresponding to the specified payment method token.", thirdPartyExceptions.getMessage());
    }

    @Test
    void testGetCustomerIdFromSpreedlyThirdPartyVaultWithValidPaymentMethodToken() throws UnirestException {

        HttpResponse<JsonNode> storeApiResponse = getJsonNodeHttpResponse(200, spreedlyResponseMap.get("store_third_party_vault_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executePaymentMethodStoreApi(anyString(), any())).thenReturn(storeApiResponse);

        CustomerCardDto customerCardData = spreedlyGatewayServiceImpl.getCustomerIdFromSpreedlyThirdPartyVault(stripe, PAYMENT_METHOD_TOKEN, event, user, null);

        assertEquals("987654321", customerCardData.getCustomerId());
        assertEquals("1111", customerCardData.getLastFour());
        assertEquals(12, customerCardData.getExpDateMonth());
        assertEquals(2029, customerCardData.getExpDateYear());
        assertEquals("visa", customerCardData.getCardType());
        assertEquals(FINGERPRINT, customerCardData.getFingerPrint());
        assertEquals(PAYMENT_METHOD_TOKEN, customerCardData.getCardId());
    }

    @Test
    void testGetCustomerIdFromSpreedlyThirdPartyVaultWithValidPaymentMethodTokenWithCustomerId() throws UnirestException {

        HttpResponse<JsonNode> storeApiResponse = getJsonNodeHttpResponse(200, spreedlyResponseMap.get("store_third_party_vault_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executePaymentMethodStoreApi(anyString(), any())).thenReturn(storeApiResponse);

        CustomerCardDto customerCardData = spreedlyGatewayServiceImpl.getCustomerIdFromSpreedlyThirdPartyVault(stripe, PAYMENT_METHOD_TOKEN, event, user, customerId);

        assertEquals(customerId, customerCardData.getCustomerId());
        assertEquals("1111", customerCardData.getLastFour());
        assertEquals(12, customerCardData.getExpDateMonth());
        assertEquals(2029, customerCardData.getExpDateYear());
        assertEquals("visa", customerCardData.getCardType());
        assertEquals(FINGERPRINT, customerCardData.getFingerPrint());
        assertEquals(PAYMENT_METHOD_TOKEN, customerCardData.getCardId());
    }

    @Test
    void testGetCardDetailsFromSpreedlyWithInvalidToken() throws UnirestException {

        HttpResponse<JsonNode> getApiResponse = getJsonNodeHttpResponse(404,
                spreedlyResponseMap.get("get_card_details_api_response_error_string"));

        //mock
        when(spreedlyApiExecutionService.executeGetPaymentMethodTokenDetailApi(anyString())).thenReturn(getApiResponse);

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.getCardDetailsFromSpreedly(stripe, null, Constants.STRING_EMPTY , event, user, true));

        verify(spreedlyApiExecutionService, atLeastOnce()).executeGetPaymentMethodTokenDetailApi(anyString());
        assertEquals("Unable to find the specified payment method.", thirdPartyExceptions.getMessage());
    }

    @Test
    void testGetCardDetailsFromSpreedlyWitValidTokenAndRetainIsFalse() throws UnirestException {

        HttpResponse<JsonNode> getApiResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.get("get_card_details_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executeGetPaymentMethodTokenDetailApi(anyString())).thenReturn(getApiResponse);

        CustomerCardDto customerCardData = spreedlyGatewayServiceImpl.getCardDetailsFromSpreedly(stripe, null, PAYMENT_METHOD_TOKEN, event, user, false);

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeGetPaymentMethodTokenDetailApi(anyString());
        verify(spreedlyGatewayAsyncTaskService, never()).retainSpreedlyPaymentMethodToken(anyString());

        assertEquals("cus_eventId_1_userId_1", customerCardData.getCustomerId());
        assertEquals("1111", customerCardData.getLastFour());
        assertEquals(12, customerCardData.getExpDateMonth());
        assertEquals(2029, customerCardData.getExpDateYear());
        assertEquals("visa", customerCardData.getCardType());
        assertEquals(FINGERPRINT, customerCardData.getFingerPrint());
        assertEquals(PAYMENT_METHOD_TOKEN, customerCardData.getCardId());

    }

    @Test
    void testGetCardDetailsFromSpreedlyWitValidTokenAndRetainIsTrue() throws UnirestException {

        HttpResponse<JsonNode> getApiResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.get("get_card_details_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executeGetPaymentMethodTokenDetailApi(anyString())).thenReturn(getApiResponse);

        CustomerCardDto customerCardData = spreedlyGatewayServiceImpl.getCardDetailsFromSpreedly(stripe, null, PAYMENT_METHOD_TOKEN, event, user, true);

        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeGetPaymentMethodTokenDetailApi(anyString());
        verify(spreedlyGatewayAsyncTaskService, atLeastOnce()).retainSpreedlyPaymentMethodToken(anyString());

        assertEquals("cus_eventId_1_userId_1", customerCardData.getCustomerId());
        assertEquals(FINGERPRINT, customerCardData.getFingerPrint());
        assertEquals(PAYMENT_METHOD_TOKEN, customerCardData.getCardId());

    }

    @Test
    void testCreateChargeFromSpreedlyWithInvalidPaymentMethodToken() throws UnirestException {

        //mock
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(pmTokenErrorResponse);
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(Collections.emptyList());
        
        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.createChargeFromSpreedly(stripe, null, 100,  CURRENCY_CODE,
                        false, "BraintreeEvent", chargeMetadata, Constants.STRING_EMPTY));

        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());
        assertEquals("There is no payment method corresponding to the specified payment method token.", thirdPartyExceptions.getMessage());
    }

    @Test
    void testCreateChargeFromSpreedlyWithInvalidAmount() throws UnirestException {

        HttpResponse<JsonNode> purchaseApiErrorResponse = getJsonNodeHttpResponse(422,
                spreedlyResponseMap.get("invalid_amount_error_string"));

        //mock
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiErrorResponse);
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(Collections.emptyList());

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.createChargeFromSpreedly(stripe, null, 0,  CURRENCY_CODE,
                        false, "BraintreeEvent", chargeMetadata, PAYMENT_METHOD_TOKEN));

        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());
        assertEquals("Amount must be greater than 0", thirdPartyExceptions.getMessage());
    }

    @Test
    void testCreateChargeFromSpreedlyValidData() throws UnirestException {

        HttpResponse<JsonNode> purchaseApiSuccessResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.get("purchase_api_success_response_string"));

        //mock
        when(spreedlyApiExecutionService.executePurchaseApi(anyString(), any())).thenReturn(purchaseApiSuccessResponse);
        when(eventCommonRepoService.findByOrder(anyLong())).thenReturn(Collections.emptyList());

        TransactionDataDTO transactionDto = spreedlyGatewayServiceImpl.createChargeFromSpreedly(stripe, null, 1, event.getCurrency().getISOCode(),
                false, "BraintreeEvent", chargeMetadata, PAYMENT_METHOD_TOKEN);
        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executePurchaseApi(anyString(), any());

        assertEquals("Ppq2pMNeNTgtwakx5m0KIJ5", transactionDto.getToken());
        assertEquals(100, transactionDto.getAmount());
        assertEquals(user.getEmail(), transactionDto.getEmail());
        assertEquals(String.valueOf(ORDERID), transactionDto.getOrderId());
        assertEquals("EVENT-BraintreeEvent", transactionDto.getDescription());
        assertEquals("987654321", transactionDto.getGatewaySpecificFields().get(SpreedlyGatewayType.SP_BRAINTREE.getValue()).get(CUSTOMER_ID));
    }

    @Test
    void testRefundChargeFromSpreedlyWithInvalidChargeId() throws UnirestException {

        HttpResponse<JsonNode> refundApiErrorResponse = getJsonNodeHttpResponse(404,
                spreedlyResponseMap.get("refund_charge_with_invalid_charge_id_response"));

        //mock
        when(spreedlyApiExecutionService.executeRefundApi(anyString(), any())).thenReturn(refundApiErrorResponse);

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.refundChargeFromSpreedly(stripe, Constants.STRING_EMPTY, 1,  CURRENCY_CODE,
                        null));

        verify(spreedlyApiExecutionService, atLeastOnce()).executeRefundApi(anyString(), any());
        assertEquals("Unable to find the specified reference transaction.", thirdPartyExceptions.getMessage());
    }

    @Test
    void testRefundChargeFromSpreedlyWithInvalidAmount() throws UnirestException {

        HttpResponse<JsonNode> refundApiErrorResponse = getJsonNodeHttpResponse(422,
                spreedlyResponseMap.get("invalid_amount_error_string"));

        //mock
        when(spreedlyApiExecutionService.executeRefundApi(anyString(), any())).thenReturn(refundApiErrorResponse);

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.refundChargeFromSpreedly(stripe, Constants.STRING_EMPTY, 0,  CURRENCY_CODE,
                       null));

        verify(spreedlyApiExecutionService, atLeastOnce()).executeRefundApi(anyString(), any());
        assertEquals("Amount must be greater than 0", thirdPartyExceptions.getMessage());
    }

    @Test
    void testRefundChargeFromSpreedlyWithoutAmountSettled() throws UnirestException {

        HttpResponse<JsonNode> refundApiErrorResponse = getJsonNodeHttpResponse(406,
                spreedlyResponseMap.get("un_settled_amount_refund_error_string"));

        //mock
        when(spreedlyApiExecutionService.executeRefundApi(anyString(), any())).thenReturn(refundApiErrorResponse);

        ThirdPartyExceptions thirdPartyExceptions = assertThrows(ThirdPartyExceptions.class,
                () -> spreedlyGatewayServiceImpl.refundChargeFromSpreedly(stripe, Constants.STRING_EMPTY, 1,  CURRENCY_CODE,
                        null));

        verify(spreedlyApiExecutionService, atLeastOnce()).executeRefundApi(anyString(), any());
        assertEquals("Cannot refund transaction unless it is settled. (91506)", thirdPartyExceptions.getMessage());
    }

    @Test
    void testRefundChargeFromSpreedlyValidData() throws UnirestException {

        HttpResponse<JsonNode> refundApiSuccessResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.get("refund_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executeRefundApi(anyString(), any())).thenReturn(refundApiSuccessResponse);

        RefundInfoDto refundInfoDto = spreedlyGatewayServiceImpl.refundChargeFromSpreedly(stripe, "2ppthU38gsq0Q7jUKqgYN", 1, event.getCurrency().getISOCode(), Collections.emptyMap());
        //verify
        verify(spreedlyApiExecutionService, atLeastOnce()).executeRefundApi(anyString(), any());

        assertEquals("2ppthU38gsq0Q7jUKqgYNMASz", refundInfoDto.getId());
        assertEquals(100, refundInfoDto.getRefundedAmount());
        assertEquals(SUCCEEDED, refundInfoDto.getStatus());
        assertEquals(100, refundInfoDto.getStripeNetSale());
        assertEquals(0, refundInfoDto.getRefundedApplicationFee());
    }

    @Test
    @ExtendWith(OutputCaptureExtension.class)
    void testRemoveCardFromSpreedlyWithValidData(CapturedOutput capturedOutput) throws UnirestException {

        HttpResponse<JsonNode> redactApiSuccessResponse = getJsonNodeHttpResponse(200,
                spreedlyResponseMap.get("remove_card_details_api_response_string"));

        //mock
        when(spreedlyApiExecutionService.executeRedactPaymentMethodToken(anyString())).thenReturn(redactApiSuccessResponse);

        spreedlyGatewayServiceImpl.removeCardFromSpreedly(event, user, PAYMENT_METHOD_TOKEN);
        //verify
        assertThat(capturedOutput.getOut()).contains("removeCardFromSpreedly, Payment method token is removed successfully for event 1 user 1");

    }


}
