package com.accelevents.services.impl;

import com.accelevents.common.dto.ItemInstructionDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.event.EventModules;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.repositories.CauseAuctionRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.ItemService;
import com.accelevents.services.PledgeService;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CauseAuctionServiceImplTest {

	@InjectMocks
	@Spy
	CauseAuctionServiceImpl causeAuctionServiceImpl;
	@Mock
	TextMessageUtils textMessageUtils;
	@Mock
	CauseAuctionRepository causeAuctionRepository;
    @Mock
    ROStaffService roStaffService;
	@Mock
	PledgeService pledgeService;
	@Mock
	private ItemService itemService;
    @Mock
    private ROEventService roEventService;

    private User user;
	
	@Test
	void testItemCheck() {
		Event event = new Event();
		event.setEventId(1L);
		event.setCurrency(Currency.USD);
		
		int amount = 10;
		
		Item item = new Item();
		item.setCurrentBid(100d);
		item.setStartingBid(10);
		item.setCode("AUC");
		
		boolean isStaff = false;
		
		CauseAuction causeAuction = new CauseAuction();
		when(causeAuctionRepository.findByEventId(event.getEventId())).thenReturn(causeAuction);
		
		causeAuctionServiceImpl.itemCheck(event, amount, item , false);
		verify(textMessageUtils).getCauseAuctionPriceCheckNoBidsMessage(event, causeAuction, item, event.getCurrency().getSymbol(), false, amount);
	}
	
	@Test
	void testPledgeForDummyMsgString() {
		CauseAuction causeAuction = new CauseAuction();
		
		Event event = new Event();
		event.setEventId(1L);
		event.setCurrency(Currency.USD);
		
		User user = new User();
		
		boolean isStaff = true;
		
		Item item = new Item();
		int amount = 10;
		String message = "dummyText";
		
		when(causeAuctionRepository.findByEventId(event.getEventId())).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);
		when(textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item , event.getCurrency().getSymbol(), true, amount, event)).thenReturn(message);
		
		causeAuctionServiceImpl.pledge(event, item, user, amount);
		
		verify(roStaffService).isEventStaffOrAdmin(event, user);
		verify(textMessageUtils).getCauseAuctionStatusCheckMessage(causeAuction, item, event.getCurrency().getSymbol(), true, amount, event);
	}

	@Test
	void testPledgeForEmptyMsgStringCCEnabledAndFirstNameNotEmpty() {
		CauseAuction causeAuction = new CauseAuction();
		
		Event event = new Event();
		event.setEventId(1L);
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		
		User user = new User();
		user.setFirstName("User 1");
		
		boolean isStaff = true;
		
		Item item = new Item();
		double amount = 10d;
		String message = StringUtils.EMPTY;
		
		when(causeAuctionRepository.findByEventId(event.getEventId())).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);
		when(textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item , event.getCurrency().getSymbol(), true, amount, event)).thenReturn(message);
		
		boolean isConfirmedPledge = true;
		
		Pledge pledge = new Pledge(user, causeAuction.getId(), item, amount, BiddingSource.SMS,
				true);
		pledge.setPledgeTime(new Date());
		

		
		causeAuctionServiceImpl.pledge(event, item, user, amount);
		
		verify(roStaffService).isEventStaffOrAdmin(event, user);
		verify(textMessageUtils).getCauseAuctionStatusCheckMessage(causeAuction, item, event.getCurrency().getSymbol(), true, amount, event);
		verify(textMessageUtils).getPledgeCheckoutMessage(event, user, item, amount);
	}
	
	@Test
	void testPledgeForEmptyMsgStringCCEnabledAndFirstNameEmpty() {
		CauseAuction causeAuction = new CauseAuction();
		
		Event event = new Event();
		event.setEventId(1L);
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(true);
		
		User user = new User();
		
		boolean isStaff = true;
		
		Item item = new Item();
		double amount = 10d;
		String message = StringUtils.EMPTY;
		
		when(causeAuctionRepository.findByEventId(event.getEventId())).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);
		when(textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item , event.getCurrency().getSymbol(), true, amount, event)).thenReturn(message);
		
		boolean isConfirmedPledge = true;
		
		Pledge pledge = new Pledge(user, causeAuction.getId(), item, amount, BiddingSource.SMS,
				true);
		pledge.setPledgeTime(new Date());
		

		
		causeAuctionServiceImpl.pledge(event, item, user, amount);
		
		verify(roStaffService).isEventStaffOrAdmin(event, user);
		verify(textMessageUtils).getCauseAuctionStatusCheckMessage(causeAuction, item, event.getCurrency().getSymbol(), true, amount, event);
		verify(textMessageUtils).getPledgeCheckoutMessage(event, user, item, amount);
	}
	
	@Test
	void testPledgeForEmptyMsgStringCCDisablesAndFirstNameNotEmpty() {
		CauseAuction causeAuction = new CauseAuction();
		
		Event event = new Event();
		event.setEventId(1L);
		event.setCurrency(Currency.USD);
		event.setCreditCardEnabled(false);
		
		User user = new User();
		user.setFirstName("user 2");
		
		boolean isStaff = true;
		
		Item item = new Item();
		double amount = 10d;
		String message = StringUtils.EMPTY;
		
		when(causeAuctionRepository.findByEventId(event.getEventId())).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);
		when(textMessageUtils.getCauseAuctionStatusCheckMessage(causeAuction, item , event.getCurrency().getSymbol(), true, amount, event)).thenReturn(message);
		
		boolean isConfirmedPledge = true;
		
		Pledge pledge = new Pledge(user, causeAuction.getId(), item, amount, BiddingSource.SMS,
				true);
		pledge.setPledgeTime(new Date());
		causeAuctionServiceImpl.pledge(event, item, user, amount);
		
		verify(roStaffService).isEventStaffOrAdmin(event, user);
		verify(textMessageUtils).getCauseAuctionStatusCheckMessage(causeAuction, item, event.getCurrency().getSymbol(), true, amount, event);
		verify(textMessageUtils).getPledgeCheckoutMessage(event, user, item, amount);
	}

	@Test
	void test_updateInstructionForFundANeedItemInstructionNOTFound(){

		Event event = new Event();
		event.setAuctionId(1);

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = null;
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//mock

		//execution
		String actualResult = causeAuctionServiceImpl.updateInstructionForFundANeed(event, ph,user,languageMap);
		assertTrue(Constants.STRING_EMPTY.equalsIgnoreCase(actualResult));
	}

	@Test
	void test_updateInstructionForFundANeed_ItemInstructionFound(){

		Event event = new Event();
		event.setAuctionId(1);
		event.setCurrency(Currency.USD);

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = new ItemInstructionDto();
		itemInstructionDto.setCode("AAA");
		itemInstructionDto.setStartingBid(50);
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();
		//mock
		when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getCauseAuctionId(),  ModuleType.CAUSEAUCTION)).thenReturn(itemInstructionDto);
		when(roEventService.getLanguageCodeByUserOrEvent(any(),any())).thenReturn("EN");

		//execution
		String actualResult = causeAuctionServiceImpl.updateInstructionForFundANeed(event, ph,user,languageMap);
		assertTrue("Pledge here or text Your Pledge To: 123123123 with the item's three letter code and pledge amount ex. AAA50".equalsIgnoreCase(actualResult));
	}

	@Test
	void test_getPerformanceData(){

		//SetUp
		Event event = new Event();
		event.setEventId(1L);
		event.setCauseAuctionEnabled(true);

		EventModules eventModules = new EventModules();
		//Mock
		when(causeAuctionRepository.findCauseAuctionModuleByEventId(anyLong())).thenReturn(eventModules);

		//Execute
		causeAuctionServiceImpl.getPerformanceData(event);

		Mockito.verify(causeAuctionRepository, Mockito.times(1)).findCauseAuctionModuleByEventId(event.getEventId());
	}
}
