package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.session_speakers.MeetingSchedule;
import com.accelevents.session_speakers.services.MeetingScheduleRepoService;
import com.accelevents.session_speakers.services.impl.MeetingScheduleServiceImpl;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class MeetingScheduleServiceImplTest {

    @Spy
    @InjectMocks
    private MeetingScheduleServiceImpl meetingScheduleServiceImpl = new MeetingScheduleServiceImpl();
    private Event event;
    private Long user1;
    private Long user2;
    private MeetingSchedule meetingSchedule;

    @Mock
    private MeetingScheduleRepoService meetingScheduleRepoService;
    private Date timeStamp = DateUtils.getCurrentDate();

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        meetingSchedule = new MeetingSchedule();
        event = EventDataUtil.getEvent();
        user1 = EventDataUtil.getUser().getUserId();
        user2 = EventDataUtil.getOtherUser().getUserId();
        setMeetingSchedule(meetingSchedule);
    }

    @Test
    void test_save() {
        meetingScheduleRepoService.save(meetingSchedule);
        ArgumentCaptor<MeetingSchedule> meetingScheduleArgumentCaptor = ArgumentCaptor.forClass(MeetingSchedule.class);
        Mockito.verify(meetingScheduleRepoService).save(meetingScheduleArgumentCaptor.capture());

        MeetingSchedule meetingSchedule1 = meetingScheduleArgumentCaptor.getValue();
        assertcheckMeeting(meetingSchedule1);
    }

    private void setMeetingSchedule(MeetingSchedule meetingSchedule) {
        meetingSchedule.setEvent(event);
        meetingSchedule.setNote("new meeting");
        meetingSchedule.setMeetingStartTime(timeStamp);
        meetingSchedule.setSenderUserId(user1);
        meetingSchedule.setReceiverUserId(user2);
    }

    private void assertcheckMeeting(MeetingSchedule meetingSchedule) {
        assertEquals(meetingSchedule.getMeetingStartTime(), timeStamp);
        assertEquals(meetingSchedule.getEvent().getEventId(), event.getEventId());
        assertEquals(meetingSchedule.getSenderUserId(), user1);
        assertEquals(meetingSchedule.getReceiverUserId(), user2);

    }
}
