package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.common.dto.HostEventTicketDetail;
import com.accelevents.common.dto.TicketGraphDetail;
import com.accelevents.common.dto.TicketingTypeGraphDetail;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.EventTicketRevenueDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.stats.EventStats;
import com.accelevents.dto.stats.TicketStats;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.perfomance.dto.TicketingSaleData;
import com.accelevents.perfomance.dto.TicketingSaleWrapper;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.StaffRepository;
import com.accelevents.repositories.TicketingTypeRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.session_speakers.repo.SpeakerRepo;
import com.accelevents.ticketing.dto.TicketingBuyerDataFromDB;
import com.accelevents.ticketing.dto.TicketingHomeDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.math.BigInteger;
import java.util.*;

import static com.accelevents.utils.TimeZoneUtil.getDateInLocal;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class TicketingStatisticsServiceImplTest {

    @InjectMocks
    @Spy
    TicketingStatisticsServiceImpl ticketingStatisticsServiceImpl;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private StripeService stripeService;
    @Mock
    private CommonEventService commonEventService;
    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private EventTicketsRepository eventTicketsRepository;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private StaffRepository staffRepository;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private UserService userService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private SpeakerRepo speakerRepo;
    @Mock
    private EventTicketTransactionService eventTicketTransactionService;
    private Event event;
    private TicketingType ticketingType;
    private TicketStats ticketStats;
    private TicketingBuyerDataFromDB ticketingBuyerDataFromDB;
    private StripeDTO stripeDTO;
	private TicketingCoupon ticketingCoupon;
    private TicketingOrderManager ticketingOrderManager;
    private RecurringEvents recurringEvent;
    private Ticketing ticketing;

    private Long id = 1L;
    private Long eventId = 1L;
    private Long recurringEventId = 1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketingType = new TicketingType();
        ticketStats = new TicketStats();
        stripeDTO = new StripeDTO();
        stripeDTO.setPaymentGateway("StripePaymentGateway");
        ticketingBuyerDataFromDB = new TicketingBuyerDataFromDB(0,0,0,0,0,0,0,0, 0, 0, 0);
		TicketGraphDetail ticketGraphDetail = new TicketGraphDetail(new Date(), 0L, 0d);
        ticketingCoupon = new TicketingCoupon();
        ticketingOrderManager = new TicketingOrderManager();
        recurringEvent = new RecurringEvents();
        ticketing = new Ticketing();
    }

    @Test
    void test_getTicketSellData() {

        //Setup
        ticketStats.setSoldCount(1L);

        ticketingType.setEndDate(new Date());

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //Mock
        when(ticketingTypeService.getAllByEventIdAndRecurringId(event, recurringEventId, DataType.ADDON)).thenReturn(ticketingTypes);
        when(eventCommonRepoService.countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED)).thenReturn(ticketStats);

        //Execution
        List<TicketingSaleData> ticketingSaleData = ticketingStatisticsServiceImpl.getTicketSellData(event,recurringEventId, DataType.ADDON);

        //Assertion
        assertEquals(ticketingSaleData.get(0).getTicketSold(),ticketStats.getSoldCount(),0);
        assertEquals(ticketingSaleData.get(0).getTotalTickets(),ticketingTypes.get(0).getNumberOfTickets());
        assertEquals(ticketingSaleData.get(0).getTicketTypeName(),ticketingTypes.get(0).getTicketTypeName());
        assertEquals(ticketingSaleData.get(0).getTicketPrice(),ticketingTypes.get(0).getPrice(),0);
        assertEquals(ticketingSaleData.get(0).getEndDate(),getDateInLocal(ticketingType.getEndDate(), event.getEquivalentTimeZone(), Constants.DATE_FORMAT_WITH_AM_PM));
        assertEquals(ticketingSaleData.get(0).getStatus(),Constants.STRING_SOLD);

        verify(ticketingTypeService).getAllByEventIdAndRecurringId(event, recurringEventId, DataType.ADDON);
        verify(eventCommonRepoService).countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED);
    }

    @Test
    void test_getNumberOfTicketsAndRevenue() {

        //Setup
        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(Collections.emptyList());

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(commonEventService.getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any())).thenReturn(ticketingSaleData);
        when(eventCommonRepoService.findByEventIdJoinFetch(event,recurringEventId,true, null, null, null)).thenReturn(null);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        //Execution
        EventTicketRevenueDto eventTicketRevenueDto = ticketingStatisticsServiceImpl.getNumberOfTicketsAndRevenue(event,recurringEventId);

        //Assertion
        assertEquals(eventTicketRevenueDto.getNetSales(),0,0);
        assertEquals(eventTicketRevenueDto.getTotalSales(),0,0);
        assertEquals(eventTicketRevenueDto.getTotalTicketSold(),0,0);
        assertEquals(eventTicketRevenueDto.getTotalWLFees(),0,0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any());
        verify(eventCommonRepoService).findByEventIdJoinFetch(event,recurringEventId,true, null, null, null);
    }

    public static Object[] getOrderAEFee(){
        return new Object[]{
                new Object[]{0,null} ,
                new Object[]{0,EnumPaymentGateway.STRIPE.name()} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getOrderAEFee")
    void test_getNetSale(double orderAEFee,String paymentGateway) {
        //Setup
        stripeDTO.setPaymentGateway(paymentGateway);

        ticketingBuyerDataFromDB.setTotalTicketCount(1L);
        ticketingBuyerDataFromDB.setOrderAEFee(orderAEFee);

        List<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS =new ArrayList<>();
        ticketingBuyerDataFromDBS.add(ticketingBuyerDataFromDB);

        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(ticketingBuyerDataFromDBS);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(commonEventService.getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any())).thenReturn(ticketingSaleData);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        //Execution
        double netSale = ticketingStatisticsServiceImpl.getNetSale(event,recurringEventId,true);

        //Assertion
        assertEquals(netSale,ticketingBuyerDataFromDB.getOrderPaidAmount()-ticketingBuyerDataFromDB.getOrderAEFee(),0);
        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any());
    }

    @Test
    void test_getNetSale_orderGrossAmountZero() {
        //Setup
        ticketingBuyerDataFromDB.setTotalTicketCount(1L);
        ticketingBuyerDataFromDB.setOrderRefundedAmount(100L);
        ticketingBuyerDataFromDB.setOrderPaidAmount(100L);

        List<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS =new ArrayList<>();
        ticketingBuyerDataFromDBS.add(ticketingBuyerDataFromDB);

        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(ticketingBuyerDataFromDBS);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(commonEventService.getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any())).thenReturn(ticketingSaleData);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        //Execution
        double netSale = ticketingStatisticsServiceImpl.getNetSale(event,recurringEventId,true);

        //Assertion
        assertEquals(netSale,0,0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any());
    }

    public static Object[] getEventTickets(){
        EventTickets eventTicket = new EventTickets();
        eventTicket.setPaidAmount(100);
        List<EventTickets> eventTickets = new ArrayList<>();
        eventTickets.add(eventTicket);
        return new Object[]{
                new Object[]{Collections.emptyList(),0} ,
                new Object[]{eventTickets,eventTicket.getPaidAmount()} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getEventTickets")
    void test_getGrossSale(List<EventTickets> eventTickets,double grossSaleAmount) {
        //Mock
        when(eventCommonRepoService.findByEventIdJoinFetch(event,recurringEventId,true, null, null, null)).thenReturn(eventTickets);

        //Execution
        double grossSale = ticketingStatisticsServiceImpl.getGrossSale(event,recurringEventId,true);

        //Assertion
        assertEquals(grossSale,grossSaleAmount,0);
        verify(eventCommonRepoService).findByEventIdJoinFetch(event,recurringEventId,true, null, null, null);
    }

    public static Object[] getFromAndToDateAndRecurringEventId(){
        return new Object[]{
                new Object[]{null,null,1L} ,
                new Object[]{new Date(),null,-1L} ,
                new Object[]{null,new Date(),null} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getFromAndToDateAndRecurringEventId")
    void test_getGraphDetailsForTicketSoldBetweenDatesWithNetSales_orderGrossAmountGreaterThenZero(Date from,Date to,Long recurringEventId) {

        //Setup
        ticketingBuyerDataFromDB.setOrderDate(new Date());
        ticketingBuyerDataFromDB.setOrderPaidAmount(0);
        List<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS =new ArrayList<>();
        ticketingBuyerDataFromDBS.add(ticketingBuyerDataFromDB);

        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(ticketingBuyerDataFromDBS);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        when(commonEventService.getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any())).thenReturn(ticketingSaleData);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());

        //Execution
        List<TicketGraphDetail> ticketGraphDetails = ticketingStatisticsServiceImpl.getGraphDetailsForTicketSoldBetweenDatesWithNetSales(event, from, to, recurringEventId, DataType.TICKET);

        //Assertion
        assertEquals(ticketGraphDetails.get(0).getSaleDate().toString(), DateUtils.setHoursMinutesToZero(new Date()).toString());
        assertEquals(ticketGraphDetails.get(0).getNumberOfTicketSold(),ticketingBuyerDataFromDB.getCountOfIndividualTickets() + ticketingBuyerDataFromDB.getCountOfNonIndividualTickets());
        assertEquals(ticketGraphDetails.get(0).getTotalSale(),ticketingBuyerDataFromDB.getOrderPaidAmount(),0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerData(anyLong(),any(),anyBoolean(),anyLong(),anyBoolean(),anyBoolean(),any());
        verify(ticketingStatisticsServiceImpl,atMost(1)).getFee(any(),any(), anyList(), any(), anyList());
    }

    @Test
    void test_getGraphDetailsForTicketSoldBetweenDatesWithNetSales_orderGrossAmountZero() {

        //Setup
        ticketingBuyerDataFromDB.setOrderDate(new Date());
        ticketingBuyerDataFromDB.setOrderPaidAmount(0);
        List<TicketingBuyerDataFromDB> ticketingBuyerDataFromDBS =new ArrayList<>();
        ticketingBuyerDataFromDBS.add(ticketingBuyerDataFromDB);
        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(ticketingBuyerDataFromDBS);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(commonEventService.getBuyerDataBetweenDates(anyLong(),any(),anyBoolean(),any(),any(),anyLong(),any())).thenReturn(ticketingSaleData);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        //Execution
        List<TicketGraphDetail> ticketGraphDetails = ticketingStatisticsServiceImpl.getGraphDetailsForTicketSoldBetweenDatesWithNetSales(event, new Date(), new Date(), recurringEventId, DataType.TICKET);

        //Assertion
        assertEquals(ticketGraphDetails.get(0).getSaleDate().toString(), DateUtils.setHoursMinutesToZero(new Date()).toString());
        assertEquals(ticketGraphDetails.get(0).getNumberOfTicketSold(),ticketingBuyerDataFromDB.getCountOfIndividualTickets() + ticketingBuyerDataFromDB.getCountOfNonIndividualTickets());
        assertEquals(ticketGraphDetails.get(0).getTotalSale(),ticketingBuyerDataFromDB.getOrderPaidAmount(),0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerDataBetweenDates(anyLong(),any(),anyBoolean(),any(),any(),anyLong(),any());
    }

    @Test
    void test_getGraphDetailsForTicketSoldBetweenDatesWithNetSales_ticketGraphDetailsEmpty() {

        //Setup
        Page<TicketingBuyerDataFromDB> ticketingSaleData = new PageImpl<>(Collections.emptyList());

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(commonEventService.getBuyerDataBetweenDates(anyLong(),any(),anyBoolean(),any(),any(),anyLong(),any())).thenReturn(ticketingSaleData);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(Collections.emptyList());
        //Execution
        List<TicketGraphDetail> ticketGraphDetails = ticketingStatisticsServiceImpl.getGraphDetailsForTicketSoldBetweenDatesWithNetSales(event, new Date(), new Date(), recurringEventId, DataType.TICKET);

        //Assertion
        assertTrue(ticketGraphDetails.isEmpty());

        verify(stripeService).getStripeFeesByEvent(event);
        verify(commonEventService).getBuyerDataBetweenDates(anyLong(),any(),anyBoolean(),any(),any(),anyLong(),any());
    }

    public static Object[] getTicketingTypes(){
        return new Object[]{
                new Object[]{null} ,
                new Object[]{Collections.emptyList()} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketingTypes")
    void test_getTotalTicketsSoldCount_ticketingTypesEmpty(List<TicketingType> ticketingTypes) {
        //Mock
        when(ticketingTypeService.findAllByEventId(event, false)).thenReturn(ticketingTypes);

        //Execution
        int totalTicketsSold = ticketingStatisticsServiceImpl.getTotalTicketsSoldCount(event);

        //Assertion
        assertEquals(totalTicketsSold,0);
        verify(ticketingTypeService).findAllByEventId(event, false);
    }

    public static Object[] getCountOfeventTickets(){
        return new Object[]{
                new Object[]{null,0} ,
                new Object[]{1L,1} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getCountOfeventTickets")
    void test_getTotalTicketsSoldCount(Long countOfeventTickets,int ticketsSold) {

        //Setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //Mock
        when(ticketingTypeService.findAllByEventId(event, false)).thenReturn(ticketingTypes);
        when(eventCommonRepoService.findByTypeIdAndStatusIdNotIn(any(), anyList())).thenReturn(countOfeventTickets);

        //Execution
        int totalTicketsSold = ticketingStatisticsServiceImpl.getTotalTicketsSoldCount(event);

        //Assertion
        assertEquals(totalTicketsSold,ticketsSold);
        verify(ticketingTypeService).findAllByEventId(event, false);
        verify(eventCommonRepoService).findByTypeIdAndStatusIdNotIn(any(), anyList());
    }

    @Test
    void test_getTotalTicketsSoldCountByTicketType() {

        //Execution
        int totalTicketsSold = ticketingStatisticsServiceImpl.getTotalTicketsSoldCountByTicketType(Collections.emptyList());

        //Assertion
        assertEquals(totalTicketsSold,0);
    }

    /*@Test
    void test_getGraphForTicketSoldBetweenDates() {

        //Setup
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        List<TicketGraphDetail> eventTicketGraphDetail = new ArrayList<>();
        eventTicketGraphDetail.add(ticketGraphDetail);

        //Mock
        when(eventTicketsCommonRepo.getEventTicketGraphDetail(ticketingTypes, TicketStatus.REFUNDED)).thenReturn(eventTicketGraphDetail);

        //Execution
        ticketingStatisticsServiceImpl.getGraphForTicketSoldBetweenDates(event,new Date(),new Date(),recurringEventId);

        //Assertion
        verify(eventTicketsCommonRepo).getEventTicketGraphDetail(ticketingTypes, TicketStatus.REFUNDED);
    }*/

    public static Object[] getNumberOfTicketPerTable(){
        return new Object[]{
                new Object[]{0} ,
                new Object[]{1} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getNumberOfTicketPerTable")
    void test_getRemaningTicketByCountingSoldTicketOnly(int numberOfTicketPerTable) {
        //Setup
        ticketingType.setNumberOfTicketPerTable(numberOfTicketPerTable);

        ticketingType.setNumberOfTickets(1L);

        //Mock
        when(eventCommonRepoService.countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED)).thenReturn(null);

        //Execution
        TicketStats stats = ticketingStatisticsServiceImpl.getRemaningTicketByCountingSoldTicketOnly(ticketingType);

        //Assertion
        assertEquals(stats.getSoldCount(),0);
        assertEquals(stats.getReamingCount(),1);
        assertEquals(stats.getGrossSale(),0,0);

        verify(eventCommonRepoService).countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED);
    }

    public static Object[] getTicketCount(){
        return new Object[]{
                new Object[]{BigInteger.valueOf(0L), TicketBundleType.INDIVIDUAL_TICKET} ,
                new Object[]{null,TicketBundleType.TABLE} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketCount")
    void test_getRemainingTicketCount(BigInteger count,TicketBundleType ticketBundleType) {

        //Setup
        ticketStats.setSoldCount(1L);
        ticketingType.setBundleType(ticketBundleType);
        ticketingType.setNumberOfTicketPerTable(1);
        ticketingType.setNumberOfTickets(1L);

        //Mock
        when(eventCommonRepoService.countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED)).thenReturn(ticketStats);
        when(ticketingOrderManagerService.getCreateNotExpiredTicket(any(),any())).thenReturn(count);

        //Execution
        long remainingTicketCount = ticketingStatisticsServiceImpl.getRemainingTicketCount(ticketingType);

        //Assertion
        assertEquals(remainingTicketCount,0L,0);
        verify(eventCommonRepoService).countByTypeIdAndStatusIdNotInAndStatusNot(ticketingType, TicketStatus.CANCELED);
        verify(ticketingOrderManagerService).getCreateNotExpiredTicket(any(),any());
    }

    @Test
    void test_getEventTicketingRevenue() {

        //Setup
        EventStats eventStats = new EventStats();
        eventStats.setGrossSale(-1d);
        eventStats.setSoldCount(1L);
        eventStats.setWlFee(5d);

        //Mock
        when(eventTicketsRepoService.countByEventIdAndTicketStatusIdNotInAndStatusNot(event, TicketStatus.CANCELED)).thenReturn(eventStats);

        //Execution
        EventTicketRevenueDto eventTicketRevenueDto = ticketingStatisticsServiceImpl.getEventTicketingRevenue(event);

        //Assertion
        assertEquals(eventTicketRevenueDto.getTotalWLFees(),eventStats.getWlFee(),0);
        assertEquals(eventTicketRevenueDto.getTotalTicketSold(),eventStats.getSoldCount(),0);
        assertEquals(eventTicketRevenueDto.getTotalSales(),eventStats.getGrossSale(),0);
        verify(eventTicketsRepoService).countByEventIdAndTicketStatusIdNotInAndStatusNot(event, TicketStatus.CANCELED);
    }

    @Test
    void test_getTotalTicketsForDiscount_AndTotalTicketPrice_ticketingCouponNull() {
        //Setup

        //Execution
        int TotalTicketsForDiscount = ticketingStatisticsServiceImpl.getTotalTicketsForDiscountAndTotalTicketPrice(Collections.emptyList(),null).getTotalTickets();

        //Assertion
        assertEquals(TotalTicketsForDiscount,0);
    }

    public static Object[] getEventTicketTypeId(){
        return new Object[]{
                new Object[]{Constants.STRING_EMPTY} ,
                new Object[]{"2"} ,
                new Object[]{"1"} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getEventTicketTypeId")
    void test_getTotalTicketsForDiscount_AndTotalTicketPrice_ticketingCoupon(String eventTicketTypeId) {
        //Setup

        ticketingCoupon.setEventTicketTypeId(eventTicketTypeId);
        ticketingType.setId(id);
        ticketingOrderManager.setTicketType(ticketingType);
        List<TicketingOrderManager> ticketingOrderManagers = new ArrayList<>();
        ticketingOrderManagers.add(ticketingOrderManager);

        //Execution
        int TotalTicketsForDiscount = ticketingStatisticsServiceImpl.getTotalTicketsForDiscountAndTotalTicketPrice(ticketingOrderManagers,ticketingCoupon).getTotalTickets();

        //Assertion
        assertEquals(TotalTicketsForDiscount,0);
    }

    public static Object[] getCreatedForm(){
        return new Object[]{
                new Object[]{null} ,
                new Object[]{1L} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getCreatedForm")
    void test_getGraphForNumberOfTicketSoldByTicketType_ticketingTypes(Long createdForm) {
        //Setup
        ticketingType.setCreatedFrom(createdForm);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);

        //Mock
        when(ticketingTypeService.getAllByEventIdAndRecurringId(event, recurringEventId, DataType.TICKET)).thenReturn(ticketingTypes);

        //Execution
        List<TicketingTypeGraphDetail> ticketingTypeGraphDetails = ticketingStatisticsServiceImpl.getGraphForNumberOfTicketSoldByTicketType(event,recurringEventId,DataType.TICKET);

        //Assertion
        assertEquals(ticketingTypeGraphDetails.get(0).getTotalTickets(),0,0);
        assertEquals(ticketingTypeGraphDetails.get(0).getRemainingTickets(),0,0);
        assertEquals(ticketingTypeGraphDetails.get(0).getGrossSale(),0,0);

        verify(ticketingTypeService).getAllByEventIdAndRecurringId(event, recurringEventId, DataType.TICKET);
    }

    public static Object[] getTicketingTypeId(){
        return new Object[]{
                new Object[]{0L} ,
                new Object[]{1L} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketingTypeId")
    void test_getGraphForNumberOfTicketSoldByTicketType_ticketingTypesAndRecurringEventsIdZero(Long id) {
        //Setup
        ticketingType.setId(id);
        ticketingType.setCreatedFrom(null);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        TicketingType ticketingType1 = new TicketingType();
        ticketingType1.setCreatedFrom(1L);
        ticketingType1.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        List<TicketingType> ticketingTypes = new ArrayList<>();
        ticketingTypes.add(ticketingType);
        ticketingTypes.add(ticketingType1);

        //Mock
        when(ticketingTypeService.getAllByEventIdAndRecurringId(any(),anyLong(),any())).thenReturn(ticketingTypes);

        //Execution
        List<TicketingTypeGraphDetail> ticketingTypeGraphDetails = ticketingStatisticsServiceImpl.getGraphForNumberOfTicketSoldByTicketType(event,0L,DataType.TICKET);

        //Assertion
        assertEquals(ticketingTypeGraphDetails.get(0).getTotalTickets(),0,0);
        assertEquals(ticketingTypeGraphDetails.get(0).getRemainingTickets(),0,0);
        assertEquals(ticketingTypeGraphDetails.get(0).getGrossSale(),0,0);

        verify(ticketingTypeService).getAllByEventIdAndRecurringId(any(),anyLong(),any());
    }

    @Test
    void test_getTicketSellWrapper() {
        //Mock
        doReturn(Collections.emptyList()).when(ticketingStatisticsServiceImpl).getTicketSellData(any(),anyLong(),any());
        doReturn(Collections.emptyList()).when(ticketingStatisticsServiceImpl).getGraphForTicketSoldBetweenDates(any(),any(),any(),anyLong());
        doReturn(Collections.emptyList()).when(ticketingStatisticsServiceImpl).getGraphForNumberOfTicketSoldByTicketType(any(),anyLong(),any());

        //Execution
        TicketingSaleWrapper TicketingSaleWrapper = ticketingStatisticsServiceImpl.getTicketSellWrapper(event,recurringEventId,DataType.TICKET);

        //Assertion
        assertTrue(TicketingSaleWrapper.getGraphDetails().isEmpty());
        assertTrue(TicketingSaleWrapper.getTicketingTypeGraph().isEmpty());
        assertTrue(TicketingSaleWrapper.getTicketingSales().isEmpty());

        verify(ticketingStatisticsServiceImpl).getTicketSellData(any(),anyLong(),any());
        verify(ticketingStatisticsServiceImpl).getGraphForTicketSoldBetweenDates(any(),any(),any(),anyLong());
        verify(ticketingStatisticsServiceImpl).getGraphForNumberOfTicketSoldByTicketType(any(),anyLong(),any());
    }

    @Test
    void test_getRecurringEventGrossSale_pastEventTrue() {
        //Setup
        Page<RecurringEvents> recurringEvents = new PageImpl<>(Collections.emptyList());

        //Mock
        when(recurringEventsScheduleService.getPastRecurringEvents(event, 0, 10)).thenReturn(recurringEvents);

        //Execution
        DataTableResponse dataTableResponse = ticketingStatisticsServiceImpl.getRecurringEventGrossSale(event,0,10,true);

        //Assertion
        assertTrue(dataTableResponse.getData().isEmpty());
        assertEquals(dataTableResponse.getRecordsFiltered(),0);
        assertEquals(dataTableResponse.getRecordsTotal(),0);
        verify(recurringEventsScheduleService).getPastRecurringEvents(event, 0, 10);
    }

    //TODO: Fix test case after spring-boot-upgrade
    /*@Test
    void test_getRecurringEventGrossSale_pastEventFalse() {
        //Setup
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvent);
        Page<RecurringEvents> recurringEvents = new PageImpl<>(recurringEventsList);

        List<Object[]> reccuringEventGrossSale = new ArrayList<>();
        reccuringEventGrossSale.add(0,new Object[]{BigInteger.valueOf(0L),1d,BigInteger.valueOf(1L), BigDecimal.valueOf(1L),BigDecimal.valueOf(1L)});

        //Mock
        when(recurringEventsScheduleService.getUpcomingRecurringEvents(event, 0, 10)).thenReturn(recurringEvents);
        when(eventTicketsRepoService.getRecurringEventGrossSaleByIds(anySet())).thenReturn(reccuringEventGrossSale);

        //Execution
        DataTableResponse dataTableResponse = ticketingStatisticsServiceImpl.getRecurringEventGrossSale(event,0,10,false);

        //Assertion
        assertEquals(((RecurringEventGrossSaleDto) dataTableResponse.getData().get(0)).getGrossRevenue(),1,0);
        assertEquals(((RecurringEventGrossSaleDto) dataTableResponse.getData().get(0)).getRecurringEventId(),recurringEvent.getId(),0);
        assertEquals(((RecurringEventGrossSaleDto) dataTableResponse.getData().get(0)).getTicketsSold(),2,0);
        assertEquals(((RecurringEventGrossSaleDto) dataTableResponse.getData().get(0)).getTotalTickets(),1,0);
        assertEquals(0, dataTableResponse.getRecordsFiltered());
        assertEquals(1, dataTableResponse.getRecordsTotal());

        verify(recurringEventsScheduleService).getUpcomingRecurringEvents(event, 0, 10);
        verify(eventTicketsRepoService).getRecurringEventGrossSaleByIds(anySet());
    }*/

    @Test
    void test_getAvailableTicketsForTicketType_ticketingTypeNull() {
        //Mock
        when(ticketingTypeService.findByidAndEvent(1L, event)).thenReturn(null);

        //Execution
        int availableTicketsForTicketType = ticketingStatisticsServiceImpl.getAvailableTicketsForTicketType(event,1L);

        //Assertion
        assertEquals(availableTicketsForTicketType,0);
        verify(ticketingTypeService).findByidAndEvent(1L, event);
    }

    @Test
    void test_getAvailableTicketsForTicketType_success() {
        //Setup
        ticketingType.setNumberOfTickets(1L);

        //Mock
        when(ticketingTypeService.findByidAndEvent(1L, event)).thenReturn(ticketingType);
        when(ticketingOrderManagerService.getCreateNotExpiredTicket(any(),any())).thenReturn(null);
        when(eventCommonRepoService.findByTypeIdAndStatusIdNotIn(any(), anyList())).thenReturn(null);

        //Execution
        int availableTicketsForTicketType = ticketingStatisticsServiceImpl.getAvailableTicketsForTicketType(event,1L);

        //Assertion
        assertEquals(availableTicketsForTicketType,1);
        verify(ticketingTypeService).findByidAndEvent(1L, event);
        verify(ticketingOrderManagerService).getCreateNotExpiredTicket(any(),any());
        verify(eventCommonRepoService).findByTypeIdAndStatusIdNotIn(any(), anyList());
    }

    @Test
    void test_getAvailableTicketsForFullEvent() {
        //Mock
        when(ticketingTypeService.findAllByEventId(event, false)).thenReturn(Collections.emptyList());

        //Execution
        int availableTicketsForFullEvent = ticketingStatisticsServiceImpl.getAvailableTicketsForFullEvent(event);

        //Assertion
        assertEquals(availableTicketsForFullEvent,0);
        verify(ticketingTypeService).findAllByEventId(event, false);
    }

    @Test
    void test_getTicketingSalesDataForDashboard() {
        //Mock
        when(eventCommonRepoService.getSalesDataForDashboard(1, recurringEventId, 10d , 10d,EnumPaymentGateway.SQUARE.value(), List.of(DataType.TICKET.name()))).thenReturn(Collections.emptyList());

        //Execution
        List<TicketingHomeDto> ticketingHomeDtos = ticketingStatisticsServiceImpl.getTicketingSalesDataForDashboard(1L,recurringEventId,10d,10d,EnumPaymentGateway.SQUARE.value(),Arrays.asList(DataType.TICKET.name()));

        //Assertion
        assertTrue(ticketingHomeDtos.isEmpty());
        verify(eventCommonRepoService).getSalesDataForDashboard(1, recurringEventId, 10d , 10d,EnumPaymentGateway.SQUARE.value(), List.of(DataType.TICKET.name()));
    }

    public static Object[] getEventIds(){
        return new Object[]{
                new Object[]{null} ,
                new Object[]{new HashSet<>()} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getEventIds")
    void test_getTicketsSoldCount_eventIdNullAndEmpty(Set<Long> eventIds) {

        //Execution
        List<Object[]> ticketsSoldCount = ticketingStatisticsServiceImpl.getTicketsSoldCount(eventIds);

        //Assertion
        assertTrue(ticketsSoldCount.isEmpty());
    }

    @Test
    void test_getTicketsSoldCount_success() {
        //Setup
        Set<Long> eventIds = new HashSet<>();
        eventIds.add(1L);

        //Mock
        when(eventTicketsRepository.getTicketSoldCount(eventIds,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED))).thenReturn(Collections.emptyList());

        //Execution
        List<Object[]> ticketsSoldCount = ticketingStatisticsServiceImpl.getTicketsSoldCount(eventIds);

        //Assertion
        assertTrue(ticketsSoldCount.isEmpty());
        verify(eventTicketsRepository).getTicketSoldCount(eventIds,  Arrays.asList( TicketStatus.CANCELED,TicketStatus.DELETED));
    }

    @Test
    void test_getHostEventTicketDetail_ticketingNull() {

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(null);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(null);

        //Execution
        HostEventTicketDetail hostEventTicketDetail = ticketingStatisticsServiceImpl.getHostEventTicketDetail(event,recurringEventId,Arrays.asList(DataType.TICKET.name()),true);

        //Assertion
        assertEquals(hostEventTicketDetail,null);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
    }

    @Test
    void test_getHostEventTicketDetail_success() {

        //Setup
        event.setTicketingId(id);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        doReturn(Collections.emptyList()).when(ticketingStatisticsServiceImpl).getTicketingSalesDataForDashboard(anyLong(),anyLong(),anyDouble(),anyDouble(),anyString(),any());

        //Execution
        HostEventTicketDetail hostEventTicketDetail = ticketingStatisticsServiceImpl.getHostEventTicketDetail(event,recurringEventId, List.of(DataType.TICKET.name()),true);

        //Assertion
        assertFalse(hostEventTicketDetail.isActive());
        assertEquals(hostEventTicketDetail.getCollectedAmout(),0,0);
        assertEquals(hostEventTicketDetail.getNumberOfTicketSold(),0,0);
        assertNull(hostEventTicketDetail.getStartDate());
        assertNull(hostEventTicketDetail.getEndDate());
        assertEquals(hostEventTicketDetail.getTotalPaidTickets(),0,0);
        assertEquals(hostEventTicketDetail.getTotalFreeTickets(),0,0);
        assertEquals(hostEventTicketDetail.getTotalFreeTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalPaidTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalDonationTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalTickets(),0,0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
        verify(ticketingStatisticsServiceImpl).getTicketingSalesDataForDashboard(anyLong(),anyLong(),anyDouble(),anyDouble(),anyString(),any());
    }

    @Test
    void test_getHostEventTicketDetail_WithNetSales_success() {

        //Setup
        event.setTicketingId(id);

        //Mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);
        doReturn(Collections.emptyList()).when(ticketingStatisticsServiceImpl).getTicketingSalesDataForDashboard(anyLong(),anyLong(),anyDouble(),anyDouble(),anyString(),any());

        //Execution
        HostEventTicketDetail hostEventTicketDetail = ticketingStatisticsServiceImpl.getHostEventTicketDetail(event,recurringEventId, List.of(DataType.TICKET.name()),true);

        //Assertion
        assertFalse(hostEventTicketDetail.isActive());
        assertEquals(hostEventTicketDetail.getCollectedAmout(),0.0,0.0);
        assertEquals(hostEventTicketDetail.getNumberOfTicketSold(),0,0);
        assertNull(hostEventTicketDetail.getStartDate());
        assertNull(hostEventTicketDetail.getEndDate());
        assertEquals(hostEventTicketDetail.getTotalPaidTickets(),0,0);
        assertEquals(hostEventTicketDetail.getTotalFreeTickets(),0,0);
        assertEquals(hostEventTicketDetail.getTotalFreeTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalPaidTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalDonationTicketsSold(),0,0);
        assertEquals(hostEventTicketDetail.getTotalTickets(),0,0);

        verify(stripeService).getStripeFeesByEvent(event);
        verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
        verify(ticketingStatisticsServiceImpl).getTicketingSalesDataForDashboard(anyLong(),anyLong(),anyDouble(),anyDouble(),anyString(),any());
    }
}