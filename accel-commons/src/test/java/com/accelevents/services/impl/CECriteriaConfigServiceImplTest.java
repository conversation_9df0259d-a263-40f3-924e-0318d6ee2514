package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.billing.chargebee.service.impl.ChargeBeePaymentHandler;
import com.accelevents.common.dto.ChallengeConfigMasterDTO;
import com.accelevents.common.dto.RedisChallengeAreaDTO;
import com.accelevents.domain.ChallengeConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventCECriteria;
import com.accelevents.domain.TicketingType;
import com.accelevents.dto.EventCECriteriaDTO;
import com.accelevents.repositories.ChallengeConfigRepository;
import com.accelevents.services.ChallengeConfigService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.services.repo.helper.EventCECriteriaRepoService;
import com.accelevents.services.repo.helper.VirtualEventSettingsRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackService;
import com.accelevents.utils.Constants;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.accelevents.billing.chargebee.enums.ChargebeeEntitlements.CE_CREDIT_TRACKING;
import static com.accelevents.continuing.ed.ContinuingEdConstants.*;
import static com.accelevents.domain.enums.DataType.TICKET;
import static com.accelevents.enums.PlanConfigNames.ENTERPRISE;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.AREA;
import static com.accelevents.services.elasticsearch.leaderboard.LeaderBoardConstant.ChallengeConstants;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class) 
class CECriteriaConfigServiceImplTest {

    @Spy
    @InjectMocks
    private EventCECriteriaServiceImpl eventCECriteriaServiceImpl;

    @Mock
    private ChallengeConfigService challengeConfigService;
    @Mock
    private EventCECriteriaRepoService eventCECriteriaRepoService;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private ChargeBeePaymentHandler chargeBeePaymentHandler;
    @Mock
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Mock
    private ChallengeConfigRepository challengeConfigRepository;
    @Mock
    private TicketingTypeTagAndTrackService ticketingTypeTagAndTrackService;
    @Mock
    private SessionService sessionService;

    private static String challengeName = "Test Criteria";
    private EventCECriteriaDTO ceCriteriaDTO;
    private EventCECriteria eventCECriteria;
    private static final Event event = EventDataUtil.getEvent();
    private static List<TicketingType> ticketingTypeList;
    private final List<Map<String, Object>> actionMapListForSurvey = EventDataUtil.getActionListForContinueEdSurvey();
    private Map<String, List<Long>> triggerMapForSurvey;
    private static ChallengeConfigMasterDTO masterChallengeDTO;
    private static ChallengeConfiguration challengeConfiguration;

    @BeforeAll
    static void setupBeforeAll() {
        challengeConfiguration = new ChallengeConfiguration();
        challengeConfiguration.setId(3);
        challengeConfiguration.setArea(AREA.SESSIONS);
        challengeConfiguration.setJsonValue("{\"action\":[{\"name\":\"visit\",\"label\":\"Visit\",\"config\":[{\"element\":\"point\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points per Session Joined\",\"minValue\":0},{\"element\":\"minVisitDuration\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Required Attendance Duration (In Seconds)\"},{\"element\":\"perVisitPoint\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Consolation Points for Joining less than the Required Duration\"}]},{\"name\":\"watch\",\"label\":\"Watch\",\"config\":[{\"element\":\"point\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points per Live Stream Watched\"},{\"element\":\"minimumWatchDuration\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Minimum Watch Duration (In Seconds)\"},{\"element\":\"recordingWatchPoint\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points for Watching a Recording\"},{\"element\":\"minimumRecordingWatchDuration\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Required Watch Time (In Seconds)\"}]},{\"name\":\"askQuestion\",\"label\":\"Ask Question\",\"config\":[{\"element\":\"point\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points for submitting an Ask Question\"}]},{\"name\":\"chat\",\"label\":\"Chat\",\"config\":[{\"element\":\"point\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points for submitting a Chat Message\"}]},{\"name\":\"poll\",\"label\":\"Poll\",\"config\":[{\"element\":\"point\",\"elementType\":\"text\",\"valueType\":\"numeric\",\"label\":\"Points for submitting a Poll\"}]}],\"trigger\":[{\"element\":\"main_stage\",\"elementType\":\"multiSelectBox\",\"label\":\"Main Stage Session\"},{\"element\":\"breakout_session\",\"elementType\":\"multiSelectBox\",\"label\":\"Regular Session\"},{\"element\":\"workshop\",\"elementType\":\"multiSelectBox\",\"label\":\"Workshop\"}]}");

        masterChallengeDTO = new ChallengeConfigMasterDTO(challengeConfiguration);

        TicketingType ticketingType =  EventDataUtil.getTicketingType(event);
        ticketingTypeList = List.of(ticketingType);
    }

    @BeforeEach
    void setUp() {
        ceCriteriaDTO = EventDataUtil.getSurveyEventCECriteriaDTO();

        triggerMapForSurvey = new HashMap<>();
        triggerMapForSurvey.put(RedisChallengeAreaDTO.Sessions.breakout_session.name(), List.of(1L));

        eventCECriteria = ceCriteriaDTO.createEntity(event);
        eventCECriteria.setId(1L);
    }

    void mockMethodForSurveyChallenges() {
        when(eventPlanConfigService.getEventPlanName(event.getEventId())).thenReturn(ENTERPRISE.getName());
        when(challengeConfigService.findJsonValueByArea(anyString())).thenReturn(masterChallengeDTO);
        when(eventCECriteriaRepoService.existsByEventIdAndCriteriaNameIgnoreCase(event.getEventId(), ceCriteriaDTO.getName())).thenReturn(Boolean.FALSE);
        when(ticketingTypeService.findAllByEventIdHost(event, false, TICKET)).thenReturn(ticketingTypeList);
        when(chargeBeePaymentHandler.isEntitlementsAvailable(event, CE_CREDIT_TRACKING)).thenReturn(Boolean.TRUE);
    }


    @Test
    void testCreateEventChallengeOrThrowErrorWithContinueEdAndActionIsNotSubmit() throws JSONException {

        //setup
        ceCriteriaDTO.getAction().get(0).put(Constants.NAME_SMALL, Constants.VISIT);

        // Mock
        this.mockMethodForSurveyChallenges();
        when(ticketingTypeTagAndTrackService.findTagAndTrackByTicketingTypeIds(anyList())).thenReturn(Collections.emptyList());
        when(sessionService.getAllSessionByIds(any(), any())).thenReturn(Collections.emptyList());
        when(sessionService.findSessionsByTagOrTrackIds(any())).thenReturn(Collections.emptyList());

        //Execution
        EventCECriteriaDTO expectedEventChallengeDto = eventCECriteriaServiceImpl.createEventCECriteriaOrThrowError(event, ceCriteriaDTO);

        assertNull(expectedEventChallengeDto.getTriggerErrors());
        assertNull(expectedEventChallengeDto.getInvalidTriggers());
        assertNull(expectedEventChallengeDto.getActionErrors());
        assertNotNull(expectedEventChallengeDto.getInvalidActions());
        assertEquals(Constants.VISIT, expectedEventChallengeDto.getInvalidActions().get(0).get(ChallengeConstants.ELEMENT));
        assertEquals(expectedEventChallengeDto.getInvalidActions().get(0).get(Constants.ERROR), String.format(Constants.INVALID_ACTION_MESSAGE_SESSION, Constants.VISIT));
    }

    @Test
    void testCreateEventChallengeOrThrowErrorWithContinueEdAndActionIsSubmitWithInValidParameter() throws JSONException {

        //setup
        Map<String, Object> actionMap = actionMapListForSurvey.get(0);
        actionMap.remove(REQUIRED_QUIZ_SCORE);
        actionMap.put(REQUIRED_WATCH_TIME, 50);
        actionMap.put(ChallengeConstants.POINT, -10);
        ceCriteriaDTO.setAction(actionMapListForSurvey);

        // Mock
        this.mockMethodForSurveyChallenges();
        when(ticketingTypeTagAndTrackService.findTagAndTrackByTicketingTypeIds(anyList())).thenReturn(Collections.emptyList());
        when(sessionService.getAllSessionByIds(any(), any())).thenReturn(Collections.emptyList());
        when(sessionService.findSessionsByTagOrTrackIds(any())).thenReturn(Collections.emptyList());

        //Execution
        EventCECriteriaDTO expectedEventChallengeDto = eventCECriteriaServiceImpl.createEventCECriteriaOrThrowError(event, ceCriteriaDTO);

        List<Map<String, String>> actionErrorList = expectedEventChallengeDto.getActionErrors().get(SURVEY_SUBMISSION);
        assertNull(expectedEventChallengeDto.getTriggerErrors());
        assertNull(expectedEventChallengeDto.getInvalidTriggers());
        assertNull(expectedEventChallengeDto.getInvalidActions());
        assertNotNull(expectedEventChallengeDto.getActionErrors());
        assertEquals(REQUIRED_QUIZ_SCORE, actionErrorList.get(0).get(ChallengeConstants.ELEMENT));
        assertEquals(actionErrorList.get(0).get(Constants.ERROR), REQUIRED_QUIZ_SCORE_LABEL + Constants.SHOULD_NOT_BE_LESS_THAN + 0 + Constants.NOT_GREATER_THAN + 100);
        assertEquals(ChallengeConstants.POINT, actionErrorList.get(1).get(ChallengeConstants.ELEMENT));
        assertEquals(actionErrorList.get(1).get(Constants.ERROR), CE_CREDIT_LABEL + Constants.SHOULD_NOT_BE_LESS_THAN + 0);
    }

    @Test
    void testCreateEventChallengeOrThrowErrorWithContinueEdAndActionIsSubmitWithInvalidTrigger() throws JSONException {

        //setup
        String triggerName = "survey";
        List<Long> triggerId = List.of(1L);
        triggerMapForSurvey.put(triggerName, triggerId);
        triggerMapForSurvey.put(TRACK, triggerId);
        ceCriteriaDTO.setTrigger(triggerMapForSurvey);

        // Mock
        this.mockMethodForSurveyChallenges();

        //Execution
        EventCECriteriaDTO expectedEventChallengeDto = eventCECriteriaServiceImpl.createEventCECriteriaOrThrowError(event, ceCriteriaDTO);

        List<Map<String, String>> invalidTriggerList = expectedEventChallengeDto.getInvalidTriggers();
        List<Map<String, String>> triggerErrorList = expectedEventChallengeDto.getTriggerErrors();

        assertNull(expectedEventChallengeDto.getInvalidActions());
        assertNull(expectedEventChallengeDto.getActionErrors());
        assertNotNull(expectedEventChallengeDto.getInvalidTriggers());
        assertNotNull(expectedEventChallengeDto.getTriggerErrors());
        assertEquals(TRACK, triggerErrorList.get(0).get(ChallengeConstants.ELEMENT));
        assertEquals("You can not add Track and Sessions both together in triggers", triggerErrorList.get(0).get(Constants.ERROR));
        assertEquals(triggerName, invalidTriggerList.get(0).get(ChallengeConstants.ELEMENT));
        assertEquals(invalidTriggerList.get(0).get(Constants.ERROR), Constants.INVALID_TRIGGER + triggerName + Constants.OF_SESSION_AREA);
    }

    @Test
    void testCreateEventChallengeOrThrowErrorWithContinueEdAndActionIsSubmitAndTriggerWithEmptyList() throws JSONException {

        //setup
        triggerMapForSurvey.put(Constants.WORKSHOP.toLowerCase(), Collections.emptyList());
        ceCriteriaDTO.setTrigger(triggerMapForSurvey);

        // Mock
        this.mockMethodForSurveyChallenges();

        //Execution
        EventCECriteriaDTO expectedEventChallengeDto = eventCECriteriaServiceImpl.createEventCECriteriaOrThrowError(event, ceCriteriaDTO);

        List<Map<String, String>> triggerErrorList = expectedEventChallengeDto.getTriggerErrors();
        assertNull(expectedEventChallengeDto.getInvalidActions());
        assertNull(expectedEventChallengeDto.getActionErrors());
        assertNull(expectedEventChallengeDto.getInvalidTriggers());
        assertNotNull(expectedEventChallengeDto.getTriggerErrors());
        assertEquals(Constants.WORKSHOP.toLowerCase(), triggerErrorList.get(0).get(ChallengeConstants.ELEMENT));
        assertEquals(triggerErrorList.get(0).get(Constants.ERROR), Constants.YOU_CAN_NOT_ADD_EMPTY + Constants.WORKSHOP + Constants.LIST);
    }

    @Test
    void testCreateEventChallengeOrThrowErrorWithContinueEdAndActionIsSubmitWithValidTrigger() throws JSONException {

        //setup
        String jsonValueForChallengeDetail = ceCriteriaDTO.convertToJSON();

        // Mock
        this.mockMethodForSurveyChallenges();
        when(eventCECriteriaRepoService.save(any())).thenReturn(eventCECriteria);

        //Execution
        EventCECriteriaDTO expectedEventChallengeDto = eventCECriteriaServiceImpl.createEventCECriteriaOrThrowError(event, ceCriteriaDTO);

        ArgumentCaptor<EventCECriteria> eventChallengeArgumentCaptor = ArgumentCaptor.forClass(EventCECriteria.class);
        verify(eventCECriteriaRepoService, times(1)).save(eventChallengeArgumentCaptor.capture());
        EventCECriteria eventChallengeArgumentCaptorValue = eventChallengeArgumentCaptor.getValue();

        assertEquals(eventChallengeArgumentCaptorValue.getCriteriaName(), challengeName);
        assertEquals(eventChallengeArgumentCaptorValue.getCriteriaConfig(), jsonValueForChallengeDetail);

        assertEquals(1L, expectedEventChallengeDto.getCriteriaId());
        assertEquals(expectedEventChallengeDto.getName(), challengeName);
        assertEquals(AREA.SESSIONS, expectedEventChallengeDto.getArea());
        assertEquals(expectedEventChallengeDto.getAction(), actionMapListForSurvey);
        assertEquals(expectedEventChallengeDto.getTrigger(), triggerMapForSurvey);

    }
}
