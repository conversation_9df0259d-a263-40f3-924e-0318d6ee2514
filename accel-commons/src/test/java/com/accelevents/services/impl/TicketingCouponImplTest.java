package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.Ticketing;
import com.accelevents.domain.TicketingCoupon;
import com.accelevents.repositories.TicketingCouponRepository;
import com.accelevents.repositories.TicketingRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StopWatch;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingCouponImplTest {

	@Spy
	@InjectMocks
	private TicketingCouponServiceImpl ticketingCouponService;

	@Mock
	private TicketingRepository ticketingRepository;
	@Mock
	private TicketingCouponRepository ticketingCouponRepository;

	private Event event;
	private Ticketing ticketing;

	@Test
	void test_getByEventIdAndCouponCode(){

		when(ticketingCouponRepository.findByEventIdAndNameAndRecurringEventId(1L, "couponCode", 1L)).thenReturn(new TicketingCoupon());
		TicketingCoupon expected = ticketingCouponService.getByEventIdAndCouponCode(1L,"couponCode",1L);
		assertNotNull(expected);

	}

	/*@BeforeEach
	void setUp() {
		// Prepare data
		event = new Event("TestEvent", true, true, true, true, true);
		event.setEventId(1L);
		event.setCurrency(Currency.AUD);

		ticketing = new Ticketing();
		ticketing.setId(1L);
		ticketing.setActivated(true);
		ticketing.setEventid(event);
		ticketing.setEventAddress("testAddress");
		ticketing.setHideRemainingTickets(false);
		ticketing.setEventStartDate(new Date());
		ticketing.setEventEndDate(new Date());
		ticketing.setChartKey("chart_key");
	}


	@Test
	void saveDiscountCodesForTicketingForApplyHiddenTicketTrue(){
		//Setup
		TicketingCouponDto ticketingCouponDto = getTicketingCouponDto(true);
		when(ticketingCouponRepository.findByEventIdAndName(event.getEventId(),event.getName())).thenReturn(new TicketingCoupon());

		// Execute
		ticketingCouponService.save(ticketingCouponDto,event);

		//Verify
		verify(ticketingCouponRepository, times(1)).save(any(TicketingCoupon.class));
		ArgumentCaptor<TicketingCoupon> argument = ArgumentCaptor.forClass(TicketingCoupon.class);
		verify(ticketingCouponRepository).save(argument.capture());
		assertEquals(true, argument.getValue().getApplyToHiddenTickets());
	}


	@Test
	void saveDiscountCodesForTicketingForApplyHiddenTicketFalse(){
		//Setup
		TicketingCouponDto ticketingCouponDto = getTicketingCouponDto(false);
		when(ticketingCouponRepository.findByEventIdAndName(event.getEventId(),event.getName())).thenReturn(new TicketingCoupon());

		// Execute
		ticketingCouponService.save(ticketingCouponDto,event);

		//Verify
		verify(ticketingCouponRepository, times(1)).save(any(TicketingCoupon.class));
		ArgumentCaptor<TicketingCoupon> argument = ArgumentCaptor.forClass(TicketingCoupon.class);
		verify(ticketingCouponRepository).save(argument.capture());
		assertEquals(false, argument.getValue().getApplyToHiddenTickets());
	}

	private TicketingCouponDto getTicketingCouponDto(boolean isApplyToHiddenTickets) {
		TicketingCouponDto ticketingCouponDto = new TicketingCouponDto();
		ticketingCouponDto.setCouponCode("AOC");
		ticketingCouponDto.setEventTicketTypeId("12");
		ticketingCouponDto.setUses(1L);
		ticketingCouponDto.setAmount(new Double(100));
		ticketingCouponDto.setDiscountType(DiscountType.PERCENTAGE);
		ticketingCouponDto.setUsesPerUser(1L);
		ticketingCouponDto.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);
		ticketingCouponDto.setApplicableToHiddenCoupon(isApplyToHiddenTickets);
		ticketingCouponDto.setCouponStartDate(DateUtils.getDateString(new Date(), DATE_FORMAT));
		ticketingCouponDto.setCouponEndDate(DateUtils.getDateString(new Date(), DATE_FORMAT));
		return ticketingCouponDto;
	}*/

	@Test
    void test123(){
        Instant instant = Instant.parse("2022-05-19T12:50:01.00Z");

        System.out.println("Instant before"
                + " truncate: "
                + instant);

        Instant returnvalue
                = instant.truncatedTo(ChronoUnit.MINUTES);
        Instant returnvalueM
                = instant.truncatedTo(ChronoUnit.SECONDS);

        // print result
        System.out.println("Instant after hours "
                + " truncate: "
                + returnvalue);

        System.out.println("Instant after min  "
                + " truncate: "
                + returnvalueM);
        System.out.println(" HOUR - MIN "+ (returnvalueM.toEpochMilli() - returnvalue.toEpochMilli()) +" 123");

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        stopWatch.stop();
        System.out.println("time log--> "+stopWatch.getTotalTimeMillis());


    }
}
