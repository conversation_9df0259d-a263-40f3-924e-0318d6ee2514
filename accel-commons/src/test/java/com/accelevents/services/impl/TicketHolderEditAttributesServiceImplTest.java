package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.dto.RegistrationConfigDto;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.AttributeValueType;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.DataType;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TicketHolderAttributesHelper;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.services.zapier.ZapierService;
import com.accelevents.session_speakers.services.UserSessionService;
import com.accelevents.ticketing.dto.DisplayAttributeDto;
import com.accelevents.ticketing.dto.DisplayNestedQueDto;
import com.accelevents.ticketing.dto.UpdateNestedQueAnsDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.JsonMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.JAXBException;
import java.lang.reflect.Method;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketHolderEditAttributesServiceImplTest {

    @InjectMocks
    @Spy
    TicketHolderEditAttributesServiceImpl ticketHolderEditAttributesServiceImpl;

    @Mock
    private TrayIntegrationService trayIntegrationService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private TicketHolderAttributesHelper ticketHolderAttributesHelper;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private EventTickets eventTicket;

    @Mock
    private Ticketing ticketing;
    @Mock
    private ZapierService zapierService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingPurchaseService ticketingPurchaseService;
    @Mock
    private TicketHolderRequiredAttributesService ticketRequireAttributeService;
    @Mock
    private TwilioPhoneNumberValidateService twilioPhoneNumberValidateService;
    @Mock
    private VatTaxService vatTaxService;
    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;
    @Mock
    private UserSessionService userSessionService;
    @Mock
    private AttendeeProfileService attendeeProfileService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private ROStaffService roStaffService;


    private Event event;
    private User user;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private TicketHolderAttributes ticketHolderAttributes;
    private TicketingType ticketingType;

    private TicketingType addonTicketingType;
    private TicketAttributeValueDto ticketAttributeValueDto;
    private HolderDisplayAttributesDto holderDisplayAttributesDto;
    private TicketingOrder ticketingOrder;
    private AttendeeAttributeValueDto attendeeAttributeValueDto;
    private TicketAttributeValueDto1 ticketAttributeValueDto1;
    private AttributeKeyValueDto attributeKeyValueDto;
    private DisplayAttributeDto displayAttributeDto;
    private UpdateNestedQueAnsDto updateNestedQueAnsDto;
    private DisplayNestedQueDto displayNestedQueDto;
    private ValueDto valueDtoHolder;
    private Long recurringEventId =1L;
    private EventPlanConfig planConfig;
    private RegistrationConfigDto registrationConfigDto;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        eventTicket = new EventTickets();
        eventTicket.setEvent(event);
        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(0L);
        ticketHolderAttributes.setJsonValue("{}");
        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        addonTicketingType = new TicketingType();
        addonTicketingType.setId(2L);
        addonTicketingType.setTicketTypeName("Addon");

        ticketAttributeValueDto = new TicketAttributeValueDto();
        holderDisplayAttributesDto = new HolderDisplayAttributesDto();
        ticketingOrder = new TicketingOrder();
        attendeeAttributeValueDto = new AttendeeAttributeValueDto();
        ticketAttributeValueDto1 = new TicketAttributeValueDto1();
        attributeKeyValueDto = new AttributeKeyValueDto();
        displayAttributeDto = new DisplayAttributeDto();
        User purchaser = new User();
        purchaser.setUserId(1L);
        purchaser.setEmail("<EMAIL>");
        ticketingOrder.setEventid(event);
        ticketingOrder.setPurchaser(purchaser);
        updateNestedQueAnsDto =new UpdateNestedQueAnsDto();

        displayNestedQueDto = new DisplayNestedQueDto();
        planConfig = new EventPlanConfig();
        registrationConfigDto = new RegistrationConfigDto();

        user = EventDataUtil.getUser();

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
    }

    @Test
    void test_getTicketHolderAttributes_eventTicketsNull() throws JAXBException {

        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(null);

        //Execution
        ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,event);

        //Assertion
        verify(eventCommonRepoService).findById(1L);
    }

    public static Object[] getRecurringEventIdAndDataType(){

        return new Object[]{
                new Object[]{null,DataType.ADDON,"1"},
                new Object[]{1L,DataType.TICKET,"1-1"},
                new Object[]{-1L,DataType.ADDON,"1"},
        };
    }

    @ParameterizedTest
    @MethodSource("getRecurringEventIdAndDataType")
    void test_getTicketHolderAttributes_eventTicketsAndAttributesEmpty(Long recurringEventId,DataType dataType,String eventKey) throws JAXBException {

        //Setup
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setDataType(dataType);
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setRecurringEventId(recurringEventId);


        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketHolderRequiredAttributesService.getHolderAttributesExcludingTypes(any(),nullable(Long.class),any(),any())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        HolderDisplayAttributesDto holderDisplayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,event);

        //Assertion
        assertTrue(holderDisplayAttributesDto.getAttributes().isEmpty());
        assertTrue(holderDisplayAttributesDto.getQuestions().isEmpty());
        assertEquals(holderDisplayAttributesDto.getEventTicketingId(),eventTicket.getId(),0);
        assertEquals(holderDisplayAttributesDto.getSeatNumber(),eventTicket.getSeatNumber());
        assertEquals(holderDisplayAttributesDto.getEventKey(),eventKey);

        verify(eventCommonRepoService).findById(1L);
        verify(ticketHolderRequiredAttributesService).getHolderAttributesExcludingTypes(any(),nullable(Long.class),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    static Object[] getHolderCountryCode(){

        return new Object[]{
                new Object[]{""},
                new Object[]{"US"},
        };
    }

    @ParameterizedTest
    @MethodSource("getHolderCountryCode")
    void test_getTicketHolderAttributes_eventTicketsAndHolderQuestionDtoNotEmpty(String holderCountryCode) throws JAXBException {

        //Setup
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.IMAGE);
        ticketHolderRequiredAttributes.setName("Cell Phone");
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId("1");

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setDataType(DataType.ADDON);
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setRecurringEventId(recurringEventId);
        eventTicket.setHolderCountryCode(holderCountryCode);
        eventTicket.setTicketingTypeOnlyId(1L);


        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketHolderRequiredAttributesService.getHolderAttributesExcludingTypes(any(),anyLong(),any(),any())).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        HolderDisplayAttributesDto holderDisplayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,event);

        //Assertion
        assertTrue(holderDisplayAttributesDto.getAttributes().isEmpty());
        assertFalse(holderDisplayAttributesDto.getQuestions().isEmpty());
        assertEquals(holderDisplayAttributesDto.getEventTicketingId(),eventTicket.getId(),0);
        assertEquals(holderDisplayAttributesDto.getSeatNumber(),eventTicket.getSeatNumber());
        assertEquals(holderDisplayAttributesDto.getEventKey(),"1-1");

        verify(eventCommonRepoService).findById(1L);
        verify(ticketHolderRequiredAttributesService).getHolderAttributesExcludingTypes(any(),anyLong(),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    static Object[] getTicketAttributeValueDtoAndattributeValueType(){

        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("EMAIL");
        keyValueDto.setValue("EMAIL");
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(keyValueDto);
        ValueDto valueDto = new ValueDto();
        valueDto.setAttributes(attributeKeyValueDtos);
        ticketAttributeValueDto.setHolder(valueDto);

        return new Object[]{
                new Object[]{new TicketAttributeValueDto(),AttributeValueType.BILLING_ADDRESS},
                new Object[]{ticketAttributeValueDto,AttributeValueType.SHIPPING_ADDRESS},
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketAttributeValueDtoAndattributeValueType")
    void test_getTicketHolderAttributes_eventTicketsAndHolderAttributesDtosNotEmpty(TicketAttributeValueDto ticketAttributeValueDto,AttributeValueType attributeValueType) throws JAXBException {

        //Setup
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setAttributeValueType(attributeValueType);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("EMAIL");
        ticketHolderRequiredAttributes.setHolderOptionalTicketTypeId("1");

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setRecurringEventId(recurringEventId);
        eventTicket.setHolderCountryCode("US");


        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketHolderRequiredAttributesService.getHolderAttributesExcludingTypes(any(),anyLong(),any(),anyList())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);
        when(eventTicketsRepoService.getAddonTicketingTypeByEventIdAndTicketIdForAddon(anyLong(), anyLong())).thenReturn(Collections.singletonList(addonTicketingType));

        //Execution
        HolderDisplayAttributesDto holderDisplayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,event);

        //Assertion
        assertFalse(holderDisplayAttributesDto.getAttributes().isEmpty());
        assertTrue(holderDisplayAttributesDto.getQuestions().isEmpty());
        assertEquals(holderDisplayAttributesDto.getEventTicketingId(),eventTicket.getId(),0);
        assertEquals(holderDisplayAttributesDto.getSeatNumber(),eventTicket.getSeatNumber());
        assertEquals(holderDisplayAttributesDto.getEventKey(),"1-1");

        verify(eventCommonRepoService).findById(1L);
        verify(ticketHolderRequiredAttributesService).getHolderAttributesExcludingTypes(any(),anyLong(),any(),anyList());
        verify(ticketHolderAttributesService).findById(anyLong());
        //TODO: Mockito ReWrite
        //verify(ticketHolderAttributesService).parseJsonToObject(anyString());
    }

    @Test
    void test_getTicketHolderAttributes_eventTicketsAndHolderAttributesDtosNotEmptyAndTicketHolderAttributeValue() throws JAXBException {

        //Setup
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.IMAGE);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName(STRING_LAST_SPACE_NAME);
        ticketHolderRequiredAttributes.setHolderOptionalTicketTypeId("1");

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setRecurringEventId(recurringEventId);
        eventTicket.setHolderCountryCode("US");
        eventTicket.setHolderLastName("Kazarian");


        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketHolderRequiredAttributesService.getHolderAttributesExcludingTypes(any(),anyLong(),any(),any())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);
        when(eventTicketsRepoService.getAddonTicketingTypeByEventIdAndTicketIdForAddon(anyLong(), anyLong())).thenReturn(Collections.singletonList(addonTicketingType));

        //Execution
        HolderDisplayAttributesDto holderDisplayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,event);

        //Assertion
        assertFalse(holderDisplayAttributesDto.getAttributes().isEmpty());
        assertTrue(holderDisplayAttributesDto.getQuestions().isEmpty());
        assertEquals(holderDisplayAttributesDto.getEventTicketingId(),eventTicket.getId(),0);
        assertEquals(holderDisplayAttributesDto.getSeatNumber(),eventTicket.getSeatNumber());
        assertEquals(holderDisplayAttributesDto.getEventKey(),"1-1");

        verify(eventCommonRepoService).findById(1L);
        verify(ticketHolderRequiredAttributesService).getHolderAttributesExcludingTypes(any(),anyLong(),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    @Test
    void test_getTicketHolderAttributes_successEventTicketsNull() throws JAXBException {

        //Mock
        when(eventCommonRepoService.findById(1L)).thenReturn(null);

        //Execution
        ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,1L);

        //Assertion
        verify(eventCommonRepoService).findById(1L);
    }

    @Test
    void test_getTicketHolderAttributes_success() throws JAXBException {

        //setup
        eventTicket.setTicketingOrder(ticketingOrder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        doReturn(holderDisplayAttributesDto).when(ticketHolderEditAttributesServiceImpl).getEditTicketHoldersAttributeDto(any(),any());

        User user = new User();
        user.setUserId(1L);
        eventTicket.setHolderUserId(user);
        //Execution
        HolderDisplayAttributesDto displayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,1L);

        //verify
        assertEquals(displayAttributesDto,holderDisplayAttributesDto);
        verify(eventCommonRepoService).findById(1L);
        verify(ticketHolderEditAttributesServiceImpl).getEditTicketHoldersAttributeDto(any(),any());
    }

    @Test
    void test_getTicketHolderAttributes_error() throws NotFoundException {

        //mock
        when(eventCommonRepoService.findById(anyLong())).thenReturn(null);

        //Execution
        try {
            ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L, 1L);
        }catch (NotFoundException e){
            assertEquals(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND.getDeveloperMessage(),e.getDeveloperMessage());
        }
        catch (JAXBException e) {
            e.printStackTrace();
        }

    }

    @Test
    void test_getTicketPurchaserAttributes_successEventTicketsEmpty() throws JAXBException {

        //Mock
        when(ticketingOrderService.findByidAndEventid(1L, event)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(List.of());

        //Execution
        ticketHolderEditAttributesServiceImpl.getTicketPurchaserAttributesForHost(1L,event,DataType.ADDON);

        //Assertion
        verify(ticketingOrderService).findByidAndEventid(Mockito.eq(1L), Mockito.refEq(event));
        verify(eventCommonRepoService).findByOrder(ticketingOrder);
    }

    @Test
    void test_getTicketPurchaserAttributes_successEventTickets() throws JAXBException {

        //Mock
        when(ticketingOrderService.findByidAndEventid(1L, event)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(List.of(eventTicket));
        doReturn(holderDisplayAttributesDto).when(ticketHolderEditAttributesServiceImpl).getEditTicketPurchaserAttributeDto(any(),any(),any());

        //Execution
        HolderDisplayAttributesDto displayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketPurchaserAttributesForHost(1L,event,DataType.ADDON);

        //Assertion
        assertEquals(holderDisplayAttributesDto, displayAttributesDto);


        verify(ticketingOrderService).findByidAndEventid(Mockito.eq(1L), Mockito.refEq(event));
        verify(eventCommonRepoService).findByOrder(ticketingOrder);
        verify(ticketHolderEditAttributesServiceImpl).getEditTicketPurchaserAttributeDto(any(),any(),any());
    }

    static Object[] getDataType(){

        return new Object[]{
                new Object[]{DataType.TICKET},
                new Object[]{DataType.ADDON},
        };
    }

    @ParameterizedTest
    @MethodSource("getDataType")
    void test_updateTicketHolderData(DataType dataType) throws JAXBException {

        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setDataType(dataType);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);

        attributeKeyValueDto.setKey(Constants.STRING_EMAIL_SPACE);
        attributeKeyValueDto.setValue("<EMAIL>");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        ticketing = new Ticketing();

        registrationConfigDto.setMaxTicketTransferLimit(5L);
        String registrationDtoString = JsonMapper.parseToJsonString(registrationConfigDto);
        planConfig.setRegistrationConfigJson(registrationDtoString);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(eventPlanConfigService.findByEventId(event.getEventId())).thenReturn(planConfig);
        doNothing().when(ticketHolderEditAttributesServiceImpl).orderAuditLog(any(), any(), any(), any(),anyBoolean());
        //Execution
        ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto );

        //Assertion
        ArgumentCaptor<TicketHolderAttributes> ticketHolderAttributesServiceArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesService, times(2)).save(ticketHolderAttributesServiceArgumentCaptor.capture());

        TicketHolderAttributes ticketHolderAttributesData = ticketHolderAttributesServiceArgumentCaptor.getValue();

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, times(1)).save(eventTicketsArgumentCaptor.capture());

        EventTickets eventTicketsData = eventTicketsArgumentCaptor.getValue();

        assertEquals(ticketHolderAttributesData,ticketHolderAttributes);
        assertEquals(eventTicketsData,eventTicket);
        verify(eventCommonRepoService).findById(1L);
    }

    @Test
    void test_updateTicketPurchaserDataByOrder_successEventTicketsNull() throws JAXBException {

        //Mock
        when(ticketingOrderService.findByid(1L)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(Collections.emptyList());
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setValue("<EMAIL>");
        keyValueDto.setKey("Email");
        attendeeAttributeValueDto.setAttributes(Collections.singletonList(keyValueDto));
        doNothing().when(ticketHolderEditAttributesServiceImpl).orderAuditLog(any(), any(), any(), any(),anyBoolean());

        //Execution
        ticketHolderEditAttributesServiceImpl.updateTicketPurchaserDataByOrder(1L, 1L, attendeeAttributeValueDto);

        //Assertion
        verify(ticketingOrderService).findByid(1L);
        verify(eventCommonRepoService).findByOrder(ticketingOrder);
    }

    @Test
    void test_updateTicketPurchaserDataByOrder_user_phone_number_change() throws JAXBException {

        when(ticketingOrderService.findByid(1L)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(Collections.emptyList());
        when(roUserService.getUserByAEPhoneNumber(any(AccelEventsPhoneNumber.class))).thenReturn(Optional.empty());
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        List<AttributeKeyValueDto> keyValueDtos = new ArrayList<>();
        {{
            keyValueDto.setValue("<EMAIL>");
            keyValueDto.setKey("Email");
            keyValueDtos.add(keyValueDto);
            keyValueDto = new AttributeKeyValueDto();
            keyValueDto.setValue("458315");
            keyValueDto.setKey(STRING_PHONE_NUMBER);
            keyValueDtos.add(keyValueDto);
        }}

        User user = new User();

        Optional<User> userOpt = Optional.of(user);
        when(roUserService.getUserById(anyLong())).thenReturn(userOpt);

        attendeeAttributeValueDto.setAttributes(keyValueDtos);
        doNothing().when(ticketHolderEditAttributesServiceImpl).orderAuditLog(any(), any(), any(), any(),anyBoolean());

        //Execution
        ticketHolderEditAttributesServiceImpl.updateTicketPurchaserDataByOrder(1L, 1L, attendeeAttributeValueDto);

        ArgumentCaptor<User> userSessionArgumentCaptor = ArgumentCaptor.forClass(User.class);
        verify(userService).updateUserPhoneNumber(userSessionArgumentCaptor.capture());
    }

    @Test
    void test_updateTicketPurchaserDataByOrder_successEventTickets() throws JAXBException {
        //setup

        attributeKeyValueDto.setKey(Constants.STRING_EMAIL_SPACE);
        attributeKeyValueDto.setValue("<EMAIL>");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setTicketingTypeId(ticketingType);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTicket);

        //Mock
        when(ticketingOrderService.findByid(1L)).thenReturn(ticketingOrder);
        when(eventCommonRepoService.findByOrder(ticketingOrder)).thenReturn(eventTicketsList);

        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setValue("<EMAIL>");
        keyValueDto.setKey("Email");
        attendeeAttributeValueDto.setAttributes(Collections.singletonList(keyValueDto));
        doNothing().when(ticketHolderEditAttributesServiceImpl).orderAuditLog(any(), any(), any(), any(),anyBoolean());

        //Execution
        ticketHolderEditAttributesServiceImpl.updateTicketPurchaserDataByOrder(1L, 1L, attendeeAttributeValueDto);

        //Assertion
        ArgumentCaptor<TicketHolderAttributes> ticketHolderAttributesServiceArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesService, times(1)).save(ticketHolderAttributesServiceArgumentCaptor.capture());

        TicketHolderAttributes ticketHolderAttributesData = ticketHolderAttributesServiceArgumentCaptor.getValue();
        assertEquals(ticketHolderAttributesData,ticketHolderAttributes);

        verify(ticketingOrderService).findByid(1L);
        verify(eventCommonRepoService).findByOrder(ticketingOrder);
    }

    static Object[] getHolderRequiredAttributes(){

        TicketHolderRequiredAttributes ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName("Email");
        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);
        return new Object[]{
                new Object[]{Collections.emptyList()},
                new Object[]{holderRequiredAttributes},
        };
    }

    @ParameterizedTest
    @MethodSource("getHolderRequiredAttributes")
    void test_getGenderAndAgeTicketHoldersAttribute_displayAttributeDtosEmpty(List<TicketHolderRequiredAttributes> holderRequiredAttributes) throws JAXBException {

        //setup
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);

        //mock
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        List<DisplayAttributeDto> displayAttributeDtos = ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(eventTicket,holderRequiredAttributes,true);

        //verify
        assertTrue(displayAttributeDtos.isEmpty());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    static Object[] getEnableForTicketHolderAndEnabledForTicketHolderAndEnabledForTicketPurchaser(){
        return new Object[]{
                new Object[]{true,false,false},
                new Object[]{false,true,false},
        };
    }

    @ParameterizedTest
    @MethodSource("getEnableForTicketHolderAndEnabledForTicketHolderAndEnabledForTicketPurchaser")
    void test_getGenderAndAgeTicketHoldersAttribute_successIsAttributeTrue(boolean enableForTicketHolder,boolean enabledForTicketHolder,boolean enabledForTicketPurchaser) throws JAXBException {

        //setup
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);

        ticketHolderRequiredAttributes.setName(GENDER);
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.IMAGE);
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(enabledForTicketHolder);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(enabledForTicketPurchaser);
        ticketHolderRequiredAttributes.setHolderOptionalTicketTypeId("1");
        ticketHolderRequiredAttributes.setBuyerOptionalTicketTypeId("1");

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        List<DisplayAttributeDto> displayAttributeDtos = ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(eventTicket,holderRequiredAttributes,enableForTicketHolder);

        assertTrue(displayAttributeDtos.isEmpty());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    static Object[] getEnableForTicketHolderAndEnabledForTicketHolderAndEnabledForTicketPurchaserAndDataType(){
        return new Object[]{
                new Object[]{true,true,false,DataType.TICKET},
                new Object[]{true,true,false,DataType.ADDON},
                new Object[]{false,false,true,DataType.ADDON},
        };
    }


        @ParameterizedTest
        @MethodSource("getEnableForTicketHolderAndEnabledForTicketHolderAndEnabledForTicketPurchaserAndDataType")
        void test_getGenderAndAgeTicketHoldersAttribute_successIsAttributeFalse(boolean enableForTicketHolder,boolean enabledForTicketHolder,boolean enabledForTicketPurchaser,DataType dataType) throws JAXBException {
            //setup
        eventTicket.setTicketingTypeId(ticketingType);
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setDataType(dataType);

        ticketHolderRequiredAttributes.setName(AGE);
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.IMAGE);
        ticketHolderRequiredAttributes.setAttribute(false);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(enabledForTicketHolder);
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(enabledForTicketPurchaser);

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);
        //when(ticketHolderAttributesService.unmashler(anyString(), any())).thenReturn(ticketAttributeValueDto1);

        //Execution
        List<DisplayAttributeDto> displayAttributeDtos = ticketHolderEditAttributesServiceImpl.getGenderAndAgeTicketHoldersAttribute(eventTicket,holderRequiredAttributes,enableForTicketHolder);

        //verify
        assertTrue(displayAttributeDtos.isEmpty());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    @Test
    public void
    test_filterAttributes_success() {
        //setup
        displayAttributeDto.setEventTicketTypeId("1");
        displayAttributeDto.setOptionalTicketTypeId("1");

        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        holderAttributesDto.add(displayAttributeDto);

        //Execution
        List<DisplayAttributeDto> holderAttributesDtos = ticketHolderEditAttributesServiceImpl.filterAttributes(holderAttributesDto,1L);

        //assert
        assertFalse(holderAttributesDtos.isEmpty());
    }

    @Test
    void test_filterAttributes_successHolderAttributesDtosEmpty() {
        //setup
        displayAttributeDto.setOptionalTicketTypeId("2,null");

        List<DisplayAttributeDto> holderAttributesDto = new ArrayList<>();
        holderAttributesDto.add(displayAttributeDto);

        //Execution
        List<DisplayAttributeDto> holderAttributesDtos = ticketHolderEditAttributesServiceImpl.filterAttributes(holderAttributesDto,1L);

        //assert
        assertTrue(holderAttributesDtos.isEmpty());
    }

    static Object[] getHolderPhoneNumber(){
        return new Object[]{
                new Object[]{-1L,null},
                new Object[]{8595857878L,"8595857878"},
        };
    }

    @ParameterizedTest
    @MethodSource("getHolderPhoneNumber")
    void test_getHolderPhoneNumber_success(Long phoneNumber,String response) {
        //setup
        eventTicket.setHolderPhoneNumber(phoneNumber);

        //Execution
        String holderPhoneNumber = ticketHolderEditAttributesServiceImpl.getHolderPhoneNumber(eventTicket);

        //assert
        assertEquals(holderPhoneNumber,response);
    }

    static Object[] getAttributeKey(){
        return new Object[]{
                new Object[]{STRING_EMAIL_SPACE,"<EMAIL>"},
                new Object[]{STRING_FIRST_SPACE_NAME,"Jon"},
                new Object[]{STRING_LAST_SPACE_NAME,"Kazarian"},
                new Object[]{null,null},
        };
    }

    @ParameterizedTest
    @MethodSource("getAttributeKey")
    void test_getTicketHolderAttributesValues(String attributeKey,String response) {
        //setup
        eventTicket.setHolderEmail("<EMAIL>");
        eventTicket.setHolderFirstName("Jon");
        eventTicket.setHolderLastName("Kazarian");

        //Execution
        String TicketHolderAttributesValues = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributesValues(eventTicket,attributeKey);

        //assert
        assertEquals(TicketHolderAttributesValues,response);
    }

    static Object[] getAttributeKeyAndAttributeValue(){
        return new Object[]{
                new Object[]{STRING_EMAIL_SPACE,"<EMAIL>"},
                new Object[]{STRING_FIRST_SPACE_NAME,"Jon"},
                new Object[]{STRING_LAST_SPACE_NAME,"Kazarian"},
                new Object[]{STRING_PHONE_NUMBER,"8589877896"},
                new Object[]{STRING_PHONE_NUMBER,""},
                new Object[]{STRING_COUNTRY_CODE,"US"},
        };
    }

    @ParameterizedTest
    @MethodSource("getAttributeKeyAndAttributeValue")
    void test_setEventsTicketsBasedOnAttributeValue(String attributeKey,String attributeValue) {

        //Setup
        Map<String, String> holderAttributes = new HashMap<>();
        holderAttributes.put("First Name","Milan");
        holderAttributes.put("Last Name","Dobariya");

        UserSignupDto userSignupDto = new UserSignupDto();
        userSignupDto.setFirstName("Jon");
        userSignupDto.setLastName("Kazarian");
        userSignupDto.setEmail("<EMAIL>");
        userSignupDto.setPhoneNumber(8589877896L);
        userSignupDto.setCountryCode("US");

        //Execution
        ticketHolderEditAttributesServiceImpl.setEventsTicketsBasedOnAttributeValue(event, eventTicket,attributeKey,attributeValue, holderAttributes);
        assertTrue(true);
    }

    @ParameterizedTest
    @MethodSource("getHolderRequiredAttributes")
    void test_getEditTicketPurchaserAttributeDto(List<TicketHolderRequiredAttributes> holderRequiredAttributes) throws JAXBException {

        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingTypeId(ticketingType);

        //mock
        when(ticketHolderRequiredAttributesService.getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        HolderDisplayAttributesDto attendee = ticketHolderEditAttributesServiceImpl.getEditTicketPurchaserAttributeDto(event,Collections.singletonList(eventTicket), DataType.ADDON);

        //assert
        assertTrue(attendee.getAttributes().isEmpty());
        assertTrue(attendee.getQuestions().isEmpty());

        verify(ticketHolderRequiredAttributesService).getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());

    }

    static Object[] getTicketAttributeValueDtoWithPurchaserAndattributeValueTypeAndTicketingOrder(){

        TicketAttributeValueDto ticketAttributeValueDto = new TicketAttributeValueDto();
        AttributeKeyValueDto keyValueDto = new AttributeKeyValueDto();
        keyValueDto.setKey("Cell Phone");
        keyValueDto.setValue("|+1");
        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(keyValueDto);
        ValueDto valueDto = new ValueDto();
        valueDto.setQuestions(attributeKeyValueDtos);
        ticketAttributeValueDto.setPurchaser(valueDto);

        TicketingOrder ticketingOrder = new TicketingOrder();
        ticketingOrder.setPurchaser(new User());

        User user = new User();
        user.setPhoneNumber(8587899874L);
        user.setCountryCode(CountryCode.US);

        TicketingOrder ticketingOrder1 = new TicketingOrder();
        ticketingOrder1.setPurchaser(user);

        return new Object[]{
                new Object[]{new TicketAttributeValueDto(),AttributeValueType.BILLING_ADDRESS,ticketingOrder},
                new Object[]{ticketAttributeValueDto,AttributeValueType.SHIPPING_ADDRESS,ticketingOrder1},
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketAttributeValueDtoWithPurchaserAndattributeValueTypeAndTicketingOrder")
    void test_getEditTicketPurchaserAttributeDto(TicketAttributeValueDto ticketAttributeValueDto,AttributeValueType attributeValueType,TicketingOrder ticketingOrder) throws JAXBException {

        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setTicketingTypeId(ticketingType);

        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setAttributeValueType(attributeValueType);
        ticketHolderRequiredAttributes.setName("Cell Phone");
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(String.valueOf(eventTicket.getTicketingTypeId().getId()));
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId(String.valueOf(eventTicket.getTicketingTypeId().getId()));

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        HolderDisplayAttributesDto attendee = ticketHolderEditAttributesServiceImpl.getEditTicketPurchaserAttributeDto(event,Collections.singletonList(eventTicket), DataType.ADDON);

        //assert
        assertTrue(attendee.getAttributes().isEmpty());
        //TODO: Mockito ReWrite
        //assertFalse(attendee.getQuestions().isEmpty());

        verify(ticketHolderRequiredAttributesService).getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());
    }

    @Test
    void test_getEditTicketPurchaserAttributeDtoIsAttributeTrue() throws JAXBException {

        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingTypeId(ticketingType);

        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.TEXT);
        ticketHolderRequiredAttributes.setName("Email");
        ticketHolderRequiredAttributes.setAttribute(true);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(String.valueOf(eventTicket.getTicketingTypeId().getId()));
        ticketHolderRequiredAttributes.setBuyerEventTicketTypeId(String.valueOf(eventTicket.getTicketingTypeId().getId()));

        List<TicketHolderRequiredAttributes> holderRequiredAttributes = new ArrayList<>();
        holderRequiredAttributes.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any())).thenReturn(holderRequiredAttributes);
        when(ticketHolderAttributesService.findById(anyLong())).thenReturn(ticketHolderAttributes);

        //Execution
        HolderDisplayAttributesDto attendee = ticketHolderEditAttributesServiceImpl.getEditTicketPurchaserAttributeDto(event,Collections.singletonList(eventTicket), DataType.ADDON);

        //assert
        assertTrue(attendee.getQuestions().isEmpty());
        //TODO: Mockito ReWrite
//        assertFalse(attendee.getAttributes().isEmpty());

        verify(ticketHolderRequiredAttributesService).getBuyerAttributesExcludingTypes(any(),nullable(Long.class),any(),any());
        verify(ticketHolderAttributesService).findById(anyLong());
    }
    @Test
    void test_updateTicketHolderData1_throwException_FIRST_NAME_SIZE_LIMIT() throws NotAcceptableException,JAXBException{
        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);

        attributeKeyValueDto.setKey(FIRST_NAME);
        attributeKeyValueDto.setValue("123456789123456789123456789123456789123111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);



        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto ));
        assertEquals(NotAcceptableException.SizeLimitExceptionMsg.FIRST_NAME_SIZE_LIMIT.getDeveloperMessage(), exception.getMessage());

    }
    @Test
    void test_updateTicketHolderData1_throwException_LAST_NAME_SIZE_LIMIT() throws NotAcceptableException,JAXBException{
        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);

        attributeKeyValueDto.setKey(LAST_NAME);
        attributeKeyValueDto.setValue("123456789123456789123456789123456789123111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);



        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto ));

        assertEquals(NotAcceptableException.SizeLimitExceptionMsg.LAST_NAME_SIZE_LIMIT.getDeveloperMessage(), exception.getMessage());

    }
    @Test
    void test_updateTicketHolderData1_throwException_EMAIL_SIZE_LIMIT() throws NotAcceptableException,JAXBException{
        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);
        attributeKeyValueDto.setKey(EMAIL);
        attributeKeyValueDto.setValue("<EMAIL>");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto ));
        assertEquals(NotAcceptableException.SizeLimitExceptionMsg.EMAIL_SIZE_LIMIT.getDeveloperMessage(), exception.getMessage());

    }
    @Test
    void test_updateTicketHolderData1_throwException_INVALID_EMAIL() throws NotAcceptableException,JAXBException{
        //setup
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);

        attributeKeyValueDto.setKey(EMAIL);
        attributeKeyValueDto.setValue("Abc@gmail");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto ));
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.INVALID_EMAIL.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_updateTicketHolderData_to_make_as_guest_of_buyer() throws JAXBException {
        //setup
        User otherUser = EventDataUtil.getOtherUser();
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);
        eventTicket.setHolderEmail(user.getEmail());
        eventTicket.setTicketPurchaserId(otherUser);

        attributeKeyValueDto.setKey(FIRST_NAME);
        attributeKeyValueDto.setValue("Jon");
        attributeKeyValueDto.setKey(LAST_NAME);
        attributeKeyValueDto.setValue("Kazarian");
        attributeKeyValueDto.setKey(EMAIL);
        attributeKeyValueDto.setValue(STRING_EMPTY);

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);


        Map<String, String> holderAttributes = new HashMap<>();
        holderAttributes.put(EMAIL, user.getEmail());

        Map<String, Map<String, String>> attributes = new HashMap<>();
        attributes.put(TICKETING.ATTRIBUTES, holderAttributes);

        ticketAttributeValueDto1.setHolder(attributes);
        ticketAttributeValueDto1.setAddOn(Collections.emptyMap());

        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        doNothing().when(ticketHolderEditAttributesServiceImpl).orderAuditLog(any(), any(), any(), any(),anyBoolean());


        //Execution
        ticketHolderEditAttributesServiceImpl.updateTicketHolderData(1L,1L, attendeeAttributeValueDto );

        ArgumentCaptor<EventTickets> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository).save(eventTicketsArgumentCaptor.capture());
        EventTickets eventTicketsData = eventTicketsArgumentCaptor.getValue();
        assertEquals(eventTicketsData.getHolderUserId().getUserId(),otherUser.getUserId());
        assertEquals(STRING_EMPTY, eventTicketsData.getHolderEmail());
        assertEquals(Boolean.TRUE, eventTicketsData.getGuestOfBuyer());
    }

    @Test
    void test_updateTicketHolderData_updateBillingAndShippingAddress() throws Exception{
        //setup
        updateNestedQueAnsDto.setId(1L);
        updateNestedQueAnsDto.setParentQueId(0L);
        updateNestedQueAnsDto.setName("question");
        updateNestedQueAnsDto.setType(AttributeValueType.CONDITIONAL_QUE.name());
        updateNestedQueAnsDto.setValue("value");
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTicket.setTicketingOrder(ticketingOrder);
        eventTicket.setHolderUserId(user);

        registrationConfigDto.setMaxTicketTransferLimit(5L);
        String registrationDtoString = JsonMapper.parseToJsonString(registrationConfigDto);
        planConfig.setRegistrationConfigJson(registrationDtoString);
        attributeKeyValueDto.setKey(Constants.STRING_EMAIL_SPACE);
        attributeKeyValueDto.setValue("<EMAIL>");

        List<AttributeKeyValueDto> attributeKeyValueDtos = new ArrayList<>();
        attributeKeyValueDtos.add(attributeKeyValueDto);

        attendeeAttributeValueDto.setAttributes(attributeKeyValueDtos);
        attendeeAttributeValueDto.setQuestions(attributeKeyValueDtos);

        List<UpdateNestedQueAnsDto> attributeKeyValueDtos1 = new ArrayList<>();
        attributeKeyValueDtos1.add(updateNestedQueAnsDto);
        attendeeAttributeValueDto.setNestedQuestions(attributeKeyValueDtos1);
        ticketAttributeValueDto1.setHolder(new HashMap());
        ticketAttributeValueDto1.setAddOn(new HashMap());


        valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtos);

        ticketAttributeValueDto = new TicketAttributeValueDto();
        ticketAttributeValueDto.setHolder(valueDtoHolder);
        ticketing = new Ticketing();

    }

    @Test
    void test_updateCellPhone(){

        //Setup
        eventTicket.setHolderPhoneNumber(9558620170L);



        //Execution
        try {

            Method method = TicketHolderEditAttributesServiceImpl.class.getDeclaredMethod("updateCellPhone", EventTickets.class, DisplayAttributeDto.class);
            method.setAccessible(true);
            method.invoke(ticketHolderEditAttributesServiceImpl, eventTicket, displayAttributeDto);
        }catch (Exception ex){
            //ignored
        }
        verify(twilioPhoneNumberValidateService).getPhoneDetail(null, "9558620170");
    }

    @Test
    void test_blockUnblock(){

        when(eventCommonRepoService.findById(anyLong())).thenReturn(eventTicket);
        doNothing().when(ticketHolderEditAttributesServiceImpl).attendeeBlockInNeptune(anyBoolean(),any(),any());
        when(eventCommonRepoService.findOtherEventTicketForSameUserANDEvent(any(),any(),anyLong())).thenReturn(Collections.singletonList(eventTicket));

        EventTickets eventTickets = ticketHolderEditAttributesServiceImpl.blockUnblock(1L, true, event);

    }

    @Test
    void test_updateUserEventTicketAndOrderDetails() throws JAXBException {

        ticketHolderAttributes.setJsonValue("test");
        eventTicket.setTicketHolderAttributesId(ticketHolderAttributes);

        ticketHolderRequiredAttributes.setEnabledForTicketHolder(true);
        ticketHolderRequiredAttributes.setName("Cell Phone");

        when(eventCommonRepoService.getHolderEventTicketsANDNotINRecordStatusAndEventNotDeleted(any())).thenReturn(Collections.singletonList(eventTicket));
        doNothing().when(eventCommonRepoService).saveAll(anyList());
        when(ticketingOrderService.getOrderByPurchaseridAndRecordStatusNotDeleted(any())).thenReturn(Collections.singletonList(ticketingOrder));
        doNothing().when(ticketingOrderService).saveAll(any());
        when(eventCommonRepoService.findAllByListOrderIds(any())).thenReturn(Collections.singletonList(eventTicket));
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(),anyLong())).thenReturn(Collections.singletonList(ticketHolderRequiredAttributes));
        doReturn(ticketAttributeValueDto1).when(ticketHolderEditAttributesServiceImpl).getTicketAttributeValueDto(eventTicket);

        ticketHolderEditAttributesServiceImpl.updateUserEventTicketAndOrderDetails(user);
        verify(eventCommonRepoService).saveAll(Collections.singletonList(eventTicket));

    }

    @Test
    void test_getTicketPurchaserAttributesForMyProfile() throws JAXBException {

        holderDisplayAttributesDto.setEventTicketingId(null);

        when(ticketingOrderService.findByIdAndpurchaserId(anyLong(),anyLong())).thenReturn(ticketingOrder);

        HolderDisplayAttributesDto ticketPurchaserAttributesForMyProfile = ticketHolderEditAttributesServiceImpl.getTicketPurchaserAttributesForMyProfile(1L, user, DataType.TICKET);

        assertEquals(holderDisplayAttributesDto.getEventTicketingId(),ticketPurchaserAttributesForMyProfile.getEventTicketingId());

    }

    @Test
    void test_getTicketHolderDataAttributes() throws JAXBException {
        List<Long> ids = new ArrayList<>(Arrays.asList(1L,2L));
        //setup
        eventTicket.setTicketingOrder(ticketingOrder);

        //mock
        when(eventCommonRepoService.findById(1L)).thenReturn(eventTicket);
        doReturn(holderDisplayAttributesDto).when(ticketHolderEditAttributesServiceImpl).getEditTicketHoldersAttributeDto(any(),any());

        User user = new User();
        user.setUserId(1L);
        eventTicket.setHolderUserId(user);
        //Execution
        HolderDisplayAttributesDto displayAttributesDto = ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L,1L);

        //verify
        assertEquals(displayAttributesDto,holderDisplayAttributesDto);
        verify(eventCommonRepoService).findById(1L);

        when(eventCommonRepoService.findAllByIdAndHolderUserIdOrPurchaserUserId(anyList(), anyLong())).thenReturn(Collections.singletonList(eventTicket));
        doReturn(holderDisplayAttributesDto).when(ticketHolderEditAttributesServiceImpl).getEditTicketHoldersAttributeDto(any(),any());

        List<HolderDisplayAttributesDto> ticketHolderDataAttributes = ticketHolderEditAttributesServiceImpl.getTicketHolderDataAttributes(ids, 1L);

        assertEquals(1,ticketHolderDataAttributes.size());
    }

    @Test
    void test_displayNestedQueDto(){
        ticketHolderRequiredAttributes.setAttributeValueType(AttributeValueType.CONDITIONAL_QUE);
        try {
            Method method = TicketHolderEditAttributesServiceImpl.class.getDeclaredMethod
                    ("setHolderAttributesData", List.class, List.class,TicketHolderRequiredAttributes.class,DisplayAttributeDto.class);
            method.setAccessible(true);
            method.invoke(ticketHolderEditAttributesServiceImpl,
                    Collections.singletonList(displayAttributeDto), Collections.singletonList(displayNestedQueDto), ticketHolderRequiredAttributes, displayAttributeDto);
        }catch (Exception ex){
            //ignored
        }
        assertTrue(true);
    }

    @Test
    void test_getTicketHolderAttributes() throws JAXBException {

        eventTicket.setHolderUserId(user);
        eventTicket.getHolderUserId().setUserId(2L);
        eventTicket.setTicketPurchaserId(user);
        eventTicket.getTicketPurchaserId().setUserId(2L);

        when(eventCommonRepoService.findById(anyLong())).thenReturn(eventTicket);

        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketHolderEditAttributesServiceImpl.getTicketHolderAttributes(1L, 1L));
        
        assertEquals(NotFoundException.TicketingOrderExceptionMsg.EVENT_TICKETS_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }
}