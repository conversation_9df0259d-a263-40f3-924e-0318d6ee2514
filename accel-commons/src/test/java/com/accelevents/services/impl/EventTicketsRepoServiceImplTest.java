package com.accelevents.services.impl;

import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.services.repo.helper.impl.EventTicketsRepoServiceImpl;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(MockitoExtension.class)
public class EventTicketsRepoServiceImplTest {

    @InjectMocks
    @Spy
    EventTicketsRepoServiceImpl eventTicketsRepoServiceImpl = new EventTicketsRepoServiceImpl();

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Test
    public void test_getSoldCountByPassedListOfRecurringList_Empty_List(){

        //SetUp
        List<Long> recIds = new ArrayList<>();

        //Execution
        List<RecurringEventsTicketingTypeAndSoldCountDTO> typeAndSoldCountDTOS = eventTicketsRepoServiceImpl.getSoldCountByListOfRecurringList(recIds);

        assertTrue(CollectionUtils.isEmpty(typeAndSoldCountDTOS));
    }


}
