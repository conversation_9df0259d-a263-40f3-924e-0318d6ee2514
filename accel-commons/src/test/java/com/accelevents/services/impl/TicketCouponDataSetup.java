package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.DiscountType;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by Naresh on 2/9/2018.
 */
public class TicketCouponDataSetup {

    public static TicketingCoupon getCouponForTest(Event event, Ticketing ticketing){
        //Set up Transaction fee type


        //Set up ticket type
        getTicketingType(ticketing, event);

        //Set up ticket coupon
        Calendar couponStartDate = Calendar.getInstance();
        Calendar couponEndDate = Calendar.getInstance();
        couponEndDate.add(Calendar.DATE, 1);

        TicketingCoupon ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setId(1L);
        ticketingCoupon.setCouponStartDate(couponStartDate.getTime());
        ticketingCoupon.setCouponEndDate(couponEndDate.getTime());
        ticketingCoupon.setUses(2);
        ticketingCoupon.setAmount(2);
        ticketingCoupon.setEventId(event.getEventId());
        ticketingCoupon.setDiscountType(DiscountType.PERCENTAGE);
        ticketingCoupon.setName("TEST123");
        ticketingCoupon.setUsesPerUser(1);

        return ticketingCoupon;
    }

    public static TicketingType getTicketingType(Ticketing ticketing, Event event) {
        Calendar ticketTypeStartDate = Calendar.getInstance();
        Calendar ticketTypeEndDate = Calendar.getInstance();
        ticketTypeEndDate.add(Calendar.DATE, 30);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setAllow_discount_coupon(true);
        ticketingType.setEnableTicketDescription(true);
        ticketingType.setEndDate(ticketTypeEndDate.getTime());
        ticketingType.setHidden(false);
        ticketingType.setMaxTicketsPerBuyer(3L);
        ticketingType.setNumberOfTickets(200L);
        ticketingType.setPassfeetobuyer(false);
        ticketingType.setPrice(200d);
        ticketingType.setStartDate(ticketTypeStartDate.getTime());
        ticketingType.setTicketTypeDescription(null);
        ticketingType.setTicketTypeName("Naresh Ticket Type");
        ticketingType.setTicketing(ticketing);
        ticketingType.setMinTicketsPerBuyer(0);
        ticketingType.setPosition(1000);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setNumberOfTicketPerTable(0);
        ticketingType.setAeFeeFlat(1);
        ticketingType.setAeFeePercentage(1);
        ticketingType.setWlAFeeFlat(0);
        ticketingType.setWlAFeePercentage(0);
        ticketingType.setTicketType(TicketType.PAID);

        return ticketingType;
    }

    public static List<TicketingOrder> getOrdersWithoutAppliedCouponForTest(Event event){

        List<TicketingOrder> ticketingOrders = new ArrayList<>();

        TicketingOrder ticketingOrder1 = new TicketingOrder();

        Calendar orderExpiry = Calendar.getInstance();
        orderExpiry.add(Calendar.MINUTE, 10);

        User user = getUser();

        ticketingOrder1.setId(1L);
        ticketingOrder1.setExpirein(orderExpiry.getTime());
        ticketingOrder1.setOrderDate(new Date());
        ticketingOrder1.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder1.setEventid(event);
        ticketingOrder1.setPurchaser(user);
        ticketingOrder1.setTicketingCoupon(null);
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrders.add(ticketingOrder1);

        return ticketingOrders;
    }

    public static List<TicketingOrder> getOrdersWithAppliedCouponForTest(Event event, TicketingCoupon ticketingCoupon){

        List<TicketingOrder> ticketingOrders = new ArrayList<>();

        TicketingOrder ticketingOrder1 = new TicketingOrder();

        Calendar orderExpiry = Calendar.getInstance();
        orderExpiry.add(Calendar.MINUTE, 10);

        User user = getUser();

        ticketingOrder1.setId(1L);
        ticketingOrder1.setExpirein(orderExpiry.getTime());
        ticketingOrder1.setOrderDate(new Date());
        ticketingOrder1.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        ticketingOrder1.setEventid(event);
        ticketingOrder1.setPurchaser(user);
        ticketingOrder1.setTicketingCoupon(ticketingCoupon);
        ticketingOrder1.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrders.add(ticketingOrder1);

        return ticketingOrders;
    }

    private static User getUser() {
        User user = new User();
        user.setCountryCode(CountryCode.US);
        user.setEmail("<EMAIL>");
        user.setPhoneNumber(9999999999L);
        return user;
    }

}
