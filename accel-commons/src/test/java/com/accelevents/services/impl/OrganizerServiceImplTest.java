package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.repo.ChargebeePlanRepoService;
import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.billing.chargebee.repo.OrganizerRepoService;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.common.dto.ContactDto;
import com.accelevents.common.dto.EventInfoDto;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.dto.PageSizeSearchObj;
import com.accelevents.enums.OrganizerRole;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.event.EventDesignSettingsDto;
import com.accelevents.exceptions.AuthorizationException;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.hubspot.service.HubspotOrganizerService;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.*;
import com.accelevents.ro.event.service.ROJoinUserWithOrganizerService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;

import java.util.*;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OrganizerServiceImplTest {

    @Spy
    @InjectMocks
    private OrganizerServiceImpl organizerServiceImpl;
    @Spy
    private ImageConfiguration imageConfiguration = new ImageConfiguration();
    @Mock
    private OrganizerRepository organizerRepository;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private JoinEventWithPDProductService joinEventWithPDProductService;

    @Mock
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Mock
    private ROJoinUserWithOrganizerService roJoinUserWithOrganizerService;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;

    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

    @Mock
    private StaffService staffService;

    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Mock
    private TicketingDisplayService ticketingDisplayService;

    @Mock
    private ChargebeePlanService chargebeePlanService;

    @Mock
    private JoinUserWithOrganizerRepoService joinUserWithOrganizerRepoService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Mock
    private ChargebeePaymentService chargebeePaymentService;
    @Mock
    private OrganizerRepoService organizerRepoService;
    @Mock
    private BeeFreeRepository beeFreeRepository;
    @Mock
    private EventChargeUsagesService eventChargeUsagesService;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private ChargebeePlanRepoService chargebeePlanRepoService;
    @Mock
    private AddressDetailsRepository addressDetailsRepository;
    @Mock
    private HubspotOrganizerService hubspotOrganizerService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private TrayIntegrationService trayIntegrationService;

    private User user;
    private Event event;
    private Organizer organizer;
    private OrganizerDto organizerDto;
    private JoinUsersWithOrganizers joinUsersWithOrganizers;
    private Staff staff;
    private WhiteLabel whiteLabel;
    private ContactDto contactDto;
    private Ticketing ticketing;
    private AddressDetails addressDetails;
    private PageSizeSearchObj pageSizeSearchObj;

    private String startDate = "2019/01/01 05:30";
    private String endDate = "2019/04/04 05:30";
    private Date startDate1 = new Date(startDate);
    private Date endDate1 = new Date(endDate);
    private String organizerUrl = "EventOrganizer";
    private long organizerId = 1L;
    private String eventLogo =  "b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg";
    private String eventLocation = "newYork";
    private String eventDesciption = "Event description";
    private String eventFormat = "VIRTUAL";
    private PlanConfig chargebeePlan;
    boolean isFromOrganizerProfilePage;
    private EventDesignDetail eventDesignDetail;

    private int page = 0;

    private int size = 10;

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setName("testEvent123");
        pageSizeSearchObj = new PageSizeSearchObj(page, size,STRING_EMPTY, "event_end_date", Sort.Direction.DESC);
        user = EventDataUtil.getUser();

        organizer = new Organizer();
        organizer.setName("testOrganizer");
        organizer.setId(1L);
        organizer.setOrganizerPageURL("EventOrganizer");

        organizerDto = new OrganizerDto();
        organizerDto.setName(organizer.getName());
        organizerDto.setOrganizerId(organizer.getId());
        organizerDto.setOrganizerPageURL(organizer.getOrganizerPageURL());
        organizerDto.setBackgroundColor("#D1D1D1");
        organizerDto.setFacebookLink("facebookLink");
        organizerDto.setTwitterLink("twitterLink");
        organizerDto.setLinkedInLink("linkedInLink");
        organizerDto.setLogoImage("logoImage");
        organizerDto.setOrganizerDescription("organizerDescription");
        organizerDto.setTextColor("#D2D2D2");
        organizerDto.setWebsite("www.event1.com");
        organizerDto.setContactEmailAddress("<EMAIL>");

        chargebeePlan = new PlanConfig();
        chargebeePlan.setChargebeePlanId(PlanConfigNames.FREE_PLAN.getName());

        addressDetails = new AddressDetails();
        addressDetails.setAddress1("Address 1");

        eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setEvent(event);

    }

    //@Test
    void test_save_with_organizerList_empty() {

        //setup
        EventInfoDto eventInfoDto = new EventInfoDto("akOrganizertest","akURLTest",new Date(), "xyzLogo", "test location");

        Set<EventInfoDto> eventInfoDtos = new HashSet<>();
        eventInfoDtos.add(eventInfoDto);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName(organizer.getName());

        String organizerURL = organizer.getName().replaceAll("[^a-zA-Z0-9]", Constants.STRING_EMPTY);

        //mock
        when(joinUsersWithOrganizersRepository.findOrganizerByUserAndOrganizerName(anyLong(),anyString())).thenReturn(Collections.EMPTY_LIST);
        when(organizerRepository.findOrganizerByOrganizerPageURL("testOrganizer")).thenReturn(Optional.empty());
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        when(organizerRepository.save(any())).thenReturn(organizer);

        //Execution
        OrganizerDto organizerDtoData = organizerServiceImpl.save(eventDesignSettingsDto.getOrganizerName(), user, event);

        //Assertion
        assertEquals(organizerDtoData.getName(), organizer.getName());
        assertEquals(organizerDtoData.getOrganizerPageURL(), organizerURL);
        assertEquals(1, organizerDtoData.getOrganizerId());

        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getName(), organizer.getName());
        assertEquals(organizerData.getOrganizerPageURL(), organizerURL);
        assertEquals(organizerData.getCreatedBy(), user);

        ArgumentCaptor<JoinUsersWithOrganizers> joinUsersWithOrganizersArgumentCaptor = ArgumentCaptor.forClass(JoinUsersWithOrganizers.class);
        verify(joinUsersWithOrganizersRepository, times(1)).save(joinUsersWithOrganizersArgumentCaptor.capture());

        JoinUsersWithOrganizers joinUsersWithOrganizersData = joinUsersWithOrganizersArgumentCaptor.getValue();
        assertEquals(joinUsersWithOrganizersData.getUserId(),  user.getUserId().longValue());
        assertEquals(joinUsersWithOrganizersData.getOrganizerId(),  0);
    }

   // @Test
    void test_save_with_organizerList_null() {

        //setup
        String organizerURL = organizer.getName().replaceAll("[^a-zA-Z0-9]", Constants.STRING_EMPTY);

        EventInfoDto eventInfoDto = new EventInfoDto("akOrganizertest","akURLTest",new Date(), "xyzLogo", "test location");

        Set<EventInfoDto> eventInfoDtos = new HashSet<>();
        eventInfoDtos.add(eventInfoDto);

        List<Object[]> organizerObject = new ArrayList<>();
        Object[] obj = new Object[] { 10000L, "testOrganizer", "testURL" ,"testDescription", "testLogo", "testFacebookLink", "testTwitterLink", "testLinkedInLink", "backgroundColor", "textcolor" , "website" , "contactEmailAddress"};
        organizerObject.add(obj);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName(organizer.getName());

        //mock
        when(joinUsersWithOrganizersRepository.findOrganizerByUserAndOrganizerName(anyLong(),anyString())).thenReturn(null);
        when(organizerRepository.findOrganizerByOrganizerPageURL("testOrganizer")).thenReturn(Optional.empty());
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        when(organizerRepository.save(any())).thenReturn(organizer);

        //Execution
        OrganizerDto organizerDtoData = organizerServiceImpl.save(eventDesignSettingsDto.getOrganizerName(), user, event);

        //Assertion
        assertEquals(organizer.getName(), organizerDtoData.getName());
        assertEquals(organizer.getOrganizerPageURL(), organizerDtoData.getOrganizerPageURL());
        assertEquals(0, Optional.ofNullable(organizerDtoData.getOrganizerId()));

        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getName(), organizer.getName());
        assertEquals(organizerData.getOrganizerPageURL(), organizerURL);
        assertEquals(organizerData.getCreatedBy(), user);

        ArgumentCaptor<JoinUsersWithOrganizers> joinUsersWithOrganizersArgumentCaptor = ArgumentCaptor.forClass(JoinUsersWithOrganizers.class);
        verify(joinUsersWithOrganizersRepository, times(1)).save(joinUsersWithOrganizersArgumentCaptor.capture());

        JoinUsersWithOrganizers joinUsersWithOrganizersData = joinUsersWithOrganizersArgumentCaptor.getValue();
        assertEquals(joinUsersWithOrganizersData.getUserId(),  user.getUserId().longValue());
        assertEquals(joinUsersWithOrganizersData.getOrganizerId(),  0);
    }

    @Test
    void test_save_with_organizer1() {

        //setup
        Organizer organizer1 = new Organizer();

        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.FREE_PLAN.getName());
        organizer.setPlanConfig(planConfig);
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setId(0);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName(organizer.getName());

        //mock

        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer1));
        Mockito.doReturn(organizerUrl).when(organizerServiceImpl).getOrganizerUrl(anyString(), anyInt(), anyInt(), anyBoolean());
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        when(organizerRepository.save(any())).thenReturn(organizer);
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);




        //Execution
        OrganizerDto organizerDtoData = organizerServiceImpl.save(eventDesignSettingsDto.getOrganizerName(), user, event);

        //Assertion
        assertEquals(organizerDtoData.getName(), organizer.getName());
        assertEquals(organizerDtoData.getOrganizerPageURL(), organizer.getOrganizerPageURL());
        assertEquals(Optional.ofNullable(organizerDtoData.getOrganizerId()), Optional.of(1L));

//        verify(joinUsersWithOrganizersRepository, times(1)).findOrganizerByUserAndOrganizerName(anyLong(),anyString());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    //@Test
    void test_save_throw_Exception_ORGANIZER_URL_ALREADY_EXIST() {

        //setup
        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName(organizer.getName());
        
        List<Object[]> organizerObject = new ArrayList<>();
        Object[] obj = new Object[] { 10000L, "testOrganizer", "testURL" ,"testDescription", "testLogo", "testFacebookLink", "testTwitterLink", "testLinkedInLink", "backgroundColor", "textcolor" , "website" , "contactEmailAddress"};
        organizerObject.add(obj);

        //mock
        when(joinUsersWithOrganizersRepository.findOrganizerByUserAndOrganizerName(anyLong(),anyString())).thenReturn(organizerObject);
        when( organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        doNothing().when(joinUserWithOrganizerRepoService).associateUserWithOrganization(user, organizer, OrganizerRole.owner, true, 1L);
        when(organizerRepository.save(any())).thenReturn(organizer);

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> organizerServiceImpl.save(eventDesignSettingsDto.getOrganizerName(), user, event));

        //Assertion
        assertEquals(ConflictException.UserExceptionConflictMsg.ORGANIZER_URL_ALREADY_EXIST.getDeveloperMessage(), exception.getMessage());
       // verify(joinUsersWithOrganizersRepository, times(1)).findOrganizerByUserAndOrganizerName(anyLong(),anyString());
    }

    @Test
    void test_genarateOrganizer_with_organizerList_empty() {

        //setup
        int count = 1;
        List<Object[]> organizerList = new ArrayList<>();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.FREE_PLAN.getName());
        organizer.setPlanConfig(planConfig);
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setId(0);

        //mock
        when( organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        doNothing().when(joinUserWithOrganizerRepoService).associateUserWithOrganization(user, organizer, OrganizerRole.owner, true, organizer.getWhiteLabel() != null ? organizer.getWhiteLabel().getId() :null);
        when(organizerRepository.save(any())).thenReturn(organizer);
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig);


        //Execution
        OrganizerDto organizerDtoData = organizerServiceImpl.save(organizer.getName(), user, event);

        //Assertion
        assertEquals(organizerDtoData.getName(), organizerDto.getName());
        assertEquals(organizerDtoData.getOrganizerId(), organizerDto.getOrganizerId());
        assertEquals(organizerDtoData.getOrganizerPageURL(), organizerDto.getOrganizerPageURL());

        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_validateOrganizerName_with_organizer() {

        //setup
        String validatedOrganizer = String.format(Constants.EVENT_WILL_BE_JOINED_WITH_ORGANIZER, organizerUrl);

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer));

        //Execution
        String validatedOrganizerName = organizerServiceImpl.validateOrganizerName(organizerUrl);

        //Assertion
        assertEquals(validatedOrganizerName, validatedOrganizer);

        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_validateOrganizerName_without_organizer() {

        //setup
        String validatedOrganizer = Constants.NEW_ORGANIZER_WILL_BE_CREATED;

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());

        //Execution
        String validatedOrganizerName = organizerServiceImpl.validateOrganizerName(organizerUrl);

        //Assertion
        assertEquals(validatedOrganizerName, validatedOrganizer);

        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_updateOrganizer_organizer_and_superAdminUser_true() {

        //mock
        when(roUserService.isSuperAdminUser(any())).thenReturn(true);
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.of(organizer));
        when(addressDetailsRepository.findByOrganizerId(organizerId)).thenReturn(Optional.empty());

        //Execution
        organizerServiceImpl.updateOrganizer(organizerId, organizerDto, user, Boolean.FALSE);

        //Assertion
        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getBackgroundColor(), organizerDto.getBackgroundColor());
        assertEquals(organizerData.getFacebookLink(), organizerDto.getFacebookLink());
        assertEquals(organizerData.getTwitterLink(), organizerDto.getTwitterLink());
        assertEquals(organizerData.getLinkedInLink(), organizerDto.getLinkedInLink());
        assertEquals(organizerData.getLogoImage(), organizerDto.getLogoImage());
        assertEquals(organizerData.getOrganizerDescription(), organizerDto.getOrganizerDescription());
        assertEquals(organizerData.getTextColor(), organizerDto.getTextColor());
        assertEquals(organizerData.getName(), organizerDto.getName());
        assertEquals(organizerData.getOrganizerPageURL(), organizerDto.getOrganizerPageURL());
        assertEquals(organizerData.getWebsite(), organizerDto.getWebsite());
        assertEquals(organizerData.getContactEmailAddress(), organizerDto.getContactEmailAddress());

        verify(roUserService, times(1)).isSuperAdminUser(any());
        verify(organizerRepository, times(1)).findById(anyLong());
    }

    @Test
    void test_updateOrganizer_throwExeption() {
        //mock

        when(organizerRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.updateOrganizer(organizerId, organizerDto, user,Boolean.FALSE));

        verify(organizerRepository, times(1)).findById(anyLong());
        
        assertEquals(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }


    @Test
    void test_updateOrganizer_organizer_and_superAdminUser_false() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock
        when(roUserService.isSuperAdminUser(any())).thenReturn(false);
        when(organizerRepoService.findByIdThrowException(organizerId)).thenReturn(organizer);
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.of(organizer));
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.of(joinUsersWithOrganizers));
        when(addressDetailsRepository.findByOrganizerId(organizerId)).thenReturn(Optional.of(addressDetails));

        //Execution
        organizerServiceImpl.updateOrganizer(organizerId, organizerDto, user, Boolean.FALSE);

        //Assertion
        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getBackgroundColor(), organizerDto.getBackgroundColor());
        assertEquals(organizerData.getFacebookLink(), organizerDto.getFacebookLink());
        assertEquals(organizerData.getTwitterLink(), organizerDto.getTwitterLink());
        assertEquals(organizerData.getLinkedInLink(), organizerDto.getLinkedInLink());
        assertEquals(organizerData.getLogoImage(), organizerDto.getLogoImage());
        assertEquals(organizerData.getOrganizerDescription(), organizerDto.getOrganizerDescription());
        assertEquals(organizerData.getTextColor(), organizerDto.getTextColor());
        assertEquals(organizerData.getName(), organizerDto.getName());
        assertEquals(organizerData.getOrganizerPageURL(), organizerDto.getOrganizerPageURL());
        assertEquals(organizerData.getWebsite(), organizerDto.getWebsite());
        assertEquals(organizerData.getContactEmailAddress(), organizerDto.getContactEmailAddress());

        verify(roUserService, times(1)).isSuperAdminUser(any());
        verify(organizerRepository, times(1)).findById(anyLong());
        verify(roJoinUserWithOrganizerService, times(1)).getByUserIdAndOrgId(anyLong(), anyLong());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_activeEventsAndTicketingPriceRangeSizeZero() {

        //setup
        List<Object[]> eventInfoDtos = new ArrayList<>();

        Object[] obj = new Object[] {"activeEvent", "testUrl", new Date(), "testLogo" ,"testLocation", new Date(), "Asia/Calcutta", false,1,1,2};
        Object[] obj2 = new Object[] {"activeEvent", "testUrl", new Date(), "testLogo" ,"testLocation", new Date(), "Asia/Calcutta", false,1,1,2};

        eventInfoDtos.add(obj);
        eventInfoDtos.add(obj2);

        Page<Object[]> pageEventInfo = new PageImpl<>(eventInfoDtos);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName(organizer.getName());
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        //mock
        when(eventRepository.findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);

        //Execution
        DataTableResponse events = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,"org2",false,isFromOrganizerProfilePage,pageSizeSearchObj);

        //Assertion
        assertTrue(events.getData().isEmpty());
        verify(eventRepository, times(1)).findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_activeEvents_with_ticketPriceRange() {

        //setup
        boolean pastEvent = false;
        boolean isRecurringEvent = true;
        Double minTicketPrice = 100d;
        Double maxTicketPrice = 1000d;

        ticketing = new Ticketing();
        ticketing.setEventEndDate(new Date());

        Object[] ticketPrice = {maxTicketPrice, minTicketPrice,new Date()};
        List<Object[]> ticketPriceRange = new ArrayList<>();
        ticketPriceRange.add(ticketPrice);

        Object[] activeEvent = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, eventDesciption, null, null, false, false,eventFormat, false};
        List<Object[]> activeEvents = new ArrayList<>();
        activeEvents.add(activeEvent);

        Page<Object[]> pageEventInfo = new PageImpl<>(activeEvents);

        EventInfoDto eventInfoDto = new EventInfoDto(event.getEventId(), event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(),
                true, (Double) ticketPriceRange.get(0)[1], (Double) ticketPriceRange.get(0)[0], Currency.USD.toString(), (Date) ticketPriceRange.get(0)[2],   eventDesciption, null, null, null, null,event.isHideEventFromOrganizer(), event.isHideEventDate(),eventFormat, event.isHideEventFromApp());
        List<EventInfoDto> finalList = new ArrayList<>();
        finalList.add(eventInfoDto);
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        //mock
        when(eventRepository.findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(ticketPriceRange);
        when(ticketingDisplayService.getEventInfoDtoWithTicketTypes(any())).thenReturn(finalList);

        //Execution
        DataTableResponse activeEventsDataResponse = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,organizerUrl, pastEvent,isFromOrganizerProfilePage,pageSizeSearchObj);

        List<EventInfoDto> activeEventsData = (List<EventInfoDto>) activeEventsDataResponse.getData();

        //Assertion
        assertTrue(activeEventsData.size() == 1);
        assertEquals(activeEventsData.get(0).getEventId().longValue(), event.getEventId());
        assertEquals(activeEventsData.get(0).getName(), event.getName());
        assertEquals(activeEventsData.get(0).getEventURL(), event.getEventURL());
        assertEquals(activeEventsData.get(0).getEventEndDate(), endDate1);
        assertEquals(activeEventsData.get(0).getEventLogo(), eventLogo);
        assertEquals(activeEventsData.get(0).getEventLocation(), eventLocation);
        assertEquals(activeEventsData.get(0).getEventStartDate(), startDate1);
        assertEquals(activeEventsData.get(0).isRecurringEvent(), isRecurringEvent);
        assertEquals(activeEventsData.get(0).getMinTicketPrice(), minTicketPrice);
        assertEquals(activeEventsData.get(0).getMaxTicketPrice(), maxTicketPrice);
        assertEquals(activeEventsData.get(0).getCurrency(), Currency.USD.toString());
        assertEquals(activeEventsData.get(0).getEventFormat(), eventFormat);

        verify(eventRepository, times(1)).findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_activeEvents_with_ticketPriceRange_null() {

        //setup
        boolean pastEvent = false;
        boolean isRecurringEvent = true;

        Object[] activeEvent = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, eventDesciption, isRecurringEvent};
        List<Object[]> activeEvents = new ArrayList<>();
        activeEvents.add(activeEvent);
        Page<Object[]> pageEventInfo = new PageImpl<>(activeEvents);

        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        //mock
        when(eventRepository.findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(null);

        //Execution
        DataTableResponse activeEventsDataResponse = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,organizerUrl, pastEvent,isFromOrganizerProfilePage,pageSizeSearchObj);

        //Assertion
        assertTrue(activeEventsDataResponse.getData().isEmpty());

        verify(eventRepository, times(1)).findActiveEventsByOrganizerURL(anyString(),anyBoolean(), any(),any());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_pastEvents_with_ticketPriceRange() {

        //setup
        boolean pastEvent = true;
        boolean isRecurringEvent = true;
        Double minTicketPrice = 100d;
        Double maxTicketPrice = 1000d;

        ticketing = new Ticketing();
        ticketing.setEventEndDate(new Date());

        Object[] ticketPrice = {maxTicketPrice, minTicketPrice,new Date()};
        List<Object[]> ticketPriceRange = new ArrayList<>();
        ticketPriceRange.add(ticketPrice);

        Object[] pastEvents = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, eventDesciption, null, null,false, false, eventFormat, false};
        List<Object[]> pastEventList = new ArrayList<>();
        pastEventList.add(pastEvents);

        EventInfoDto eventInfoDto = new EventInfoDto(event.getEventId(), event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(),
                true, (Double) ticketPriceRange.get(0)[1], (Double) ticketPriceRange.get(0)[0], Currency.USD.toString(), (Date) ticketPriceRange.get(0)[2],   eventDesciption,
                null, null, null, null,event.isHideEventFromOrganizer(), event.isHideEventDate(),eventFormat, event.isHideEventFromApp());
        List<EventInfoDto> finalList = new ArrayList<>();
        finalList.add(eventInfoDto);
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        Page<Object[]> pageEventInfo = new PageImpl<>(pastEventList);

        //mock
        when(eventRepository.findPastEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(ticketPriceRange);
        when(ticketingDisplayService.getEventInfoDtoWithTicketTypes(any())).thenReturn(finalList);

        //Execution
        DataTableResponse pastEventsDataResponse = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,organizerUrl, pastEvent,isFromOrganizerProfilePage,pageSizeSearchObj);

        List<EventInfoDto> pastEventsData = (List<EventInfoDto>) pastEventsDataResponse.getData();

        //Assertion
        assertEquals(1, pastEventsData.size());
        assertEquals(pastEventsData.get(0).getEventId().longValue(), event.getEventId());
        assertEquals(pastEventsData.get(0).getName(), event.getName());
        assertEquals(pastEventsData.get(0).getEventURL(), event.getEventURL());
        assertEquals(pastEventsData.get(0).getEventEndDate(), endDate1);
        assertEquals(pastEventsData.get(0).getEventLogo(), eventLogo);
        assertEquals(pastEventsData.get(0).getEventLocation(), eventLocation);
        assertEquals(pastEventsData.get(0).getEventStartDate(), startDate1);
        assertEquals(pastEventsData.get(0).isRecurringEvent(), isRecurringEvent);
        assertEquals(pastEventsData.get(0).getMinTicketPrice(), minTicketPrice);
        assertEquals(pastEventsData.get(0).getMaxTicketPrice(), maxTicketPrice);
        assertEquals(pastEventsData.get(0).getCurrency(), Currency.USD.toString());
        assertEquals(pastEventsData.get(0).getEventFormat(), eventFormat);

        verify(eventRepository, times(1)).findPastEventsByOrganizerURL(anyString(),anyBoolean(),any(),any());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_pastEvents_with_ticketPriceRange_null() {

        //setup
        boolean pastEvent = true;
        boolean isRecurringEvent = true;

        Object[] pastEvents = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, isRecurringEvent};
        List<Object[]> pastEventList = new ArrayList<>();
        pastEventList.add(pastEvents);

        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        Page<Object[]> pageEventInfo = new PageImpl<>(pastEventList);

        //mock
        when(eventRepository.findPastEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(null);

        //Execution
        DataTableResponse pastEventsData = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,organizerUrl, pastEvent,isFromOrganizerProfilePage,pageSizeSearchObj);

        //Assertion
        assertTrue(pastEventsData.getData().isEmpty());

        verify(eventRepository, times(1)).findPastEventsByOrganizerURL(anyString(),anyBoolean(), any(),any());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getEventsByOrganizerURLAndPastEvent_with_pastEvents() {

        //setup
        List<Object[]> eventInfoDtos = new ArrayList<>();
        Object[] obj = new Object[] { "pastEvent", "testUrl", new Date(), "testLogo" ,"testLocation" , new Date(), "Asia/Calcutta" , false,1,1,2};
        eventInfoDtos.add(obj);

        Page<Object[]> pageEventInfo = new PageImpl<>(eventInfoDtos);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName("testOrganizer");
        EventPlanConfig eventPlanConfig = new EventPlanConfig();
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("Test Plan Name");
        eventPlanConfig.setPlanConfig(planConfig);

        //mock
        when(eventRepository.findPastEventsByOrganizerURL(anyString(),anyBoolean(), any(),any())).thenReturn(pageEventInfo);
        //Execution
        DataTableResponse events = organizerServiceImpl.getEventsByOrganizerURLAndPastEvent(user,"org2",true,isFromOrganizerProfilePage,pageSizeSearchObj);

        //Assertion
        assertTrue(events.getData().isEmpty());

        verify(eventRepository, times(1)).findPastEventsByOrganizerURL(anyString(),anyBoolean(), any(),any());
    }

    @Test
    void getEventsByOrganizerURL_withTicketPriceRangeSizeZero() {

        //setup
        List<Object[]> eventInfoDtos = new ArrayList<>();
        Object[] obj = new Object[] {"testEvent", "testUrl", new Date(), "testLogo" ,"testLocation", new Date(), "Asia/Calcutta", false,1, 1, 2};
        eventInfoDtos.add(obj);

        EventDesignSettingsDto eventDesignSettingsDto = new EventDesignSettingsDto();
        eventDesignSettingsDto.setOrganizerName("testOrganizer");

        //mock
        when(eventRepository.findEventsByOrganizerURL(anyString())).thenReturn(eventInfoDtos);

        //Execution
        Set<EventInfoDto> events = organizerServiceImpl.getEventsByOrganizerURL("org2");

        //Assertion
        assertTrue(events.isEmpty());

        verify(eventRepository, times(1)).findEventsByOrganizerURL(anyString());
    }

    @Test
    void test_getEventsByOrganizerURL_with_ticketPriceRange() {

        //setup
        boolean isRecurringEvent = true;
        Double minTicketPrice = 100d;
        Double maxTicketPrice = 1000d;

        ticketing = new Ticketing();
        ticketing.setEventEndDate(new Date());

        Object[] ticketPrice = {maxTicketPrice, minTicketPrice, new Date()};
        List<Object[]> ticketPriceRange = new ArrayList<>();
        ticketPriceRange.add(ticketPrice);
        Object[] events = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, isRecurringEvent};
        List<Object[]> eventList = new ArrayList<>();
        eventList.add(events);

        //mock
        when(eventRepository.findEventsByOrganizerURL(anyString())).thenReturn(eventList);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(ticketPriceRange);

        //Execution
        Set<EventInfoDto> eventsDetail = organizerServiceImpl.getEventsByOrganizerURL(organizerUrl);

            assertEquals(eventsDetail.iterator().next().getEventId().longValue(), event.getEventId());
            assertEquals(eventsDetail.iterator().next().getName(), event.getName());
            assertEquals(eventsDetail.iterator().next().getEventURL(), event.getEventURL());
            assertEquals(eventsDetail.iterator().next().getEventEndDate(), endDate1);
            assertEquals(eventsDetail.iterator().next().getEventLogo(), eventLogo);
            assertEquals(eventsDetail.iterator().next().getEventLocation(), eventLocation);
            assertEquals(eventsDetail.iterator().next().getEventStartDate(), startDate1);
            assertTrue(eventsDetail.iterator().next().isRecurringEvent());
            assertEquals(eventsDetail.iterator().next().getMaxTicketPrice(), maxTicketPrice);
            assertEquals(eventsDetail.iterator().next().getMinTicketPrice(), minTicketPrice);

        verify(eventRepository, times(1)).findEventsByOrganizerURL(anyString());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getEventsByOrganizerURL_with_ticketPriceRange_null() {

        //setup
        boolean isRecurringEvent = true;

        Object[] events = {event.getName(), event.getEventURL(), endDate1, eventLogo, eventLocation, startDate1, event.getEquivalentTimeZone(), true, event.getEventId(), Currency.USD, isRecurringEvent};
        List<Object[]> eventList = new ArrayList<>();
        eventList.add(events);

        //mock
        when(eventRepository.findEventsByOrganizerURL(anyString())).thenReturn(eventList);
        when(ticketingTypeRepository.findTicketPriceRange(anyLong(), anyList())).thenReturn(null);

        //Execution
        Set<EventInfoDto> eventsDetail = organizerServiceImpl.getEventsByOrganizerURL(organizerUrl);

        //Assertion
        assertTrue(eventsDetail.isEmpty());

        verify(eventRepository, times(1)).findEventsByOrganizerURL(anyString());
        verify(ticketingTypeRepository, times(1)).findTicketPriceRange(anyLong(), anyList());
    }

    @Test
    void test_getListOfOrganizer_success() {

        //setup
        List<OrganizerDto> organizerDtoList = new ArrayList<>();
        organizerDtoList.add(organizerDto);

        //mock
        when(organizerRepository.findAllOrganizer()).thenReturn(organizerDtoList);

        //Execution
        List<OrganizerDto> organizerDtoData = organizerServiceImpl.getListOfOrganizer();

        //Assertion
        assertTrue(organizerDtoData.size() == 1);
        assertEquals(organizerDtoData.get(0).getBackgroundColor(), organizerDto.getBackgroundColor());
        assertEquals(organizerDtoData.get(0).getFacebookLink(), organizerDto.getFacebookLink());
        assertEquals(organizerDtoData.get(0).getTwitterLink(), organizerDto.getTwitterLink());
        assertEquals(organizerDtoData.get(0).getLinkedInLink(), organizerDto.getLinkedInLink());
        assertEquals(organizerDtoData.get(0).getLogoImage(), organizerDto.getLogoImage());
        assertEquals(organizerDtoData.get(0).getOrganizerDescription(), organizerDto.getOrganizerDescription());
        assertEquals(organizerDtoData.get(0).getTextColor(), organizerDto.getTextColor());
        assertEquals(organizerDtoData.get(0).getName(), organizerDto.getName());
        assertEquals(organizerDtoData.get(0).getOrganizerPageURL(), organizerDto.getOrganizerPageURL());
        assertEquals(organizerDtoData.get(0).getWebsite(), organizerDto.getWebsite());
        assertEquals(organizerDtoData.get(0).getContactEmailAddress(), organizerDto.getContactEmailAddress());

        verify(organizerRepository, times(1)).findAllOrganizer();
    }

    @Test
    void test_getOrganizerByOrganizerURL_success() throws Exception {

        //mock
        doReturn(organizerDto).when(organizerServiceImpl).getOrganizerByOrganizerPageURL(anyString());

        //Execution
        OrganizerDto organizerDtoData = organizerServiceImpl.getOrganizerByOrganizerURL(organizerUrl);

        //Assertion
        assertEquals(organizerDtoData.getBackgroundColor(), organizerDto.getBackgroundColor());
        assertEquals(organizerDtoData.getFacebookLink(), organizerDto.getFacebookLink());
        assertEquals(organizerDtoData.getTwitterLink(), organizerDto.getTwitterLink());
        assertEquals(organizerDtoData.getLinkedInLink(), organizerDto.getLinkedInLink());
        assertEquals(organizerDtoData.getLogoImage(), organizerDto.getLogoImage());
        assertEquals(organizerDtoData.getOrganizerDescription(), organizerDto.getOrganizerDescription());
        assertEquals(organizerDtoData.getTextColor(), organizerDto.getTextColor());
        assertEquals(organizerDtoData.getName(), organizerDto.getName());
        assertEquals(organizerDtoData.getOrganizerPageURL(), organizerDto.getOrganizerPageURL());
        assertEquals(organizerDtoData.getWebsite(), organizerDto.getWebsite());
        assertEquals(organizerDtoData.getContactEmailAddress(), organizerDto.getContactEmailAddress());

        verify(organizerServiceImpl, times(1)).getOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_getOrganizerByLogo_throwException_ORGANIZER_NOT_FOUND()  {

        //mock
        doReturn(Optional.empty()).when(organizerRepository).findOrganizerByOrganizerPageURL(anyString());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.getLogoByOrganizer(organizerUrl));

        //Assertion
        assertEquals(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(organizerServiceImpl, times(1)).getLogoByOrganizer(anyString());
    }

    @Test
    void test_getOrganizerByLogo_with_whitelabel() {
        organizer = new Organizer();
        organizer.setLogoImage("b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        //mock
        doReturn(Optional.of(organizer)).when(organizerRepository).findOrganizerByOrganizerPageURL(anyString());

        //Execution
        String logo = organizerServiceImpl.getLogoByOrganizer(organizerUrl);

        //Assertion
        verify(organizerServiceImpl, times(1)).getLogoByOrganizer(anyString());
        assertEquals(organizer.getLogoImage(),logo);

    }


    @Test
    void test_getOrganizerLogoLocation_with_logoImage() {

        //setup
        String size = "1";
        organizer.setLogoImage("b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        when(imageConfiguration.getImagePrefix()).thenReturn("Organizer");
        when(imageConfiguration.getCloudName()).thenReturn("cloudName");
        String logoImage = imageConfiguration.getImagePrefix() + size + (organizer.getLogoImage() == null ? imageConfiguration.getBlackLogo() : organizer.getLogoImage());
        when(imageConfiguration.getCloudinaryImageUrlForItem(organizer.getLogoImage(), "50")).thenReturn("Organizer1b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        //Execution
        String logo = organizerServiceImpl.getOrganizerLogoLocation(organizer, size);

        //Assertion
        assertEquals(logoImage, logo);
    }

    @Test
    void test_getOrganizerLogoLocation_with_logoImage_null() {

        //setup
        String size = "1";
        organizer.setLogoImage(null);
        when(imageConfiguration.getBlackLogo()).thenReturn("b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        when(imageConfiguration.getImagePrefix()).thenReturn("Organizer");
        when(imageConfiguration.getCloudName()).thenReturn("cloudName");
        String logoImage =  imageConfiguration.getImagePrefix() + size + (organizer.getLogoImage() == null ? imageConfiguration.getBlackLogo() : organizer.getLogoImage());
        when(imageConfiguration.getCloudinaryImageUrlForItem(anyString(), anyString())).thenReturn("Organizer1b6e4e451-e7a5-4280-939f-77d313f3f198_vxcvcxvxcvxcvjpeg");
        //Execution
        String logo = organizerServiceImpl.getOrganizerLogoLocation(organizer, size);

        //Assertion
        assertEquals(logoImage, logo);
    }

    @Test
    void test_getListOfOrganizerByUser_with_superAdminUser_true_and_joinUsersWithOrganizersList_empty() throws Exception {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();
        List<JoinUsersWithOrganizers> joinUsersWithOrganizersList = new ArrayList<>();

        //mock
        when(roUserService.isSuperAdminUser(any())).thenReturn(true);


        //Execution
        List<OrganizerDto> organizerDtosData = organizerServiceImpl.getListOfOrganizerByUser(user, event);

        //Assertion
        assertTrue(organizerDtosData.isEmpty());

    }

    @Test
    void test_getListOfOrganizerByUser_with_superAdminUser_false_and_joinUsersWithOrganizersList() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();
        joinUsersWithOrganizers.setOrganizerId(1L);
        List<JoinUsersWithOrganizers> joinUsersWithOrganizersList = new ArrayList<>();
        joinUsersWithOrganizersList.add(joinUsersWithOrganizers);
        List<Long> organizerIds = new ArrayList<>();
        organizerIds.add(1L);
        List<OrganizerDto> organizerDtoList = new ArrayList<>();
        organizerDtoList.add(organizerDto);
        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        when(joinUsersWithOrganizersRepository.findOrganizerByUserAndWhiteLabelIsNull(anyLong())).thenReturn(joinUsersWithOrganizersList);
        when(organizerRepository.findOrganiserDetailsByOrganizerIds(anyList())).thenReturn(organizerDtoList);



        //Execution
        List<OrganizerDto> organizerDtosData = organizerServiceImpl.getListOfOrganizerByUser(user, event);

        //Assertion
        assertTrue(organizerDtosData.size() == 1);
        assertEquals(organizerDtosData.get(0).getBackgroundColor(), organizerDto.getBackgroundColor());
        assertEquals(organizerDtosData.get(0).getFacebookLink(), organizerDto.getFacebookLink());
        assertEquals(organizerDtosData.get(0).getTwitterLink(), organizerDto.getTwitterLink());
        assertEquals(organizerDtosData.get(0).getLinkedInLink(), organizerDto.getLinkedInLink());
        assertEquals(organizerDtosData.get(0).getLogoImage(), organizerDto.getLogoImage());
        assertEquals(organizerDtosData.get(0).getOrganizerDescription(), organizerDto.getOrganizerDescription());
        assertEquals(organizerDtosData.get(0).getTextColor(), organizerDto.getTextColor());
        assertEquals(organizerDtosData.get(0).getName(), organizerDto.getName());
        assertEquals(organizerDtosData.get(0).getOrganizerPageURL(), organizerDto.getOrganizerPageURL());
        assertEquals(organizerDtosData.get(0).getWebsite(), organizerDto.getWebsite());
        assertEquals(organizerDtosData.get(0).getContactEmailAddress(), organizerDto.getContactEmailAddress());

        verify(joinUsersWithOrganizersRepository, times(1)).findOrganizerByUserAndWhiteLabelIsNull(anyLong());
        verify(organizerRepository, times(1)).findOrganiserDetailsByOrganizerIds(anyList());
    }

    @Test
    void test_updateOrganizerUrl_with_organizer() {

        //setup
        Organizer organizer1 = new Organizer();
        organizer1.setId(1L);

        joinUsersWithOrganizers = new JoinUsersWithOrganizers();
        //mock
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.of(organizer));
        when(organizerRepoService.findByIdThrowException(organizerId)).thenReturn(organizer);
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer1));
        Mockito.doReturn(organizerUrl).when(organizerServiceImpl).getOrganizerUrl(anyString(), anyInt(), anyInt(), anyBoolean());
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.of(joinUsersWithOrganizers));
        //Execution
        organizerServiceImpl.updateOrganizerUrl(organizerUrl, organizerId, user);

        //Assertion
        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getOrganizerPageURL(), organizerUrl);

        verify(organizerRepository, times(1)).findById(anyLong());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_updateOrganizerUrl_with_both_organizer_same() {

        //setup
        Organizer organizer1 = new Organizer();
        organizer1.setId(1L);

        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.of(organizer));
        when(organizerRepoService.findByIdThrowException(organizerId)).thenReturn(organizer1);
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer));
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.of(joinUsersWithOrganizers));
        //Execution
        organizerServiceImpl.updateOrganizerUrl(organizerUrl, organizerId, user);

        //Assertion
        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getOrganizerPageURL(), organizerUrl);

        verify(organizerRepository, times(1)).findById(anyLong());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_updateOrganizerUrl_with_organizer1_null() {

        //setup
        Organizer organizer1 = new Organizer();
        organizer1.setId(1L);

        joinUsersWithOrganizers = new JoinUsersWithOrganizers();
        //mock
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.of(organizer));
        when(organizerRepoService.findByIdThrowException(organizerId)).thenReturn(organizer);
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.of(joinUsersWithOrganizers));
        //Execution
        organizerServiceImpl.updateOrganizerUrl(organizerUrl, organizerId, user);

        //Assertion
        ArgumentCaptor<Organizer> organizerArgumentCaptor = ArgumentCaptor.forClass(Organizer.class);
        verify(organizerRepository, times(1)).save(organizerArgumentCaptor.capture());

        Organizer organizerData = organizerArgumentCaptor.getValue();
        assertEquals(organizerData.getOrganizerPageURL(), organizerUrl);

        verify(organizerRepository, times(1)).findById(anyLong());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_updateOrganizerUrl_throwException_ORGANIZER_NOT_FOUND() {

        //setup
        long organizerId = 1L;

        joinUsersWithOrganizers = new JoinUsersWithOrganizers();
        
        //mock
        when(organizerRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.updateOrganizerUrl(organizerUrl, organizerId, user));

        //Assertion
        assertEquals(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(organizerRepository, times(1)).findById(anyLong());
    }

    @Test
    void test_getOrganizerUrl_with_organizer_and_updateUrl_true_throwException_ORGANIZER_URL_ALREADY_EXIST() {

        //setup
        int count = 1;
        int urlLength = organizerUrl.length();
        boolean updateUrl = true;
        organizer = new Organizer();

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer));

        //Execution
        Exception exception = assertThrows(ConflictException.class,
                () -> organizerServiceImpl.getOrganizerUrl(organizerUrl, count, urlLength, updateUrl));

        //Assertion
        assertEquals(ConflictException.UserExceptionConflictMsg.ORGANIZER_URL_ALREADY_EXIST.getDeveloperMessage(), exception.getMessage());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_getOrganizerUrl_with_organizer_null_and_updateUrl_false() {

        //setup
        String organizerUrl = "EventOrganizer1";
        int count = 1;
        int urlLength = organizerUrl.length();
        boolean updateUrl = false;
        organizer = new Organizer();

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());

        //Execution
        String url = organizerServiceImpl.getOrganizerUrl(organizerUrl, count, urlLength, updateUrl);

        //Assertion
        assertEquals(url, organizerUrl);
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_getOrganizerUrl_with_organizer_and_updateUrl_true_throwException_ORGANIZER_NOT_FOUND() {

        //setup
        String organizerUrl = "EventOrganizer1";
        int count = 1;
        int urlLength = organizerUrl.length();
        boolean updateUrl = true;

        //mock
        Mockito.doThrow(NotFoundException.class).when(organizerRepository).findOrganizerByOrganizerPageURL(anyString());

        //Execution
        assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.getOrganizerUrl(organizerUrl, count, urlLength, updateUrl));

        //Assertion
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_getOrganizerUrl_with_updateUrl_true_and_throwException_URL_NOT_SUPPORT_SPECIAL_CHART() {

        //setup
        String organizerUrl = "EventOrganizer@123";
        int count = 1;
        int urlLength = organizerUrl.length();
        boolean updateUrl = true;

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> organizerServiceImpl.getOrganizerUrl(organizerUrl, count, urlLength, updateUrl));
        
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORT_SPECIAL_CHART.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getOrganizerByURL_throwException_ORGANIZER_NOT_FOUND() {

        //setup
        String organizerUrl = "EventOrganizer@123`/.";

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.getOrganizerByURL(organizerUrl));

        //Assertion
        assertEquals(NotFoundException.OrganizerNotFound.ORGANIZER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());
    }

    @Test
    void test_getOrganizerByURL_with_organizer() {

        //setup
        String organizerUrl = "EventOrganizer@123`/.";
        organizer.setTextColor("#D2D2D2");

        //mock
        when(organizerRepository.findOrganizerByOrganizerPageURL(anyString())).thenReturn(Optional.of(organizer));

        //Execution
        Organizer organizerData = organizerServiceImpl.getOrganizerByURL(organizerUrl);

        //Assertion
        verify(organizerRepository, times(1)).findOrganizerByOrganizerPageURL(anyString());

        assertEquals(organizerData.getId(), organizer.getId());
        assertEquals(organizerData.getTextColor(), organizer.getTextColor());
    }

    @Test
    void test_addJoinUsersWithOrganizers_with_joinUsersWithOrganizers_empty() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock


        //Execution
        organizerServiceImpl.addJoinUsersWithOrganizers(user, organizer, OrganizerRole.admin, true);

        //Assertion
//        ArgumentCaptor<JoinUsersWithOrganizers> joinUsersWithOrganizersArgumentCaptor = ArgumentCaptor.forClass(JoinUsersWithOrganizers.class);
//        verify(joinUsersWithOrganizersRepository, times(1)).save(joinUsersWithOrganizersArgumentCaptor.capture());
//
//        JoinUsersWithOrganizers joinUsersWithOrganizersData = joinUsersWithOrganizersArgumentCaptor.getValue();
//        assertEquals(joinUsersWithOrganizersData.getOrganizerId(), organizer.getId());
//        assertEquals(joinUsersWithOrganizersData.getUserId(), user.getUserId().longValue());

        verify(joinUsersWithOrganizersRepository, times(1)).findJoinOrgByOrgId(anyLong());
    }

    @Test
    void test_addJoinUsersWithOrganizers_with_joinUsersWithOrganizers() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock


        //Execution
        organizerServiceImpl.addJoinUsersWithOrganizers(user, organizer, OrganizerRole.admin, true);

        //Assertion
        verify(joinUsersWithOrganizersRepository, times(1)).findJoinOrgByOrgId(anyLong());
    }


    @Test
    void test_deleteJoinUsersWithOrganizers_with_joinUsersWithOrganizers() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.of(joinUsersWithOrganizers));

        //Execution
        organizerServiceImpl.deleteJoinUsersWithOrganizers(user, organizer);

        //Assertion
        verify(roJoinUserWithOrganizerService, times(1)).getByUserIdAndOrgId(anyLong(), anyLong());
        verify(joinUsersWithOrganizersRepository, times(1)).delete(any());
    }

    @Test
    void test_deleteJoinUsersWithOrganizers_without_joinUsersWithOrganizers() {

        //setup
        joinUsersWithOrganizers = new JoinUsersWithOrganizers();

        //mock
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.empty());

        //Execution
        organizerServiceImpl.deleteJoinUsersWithOrganizers(user, organizer);

        //Assertion
        verify(roJoinUserWithOrganizerService, times(1)).getByUserIdAndOrgId(anyLong(), anyLong());
    }


    @Test
    void test_sendContactEmailToOrganizer_user_null() {

        //setup
        contactDto = new ContactDto();

        //Execution
        Exception exception = assertThrows(AuthorizationException.class,
                () -> organizerServiceImpl.sendContactEmailToOrganizer(null, event, contactDto, organizerUrl));
        
        assertEquals(NOT_AUTHORIZE, exception.getMessage());
    }

    @Test
    void test_sendContactEmailToOrganizer_user_null_and_contact_email() {

        //setup
        contactDto = new ContactDto();
        contactDto.setName(FIRST_NAME);
        contactDto.setEmail(EMAIL);
        contactDto.setMessage("message");

        organizer.setContactEmailAddress("<EMAIL>");

        //mock
        Mockito.doReturn(organizer).when(organizerServiceImpl).getOrganizerByURL(anyString());
        doNothing().when(sendGridMailPrepareService).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);

        //Execution
        organizerServiceImpl.sendContactEmailToOrganizer(null, event, contactDto, organizerUrl);

        //Assertion
        verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
    }

    @Test
    void test_sendContactEmailToOrganizer_user_throwException() {

        //setup
        user.setEmail("<EMAIL>");
        contactDto = new ContactDto();
        contactDto.setName(FIRST_NAME);
        contactDto.setEmail(EMAIL);
        contactDto.setMessage("message");

        organizer.setContactEmailAddress("<EMAIL>");

        //mock
        Mockito.doReturn(organizer).when(organizerServiceImpl).getOrganizerByURL(anyString());
        Mockito.doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);

        //Execution
        organizerServiceImpl.sendContactEmailToOrganizer(null, event, contactDto, organizerUrl);

        //Assertion
        verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
    }

    @Test
    void test_sendContactEmailToOrganizer_user_and_organizer_contactEmailAddress_null() {

        //setup
        user.setEmail("<EMAIL>");
        contactDto = new ContactDto();
        contactDto.setName(FIRST_NAME);
        contactDto.setEmail(EMAIL);
        contactDto.setMessage("message");

        organizer = new Organizer();
        organizer.setContactEmailAddress(null);

        //mock
        Mockito.doReturn(organizer).when(organizerServiceImpl).getOrganizerByURL(anyString());
        doNothing().when(sendGridMailPrepareService).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);

        //Execution
        organizerServiceImpl.sendContactEmailToOrganizer(user, event, contactDto, organizerUrl);

        //Assertion
        verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
    }

    @Test
    void test_sendContactEmailToOrganizer_user_firstName_empty_and_organizer_null() {

        //setup
        user.setEmail("<EMAIL>");
        user.setFirstName("");
        contactDto = new ContactDto();
        contactDto.setName(FIRST_NAME);
        contactDto.setEmail(EMAIL);
        contactDto.setMessage("message");

        //mock
        Mockito.doReturn(null).when(organizerServiceImpl).getOrganizerByURL(anyString());
        doNothing().when(sendGridMailPrepareService).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);


        //Execution
        organizerServiceImpl.sendContactEmailToOrganizer(user, event, contactDto, organizerUrl);

        //Assertion
        verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMailToOrganizer(any(), any(), any(), anySet());
    }

    @Test
    void test_checkUserIsAllowToUpdateElseNotThenThrowException_throwException_USER_NOT_ALLOW_TO_UPDATE() {

        //mock
        when(roJoinUserWithOrganizerService.getByUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.empty());
        when(roJoinUserWithOrganizerService.getByApiUserIdAndOrgId(anyLong(), anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> organizerServiceImpl.checkUserIsAllowToUpdateElseNotThenThrowException(user, organizer));

        //Assertion
        assertEquals(NotFoundException.OrganizerNotFound.USER_NOT_ALLOW_TO_UPDATE.getDeveloperMessage(), exception.getMessage());
        verify(roJoinUserWithOrganizerService, times(1)).getByUserIdAndOrgId(anyLong(), anyLong());
    }
}