package com.accelevents.services.impl;

import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.repositories.AttendeeCheckInRecordsRepository;
import com.accelevents.repositories.EventRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.PaymentMethod;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EventBillingServiceImplTest {

    @Spy
    @InjectMocks
    private EventBillingServiceImpl eventBillingServiceImpl = new EventBillingServiceImpl();
    @Mock
    private StripeTransactionService stripeTransactionService;

    @Mock
    private EventService eventService;
    @Mock
    private ROEventService roEventService;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private BillingEventsService billingEventsService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventBillingAddOnsService eventBillingAddOnsService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private TextMessageUtils textMessageUtils;

    @Mock
    private StripeConfiguration stripeConfiguration;

    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;

    @Mock
    private AttendeeCheckInRecordsRepository attendeeCheckInRecordsRepository;

    @Mock
    private AttendeeProfileService attendeeProfileService;

    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;

    @Mock
    private StripePaymentService stripePaymentService;

    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

    @Mock
    private HubspotEventService hubSpotEventService;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private IntercomDetailsService intercomDetailsService;

    private Event event;
    private Event event1;
    private User user;
    private List<Object[]> stripeTransactionList = new ArrayList<>();
    private BillingEvents billingEvents;
    private EventBillingAddOns eventBillingAddOns;
    private Staff staff;
    private EventBillingInfo eventBillingInfo;
    private Object[] obj;
    private AttendeeCheckInRecords attendeeCheckInRecords;
    private StripeCreditCardDto stripeCreditCardDto;
    private Stripe stripe;
    private StripeTransaction stripeTransaction;



    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        event1 = EventDataUtil.getEvent();
        event1.setEventFormat(EventFormat.IN_PERSON);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());
        user.setEmail("<EMAIL>");

        obj = new Object[30];
        obj[0] = 1L;
        obj[1] = null;
        obj[2] = new Date();
        obj[3] = "test";
        obj[4] = "<EMAIL>";
        obj[5] = "milan";
        obj[6] = new Timestamp(0);
        obj[7] = 2;
        obj[8] = 2;
        obj[9] = 2;
        obj[10] = 2;
        obj[11] = new Timestamp(2);
        obj[12] = 10;
        obj[13] = new Timestamp(10);
        obj[14] = 10;
        obj[15] = 2;

        billingEvents = new BillingEvents();
        billingEvents.setId(1);
        billingEvents.setEventId(1L);
        billingEvents.setComment("test");
        billingEvents.setPaidDate(String.valueOf(new Date()));
        billingEvents.setPaymentStatus("Paid");
        billingEvents.setTotalAeRevenue(1000D);
        billingEvents.setRecordStatus(RecordStatus.CREATE);

        eventBillingAddOns = new EventBillingAddOns();
        eventBillingAddOns.setEvent(event);
        eventBillingAddOns.setEventId(1L);
        eventBillingAddOns.setPaymentStatus(PaymentStatus.PAID);
        eventBillingAddOns.setNotes("test");
        eventBillingAddOns.setAmount(100);
        eventBillingAddOns.setAddOnType(AddOnType.DISCOUNT);
        eventBillingAddOns.setDiscountType(DiscountType.PERCENTAGE);

        staff = new Staff();
        staff.setUser(user);
        staff.setEvent(event);
        staff.setRole(StaffRole.whitelabeladmin);
        staff.setId(1);
        staff.setEventId(1L);
        staff.setUserId(1L);

        eventBillingInfo = new EventBillingInfo();
        eventBillingInfo.setEmail("<EMAIL>");
        eventBillingInfo.setEventId(event.getEventId());
        eventBillingInfo.setEventUrl(event.getEventURL());
        eventBillingInfo.setStripeKey("123_stripe_id");
        eventBillingInfo.setUserId(user.getUserId());
        eventBillingInfo.setTokenOrIntentId("tokenOrIntentId_mock");

        attendeeCheckInRecords = new AttendeeCheckInRecords();
        attendeeCheckInRecords.setEventId(1L);
        attendeeCheckInRecords.setUserId(1L);
        attendeeCheckInRecords.setId(1L);
        attendeeCheckInRecords.setPaymentStatus(EnumUserSessionPaymentStatus.PAID);

        stripeCreditCardDto = new StripeCreditCardDto();
        stripeCreditCardDto.setId("1");
        stripeCreditCardDto.setCardType("debit");
        stripeCreditCardDto.setLast4("1234");
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setExpmonth(12);
        stripeCreditCardDto.setExpyear(2026);

        stripe = new Stripe();
        stripe.setEvent(event);
        stripe.setAccessToken("sk_test_abSrkfVraozPahMjTuzWM0ya");
        stripe.setLivemode(false);
        stripe.setRefreshToken("rt_9nlm7w616mnKAvHg7akrapUVJljLNbab0Tlvx0atLgfQtQ1c");
        stripe.setTokenType("bearer");
        stripe.setStripePublishableKey("pk_test_Ifs6ZfB0PKZz1qRVW5HmYZTL");
        stripe.setStripeUserId("acct_19UFGpE8xoUg8dJF");
        stripe.setScope("read_write");
        stripe.setCode("ac_9nsFr39zhFLhT56HXxB1dNqop4HgFHnn");
        stripe.setAccountSource("Oauth");
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        stripeTransaction = new StripeTransaction();
        stripeTransaction.setStripecustomerid("cx_millan");
    }

    @Test
    void test_geEventBillingDetail_eventCanNotFindPaymentDetail_exception(){

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventBillingServiceImpl.geEventBillingDetail(event,user));

        assertEquals(NotAcceptableException.EventExceptionMsg.EVENT_CAN_NOT_FIND_PAYMENT_DETAILS.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_geEventBillingDetail(){

        //setUp
        stripeTransactionList.add(obj);

        event.setTicketingId(0L);
        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(anyLong())).thenReturn(event);
        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);
        when(eventTicketsRepoService.getSumOfAEFeesOfPaidTicketsToBeCharge(anyLong(), anyDouble())).thenReturn(BigDecimal.valueOf(10));
        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");
        when(stripeConfiguration.getPUBLIC_KEY()).thenReturn("123_stripe_id");
        when(staffService.findFirstByEventId(any())).thenReturn(staff);

        //Execution
        EventBillingInfo eventBillingInfoResult = eventBillingServiceImpl.geEventBillingDetail(event, user);

        assertEquals(eventBillingInfo.getEmail(),eventBillingInfoResult.getEmail());
        assertEquals(eventBillingInfo.getEventId(),eventBillingInfoResult.getEventId());
        assertEquals(eventBillingInfo.getEventUrl(),eventBillingInfoResult.getEventUrl());
        assertEquals(eventBillingInfo.getStripeKey(),eventBillingInfoResult.getStripeKey());

    }

    @Test
    void test_getEventBillingInfo_stripeTransaction_null(){

        when(stripeTransactionService.getAllStripeTransactions(anyString(), any(), anyString(), any(), any(), anyString())).thenReturn(null);

        DataTableResponse eventBillingInfo = eventBillingServiceImpl.getEventBillingInfo("tab", 10, 0, "test", "10/01/2021", "10/01/2022", "create");

        assertNull(eventBillingInfo.getData());
    }

    @Test
    void test_getEventBillingInfo_stripeTransaction(){

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        Object[] obj2 = Arrays.copyOf(obj, 30);
        Object[] obj3 = Arrays.copyOf(obj, 30);
        Object[] obj4 = Arrays.copyOf(obj, 30);
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);
        obj2[0] = 2L;
        obj2[3] = "EVENT_BILLING_MARKED_VOID";
        stripeTransactionList.add(obj2);
        obj3[3] = "EVENT_BILLING_CHARGE_FAILED";
        stripeTransactionList.add(obj3);
        obj4[3] = null;
        stripeTransactionList.add(obj4);

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);


        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);
        when(roEventService.getEventByIdIfPresentOrNull(2L)).thenReturn(event1);
        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");

        when(staffService.findFirstByEventId(any())).thenReturn(staff);


        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());

        when(stripeTransactionService.getAllStripeTransactions(anyString(), any(), anyString(), any(), any(), anyString())).thenReturn((pageImpl));

        //Execution
        DataTableResponse eventBillingInfo = eventBillingServiceImpl.getEventBillingInfo("tab", 10, 0, "test", "10/01/2021", null, "create");

        assertEquals(dataTableResponse.getData().size(),eventBillingInfo.getData().size());
    }

    @Test
    void test_isAllowToDisplay_usingCollectedAmount(){

        eventBillingInfo.setTotalAmountOwed(new BigDecimal(100));
        eventBillingInfo.setTotalAmountCollected(new BigDecimal(95));

        boolean allowToDisplay = eventBillingServiceImpl.isAllowToDisplay(eventBillingInfo);
        assertTrue(allowToDisplay);
    }

    @Test
    void test_isAllowToDisplay_usingStatus(){

        eventBillingInfo.setTotalAmountOwed(new BigDecimal(1));
        eventBillingInfo.setTotalAmountCollected(new BigDecimal(2));
        eventBillingInfo.setStatus("PAID");

        boolean allowToDisplay = eventBillingServiceImpl.isAllowToDisplay(eventBillingInfo);
        eventBillingInfo.setStatus("VOID");
        boolean allowToDisplay1 = eventBillingServiceImpl.isAllowToDisplay(eventBillingInfo);
        assertTrue(allowToDisplay);
        assertTrue(allowToDisplay1);
    }

    @Test
    void test_calculationsForEventBillingForDataFix(){

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);


        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);




        when(attendeeCheckInRecordsRepository.findRecordsHavingStatusUnpaid(anyLong())).thenReturn(Collections.singletonList(attendeeCheckInRecords));


        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());



        //Execution
        EventBillingInfo eventBillingInfo = eventBillingServiceImpl.calculationsForEventBillingForDataFix(obj1);

        assertEquals(obj1[0],eventBillingInfo.getEventId());

    }


    @Test
    void test_getLinkedCardByCarIdFromStripe(){


        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(stripeTransaction);
        when(stripeConfiguration.getAPI_KEY()).thenReturn("As_adadfDs");

        //Execution
        StripeCreditCardDto linkedCardByCarIdFromStripe = eventBillingServiceImpl.getLinkedCardByCarIdFromStripe(event, user);

        assertNull(linkedCardByCarIdFromStripe);

    }

    @Test
    void test_getCardDetailsWhomActivatedModule(){



        when(stripeConfiguration.getAPI_KEY()).thenReturn("As_adadfDs");

        StripeCreditCardDto cardDetailsWhomActivatedModule = eventBillingServiceImpl.getCardDetailsWhomActivatedModule(stripeTransaction);
        assertNull(cardDetailsWhomActivatedModule);
    }

    @Test
    void test_createEventBillingCharges_withException(){

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = null;
        stripeTransactionList.add(obj1);

        eventBillingInfo = new EventBillingInfo();
        eventBillingInfo.setEmail("<EMAIL>");
        eventBillingInfo.setEventId(event.getEventId());
        eventBillingInfo.setEventUrl(event.getEventURL());
        eventBillingInfo.setStripeKey("123_stripe_id");
        eventBillingInfo.setTokenOrIntentId("test_token");
        eventBillingInfo.setAmount(200D);
        eventBillingInfo.setTotalAmountOwed(new BigDecimal(100));
        eventBillingAddOns.setAmount(30D);
        billingEvents.setPaymentStatus("UNPAID");

        stripeTransaction.setId(1L);
        stripeTransaction.setPaymentGateway("STRIPE");
        stripeTransaction.setSource(StripeTransactionSource.PRE_PAYMENT);
        stripeTransaction.setDesciption("test");
        stripeTransaction.setAmount(10D);
        stripeTransaction.setEvent(event);
        stripeTransaction.setStripecardid("1234");
        stripeTransaction.setStripecustomerid("customer_12345");

        Charge charges = new Charge();
        charges.setId("123");
        charges.setAmount(100L);

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");

        when(staffService.findFirstByEventId(any())).thenReturn(staff);




        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());



        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(roUserService.getUserById(any())).thenReturn(Optional.ofNullable(user));
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(stripeTransaction);
        doNothing().when(billingEventsService).updateBillingEvents(any(), any(), any(), any(), any());
        doNothing().when(hubSpotEventService).updateHubspotEventDetailsWithoutAsync(any());
        when(roEventService.findEventByEventId(anyLong())).thenReturn(event);

        List<EventBillingInfo> eventBillingCharges = eventBillingServiceImpl.createEventBillingCharges(Collections.singletonList(eventBillingInfo));
        assertEquals(Collections.emptyList(),eventBillingCharges);
    }

    @Test
    void test_updatePrePayment_notFoundException(){

        EventBillingPrePaymentDto eventBillingPrePaymentDto = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto.setEventId(1L);
        eventBillingPrePaymentDto.setPrePaymentId(1L);

        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(stripeTransactionService.findByIdAndEventId(anyLong(), any())).thenReturn(null);

        Exception exception = assertThrows(NotFoundException.class,
                () -> eventBillingServiceImpl.updatePrePayment(1L,eventBillingPrePaymentDto,1L));

        assertEquals(NotFoundException.NotFound.PRE_PAYMENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_updatePrePayment_notAcceptableException(){

        EventBillingPrePaymentDto eventBillingPrePaymentDto = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto.setEventId(1L);
        eventBillingPrePaymentDto.setPrePaymentId(1L);

        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(stripeTransactionService.findByIdAndEventId(anyLong(), any())).thenReturn(stripeTransaction);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventBillingServiceImpl.updatePrePayment(1L,eventBillingPrePaymentDto,1L));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_SUPPORT_ACCEPT_CASH_CHECK_OR_INVOICE_AS_PRE_PAYMENT_FOR_NOW.getDeveloperMessage(), exception.getMessage());
    }


    @Test
    void test_updatePrePayment(){

        EventBillingPrePaymentDto eventBillingPrePaymentDto = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto.setEventId(1L);
        eventBillingPrePaymentDto.setPrePaymentId(1L);
        eventBillingPrePaymentDto.setSource(StripeTransactionSource.PRE_PAYMENT);
        eventBillingPrePaymentDto.setPaymentGateway(EnumPaymentGateway.CASH);

        EventBillingPrePaymentDto eventBillingPrePaymentDto1 = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto1.setEventId(1L);
        eventBillingPrePaymentDto1.setPrePaymentId(1L);
        eventBillingPrePaymentDto1.setSource(StripeTransactionSource.PRE_PAYMENT);
        eventBillingPrePaymentDto1.setPaymentGateway(EnumPaymentGateway.CHECK);

        EventBillingPrePaymentDto eventBillingPrePaymentDto2 = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto2.setEventId(1L);
        eventBillingPrePaymentDto2.setPrePaymentId(1L);
        eventBillingPrePaymentDto2.setSource(StripeTransactionSource.PRE_PAYMENT);
        eventBillingPrePaymentDto2.setPaymentGateway(EnumPaymentGateway.INVOICE);


        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(stripeTransactionService.findByIdAndEventId(anyLong(), any())).thenReturn(stripeTransaction);
        when(roUserService.getUserById(anyLong())).thenReturn(Optional.ofNullable(user));

        eventBillingServiceImpl.updatePrePayment(1L,eventBillingPrePaymentDto,1L);
        eventBillingServiceImpl.updatePrePayment(1L,eventBillingPrePaymentDto1,1L);
        eventBillingServiceImpl.updatePrePayment(1L,eventBillingPrePaymentDto2,1L);
        assertTrue(true);
    }

    @Test
    void test_prePaymentForEvent(){

        EventBillingPrePaymentDto eventBillingPrePaymentDto = new EventBillingPrePaymentDto();
        eventBillingPrePaymentDto.setEventId(1L);
        eventBillingPrePaymentDto.setPrePaymentId(1L);
        eventBillingPrePaymentDto.setSource(StripeTransactionSource.PRE_PAYMENT);
        eventBillingPrePaymentDto.setPaymentGateway(EnumPaymentGateway.CASH);

        when(roEventService.getEventById(anyLong())).thenReturn(event);

        when(roUserService.getUserById(anyLong())).thenReturn(Optional.ofNullable(user));

        when(roEventService.getEventById(anyLong())).thenReturn(event);

        eventBillingServiceImpl.prePaymentForEvent(eventBillingPrePaymentDto,1L);
        assertTrue(true);

    }

    @Test
    void test_getPrePaymentForEvent(){

        stripeTransaction.setId(1L);
        stripeTransaction.setPaymentGateway("STRIPE");
        stripeTransaction.setSource(StripeTransactionSource.PRE_PAYMENT);
        stripeTransaction.setDesciption("test");
        stripeTransaction.setAmount(10D);
        stripeTransaction.setEvent(event);

        when(stripeTransactionService.findById(anyLong())).thenReturn(stripeTransaction);
        EventBillingPrePaymentDto prePaymentForEvent = eventBillingServiceImpl.getPrePaymentForEvent(1L);

        assertEquals(1L,prePaymentForEvent.getEventId());

    }

    @Test
    void test_deletePrePaymentById_prePaymentNotFound(){

        when(stripeTransactionService.findByIdAndEventId(anyLong(), any())).thenReturn(null);

        Exception exception = assertThrows(NotFoundException.class,
                () -> eventBillingServiceImpl.deletePrePaymentById(1L,event));

        assertEquals(NotFoundException.NotFound.PRE_PAYMENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_deletePrePaymentById(){
        when(stripeTransactionService.findByIdAndEventId(anyLong(), any())).thenReturn(stripeTransaction);

        eventBillingServiceImpl.deletePrePaymentById(1L,event);
        assertTrue(true);
    }

    @Test
    void test_getListOfPrePayment(){

        stripeTransaction.setId(1L);
        stripeTransaction.setPaymentGateway("STRIPE");
        stripeTransaction.setSource(StripeTransactionSource.PRE_PAYMENT);
        stripeTransaction.setDesciption("test");
        stripeTransaction.setAmount(10D);
        stripeTransaction.setEvent(event);

        when(stripeTransactionService.findByEventAndSource(any(), any())).thenReturn(Collections.singletonList(stripeTransaction));

        List<EventBillingPrePaymentDto> listOfPrePayment = eventBillingServiceImpl.getListOfPrePayment(event);

       listOfPrePayment.forEach(e->{
           assertEquals(stripeTransaction.getEvent().getEventId(),e.getEventId());
           assertEquals(stripeTransaction.getPaymentGateway(),e.getPaymentGateway().value());
       });

    }

    @Test
    void test_createEventBillingCharges() throws StripeException {

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = null;
        stripeTransactionList.add(obj1);

        eventBillingInfo = new EventBillingInfo();
        eventBillingInfo.setEmail("<EMAIL>");
        eventBillingInfo.setEventId(event.getEventId());
        eventBillingInfo.setEventUrl(event.getEventURL());
        eventBillingInfo.setStripeKey("123_stripe_id");
        eventBillingInfo.setTokenOrIntentId("test_token");
        eventBillingInfo.setAmount(200D);
        eventBillingInfo.setTotalAmountOwed(new BigDecimal(100));
        eventBillingAddOns.setAmount(30D);
        billingEvents.setPaymentStatus("UNPAID");

        stripeTransaction.setId(1L);
        stripeTransaction.setPaymentGateway("STRIPE");
        stripeTransaction.setSource(StripeTransactionSource.PRE_PAYMENT);
        stripeTransaction.setDesciption("test");
        stripeTransaction.setAmount(10D);
        stripeTransaction.setEvent(event);
        stripeTransaction.setStripecardid("1234");
        stripeTransaction.setStripecustomerid("customer_12345");

        Charge charges = new Charge();
        charges.setId("123");
        charges.setAmount(100L);

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);
        when(roEventService.getEventByIdIfPresentOrNull(2L)).thenReturn(event1);
        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);
        when(eventTicketsRepoService.getSumOfAEFeesOfPaidTicketsToBeCharge(anyLong(), anyLong())).thenReturn(BigDecimal.valueOf(10));
        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");
        when(stripeConfiguration.getPUBLIC_KEY()).thenReturn("123_stripe_id");
        when(staffService.findFirstByEventId(any())).thenReturn(staff);

        when(attendeeCheckInRecordsRepository.findRecordsHavingStatusUnpaid(anyLong())).thenReturn(Collections.singletonList(attendeeCheckInRecords));


        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());

        when(stripeTransactionService.getAllStripeTransactions(anyString(), any(), anyString(), any(), any(), anyString())).thenReturn((pageImpl));

        when(roEventService.getEventById(anyLong())).thenReturn(event);
        when(roUserService.getUserById(any())).thenReturn(Optional.ofNullable(user));
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(stripeTransaction);
        doNothing().when(billingEventsService).updateBillingEvents(any(), any(), any(), any(), any());
        doNothing().when(hubSpotEventService).updateHubspotEventDetailsWithoutAsync(any());
        when(roEventService.findEventByEventId(anyLong())).thenReturn(event);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyString(), anyLong(), anyString(), any(), anyString(), anyString())).thenReturn(charges);
        when(stripePaymentService.retrieveCardById(anyString(), anyString(), anyString())).thenReturn(stripeCreditCardDto);
        doNothing().when(intercomDetailsService).sendEventBillingSucceedEmail(any(), any(), any());
        doNothing().when(intercomDetailsService).sendEventBillingWithReviewRequestEmail(any(), any(), any());

        List<EventBillingInfo> eventBillingCharges = eventBillingServiceImpl.createEventBillingCharges(Collections.singletonList(eventBillingInfo));
        assertEquals(Collections.emptyList(),eventBillingCharges);
    }

    @Test
    void test_eventBillingCheckout() throws StripeException {

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);
        staff.setStripeCustomerId("test_customerId");

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(Collections.singletonList(obj1));
        when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(staff);
        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");

        when(stripeConfiguration.getAPI_KEY()).thenReturn("API_KEY");
        when(staffService.findFirstByEventId(any())).thenReturn(staff);
        when(stripePaymentService.addCardToCustomerUsingSetupIntentForSubscription(anyString(), anyString(),
                anyString())).thenReturn("test_cardId");




        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());



        StripeCreditCardDto stripeCreditCardDto = eventBillingServiceImpl.eventBillingCheckout(event, user, eventBillingInfo);

        assertEquals(stripeCreditCardDto.getLast4(),stripeCreditCardDto.getLast4());

    }

    @Test
    void test_eventBillingCheckout_customerIdNull() throws StripeException {

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);

        CustomerCardDto customerCardDto = new CustomerCardDto("test_customerId","test_cardId");
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setId("1L");

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(Collections.singletonList(obj1));
        when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(staff);
        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");

        when(staffService.findFirstByEventId(any())).thenReturn(staff);
        when(stripeConfiguration.getAPI_KEY()).thenReturn("stripe_api_key");
        when(stripePaymentService.createCustomerAndAddCardToCustomer(anyString(),anyString(),
                anyMap(), anyString(),anyString(), anyBoolean(), anyBoolean())).thenReturn(customerCardDto);
        when(stripePaymentService.retrieveDefaultPaymentIdOfCustomerUsingSetupIntent(anyString(),
                anyString(), anyString())).thenReturn(paymentMethod);




        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());


//
//        Map<String, Object> metadata = new HashMap<>();
//        metadata.put("description", "event:" + event.getName() + " user email:" + user.getEmail());
//        when(stripePaymentService.createCustomerAndAddCardToCustomer("stripe_api_key", eventBillingInfo.getTokenOrIntentId(),
//                metadata, user.getEmail(),event.getName(), true, false)).thenReturn(customerCardDto);

        StripeCreditCardDto stripeCreditCardDto = eventBillingServiceImpl.eventBillingCheckout(event, user, eventBillingInfo);

        assertEquals(stripeCreditCardDto.getLast4(),stripeCreditCardDto.getLast4());

    }

    @Test
    void test_eventsBillingPaymentRequest(){

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);

        eventBillingInfo.setTokenOrIntentId("test_token");

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);
        when(billingEventsService.findBillingEventsByEventId(anyLong())).thenReturn(billingEvents);

        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("first_event");

        when(staffService.findFirstByEventId(any())).thenReturn(staff);




        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());



        when(roEventService.getEventListByIds(anyList())).thenReturn(Collections.singletonList(event));
        when(roUserService.getUserById(anyLong())).thenReturn(Optional.ofNullable(user));
        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(Collections.singletonList(obj1));
        when(textMessageUtils.getEventBaseUrl(event)).thenReturn("test_message");

        eventBillingServiceImpl.eventsBillingPaymentRequest(Collections.singletonList(eventBillingInfo));

        assertTrue(true);
    }

    @Test
    void test_markEventsAsPaid(){

        //setUp
        Object[] obj1 = Arrays.copyOf(obj, 30);
        obj1[12] = 0;
        obj1[3] = "EVENT_BILLING_PAID_SUCCESS";
        stripeTransactionList.add(obj1);

        EventBillingUpdate eventBillingUpdate = new EventBillingUpdate();
        eventBillingUpdate.setPaidType(MarkAsPaidType.PAID);
        eventBillingUpdate.setEventIds(Collections.singletonList(1L));

        eventBillingInfo.setTokenOrIntentId("test_token");

        billingEvents.setPaymentStatus("UNPAID");

        DataTableResponse dataTableResponse = new DataTableResponse();
        dataTableResponse.setData(stripeTransactionList);

        Pageable pageable = PageRequest.of(1, 10);

        when(stripeTransactionService.getStripeTransactionsRecordByEventId(anyLong())).thenReturn(stripeTransactionList);

        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);
        when(transactionFeeConditionalLogicService.getBillingAmount(any())).thenReturn(10D);


        when(eventBillingAddOnsService.findAllByEvent(any())).thenReturn(Collections.singletonList(eventBillingAddOns));
        when(ticketingHelperService.isTicketingModuleActivated(any())).thenReturn(Boolean.TRUE);


        when(staffService.findFirstByEventId(any())).thenReturn(staff);




        Page<Object[]> pageImpl = new PageImpl<>(stripeTransactionList,pageable, stripeTransactionList.size());


        when(roEventService.findEventByEventId(anyLong())).thenReturn(event);
        when(userService.findById(anyLong())).thenReturn(user);


        eventBillingServiceImpl.markEventsAsPaid(eventBillingUpdate);
        assertTrue(true);

    }


}
