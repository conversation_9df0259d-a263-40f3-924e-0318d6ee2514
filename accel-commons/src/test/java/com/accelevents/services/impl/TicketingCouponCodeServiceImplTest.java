package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.TicketPriceDetails;
import com.accelevents.dto.TotalTicketsAndTotalTicketPriceDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.TicketType;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.TicketingOrderRepoService;
import com.accelevents.ticketing.dto.TicketingAppliedCouponDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.accelevents.utils.Constants.COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE;
import static com.accelevents.utils.Constants.COUPON_APPLIED_SUCCESS_MSG;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingCouponCodeServiceImplTest {

    @Spy
    @InjectMocks
    private  TicketingCouponCodeServiceImpl ticketingCouponCodeServiceImpl = new TicketingCouponCodeServiceImpl();
    @Mock
    private TicketingOrderRepoService ticketingOrderRepoService;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private TicketingCouponServiceImpl ticketingCouponServiceImpl;

    @Mock
    private ROEventService roEventService;

    @Mock
    private TicketingStatisticsService ticketingStatisticsService;

    @Mock
    private StripeService stripeService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock SalesTaxService salesTaxService;

    private TicketingOrder ticketingOrder;
    private TicketingOrderManager ticketingOrderManager;
    private TicketingCoupon ticketingCoupon;
    private TicketingType ticketingType;
    private Ticketing ticketing;
    private Event event;
    private TicketPriceDetails ticketPriceDetails;
    private StripeDTO stripeDTO;
    private TotalTicketsAndTotalTicketPriceDto totalTicketsAndTotalTicketPriceDto;

    private String startDate = "01/01/2030 00:00:00";
    private String EventUrl = "asd";
    private String couponCode = "couponCode";
    private String couponCode1 = "CouponCode";
    private Date startDate1 = new Date(startDate);
    private long usedPerUser = 1L;
    private Long orderId = 1L;
    private long numberOfUsageLeft =2L;
    private int totalTickets = 2;
    private long discountToBeAppliedOn = 1L;
    private int passFeeToBuyerCount = 1;
    private double price = 100d;
	private long recurringEventId=1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketingCoupon = EventDataUtil.getTicketingCoupon();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        ticketingType = EventDataUtil.getTicketingType(event);
        ticketingOrderManager = EventDataUtil.getTicketingOrderManager();
        stripeDTO = EventDataUtil.getStripeDTO();
        totalTicketsAndTotalTicketPriceDto = EventDataUtil.getTotalTicketsAndTotalTicketPriceDto();
    }

    @Test
    void test_removeCouponFromTicketingOrder_success() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        when(roEventService.getEventByURL(any())).thenReturn(event);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(ticketingOrderManagerList);

        //Execution
        ticketingCouponCodeServiceImpl.removeCouponFromTicketingOrder(EventUrl, orderId);

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, times(1)).save(ticketingOrderManagerArgumentCaptor.capture());

        TicketingOrderManager actualData = ticketingOrderManagerArgumentCaptor.getValue();
        assertEquals(0, actualData.getNumberOfDiscountedTicket());

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder actualData1 = ticketingOrderArgumentCaptor.getValue();
        assertNull(actualData1.getTicketingCoupon());
    }

    @Test
    void test_removeCouponFromTicketingOrder_throwExceptionCouponIsNotAppliedToThisOrder() {

        //setup
        ticketingOrderManager.setNumberOfDiscountedTicket(0);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingOrder.setTicketingCoupon(null);

        //mock
        when(roEventService.getEventByURL(any())).thenReturn(event);
        when(ticketingOrderService.findByidAndEventid(anyLong(),any())).thenReturn(ticketingOrder);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.removeCouponFromTicketingOrder(EventUrl, orderId));

        //Assert
        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, times(1)).save(ticketingOrderManagerArgumentCaptor.capture());
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_IS_NOT_APPLIED_TO_THIS_ORDER.getDeveloperMessage(), exception.getMessage());

        TicketingOrderManager actualData = ticketingOrderManagerArgumentCaptor.getValue();
        assertEquals(0, actualData.getNumberOfDiscountedTicket());

    }
    @Test
    void test_allTicketsAreOfDonationType_success_with_notAllTicketDonationType() {

        //setup
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        boolean ticketTypeIsDonation = ticketingCouponCodeServiceImpl.allTicketsAreOfDonationType(ticketingOrderManagerList, ticketingCoupon);
        assertFalse(ticketTypeIsDonation);

    }

    @Test
    void test_allTicketsAreOfDonationType_success_with_allTicketDonationType() {

        //setup
        ticketingType.setTicketType(TicketType.DONATION);
        ticketingOrderManager.setTicketType(ticketingType);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        boolean ticketTypeIsDonation = ticketingCouponCodeServiceImpl.allTicketsAreOfDonationType(ticketingOrderManagerList, ticketingCoupon);
        assertTrue(ticketTypeIsDonation);
    }

    @Test
    void test_allTicketsAreOfDonationType_success_with_ticketingCouponNull() {

        //setup
        ticketingType.setTicketType(TicketType.DONATION);
        ticketingOrderManager.setTicketType(ticketingType);
        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        boolean ticketTypeIsDonation = ticketingCouponCodeServiceImpl.allTicketsAreOfDonationType(ticketingOrderManagerList, null);
        assertFalse(ticketTypeIsDonation);
    }

    @Test
    void test_validateCoupon_throwExceptionAppliedCouponNotFound() {

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(null,ticketingOrder, event,recurringEventId));
        
        assertEquals(NotAcceptableException.TicketingExceptionMsg.APPLIED_COUPON_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_validateCoupon_throwExceptionThisDiscountCodeHasAlreadyBeenAppliedToYourTransaction() {

        //setup
        ticketingOrder.setTicketingCoupon(ticketingCoupon);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrder, event,recurringEventId));
        assertEquals(NotAcceptableException.TicketingExceptionMsg.THIS_DISCOUNT_CODE_HAS_ALREADY_BEEN_APPLIED_TO_YOUR_TRANSACTION.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_validateCoupon_throwExceptionCouponReachedMaxUsagePerUser() {

        //setup
        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        ticketingOrder.setTicketingCoupon(ticketingCouponData);

        //mock
        doReturn(1L).when(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(usedPerUser);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrder, event,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_REACHED_MAX_USAGE_PER_USER.getDeveloperMessage(), exception.getMessage());
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_validateCoupon_throwExceptionCouponReachedMaxUsage() {

        //setup
        long usedCount = 1L;

        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        TicketingOrder ticketingOrderData = new TicketingOrder();
        ticketingOrderData.setTicketingCoupon(ticketingCouponData);
        User purchaser = new User();
        purchaser.setUserId(2L);
        ticketingOrderData.setPurchaser(purchaser);

        //mock
        when(ticketingCouponServiceImpl.couponUsed(any(),any(),any(),anyLong())).thenReturn(usedCount);
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrderData, event,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_REACHED_MAX_USAGE.getDeveloperMessage(), exception.getMessage());
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_validateCoupon_throwExceptionCouponIsNotAvaliable() {

        //setup
        long usedCount = -1L;

        ticketingCoupon.setCouponStartDate(startDate1);

        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        TicketingOrder ticketingOrderData = new TicketingOrder();
        ticketingOrderData.setTicketingCoupon(ticketingCouponData);
        User purchaser = new User();
        purchaser.setUserId(2L);
        ticketingOrderData.setPurchaser(purchaser);

        //mock
        when(ticketingCouponServiceImpl.couponUsed(any(),any(),any(),anyLong())).thenReturn(usedCount);
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrderData, event,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_IS_NOT_AVAILABLE.getDeveloperMessage(), exception.getMessage());
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_validateCoupon_throwExceptionCouponCodeExpired() {

        //setup
        long usedCount = -1L;

        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        TicketingOrder ticketingOrderData = new TicketingOrder();
        ticketingOrderData.setTicketingCoupon(ticketingCouponData);
        User purchaser = new User();
        purchaser.setUserId(2L);
        ticketingOrderData.setPurchaser(purchaser);

        //mock
        when(ticketingCouponServiceImpl.couponUsed(any(),any(),any(),anyLong())).thenReturn(usedCount);
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrderData, event,recurringEventId));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_EXPIRED.getDeveloperMessage(), exception.getMessage());
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_validateCoupon_success_with_usedPerUser_lessThanZero() {

        //setup
        long usedCount = 1L;
        long usedPerUser = 1L;

        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        ticketingCoupon.setUsesPerUser(5L);
        ticketingCoupon.setUses(5L);
        ticketingCoupon.setCouponEndDate(new Date("01/01/2030 00:00:00"));

        User purchaser = new User();

        TicketingOrder ticketingOrderData = new TicketingOrder();
        ticketingOrderData.setPurchaser(purchaser);

        //mock
        when(ticketingCouponServiceImpl.couponUsed(any(),any(),any(),anyLong())).thenReturn(usedCount);
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(usedPerUser);

        //Execution
        ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrderData, event,recurringEventId);

        //Assert
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_validateCoupon_success_with_usesPerUser_greaterThanZero() {

        //setup
        long usedCount = 1L;
        long usedPerUser = 1L;

        TicketingCoupon ticketingCouponData = new TicketingCoupon();
        ticketingCouponData.setName(couponCode1);

        ticketingCoupon.setUsesPerUser(5L);
        ticketingCoupon.setUses(5L);
        ticketingCoupon.setCouponEndDate(new Date("01/01/2030 00:00:00"));

        User purchaser = new User();

        TicketingOrder ticketingOrderData = new TicketingOrder();
        ticketingOrderData.setPurchaser(purchaser);

        //mock
        when(ticketingCouponServiceImpl.couponUsed(any(),any(),any(),anyLong())).thenReturn(usedCount);
        when(ticketingCouponServiceImpl.couponUsedPerUser(any(),any(),any(),anyLong())).thenReturn(usedPerUser);

        //Execution
        ticketingCouponCodeServiceImpl.validateCoupon(ticketingCoupon,ticketingOrderData, event,recurringEventId);

        //Assert
        verify(ticketingCouponServiceImpl).couponUsed(any(),any(),any(),anyLong());
        verify(ticketingCouponServiceImpl).couponUsedPerUser(any(),any(),any(),anyLong());
    }

    @Test
    void test_applyCouponCode_throwExceptionCouponCodeNotApplicableOnDonationTicketingType() {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);

        ticketingType.setTicketType(TicketType.DONATION);

        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        when(ticketingOrderService.findByidAndEventid(anyLong(),any())).thenReturn(ticketingOrder);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);

        //Exception
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, null));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE.getDeveloperMessage(), exception.getMessage());
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(),anyString(),anyLong());
        verify(ticketingOrderService).findByidAndEventid(anyLong(),any());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(ticketingOrderManagerService).getAllByOrderId(any());
    }
    static Object[] getRecurringEventId(){
        return new Object[]{
                new Object[]{null},
                new Object[]{0L},
        };
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_applyCouponCode_throwExceptionRecurringIdIsNotEmpty(Long recurringEventId) {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        //Exception
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, ticketingOrder));

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        assertEquals(NotAcceptableException.RecurringExceptionMsg.RECURRING_EVENT_ID_NOT_EMPTY.getDeveloperMessage(), exception.getMessage());
    }
    @Test
    void test_applyCouponCode_success_with_ticketType_paid() {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPrice(price);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setEnableTicketDescription(true);

        ticketingOrderManager.setNumberofticket(1);
        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
        when(ticketingCouponServiceImpl.getRemainingCount(any(), any())).thenReturn(numberOfUsageLeft);
        when(ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(anyList(), any())).thenReturn(totalTicketsAndTotalTicketPriceDto);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(),any(),anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);

        //Execution
        TicketingAppliedCouponDto ticketingAppliedCouponDtoData = ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, ticketingOrder);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(),anyString(),anyLong());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(ticketingOrderManagerService).getAllByOrderId(any());

        assertEquals(COUPON_APPLIED_SUCCESS_MSG, ticketingAppliedCouponDtoData.getMessage());
        assertEquals(ticketingOrderManager.getTicketType().getTicketTypeName(), ticketingAppliedCouponDtoData.getDiscountTicketTypes().iterator().next().getTicketTypeName());
        assertEquals(ticketPriceDetails.getPriceWithFee(), ticketingAppliedCouponDtoData.getTotalPrice());
        assertEquals(ticketPriceDetails.getDiscountedAmount(), ticketingAppliedCouponDtoData.getDiscountAmount());

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, times(1)).save(ticketingOrderManagerArgumentCaptor.capture());

        TicketingOrderManager actualData = ticketingOrderManagerArgumentCaptor.getValue();
        assertEquals(ticketingOrderManager.getNumberofticket(),actualData.getNumberOfDiscountedTicket());

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder actualData1 = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingCoupon.getName(), actualData1.getTicketingCoupon().getName());
    }
    @Test
    void test_applyCouponCode_success1_with_eventTicketTypeid_empty_and_applicableTo_PER_ORDER() {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketingCoupon.setEventTicketTypeId("");
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_ORDER);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPrice(200d);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setEnableTicketDescription(true);

        ticketingOrderManager.setNumberofticket(10);
        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        when(ticketingOrderService.findByidAndEventid(anyLong(),any())).thenReturn(ticketingOrder);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
        when(ticketingCouponServiceImpl.getRemainingCount(any(), any())).thenReturn(numberOfUsageLeft);
        when(ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(anyList(), any())).thenReturn(totalTicketsAndTotalTicketPriceDto);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(),any(),anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);
        when(roEventService.getLanguageCodeByUserOrEvent(any(),any())).thenReturn("EN");

        //Exception
        TicketingAppliedCouponDto ticketingAppliedCouponDtoData = ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, null);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(),anyString(),anyLong());
        verify(ticketingOrderService).findByidAndEventid(anyLong(),any());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(ticketingOrderManagerService).getAllByOrderId(any());

        assertEquals(COUPON_APPLIED_SUCCESS_MSG, ticketingAppliedCouponDtoData.getMessage());
    }

    @Test
    void test_applyCouponCode_success1_with_eventTicketTypeid_empty_and_applicableTo_PER_TICKET() {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketingCoupon.setEventTicketTypeId("");
        ticketingCoupon.setApplicableTo(TicketingCoupon.ApplicableTo.PER_TICKET);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPrice(200d);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setEnableTicketDescription(true);

        ticketingOrderManager.setNumberofticket(10);
        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        when(ticketingOrderService.findByidAndEventid(anyLong(),any())).thenReturn(ticketingOrder);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
        when(ticketingCouponServiceImpl.getRemainingCount(any(), any())).thenReturn(numberOfUsageLeft);
        when(ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(anyList(), any())).thenReturn(totalTicketsAndTotalTicketPriceDto);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(),any(),anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);

        TicketingAppliedCouponDto ticketingAppliedCouponDtoData = ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, null);

        //Assert
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(),anyString(),anyLong());
        verify(ticketingOrderService).findByidAndEventid(anyLong(),any());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(ticketingOrderManagerService).getAllByOrderId(any());

        assertEquals(COUPON_APPLIED_SUCCESS_MSG, ticketingAppliedCouponDtoData.getMessage());
    }
    @Test
    void test_applyCouponCode_success_with_ticketType_donation() {

        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPriceWithFee(price);
        ticketPriceDetails.setDiscounted(true);
		long discountAmount = 10L;
		ticketPriceDetails.setDiscountedAmount(discountAmount);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setEnableTicketDescription(true);
        ticketingType.setTicketType(TicketType.DONATION);

        ticketingOrderManager.setNumberofticket(-1);
        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        //when(ticketingOrderService.findByidAndEventid(anyLong(),any())).thenReturn(ticketingOrder);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
        when(ticketingCouponServiceImpl.getRemainingCount(any(), any())).thenReturn(-1L);
        when(ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(anyList(), any())).thenReturn(totalTicketsAndTotalTicketPriceDto);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(),any(),anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);
        doReturn(false).when(ticketingCouponCodeServiceImpl).allTicketsAreOfDonationType(any(), any());

        //Exception
        TicketingAppliedCouponDto ticketingAppliedCouponDtoData = ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, ticketingOrder);

        assertEquals(COUPON_APPLIED_BUT_NOT_WITH_DONATION_TICKET_TYPE, ticketingAppliedCouponDtoData.getMessage());
        assertEquals(ticketingOrderManager.getTicketType().getTicketTypeName(), ticketingAppliedCouponDtoData.getDiscountTicketTypes().iterator().next().getTicketTypeName());
        assertEquals(ticketPriceDetails.getPriceWithFee(), ticketingAppliedCouponDtoData.getTotalPrice());
        assertEquals(ticketPriceDetails.getDiscountedAmount(), ticketingAppliedCouponDtoData.getDiscountAmount());

        ArgumentCaptor<TicketingOrderManager> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        verify(ticketingOrderManagerService, times(1)).save(ticketingOrderManagerArgumentCaptor.capture());

        TicketingOrderManager actualData = ticketingOrderManagerArgumentCaptor.getValue();
        assertEquals(ticketingOrderManager.getNumberofticket(),actualData.getNumberOfDiscountedTicket());

        ArgumentCaptor<TicketingOrder> ticketingOrderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepoService, times(1)).save(ticketingOrderArgumentCaptor.capture());

        TicketingOrder actualData1 = ticketingOrderArgumentCaptor.getValue();
        assertEquals(ticketingCoupon.getName(), actualData1.getTicketingCoupon().getName());
    }
    @Test
    void test_applyCouponCode_throwException_COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE() {
        //setup
        ticketing = new Ticketing();
        ticketing.setRecurringEvent(true);

        ticketPriceDetails = new TicketPriceDetails();
        ticketPriceDetails.setPrice(price);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setEnableTicketDescription(true);
        ticketingType.setId(2L);
        ticketingType.setTicketType(TicketType.DONATION);

        ticketingOrderManager.setNumberofticket(1);
        ticketingOrderManager.setTicketType(ticketingType);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        List<TicketPriceDetails> ticketPriceDetailsList = new ArrayList<>();
        ticketPriceDetailsList.add(ticketPriceDetails);

        //mock
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(ticketingCouponServiceImpl.getByEventIdAndCouponCode(anyLong(),anyString(),anyLong())).thenReturn(ticketingCoupon);
        doNothing().when(ticketingCouponCodeServiceImpl).validateCoupon(any(), any(),any(),anyLong());
        when(stripeService.getStripeFeesByEvent(any())).thenReturn(stripeDTO);
        when(ticketingOrderManagerService.getAllByOrderId(any())).thenReturn(ticketingOrderManagerList);
        when(ticketingCouponServiceImpl.getRemainingCount(any(), any())).thenReturn(numberOfUsageLeft);
        when(ticketingStatisticsService.getTotalTicketsForDiscountAndTotalTicketPrice(anyList(), any())).thenReturn(totalTicketsAndTotalTicketPriceDto);
        when(ticketingHelperService.getTicketPriceDetails(anyLong(), any(), anyBoolean(), any(),any(),anyInt(), anyInt(), any(), any(), anyBoolean(), anyBoolean(), any(), anyDouble(), anyDouble())).thenReturn(ticketPriceDetailsList);
        doReturn(false).when(ticketingCouponCodeServiceImpl).allTicketsAreOfDonationType(any(), any());
        //Exception
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingCouponCodeServiceImpl.applyCouponCode(couponCode, orderId, event,recurringEventId, ticketingOrder));

        //Assert
        assertEquals(NotAcceptableException.TicketingExceptionMsg.COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE.getDeveloperMessage(), exception.getMessage());
        verify(ticketingHelperService).findTicketingByEvent(any());
        verify(ticketingCouponServiceImpl).getByEventIdAndCouponCode(anyLong(),anyString(),anyLong());
        verify(stripeService).getStripeFeesByEvent(any());
        verify(ticketingOrderManagerService).getAllByOrderId(any());
    }
}