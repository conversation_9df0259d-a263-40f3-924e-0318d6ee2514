package com.accelevents.services.impl;

import com.accelevents.domain.EventTicketRefundTracker;
import com.accelevents.repositories.TableRefundRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TableRefundServiceImplTest {

    @Spy
    @InjectMocks
    TableRefundServiceImpl tableRefundServiceImpl = new TableRefundServiceImpl();

    @Mock
    private TableRefundRepository tableRefundRepository;

    private EventTicketRefundTracker eventTicketRefundTracker;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        eventTicketRefundTracker = new EventTicketRefundTracker();
    }

    @Test
    void test_save() {

        //Execution
        tableRefundServiceImpl.save(eventTicketRefundTracker);

        //assert
        ArgumentCaptor<EventTicketRefundTracker> eventTicketRefundTrackerArgumentCaptor = ArgumentCaptor.forClass(EventTicketRefundTracker.class);
        verify(tableRefundRepository, times(1)).save(eventTicketRefundTrackerArgumentCaptor.capture());

        EventTicketRefundTracker actual = eventTicketRefundTrackerArgumentCaptor.getValue();
        assertEquals(actual,eventTicketRefundTracker);
    }
}