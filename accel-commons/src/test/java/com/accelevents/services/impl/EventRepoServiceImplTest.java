package com.accelevents.services.impl;

import com.accelevents.domain.Event;
import com.accelevents.domain.JoinEventWithDealProduct;
import com.accelevents.services.repo.helper.impl.EventRepoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class EventRepoServiceImplTest {

    @InjectMocks
    @Spy
    EventRepoServiceImpl eventRepoServiceImpl = new EventRepoServiceImpl();


    private JoinEventWithDealProduct joinEventWithDealProduct;
    private Event event;
    private Long id = 1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
    }

    public EventRepoServiceImplTest() {
    }



    private void getJoinEventWithDealProductData() {
        joinEventWithDealProduct = new JoinEventWithDealProduct();
        joinEventWithDealProduct.setEvent(event);
        joinEventWithDealProduct.setId(id);
        joinEventWithDealProduct.setProductId(id);
        joinEventWithDealProduct.setDealProductId(id);
    }


}
