package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.dto.PhoneNumberDto;
import com.accelevents.dto.UserSignupDto;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class PhoneNumberServiceImplTest {

    @InjectMocks
    @Spy
    PhoneNumberServiceImpl phoneNumberServiceImpl = new PhoneNumberServiceImpl();

    private Event event;

    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
    }

    @Test
    void test_getAePhoneNumber() {

        //setup
        PhoneNumberDto phoneNumberDto = new PhoneNumberDto();
        phoneNumberDto.setPhoneNumber("+14379802236");
        phoneNumberDto.setCountryCode("CA");

        //Execution
        AccelEventsPhoneNumber accelEventsPhoneNumber = phoneNumberServiceImpl.getAePhoneNumber(phoneNumberDto);

        //assert
        assertEquals(accelEventsPhoneNumber.getPhoneNumber(),4379802236L);
        assertEquals(accelEventsPhoneNumber.getCountryCode().name(),"CA");
    }

    @Test
    void test_getDisplayNumber_throwNumberFormateException() {

        //setup
        //Execution
        Exception exception = assertThrows(NumberFormatException.class,
                () -> phoneNumberServiceImpl.getDisplayNumber(new RecurringEvents()));
        assertEquals("Unable to parse the number to the intended format.", exception.getMessage());
    }

    public static Object[] getEventUserOrTwilioHistoryAndDisplayNumber(){
        Event event = new Event();
        event.setPhoneNumber(4379802236L);
        event.setCountryCode(CountryCode.CA);

        User user = new User();
        user.setPhoneNumber(4379802236L);
        user.setCountryCode(CountryCode.GB);

        TwilioNumberHistory twilioNumberHistory = new TwilioNumberHistory();
        twilioNumberHistory.setPhoneNumber(4379802236L);
        twilioNumberHistory.setCountryCode(CountryCode.AU);

        return new Object[]{
                new Object[]{event,"(*************"} ,
                new Object[]{user,"+44 ************"} ,
                new Object[]{twilioNumberHistory,"0437 980 223"} ,
        };
    }

    @ParameterizedTest
    @MethodSource("getEventUserOrTwilioHistoryAndDisplayNumber")
    void test_getDisplayNumber_successWithDifferentObject(Object eventUserOrTwilioHistory,String displayNumber) {

        //Execution
        String number = phoneNumberServiceImpl.getDisplayNumber(eventUserOrTwilioHistory);

        //assert
        assertEquals(number,displayNumber);
    }

    @Test
    void test_getDisplayNumber_successWithPhoneNumberZero() {

        //Execution
        String number = phoneNumberServiceImpl.getDisplayNumber(new Event());

        //assert
        assertEquals(number, Constants.ACCOUNT_ACTIVATION_REQUIRED);
    }

    @Test
    void test_getDisplayNumber_throwException() {

        //setup
        event.setPhoneNumber(-1L);

        //Execution
        String number = phoneNumberServiceImpl.getDisplayNumber(event);

        //assert
        assertEquals(number, Constants.ACCOUNT_ACTIVATION_REQUIRED);
    }

    @Test
    void test_getAePhoneNumber_withUserSignUpDto() {

        //setup
        UserSignupDto userSignupDto = new UserSignupDto();
        userSignupDto.setCountryCode(CountryCode.IN.name());
        userSignupDto.setPhoneNumber(4379802236L);

        //Execution
        AccelEventsPhoneNumber accelEventsPhoneNumber = phoneNumberServiceImpl.getAePhoneNumber(userSignupDto);

        //assert
        assertEquals(accelEventsPhoneNumber.getPhoneNumber(),userSignupDto.getPhoneNumber());
        assertEquals(accelEventsPhoneNumber.getCountryCode().name(),userSignupDto.getCountryCode());
    }
}