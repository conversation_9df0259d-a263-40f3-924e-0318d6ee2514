package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.domain.Event;
import com.accelevents.domain.TicketingType;
import com.accelevents.domain.WaitList;
import com.accelevents.domain.WaitListSettings;
import com.accelevents.domain.enums.Status;
import com.accelevents.dto.WaitListSettingsDto;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.WaitListStatus;
import com.accelevents.repositories.WaitListRepository;
import com.accelevents.repositories.WaitListSettingRepository;
import com.accelevents.services.EventService;
import com.accelevents.services.TicketTypeService;
import com.accelevents.services.TicketingTypeService;
import com.accelevents.ticketing.dto.GetTicketTypeSettingDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WaitListSettingsServiceImplTest {

	@Spy
	@InjectMocks
	private WaitListSettingServiceImpl waitListSettingServiceImpl = new WaitListSettingServiceImpl();
    @Mock
	private EventService eventService;

	@Mock
    private WaitListSettingRepository waitListSettingRepository;

	@Mock
    private TicketTypeService ticketTypeService ;

	@Mock
    private WaitListRepository waitListRepository;

	@Mock
    private TicketingTypeService ticketingTypeService;
	@Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

	private Event event;
	private WaitListSettings waitListSettings;
	private TicketingType ticketingType;
	private GetTicketTypeSettingDto getTicketTypeSettingDto;
	private WaitListSettingsDto waitListSettingsDto;

	private Long eventId = 1L;
    private Long recurringEventId = 1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        waitListSettings = new WaitListSettings();
        ticketingType = new TicketingType();
        getTicketTypeSettingDto = new GetTicketTypeSettingDto(ticketingType);
        waitListSettingsDto = new WaitListSettingsDto();
    }

    @Test
    void test_getWaitListSettingsByEvent_successWaitListSettingNull() {

        //mock
        when(waitListSettingRepository.findFirstByEventId(eventId)).thenReturn(null);

        //execution
        WaitListSettingsDto waitListSettingsDto = waitListSettingServiceImpl.getWaitListSettingsByEvent(event,recurringEventId);

        //assert
        assertEquals(waitListSettingsDto.getTimeToRespondInDays(),1);
        verify(waitListSettingRepository).findFirstByEventId(eventId);
    }

    public static Object[] getCreatedForm(){
        return new Object[]{
                new Object[]{0L},
                new Object[]{1L},
        };
    }

    @ParameterizedTest
    @MethodSource("getCreatedForm")
    void test_getWaitListSettingsByEvent_successWaitListSettingNotNullAndmaxWaitListSizeZero(Long createdForm) {

        //setup
        getTicketTypeSettingDto.setCreatedFrom(createdForm);
        List<GetTicketTypeSettingDto> ticketTypes = new ArrayList<>();
        ticketTypes.add(getTicketTypeSettingDto);

        //mock
        when(waitListSettingRepository.findFirstByEventId(eventId)).thenReturn(waitListSettings);
        when(ticketTypeService.getTicketTypes(event, false, recurringEventId)).thenReturn(ticketTypes);

        //execution
        WaitListSettingsDto waitListSettingsDto = waitListSettingServiceImpl.getWaitListSettingsByEvent(event,recurringEventId);

        //assert
        assertFalse(waitListSettingsDto.isMaxWaitListSizeReached());
        assertEquals(waitListSettingsDto.getWaitListTrigger(),0L,0);

        verify(waitListSettingRepository).findFirstByEventId(eventId);
        verify(ticketTypeService).getTicketTypes(event, false, recurringEventId);
    }

    public static Object[] getWaitListsAndRecurringEventIdAndIsMaxWaitListSizeReachedAndrRemainingTickets(){
        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(new WaitList());
        waitLists.add(new WaitList());
        return new Object[]{
                new Object[]{Collections.emptyList(),0L,false,1},
                new Object[]{waitLists,null,true,-1},
        };
    }

    @ParameterizedTest
    @MethodSource("getWaitListsAndRecurringEventIdAndIsMaxWaitListSizeReachedAndrRemainingTickets")
    void test_getWaitListSettingsByEvent_successWaitListSettingNotNullAndmaxWaitListSizeNotZero(List<WaitList> waitLists,Long recurringEventId,
                                                                                                       boolean isMaxWaitListSizeReached,int remainingTickets) {

        //setup
        waitListSettings.setMaxWaitListSize(1);

        //mock
        when(waitListSettingRepository.findFirstByEventId(eventId)).thenReturn(waitListSettings);
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(waitLists);

        //execution
        WaitListSettingsDto waitListSettingsDto = waitListSettingServiceImpl.getWaitListSettingsByEvent(event,recurringEventId);

        //assert
        assertEquals(waitListSettingsDto.isMaxWaitListSizeReached(),isMaxWaitListSizeReached);
        assertEquals(waitListSettingsDto.getRemainingTickets(),remainingTickets,0);

        verify(waitListSettingRepository).findFirstByEventId(eventId);
        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
    }

    @Test
    void test_save_throwExceptionCanNotDecreaseWailtListSize() {

        //setup
        waitListSettingsDto.setMaxWaitListSize(1);

        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(new WaitList());
        waitLists.add(new WaitList());

        //mock
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(waitLists);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListSettingServiceImpl.save(event,waitListSettingsDto));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.CANNOT_DECREASE_WAITLIST_SIZE.getDeveloperMessage(), exception.getMessage());
        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
    }

    @Test
    void test_save_throwExceptionInvalidWaitListTimeToRespond() {

        //setup
        waitListSettingsDto.setMaxWaitListSize(1);
        waitListSettingsDto.setWaitListTrigger(1L);

        //mock
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(Collections.emptyList());
        when(ticketingTypeService.findByidAndEvent(1L, event)).thenReturn(ticketingType);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListSettingServiceImpl.save(event,waitListSettingsDto));

        //assert
        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
        verify(ticketingTypeService).findByidAndEvent(1L, event);
        assertEquals(NotAcceptableException.WaitListExceptionMsg.INVALID_WAIT_LIST_TIME_TO_RESPOND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_save_throwExceptionInvalidWaitListTrigger() {

        //setup
        waitListSettingsDto.setMaxWaitListSize(0);
        waitListSettingsDto.setWaitListTrigger(1L);

        //mock
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(Collections.emptyList());
        when(ticketingTypeService.findByidAndEvent(1L, event)).thenReturn(null);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> waitListSettingServiceImpl.save(event,waitListSettingsDto));

        //assert
        assertEquals(NotAcceptableException.WaitListExceptionMsg.INVALID_WAIT_LIST_TRIGGER.getDeveloperMessage(), exception.getMessage());
        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
        verify(ticketingTypeService).findByidAndEvent(1L, event);
    }

    public static Object[] getWaitListSetting(){
        List<WaitList> waitLists = new ArrayList<>();
        waitLists.add(new WaitList());
        waitLists.add(new WaitList());
        return new Object[]{
                new Object[]{null},
                new Object[]{new WaitListSettings()},
        };
    }

    @ParameterizedTest
    @MethodSource("getWaitListSetting")
    void test_save_success(WaitListSettings currentSettings) {

        //setup
        waitListSettingsDto.setMaxWaitListSize(1);
        waitListSettingsDto.setWaitListTrigger(0L);
        waitListSettingsDto.setTimeToRespondInDays(1);

        //mock
        when(waitListRepository.findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING)).thenReturn(Collections.emptyList());
        when(waitListSettingRepository.findFirstByEventId(eventId)).thenReturn(currentSettings);

        //execution
        waitListSettingServiceImpl.save(event,waitListSettingsDto);

        //assert

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingRepository, times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettings = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettings.getMaxWaitListSize(),waitListSettingsDto.getMaxWaitListSize());
        assertEquals(waitListSettings.getWaitListTrigger(),waitListSettingsDto.getWaitListTrigger());
        assertEquals(waitListSettings.getTimeToRespond(),(waitListSettingsDto.getTimeToRespondInDays()*24 + waitListSettingsDto.getTimeToRespondInHours())*60 + waitListSettingsDto.getTimeToRespondInMinutes());
        assertEquals(waitListSettings.getEventId(),event.getEventId());
        assertEquals(waitListSettings.getId(),0,0);

        verify(waitListRepository).findByEventIdAndStatus(event.getEventId(), WaitListStatus.WAITING);
        verify(waitListSettingRepository).findFirstByEventId(eventId);
    }

    @Test
    void test_getAllWaitListSettings_success() {

        //mock
        when(waitListSettingRepository.findAllByWaitListEnabled(true, false)).thenReturn(Collections.emptyList());

        //execution
        List<WaitListSettings> getAllWaitListSettings = waitListSettingServiceImpl.getAllWaitListSettings();

        //assert
        assertTrue(getAllWaitListSettings.isEmpty());
        verify(waitListSettingRepository).findAllByWaitListEnabled(true, false);
    }

    @Test
    void test_save_success() {

        //execution
        waitListSettingServiceImpl.save(waitListSettings);

        //assert
        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingRepository, times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings newWaitListSettings = waitListSettingsArgumentCaptor.getValue();
        assertEquals(newWaitListSettings,waitListSettings);
    }

    @Test
    void test_updateStatusDeleted_success() {

        //mock
        doNothing().when(waitListSettingRepository).updateStatusDeleted(eventId, Status.DELETED);

        //execution
        waitListSettingServiceImpl.updateStatusDeleted(eventId,Status.DELETED);

        //assert
        verify(waitListSettingRepository).updateStatusDeleted(eventId, Status.DELETED);
    }

}
