package com.accelevents.services.impl;

import com.accelevents.common.dto.SubcriptionPlanDto;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.dto.CardInfoDto;
import com.accelevents.dto.DonationPurchaseDto;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.dto.StripeDTO;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.services.*;
import com.accelevents.ticketing.dto.ChargeDto;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.ApiConnectionException;
import com.stripe.exception.StripeException;
import com.stripe.model.Plan;
import com.stripe.model.Subscription;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.accelevents.exceptions.NotAcceptableException.PaymentCreationExceptionMsg.RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PaymentHandlerServiceImplTest {

    @Spy
    @InjectMocks
    PaymentHandlerServiceImpl paymentHandlerService;
    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private AllPaymentService allPaymentService;
    @Mock
    private StripeTransactionService stripeTransactionService;
    @Mock
    private WhiteLabelTransferService whiteLabelTransferService;
    @Mock
    private RefundTransactionService refundTransactionService;
    @Mock
    private TableRefundService tableRefundService;
    @Mock
    private StripePaymentHandlerService stripePaymentHandlerService;
    @Mock
    private PurchasedRaffleTicketService purchasedRaffleTicketService;
    @Spy
    private StripeConfiguration stripeConfiguration = new StripeConfiguration();
    @Mock
    private StripePaymentService stripePaymentService;

    private Event event;
    private User user;
    private String stripeKey = "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf";
    private String productId = "prod_Cenq4VrQpqZ3EU";
    private String chargeId= "chargeId";

    @BeforeEach
    void setUp() {
        event = EventDataUtil.getEvent();

        user = new User();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setFirstName("Normal");
        user.setLastName("User");
        user.setMostRecentEventId(1L);

        System.setProperty("stripe.apiKey", "sk_test_SIBJUV2kWfsQ5zcFUNuQmJXf");
        System.setProperty("stripe.texttogive.product", "prod_Cenq4VrQpqZ3EU");

        ReflectionTestUtils.setField(stripeConfiguration, "API_KEY", "sk_test_VdWjJ3utvEVEMNvIojtMYMWn");
        ReflectionTestUtils.setField(stripeConfiguration, "auctionProductId", "prod_Cen5qwPvDrUYv9");
        ReflectionTestUtils.setField(stripeConfiguration, "raffleProductId", "prod_Cen5RURzoolzB6");
        ReflectionTestUtils.setField(stripeConfiguration, "fundANeedProductId", "prod_Cen5hDjNygMuzg");
        ReflectionTestUtils.setField(stripeConfiguration, "textToGiveProductId", "prod_EbdQGd6Lah0Zmq");

    }

//    @Test
    void test_createTicketPurchasePayment() throws Exception {
        //setup
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), false)).thenReturn(new Payment());
        when(allPaymentService.retrieveDefaultCardOfCustomer(anyString(), any(), null)).thenReturn(new CardInfoDto());
        mockCreateChargeToCust(new ChargeDto());
        doNothing().when(stripeTransactionService).save(any());

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 0.0,
                true, "test_token", "user name", 1, new Date(),
                null, 0.0, 0.0, new User(), null);

        //verify
        assertNotNull(card);
    }

    @Test
    void test_createTicketPurchasePayment_amount_less_then_zero() throws Exception {

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 0.0, 0.0,
                true, "test_token", "user name", 1, new Date(),
                null, 0.0, 0.0, new User(), null);

        Assertions.assertTrue(null==card);
    }

    @Test
    void test_createTicketPurchasePayment_isCardPayment_false() throws Exception {

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 0.0,
                false, "test_token", "user name", 1, new Date(),
                null, 0.0, 0.0, new User(),null);

        Assertions.assertTrue(null==card);
    }

//    @Test
    void test_createTicketPurchasePaymentForWhitelabel() throws Exception {
        //setup
        event.setWhiteLabel(new WhiteLabel());
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), false)).thenReturn(new Payment());
        when(allPaymentService.retrieveDefaultCardOfCustomer(anyString(), any(), null)).thenReturn(new CardInfoDto());
        mockCreateChargeToCust(new ChargeDto());
        doNothing().when(stripeTransactionService).save(any());
        doNothing().when(whiteLabelTransferService).startWhiteLabelTransfer(anyLong(), anyDouble(), any(), any(), any(), any());

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 1.0,
                true, "test_token", "user name", 1, new Date(),
                null, 1.0, 1.0, new User(),null);

        //verify
        assertNotNull(card);
    }

//    @Test
    void test_createTicketPurchasePayment_ForWhitelabel_wla_less_then_zero() throws Exception {
        //setup
        event.setWhiteLabel(new WhiteLabel());
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(new Payment());
        when(allPaymentService.retrieveDefaultCardOfCustomer(anyString(), any(), null)).thenReturn(new CardInfoDto());
        mockCreateChargeToCust(new ChargeDto());
        doNothing().when(stripeTransactionService).save(any());
        doNothing().when(whiteLabelTransferService).startWhiteLabelTransfer(anyLong(), anyDouble(), any(), any(), any(), any());

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 1.0,
                true, "test_token", "user name", 1, new Date(),
                null, 0.0, 1.0, new User(),null);

        //verify
        assertNotNull(card);
    }

//    @Test
    void test_createTicketPurchasePayment_ForWhitelabel_wlb_less_then_zero() throws Exception {
        //setup
        event.setWhiteLabel(new WhiteLabel());
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(new Payment());
        when(allPaymentService.retrieveDefaultCardOfCustomer(anyString(), any(), null)).thenReturn(new CardInfoDto());
        mockCreateChargeToCust(new ChargeDto());
        doNothing().when(stripeTransactionService).save(any());
        doNothing().when(whiteLabelTransferService).startWhiteLabelTransfer(anyLong(), anyDouble(), any(), any(), any(), any());

        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 1.0,
                true, "test_token", "user name", 1, new Date(),
                null, 1.0, 0.0, new User(),null);

        //verify
        assertNotNull(card);
    }

    /*@Test
    void test_createTicketPurchasePayment_throws_CARD_NOT_FOUND() throws Exception {
        //setup
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any())).thenReturn(new Payment());
        when(allPaymentService.retrieveDefaultPaymentIdOfCustomer(anyString(), any(), null)).thenReturn(null);

        //expect


        //execute
        CardInfoDto card = paymentHandlerService.createTicketPurchasePayment(user, event, 50.0, 0.0,
                true, "test_token", "user name", 1, new Date(),
                null, 0.0, 0.0, new User(),null);
        assertNotNull(card);
    }*/

    @Test
    void test_getBuyerNameWithLoginUser() {
        //setup
        User buyerInfo = new User();
        User loginUser = user;
        //mock
        //execute
        String userName = paymentHandlerService.getBuyerName(buyerInfo, loginUser);
        //verify
        Assertions.assertEquals(loginUser.getFirstName() + " " + loginUser.getLastName(), userName);
    }

    @Test
    void test_getBuyerNameWithBuyerInfo() {
        //setup
        User buyerInfo = user;
        User loginUser = new User();
        //mock
        //execute
        String userName = paymentHandlerService.getBuyerName(buyerInfo, loginUser);
        //verify
        Assertions.assertEquals(buyerInfo.getFirstName() + " " + buyerInfo.getLastName(), userName);
    }

    @Test
    void test_getBuyerNameWithBuyerInfo_first_name_null() {
        //setup
        User buyerInfo = new User();
        buyerInfo.setLastName("lastName");
        User loginUser = new User();
        //mock
        //execute
        String userName = paymentHandlerService.getBuyerName(buyerInfo, loginUser);
        //verify
        Assertions.assertEquals("null " + buyerInfo.getLastName(), userName);
    }

    @Test
    void test_startRefund() throws StripeException, ApiException {
        //setup

        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(new Stripe());
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());
        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), false, true,
                new ArrayList<>(), new HashMap<>(),
                50.0, 0.0,
                0.0, 0.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_startRefundWithoutFullOrderAndStripe() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(stripe);
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());

        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), true, false,
                new ArrayList<>(), new HashMap<>(),
                50.0, 0.0,
                1.0, 1.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_startRefundWithRefundTracker() throws StripeException, ApiException {
        //setup
        EventTicketRefundTracker ticketRefundTracker = new EventTicketRefundTracker();
        List<EventTicketRefundTracker> ticketRefundTrackers = new ArrayList<>();
        ticketRefundTrackers.add(ticketRefundTracker);
        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(new Stripe());
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());
        doNothing().when(tableRefundService).save(any());
        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), false, true,
                ticketRefundTrackers, new HashMap<>(),
                50.0, 0.0,
                0.0, 0.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_startRefundWithoutFullOrderAndStripe_stripe_wlaRefund() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(stripe);
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());

        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), true, false,
                new ArrayList<>(), new HashMap<>(),
                50.0, 0.0,
                1.0, 0.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_startRefundWithoutFullOrderAndStripe_stripe_wlBRefund() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setAccessToken("Stripe_AccessToken");
        //mock
        StripeTransaction stripeTransaction1 = new StripeTransaction();
        stripeTransaction1.setChargeid("ChargeId");
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(stripeTransaction1);
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(stripe);
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());

        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), true, false,
                new ArrayList<>(), new HashMap<>(),
                50.0, 0.0,
                0.0, 1.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }


    @Test
    void test_startRefundWithRefundTracker_isFullOrderRefund_true_wlARefunded_grater_then_zero() throws StripeException, ApiException {
        //setup
        EventTicketRefundTracker ticketRefundTracker = new EventTicketRefundTracker();
        List<EventTicketRefundTracker> ticketRefundTrackers = new ArrayList<>();
        ticketRefundTrackers.add(ticketRefundTracker);
        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(new Stripe());
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());
        doNothing().when(tableRefundService).save(any());
        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), false, false,
                ticketRefundTrackers, new HashMap<>(),
                50.0, 0.0,
                1.0, 0.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_startRefundWithRefundTracker_isFullOrderRefund_true_wlBRefunded_grater_then_zero() throws StripeException, ApiException {
        //setup
        EventTicketRefundTracker ticketRefundTracker = new EventTicketRefundTracker();
        List<EventTicketRefundTracker> ticketRefundTrackers = new ArrayList<>();
        ticketRefundTrackers.add(ticketRefundTracker);
        //mock
        when(stripeTransactionService.findBySourceAndSourceIdAndChargeId(any(), anyLong(), anyString())).thenReturn(new StripeTransaction());
        when(stripeService.findByEventAndStripeUserId(event,null)).thenReturn(new Stripe());
        when(allPaymentService.createRefund(any(), any(), anyDouble(), any(), anyString(), anyBoolean(), anyString())).thenReturn(new RefundInfoDto());
        doNothing().when(refundTransactionService).save(any());
        doNothing().when(tableRefundService).save(any());
        //execute
        StripeTransaction stripeTransaction = paymentHandlerService.startRefund(1L, event, new Date(), false, false,
                ticketRefundTrackers, new HashMap<>(),
                50.0, 0.0,
                0.0, 1.0, chargeId);
        //verify
        assertNotNull(stripeTransaction);
    }

    /*@Test
    void test_handlePaymentForDonation() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setRecurringDonation(false);
        DonationSettings donationSettings = new DonationSettings();
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(new Payment());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        doNothing().when(stripeTransactionService).save(any());
        doNothing().when(stripePaymentHandlerService).handleStripeForRecurringDonation(any(), any(), anyBoolean(), any(), any(), any(), any());
        //execute
        Double amt = paymentHandlerService.handlePaymentForDonation(user, event, donationPurchaseDto, null, new Donation(), true, donationSettings, 0.0, 10);
        //verify
        assertEquals(amt, 10, 0);
    }*/

    private void mockCreateChargeToCust(ChargeDto chargeDto) throws StripeException, ApiException {
        when(allPaymentService.createChargeToCustomer(anyString(), anyDouble(), anyString(), anyBoolean(), any(), anyDouble(), any(), anyString(), anyString(), anyString(), null, false)).thenReturn(chargeDto);
    }

    /*@Test
    void test_handlePaymentForDonation_RecurringDonation_true_PaymentGateway_NOT_Square() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setRecurringDonation(true);
        DonationSettings donationSettings = new DonationSettings();
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(new Payment());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        when(allPaymentService.createChargeToCustomer(anyString(), anyDouble(), anyString(), anyBoolean(), any(), anyDouble(), any(), anyString(), anyString(),anyString(), null)).thenReturn(chargeDto);
        doNothing().when(stripeTransactionService).save(any());
        doNothing().when(stripePaymentHandlerService).handleStripeForRecurringDonation(any(), any(), anyBoolean(), any(), any(), any(), any());
        //execute
        Double amt = paymentHandlerService.handlePaymentForDonation(user, event, donationPurchaseDto, null, new Donation(), true, donationSettings, 0.0, 10);
        //verify
        assertEquals(amt, 10, 0);
    }*/

    @Test
    void test_handlePaymentForDonation_throws_RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE() throws StripeException, ApiException {
        //setup
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
        DonationPurchaseDto donationPurchaseDto = new DonationPurchaseDto();
        donationPurchaseDto.setRecurringDonation(true);
        DonationSettings donationSettings = new DonationSettings();
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);

        //execute
        Exception exception = Assertions.assertThrows(NotAcceptableException.class,
                () -> paymentHandlerService.handlePaymentForDonation(user, event, donationPurchaseDto, null, new Donation(), true, donationSettings, 0.0, 50.0));
        
        Assertions.assertEquals(RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE.getDeveloperMessage(), exception.getMessage());
    }
/*
    @Test
    void test_getPaidAmountForAuctionAfterStripeTransaction() throws StripeException, ApiException {
        //setup
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        //execute
        double amt = paymentHandlerService.getPaidAmountForAuctionAfterStripeTransaction(user, event, new Item(), new Date(), 10, new Payment(), "apiKey", new AuctionBid());
        //verify
        assertEquals(amt, 10, 0);
    }*/

    /*@Test
    void test_getPaidAmountForPledgeAfterStripeTransaction() throws StripeException, ApiException {
        //setup
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        Pledge pledge = new Pledge();
        pledge.setAmount(10);
        //mock
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        doNothing().when(stripeTransactionService).save(any());
        //execute
        double amt = paymentHandlerService.getPaidAmountForPledgeAfterStripeTransaction(event, user, false, new Date(), new Stripe(), pledge, new Payment());
        //verify
        assertEquals(amt, 10, 0);
    }*/

    /*@Test
    void test_getPaidAmountForRaffleTicketsAfterStripeTransaction() throws StripeException, ApiException {
        //setup
        RaffleTicket raffleTicket = new RaffleTicket();
        raffleTicket.setPrice(10);
        PurchasedRaffleTicket purchasedRaffleTicket = new PurchasedRaffleTicket();
        purchasedRaffleTicket.setId(1L);

        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        //execute
        double amt = paymentHandlerService.getPaidAmountForRaffleTicketsAfterStripeTransaction(event, user, raffleTicket, purchasedRaffleTicket, new Payment());
        verify(stripeTransactionService, Mockito.times(1)).save(any());
        //verify
        assertEquals(amt, 10, 0);
    }*/

    /*@Test
    void test_getPaidAmountForRaffleAfterStripeTransaction() throws StripeException, ApiException {
        //setup
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        PurchasedRaffleTicket purchasedRaffleTicket = new PurchasedRaffleTicket();
        purchasedRaffleTicket.setId(1L);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        doNothing().when(stripeTransactionService).save(any());
        //execute
        double amt = paymentHandlerService.getPaidAmountForRaffleAfterStripeTransaction(user, event, new RaffleTicket(), purchasedRaffleTicket, new Payment());
        ArgumentCaptor<PurchasedRaffleTicket> raffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
        verify(purchasedRaffleTicketService, Mockito.times(1)).save(any());
        //verify
        assertEquals(amt, 10, 0);
    }*/

    /*@Test
    void test_getPaidAmountPledgeCheckout() throws StripeException, ApiException {
        //setup
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        //execute
        double amt = paymentHandlerService.getPaidAmountPledgeCheckout(event, user, new Payment(), 10.0);
        //verify
        verify(stripeTransactionService, times(1)).save(any());
        assertEquals(amt, 10, 0);
    }*/

    /*@Test
    void test_getPaidAmountBidCheckout() throws StripeException, ApiException {
        //setup
        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setAmount(1000);
        //mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(allPaymentService.getDefaultSource(anyString(), any())).thenReturn("default_source_id");
        mockCreateChargeToCust(chargeDto);
        //execute
        double amt = paymentHandlerService.getPaidAmountBidCheckout(user, event, new BidCheckOutDto(), new Payment(), new HashMap<>(), 10.0,0);
        //verify
        verify(stripeTransactionService, times(1)).save(any());
        assertEquals(amt, 10, 0);

    }*/

//    @Test
    void test_subscribePlanToCustomer() throws StripeException {
        //setup
        Subscription subscription = new Subscription();
        subscription.setId("1");
        Plan plan = new Plan();
        plan.setAmount(10L);
        //mock
        when(stripePaymentService.subscribeStripePlanToCustomer(anyString(), anyString(), anyString(), "pm_card_visa",event.getEventURL())).thenReturn(subscription);
        //execute
        SubcriptionPlanDto textToGiveRecurringPlan = paymentHandlerService.subscribePlanToCustomer("customerId", plan, "pm_card_visa",event.getEventURL());
        //verify
        assertNotNull(textToGiveRecurringPlan);
    }

    /*@Test
    void test_createRecurringPlan() throws StripeException {

        //Setup
        StripeDTO stripe = new StripeDTO();
        stripe.setCCPercentageFee(2);
        stripe.setCCFlatFee(5);

        Plan plan = new Plan();
        plan.setId("id");

        Product product = new Product();
        product.setId("product_id");

        //Mock
        //when(stripeConfiguration.getAPI_KEY()).thenReturn(API_KEY);
        //when(stripeConfiguration.getTextToGiveProductId()).thenReturn("text_to_give_product_key");
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripe);
        when(stripePaymentService.createRecurringStripePlan(anyString(), anyString(), any(), any(), anyDouble(), anyBoolean(), anyString(), anyDouble(), anyDouble())).thenReturn(plan);
        when(Product.retrieve(anyString())).thenReturn(product);

        //execute
        Plan plan1 = paymentHandlerService.createRecurringPlan(event);
        //assertNotNull(plan1);
    }*/

    /*@Test
    void test_processOrderAmountToActivateModule_amountPaid_greater_then_zero() throws StripeException {

        //Set up
        event.setName("Event name");

        Coupon coupon = new Coupon();
        coupon.setId("1");

        StringBuilder stripeTransactionMsg = new StringBuilder();
        stripeTransactionMsg.append("Demo test message");

        List<Object> itemsParams = new ArrayList<>();
        itemsParams.add(new Object());

        Order order = new Order();
        order.setAmount(100L);

        Charge charges = new Charge();
        charges.setId("1");

        Plan plan = new Plan();
        plan.setAmount(10L);

        SubcriptionPlanDto textToGiveRecurringPlan = new SubcriptionPlanDto("sub_id", 100L);

        //Mock
        when(stripePaymentService.createOrder(anyString(), anyString(), anyString(), any(), anyString(), any(), anyString(), any())).thenReturn(order);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyDouble(), anyString(), any(), anyString())).thenReturn(charges);
        doReturn(textToGiveRecurringPlan).when(paymentHandlerService).subscribePlanToCustomer(any(), any());

        //Execute
        paymentHandlerService.processOrderAmountToActivateModule(null, user, event, coupon, "customerId", "cardId", stripeTransactionMsg, itemsParams, plan);


        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction stripeTransaction = stripeTransactionArgumentCaptor.getValue();
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_processOrderAmountToActivateModule_amountPaid_greater_then_zero_Order_null() throws StripeException {

        //Set up
        event.setName("Event name");

        Coupon coupon = new Coupon();
        coupon.setId("1");

        StringBuilder stripeTransactionMsg = new StringBuilder();
        stripeTransactionMsg.append("Demo test message");

        List<Object> itemsParams = new ArrayList<>();
        itemsParams.add(new Object());

        Charge charges = new Charge();
        charges.setId("1");

        Plan plan = new Plan();
        plan.setAmount(10L);

        SubcriptionPlanDto textToGiveRecurringPlan = new SubcriptionPlanDto("sub_id", 100L);

        //Mock
        when(stripePaymentService.createOrder(anyString(), anyString(), anyString(), any(), anyString(), any(), anyString(), any())).thenReturn(null);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyDouble(), anyString(), any(), anyString())).thenReturn(charges);
        doReturn(textToGiveRecurringPlan).when(paymentHandlerService).subscribePlanToCustomer(any(), any());

        //Execute
        paymentHandlerService.processOrderAmountToActivateModule(null, user, event, coupon, "customerId", "cardId", stripeTransactionMsg, itemsParams, plan);


        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction stripeTransaction = stripeTransactionArgumentCaptor.getValue();
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_processOrderAmountToActivateModule_amountPaid_greater_then_zero_coupon_null() throws StripeException {

        //Set up
        event.setName("Event name");

        StringBuilder stripeTransactionMsg = new StringBuilder();
        stripeTransactionMsg.append("Demo test message");

        List<Object> itemsParams = new ArrayList<>();
        itemsParams.add(new Object());

        Order order = new Order();
        order.setAmount(100L);

        Charge charges = new Charge();
        charges.setId("1");

        Plan plan = new Plan();
        plan.setAmount(10L);

        SubcriptionPlanDto textToGiveRecurringPlan = new SubcriptionPlanDto("sub_id", 100L);

        //Mock
        when(stripePaymentService.createOrder(anyString(), anyString(), anyString(), any(), anyString(), any(), anyString(), any())).thenReturn(order);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyDouble(), anyString(), any(), anyString())).thenReturn(charges);
        doReturn(textToGiveRecurringPlan).when(paymentHandlerService).subscribePlanToCustomer(any(), any());

        //Execute
        paymentHandlerService.processOrderAmountToActivateModule(null, user, event, null, "customerId", "cardId", stripeTransactionMsg, itemsParams, plan);


        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction stripeTransaction = stripeTransactionArgumentCaptor.getValue();
        assertNotNull(stripeTransaction);
    }

    @Test
    void test_processOrderAmountToActivateModule_amountPaid_greater_then_zero_itemsParams_null() throws StripeException {

        //Set up
        event.setName("Event name");

        Coupon coupon = new Coupon();
        coupon.setId("1");

        StringBuilder stripeTransactionMsg = new StringBuilder();
        stripeTransactionMsg.append("Demo test message");

        Order order = new Order();
        order.setAmount(100L);

        Charge charges = new Charge();
        charges.setId("1");

        SubcriptionPlanDto textToGiveRecurringPlan = new SubcriptionPlanDto("sub_id", 100L);

        //Mock
        when(stripePaymentService.createOrder(anyString(), anyString(), anyString(), any(), anyString(), any(), anyString(), any())).thenReturn(order);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyDouble(), anyString(), any(), anyString())).thenReturn(charges);
        //doReturn(textToGiveRecurringPlan).when(paymentHandlerService).subscribePlanToCustomer(any(), any());

        //Execute

        doNothing().when(paymentHandlerService).processOrderAmountToActivateModule(null, user, event, coupon, "customerId", "cardId", stripeTransactionMsg, Collections.EMPTY_LIST, null);
    }

    @Test
    void test_processOrderAmountToActivateModule_amountPaid_less_then_zero() throws StripeException {

        //Set up
        event.setName("Event name");

        Coupon coupon = new Coupon();
        coupon.setId("1");

        StringBuilder stripeTransactionMsg = new StringBuilder();
        stripeTransactionMsg.append("Demo test message");

        List<Object> itemsParams = new ArrayList<>();
        itemsParams.add(new Object());

        Order order = new Order();
        order.setAmount(10L);

        Charge charges = new Charge();
        charges.setId("1");

        Plan plan = new Plan();
        plan.setAmount(10L);

        SubcriptionPlanDto textToGiveRecurringPlan = new SubcriptionPlanDto("sub_id", 100L);

        //Mock
        when(stripePaymentService.createOrder(anyString(), anyString(), anyString(), any(), anyString(), any(), anyString(), any())).thenReturn(order);
        when(stripePaymentService.createCharge(anyString(), anyString(), anyString(), anyDouble(), anyString(), any(), anyString())).thenReturn(charges);
        doReturn(textToGiveRecurringPlan).when(paymentHandlerService).subscribePlanToCustomer(any(), any());

        //Execute
        paymentHandlerService.processOrderAmountToActivateModule("activeModulesDto.getTokenOrIntentId()", user, event, coupon, "customerId", "cardId", stripeTransactionMsg, itemsParams, plan);
        ArgumentCaptor<StripeTransaction> stripeTransactionArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionArgumentCaptor.capture());

        StripeTransaction stripeTransaction = stripeTransactionArgumentCaptor.getValue();
        assertNotNull(stripeTransaction);
    }*/



    @Test
    void test_createCustomer_throw_exception() throws StripeException, ApiException {
        StripeException stripeException = new ApiConnectionException("Exception");

        when(allPaymentService.createCustomerAndCardToCustomer(any(), any())).thenThrow(stripeException);

        Assertions.assertThrows(NotAcceptableException.class,
                () -> paymentHandlerService.createCustomer(anyString(), any()));
    }

    @Test
    void test_createCustomer_success() {

        try {
            when(allPaymentService.createCustomerAndCardToCustomer(any(), any())).thenReturn("Create custom");
        } catch (StripeException e) {
            e.printStackTrace();
        } catch (ApiException e) {
            e.printStackTrace();
        }

        String createCustomer = paymentHandlerService.createCustomer(anyString(), any());
        assertNotNull(createCustomer);
    }


    @Test
    void test_handleEventBilling_throw_exception() {
        //Mock
        when(roStripeService.findByEvent(event)).thenReturn(null);
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(null);
        //Execute
        Exception exception = Assertions.assertThrows(NotAcceptableException.class,
                () -> paymentHandlerService.handleEventBilling(event, user, 100));
        Assertions.assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CARD_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_handleEventBilling_throw_exception_stripeTranscation_null() {
        //Mock
        when(roStripeService.findByEvent(event)).thenReturn(new Stripe());
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(null);
        //Execute
        Exception exception = Assertions.assertThrows(NotAcceptableException.class,
                () -> paymentHandlerService.handleEventBilling(event, user, 100));
        
        Assertions.assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CARD_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }

    @Test
    void test_handleEventBilling_throw_exception_stripe_null() {
        //Mock
        when(roStripeService.findByEvent(event)).thenReturn(null);
        when(stripeTransactionService.findByUserAndEvent(any(), any())).thenReturn(new StripeTransaction());

        //Execute
        Exception exception = Assertions.assertThrows(NotAcceptableException.class,
                () -> paymentHandlerService.handleEventBilling(event, user, 100));
        
        Assertions.assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CARD_NOT_FOUND.getErrorMessage(), exception.getMessage());
    }

    /*@Test
    void test_handleEventBilling_success() throws StripeException, ApiException {

        //Setup
        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setStripecustomerid("stripe_cus_id");

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setId("id");
        chargeDto.setTenderId("tender_id");
        chargeDto.setLocationId("location_id");
        chargeDto.setPaymentGateway("payment_gateway");

        //Mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(stripeTransactionService.findByUserIdAndEventId(any(), any())).thenReturn(stripeTransaction);
        when(allPaymentService.getDefaultSource(any(), any())).thenReturn("source_id");
        mockCreateChargeToCust(chargeDto);

        //Execute
        paymentHandlerService.handleEventBilling(event, user, 100);

        ArgumentCaptor<StripeTransaction> stripeTransactionServiceArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionServiceArgumentCaptor.capture());

        StripeTransaction transaction = stripeTransactionServiceArgumentCaptor.getValue();
        assertNotNull(transaction);
    }*/

    /*@Test
    void test_handleEventBilling_exception_handle_in_catch() throws StripeException, ApiException {

        //Setup
        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setStripecustomerid("stripe_cus_id");

        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(true);

        ChargeDto chargeDto = new ChargeDto();
        chargeDto.setId("id");
        chargeDto.setTenderId("tender_id");
        chargeDto.setLocationId("location_id");
        chargeDto.setPaymentGateway("payment_gateway");

        StripeException stripeException = new ApiConnectionException("Exception");

        //Mock
        when(roStripeService.findByEvent(event)).thenReturn(stripe);
        when(stripeTransactionService.findByUserIdAndEventId(any(), any())).thenReturn(stripeTransaction);
        when(allPaymentService.getDefaultSource(any(), any())).thenReturn("source_id");
        mockCreateChargeToCust(chargeDto);
        doThrow(StripeException.class).when(stripeTransactionService).save(stripeTransaction);

        //Execute
        //Mockito.doThrow(StripeException.class).when(stripeTransactionService).save(stripeTransaction);
        //when(stripeTransactionService.save(stripeTransaction)).thenThrow(stripeException);
        paymentHandlerService.handleEventBilling(event, user, 100);

        ArgumentCaptor<StripeTransaction> stripeTransactionServiceArgumentCaptor = ArgumentCaptor.forClass(StripeTransaction.class);
        verify(stripeTransactionService, Mockito.times(1)).save(stripeTransactionServiceArgumentCaptor.capture());

        StripeTransaction transaction = stripeTransactionServiceArgumentCaptor.getValue();
        assertNotNull(transaction);
    }*/

    @Test
    void test_createRecurringPlan_success() throws StripeException {

        StripeDTO stripeDto = new StripeDTO();
        stripeDto.setCCFlatFee(2);
        stripeDto.setCCPercentageFee(1);

        Plan planResponse = new Plan();
        planResponse.setNickname("Text to give");

        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDto);
        when(stripePaymentService.createRecurringStripePlan(anyString(), anyString(), any(), any(), anyDouble(), anyBoolean(), anyString(), anyDouble(), anyDouble())).thenReturn(planResponse);

        Plan plan = paymentHandlerService.createRecurringPlan(event);
        assertNotNull(plan);

    }
}
