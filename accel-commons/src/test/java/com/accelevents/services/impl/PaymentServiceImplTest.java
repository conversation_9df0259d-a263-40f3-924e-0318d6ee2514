package com.accelevents.services.impl;

import com.accelevents.common.dto.CustomerCardDto;
import com.accelevents.domain.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.repositories.JoinPaymentItemRepository;
import com.accelevents.repositories.PaymentRepository;
import com.accelevents.services.AllPaymentService;
import com.accelevents.services.BidderNumberService;
import com.accelevents.services.TextMessageService;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.accelevents.utils.Constants.ONLINE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PaymentServiceImplTest {

    @Spy
    @InjectMocks
    private PaymentServiceImpl paymentServiceImpl;
    @Mock
    private PaymentRepository paymentRepository;
    @Mock
    private AllPaymentService allPaymentService;
    @Mock
    private BidderNumberService bidderNumberService;
    @Mock
    private TextMessageService textMessageService;
    @Mock
    private TextMessageUtils textMessageUtils;
    @Mock
    private JoinPaymentItemRepository joinPaymentItemRepository;

    private Payment payment;
    private User user, newUser;
    private Event event;
    private Stripe stripe;
    private CustomerCardDto customerCardDto;
    private BidderNumber bidderNumber;

    private final String customerId = "CBASEF693QxMmpH2gI0R9MQQ60AgAQ";
    private final String cardId = "a93a026c-9366-52dd-7d6f-e2639b6a439b";
    private final String token = "token";
	private final String name = "name";
	private final Long paymentId = 1L;

    @BeforeEach
    void setUp() throws Exception {
        user = EventDataUtil.getUser();
        newUser = EventDataUtil.getUser();
        event = EventDataUtil.getEvent();
        stripe = new Stripe();
        customerCardDto = new CustomerCardDto(customerId, cardId);

        bidderNumber = new BidderNumber();
		long bidderNumbers = 1L;
		bidderNumber.setBidderNumber(bidderNumbers);
        bidderNumber.setEventId(event.getEventId());
		long id1 = 1L;
		bidderNumber.setId(id1);
        bidderNumber.setStaffUserId(user);
        bidderNumber.setUserId(user.getUserId());

        payment = new Payment();
        payment.setUserId(user.getUserId());
        payment.setEventId(event.getEventId());
		long staffUserId = 1L;
		payment.setStaffUserId(staffUserId);
		String stripeToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		payment.setStripeToken(stripeToken);
        payment.setBidderNumber(bidderNumber);
        payment.setCardIdOrPaymentMethodId(cardId);
        payment.setPaymentId(paymentId);
    }

    @Test
    void test_save_success() {

        //Execution
        paymentServiceImpl.save(payment);

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).save(paymentArgumentCaptor.capture());

        Payment paymentData = paymentArgumentCaptor.getValue();
        assertEquals(paymentData.getUserId(), payment.getUserId());
        assertEquals(paymentData.getEventId(), payment.getEventId());
        assertEquals(paymentData.getStaffUserId(), payment.getStaffUserId());
        assertEquals(paymentData.getStripeToken(), payment.getStripeToken());
        assertEquals(paymentData.getBidderNumber().getId(), bidderNumber.getId());
        assertEquals(paymentData.getCardIdOrPaymentMethodId(), payment.getCardIdOrPaymentMethodId());
        assertEquals(paymentData.getPaymentId(), payment.getPaymentId());
    }

    @Test
    void test_findByUserIdAndEventId_success() {

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));

        //Execution
        Optional<Payment> paymentDetails = paymentServiceImpl.findByUserIdAndEventId(user.getUserId(), event.getEventId());

        assertEquals(paymentDetails.get().getUserId(), payment.getUserId());
        assertEquals(paymentDetails.get().getEventId(), payment.getEventId());
        assertEquals(paymentDetails.get().getStaffUserId(), payment.getStaffUserId());
        assertEquals(paymentDetails.get().getStripeToken(), payment.getStripeToken());
        assertEquals(paymentDetails.get().getBidderNumber().getId(), bidderNumber.getId());
        assertEquals(paymentDetails.get().getCardIdOrPaymentMethodId(), payment.getCardIdOrPaymentMethodId());
        assertEquals(paymentDetails.get().getPaymentId(), payment.getPaymentId());
    }

    @Test
    void test_updateUserByNewUser_success() {

        //setup
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(payment);

        List<Long> newEventsDataList = new ArrayList<>();
        newEventsDataList.add(paymentList.iterator().next().getEventId());
        newEventsDataList.add(payment.getPaymentId());

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).delete(paymentArgumentCaptor.capture());
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_empty_and_newEventDataList_empty() {

        //setup
        List<Payment> paymentList = new ArrayList<>();

        List<Long> newEventsDataList = new ArrayList<>();

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success1() {

        //setup
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(payment);

        List<Long> newEventsDataList = new ArrayList<>();
        newEventsDataList.add(payment.getPaymentId());

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_And_newEventDataList_null() {

        //setup
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(payment);

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(null);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_empty_and_newEventDataList_null() {

        //setup
        List<Payment> paymentList = new ArrayList<>();

        List<Long> newEventsDataList = new ArrayList<>();
        newEventsDataList.add(payment.getPaymentId());

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(null);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_null_and_newEventDataList() {

        //setup
        List<Long> newEventsDataList = new ArrayList<>();
        newEventsDataList.add(payment.getPaymentId());

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(null);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_null_and_newEventDataList_empty() {

        //setup
        List<Long> newEventsDataList = new ArrayList<>();

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(null);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_and_newEventDataList_empty() {

        //setup
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(payment);

        List<Long> newEventsDataList = new ArrayList<>();

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_updateUserByNewUser_success_with_paymentList_empty_and_newEventDataList() {

        //setup
        List<Payment> paymentList = new ArrayList<>();

        List<Long> newEventsDataList = new ArrayList<>();
        newEventsDataList.add(payment.getEventId());
        newEventsDataList.add(payment.getPaymentId());

        //mock
        when(paymentRepository.findByUserId(user.getUserId())).thenReturn(paymentList);
        when(paymentRepository.findEventByUserId(newUser.getUserId())).thenReturn(newEventsDataList);
        Mockito.doNothing().when(paymentRepository).updateByNewUser(user.getUserId(), newUser.getUserId());

        //Execution
        paymentServiceImpl.updateUserByNewUser(user, newUser);
    }

    @Test
    void test_hasCardLinked_with_payment() {

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));

        //Execution
        boolean hasCardLinked = paymentServiceImpl.hasCardLinked(user.getUserId(), event.getEventId());

        assertTrue(hasCardLinked);
    }

    @Test
    void test_hasCardLinked_with_payment_empty() {

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.empty());

        //Execution
        boolean hasCardLinked = paymentServiceImpl.hasCardLinked(user.getUserId(), event.getEventId());

        assertFalse(hasCardLinked);
    }

    @Test
    void test_isCustomerCreatedForEvent_success() {

        //setup
        boolean customerCreated = false;

        //mock
        when(paymentRepository.isCustomerCreated(event.getEventId())).thenReturn(customerCreated);

        //Execution
        boolean isCustomerCreated = paymentServiceImpl.isCustomerCreatedForEvent(event);

        assertEquals(isCustomerCreated, customerCreated);
    }

    @Test
    void test_createOrGetPayment_success() throws StripeException, ApiException {

        //setup
        event.setEnableBidderRegistration(true);

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));
        when(allPaymentService.addCardToCustomer(payment.getStripeToken(), token, stripe, event, user, false, null)).thenReturn(cardId);
        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(bidderNumber);


        //Execution
        Payment paymentData = paymentServiceImpl.createOrGetPayment(token, user, event, name, stripe, newUser, false);

        assertEquals(paymentData.getUserId(), payment.getUserId());
        assertEquals(paymentData.getEventId(), payment.getEventId());
        assertEquals(paymentData.getStaffUserId(), payment.getStaffUserId());
        assertEquals(paymentData.getStripeToken(), payment.getStripeToken());
        assertEquals(paymentData.getBidderNumber().getId(), bidderNumber.getId());
        assertEquals(paymentData.getCardIdOrPaymentMethodId(), cardId);
        assertEquals(paymentData.getPaymentId(), payment.getPaymentId());

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).save(paymentArgumentCaptor.capture());

        Payment actualData = paymentArgumentCaptor.getValue();

        assertEquals(actualData.getUserId(), payment.getUserId());
        assertEquals(actualData.getEventId(), payment.getEventId());
        assertEquals(actualData.getStaffUserId(), payment.getStaffUserId());
        assertEquals(actualData.getStripeToken(), payment.getStripeToken());
        assertEquals(actualData.getBidderNumber().getId(), bidderNumber.getId());
        assertEquals(actualData.getCardIdOrPaymentMethodId(), cardId);
        assertEquals(actualData.getPaymentId(), payment.getPaymentId());
    }

    @Test
    void test_createOrGetPayment1_success_with_payment_and_without_stripeToken() throws StripeException, ApiException{

        //setup
        event.setEnableBidderRegistration(true);

        Payment payment = new Payment();
        payment.setPaymentId(paymentId);
        payment.setBidderNumber(bidderNumber);

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));

        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(bidderNumber);
        when(allPaymentService.createCustomerAndCardToCustomer(any(), any(), any(), any(), anyBoolean(), eq(null))).thenReturn(customerCardDto);



        //Execution
        Payment paymentData = paymentServiceImpl.createOrGetPayment(token, user, event, name, stripe, newUser, true, false);

        assertEquals(paymentData.getStripeToken(), customerCardDto.getCustomerId());
        assertEquals(paymentData.getBidderNumber().getId(), payment.getBidderNumber().getId());
        assertEquals(paymentData.getCardIdOrPaymentMethodId(), customerCardDto.getCardId());
        assertEquals(paymentData.getPaymentId(), payment.getPaymentId());

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).save(paymentArgumentCaptor.capture());

        Payment actualData = paymentArgumentCaptor.getValue();

        assertEquals(actualData.getStripeToken(), customerCardDto.getCustomerId());
        assertEquals(actualData.getBidderNumber().getId(), payment.getBidderNumber().getId());
        assertEquals(actualData.getCardIdOrPaymentMethodId(), customerCardDto.getCardId());
        assertEquals(actualData.getPaymentId(), payment.getPaymentId());
    }

    @Test
    void test_createOrGetPayment1_success_with_payment_null_and_without_stripeToken() throws StripeException, ApiException{

        //setup
        event.setEnableBidderRegistration(true);

        customerCardDto = new CustomerCardDto(customerId, cardId);

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.empty());

        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(bidderNumber);
        when(allPaymentService.createCustomerAndCardToCustomer(any(), any(), any(), any(), anyBoolean(), eq(null))).thenReturn(customerCardDto);




        //Execution
        Payment paymentData = paymentServiceImpl.createOrGetPayment(token, user, event, name, stripe, newUser, true, false);

        assertEquals(paymentData.getStripeToken(), customerId);
        assertEquals(paymentData.getUserId(), (long) user.getUserId());
        assertEquals(paymentData.getEventId(), event.getEventId());
        assertEquals(paymentData.getCardIdOrPaymentMethodId(), cardId);

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).save(paymentArgumentCaptor.capture());

        Payment actualData = paymentArgumentCaptor.getValue();
        assertEquals(actualData.getStripeToken(), customerId);
        assertEquals(actualData.getUserId(), (long) user.getUserId());
        assertEquals(actualData.getEventId(), event.getEventId());
        assertEquals(actualData.getCardIdOrPaymentMethodId(), cardId);
    }

    @Test
    void test_createOrGetPayment1_success_with_payment_null_and_without_stripeToken_enableBidderRegistration_false() throws StripeException, ApiException{

        //setup
        event.setEnableBidderRegistration(false);

        customerCardDto = new CustomerCardDto(customerId, cardId);

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.empty());


        when(allPaymentService.createCustomerAndCardToCustomer(any(), any(), any(), any(), anyBoolean(), eq(null))).thenReturn(customerCardDto);




        //Execution
        Payment paymentData = paymentServiceImpl.createOrGetPayment(token, user, event, name, stripe, newUser, true, false);

        assertEquals(paymentData.getStripeToken(),customerId);
        assertEquals(paymentData.getUserId(), (long) user.getUserId());
        assertEquals(paymentData.getEventId(), event.getEventId());
        assertEquals(paymentData.getCardIdOrPaymentMethodId(), cardId);

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentRepository, times(1)).save(paymentArgumentCaptor.capture());

        Payment actualData = paymentArgumentCaptor.getValue();
        assertEquals(actualData.getStripeToken(), customerId);
        assertEquals(actualData.getUserId(), (long) user.getUserId());
        assertEquals(actualData.getEventId(), event.getEventId());
        assertEquals(actualData.getCardIdOrPaymentMethodId(), cardId);
    }

    //@Test
    void test_createOrGetPayment1_throwException_CARD_NOT_LINKED() throws StripeException, ApiException{

        //setup
        Payment payment = new Payment();
        payment.setPaymentId(paymentId);
        payment.setBidderNumber(bidderNumber);

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));
        when(paymentRepository.findByUserIdAndEventIdAndCardId(anyLong(),anyLong(),anyString())).thenReturn(Optional.of(payment));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> paymentServiceImpl.createOrGetPayment("", user, event, name, stripe, newUser, true, false));
        assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.CARD_NOT_LINKED.getDeveloperMessage(), exception.getMessage());
    }

//    @Test
    void test_createOrGetPayment1_throwException_PAYMENT_NOT_FOUND_IN_DB() throws StripeException, ApiException{

        //setup
        Payment payment = new Payment();
        payment.setStripeToken("");

        //mock
        when(paymentRepository.findFirstByUserIdAndEventIdOrderByPaymentIdAsc(anyLong(), anyLong())).thenReturn(Optional.of(payment));
        when(paymentRepository.findByUserIdAndEventIdAndCardId(anyLong(),anyLong(),anyString())).thenReturn(Optional.of(payment));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> paymentServiceImpl.createOrGetPayment("", user, event, name, stripe, newUser, true, false));

        assertEquals(NotAcceptableException.PaymentCreationExceptionMsg.PAYMENT_NOT_FOUND_IN_DB.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createBidderIfNotPresent_success(){

        //setup
        long nextCount = 1;
        String message = user.getFirstName() + " welcome to the event! Your bidder number is "+bidderNumber.getBidderNumber()+". Please visit [auction_url] to bid online";

        nextCount = nextCount + bidderNumber.getBidderNumber();

        event.setEnableBidderRegistration(true);

        customerCardDto = new CustomerCardDto(customerId, "");

        payment.setTokenCreatedTime(new Date());

        //mock
        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(null);
        when(bidderNumberService.findOneByEventIdDesc(event)).thenReturn(bidderNumber);


        //Execution
        paymentServiceImpl.createBidderIfNotPresent(user, event, newUser, payment, true);

        ArgumentCaptor<BidderNumber> bidderNumberArgumentCaptor = ArgumentCaptor.forClass(BidderNumber.class);
        verify(bidderNumberService, times(1)).save(bidderNumberArgumentCaptor.capture());

        BidderNumber actualData = bidderNumberArgumentCaptor.getValue();
        assertEquals(actualData.getUserId(), (long) user.getUserId());
        assertEquals(actualData.getEventId(), event.getEventId());
        assertEquals(actualData.getBidderNumber(), nextCount);
        assertEquals(actualData.getRegistrationTime().toString(), payment.getTokenCreatedTime().toString());
        assertEquals(actualData.getSource(), newUser.getUserId().toString());
        assertEquals(actualData.getStaffUserId().getFirstName(), newUser.getFirstName());
    }

    @Test
    void test_createBidderIfNotPresent_success_with_lastBidderNumber_null(){

        //setup
        long nextCount = 1;
        String message = user.getFirstName() + " welcome to the event! Your bidder number is "+bidderNumber.getBidderNumber()+". Please visit [auction_url] to bid online";

        event.setEnableBidderRegistration(true);

        customerCardDto = new CustomerCardDto(customerId, "");

        payment.setTokenCreatedTime(new Date());

        //mock
        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(null);
        when(bidderNumberService.findOneByEventIdDesc(event)).thenReturn(null);
        when(textMessageUtils.getStaffBidderMessage(event, user, bidderNumber.getBidderNumber(),bidderNumber.getUserId(), user, true)).thenReturn(message);

        //Execution
        paymentServiceImpl.createBidderIfNotPresent(user, event, newUser, payment, true);

        ArgumentCaptor<BidderNumber> bidderNumberArgumentCaptor = ArgumentCaptor.forClass(BidderNumber.class);
        verify(bidderNumberService, times(1)).save(bidderNumberArgumentCaptor.capture());

        BidderNumber actualData = bidderNumberArgumentCaptor.getValue();
        assertEquals(actualData.getUserId(), (long) user.getUserId());
        assertEquals(actualData.getEventId(), event.getEventId());
        assertEquals(actualData.getBidderNumber(), nextCount);
        assertEquals(actualData.getRegistrationTime().toString(), payment.getTokenCreatedTime().toString());
        assertEquals(actualData.getSource(), newUser.getUserId().toString());
        assertEquals(actualData.getStaffUserId().getFirstName(), newUser.getFirstName());
    }

    @Test
    void test_createBidderIfNotPresent_success_with_newUser_null_and_isDonationmsg_false(){

        //setup
        long nextCount = 1;
        String message = user.getFirstName() + " welcome to the event! Your bidder number is "+bidderNumber.getBidderNumber()+". Please visit [auction_url] to bid online";

        nextCount = nextCount + bidderNumber.getBidderNumber();

        event.setEnableBidderRegistration(true);

        customerCardDto = new CustomerCardDto(customerId, "");

        payment.setTokenCreatedTime(new Date());

        //mock
        when(bidderNumberService.findByEventAndUser(event, user)).thenReturn(null);
        when(bidderNumberService.findOneByEventIdDesc(event)).thenReturn(bidderNumber);



        //Execution
        paymentServiceImpl.createBidderIfNotPresent(user, event, null, payment, false);

        ArgumentCaptor<BidderNumber> bidderNumberArgumentCaptor = ArgumentCaptor.forClass(BidderNumber.class);
        verify(bidderNumberService, times(1)).save(bidderNumberArgumentCaptor.capture());

        BidderNumber actualData = bidderNumberArgumentCaptor.getValue();
        assertEquals(actualData.getUserId(), (long) user.getUserId());
        assertEquals(actualData.getEventId(), event.getEventId());
        assertEquals(actualData.getBidderNumber(), nextCount);
        assertEquals(actualData.getRegistrationTime().toString(), payment.getTokenCreatedTime().toString());
        assertEquals(ONLINE, actualData.getSource());
        assertNull(actualData.getStaffUserId());
    }

    @Test
    void test_findByEventIdOrderByUserIdAsc_success() {

        //setup
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(payment);

        //mock
        when(paymentRepository.findByEventIdAndStripeTokenNotNullNotEmptyOrderByUserIdASC(anyLong())).thenReturn(paymentList);

        //Execution
        List<Payment> paymentDetails = paymentServiceImpl.findByEventIdOrderByUserIdAsc(event.getEventId());

        for (Payment actualData : paymentDetails) {

            assertEquals(actualData.getUserId(), payment.getUserId());
            assertEquals(actualData.getEventId(), payment.getEventId());
            assertEquals(actualData.getStaffUserId(), payment.getStaffUserId());
            assertEquals(actualData.getStripeToken(), payment.getStripeToken());
            assertEquals(actualData.getBidderNumber().getId(), bidderNumber.getId());
            assertEquals(actualData.getCardIdOrPaymentMethodId(), payment.getCardIdOrPaymentMethodId());
            assertEquals(actualData.getPaymentId(), payment.getPaymentId());
        }
    }

    @Test
    void test_findDistinctEventIdByUserId_success() {

        //setup
        List<Long> distinctEventIdList = new ArrayList<>();
        distinctEventIdList.add(event.getEventId());

        //mock
        when(paymentRepository.findDistinctEventIdByUserId(anyLong())).thenReturn(distinctEventIdList);

        //Execution
        List<Long> eventIds = paymentServiceImpl.findDistinctEventIdByUserId(user.getUserId());

        for (Long actualData : eventIds) {

            assertEquals( actualData.longValue(), event.getEventId());
        }
    }
}