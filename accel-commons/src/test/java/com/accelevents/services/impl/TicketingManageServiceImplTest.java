package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.badges.Badges;
import com.accelevents.badges.BadgesMaster;
import com.accelevents.billing.chargebee.dto.CAPUsageDto;
import com.accelevents.billing.chargebee.service.ChargebeePlanService;
import com.accelevents.billing.chargebee.service.ChargebeeService;
import com.accelevents.billing.chargebee.service.EventPlanConfigService;
import com.accelevents.billing.chargebee.util.ChargeBeeUtils;
import com.accelevents.common.dto.CreditCardChargesDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.BadgesResponseData;
import com.accelevents.dto.StripeDTO;
import com.accelevents.dto.TicketDisplayPageDto;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.EnumEventVenue;
import com.accelevents.messages.TicketBundleType;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.elasticsearch.leaderboard.LeaderboardService;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.session_speakers.services.TicketingTypeTagAndTrackService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.Constants;
import com.accelevents.utils.TimeZoneUtil;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg.NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED;
import static com.accelevents.exceptions.NotAcceptableException.TicketingExceptionMsg.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingManageServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingManageServiceImpl ticketingManageServiceImpl = new TicketingManageServiceImpl();

    @Spy
    private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl = new SeatsIoForMultipleEventsImpl();
    @Mock
    private RecurringEventsRepository recurringEventsRepository;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingTypeService ticketingTypeService;
    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    private TicketingCouponService ticketingCouponService;
    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;
    @Mock
    private SeatsIoService seatsIoService;
    @Mock
    private TicketingTypeRepository ticketingTypeRepository;
    @Mock
    private StripeService stripeService;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private TicketingStatisticsService ticketingStatisticsService;
    @Mock
    private EventTicketsRepoService eventTicketsRepoService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private TicketTypeTrackSessionLimitsService ticketTypeSessionRegistrationRestrictionService;

    @Mock
    private VatTaxService vatTaxService;

    @Mock
    private EventService eventService;
    @Mock
    private TicketingService ticketingService;
    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;
    @Mock
    private TicketingCouponRepository ticketingCouponRepository;
    @Mock
    private TicketingAccessCodeRepository ticketingAccessCodeRepository;
    @Mock
    private ChargebeePlanService chargebeePlanService;
    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;
    @Mock
    private SeatingCategoryService seatingCategoryService;
    @Mock
    private ResendTicketingEmailService resendTicketingEmailService;
    @Mock
    private SessionService sessionService;
    @Mock
    private LeaderboardService leaderboardService;
    @Mock
    private ChallengeConfigService challengeConfigService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private ChargeBeeUtils chargeBeeUtils;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private ChargebeeService chargebeeService;
    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;
    @Mock
    private TicketingTypeTagAndTrackService ticketingTypeTagAndTrackService;
    @Mock
    private BadgesService badgesService;
    @Mock
    private PayFlowConfigServiceImpl payFlowConfigService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Mock
    private SessionRepoService sessionRepoService ;
    @Mock
    private ConfirmationPagesRepository confirmationPagesRepository;
    @Mock
    private ConfirmationEmailRepository confirmationEmailRepository;

    private Event event;
    private Ticketing ticketing;
	private TicketStatus ticketStatus;
	private TicketingTypeSettingsDto ticketingTypeSettingsDto;
    private TicketingType ticketingType;
    private SeatingCategories seatingCategories;
    private EventTicketingDto eventTicketingDto;
	private CreditCardChargesDto creditCardChargesDto;
	private TicketTypeSettingDto ticketTypeSettingDto;

    private Optional<VatTax> vatTax;
	private TicketingCoupon  ticketingCoupon;
    private TicketingAccessCode ticketingAccessCode;
    private EventCategoryDto eventCategoryDto;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private RecurringEvents recurringEventsOpt;
    private Optional<EventPlanConfig> eventPlanConfig;
    private CAPUsageDto capUsageDto;
    private boolean isFromEventDetailsPage;
    private  boolean isSuperadmin;
    private String planName;
    private Organizer organizer;
    private boolean isSalesSuperAdmin;
    private Long customEmailTemplateId;


    private static String startDate = "2029/01/01 05:30";
    private static String endDate = "2029/04/04 05:30";
    private static Date startDate1 = new Date(startDate);
    private static Date endDate1 = new Date(endDate);
    private String chartKey = "chartKey";
    private long id =1L;
    private long recurringEventId=1L;
    private TicketTypeSettingDto ticketingTypeTemp;
    private TicketingType ticketingTypeFromDb;
    private BadgesResponseData badgesResponseData;
    private List<Long> ids = new ArrayList<>();
    private BadgesMaster badgesMaster;
    private Badges customeBadges;
    User user;

    @BeforeEach
    void setUp(){
        MockitoAnnotations.openMocks(this);
        // Prepare data
        event = EventDataUtil.getEvent();

        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setActivated(true);
        ticketing.setEventid(event);
        ticketing.setEventAddress("testAddress");
        ticketing.setShowRemainingTickets(true);
        ticketing.setEventStartDate(new Date());
        ticketing.setEventEndDate(new Date());

        TicketDisplayPageDto displayPageDto = new TicketDisplayPageDto();
        displayPageDto.setStartDate(ticketing.getEventStartDate());
        displayPageDto.setEndDate(ticketing.getEventEndDate());

		TicketHolderAttributes ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);

		user = EventDataUtil.getUser();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(id);

		StripeDTO stripeDTO = new StripeDTO();
        stripeDTO.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripeDTO.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);

        ticketingType = EventDataUtil.getTicketingType(event);

        recurringEventsOpt = new RecurringEvents();
        recurringEventsOpt.setId(id);
        recurringEventsOpt.setShowRemainingTicketsForRecurringEvent(RecurringEvents.ShowRemainingTicketsForRecurringEvent.TRUE);

        ticketingTypeTemp = new TicketTypeSettingDto();
        ticketingTypeTemp.setTypeId(1L);
        ticketingTypeTemp.setTracksSessionsLimitAllowed(false);

        ticketingTypeFromDb = new TicketingType();
        ticketingTypeFromDb.setId(1L);

        eventPlanConfig = Optional.of(new EventPlanConfig());
        capUsageDto = new CAPUsageDto();

        vatTax = Optional.of(new VatTax());

        badgesResponseData = new BadgesResponseData();
        badgesResponseData.setBadgeId(1L);
        ids.add(1L);
        ids.add(2L);
        badgesResponseData.setTicketingTypeIds(ids);

        badgesMaster = new BadgesMaster();
        badgesMaster.setTitle("Test Badge Save And Update");
        badgesMaster.setCustom(true);
        badgesMaster.setWidth(10.0);
        badgesMaster.setHeight(10.0);
        badgesMaster.setBleedAreaHeight(5.0);
        badgesMaster.setBleedAreaWidth(5.0);
        badgesMaster.setSafeAreaHeight(5.0);
        badgesMaster.setSafeAreaWidth(5.0);
        badgesMaster.setNumberOfView(1);
        badgesMaster.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        customeBadges = new Badges();
        customeBadges.setEventId(event.getEventId());
        customeBadges.setBadgeMasterId(1L);
        customeBadges.setBadgesMaster(badgesMaster);
        customeBadges.setBadgeName("Test Badge Save And Update");
        customeBadges.setWidth(10.0);
        customeBadges.setHeight(10.0);
        customeBadges.setBleedAreaHeight(5.0);
        customeBadges.setBleedAreaWidth(5.0);
        customeBadges.setSafeAreaHeight(5.0);
        customeBadges.setSafeAreaWidth(5.0);
        customeBadges.setNumberOfView(1);
        customeBadges.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");
    }

    @Test
    void test_saveTicketType_witIsSeatingFalse() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        ticketTypeSettingDto.setAllowAssignedSeating(false);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        Mockito.doNothing().when(ticketingManageServiceImpl).updateHolderRequiredAttributesForNewTicket(any(), any());
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);
        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

        //verify
        verify(transactionFeeConditionalLogicService, Mockito.times(1)).applyFeeInTicketType(any(), any(), anyBoolean(), anyBoolean());
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

    }

    @Test
    void test_saveTicketType_with_seatingAllow_Donation_throw_exception() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.HYBRID);
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setAllowAssignedSeating(true);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);
        event.setEventFormat(EventFormat.HYBRID);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_FOR_TICKET.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_saveTicketType_with_seatingAllow_AddON_throw_exception() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.HYBRID);
        ticketTypeSettingDto.setDataType(DataType.ADDON);
        ticketTypeSettingDto.setCategoryId(1L);
        ticketTypeSettingDto.setAllowAssignedSeating(true);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        event.setEventFormat(EventFormat.HYBRID);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_FOR_TICKET.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_saveTicketType_with_seatingAllow_With_paid_tickets_With_FreePlan_success() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.HYBRID);
        ticketTypeSettingDto.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setAllowAssignedSeating(true);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        event.setEventFormat(EventFormat.HYBRID);
        eventPlanConfig.get().setPlanConfigId(4L);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        Mockito.doNothing().when(ticketingManageServiceImpl).updateHolderRequiredAttributesForNewTicket(any(), any());
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig.get());
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);
        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());
        //assert
        assertEquals(Boolean.TRUE, ticketingTypeArgumentCaptor.getValue().isAllowAssignedSeating());
    }

    @Test
    void test_saveTicketType_with_seatingAllow_With_paid_tickets_With_PaidPlan_success() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.HYBRID);
        ticketTypeSettingDto.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setAllowAssignedSeating(true);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        event.setEventFormat(EventFormat.HYBRID);
        eventPlanConfig.get().setPlanConfigId(14L);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        Mockito.doNothing().when(ticketingManageServiceImpl).updateHolderRequiredAttributesForNewTicket(any(), any());

        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig.get());
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);
        when(confirmationPagesRepository.findByEventAndIsDefaultPage(any(), anyBoolean())).thenReturn(new CustomTemplates());
        when(confirmationEmailRepository.findCustomTemplatesIdByEventAndIsDefaultEmail(any(), anyBoolean(), any())).thenReturn(customEmailTemplateId);
        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());
        //assert
        assertEquals(Boolean.TRUE, ticketingTypeArgumentCaptor.getValue().isAllowAssignedSeating());
    }

    @Test
    void test_saveTicketType_with_seatingAllow_FreePlan_FreeTicket_throw_exception() throws InterruptedException {

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.PAID, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.HYBRID);
        ticketTypeSettingDto.setTicketType(TicketType.FREE);
        ticketTypeSettingDto.setAllowAssignedSeating(true);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);

        event.setEventFormat(EventFormat.HYBRID);
        eventPlanConfig.get().setPlanConfigId(4L);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(eventPlanConfigService.findByEventId(anyLong())).thenReturn(eventPlanConfig.get());
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingTypeFromDb);

        //execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.SEATING_FEATURE_NOT_ALLOWED_ON_FREE_PLAN.getDeveloperMessage(), exception.getMessage());
    }

   // @Test
    void test_saveTicketType_withTicketTypeDONATION() throws InterruptedException {

        //setup
        ticketing.setChartKey("ChartKey");
        ticketing.setActivated(false);
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, TicketType.DONATION, 1, 1L);
        ticketTypeSettingDto.setPrice(50.0);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(id);
        ticketingType.setTicketType(TicketType.DONATION);

        //mock
        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(null);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketHolderRequiredAttributesService.findByEventId(any())).thenReturn(new ArrayList<>());
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketingAccessCodeService.findByEvent(any())).thenReturn(new ArrayList<TicketingAccessCode>());
        when(stripeService.isStripeConnected(any())).thenReturn(true);
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        Mockito.doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any(), any(), any());
        Mockito.doReturn(true).when(ticketingManageServiceImpl).isPaidTicketExistsNow(anyBoolean(), any());

        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

        //verify
        verify(ticketingRepository, Mockito.times(1)).save(ticketingArgumentCaptor.capture());
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());
        assertNotNull(ticketingArgumentCaptor.getValue().getId());
    }
    static Object[] getRecurringEventId(){
        return new  Object[]{
             new Object[]{null},
             new Object[]{0L}
        };
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventId")
    void test_handleHolderRequiredAttributesForRecurringEvent_WithRecurringEventTrue(Long recurringEventId) throws InterruptedException {
        //setup
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1);
        ticketingType.setRecurringEventId(recurringEventId);

        List<TicketingType> newDefaultTicketType = new ArrayList<TicketingType>();
        newDefaultTicketType.add(ticketingType);

        RecurringEvents recurringEvents =new RecurringEvents();
        recurringEvents.setId(1L);

        RecurringEvents recurringEvents1 =new RecurringEvents();
        recurringEvents1.setId(2L);

        List<RecurringEvents> recurringEventsList = new ArrayList<RecurringEvents>();
        recurringEventsList.add(recurringEvents);
        recurringEventsList.add(recurringEvents1);

        //mock
        when(recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(any())).thenReturn(recurringEventsList);
        doNothing().when(recurringEventsMainScheduleService).executeHolderRequiredAttributesForRecurringEvent(any(),anyList(),anyList(),anyBoolean());

        //execute
        ticketingManageServiceImpl.handleHolderRequiredAttributesForRecurringEvent(event,newDefaultTicketType,recurringEventId);

        //verify
        verify(recurringEventsScheduleService).getRecurringEventsOnlyFutureDate(any());
    }

    @Test
    void test_handleHolderRequiredAttributesForRecurringEvent_WithRecurringEventTrueAndRecurringEventId() throws InterruptedException {
        //setup
        Long recurringEventId =1L;

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1);
        ticketingType.setRecurringEventId(recurringEventId);

        List<TicketingType> newDefaultTicketType = new ArrayList<TicketingType>();
        newDefaultTicketType.add(ticketingType);

        RecurringEvents recurringEvents =new RecurringEvents();
        recurringEvents.setId(recurringEventId);

        List<RecurringEvents> recurringEventsList = new ArrayList<RecurringEvents>();
        recurringEventsList.add(recurringEvents);

        //mock
        when(recurringEventsScheduleService.getRecurringEventById(anyLong())).thenReturn(Optional.of(recurringEvents));
        doNothing().when(recurringEventsMainScheduleService).executeHolderRequiredAttributesForRecurringEvent(any(),anyList(),anyList(),anyBoolean());

        //execute
        ticketingManageServiceImpl.handleHolderRequiredAttributesForRecurringEvent(event,newDefaultTicketType,recurringEventId);

        //verify
        verify(recurringEventsScheduleService).getRecurringEventById(anyLong());
    }

   // @Test
    void test_handleHolderRequiredAttributesForRecurringEvent_WithRecurringEventFalse() throws InterruptedException {
        //setup
        Long recurringEventId =1L;

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1);
        ticketingType.setRecurringEventId(recurringEventId);

        List<TicketingType> newDefaultTicketType = new ArrayList<TicketingType>();
        newDefaultTicketType.add(ticketingType);

        //execute
        ticketingManageServiceImpl.handleHolderRequiredAttributesForRecurringEvent(event,newDefaultTicketType,recurringEventId);
    }

    @Test
    void test_saveTicketTypeWithEnableSeatingAndNew() throws InterruptedException {
        //setup
        ticketing.setChartKey("chart_key");

        seatingCategories = setSeatingCategories(GENERAL_ADMISSION, true);
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, null, 1, 0L);
        ticketTypeSettingDto.setPrice(50.0);
        ticketTypeSettingDto.setCategoryId(1L);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any() , any(), any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(new TicketingType());

        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        ////doReturn(true).when(ticketingManageServiceImpl).isRequireToCallUpdateChartTableModes(any(), any(), any());

        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event,user);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

        //verify
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());
    }

    @Test
    void test_saveTicketTypeWithEnableSeating() throws InterruptedException {
        //setup
        ticketing.setChartKey("chart_key");

        seatingCategories = setSeatingCategories(GENERAL_ADMISSION, true);
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, null, 1, 0L);
        ticketTypeSettingDto.setPrice(50.0);

        customeBadges.setId(1L);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);


        //mock
        doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any() , any(), any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(new TicketingType());






        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());



        //doReturn(true).when(ticketingManageServiceImpl).isRequireToCallUpdateChartTableModes(any(), any(), any());


        //execute
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setCategoryId(1L);

        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

    }

    @Test
    void test_saveTicketTypeWithEnableSeatingAnd() throws InterruptedException {
        //setup
        String categories = "1";
        ticketing.setChartKey("chart_key");
        ticketing.setActivated(false);
        EventCategoryDto eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setName(Constants.GENERAL_ADMISSION);
        eventCategoryDto.setHavingVariations(true);
        eventCategoryDto.setId(id);
        List<EventCategoryDto> eventCategoryDtos = new ArrayList<>();

        seatingCategories = setSeatingCategories( GENERAL_ADMISSION, true);
        TicketTypeSettingDto ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,Constants.GENERAL_ADMISSION, null, 00, false, 0L, 0L, null, 0, 0L, null, 1, 1L);
        ticketTypeSettingDto.setPrice(50.0);
        ticketTypeSettingDto.setEventCategories(eventCategoryDtos);
        ticketTypeSettingDto.setCategoryId(1L);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(id);

        //mock
        doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any() , any(), any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(new TicketingType());






        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());
        //doReturn(true).when(ticketingManageServiceImpl).isRequireToCallUpdateChartTableModes(any(), any(), any());


        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

        //verify
        //verify(ticketingManageServiceImpl, Mockito.times(1)).isRequireToCallUpdateChartTableModes(any(), any(), any());
    }

    @Test
    void test_saveTicketTypeWithEnableSeatingAndSeatsIoError() throws InterruptedException {
        //setup
        ticketing.setChartKey("chart_key");
        EventCategoryDto eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setName(Constants.GENERAL_ADMISSION);
        eventCategoryDto.setHavingVariations(true);
        eventCategoryDto.setId(id);
        List<EventCategoryDto> eventCategoryDtos = new ArrayList<>();
        eventCategoryDtos.add(eventCategoryDto);

        seatingCategories = setSeatingCategories(GENERAL_ADMISSION, true);
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,Constants.GENERAL_ADMISSION, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 0L, null, 1, 0L);
        ticketTypeSettingDto.setPrice(50.0);
        ticketTypeSettingDto.setEventCategories(eventCategoryDtos);
        ticketTypeSettingDto.setHidden(false);
        ticketTypeSettingDto.setCategoryId(1L);

        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1);
        ticketingType.setNumberofticket(2);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);

        //mock
        doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any() , any(), any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingTypeService.findByidAndEvent(anyLong(), any())).thenReturn(ticketingType);
        doReturn(ticketingType).when(ticketingTypeService).setPositionForTicketingTypeAndsaveTicketingType(any());

        //execute
        ticketingManageServiceImpl.saveTicketType(ticketTypeSettingDto, event, user);
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);

        //verify
        verify(ticketingTypeService, Mockito.times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());
    }

    @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingFalse_For_NonRecurringEvents(){

        //setup
        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(false);
        ticketingTypeSettingsDto.setLimitEventCapacity(false);
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(eventCommonRepoService.findSoldCountByEvent(event)).thenReturn(BigDecimal.ZERO);
//        Mockito.doNothing().when(ticketingManageServiceImpl).handleSeatingLogicWithCategory(anyBoolean(), any(), any());

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,null);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.isShowRemainingTickets(), ticketingTypeSettingsDto.getShowRemainingTickets());
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
        assertFalse(ticketingData.isLimitEventCapacity());
    }


    @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingFalse_For_NonRecurringEvents_LimitEventCapacitySet(){

        //setup
        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(false);
        ticketingTypeSettingsDto.setLimitEventCapacity(true);
        ticketingTypeSettingsDto.setEventCapacity(new Double(10));
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(eventCommonRepoService.findSoldCountByEvent(event)).thenReturn(BigDecimal.ZERO);
//        Mockito.doNothing().when(ticketingManageServiceImpl).handleSeatingLogicWithCategory(anyBoolean(), any(), any());

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,null);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.isShowRemainingTickets(), ticketingTypeSettingsDto.getShowRemainingTickets());
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
        assertTrue(ticketingData.isLimitEventCapacity());
        assertEquals(ticketingData.getEventCapacity(), ticketing.getEventCapacity());

    }

    @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingFalset_ForRecurringEvents_GreaterThanZero_RecurringEventId(){

        //setup
        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(false);
        ticketingTypeSettingsDto.setLimitEventCapacity(false);
        ticketing.setRecurringEvent(true);

        TicketingTypeSettingStatusDto dto = new TicketingTypeSettingStatusDto();
        dto.setEventLimitCapacity("");
        dto.setShowRemainingTickets("CUSTOM_TRUE");
        //mock

        when(recurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,1L);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        ArgumentCaptor<RecurringEvents> recurringEventsArgumentCaptor=ArgumentCaptor.forClass(RecurringEvents.class);

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
        assertEquals(RecurringEvents.ShowRemainingTicketsForRecurringEvent.CUSTOM_TRUE,recurringEventsOpt.getShowRemainingTicketsForRecurringEvent());
    }

    @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingFalset_ForRecurringEvents_Not_GreaterThanZero_RecurringEventId(){

        //setup

        List<RecurringEvents> recurringEventsListNew= new ArrayList<RecurringEvents>();
        RecurringEvents recurringEvents1 =new RecurringEvents();
        recurringEvents1.setId(1L);

        RecurringEvents recurringEvents2 =new RecurringEvents();
        recurringEvents2.setId(2L);

        recurringEventsListNew.add(recurringEvents1);
        recurringEventsListNew.add(recurringEvents2);

        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(false);
        ticketingTypeSettingsDto.setLimitEventCapacity(false);
        ticketing.setRecurringEvent(true);
        List<RecurringEvents.ShowRemainingTicketsForRecurringEvent> hideForRecurringEventsList=new ArrayList<>();
        hideForRecurringEventsList.add(RecurringEvents.ShowRemainingTicketsForRecurringEvent.TRUE);
        hideForRecurringEventsList.add(RecurringEvents.ShowRemainingTicketsForRecurringEvent.FALSE);

        //mock

        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
//        Mockito.doNothing().when(ticketingManageServiceImpl).handleSeatingLogicWithCategory(anyBoolean(), any(), any());

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,0L);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Class<ArrayList<RecurringEvents>> listClass = (Class<ArrayList<RecurringEvents>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<RecurringEvents>> argument = ArgumentCaptor.forClass(listClass);
//        verify(recurringEventsRepository,times(2)).saveAll(argument.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();

        assertEquals(ticketingData.isShowRemainingTickets(),ticketingTypeSettingsDto.getShowRemainingTickets());
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
    }

    @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingTrueAndTicketingTypeCategoriesEmpty(){

        //setup
        String categories  = PAID;
        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(true);
        ticketingTypeSettingsDto.setLimitEventCapacity(false);

        seatingCategories = setSeatingCategories(GENERAL_ADMISSION, true);

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setCategoryId(1L);

        ticketingType = new TicketingType();
        //ticketingType.setCategories("");
        ticketingType.setTicketTypeName("General Admission");

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        eventCategoryDto  = setEventCategoryDto(GENERAL_ADMISSION);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
 //       Mockito.doNothing().when(ticketingManageServiceImpl).handleSeatingLogicWithCategory(anyBoolean(), any(), any());

        when(eventCommonRepoService.findSoldCountByEvent(event)).thenReturn(BigDecimal.ZERO);

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,recurringEventId);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, atLeastOnce()).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.isShowRemainingTickets(), ticketingTypeSettingsDto.getShowRemainingTickets());
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
    }

   // @Test
    void test_saveTicketingTypeSettings_withTicketingTypeSettingDtoIsSeatingTrueAndTicketingTypeCategories(){

        //setup
        String categories  = "1";
        ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(true);
        ticketingTypeSettingsDto.setCustomDisclaimer("Customer Disclaimer");
        ticketingTypeSettingsDto.setRequireDisclaimerConfirmation(true);
        ticketingTypeSettingsDto.setSeating(true);
        ticketingTypeSettingsDto.setLimitEventCapacity(false);

        ticketingType = new TicketingType();
        //ticketingType.setCategories("1");
        ticketingType.setTicketTypeName("General Admission");

        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        seatingCategories = new SeatingCategories();

        eventCategoryDto  = setEventCategoryDto(GENERAL_ADMISSION);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
//        Mockito.doNothing().when(ticketingManageServiceImpl).handleSeatingLogicWithCategory(anyBoolean(), any(), any());
        when(ticketingTypeRepository.findAllByEventIdRecurringIdNullOnlyPaid(any(), any())).thenReturn(ticketingTypeList);
        when(seatingCategoryRepository.findByIds(anyList())).thenReturn(Collections.singletonList(eventCategoryDto));

        //Execution
        ticketingManageServiceImpl.saveTicketingTypeSettings(ticketingTypeSettingsDto, event,recurringEventId);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        verify(ticketingTypeRepository, times(1)).findAllByEventIdRecurringIdNullOnlyPaid(any(), any());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.isShowRemainingTickets(), ticketingTypeSettingsDto.getShowRemainingTickets());
        assertEquals(ticketingData.getCustomDisclaimer(), ticketingTypeSettingsDto.getCustomDisclaimer());
        assertEquals(ticketingData.isRequireDisclaimerConfirmation(), ticketingTypeSettingsDto.isRequireDisclaimerConfirmation());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
    }

  @Test
    void test_getTicketingTypeSettings_for_RecurringEventId_success() {

      //setup
      ticketing.setShowRemainingTickets(true);
      ticketing.setAllowEditingOfDisclaimer(true);
      ticketing.setCustomDisclaimer("CustomerDisclaimer");
      ticketing.setChartKey("ChartKey");
      ticketing.setRequireDisclaimerConfirmation(true);
      ticketing.setLimitEventCapacity(true);

      recurringEventsOpt.setEventCapacity(new Double(10));

      List<TicketingType> numberOfTickets = ticketingTypeService.findAllByTicketing(recurringEventId, event);
      long totalNumberOfEventTickets = numberOfTickets.stream().mapToLong(TicketingType::getNumberOfTickets).sum();
      if((totalNumberOfEventTickets < recurringEventsOpt.getEventCapacity())) {
          ticketing.setEventCapacity((double) totalNumberOfEventTickets);
      }else {
          ticketing.setEventCapacity(recurringEventsOpt.getEventCapacity());
      }

      TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = new TicketingTypeSettingStatusDto();
      ticketingTypeSettingStatusDto.setShowRemainingTickets(Boolean.FALSE.toString());
      ticketingTypeSettingStatusDto.setEventLimitCapacity(Boolean.TRUE.toString());
      recurringEventsOpt.setRecurringJson(parseToJsonString(ticketingTypeSettingStatusDto));

      //mock
      when(recurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
      when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

      //Execution
      NewTicketingTypeSettingDto ticketingTypeSettingDtoData = ticketingManageServiceImpl.getTicketingTypeSettings(event, recurringEventId);

      //Assertion
      assertEquals(ticketingTypeSettingDtoData.getShowRemainingTickets(), false);
      assertEquals(ticketingTypeSettingDtoData.getAllowEditingOfDisclaimer(), ticketing.isAllowEditingOfDisclaimer());
      assertEquals(ticketingTypeSettingDtoData.getCustomDisclaimer(), ticketing.getCustomDisclaimer());
      assertEquals(ticketingTypeSettingDtoData.isRequireDisclaimerConfirmation(), ticketing.isRequireDisclaimerConfirmation());
      assertEquals(ticketingTypeSettingDtoData.getSeating(), StringUtils.isNotBlank(ticketing.getChartKey()));
      assertFalse(ticketingTypeSettingDtoData.isLimitEventCapacity());
      assertEquals(ticketingTypeSettingDtoData.getEventCapacity(), ticketing.getEventCapacity());

      verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
  }

    @Test
    void test_getTicketingTypeSettings_success(){

        //setup
        ticketing.setShowRemainingTickets(true);
        ticketing.setAllowEditingOfDisclaimer(true);
        ticketing.setCustomDisclaimer("CustomerDisclaimer");
        ticketing.setChartKey("ChartKey");
        ticketing.setRequireDisclaimerConfirmation(true);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

        //Execution
        NewTicketingTypeSettingDto ticketingTypeSettingDtoData = ticketingManageServiceImpl.getTicketingTypeSettings(event,null);

        //Assertion
        assertEquals(ticketingTypeSettingDtoData.getShowRemainingTickets(), ticketing.isShowRemainingTickets());
        assertEquals(ticketingTypeSettingDtoData.getAllowEditingOfDisclaimer(), ticketing.isAllowEditingOfDisclaimer());
        assertEquals(ticketingTypeSettingDtoData.getCustomDisclaimer(), ticketing.getCustomDisclaimer());
        assertEquals(ticketingTypeSettingDtoData.isRequireDisclaimerConfirmation(), ticketing.isRequireDisclaimerConfirmation());
        assertEquals(ticketingTypeSettingDtoData.getSeating(), StringUtils.isNotBlank(ticketing.getChartKey()));

        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
    }

    @Test
    void test_saveTicketing_withRecurringEventFalseAndEventVenueVenueAndEventTicketingIdNull(){

        //setup
        eventTicketingDto = setEventTicketingDto("UTC",  EnumEventVenue.VENUE.name());

        event.setTimezoneId("Universal Coordinated Time (UTC)");
        event.setEquivalentTimeZone("UTC");
        organizer = new Organizer();
        PlanConfig planConfig  = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.FREE_PLAN.getName());
        organizer.setPlanConfig(planConfig);

        boolean isOnlineEvent = EnumEventVenue.ONLINE_VIRTUAL_EVENT.name().equals(eventTicketingDto.getEventVenueType());

        ticketing.setRecurringEvent(false);

        Date startDate = TimeZoneUtil.getDateInUTC(eventTicketingDto.getEventStartDate(), event.getEquivalentTimeZone(), null);
        Date endDate = TimeZoneUtil.getDateInUTC(eventTicketingDto.getEventEndDate(), event.getEquivalentTimeZone(), null);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);


        Mockito.doNothing().when(leaderboardService).resetGamificationData(anyLong());

        //Execution
        ticketingManageServiceImpl.saveTicketing(eventTicketingDto, event, isFromEventDetailsPage, isSuperadmin, isSalesSuperAdmin, Boolean.TRUE);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepoService, times(2)).save(eventArgumentCaptor.capture());

        Event eventData = eventArgumentCaptor.getValue();
        assertEquals(eventData.getTimezoneId(), eventTicketingDto.getTimezoneId());
        assertEquals(eventData.getEquivalentTimeZone(),eventTicketingDto.getEquivalentTimezone());
        assertEquals(eventData.getTicketingId().longValue(), ticketing.getId());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getEventStartDate(), startDate);
        assertEquals(ticketingData.getEventEndDate(), endDate);
        assertEquals(ticketingData.getEventAddress(), eventTicketingDto.getEventAddress());
        assertEquals(ticketingData.getLatitude(), eventTicketingDto.getLatitude());
        assertEquals(ticketingData.getLongitude(), eventTicketingDto.getLongitude());
        assertEquals(ticketingData.isOnlineEvent(), isOnlineEvent);

        verify(leaderboardService).resetGamificationData(anyLong());

    }

    @Test
    void test_saveTicketing_withRecurringEventTrueAndEventVenueONLINE_EVENTAndEventTicketingIdAndTimeZoneIdEmpty(){

        //setup
        eventTicketingDto = setEventTicketingDto("",  EnumEventVenue.ONLINE_VIRTUAL_EVENT.name());
        eventTicketingDto.setEventFormat(EventFormat.IN_PERSON);
        event.setTicketingId(id);
        event.setEventFormat(EventFormat.IN_PERSON);

        boolean isOnlineEvent = EnumEventVenue.ONLINE_VIRTUAL_EVENT.name().equals(eventTicketingDto.getEventVenueType());

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);


        //Execution
        ticketingManageServiceImpl.saveTicketing(eventTicketingDto, event, isFromEventDetailsPage, isSuperadmin, isSalesSuperAdmin, Boolean.TRUE);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        /*ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventService, times(1)).save(eventArgumentCaptor.capture());

        Event eventData = eventArgumentCaptor.getValue();
        assertEquals(eventData.getEventId(), event.getEventId());*/

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertNull(ticketingData.getEventAddress());
        assertEquals(ticketingData.isOnlineEvent(), isOnlineEvent);
    }

    @Test
    void test_saveTicketing_withRecurringEventTrueAndEventVenueEmptyAndEventTicketingIdAndTimeZoneIdEmpty(){

        //setup
        PlanConfig planConfig  = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.FREE_PLAN.getName());
        eventTicketingDto = setEventTicketingDto("", "");

        event.setTicketingId(id);

        ticketing.setRecurringEvent(true);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

        //Execution
        ticketingManageServiceImpl.saveTicketing(eventTicketingDto, event, isFromEventDetailsPage, isSuperadmin, isSalesSuperAdmin, Boolean.TRUE);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
       /* ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventService, times(1)).save(eventArgumentCaptor.capture());

        Event eventData = eventArgumentCaptor.getValue();
        assertEquals(eventData.getEventId(), event.getEventId());*/

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertNull(ticketingData.getEventAddress());
    }

    @Test
    void test_getTicketing_withWhiteLabelAndTransactionFeeConditionalLogicListEmpty(){

        //setup
		WhiteLabel whiteLabel = new WhiteLabel();
        event.setWhiteLabel(whiteLabel);
        event.setTimezoneId("Universal Coordinated Time (UTC)");
        event.setEquivalentTimeZone("UTC");
        ticketing.setLongitude("-71.07818750000001");
        ticketing.setLatitude("42.3493136");
        ticketing.setChartKey("chartKey");
        ticketing.setPostEventAccessMinutes(1800);
        ticketing.setPreEventAccessMinutes(30);
        EnumEventVenue enumEventVenue = EnumEventVenue.VENUE;
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        creditCardChargesDto = new CreditCardChargesDto(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingService.getEventVenue(any())).thenReturn(enumEventVenue);
        when(transactionFeeConditionalLogicService.getRecordByEvent(any())).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(any())).thenReturn(creditCardChargesDto);

        //Execution
        EventTicketingDto eventTicketingDtoData = ticketingManageServiceImpl.getTicketing(event);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        verify(ticketingService, times(1)).getEventVenue(any());
        verify(transactionFeeConditionalLogicService, times(1)).getRecordByEvent(any());
        verify(stripeService, times(1)).getCCProcessingDetails(any());

        assertEquals(eventTicketingDtoData.getEventAddress(), ticketing.getEventAddress());
        assertEquals(eventTicketingDtoData.getLatitude(), ticketing.getLatitude());
        assertEquals(eventTicketingDtoData.getLongitude(), ticketing.getLongitude());
        assertEquals(eventTicketingDtoData.getEventVenueType(), enumEventVenue.name());
        assertEquals(eventTicketingDtoData.getSeatingChartKey(), ticketing.getChartKey());
        assertEquals(eventTicketingDtoData.getEventKey(), String.valueOf(event.getEventId()));
        assertEquals(eventTicketingDtoData.getAvailableTimeZone(), TimeZoneUtil.getAllTimeZone());
        assertEquals(eventTicketingDtoData.getTimezoneId(), event.getTimezoneId());
        assertEquals(eventTicketingDtoData.getEquivalentTimezone(),event.getEquivalentTimeZone());
        assertEquals(eventTicketingDtoData.getCurrencySymbol(), event.getCurrency().getSymbol());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingFlat(), creditCardChargesDto.getCreditCardFlat());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingPercentage(), creditCardChargesDto.getCreditCardPercentage());
        assertEquals(AE_FLAT_FEE_ONE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeeFlat());
        assertEquals(AE_FEE_PERCENTAGE_THREE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeePercentage());
        assertEquals(WL_FEE_FLAT, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeeFlat());
        assertEquals(WL_FEE_PERCENTAGE, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeePercentage());
    }

    @Test
    void test_getTicketing_withWhiteLabelNUllAndTransactionFeeConditionalLogicListEmpty(){

        //setup
        event.setTimezoneId("Universal Coordinated Time (UTC)");
        event.setEquivalentTimeZone("UTC");
        ticketing.setLongitude("-71.07818750000001");
        ticketing.setLatitude("42.3493136");
        ticketing.setChartKey("chartKey");
        ticketing.setPreEventAccessMinutes(1800);
        ticketing.setPreEventAccessMinutes(30);
        EnumEventVenue enumEventVenue = EnumEventVenue.VENUE;
        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        creditCardChargesDto = new CreditCardChargesDto(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingService.getEventVenue(any())).thenReturn(enumEventVenue);
        when(transactionFeeConditionalLogicService.getRecordByEvent(any())).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(any())).thenReturn(creditCardChargesDto);

        //Execution
        EventTicketingDto eventTicketingDtoData = ticketingManageServiceImpl.getTicketing(event);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        verify(ticketingService, times(1)).getEventVenue(any());
        verify(transactionFeeConditionalLogicService, times(1)).getRecordByEvent(any());
        verify(stripeService, times(1)).getCCProcessingDetails(any());

        assertEquals(eventTicketingDtoData.getEventAddress(), ticketing.getEventAddress());
        assertEquals(eventTicketingDtoData.getLatitude(), ticketing.getLatitude());
        assertEquals(eventTicketingDtoData.getLongitude(), ticketing.getLongitude());
        assertEquals(eventTicketingDtoData.getEventVenueType(), enumEventVenue.name());
        assertEquals(eventTicketingDtoData.getSeatingChartKey(), ticketing.getChartKey());
        assertEquals(eventTicketingDtoData.getEventKey(), String.valueOf(event.getEventId()));
        assertEquals(eventTicketingDtoData.getAvailableTimeZone(), TimeZoneUtil.getAllTimeZone());
        assertEquals(eventTicketingDtoData.getTimezoneId(), event.getTimezoneId());
        assertEquals(eventTicketingDtoData.getEquivalentTimezone(),event.getEquivalentTimeZone());
        assertEquals(eventTicketingDtoData.getCurrencySymbol(), event.getCurrency().getSymbol());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingFlat(), creditCardChargesDto.getCreditCardFlat());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingPercentage(), creditCardChargesDto.getCreditCardPercentage());
        assertEquals(AE_FLAT_FEE_ONE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeeFlat());
        assertEquals(AE_FEE_PERCENTAGE_THREE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeePercentage());
        assertEquals(0, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeeFlat());
        assertEquals(0, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeePercentage());
    }

    @Test
    void test_getTicketing_withWhiteLabelNUllAndTransactionFeeConditionalLogicList(){

        //setup
        event.setTimezoneId("Universal Coordinated Time (UTC)");
        event.setEquivalentTimeZone("UTC");
        ticketing.setLongitude("-71.07818750000001");
        ticketing.setLatitude("42.3493136");
        ticketing.setChartKey("chartKey");
        ticketing.setPreEventAccessMinutes(30);
        ticketing.setPostEventAccessMinutes(1800);
        EnumEventVenue enumEventVenue = EnumEventVenue.VENUE;
		TransactionFeeConditionalLogic transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setAeFeeFlat(AE_FLAT_FEE_ONE);
        transactionFeeConditionalLogic.setAeFeePercentage(AE_FEE_PERCENTAGE_THREE);
        transactionFeeConditionalLogic.setWlAFeeFlat(WL_FEE_FLAT);
        transactionFeeConditionalLogic.setWlAFeePercentage(WL_FEE_PERCENTAGE);
        transactionFeeConditionalLogic.setFromTicketPriceThreshold(1d);
        transactionFeeConditionalLogic.setOperator("Operator");

        List<TransactionFeeConditionalLogic> transactionFeeConditionalLogicList = new ArrayList<>();
        transactionFeeConditionalLogicList.add(transactionFeeConditionalLogic);

        creditCardChargesDto = new CreditCardChargesDto(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE);

        //mock
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(ticketingService.getEventVenue(any())).thenReturn(enumEventVenue);
        when(transactionFeeConditionalLogicService.getRecordByEvent(any())).thenReturn(transactionFeeConditionalLogicList);
        when(stripeService.getCCProcessingDetails(any())).thenReturn(creditCardChargesDto);

        //Execution
        EventTicketingDto eventTicketingDtoData = ticketingManageServiceImpl.getTicketing(event);

        //Assertion
        verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
        verify(ticketingService, times(1)).getEventVenue(any());
        verify(transactionFeeConditionalLogicService, times(1)).getRecordByEvent(any());
        verify(stripeService, times(1)).getCCProcessingDetails(any());

        assertEquals(eventTicketingDtoData.getEventAddress(), ticketing.getEventAddress());
        assertEquals(eventTicketingDtoData.getLatitude(), ticketing.getLatitude());
        assertEquals(eventTicketingDtoData.getLongitude(), ticketing.getLongitude());
        assertEquals(eventTicketingDtoData.getEventVenueType(), enumEventVenue.name());
        assertEquals(eventTicketingDtoData.getSeatingChartKey(), ticketing.getChartKey());
        assertEquals(eventTicketingDtoData.getEventKey(), String.valueOf(event.getEventId()));
        assertEquals(eventTicketingDtoData.getAvailableTimeZone(), TimeZoneUtil.getAllTimeZone());
        assertEquals(eventTicketingDtoData.getTimezoneId(), event.getTimezoneId());
        assertEquals(eventTicketingDtoData.getEquivalentTimezone(),event.getEquivalentTimeZone());
        assertEquals(eventTicketingDtoData.getCurrencySymbol(), event.getCurrency().getSymbol());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingFlat(), creditCardChargesDto.getCreditCardFlat());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getCreditCardProcessingPercentage(), creditCardChargesDto.getCreditCardPercentage());
        assertEquals(AE_FLAT_FEE_ONE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeeFlat());
        assertEquals(AE_FEE_PERCENTAGE_THREE, eventTicketingDtoData.getTicketingFee().iterator().next().getAeFeePercentage());
        assertEquals(WL_FEE_FLAT, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeeFlat());
        assertEquals(WL_FEE_PERCENTAGE, eventTicketingDtoData.getTicketingFee().iterator().next().getWlFeePercentage());
        assertEquals(eventTicketingDtoData.getTicketingFee().iterator().next().getFromThreshold(), transactionFeeConditionalLogic.getFromTicketPriceThreshold());
        assertSame(eventTicketingDtoData.getTicketingFee().iterator().next().getOperator(), transactionFeeConditionalLogic.getOperator());
    }

    @Test
    void test_isSeatingToNonSeatingFlagUpdate_seatingChartAndseatingEanbleTrue(){

        //setup
        ticketing.setChartKey("chartKey");
        Boolean isSeatingEnable = false;

        //Execution
        boolean seatingToNonSeatingFlagUpdate = ticketingManageServiceImpl.isSeatingToNonSeatingFlagUpdate(isSeatingEnable, ticketing);

        //Assertion
        assertTrue(seatingToNonSeatingFlagUpdate);
    }

   // @Test
    void test_checkIsSeatingFlagUpdate_seatingChartAndseatingEanbleTrue(){

        //setup
        ticketing.setChartKey("chartKey");
        Boolean isSeatingEnable = false;

        //Execution
     //   ticketingManageServiceImpl.checkIsSeatingFlagUpdate(isSeatingEnable, event, ticketing);

        //Assertion
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingdata = ticketingArgumentCaptor.getValue();
        assertNull(ticketingdata.getChartKey());
    }

    //@Test
    void test_handleSeatingLogicWithCategory_withChartKeyAndIsSeatingFalse(){

        //setup
        ticketing.setChartKey("chartKey");
        Boolean isSeatingEnable = false;

        //mock
        Mockito.doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(anyString(), anyLong(), anyBoolean());

        //Execution
       // ticketingManageServiceImpl.handleSeatingLogicWithCategory(isSeatingEnable, event, ticketing);

        //Assertion
        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingdata = ticketingArgumentCaptor.getValue();
        assertNull(ticketingdata.getChartKey());
    }

   // @Test
    void test_handleSeatingLogicWithCategory_withChartKeyEmptyAndIsSeatingTrueAndWithSeatingCategoryAndCategoryColor(){

        //setup
        ticketing.setChartKey("");
        Boolean isSeatingEnable = true;

        ticketingType = new TicketingType();
        //ticketingType.setCategories("1");
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        seatingCategories = new SeatingCategories();
        seatingCategories.setName("blue");
        seatingCategories.setColor("#0066ff");

        //mock
        when(ticketingTypeService.findAllByTicketing(anyLong(), any())).thenReturn(ticketingTypeList);
        when(seatingCategoryRepository.findById(anyLong())).thenReturn(Optional.of(seatingCategories));
        when(seatsIoService.createOrUpdateChart(anyLong(), anyString(), anyList(), anyString())).thenReturn(chartKey);
        Mockito.doNothing().when(seatsIoService).publishChart(anyString());

        //Execution
 //       ticketingManageServiceImpl.handleSeatingLogicWithCategory(isSeatingEnable, event, ticketing);

        //Assertion
        verify(ticketingTypeService, times(1)).findAllByTicketing(anyLong(), any());
        verify(seatingCategoryRepository, times(1)).findByIdIn(anyList());
        verify(seatsIoService, times(1)).createOrUpdateChart(anyLong(), anyString(), anyList(), anyString());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingdata = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingdata.getChartKey(), chartKey);
    }

    //@Test
    void test_handleSeatingLogicWithCategory_withChartKeyEmptyAndIsSeatingTrueAndSeatingCategoryEmpty(){

        //setup
        ticketing.setChartKey("");
        Boolean isSeatingEnable = true;

        ticketingType = new TicketingType();
        //ticketingType.setCategories("1");
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        //mock
        when(ticketingTypeService.findAllByTicketing(anyLong(), any())).thenReturn(ticketingTypeList);
        when(seatingCategoryRepository.findByIdIn(anyList())).thenReturn(Collections.emptyList());
        when(seatsIoService.createOrUpdateChart(anyLong(), anyString(), anyList(), anyString())).thenReturn(chartKey);
        Mockito.doNothing().when(seatsIoService).publishChart(anyString());

        //Execution
     //   ticketingManageServiceImpl.handleSeatingLogicWithCategory(isSeatingEnable, event, ticketing);

        //Assertion
        verify(ticketingTypeService, times(1)).findAllByTicketing(anyLong(), any());
        verify(seatingCategoryRepository, times(1)).findByIdIn(anyList());
        verify(seatsIoService, times(1)).createOrUpdateChart(anyLong(), anyString(), anyList(), anyString());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingRepository, times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingdata = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingdata.getChartKey(), chartKey);
    }

  //  @Test
    void test_handleSeatingLogicWithCategory_withChartKeyAndIsSeatingTrueAndWithSeatingCategoryAndCategoryColorEmpty(){

        //setup
        ticketing.setChartKey(chartKey);
        Boolean isSeatingEnable = true;

        ticketingType = new TicketingType();
        //ticketingType.setCategories("1");
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        seatingCategories = new SeatingCategories();
        seatingCategories.setName("blue");
        seatingCategories.setColor("");

        //mock
        when(ticketingTypeService.findAllByTicketing(anyLong(), any())).thenReturn(ticketingTypeList);
        when(seatingCategoryRepository.findByIdIn(anyList())).thenReturn(Collections.singletonList(seatingCategories));
        when(seatsIoService.createOrUpdateChart(anyLong(), anyString(), anyList(), anyString())).thenReturn(chartKey);
        Mockito.doNothing().when(seatsIoService).publishChart(anyString());

        //Execution
      //  ticketingManageServiceImpl.handleSeatingLogicWithCategory(isSeatingEnable, event, ticketing);

        //Assertion
        verify(ticketingTypeService, times(1)).findAllByTicketing(anyLong(), any());
        verify(seatingCategoryRepository, times(1)).findByIdIn(anyList());
        verify(seatsIoService, times(1)).createOrUpdateChart(anyLong(), anyString(), anyList(), anyString());
    }

  //  @Test
    void test_handleSeatingLogicWithCategory_withChartKeyAndIsSeatingTrueAndWithSeatingCategoryEmptyAndCategoryColorEmpty(){

        //setup
        ticketing.setChartKey(chartKey);
        Boolean isSeatingEnable = true;

        ticketingType = new TicketingType();
        //ticketingType.setCategories("");
        List<TicketingType> ticketingTypeList = new ArrayList<>();
        ticketingTypeList.add(ticketingType);

        //mock
        when(ticketingTypeService.findAllByTicketing(anyLong(), any())).thenReturn(ticketingTypeList);
        when(seatsIoService.createOrUpdateChart(anyLong(), anyString(), anyList(), anyString())).thenReturn(chartKey);
        Mockito.doNothing().when(seatsIoService).publishChart(anyString());

        //Execution
       // ticketingManageServiceImpl.handleSeatingLogicWithCategory(isSeatingEnable, event, ticketing);

        //Assertion
        verify(ticketingTypeService, times(1)).findAllByTicketing(anyLong(), any());
        verify(seatsIoService, times(1)).createOrUpdateChart(anyLong(), anyString(), anyList(), anyString());
    }

    @Test
    void test_setTicketTypesSalesEndTimeAndStatus_withRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);

        ticketingType = new TicketingType();

        //Execution
        ticketingManageServiceImpl.setTicketTypesSalesEndTimeAndStatus(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getRecurringEventSalesEndTime(), ticketTypeSettingDto.getRecurringEventSalesEndTime());
        assertEquals(ticketingType.getRecurringEventSalesEndStatus(), ticketTypeSettingDto.getRecurringEventSalesEndStatus());
    }

    @Test
    void updateCreatedFromField_withRecurringEventSalesEndStatusNotEqual(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);

        ticketingType = new TicketingType();
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.END);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withRecurringEventSalesEndTimeNotEqual(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);

        ticketingType = new TicketingType();
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(70);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withTicketTypeNameNotEqual(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L, "GeneralAdmission", TicketingType.RecurringEventSalesEndStatus.START, 60, false, 0L, 0L, null, 0, 0L, null, 0, 0L);
        ticketingType = setTicketingType(11L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 10, 10);
        ticketingType.setTicketTypeName("GeneralAdmission1");

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withPassfeetobuyerNotEqual(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L, "General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 0L, 0L, null, 0, 0L, null, 0, 0L);
        ticketingType = setTicketingType(11L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 10, 10);
        ticketingType.setPassfeetobuyer(false);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withMaxTickerPerBuyerNotEqual(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,"General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 0L, null, 0, 0L, null, 0, 0L);
        ticketingType = setTicketingType(11L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 10, 10);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withMinTickerPerBuyerNotEqual(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,"General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 2L, null, 0, 0L, null, 0, 0L);
        ticketingType = setTicketingType(10L, 1L, TicketType.PAID, 1L, null, 0, 0);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withBundleTypeNotEqual(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L, "General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 1L, TicketBundleType.TABLE, 0, 0L, null, 0, 0L);

        ticketingType = setTicketingType(10L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 10, 10);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withBundleTypeNotEqualINDIVIDUAL_TICKET(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,"General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 1L, TicketBundleType.TABLE, 10, 0L, null, 0, 0L);
        ticketingType = setTicketingType(10L, 1L, TicketType.PAID, 1L, TicketBundleType.TABLE, 10, 10);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withBundleTypeNotEqualINDIVIDUAL_TICKETAndTicketsPerTableNUll(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L, "GeneralAdmission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 1L, TicketBundleType.TABLE, 0, 0L, null, 0, 0L);

        ticketingType = new TicketingType();
        ticketingType.setTicketTypeName("GeneralAdmission");
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(60);
        ticketingType.setPassfeetobuyer(true);
        ticketingType.setMaxTicketsPerBuyer(10L);
        ticketingType.setMinTicketsPerBuyer(1L);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(10);
        ticketingType.setCreatedFrom(1L);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertNotEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void updateCreatedFromField_withBundleTypeEqualINDIVIDUAL_TICKET(){

        //setup
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,"General Admission", TicketingType.RecurringEventSalesEndStatus.START, 60, true, 10L, 1L, TicketBundleType.INDIVIDUAL_TICKET, 11, 0L, null, 0, 0L);
        ticketingType = setTicketingType(10L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 11, 10);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getNumberOfTicketPerTable(), ticketTypeSettingDto.getTicketsPerTable().intValue());
    }

    @Test
    void test_updateCreatedFromField_withDifferentBundleType(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setName("General Admission");
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);

        ticketingType = setTicketingType(10L, 1L, TicketType.PAID, 1L, TicketBundleType.INDIVIDUAL_TICKET, 11, 10);

        //Execution
        ticketingManageServiceImpl.updateCreatedFromField(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getTicketTypeName(), ticketTypeSettingDto.getName());
    }

    @Test
    void test_getTotalTicketsDb_ticketBundleTypeTable(){

        //setup
        int numberOfTicket = 10;
        ticketingType = new TicketingType();
        ticketingType.setNumberofticket(numberOfTicket);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(2);
        int totalTicket = ticketingType.getNumberofticket() * ticketingType.getNumberOfTicketPerTable();

        //Execution
        int totalTickets = ticketingManageServiceImpl.getTotalTicketsDb(ticketingType);

        //Assertion
        assertEquals(totalTicket, totalTickets);
    }

    @Test
    void test_getTotalTicketsDb_ticketBundleTypeNotTable(){

        //setup
        int numberOfTicket = 10;
        ticketingType = new TicketingType();
        ticketingType.setNumberofticket(numberOfTicket);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketingType.setNumberOfTicketPerTable(2);
        int totalTicket = ticketingType.getNumberofticket();

        //Execution
        int totalTickets = ticketingManageServiceImpl.getTotalTicketsDb(ticketingType);

        //Assertion
        assertEquals(totalTicket, totalTickets);
    }

    @Test
    void test_getTotalTickets_ticketBundleTypeTable(){

        //setup
        int numberOfTicket = 10;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setNumberOfTicket(numberOfTicket);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(2);
        int totalTicket = ticketTypeSettingDto.getNumberOfTicket() * ticketTypeSettingDto.getTicketsPerTable();

        //Execution
        int totalTickets = ticketingManageServiceImpl.getTotalTickets(ticketTypeSettingDto);

        //Assertion
        assertEquals(totalTicket, totalTickets);
    }

    @Test
    void test_getTotalTickets_ticketBundleTypeNotTable(){

        //setup
        int numberOfTicket = 10;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setNumberOfTicket(numberOfTicket);
        ticketTypeSettingDto.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);
        ticketTypeSettingDto.setTicketsPerTable(2);
        int totalTicket = ticketTypeSettingDto.getNumberOfTicket();

        //Execution
        int totalTickets = ticketingManageServiceImpl.getTotalTickets(ticketTypeSettingDto);

        //Assertion
        assertEquals(totalTicket, totalTickets);
    }

    @Test
    void test_copyTicketingTypeElement_throwExceptionBUNDLE_TYPE_CAN_NOT_BE_CHANGED(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean recurringEvent = true;
        boolean newTicketingType = true;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setBundleType(TicketBundleType.INDIVIDUAL_TICKET);


        //mock
        when(eventCommonRepoService.isTypePurchased(any())).thenReturn(true);
        when(ticketingOrderManagerService.isTicketingTypeExist(any())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType));

        //Assertion
        assertEquals(BUNDLE_TYPE_CAN_NOT_BE_CHANGED.getDeveloperMessage(), exception.getMessage());
        verify(eventCommonRepoService, times(1)).isTypePurchased(any());
        verify(ticketingOrderManagerService, times(1)).isTicketingTypeExist(any());
    }

    @Test
    void test_copyTicketingTypeElement_throwExceptionNUMBER_OF_TICKETS_CAN_NOT_BE_LESS_THAN_ONE(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean recurringEvent = true;
        boolean newTicketingType = true;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(0);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(any())).thenReturn(true);
        when(ticketingOrderManagerService.isTicketingTypeExist(any())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType));

        //Assertion
        assertEquals(NUMBER_OF_TICKETS_CAN_NOT_BE_LESS_THAN_ONE.getDeveloperMessage(), exception.getMessage());
        verify(eventCommonRepoService, times(1)).isTypePurchased(any());
        verify(ticketingOrderManagerService, times(1)).isTicketingTypeExist(any());
    }

    @Test
    void test_copyTicketingTypeElement_throwExceptionNUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean recurringEvent = true;
        boolean newTicketingType = true;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(2);

        NotAcceptableException.TicketingExceptionMsg ticketingExceptionMsg = NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED;
        ticketingExceptionMsg.setErrorMessage(ticketingExceptionMsg.getErrorMessage().replace("{bundle_type}", ticketingType.getBundleType().toString()));
        ticketingExceptionMsg.setDeveloperMessage(ticketingExceptionMsg.getDeveloperMessage().replace("{bundle_type}", ticketingType.getBundleType().toString()));

        //mock
        when(eventCommonRepoService.isTypePurchased(any())).thenReturn(true);
        when(ticketingOrderManagerService.isTicketingTypeExist(any())).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType));

        //Assertion
        assertEquals(ticketingExceptionMsg.getDeveloperMessage(), exception.getMessage());
        verify(eventCommonRepoService, times(1)).isTypePurchased(any());
        verify(ticketingOrderManagerService, times(1)).isTicketingTypeExist(any());
    }

    @Test
    void test_copyTicketingTypeElement_withBundleTypeNotINDIVIDUAL_TICKET(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean newTicketingType = true;
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,"TicketTypeName", null, 60, true, 2L, 1L, TicketBundleType.TABLE, 1, 0L, null, 10, 1L);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setHidden(true);
        ticketTypeSettingDto.setTicketTypeDescription("TicketTypeDescription");
        ticketTypeSettingDto.setPrice(100d);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        ticketing.setRecurringEvent(true);

        //mock
        when(eventCommonRepoService.isTypePurchased(any())).thenReturn(false);
        when(ticketingOrderManagerService.isTicketingTypeExist(any())).thenReturn(false);
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);

        //Execution
        ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType);

        //Assertion
        verify(eventCommonRepoService, times(1)).isTypePurchased(any());
        verify(ticketingOrderManagerService, times(1)).isTicketingTypeExist(any());
    }

    @Test
    void test_copyTicketingTypeElement(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean recurringEvent = true;
        boolean newTicketingType = true;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(0L);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setHidden(true);
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.setTicketTypeDescription("TicketTypeDescription");
        ticketTypeSettingDto.setPassfeetobuyer(true);
        ticketTypeSettingDto.setPrice(100d);
        ticketTypeSettingDto.setName("TicketTypeName");
        ticketTypeSettingDto.setStartDate(startDate);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setBundleType(TicketBundleType.TABLE);

        //mock
        when(eventCommonRepoService.isTypePurchased(any())).thenReturn(true);
        when(ticketingOrderManagerService.isTicketingTypeExist(any())).thenReturn(true);
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);
        //Execution
        ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType);

        //Assertion
        verify(eventCommonRepoService, times(1)).isTypePurchased(any());
        verify(ticketingOrderManagerService, times(1)).isTicketingTypeExist(any());
    }

    @Test
    void test_copyTicketingTypeElement_withTicketTypeSettingDtoTypeIdZero(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        boolean recurringEvent = true;
        boolean newTicketingType = true;
        ticketTypeSettingDto = setTicketTypeSettingDto(0L,"TicketTypeName",null, 0, true, 2L, 1L, TicketBundleType.TABLE, 01, 0L, null, 10, 1L);
        ticketTypeSettingDto.setEnableTicketDescription(true);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setHidden(true);
        ticketTypeSettingDto.setTicketTypeDescription("TicketTypeDescription");
        ticketTypeSettingDto.setPrice(100d);
        ticketTypeSettingDto.setStartDate(startDate);

        ticketingType = new TicketingType();
        ticketingType.setId(0L);
        ticketingType.setBundleType(TicketBundleType.TABLE);
        ticketingType.setNumberOfTicketPerTable(1);
        when(vatTaxService.findByEventId(any())).thenReturn(vatTax);

        //Execution
        ticketingManageServiceImpl.copyTicketingTypeElement(ticketTypeSettingDto, ticketingType, timezone, ticketing,event,newTicketingType);

        //Assertion
        assertEquals(ticketingType.getBundleType(), ticketTypeSettingDto.getBundleType());
        assertEquals(ticketingType.getNumberOfTicketPerTable(), ticketTypeSettingDto.getTicketsPerTable().intValue());
        assertEquals(ticketingType.getStartDate(), TimeZoneUtil.getDateInUTC(ticketTypeSettingDto.getStartDate(), timezone, null));
    }

    public static Object[] getRecurringEventSalesEndStatus(){
        return new Object[]{
                new Object[]{TicketingType.RecurringEventSalesEndStatus.END} ,
                new Object[]{TicketingType.RecurringEventSalesEndStatus.START} ,
                new Object[]{null} ,
        };
    }
    @ParameterizedTest
    @MethodSource("getRecurringEventSalesEndStatus")
    void test_setTicketTypeEndDate_withRecurringEventTrueAndRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus recurringEventSalesEndStatus){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(recurringEventSalesEndStatus);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);
        ticketTypeSettingDto.setEndDate(endDate);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setRecurringEventId(1L);

		RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventEndDate(new Date(endDate));
        Map<CategoryDto, Integer> decrementSeatsMap= new HashMap<>();
        ticketing.setRecurringEvent(true);

        //mock
        when(recurringEventsScheduleService.getRecurringEvents(any())).thenReturn(recurringEvents);
        when(ticketingService.getStringDateWithAddedRecurringEventEndTime(any(), anyInt())).thenReturn(endDate);

        //Execution
        ticketingManageServiceImpl.setTicketTypeEndDate(ticketing,  ticketTypeSettingDto, ticketingType, timezone);

        //Assertion
        verify(recurringEventsScheduleService, times(1)).getRecurringEvents(any());
        verify(ticketingService, times(1)).getStringDateWithAddedRecurringEventEndTime(any(), anyInt());

        assertEquals(ticketTypeSettingDto.getEndDate(), endDate);
    }

    @Test
    void test_setTicketTypeEndDate_withRecurringEventIdNull(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        Map<CategoryDto, Integer> decrementSeatsMap= new HashMap<>();
        ticketing.setRecurringEvent(true);

        //Execution
        ticketingManageServiceImpl.setTicketTypeEndDate(ticketing, ticketTypeSettingDto, ticketingType, timezone);

        //Assertion
        assertNull(ticketTypeSettingDto.getEndDate());
    }

    @Test
    void test_setTicketTypeEndDate_withRecurringEventFalseAndTotalTicketsLessThantotalTicketsFromDb(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        ticketingType = setTicketingType(1L, 1L, TicketType.PAID, 1L, TicketBundleType.TABLE, 2, 10);

        ticketTypeSettingDto = new TicketTypeSettingDto(ticketingType);
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.createCategoryDto();

        Map<CategoryDto, Integer> decrementSeatsMap= new HashMap<>();
        ticketing.setRecurringEvent(false);
        ticketing.setChartKey("ChartKey");
        ticketing.setEventEndDate(endDate1);
        ticketing.setEventStartDate(startDate1);

        String endDate = TimeZoneUtil.getEndDateString(ticketing.getEventEndDate(), timezone);
        String startDate = TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), timezone);

        //Execution
        ticketingManageServiceImpl.setTicketTypeEndDate(ticketing, ticketTypeSettingDto, ticketingType, timezone);

        //Assertion
        assertEquals(ticketTypeSettingDto.getEndDate(), endDate);
        assertEquals(ticketTypeSettingDto.getStartDate(), startDate);
    }

    @Test
    void test_setTicketTypeEndDate_withRecurringEventFalseAndEndDateAndStartDateAndTotalTicketsLessThantotalTicketsFromDb(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        ticketingType = setTicketingType(1L, 1L, TicketType.PAID, 1L, TicketBundleType.TABLE, 2, 10);

        ticketTypeSettingDto = new TicketTypeSettingDto(ticketingType);
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(1);
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.createCategoryDto();
        ticketTypeSettingDto.setEndDate(endDate);
        ticketTypeSettingDto.setStartDate(startDate);

        Map<CategoryDto, Integer> decrementSeatsMap= new HashMap<>();
        ticketing.setRecurringEvent(false);
        ticketing.setChartKey("ChartKey");
        ticketing.setEventEndDate(endDate1);
        ticketing.setEventStartDate(startDate1);

        int totalTickets = ticketTypeSettingDto.getNumberOfTicket() * ticketTypeSettingDto.getTicketsPerTable();

        //Execution
        ticketingManageServiceImpl.setTicketTypeEndDate(ticketing, ticketTypeSettingDto, ticketingType, timezone);

        //Assertion
    }

    @Test
    void test_setTicketTypeEndDate_withRecurringEventFalseAndTotalTicketsGreaterThantotalTicketsFromDb(){

        //setup
        String timezone = "Universal Coordinated Time (UTC)";
        ticketingType = setTicketingType(1L, 1L, TicketType.PAID, 1L, TicketBundleType.TABLE, 1, 10);

        ticketTypeSettingDto = new TicketTypeSettingDto(ticketingType);
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(60);
        ticketTypeSettingDto.setBundleType(TicketBundleType.TABLE);
        ticketTypeSettingDto.setTicketsPerTable(2);
        ticketTypeSettingDto.setNumberOfTicket(10);
        ticketTypeSettingDto.createCategoryDto();

        Map<CategoryDto, Integer> decrementSeatsMap= new HashMap<>();
        ticketing.setRecurringEvent(false);
        ticketing.setChartKey("ChartKey");
        ticketing.setEventEndDate(endDate1);
        ticketing.setEventStartDate(startDate1);

        String endDate = TimeZoneUtil.getEndDateString(ticketing.getEventEndDate(), timezone);
        String startDate = TimeZoneUtil.getEndDateString(ticketing.getEventStartDate(), timezone);

        //Execution
        ticketingManageServiceImpl.setTicketTypeEndDate(ticketing, ticketTypeSettingDto, ticketingType, timezone);

        //Assertion
        assertEquals(ticketTypeSettingDto.getEndDate(), endDate);
        assertEquals(ticketTypeSettingDto.getStartDate(), startDate);
    }

   /* @Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_withRecurringEventIdGreaterThanZero(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(1L);

        ticketingType = new TicketingType();

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1);
    }
*/
    /*@Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_withRecurringEventIdNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();

        ticketingType = new TicketingType();
        ticketingType.setCreatedFrom(1L);

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertNotEquals(ticketingType.getCreatedFrom().longValue(), -1);
    }*/

    /*@Test
    void test_setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent_withRecurringEventIdZero(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(0L);

        ticketingType = new TicketingType();
        ticketingType.setCreatedFrom(1L);

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinuseOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertNotEquals(ticketingType.getCreatedFrom().longValue(), -1);
    }*/

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_withTicketingCouponAndAccessCode(){

        //setup
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(1L);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId("1");
        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();
        ticketingCouponList.add(ticketingCoupon);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId("1");
        List<TicketingAccessCode> accessCodeList = new ArrayList<>();
        accessCodeList.add(ticketingAccessCode);

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.findByEvent(any())).thenReturn(accessCodeList);

        //Execution
        ticketingManageServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketingTypeIds);

        //Assertion
        verify(ticketingCouponService, times(1)).getAllByEventId(anyLong());
        verify(ticketingAccessCodeService, times(1)).findByEvent(any());

        ArgumentCaptor<TicketingCoupon> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(TicketingCoupon.class);
        verify(ticketingCouponRepository, times(1)).save(ticketingCouponArgumentCaptor.capture());

        TicketingCoupon ticketingCouponData = ticketingCouponArgumentCaptor.getValue();
        assertEquals(ticketingCouponData.getEventTicketTypeId(), ticketingCoupon.getEventTicketTypeId());

        ArgumentCaptor<TicketingAccessCode> ticketingAccessCodeArgumentCaptor = ArgumentCaptor.forClass(TicketingAccessCode.class);
        verify(ticketingAccessCodeService, times(1)).save(ticketingAccessCodeArgumentCaptor.capture());

        TicketingAccessCode ticketingAccessCodeData = ticketingAccessCodeArgumentCaptor.getValue();
        assertEquals(ticketingAccessCodeData.getEventTicketTypeId(), ticketingAccessCode.getEventTicketTypeId());

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService, times(1)).save(ticketingTypeArgumentCaptor.capture());

        TicketingType ticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertTrue(ticketingTypeData.isHidden());
    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_withTicketingCouponListEmptyAndAccessCodeListEmpty(){

        //setup
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(1L);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();

        List<TicketingAccessCode> accessCodeList = new ArrayList<>();

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.findByEvent(any())).thenReturn(accessCodeList);

        //Execution
        ticketingManageServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketingTypeIds);

        //Assertion
        verify(ticketingCouponService, times(1)).getAllByEventId(anyLong());
        verify(ticketingAccessCodeService, times(1)).findByEvent(any());
    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_withTicketingCouponListNullAndAccessCodeListEmptyNull(){

        //setup
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(1L);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(null);
        when(ticketingAccessCodeService.findByEvent(any())).thenReturn(null);

        //Execution
        ticketingManageServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketingTypeIds);

        //Assertion
        verify(ticketingCouponService, times(1)).getAllByEventId(anyLong());
        verify(ticketingAccessCodeService, times(1)).findByEvent(any());
    }

    @Test
    void test_isTicketTypeApplicableForDiscountCodeAndAccessCode_withTicketingCouponAndAccessCodeAndlistOfAccessCodeNotcontainsAllticketingTypeIds(){

        //setup
        List<Long> ticketingTypeIds = new ArrayList<>();
        ticketingTypeIds.add(1L);

        ticketingType = new TicketingType();
        ticketingType.setId(1L);

        ticketingCoupon = new TicketingCoupon();
        ticketingCoupon.setEventTicketTypeId("2");
        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();
        ticketingCouponList.add(ticketingCoupon);

        ticketingAccessCode = new TicketingAccessCode();
        ticketingAccessCode.setEventTicketTypeId("2");
        List<TicketingAccessCode> accessCodeList = new ArrayList<>();
        accessCodeList.add(ticketingAccessCode);

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.findByEvent(any())).thenReturn(accessCodeList);

        //Execution
        ticketingManageServiceImpl.isTicketTypeApplicableForDiscountCodeAndAccessCode(event, ticketingType, ticketingTypeIds);

        //Assertion
        verify(ticketingCouponService, times(1)).getAllByEventId(anyLong());
        verify(ticketingAccessCodeService, times(1)).findByEvent(any());
    }

    @Test
    void test_isPaidTicketExistsNow_withTicketTypeDONATIONAndPriceZero(){

        //setup
        boolean isPaidTicketExistsNow =false;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setPrice(0d);

        //Execution
        boolean paidTicketExistNow = ticketingManageServiceImpl.isPaidTicketExistsNow(isPaidTicketExistsNow, ticketTypeSettingDto);

        //Assertion
        assertTrue(paidTicketExistNow);
    }

    @Test
    void test_isPaidTicketExistsNow_withTicketTypeDONATIONAndPriceGreaterThanZero(){

        //setup
        boolean isPaidTicketExistsNow =false;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTicketType(TicketType.DONATION);
        ticketTypeSettingDto.setPrice(1d);

        //Execution
        boolean paidTicketExistNow = ticketingManageServiceImpl.isPaidTicketExistsNow(isPaidTicketExistsNow, ticketTypeSettingDto);

        //Assertion
        assertFalse(paidTicketExistNow);
    }

    @Test
    void test_isPaidTicketExistsNow_withTicketTypePAIDAndPriceLessThanZero(){

        //setup
        boolean isPaidTicketExistsNow =false;
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setPrice(0d);

        //Execution
        boolean paidTicketExistNow = ticketingManageServiceImpl.isPaidTicketExistsNow(isPaidTicketExistsNow, ticketTypeSettingDto);

        //Assertion
        assertFalse(paidTicketExistNow);
    }

   /* @Test
    void createCategory(){

        //setup
        String category = PAID;
        Long eventId = 1L;
        boolean isTicketTypeName =true;

        seatingCategories = new SeatingCategories();
        seatingCategories.setId(1L);

        //mock
        when( seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(seatingCategories);

        //Execution
        SeatingCategories categoriesData = ticketingManageServiceImpl.createCategory(category, eventId, isTicketTypeName);

        //Assertion
        assertEquals(categoriesData.getId(), seatingCategories.getId());
    }*/

   // @Test
    void test_createOrUpdateCategory_throwExceptionSEATING_CATEGORY_NOT_FOUND(){

        //setup
        eventCategoryDto =setEventCategoryDto(null);
        seatingCategories = new SeatingCategories();

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket";
        boolean isTicketTypeName = true;

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(null);

        //Execution

        //Assertion

    }

   // @Test
    void test_createOrUpdateCategory_withSeatingCategoriesTicketTypeNameTrue(){

        //setup
        eventCategoryDto =setEventCategoryDto(null);
        seatingCategories = setSeatingCategories( PAID, true);

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket1";
        boolean isTicketTypeName = true;
        Long countCategoryInTicketType = 1L;

        SeatingCategories seatingCategories1 = new SeatingCategories();
        seatingCategories1.setId(2L);

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(seatingCategories);
        when(ticketingTypeRepository.countCategoryInTicketType(any(), anyLong())).thenReturn(countCategoryInTicketType);
        when(seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(seatingCategories1);

        //Execution


        //Assertion
        verify(seatingCategoryRepository, times(1)).findOneById(anyLong());
        verify(ticketingTypeRepository, times(1)).countCategoryInTicketType(any(), anyLong());
        verify(seatingCategoryRepository, times(1)).findByCategoryNameAndEventId(anyString(), anyLong());

    }

   // @Test
    void test_createOrUpdateCategory_withSeatingCategoriesTicketTypeNameFalse(){

        //setup
        eventCategoryDto =setEventCategoryDto("Music");
        seatingCategories = setSeatingCategories( PAID, false);

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket1";
        boolean isTicketTypeName = true;
        Long countCategoryInTicketType = 1L;

        SeatingCategories seatingCategories1 = new SeatingCategories();
        seatingCategories1.setId(1L);

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(seatingCategories);
        when(ticketingTypeRepository.countCategoryInTicketType(any(), anyLong())).thenReturn(countCategoryInTicketType);
        when(seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(seatingCategories1);

        //Execution

        //Assertion
        verify(seatingCategoryRepository, times(1)).findOneById(anyLong());
        verify(ticketingTypeRepository, times(1)).countCategoryInTicketType(any(), anyLong());
        verify(seatingCategoryRepository, times(1)).findByCategoryNameAndEventId(anyString(), anyLong());

    }

   // @Test
    void test_createOrUpdateCategory_withSeatingCategoriesTicketTypeNameFalseAndseatingCategories1NullAndcountCategoryInTicketTypeGreatrThanOne(){

        //setup
        eventCategoryDto =setEventCategoryDto("Music");
        seatingCategories = setSeatingCategories( PAID, false);

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket1";
        boolean isTicketTypeName = true;
        Long countCategoryInTicketType = 2L;

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(seatingCategories);
        when(ticketingTypeRepository.countCategoryInTicketType(any(), anyLong())).thenReturn(countCategoryInTicketType);
        when(seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(null);
        Mockito.doReturn(seatingCategories).when(ticketingManageServiceImpl).createCategoryOrGetCategoryByName(anyString(), anyLong(), anyBoolean());

        //Execution

        //Assertion
        verify(seatingCategoryRepository, times(1)).findOneById(anyLong());
        verify(ticketingTypeRepository, times(1)).countCategoryInTicketType(any(), anyLong());
        verify(seatingCategoryRepository, times(1)).findByCategoryNameAndEventId(anyString(), anyLong());

    }

 //   @Test
    void test_createOrUpdateCategory_withSeatingCategoriesTicketTypeNameFalseAndseatingCategories1NullAndcountCategoryInTicketTypeZero(){

        //setup
        eventCategoryDto =setEventCategoryDto("Music");
        seatingCategories = setSeatingCategories( PAID, false);

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket1";
        boolean isTicketTypeName = true;
        Long countCategoryInTicketType = 0L;

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(seatingCategories);
        when(ticketingTypeRepository.countCategoryInTicketType(any(), anyLong())).thenReturn(countCategoryInTicketType);
        when(seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(null);
        when(seatingCategoryRepository.save(any())).thenReturn(seatingCategories);

        //Execution

        //Assertion
        verify(seatingCategoryRepository, times(1)).findOneById(anyLong());
        verify(ticketingTypeRepository, times(1)).countCategoryInTicketType(any(), anyLong());
        verify(seatingCategoryRepository, times(1)).findByCategoryNameAndEventId(anyString(), anyLong());

    }

    //@Test
    void test_createOrUpdateCategory_withNeedToUpdateFalse(){

        //setup
        eventCategoryDto =setEventCategoryDto("Music");
        seatingCategories = setSeatingCategories("Music", false);

        List<EventCategoryDto> eventCategoryDtoList = new ArrayList<>();
        eventCategoryDtoList.add(eventCategoryDto);
        String ticketTypeName = "PaidTicket1";
        boolean isTicketTypeName = true;

        //mock
        when(seatingCategoryRepository.findOneById(anyLong())).thenReturn(seatingCategories);
        when(seatingCategoryRepository.save(any())).thenReturn(seatingCategories);

        //Assertion
        verify(seatingCategoryRepository, times(1)).findOneById(anyLong());

    }

    //@Test
    void test_handleHolderRequiredAttributesForNewTicket_withHolderEventTicketTypeIdAndBuyerEventTicketTypeId(){

        //setup
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketHolderRequiredAttributes = setTicketHolderRequiredAttributes(true, true, STRING_FIRST_NAME, "1", "1");

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(), anyLong())).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingManageServiceImpl.updateHolderRequiredAttributesForNewTicket(event, ticketingType);

        //Assertion
        verify(ticketHolderRequiredAttributesService, times(1)).getTicketHolderRequiredAttributes(any(), anyLong());
        Class<ArrayList<TicketHolderRequiredAttributes>> listClass = (Class<ArrayList<TicketHolderRequiredAttributes>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketHolderRequiredAttributes>> argument = ArgumentCaptor.forClass(listClass);
        verify(ticketHolderRequiredAttributesService).saveAll(argument.capture());

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesData = argument.getValue();
        assertEquals(ticketHolderRequiredAttributesData.get(0).getBuyerRequiredTicketTypeId(), ticketHolderRequiredAttributes.getHolderRequiredTicketTypeId());
    }

    //@Test
    void test_handleHolderRequiredAttributesForNewTicket_withHolderEventTicketTypeIdNullAndBuyerEventTicketTypeIdNUll(){

        //setup
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketHolderRequiredAttributes = setTicketHolderRequiredAttributes(true, true, STRING_FIRST_NAME, null, null);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(), anyLong())).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingManageServiceImpl.updateHolderRequiredAttributesForNewTicket(event, ticketingType);

        //Assertion
        verify(ticketHolderRequiredAttributesService, times(1)).getTicketHolderRequiredAttributes(any(), anyLong());
        Class<ArrayList<TicketHolderRequiredAttributes>> listClass = (Class<ArrayList<TicketHolderRequiredAttributes>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketHolderRequiredAttributes>> argument = ArgumentCaptor.forClass(listClass);
        verify(ticketHolderRequiredAttributesService).saveAll(argument.capture());

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesData = argument.getValue();
        assertEquals(ticketHolderRequiredAttributesData.get(0).getBuyerRequiredTicketTypeId(), String.valueOf(ticketingType.getId()));
    }

    public static Object[] getTicketHolderRequiredAttributeName(){
        return new Object[]{
                new Object[]{STRING_CELL_SPACE_PHONE} ,
                new Object[]{STRING_EMAIL_SPACE} ,
                new Object[]{STRING_LAST_SPACE_NAME} ,
                new Object[]{STRING_FIRST_SPACE_NAME} ,
        };
    }
    // @ParameterizedTest
    //	@MethodSource("getTicketHolderRequiredAttributeName")
    void test_handleHolderRequiredAttributesForNewTicket_withTicketHolderRequiredAttributeNameSTRING_CELL_SPACE_PHONE(String attributesName){

        //setup
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketHolderRequiredAttributes = setTicketHolderRequiredAttributes(true, true, attributesName, null, null);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(), anyLong())).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingManageServiceImpl.updateHolderRequiredAttributesForNewTicket(event, ticketingType);

        //Assertion
        verify(ticketHolderRequiredAttributesService, times(1)).getTicketHolderRequiredAttributes(any(), anyLong());
    }

   // @Test
    void test_handleHolderRequiredAttributesForNewTicket_withEnabledForTicketPurchaserFalseAndEnabledForTicketHolderFalse(){

        //setup
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketHolderRequiredAttributes = setTicketHolderRequiredAttributes(false, false, STRING_FIRST_NAME, null, null);
        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes);

        //mock
        when(ticketHolderRequiredAttributesService.getTicketHolderRequiredAttributes(any(), anyLong())).thenReturn(ticketHolderRequiredAttributesList);

        //Execution
        ticketingManageServiceImpl.updateHolderRequiredAttributesForNewTicket(event, ticketingType);

        //Assertion
        verify(ticketHolderRequiredAttributesService, times(1)).getTicketHolderRequiredAttributes(any(), anyLong());
    }

   /* @Test
    void test_handleRecurringTask_withTypeIdGreaterThanZeroAndCreatedFromNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        List<TicketingType> newDefaultTicketTypes = new ArrayList<>();
        newDefaultTicketTypes.add(ticketingType);
        ticketing.setRecurringEvent(true);
        recurringEvents = new RecurringEvents();
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        //mock
        when(ticketingTypeService.getTicketTypeByCreateFrom(anyLong())).thenReturn(newDefaultTicketTypes);
        Mockito.doReturn(ticketingType).when(ticketingManageServiceImpl).copyTicket(any(), any(), anyMap(), any(), any());
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(recurringEventsList);
        doNothing().when(recurringEventsMainScheduleService).createTicketType(any(), anyList(), anyString());

        //Execution
        ticketingManageServiceImpl.handleRecurringTask(ticketTypeSettingDto, event, ticketing, decrementSeatsMap, newDefaultTicketTypes);

        //Assertion
        verify(ticketingTypeService, times(1)).getTicketTypeByCreateFrom(anyLong());
        verify(recurringEventsMainScheduleService, times(1)).createTicketType(any(), anyList(), anyString());
        verify(recurringEventsScheduleService, times(1)).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any());

        Class<ArrayList<TicketingType>> listClass = (Class<ArrayList<TicketingType>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingType>> argument = ArgumentCaptor.forClass(listClass);
        verify(ticketingTypeService).saveAll(argument.capture());

        List<TicketingType> ticketingTypeListDAta = argument.getValue();
        assertEquals(ticketingTypeListDAta.get(0).getId(), ticketingType.getId());
    }*/

    /*@Test
    void test_handleRecurringTask_withTypeIdGreaterThanZeroAndCreatedFromNullAndTicketingTypeNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        List<TicketingType> newDefaultTicketTypes = new ArrayList<>();
        newDefaultTicketTypes.add(ticketingType);
        ticketing.setRecurringEvent(true);
        recurringEvents = new RecurringEvents();
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        //mock
        when(ticketingTypeService.getTicketTypeByCreateFrom(anyLong())).thenReturn(newDefaultTicketTypes);
        Mockito.doReturn(null).when(ticketingManageServiceImpl).copyTicket(any(), any(), anyMap(), any(), any());
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(recurringEventsList);
        doNothing().when(recurringEventsMainScheduleService).createTicketType(any(), anyList(), anyString());

        //Execution
        ticketingManageServiceImpl.handleRecurringTask(ticketTypeSettingDto, event, ticketing, decrementSeatsMap, newDefaultTicketTypes);

        //Assertion
        verify(ticketingTypeService, times(1)).getTicketTypeByCreateFrom(anyLong());
        verify(recurringEventsMainScheduleService, times(1)).createTicketType(any(), anyList(), anyString());
        verify(recurringEventsScheduleService, times(1)).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any());
    }*/

    /*@Test
    void test_handleRecurringTask_withTypeIdZeroAndCreatedFromNotNullAndTicketingTypeNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(0L);
        ticketTypeSettingDto.setCreatedFrom(1L);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        List<TicketingType> newDefaultTicketTypes = new ArrayList<>();
        newDefaultTicketTypes.add(ticketingType);
        ticketing.setRecurringEvent(true);
        recurringEvents = new RecurringEvents();
        List<RecurringEvents> recurringEventsList = new ArrayList<>();

        //mock
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(recurringEventsList);

        //Execution
        ticketingManageServiceImpl.handleRecurringTask(ticketTypeSettingDto, event, ticketing, decrementSeatsMap, newDefaultTicketTypes);

        //Assertion
        verify(recurringEventsScheduleService, times(1)).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any());
    }*/

   /* @Test
    void test_handleRecurringTask_withTypeIdGreaterThanZeroAndCreatedFromNotNullAndTicketingTypeNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(1L);
        ticketTypeSettingDto.setCreatedFrom(1L);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        List<TicketingType> newDefaultTicketTypes = new ArrayList<>();
        newDefaultTicketTypes.add(ticketingType);
        ticketing.setRecurringEvent(true);
        recurringEvents = new RecurringEvents();
        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        //mock
        when(recurringEventsScheduleService.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any())).thenReturn(recurringEventsList);
        doNothing().when(recurringEventsMainScheduleService).createTicketType(any(), anyList(), anyString());

        //Execution
        ticketingManageServiceImpl.handleRecurringTask(ticketTypeSettingDto, event, ticketing, decrementSeatsMap, newDefaultTicketTypes);

        //Assertion
        verify(recurringEventsMainScheduleService, times(1)).createTicketType(any(), anyList(), anyString());
        verify(recurringEventsScheduleService, times(1)).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(any());
    }*/

    @Test
    void test_copyTicket_throwExceptionCAN_NOT_DECRESS_NUMBER_OF_TICKETS(){

        //setup
        long numberOfTicketsSold = 15L;
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,null, null, 0, false, 0L, 0L, TicketBundleType.INDIVIDUAL_TICKET, 0, 1L, TicketType.PAID, 10, 0L);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();

        //mock
        Mockito.doNothing().when(ticketingManageServiceImpl).setTicketTypeEndDate(any(), any(), any(), anyString());
        when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketsSold);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () ->  ticketingManageServiceImpl.copyTicket(event, ticketing, ticketTypeSettingDto, ticketingType));

        //Assertion
        assertEquals(CAN_NOT_DECRESS_NUMBER_OF_TICKETS.getDeveloperMessage(), exception.getMessage());
        verify(ticketingStatisticsService, times(1)).soldTicketCount(any());

    }

    @Test
    void test_copyTicket_withBundleTypeINDIVIDUAL_TICKETAndTicketTypeDONATION(){

        //setup
        long numberOfTicketsSold = 15L;
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,null, null, 0, false, 0L, 0L, TicketBundleType.TABLE, 05, 1L, TicketType.DONATION, 1, 0L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setRecurringEventId(1L);
        ticketingType.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        long numberOfTicket = ticketTypeSettingDto.getNumberOfTicket()* ticketTypeSettingDto.getTicketsPerTable();

        //mock
      //  Mockito.doNothing().when(ticketingManageServiceImpl).setTicketTypeEndDate(any(), anyMap(), any(), any(), anyString());
        when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketsSold);
        Mockito.doNothing().when(ticketingManageServiceImpl).copyTicketingTypeElement(any(), any(), anyString(), any(),any(),anyBoolean());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(), any(), anyBoolean(), anyBoolean());
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingType);

        //Execution
        TicketingType copiedTicketData = ticketingManageServiceImpl.copyTicket(event, ticketing,  ticketTypeSettingDto, ticketingType);

        //Assertion
        verify(ticketingStatisticsService, times(1)).soldTicketCount(any());
        verify(ticketingTypeTicketService, times(1)).setPositionForTicketingType(any());

        assertEquals(copiedTicketData.getTicketType(), ticketTypeSettingDto.getTicketType());
        assertEquals(copiedTicketData.getTicketing().getId(), ticketing.getId());
        assertEquals(copiedTicketData.getRecurringEventId(), ticketingType.getRecurringEventId());
    }

    @Test
    void test_copyTicket_withBundleTypeINDIVIDUAL_TICKETAndTicketTypePAIDAndRecurringEventIdGreaterThanZero(){

        //setup
        long numberOfTicketsSold = 15L;
        ticketTypeSettingDto = setTicketTypeSettingDto(1L,null, null, 0, false, 0L, 0L, TicketBundleType.TABLE, 05, 1L, TicketType.PAID, 10, 1L);
        ticketTypeSettingDto.setTicketTypeFormat(TicketTypeFormat.VIRTUAL);
        ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setRecurringEventId(1L);
        ticketingType.setTicketType(TicketType.PAID);
        ticketTypeSettingDto.setTracksSessionsLimitAllowed(false);
        Map<CategoryDto, Integer> decrementSeatsMap = new HashMap<>();
        long numberOfTicket = ticketTypeSettingDto.getNumberOfTicket()* ticketTypeSettingDto.getTicketsPerTable();

        //mock
        Mockito.doNothing().when(ticketingManageServiceImpl).setTicketTypeEndDate(any(),  any(), any(), anyString());
        when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketsSold);
        Mockito.doNothing().when(ticketingManageServiceImpl).copyTicketingTypeElement(any(), any(), anyString(), any(),any(),anyBoolean());
        doNothing().when(transactionFeeConditionalLogicService).applyFeeInTicketType(any(), any(), anyBoolean(), anyBoolean());
        when(ticketingTypeTicketService.setPositionForTicketingType(any())).thenReturn(ticketingType);

        //Execution
        TicketingType copiedTicketData = ticketingManageServiceImpl.copyTicket(event, ticketing,  ticketTypeSettingDto, ticketingType);

        //Assertion
        verify(ticketingStatisticsService, times(1)).soldTicketCount(any());
        verify(ticketingTypeTicketService, times(1)).setPositionForTicketingType(any());

        assertEquals(copiedTicketData.getTicketType(), ticketTypeSettingDto.getTicketType());
        assertEquals(copiedTicketData.getTicketing().getId(), ticketing.getId());
        assertEquals(copiedTicketData.getRecurringEventId(), ticketingType.getRecurringEventId());
    }

    @Test
    void test_createCategoryOrGetCategoryByName_withSeatingCategories(){

        //setup
        String category = GENERAL_ADMISSION;
        Long eventId = id;
        boolean isTicketTypeName = true;

        seatingCategories = setSeatingCategories(category, isTicketTypeName);

        //mock
        when(seatingCategoryRepository.findByCategoryNameAndEventId(anyString(), anyLong())).thenReturn(seatingCategories);

        //Execution
        SeatingCategories seatingCategorieData = ticketingManageServiceImpl.createCategoryOrGetCategoryByName(category, eventId, isTicketTypeName);

        // Assertion
        assertEquals(seatingCategorieData.getId(), id);
        assertEquals(seatingCategorieData.getName(), category);
        assertEquals(seatingCategorieData.isHavingVariations(), isTicketTypeName);
    }

    @Test
    void test_setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(id);
        ticketingType = new TicketingType();

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void test_setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent_withRecurringEventIdLessThanZero(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setRecurringEventId(0L);
        ticketingType = new TicketingType();
        ticketingType.setCreatedFrom(1L);

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertNotEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

    @Test
    void test_setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent_withRecurringEventIdNull(){

        //setup
        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketingType = new TicketingType();
        ticketingType.setCreatedFrom(1L);

        //Execution
        ticketingManageServiceImpl.setCreatedFromMinusOneWhenItsNewTicketTypeForSpecificRecurringEvent(ticketTypeSettingDto, ticketingType);

        //Assertion
        assertNotEquals(ticketingType.getCreatedFrom().longValue(), -1L);
    }

//    @Test
//    void test_isRequireToCallUpdateChartTableModes_fromDBGetNullValue(){
//
//        boolean actualResult = ticketingManageServiceImpl.isRequireToCallUpdateChartTableModes(null, null, null);
//        assertFalse(actualResult);
//    }

//    @Test
//    void test_isRequireToCallUpdateChartTableModes_fromDBGetNotNullValueAndBundleTypeSame(){
//
//
//        ticketingTypeTemp.setBundleType(TicketBundleType.TABLE);
//        ticketingTypeFromDb.setBundleType(TicketBundleType.TABLE);
//        boolean actualResult = ticketingManageServiceImpl.isRequireToCallUpdateChartTableModes(ticketingTypeTemp, ticketing, ticketingTypeFromDb);
//        assertFalse(actualResult);
//    }
//
//    @Test
//    void test_isRequireToCallUpdateChartTableModes_fromDBGetNotNullValueAndBundleTypeNOTSame(){
//
//        ticketingTypeTemp.setBundleType(TicketBundleType.TABLE);
//        ticketing.setChartKey(chartKey);
//        ticketingTypeFromDb.setBundleType(TicketBundleType.SPONSOR);
//        boolean actualResult = ticketingManageServiceImpl.isRequireToCallUpdateChartTableModes(ticketingTypeTemp, ticketing, ticketingTypeFromDb);
//        assertTrue(actualResult);
//    }

  //  @Test
    void test_isRequireToCallUpdateChartTableModes_fromDBGetNotNullValueAndBundleTypeNOTSameChartKeyNull(){

        ticketingTypeTemp.setBundleType(TicketBundleType.TABLE);
        ticketing.setChartKey(null);
        ticketingTypeFromDb.setBundleType(TicketBundleType.SPONSOR);
 //       boolean actualResult = ticketingManageServiceImpl.isRequireToCallUpdateChartTableModes(ticketingTypeTemp, ticketing, ticketingTypeFromDb);
    //    assertFalse(actualResult);
    }

    private EventTicketingDto setEventTicketingDto(String equivalentTimeZone, String eventVenueType){

        eventTicketingDto = new EventTicketingDto();
        eventTicketingDto.setTimezoneId(TimeZoneUtil.getNameByEquivalentTimeZone(equivalentTimeZone));
        eventTicketingDto.setEquivalentTimezone(equivalentTimeZone);
        eventTicketingDto.setEventStartDate(startDate);
        eventTicketingDto.setEventEndDate(endDate);
        eventTicketingDto.setEventVenueType(eventVenueType);
        eventTicketingDto.setEventAddress("New Delhi");
        eventTicketingDto.setLongitude(" -71.07818750000001");
        eventTicketingDto.setLongitude("42.3493136");
        return eventTicketingDto;
    }

    private TicketTypeSettingDto setTicketTypeSettingDto(long typeId, String name, TicketingType.RecurringEventSalesEndStatus recurringEventSalesEndStatus, int recurringEventSalesEndTime, boolean passfeetobuyer, long maxTickerPerBuyer, long minTickerPerBuyer, TicketBundleType bundleType, int ticketsPerTable, long createdFrom, TicketType ticketType, int numberOfTicket, long recurringEventId){

        ticketTypeSettingDto = new TicketTypeSettingDto();
        ticketTypeSettingDto.setTypeId(typeId);
        ticketTypeSettingDto.setName(name);
        ticketTypeSettingDto.setRecurringEventSalesEndStatus(recurringEventSalesEndStatus);
        ticketTypeSettingDto.setRecurringEventSalesEndTime(recurringEventSalesEndTime);
        ticketTypeSettingDto.setPassfeetobuyer(passfeetobuyer);
        ticketTypeSettingDto.setMaxTickerPerBuyer(maxTickerPerBuyer);
        ticketTypeSettingDto.setMinTickerPerBuyer(minTickerPerBuyer);
        ticketTypeSettingDto.setBundleType(bundleType);
        ticketTypeSettingDto.setTicketsPerTable(ticketsPerTable);
        ticketTypeSettingDto.setCreatedFrom(createdFrom);
        ticketTypeSettingDto.setTicketType(ticketType);
        ticketTypeSettingDto.setNumberOfTicket(numberOfTicket);
        ticketTypeSettingDto.setRecurringEventId(recurringEventId);
        ticketTypeSettingDto.setPrice(50d);
        return ticketTypeSettingDto;
    }

    private TicketHolderRequiredAttributes setTicketHolderRequiredAttributes(boolean enabledForTicketPurchaser, boolean enabledForTicketHolder, String name, String holderEventTicketTypeId, String buyerEventTicketTypeId){
        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setEnabledForTicketPurchaser(enabledForTicketPurchaser);
        ticketHolderRequiredAttributes.setEnabledForTicketHolder(enabledForTicketHolder);
        ticketHolderRequiredAttributes.setName(name);
        ticketHolderRequiredAttributes.setHolderRequiredTicketTypeId(holderEventTicketTypeId);
        ticketHolderRequiredAttributes.setBuyerRequiredTicketTypeId(buyerEventTicketTypeId);
        return ticketHolderRequiredAttributes;
    }

    private SeatingCategories setSeatingCategories(String categoryname, boolean ticketTypeName){

        seatingCategories = new SeatingCategories();
        seatingCategories.setId(id);
        seatingCategories.setName(categoryname);
        seatingCategories.setHavingVariations(ticketTypeName);
        return seatingCategories;
    }

    private EventCategoryDto setEventCategoryDto(String categoryName){

        eventCategoryDto = new EventCategoryDto();
        eventCategoryDto.setId(id);
        eventCategoryDto.setName(categoryName);
        return eventCategoryDto;
    }

    private TicketingType setTicketingType(long maxTicketsPerBuyer, long
            minTicketsPerBuyer, TicketType ticketType, long recurringEventId, TicketBundleType bundleType, int numberOfTicketPerTable, int numberOfTicket){

        ticketingType = new TicketingType();
        ticketingType.setId(id);
        ticketingType.setTicketTypeName(GENERAL_ADMISSION);
        ticketingType.setPrice(100d);
        ticketingType.setTicketTypeDescription("ticketTypeDescription");
        ticketingType.setEnableTicketDescription(true);
        ticketingType.setPassfeetobuyer(true);
        ticketingType.setHidden(true);
        ticketingType.setMaxTicketsPerBuyer(maxTicketsPerBuyer);
        ticketingType.setMinTicketsPerBuyer(minTicketsPerBuyer);
        ticketingType.setTicketType(ticketType);
        ticketingType.setRecurringEventId(recurringEventId);
        ticketingType.setBundleType(bundleType);
        ticketingType.setNumberOfTicketPerTable(numberOfTicketPerTable);
        ticketingType.setNumberofticket(numberOfTicket);
        ticketingType.setRecurringEventSalesEndStatus(TicketingType.RecurringEventSalesEndStatus.START);
        ticketingType.setRecurringEventSalesEndTime(60);
        return ticketingType;
    }


    @Test
    void test_checkEventCapacity_Only_Verify(){

        TicketingTypeSettingsDto ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        Map<Long, BigDecimal> ticketSoldCount = new HashMap<>();

        //mock
        when(eventCommonRepoService.findSoldCountByEvent(event)).thenReturn(BigDecimal.ONE);

        //Execution
        ticketingManageServiceImpl.checkEventCapacity(ticketingTypeSettingsDto, event);
        verify(eventCommonRepoService, times(1)).findSoldCountByEvent(event);
    }

    @Test
    void test_throwExceptionForMoreEventCapacity_LimitEventCapacityVerifyLimitEventCapicity(){

        TicketingTypeSettingsDto ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        double soldCount = 0;

        //Execution
        ticketingManageServiceImpl.throwExceptionForMoreEventCapacity(ticketingTypeSettingsDto, soldCount);
    }

    @Test
    void test_throwExceptionForMoreEventCapacity_soldCountGraterThanEventCapacity(){

        double soldCount = 10;

        NotAcceptableException.TicketingExceptionMsg expectedException = NotAcceptableException.TicketingExceptionMsg.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS;
        expectedException.setErrorMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace("${sold_tickets}", String.valueOf(((int) soldCount))));
        expectedException.setDeveloperMessage(Constants.EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS.replace("${sold_tickets}", String.valueOf((int) soldCount)));


        TicketingTypeSettingsDto ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setLimitEventCapacity(true);
        ticketingTypeSettingsDto.setEventCapacity(new Double(9));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingManageServiceImpl.throwExceptionForMoreEventCapacity(ticketingTypeSettingsDto, soldCount));
        assertEquals(expectedException.getDeveloperMessage(), exception.getMessage());
    }

//    @Test
//    void test_setEventCapacityForRecurringEventIdGraterThanZERO_EventCapacitySet_NullAndNotNull(){
//
//        //Setup
//        TicketingTypeSettingsDto ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
//
//        //Mock
//        when(eventTicketsRepoService.getRecurringHavingTicketSold(recurringEventId)).thenReturn(new BigInteger("0"));
//        when(recurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEventsOpt));
//
//        //Execution
//        ticketingManageServiceImpl.setEventCapacityForRecurring(ticketingTypeSettingsDto, event, recurringEventId, ticketing);
//
//        ArgumentCaptor<RecurringEvents> recurringArgumentCaptor = ArgumentCaptor.forClass(RecurringEvents.class);
////        verify(recurringEventsRepository, times(1)).save(recurringArgumentCaptor.capture());
//
////        RecurringEvents recurringEvents = recurringArgumentCaptor.getValue();
//
////        assertNull(recurringEvents.getEventCapacity());
//
//        //Setup
//        ticketingTypeSettingsDto.setEventCapacity(new Double(10));
//
//        //Execution
//        ticketingManageServiceImpl.setEventCapacityForRecurring(ticketingTypeSettingsDto, event, recurringEventId, ticketing);
//
//        ArgumentCaptor<RecurringEvents> recurringArgumentCaptorOne = ArgumentCaptor.forClass(RecurringEvents.class);
////        verify(recurringEventsRepository, times(2)).save(recurringArgumentCaptorOne.capture());
//
//        RecurringEvents recurringEventsOne = recurringArgumentCaptorOne.getValue();
//        assertEquals(ticketingTypeSettingsDto.getEventCapacity(), recurringEventsOne.getEventCapacity());
//    }

    @Test
    void test_setEventCapacityForRecurringEventId_NOT_GraterThanZERO_EventCapacitySet_NullAndNotNull(){

        //Setup
        TicketingTypeSettingsDto ticketingTypeSettingsDto = new TicketingTypeSettingsDto();
        ticketingTypeSettingsDto.setShowRemainingTickets(false);
        recurringEventId = 0L;

        List<RecurringEvents> recurringEvents = new ArrayList<>();
        RecurringEvents recurringEvent = new RecurringEvents();
        recurringEvent.setId(1L);
        recurringEvents.add(recurringEvent);

        TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto = new TicketingTypeSettingStatusDto();
        ticketingTypeSettingStatusDto.setShowRemainingTickets(Boolean.TRUE.toString());
        ticketingTypeSettingStatusDto.setEventLimitCapacity(Boolean.TRUE.toString());
        recurringEvent.setRecurringJson(parseToJsonString(ticketingTypeSettingStatusDto));


        //Mock
        when(recurringEventsScheduleService.getRecurringEventsByStatus(event)).thenReturn(recurringEvents);

        //Execution
        ticketingManageServiceImpl.setEventCapacityForRecurring(ticketingTypeSettingsDto, event, recurringEventId, ticketing);


        Class<ArrayList<RecurringEvents>> arrayListClass = (Class<ArrayList<RecurringEvents>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<RecurringEvents>> recurringArgumentCaptor = ArgumentCaptor.forClass(arrayListClass);
        verify(recurringEventsRepository, times(1)).saveAll(recurringArgumentCaptor.capture());


        List<RecurringEvents> recurringEventsCap = recurringArgumentCaptor.getValue();
        assertTrue(!CollectionUtils.isEmpty(recurringEventsCap));
        assertNull(recurringEventsCap.get(0).getEventCapacity());


        //Setup
        ticketingTypeSettingsDto.setEventCapacity(new Double(10));
        ticketing.setEventCapacity(new Double(0));
        recurringEvent.setEventCapacity(Double.valueOf(0));

        //Execution
        ticketingManageServiceImpl.setEventCapacityForRecurring(ticketingTypeSettingsDto, event, recurringEventId, ticketing);

        Class<ArrayList<RecurringEvents>> arrayListClassOne = (Class<ArrayList<RecurringEvents>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<RecurringEvents>> recurringArgumentCaptorOne = ArgumentCaptor.forClass(arrayListClassOne);
        verify(recurringEventsRepository, times(2)).saveAll(recurringArgumentCaptorOne.capture());

        List<RecurringEvents> recurringEventsCapOne = recurringArgumentCaptorOne.getValue();
        assertTrue(!CollectionUtils.isEmpty(recurringEventsCapOne));
        assertNotNull(recurringEventsCapOne.get(0).getEventCapacity());
        assertEquals(ticketingTypeSettingsDto.getEventCapacity(), ticketing.getEventCapacity());
    }

    private String parseToJsonString(TicketingTypeSettingStatusDto ticketingTypeSettingStatusDto){
        Gson gson = new Gson();
        return gson.toJson(ticketingTypeSettingStatusDto,TicketingTypeSettingStatusDto.class);
    }

}
