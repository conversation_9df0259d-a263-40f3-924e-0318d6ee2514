package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.DataType;
import com.accelevents.domain.enums.TicketPaymentStatus;
import com.accelevents.domain.enums.TicketStatus;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.EventTicketsCommonRepo;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.services.repo.helper.OrderAuditLogRepoService;
import com.accelevents.ticketing.dto.*;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TicketingOrderDetailsServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingOrderDetailsServiceImpl ticketingOrderDetailsServiceImpl = new TicketingOrderDetailsServiceImpl();

    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private StripeTransactionService stripeTransactionService;

    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Mock
    private TicketingPurchaseService ticketingPurchaseService;

    @Mock
    private StripeService stripeService;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private EventRepoService eventRepoService;

    @Mock
    private OrganizerService organizerService;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;
    @Mock
    private OrderAuditLogRepoService orderAuditLogRepoService;
    @Mock
    private EventTicketTransferDetailService eventTicketTransferDetailService;

    @Mock
    private EventTicketTransactionService eventTicketTransactionService;


    private Event event;
    private User user;
    private Ticketing ticketing;
    private TicketingModuleDTO ticketingModuleDTO;
    private OrderDtoV2 orderDtoV2;
	private StripeTransaction stripeTransaction;
    private RecurringEvents recurringEvents=new RecurringEvents();
    private PurchaserInfo purchaserInfo;
    private EventTickets eventTickets;

    private EventTickets eventTickets1;
    private AttendeeDto attendeeDto;
    private TicketingTable ticketingTable;
    private TicketingOrder ticketingOrder;
    private TicketHolderAttributes ticketHolderAttributes;
    private Unmarshaller unmarshaller;
	private TicketAttributeValueDto ticketAttributeValueDto1, ticketAttributeValueDto2;
	private ValueDto valueDtoPurchaser;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes1;
	private AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute1;

	private Long id = 1L;
    private String firstName = "Jon";
    private String lastName = "Kaz";
    private String email = "<EMAIL>";
    private String search = "search String";
    private  String barcode = "1d44e288-38f2-425e-929f-68a014b08c88";
    private int size = 10;
    private int page = 1;
    private String cardType = Constants.CC;
    private String lastFour = "1111";
    private String note = "note for ticket order";
    private Date orderDate = new Date("2019/03/01 05:30");
    private Date searchTimeStamp = new Date("03/01/2019 05:30");
    private Date recurringEventStartDate = new Date("2019/02/01 05:30");
    private long numberOfTicketTypeExcludingFreeType =1;
    private StripeDTO stripeDTO;
    private Map<Long, List<EventTickets>> ticketingOrderListMap = new HashMap<>();
    private PageSizeSearchObj pageSizeSearchObj = new PageSizeSearchObj(page,size);

    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setCurrency(Currency.USD);
        event.setTimezoneId("India Time");
        event.setEquivalentTimeZone("Asia/Calcutta");

        eventTickets = EventDataUtil.getEventTickets();
        eventTickets1 = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        ticketAttributeValueDto1 =  getTicketAttributeValueDTO1();
        ticketAttributeValueDto2 =  getTicketAttributeValueDTO2();
        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        stripeDTO = new StripeDTO(0.30, 2.9, "US", EnumPaymentGateway.STRIPE.name());
        orderDtoV2 = getOrderDtoV2();
        recurringEvents.setId(1L);
        recurringEvents.setRecurringEventStartDate(ticketing.getEventStartDate());
        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), Collections.singletonList(eventTickets));
        ticketingModuleDTO = new TicketingModuleDTO();
        ticketingModuleDTO.setRecurringEvent(true);
        ticketingModuleDTO.setTicketingActivated(true);


        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

        ticketingOrder.setId(id);
        ticketingOrder.setOrderDate(orderDate);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setNote(note);

        stripeTransaction = new StripeTransaction();
        stripeTransaction.setLastFour(lastFour);
        stripeTransaction.setCardType(cardType);

        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);
        recurringEvents.setRecurringEventStartDate(recurringEventStartDate);
    }

    @Test
    void test_getOrderData_success_with_ticketHolderFirstName() throws JAXBException {

        //setup


        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock

        when(eventCommonRepoService.findAllByListOrderIds(any())).thenReturn(eventTicketsList);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);
        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), anyList(),anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(ticketingService.isRecurringEvent(any())).thenReturn(false);

        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(user.getFirstName(), getOrderData.getOrders().iterator().next().getPurchaser().getFirstName());
        assertEquals(orderDtoV2.getCurrency(), getOrderData.getOrders().iterator().next().getCurrency());
        assertEquals(orderDtoV2.getAttendee().iterator().next().getFirstName(), getOrderData.getOrders().iterator().next().getAttendee().iterator().next().getFirstName());
    }

    @Test
    void test_getOrderData_success_with_ticketingOrdersPages_null() throws JAXBException {

        //mock
        doReturn(null).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);

        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);

        assertEquals(0, getOrderData.getRecordsTotal());
    }

    @Test
    void test_getOrderData_success_with_ticketingOrdersPages_content_empty() throws JAXBException {

        //setup
        List<TicketingOrder> ticketingOrderList = new ArrayList<>();

        Page<TicketingOrder> ticketingOrdersPages = new PageImpl<>(ticketingOrderList);
        ticketingOrdersPages.getTotalElements();
        ticketingOrdersPages.getTotalPages();

        //mock

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);

        assertTrue(getOrderData.getRecordsTotal() == 0);
    }

    //@Test I think we dont want this test case.
    void test_getOrderData_success_with_eventTickets_null() throws JAXBException {

        //setup
        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(null);

        List<AttendeeDtoV2> attendeeDtoV2List = new ArrayList<>();

        orderDtoV2 = new OrderDtoV2();
        orderDtoV2.setAttendee(attendeeDtoV2List);

        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);
        when(eventTicketsCommonRepo.findByTicketingOrder(ticketingOrder.getId())).thenReturn(eventTicketsList);
        Mockito.doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(),anyLong(), any(), anyBoolean());
        doReturn(ticketingModuleDTO).when(ticketingHelperService).findTicketingFieldsByEventId(event);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);

        assertEquals(getOrderData.getRecordsFiltered() , ticketingOrdersPages.getNumberOfElements());
        assertEquals(getOrderData.getRecordsTotal() , ticketingOrdersPages.getTotalElements());
    }

    @Test
    void test_getOrderData_success_with_ticketingOrder_status_card() throws JAXBException {

        //setup

        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);



        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();

        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);


        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);


        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(getOrderData.getRecordsFiltered() , ticketingOrdersPages.getNumberOfElements());
        assertEquals(getOrderData.getRecordsTotal() , ticketingOrdersPages.getTotalElements());
    }

    @Test
    void test_getOrderData_success_with_ticketingOrder_OrderType_UNPAID() throws JAXBException {

        //setup

        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);


        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(getOrderData.getRecordsFiltered() , ticketingOrdersPages.getNumberOfElements());
        assertEquals(getOrderData.getRecordsTotal() , ticketingOrdersPages.getTotalElements());
    }

    @Test
    void test_getOrderData_success_with_ticketHolderRequiredAttributes_name_STRING_CELL_SPACE_PHONE() throws JAXBException {

        //setup

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), anyList(), anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);

        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(user.getFirstName(), getOrderData.getOrders().iterator().next().getPurchaser().getFirstName());
        assertEquals(orderDtoV2.getCurrency(), getOrderData.getOrders().iterator().next().getCurrency());
        assertEquals(orderDtoV2.getAttendee().iterator().next().getFirstName(), getOrderData.getOrders().iterator().next().getAttendee().iterator().next().getFirstName());
    }

    @Test
    void test_getOrderData_success_with_enabledForTicketPurchaser_false() throws JAXBException {

        //setup


        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(false);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), any(), anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);

        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event, pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(user.getFirstName(), getOrderData.getOrders().iterator().next().getPurchaser().getFirstName());
        assertEquals(orderDtoV2.getCurrency(), getOrderData.getOrders().iterator().next().getCurrency());
        assertEquals(orderDtoV2.getAttendee().iterator().next().getFirstName(), getOrderData.getOrders().iterator().next().getAttendee().iterator().next().getFirstName());
    }

    @Test
    void test_getOrderData_success_with_ticketAttributeValueDto_null() throws JAXBException {

        //setup


        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);
        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), any(), anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);
        when(eventTicketTransactionService.getTotalFeesByTicketingOrderIdsAndChargeStatus(anyList())).thenReturn(eventTicketFees);
        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event, pageSizeSearchObj, search, id, orderDate, DataType.TICKET, searchTimeStamp,false, null,false);
        assertEquals(user.getFirstName(), getOrderData.getOrders().iterator().next().getPurchaser().getFirstName());
        assertEquals(orderDtoV2.getCurrency(), getOrderData.getOrders().iterator().next().getCurrency());
        assertEquals(orderDtoV2.getAttendee().iterator().next().getFirstName(), getOrderData.getOrders().iterator().next().getAttendee().iterator().next().getFirstName());
    }

    @Test
    void test_getOrderData_success_with_ticketHolderlastName() throws JAXBException {

        //setup


        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setRecurringEvents(recurringEvents);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), any(), anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event, pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(user.getLastName(), getOrderData.getOrders().iterator().next().getPurchaser().getLastName());
        assertEquals(orderDtoV2.getTimezoneId(), getOrderData.getOrders().iterator().next().getTimezoneId());
        assertEquals(orderDtoV2.getEquivalentTimezone(),getOrderData.getOrders().iterator().next().getEquivalentTimezone());
        assertEquals(orderDtoV2.getAttendee().iterator().next().getLastName(), getOrderData.getOrders().iterator().next().getAttendee().iterator().next().getLastName());
    }

    @Test
    void test_getOrderData_success_with_ticketHolderEmail() throws JAXBException {

        //setup

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingOrderId(ticketingOrder.getId());
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);

        Page<TicketingOrder> ticketingOrdersPages = getTicketingOrdersPages();

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = getTicketHolderRequiredAttributes();

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);

        doReturn(ticketingOrdersPages).when(ticketingOrderService).findByEventidAndStatusIsPaid(event,size, page, search, id, orderDate, DataType.TICKET,searchTimeStamp,false);

        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), any(), anyBoolean());
        doReturn(ticketHolderRequiredAttributesList).when(ticketHolderRequiredAttributesService).getAllAttributesOrderByAttributeOrder(event);

        when(eventCommonRepoService.findAllByListOrderIds(anyList())).thenReturn(eventTicketsList);
        when(ticketingService.isRecurringEvent(any())).thenReturn(false);
        //Execution
        OrderPageData getOrderData = ticketingOrderDetailsServiceImpl.getOrderData(event,pageSizeSearchObj, search, id, orderDate, DataType.TICKET,searchTimeStamp,false, null,false);
        assertEquals(user.getEmail(), getOrderData.getOrders().iterator().next().getPurchaser().getEmail());
    }

    @Test
    void test_getOrderDataByBarcode_success(){

        //setup
        double discountAmount = 10d;
        int remainingSeconds = 600;

		TicketAttributeDto ticketAttributeDto = new TicketAttributeDto();
        ticketAttributeDto.setOnlyDonationTicket(false);
        ticketAttributeDto.setOrderId(id);
        ticketAttributeDto.setRemainingSeconds(remainingSeconds);
        ticketAttributeDto.setDiscountAmount(discountAmount);

        //mock
        when(eventTicketsCommonRepo.findByBarcodeId(barcode)).thenReturn(eventTickets);
        when(ticketingOrderDetailsServiceImpl.getOrderDataById(event,ticketingOrder.getId(),user)).thenReturn(ticketAttributeDto);
        when(ticketingOrderService.findByidAndEventid(eventTickets.getTicketingOrder().getId(),event)).thenReturn(ticketingOrder);

        //Execution
        TicketAttributeDto orderDataByBarcodeData = ticketingOrderDetailsServiceImpl.getOrderDataByBarcode(event, barcode, user);

        assertNotNull(orderDataByBarcodeData);
        assertEquals(id, orderDataByBarcodeData.getOrderId());
        assertEquals(remainingSeconds, orderDataByBarcodeData.getRemainingSeconds());
        assertEquals(orderDataByBarcodeData.getDiscountAmount(), discountAmount);
    }

    @Test
    void test_getOrderDataByBarcode_throwException_barcodeNotExist() throws IOException {

        //mock
        when(eventTicketsCommonRepo.findByBarcodeId(barcode)).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingOrderDetailsServiceImpl.getOrderDataByBarcode(event, barcode, user));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getOrderDtoV2_success() {

        //setup

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");


        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_REFUNDED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusIsCanceledThenOrderIsCanceled() {

        //setup

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock




        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusIsCanceledWithPartialRefundThenOrderIsCanceled() {

        //setup

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusIsPartiallyRefundedThenOrderIsPartiallyRefunded(){

        //setup

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.STATUS_TO_DISPLAY_PARTIALLY_REFUNDED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusesIsPartiallyRefundedAndCanceledThenOrderIsPartiallyRefunded(){

        //setup
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTickets1.setTicketStatus(TicketStatus.CANCELED);
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());

        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.STATUS_TO_DISPLAY_PARTIALLY_REFUNDED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusRefundedAndCanceledThenOrderIsRefunded(){

        //setup


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets1.setTicketStatus(TicketStatus.CANCELED);
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.STATUS_TO_DISPLAY_PARTIALLY_REFUNDED, getOrderDtoV2.getStatus());
    }

    @Test
    void whenTicketStatusCanceledThenOrderIsCanceled(){

        //setup

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTickets.setTicketStatus(TicketStatus.CANCELED);
        eventTickets1.setTicketPaymentStatus(TicketPaymentStatus.PARTIALLY_REFUNDED);
        eventTickets1.setTicketStatus(TicketStatus.CANCELED);
        eventTicketsList.add(eventTickets);
        eventTicketsList.add(eventTickets1);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        eventTickets.setRecurringEvents(recurringEvents);
        ticketingOrderListMap.put(ticketingOrder.getId(), eventTicketsList);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_success_with_recurringEvents_empty() {

        //setup
        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

        ticketingOrder.setId(id);
        ticketingOrder.setOrderDate(orderDate);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setNote(note);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);


        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");


        attendeeDto = new AttendeeDto();
        attendeeDto.setRefundedAmount(0d);
        List<AttendeeDto> attendeeDtos = new ArrayList<>();
        attendeeDtos.add(attendeeDto);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE,"US", EnumPaymentGateway.STRIPE.name());
                //mock





        //Mockito.doReturn(attendeeDtos).when(ticketingOrderDetailsServiceImpl).getAllTicketAttendee(any(), any(),any());

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_success_with_stripe_transaction_null() {

        //setup
        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

        ticketingOrder.setId(id);
        ticketingOrder.setOrderDate(orderDate);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setNote(note);

        eventTickets.setTicketStatus(TicketStatus.DELETED);
        eventTickets.setRecurringEventId(null);
        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE,"US",EnumPaymentGateway.STRIPE.name());

        //mock





        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_success_with_ticketingStatusCanceled() {

        //setup


        eventTickets.setTicketStatus(TicketStatus.CANCELED);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");


        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE,"US",EnumPaymentGateway.STRIPE.name());
        //mock





        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_success_with_ticketingStatusUnpaid() {

        //setup
        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

        ticketingOrder.setId(id);
        ticketingOrder.setOrderDate(orderDate);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.UNPAID);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.UNPAID);
        ticketingOrder.setNote(note);

        eventTickets.setRefundedAmount(0d);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE,"US",EnumPaymentGateway.STRIPE.name());

        //mock

        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_success_with_ticketingStatusPaid() {

        //setup

        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

        ticketingOrder.setId(id);
        ticketingOrder.setOrderDate(orderDate);
        ticketingOrder.setNote(note);

        eventTickets.setRefundedAmount(0d);

        List<EventTickets> eventTicketsList = new ArrayList<>();
        eventTicketsList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketsList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        Organizer organizer = new Organizer();
        organizer.setOrganizerPageURL("test");

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE,"US",EnumPaymentGateway.STRIPE.name());

        //mock





        //Execution
        OrderDtoV2 getOrderDtoV2 = ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, eventTicketFees,false);
        assertEquals(Constants.TICKETING_STATUS_CANCELED, getOrderDtoV2.getStatus());
    }

    @Test
    void test_getOrderDtoV2_throwWxception_refundOrderNotPaid() {

        //setup
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.CREATE);

        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US",EnumPaymentGateway.STRIPE.name());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> ticketingOrderDetailsServiceImpl.getOrderDtoV2(ticketingOrderListMap, ticketingOrder, event, purchaserInfo, stripeDTO, ticketing.isRecurringEvent(), 0L, Collections.emptyList(),false));

        assertEquals(NotFoundException.TicketingOrderExceptionMsg.REFUND_ORDER_NOT_PAID.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getOrderDetailsByBarcode_success() {

        //setup
        orderDtoV2 = new OrderDtoV2();
        orderDtoV2.setId(id);
        orderDtoV2.setCurrency(String.valueOf(Currency.USD));

        //mock
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(eventTicketsCommonRepo.findByBarcodeId(any())).thenReturn(eventTickets);
        when(ticketingOrderService.findByidAndEventid(eventTickets.getTicketingOrder().getId(),event)).thenReturn(ticketingOrder);
        doReturn(orderDtoV2).when(ticketingOrderDetailsServiceImpl).getOrderDtoV2(anyMap(), any(), any(), any(), any(), anyBoolean(), anyLong(), anyList(), anyBoolean());

        //Execution
        OrderDtoV2 orderDetails = ticketingOrderDetailsServiceImpl.getOrderDetailsByBarcode(event, barcode);
        assertEquals(event.getCurrency().getSymbol(), orderDetails.getCurrency());
        assertEquals(event.getTimezoneId(), orderDetails.getTimezoneId());
        assertEquals(event.getEquivalentTimeZone(),orderDetails.getEquivalentTimezone());
        assertEquals(ticketingOrder.getId(), orderDetails.getId());
    }

    //@Test
    void test_getOrderDetailsByBarcode_throwException_barcodeNotExist() {

        //mock
        when(eventTicketsCommonRepo.findByBarcodeId(any())).thenReturn(null);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> ticketingOrderDetailsServiceImpl.getOrderDetailsByBarcode(event, barcode));

        assertEquals(NotAcceptableException.TicketingExceptionMsg.BARCODE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_isTicketingActive_success() {

        //mock
        doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);

        boolean ticketingStatus = ticketingOrderDetailsServiceImpl.isTicketingActive(event);
        assertEquals(ticketing.getActivated(), ticketingStatus);
    }

    @Test
    void test_unmashler_success() throws JAXBException{

        //setup
        String xml = EventDataUtil.getSampleXML();

        unmarshaller = ticketingOrderDetailsServiceImpl.getUnmashler();

        //Execution
        TicketAttributeValueDto actualData = ticketingOrderDetailsServiceImpl.unmashler(xml, unmarshaller);
        assertNotNull(actualData);
    }

    @Test
    void test_getAllTicketAttendee_success_with_eventTickets() {

        //setup
        String search = "";
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US",EnumPaymentGateway.STRIPE.name());

        //Execution
        List<AttendeeDto> allTicketAttendee = ticketingOrderDetailsServiceImpl.getAllTicketAttendee(eventTicketList, stripeDTO, numberOfTicketTypeExcludingFreeType, eventTicketFees, event.getEquivalentTimeZone());
        assertNotNull(allTicketAttendee);
        for (AttendeeDto actualData : allTicketAttendee) {
            assertEquals(eventTickets.getTicketStatus().getStatus(), actualData.getStatus());
            assertEquals(eventTickets.getSeatNumber(), actualData.getSeatNumber());
        }
    }

    @Test
    void getAllTicketAttendeeWithpaymentStatus() {

        //setup
        String search = "";
        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.REFUNDED);
        eventTicketList.add(eventTickets);
        List<EventTicketFeesDto> eventTicketFees = eventTicketList.stream().map(eventTickets -> new EventTicketFeesDto(eventTickets.getId(), 0.0d, 0.0d, 0.0d, 0.0d, 0.0d)).collect(Collectors.toList());
        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US",EnumPaymentGateway.STRIPE.name());

        //Execution
        List<AttendeeDto> allTicketAttendee = ticketingOrderDetailsServiceImpl.getAllTicketAttendee(eventTicketList, stripeDTO, numberOfTicketTypeExcludingFreeType, eventTicketFees, event.getEquivalentTimeZone());
        assertNotNull(allTicketAttendee);
        for (AttendeeDto actualData : allTicketAttendee) {
            assertEquals(eventTickets.getTicketStatus().getStatus(), actualData.getStatus());
            assertEquals(eventTickets.getSeatNumber(), actualData.getSeatNumber());
            assertEquals(eventTickets.getTicketPaymentStatus(), actualData.getTicketPaymentStatus());
        }
    }

    @Test
    void test_getAllTicketAttendee_success() {

        //setup
        String search = "";
        StripeDTO stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US",EnumPaymentGateway.STRIPE.name());
        //mock

        //Execution
        List<AttendeeDto> allTicketAttendee = ticketingOrderDetailsServiceImpl.getAllTicketAttendee(null, stripeDTO, numberOfTicketTypeExcludingFreeType, Collections.emptyList(), event.getEquivalentTimeZone());
        assertEquals(0, allTicketAttendee.size());
    }

    @Test
    void test_setTicketType_success_with_ticketingTable() {

        //setup
        long tableNumberSequence = 1L;
        attendeeDto = new AttendeeDto();

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        //Execution
        ticketingOrderDetailsServiceImpl.setTicketType(eventTickets, attendeeDto, ticketingTable);
        assertTrue(attendeeDto.isTable());
    }

    @Test
    void test_setTicketType_success() {

        //setup
        long tableNumberSequence = 1L;
        attendeeDto = new AttendeeDto();

        ticketingTable = new TicketingTable();
        ticketingTable.setTableNoSequence(tableNumberSequence);

        //Execution
        ticketingOrderDetailsServiceImpl.setTicketType(eventTickets, attendeeDto, null);
        assertFalse(attendeeDto.isTable());
    }

    private TicketAttributeValueDto getTicketAttributeValueDTO1() {
		AttributeKeyValueDto holderAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        holderAttributeKeyValueDtoAttribute.setValue(firstName);

		AttributeKeyValueDto holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        holderAttributeKeyValueDtoQuestion.setValue(lastName);

        purchaserAttributeKeyValueDtoAttribute1 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute1.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute1.setValue(firstName);

		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute2 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute2.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoAttribute2.setValue(lastName);

		AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute3 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute3.setKey(Constants.EMAIL);
        purchaserAttributeKeyValueDtoAttribute3.setValue(email);

		AttributeKeyValueDto purchaserAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoQuestion.setValue(lastName);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderAttribute = new ArrayList<>();
        attributeKeyValueDtosHolderAttribute.add(holderAttributeKeyValueDtoAttribute);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute1);
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute2);
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute3);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserQuestion = new ArrayList<>();
        attributeKeyValueDtosPurchaserQuestion.add(purchaserAttributeKeyValueDtoQuestion);

		ValueDto valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtosHolderAttribute);
        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);

        valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);
        valueDtoPurchaser.setQuestions(attributeKeyValueDtosPurchaserQuestion);

        ticketAttributeValueDto1 = new TicketAttributeValueDto();
        ticketAttributeValueDto1.setHolder(valueDtoHolder);
        ticketAttributeValueDto1.setPurchaser(valueDtoPurchaser);

        return ticketAttributeValueDto1;
    }

    private TicketAttributeValueDto getTicketAttributeValueDTO2() {

        purchaserAttributeKeyValueDtoAttribute1 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute1.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute1.setValue(firstName);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute1);

        valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);

        ticketAttributeValueDto2 = new TicketAttributeValueDto();
        ticketAttributeValueDto2.setPurchaser(valueDtoPurchaser);

        return ticketAttributeValueDto2;
    }

    private OrderDtoV2 getOrderDtoV2() {
        user.setEmail(email);

        purchaserInfo = new PurchaserInfo(user);

		AttendeeDtoV2 attendeeDtoV2 = new AttendeeDtoV2();
        attendeeDtoV2.setFirstName(firstName);
        attendeeDtoV2.setLastName(lastName);

        List<AttendeeDtoV2> attendeeDtoV2List = new ArrayList<>();
        attendeeDtoV2List.add(attendeeDtoV2);

        orderDtoV2 = new OrderDtoV2();
        orderDtoV2.setId(id);
        orderDtoV2.setCurrency(String.valueOf(Currency.USD));
        orderDtoV2.setAttendee(attendeeDtoV2List);
        orderDtoV2.setPurchaser(purchaserInfo);

        return orderDtoV2;
    }

    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes() {
        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_FIRST_SPACE_NAME);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

		TicketHolderRequiredAttributes ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes2.setAttribute(true);
        ticketHolderRequiredAttributes2.setName(Constants.STRING_LAST_SPACE_NAME);
        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);

		TicketHolderRequiredAttributes ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes3.setAttribute(true);
        ticketHolderRequiredAttributes3.setName(Constants.STRING_EMAIL_SPACE);
        ticketHolderRequiredAttributes3.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
        return ticketHolderRequiredAttributesList;
    }

    private Page<TicketingOrder> getTicketingOrdersPages() {
        ticketingOrder.setId(id);

        List<TicketingOrder> ticketingOrderList = new ArrayList<>();
        ticketingOrderList.add(ticketingOrder);
        Page<TicketingOrder> ticketingOrdersPages = new PageImpl<>(ticketingOrderList);
        ticketingOrdersPages.getTotalElements();
        ticketingOrdersPages.getTotalPages();

        return ticketingOrdersPages;
    }

    @Test
    void test_setCCfeeAndTicketingFees(){

        EventTickets eventTickets = new EventTickets();
        eventTickets.setId(1L);
        eventTickets.setPaidAmount(53.35);
        eventTickets.setAeFeeAmount(1.5);
        eventTickets.setChargeId("ch_1");
        eventTickets.setTicketingOrder(EventDataUtil.getTicketingOrder());
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.PAID);
        eventTickets.setTicketingTypeId(ticketingType);
        EventTicketFeesDto eventTicketFeesDto = new EventTicketFeesDto(eventTickets.getId(), 0.0d, 1.5, 0.0d, 0.0d, 1.85);
        AttendeeDto attendeeDto = new AttendeeDto();

        ticketingOrderDetailsServiceImpl.setCCfeeAndTicketingFees(stripeDTO, 1, eventTickets, attendeeDto, Collections.singletonList(eventTicketFeesDto));
        assertEquals(1.85, attendeeDto.getCcFee()); // For paid amount $100 ccFee is 3.2
        assertEquals(1.5, attendeeDto.getTicketingFee());
    }

    @Test
    void test_setCCfeeAndTicketingFees_withWLEvent(){

        EventTickets eventTickets = new EventTickets();
        eventTickets.setId(1L);
        eventTickets.setPaidAmount(55.35);
        eventTickets.setAeFeeAmount(1.5);
        eventTickets.setWlAFeeAmount(1);
        eventTickets.setWlBFeeAmount(1);
        eventTickets.setChargeId("ch_1");
        eventTickets.setTicketingOrder(EventDataUtil.getTicketingOrder());
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.PAID);
        eventTickets.setTicketingTypeId(ticketingType);
        EventTicketFeesDto eventTicketFeesDto = new EventTicketFeesDto(eventTickets.getId(), 0.0d, 1.5, 1, 1, 1.91);
        AttendeeDto attendeeDto = new AttendeeDto();

        ticketingOrderDetailsServiceImpl.setCCfeeAndTicketingFees(stripeDTO, 1, eventTickets, attendeeDto, Collections.singletonList(eventTicketFeesDto));
        assertEquals(1.91, attendeeDto.getCcFee());
        assertEquals(3.5, attendeeDto.getTicketingFee());
    }

    @Test
    void test_setCCfeeAndTicketingFees_withWLEvent_RefundedWLfees(){

        EventTickets eventTickets = new EventTickets();
        eventTickets.setId(1L);
        eventTickets.setPaidAmount(55.35);
        eventTickets.setAeFeeAmount(1.5);
        eventTickets.setWlAFeeAmount(1);
        eventTickets.setWlBFeeAmount(1);
        eventTickets.setRefundedWlAFee(1);
        eventTickets.setRefundedWlBFee(1);
        eventTickets.setChargeId("ch_1");
        eventTickets.setTicketingOrder(EventDataUtil.getTicketingOrder());
        TicketingType ticketingType = new TicketingType();
        ticketingType.setTicketType(TicketType.PAID);
        eventTickets.setTicketingTypeId(ticketingType);
        EventTicketFeesDto eventTicketFeesDto = new EventTicketFeesDto(eventTickets.getId(), 0.0d, 1.5, 0.0d, 0.0d, 1.91);
        AttendeeDto attendeeDto = new AttendeeDto();

        ticketingOrderDetailsServiceImpl.setCCfeeAndTicketingFees(stripeDTO, 1, eventTickets, attendeeDto, Collections.singletonList(eventTicketFeesDto));
        assertEquals(1.91, attendeeDto.getCcFee());
        assertEquals(1.5, attendeeDto.getTicketingFee());
    }
}