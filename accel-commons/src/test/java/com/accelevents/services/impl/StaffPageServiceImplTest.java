package com.accelevents.services.impl;

import com.accelevents.common.dto.AuctionFundAndNeedItemDetail;
import com.accelevents.common.dto.RaffleItemDetailDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.StripeCreditCardDto;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.perfomance.dto.ItemNameCodeDto;
import com.accelevents.services.*;
import com.accelevents.staff.dto.StaffItemDetail;
import com.accelevents.staff.dto.StaffUserDetail;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.accelevents.utils.Constants.STRING_AUCTION;
import static com.accelevents.utils.Constants.STRING_FUNDANEED;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
public class StaffPageServiceImplTest {

    @Spy
    @InjectMocks
    private StaffPageServiceImpl staffPageServiceImpl;
    @Mock
    private ItemService itemService;
    @Mock
    private SubmittedRaffleTicketService submittedRaffleTicketService;
    @Mock
    private PledgeService pledgeService;
    @Mock
    private UserService userService;
    @Mock
    private AuctionBidService auctionBidService;
    @Mock
    private EventService eventService;
    @Mock
    private PurchasedRaffleTicketService purchasedRaffleTicketService;
    @Mock
    private AuctionService auctionService;
    @Mock
    private WinnerService winnerService;
    @Mock
    private CauseAuctionService causeAuctionService;
    @Mock
    private RaffleService raffleService;
    @Mock
    private DonationSettingsService donationSettingsService;


    private Event event;
    private StaffItemDetail staffItemDetail;
    private Item item;
    private Auction auction;
    private Winner winner;
    private SubmittedRaffleTicket submittedRaffleTicket;
    private Pledge pledge;
    private CauseAuction causeAuction;
    private Raffle raffle;

    private String itemName = "my first item";
    private String itemCode = "COD";
    private Double currentBid = 500d;
    private Double startingBid = 300d;
    private Double buyItNow = 1000d;
    private Double bidIncrement = 75d;
	private Double minPrice = 100d;
	private Double winnerBid = 500d;
    private Boolean winnerBidPaid = true;
    private Integer totalPledgeAmount = 900;
    private String moduleType = "auction";
    private String search = "name";
	String raffeModule = Constants.STRING_RAFFLE;
    String auctinModule = STRING_AUCTION;
    String fundAndNeedmodule = STRING_FUNDANEED;

    @BeforeEach
    void setUp() throws Exception {
        event = new Event();
        event.setCurrency(Currency.USD);
        staffItemDetail = new StaffItemDetail();
        item = new Item();
        item.setId(1L);
		String currency = event.getCurrency().getSymbol();
        submittedRaffleTicket=new SubmittedRaffleTicket();
        pledge =new Pledge();
        causeAuction = new CauseAuction();
        causeAuction.setId(1);
        raffle=new Raffle();
        raffle.setId(1);
    }

    @Test
    void test_getItemByValuesForMobileApp_ItemNotFound_Exception(){
        //setup
        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffPageServiceImpl.getItemByValuesForMobileApp(event, search, auctinModule, EnumLabelLanguageCode.EN.name()));

        //Assertion
        assertEquals(NotFoundException.ItemNotFound.ITEM_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());

    }

    @Test
    void test_getItemByValuesForMobileApp_NotHaveModuleType() {

        //setup
        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);
        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, auctinModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertTrue(itemValues.isEmpty());

        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withAuctionAndWinnersEmpty() {

        //setup
        setItem(0d, 1);

        setStaffItemDetailsForAuction(item);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();

        List<AuctionBid> nextAuctionBids = new ArrayList<>();
        nextAuctionBids.add(auctionBid);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        AuctionFundAndNeedItemDetail auctionFundAndNeedItem=new AuctionFundAndNeedItemDetail(auctionBid,1);
        auctionFundAndNeedItemDetails.add(auctionFundAndNeedItem);
        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(auctionService.findByEvent(any())).thenReturn(auction);
        Mockito.when(auctionBidService.findByItemOrderByAmountDesc(any(),anyList())).thenReturn(nextAuctionBids);
        Mockito.when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item.getId())).thenReturn(Collections.emptyList());
        Mockito.when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, auctinModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.get(0).getCurrentBid(), staffItemDetail.getCurrentBid());
        assertEquals(itemValues.get(0).getBidIncrement(), staffItemDetail.getBidIncrement());
        assertEquals(itemValues.get(0).getBuyItNow(), staffItemDetail.getBuyItNow());
        assertEquals(itemValues.get(0).getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.get(0).getStartingBid(), staffItemDetail.getStartingBid());
        assertFalse(itemValues.get(0).isBoolCurrentBid());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getBidderName(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getBidderName());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getBidAmount(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getBidAmount());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getStatus(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getStatus());

        verify(itemService, Mockito.times(2)).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService,Mockito.atLeastOnce()).findByEvent(any());
        verify(auctionBidService).findByItemOrderByAmountDesc(any(),anyList());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withAuctionAndWinners() {

        //setup
        setItem(currentBid, 1);

        setStaffItemDetailsForAuction(item);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        winner=new Winner();
        winner.setBidId(1);

        List<Winner> winnersList=new ArrayList<>();
        winnersList.add(winner);

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

        List<AuctionBid> nextAuctionBids = new ArrayList<>();
        nextAuctionBids.add(auctionBid);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,0));
        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(auctionService.findByEvent(any())).thenReturn(auction);
        Mockito.when(auctionBidService.getByBidId(winner.getBidId())).thenReturn(Optional.of(auctionBid));
        Mockito.when(auctionBidService.findByItemOrderByAmountDesc(any(),anyList())).thenReturn(nextAuctionBids);
        Mockito.when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item.getId())).thenReturn(winnersList);
        Mockito.when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, auctinModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.get(0).getCurrentBid(), staffItemDetail.getCurrentBid());
        assertEquals(itemValues.get(0).getBidIncrement(), staffItemDetail.getBidIncrement());
        assertEquals(itemValues.get(0).getBuyItNow(), staffItemDetail.getBuyItNow());
        assertEquals(itemValues.get(0).getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.get(0).getStartingBid(), staffItemDetail.getStartingBid());
        assertTrue(itemValues.get(0).isBoolCurrentBid());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());

        for (int i=0;i<itemValues.get(0).getAuctionFundAndNeedItemDetail().size();i++){
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getBidderName(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getBidderName());
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getBidAmount(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getBidAmount());
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getStatus(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getStatus());
        }

        verify(itemService, Mockito.times(2)).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService,Mockito.atLeastOnce()).findByEvent(any());
        verify(auctionBidService).findByItemOrderByAmountDesc(any(),anyList());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
        verify(auctionBidService).getByBidId(anyLong());
    }

    @Test
    void test_getItemByValuesForMobileApp_withAuctionAndWinnersNextAuctionBidsEmpty() {

        //setup
        setItem(currentBid, 1);

        setStaffItemDetailsForAuction(item);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        winner=new Winner();
        winner.setBidId(1);

        List<Winner> winnersList=new ArrayList<>();
        winnersList.add(winner);
        winnersList.add(winner);
        winnersList.add(winner);

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));

        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(auctionService.findByEvent(any())).thenReturn(auction);
        Mockito.when(auctionBidService.getByBidId(winner.getBidId())).thenReturn(Optional.of(auctionBid));
        Mockito.when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item.getId())).thenReturn(winnersList);
        Mockito.when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, auctinModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.get(0).getCurrentBid(), staffItemDetail.getCurrentBid());
        assertEquals(itemValues.get(0).getBidIncrement(), staffItemDetail.getBidIncrement());
        assertEquals(itemValues.get(0).getBuyItNow(), staffItemDetail.getBuyItNow());
        assertEquals(itemValues.get(0).getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.get(0).getStartingBid(), staffItemDetail.getStartingBid());
        assertTrue(itemValues.get(0).isBoolCurrentBid());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());

        for (int i=0; i<itemValues.get(0).getAuctionFundAndNeedItemDetail().size(); i++) {
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getBidderName(), staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getBidderName());
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getBidAmount(), staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getBidAmount());
            assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(i).getStatus(), staffItemDetail.getAuctionFundAndNeedItemDetail().get(i).getStatus());
        }

        verify(itemService, Mockito.times(2)).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService,Mockito.atLeastOnce()).findByEvent(any());
        verify(auctionBidService,Mockito.atLeastOnce()).getByBidId(anyLong());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withRaffleItemWinnerAndNextRaffleItemDetailEmpty() {

        //setup
        long totalRaffleTickets=100;

        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName= user.getFirstName() +" "+ user.getLastName();

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);


        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(submittedRaffleTicket));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);


        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(submittedRaffleTicketService.findTopByRaffleIdAndItemAndIsWinnerOrderByUserDesc(anyLong(), any(),anyBoolean())).thenReturn(submittedRaffleTicket);
        Mockito.when(submittedRaffleTicketService.findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any())).thenReturn(Collections.emptyList());
        Mockito.when(submittedRaffleTicketService.getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any())).thenReturn(totalRaffleTickets);
        Mockito.when(submittedRaffleTicketService.getRaffleItemWinner(anyLong(), any())).thenReturn(user);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, raffeModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getTotalTicketsSubmitted(), staffItemDetail.getTotalTicketsSubmitted());
        assertEquals(itemValues.get(0).getWinnerName(), staffItemDetail.getWinnerName());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());
        assertEquals(itemValues.get(0).getRaffleItemDetail().get(0).getTicketSubmitterName(),staffItemDetail.getRaffleItemDetail().get(0).getTicketSubmitterName());
        assertEquals(itemValues.get(0).getRaffleItemDetail().get(0).getTotalSubmittedTickets(),staffItemDetail.getRaffleItemDetail().get(0).getTotalSubmittedTickets());

        verify(submittedRaffleTicketService).getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any());
        verify(submittedRaffleTicketService).getRaffleItemWinner(anyLong(), any());
        verify(submittedRaffleTicketService).findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any());
        verify(submittedRaffleTicketService).findTopByRaffleIdAndItemAndIsWinnerOrderByUserDesc(anyLong(), any(),anyBoolean());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withRaffleItemWinnerEmptyAndNextRaffleItemDetail() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName="";

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);

        List<SubmittedRaffleTicket> nextRaffleItems=new ArrayList<>();
        nextRaffleItems.add(submittedRaffleTicket);

        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(nextRaffleItems.get(0)));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(submittedRaffleTicketService.findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any())).thenReturn(nextRaffleItems);
        Mockito.when(submittedRaffleTicketService.getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any())).thenReturn(totalRaffleTickets);
        Mockito.when(submittedRaffleTicketService.getRaffleItemWinner(anyLong(), any())).thenReturn(null);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, raffeModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getTotalTicketsSubmitted(), staffItemDetail.getTotalTicketsSubmitted());
        assertNull(itemValues.get(0).getWinnerName());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());
        assertEquals(itemValues.get(0).getRaffleItemDetail().get(0).getTicketSubmitterName(), staffItemDetail.getRaffleItemDetail().get(0).getTicketSubmitterName());
        assertEquals(itemValues.get(0).getRaffleItemDetail().get(0).getTotalSubmittedTickets(), staffItemDetail.getRaffleItemDetail().get(0).getTotalSubmittedTickets());

        verify(submittedRaffleTicketService).getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any());
        verify(submittedRaffleTicketService).getRaffleItemWinner(anyLong(), any());
        verify(submittedRaffleTicketService).findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withRaffleItemWinnerAndNextRaffleItemDetail() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName= user.getFirstName() +" "+ user.getLastName();

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);

        List<SubmittedRaffleTicket> nextRaffleItems=new ArrayList<>();
        nextRaffleItems.add(submittedRaffleTicket);

        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(submittedRaffleTicket));
        rowDetails.add(new RaffleItemDetailDto(nextRaffleItems.get(0)));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(submittedRaffleTicketService.findTopByRaffleIdAndItemAndIsWinnerOrderByUserDesc(anyLong(), any(),anyBoolean())).thenReturn(submittedRaffleTicket);
        Mockito.when(submittedRaffleTicketService.findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any())).thenReturn(nextRaffleItems);
        Mockito.when(submittedRaffleTicketService.getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any())).thenReturn(totalRaffleTickets);
        Mockito.when(submittedRaffleTicketService.getRaffleItemWinner(anyLong(), any())).thenReturn(user);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, raffeModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getTotalTicketsSubmitted(), staffItemDetail.getTotalTicketsSubmitted());
        assertEquals(itemValues.get(0).getWinnerName(), staffItemDetail.getWinnerName());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());

        for (int i=0; i<2; i++) {
            assertEquals(itemValues.get(0).getRaffleItemDetail().get(i).getTicketSubmitterName(), staffItemDetail.getRaffleItemDetail().get(i).getTicketSubmitterName());
            assertEquals(itemValues.get(0).getRaffleItemDetail().get(i).getTotalSubmittedTickets(), staffItemDetail.getRaffleItemDetail().get(i).getTotalSubmittedTickets());
        }

        verify(submittedRaffleTicketService).getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any());
        verify(submittedRaffleTicketService).getRaffleItemWinner(anyLong(), any());
        verify(submittedRaffleTicketService).findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any());
        verify(submittedRaffleTicketService).findTopByRaffleIdAndItemAndIsWinnerOrderByUserDesc(anyLong(), any(),anyBoolean());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withRaffleItemWinnerEmptyAndNextRaffleItemDetailEmpty() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        String winnerName="";

        setStaffItemDetailsForRaffle(item,winnerName);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(submittedRaffleTicketService.findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any())).thenReturn(Collections.emptyList());
        Mockito.when(submittedRaffleTicketService.getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any())).thenReturn(totalRaffleTickets);
        Mockito.when(submittedRaffleTicketService.getRaffleItemWinner(anyLong(), any())).thenReturn(null);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, raffeModule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getTotalTicketsSubmitted(), staffItemDetail.getTotalTicketsSubmitted());
        assertNull(itemValues.get(0).getWinnerName());
        assertTrue(itemValues.get(0).getRaffleItemDetail().isEmpty());
        assertEquals(itemValues.get(0).getNumberOfWinners(), staffItemDetail.getNumberOfWinners());

        verify(submittedRaffleTicketService).getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any());
        verify(submittedRaffleTicketService).getRaffleItemWinner(anyLong(), any());
        verify(submittedRaffleTicketService).findByRaffleIdOrderByTicketsSubmitted(anyLong(), any(), any());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    @Test
    void test_getItemByValuesForMobileApp_withPledgeListEmpty() {

        //setup
        setItemForPledge(item);

        setStaffItemDetailsForFundAndNeed(item);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(pledgeService.findbyItemOrderByAmountDesc(any())).thenReturn(Collections.emptyList());
        Mockito.when(pledgeService.getTotalPledgeAmount(any())).thenReturn(totalPledgeAmount);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, fundAndNeedmodule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.get(0).getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.get(0).getTotalPledgeAmount(), staffItemDetail.getTotalPledgeAmount());
        assertTrue(itemValues.get(0).getAuctionFundAndNeedItemDetail().isEmpty());

        verify(pledgeService).getTotalPledgeAmount(any());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    private void setStaffItemDetailsForFundAndNeed(Item item) {
        staffItemDetail.setItemName(item.getName());
        staffItemDetail.setModuleType(ModuleType.CAUSEAUCTION.toString());
        staffItemDetail.setMinPrice(minPrice);
        staffItemDetail.setTotalPledgeAmount(totalPledgeAmount);
        staffItemDetail.setItemCode(item.getCode());
    }

    private void setItemForPledge(Item item) {
        item.setStartingBid(minPrice);
        item.setName("First Fund a need");
        item.setModuleType(ModuleType.CAUSEAUCTION);
        item.setCode(itemCode);
    }

    @Test
    void test_getItemByValuesForMobileApp_withPledge() {

        //setup
        setItemForPledge(item);

        setStaffItemDetailsForFundAndNeed(item);

        User user = EventDataUtil.getUser();

        pledge.setUser(user);
        pledge.setAmount(100d);
        pledge.setHasPaid(true);

        List<Pledge> pledgeList = new ArrayList<>();
        pledgeList.add(pledge);

        List<AuctionFundAndNeedItemDetail> rowDetails = new ArrayList<>();
        rowDetails.add(new AuctionFundAndNeedItemDetail(pledgeList.get(0)));

        staffItemDetail.setAuctionFundAndNeedItemDetail(rowDetails);

        List<Item> listOfItems = new ArrayList<>();
        listOfItems.add(item);

        //mock
        Mockito.when(itemService.getItemByModuleTypeAndSearchString(any(), anyString(), any())).thenReturn(listOfItems);
        Mockito.when(pledgeService.findbyItemOrderByAmountDesc(any())).thenReturn(pledgeList);
        Mockito.when(pledgeService.getTotalPledgeAmount(any())).thenReturn(totalPledgeAmount);

        //Execution
        List<StaffItemDetail> itemValues = staffPageServiceImpl.getItemByValuesForMobileApp(event, search, fundAndNeedmodule, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.get(0).getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.get(0).getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.get(0).getTotalPledgeAmount(), staffItemDetail.getTotalPledgeAmount());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getBidderName(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getBidderName());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getBidAmount(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getBidAmount());
        assertEquals(itemValues.get(0).getAuctionFundAndNeedItemDetail().get(0).getStatus(),staffItemDetail.getAuctionFundAndNeedItemDetail().get(0).getStatus());

        verify(pledgeService).getTotalPledgeAmount(any());
        verify(itemService).getItemByModuleTypeAndSearchString(any(), anyString(), any());
    }

    private void getStaffItemDetails() {
		String winnerName = "winner";
		staffItemDetail.setWinnerName(winnerName);
        staffItemDetail.setItemName(itemName);
        staffItemDetail.setBidIncrement(bidIncrement);
        staffItemDetail.setBuyItNow(buyItNow);
        staffItemDetail.setCurrentBid(currentBid);
        staffItemDetail.setMinPrice(minPrice);
        staffItemDetail.setStartingBid(startingBid);
		Long totalTicketsSubmitted = 10L;
		staffItemDetail.setTotalTicketsSubmitted(totalTicketsSubmitted);
		boolean boolCurrentBid = true;
		staffItemDetail.setBoolCurrentBid(boolCurrentBid);
        staffItemDetail.setTotalPledgeAmount(totalPledgeAmount);
    }

    @Test
    void test_getItemByValues_NotHaveModuleType() {

        //setup
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        //mock
        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);

        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertTrue(itemValues.isEmpty());

        verify(itemService).findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong());
    }

    @Test
    void test_getItemByValues_withAuctionAndWinnersEmpty() {

        //setup
        setItem(0d, 1);

        setStaffItemDetailsForAuction(item);
        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode("COD");
        itemNameCodeDto.setItemName("my first item");
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();

        List<AuctionBid> nextAuctionBids = new ArrayList<>();
        nextAuctionBids.add(auctionBid);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        AuctionFundAndNeedItemDetail auctionFundAndNeedItem=new AuctionFundAndNeedItemDetail(auctionBid,1);
        auctionFundAndNeedItemDetails.add(auctionFundAndNeedItem);
        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);

        //mock




        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(), anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);

        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());

    }

    @Test
    void test_getItemByValues_withAuctionAndWinners() {

        //setup
        setItem(currentBid, 1);

        setStaffItemDetailsForAuction(item);
        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        winner=new Winner();
        winner.setBidId(1);

        List<Winner> winnersList=new ArrayList<>();
        winnersList.add(winner);

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

        List<AuctionBid> nextAuctionBids = new ArrayList<>();
        nextAuctionBids.add(auctionBid);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,0));
        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);


        //mock
        Mockito.when(auctionService.findByEvent(any())).thenReturn(auction);


        Mockito.when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION,item.getId())).thenReturn(winnersList);
        Mockito.when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);
        Mockito.when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));


        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event,search, "auction", EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.getModuleType(), staffItemDetail.getModuleType());
        assertEquals(itemValues.getCurrentBid(), staffItemDetail.getCurrentBid());
        assertEquals(itemValues.getBidIncrement(), staffItemDetail.getBidIncrement());
        assertEquals(itemValues.getBuyItNow(), staffItemDetail.getBuyItNow());
        assertEquals(itemValues.getMinPrice(), staffItemDetail.getMinPrice());
        assertEquals(itemValues.getStartingBid(), staffItemDetail.getStartingBid());
        assertTrue(itemValues.isBoolCurrentBid());
        assertEquals(itemValues.getNumberOfWinners(), staffItemDetail.getNumberOfWinners());
    }

    @Test
    void test_getItemByValues_withAuctionAndWinnersNextAuctionBidsEmpty() {

        //setup
        setItem(currentBid, 1);

        setStaffItemDetailsForAuction(item);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        itemNameCodeDto.setItemName(itemName);
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        User user = new User();
        user.setFirstName("FirstName");
        user.setLastName("LastName");

        winner=new Winner();
        winner.setBidId(1);

        List<Winner> winnersList=new ArrayList<>();
        winnersList.add(winner);
        winnersList.add(winner);
        winnersList.add(winner);

        AuctionBid auctionBid =new AuctionBid();
        auctionBid.setAmount(winnerBid);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);

        auction=new Auction();
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

        List<AuctionFundAndNeedItemDetail> auctionFundAndNeedItemDetails=new ArrayList<>();
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));
        auctionFundAndNeedItemDetails.add(new AuctionFundAndNeedItemDetail(auctionBid,item.isPurchased()));

        staffItemDetail.setAuctionFundAndNeedItemDetail(auctionFundAndNeedItemDetails);

        //mock




        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(), anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);

        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());


}

    private void setItem(Double currentBid,int numberOfWinners) {
        item.setModuleType(ModuleType.AUCTION);
        item.setName(itemName);
        item.setCurrentBid(currentBid);
        item.setStartingBid(startingBid);
        item.setBuyItNowPrice(buyItNow);
        item.setNumberOfWinners(numberOfWinners);
        item.setCode(itemCode);
    }

    private void setStaffItemDetailsForAuction(Item item) {
        staffItemDetail.setModuleType(ModuleType.AUCTION.toString());
        staffItemDetail.setCurrentBid(item.getCurrentBid());
        staffItemDetail.setBidIncrement(bidIncrement);
        staffItemDetail.setBuyItNow(item.getBuyItNowPrice());
        staffItemDetail.setStartingBid(item.getStartingBid());
        staffItemDetail.setItemName(item.getName());
        staffItemDetail.setItemCode(item.getCode());
        staffItemDetail.setBoolCurrentBid(item.getCurrentBid()>0);
        staffItemDetail.setNumberOfWinners(item.getNumberOfWinners());
    }

    @Test
    void test_getItemByValues_withRaffleItemWinnerAndNextRaffleItemDetailEmpty() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName= user.getFirstName() +" "+ user.getLastName();

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);

        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(submittedRaffleTicket));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        //mock


        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);



        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());

    }

    @Test
    void test_getItemByValues_withRaffleItemWinnerEmptyAndNextRaffleItemDetail() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName="";

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);

        List<SubmittedRaffleTicket> nextRaffleItems=new ArrayList<>();
        nextRaffleItems.add(submittedRaffleTicket);

        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(nextRaffleItems.get(0)));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        //mock

        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);



        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());

    }

    @Test
    void test_getItemByValues_withRaffleItemWinnerAndNextRaffleItemDetail() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        User user = EventDataUtil.getUser();

        String winnerName= user.getFirstName() +" "+ user.getLastName();

        setStaffItemDetailsForRaffle(item,winnerName);

        submittedRaffleTicket.setUser(user);
        submittedRaffleTicket.setTicketsSubmitted(5);
        submittedRaffleTicket.setWinner(true);

        List<SubmittedRaffleTicket> nextRaffleItems=new ArrayList<>();
        nextRaffleItems.add(submittedRaffleTicket);

        List<RaffleItemDetailDto> rowDetails = new ArrayList<>();
        rowDetails.add(new RaffleItemDetailDto(submittedRaffleTicket));
        rowDetails.add(new RaffleItemDetailDto(nextRaffleItems.get(0)));
        staffItemDetail.setRaffleItemDetail(rowDetails);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);


        //mock


        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);



        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
    }

    @Test
    void test_getItemByValues_withRaffleItemWinnerEmptyAndNextRaffleItemDetailEmpty() {

        //setup
        long totalRaffleTickets=100;
        setItemForRaffle(item);

        String winnerName="";

        setStaffItemDetailsForRaffle(item,winnerName);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        //mock

        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(),  anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);



        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());

    }

    private void setItemForRaffle(Item item) {
        item.setModuleType(ModuleType.RAFFLE);
        item.setCode(itemCode);
        item.setNumberOfWinners(1);
    }

    private void setStaffItemDetailsForRaffle(Item item, String winnerName) {
        staffItemDetail.setModuleType(ModuleType.RAFFLE.toString());
        staffItemDetail.setTotalTicketsSubmitted(100L);
        staffItemDetail.setWinnerName(winnerName);
        staffItemDetail.setItemCode(item.getCode());
        staffItemDetail.setNumberOfWinners(item.getNumberOfWinners());
    }

    @Test
    void test_getItemByValues_withPledgeListEmpty() {

        //setup
        setItemForPledge(item);

        setStaffItemDetailsForFundAndNeed(item);
        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        itemNameCodeDto.setItemName("First Fund a need");
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        //mock

        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(), anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);


        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
    }

    @Test
    void test_getItemByValues_withPledge() {

        //setup
        setItemForPledge(item);

        setStaffItemDetailsForFundAndNeed(item);

        User user = EventDataUtil.getUser();

        pledge.setUser(user);
        pledge.setAmount(100d);
        pledge.setHasPaid(true);

        List<Pledge> pledgeList = new ArrayList<>();
        pledgeList.add(pledge);

        List<AuctionFundAndNeedItemDetail> rowDetails = new ArrayList<>();
        rowDetails.add(new AuctionFundAndNeedItemDetail(pledgeList.get(0)));

        staffItemDetail.setAuctionFundAndNeedItemDetail(rowDetails);

        ItemNameCodeDto itemNameCodeDto = new ItemNameCodeDto();
        itemNameCodeDto.setItemCode(itemCode);
        itemNameCodeDto.setItemName("First Fund a need");
        List<ItemNameCodeDto> listOfItems = new ArrayList<>();
        listOfItems.add(itemNameCodeDto);

        //mock

        Mockito.when(itemService.findAllItemByModuleIdAndSearchStringOrderByPositionDesc(anyString(), anyLong(), anyLong(), anyLong())).thenReturn(listOfItems);


        //Execution
        List<ItemNameCodeDto> itemValues = staffPageServiceImpl.getItemByValues(event,search);

        //Assertion
        assertEquals(itemValues.get(0).getItemCode(), staffItemDetail.getItemCode());
        assertEquals(itemValues.get(0).getItemName(), staffItemDetail.getItemName());
    }

    @Test
    void test_getBidValue_throwErrorItemNotFound(){

        //setup
        String itemCode = "AUC";
        String module = Constants.STRING_AUCTION;

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffPageServiceImpl.getBidValue(event, itemCode, module, EnumLabelLanguageCode.EN.name()));

        //Assertion
        verify(staffPageServiceImpl).getModuleType(any());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
    }

    @Test
    void test_getBidValue_throwErrorModuleNotFound(){

        //setup
        String itemCode = "AUC";
        String module = "pledge";

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> staffPageServiceImpl.getBidValue(event, itemCode, module, EnumLabelLanguageCode.EN.name()));
        
        assertEquals(NotFoundException.ModuleNotFound.MODULE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getModuleType_withModuleTypeDonation(){

        //setup
        String module = Constants.STRING_DONATE;

        //Execution
        ModuleType moduleType = staffPageServiceImpl.getModuleType(module);

        //Assertion
        assertEquals(moduleType, ModuleType.DONATION);
    }

    @Test
    void test_getItemByValue_withAuctionAndCurrentBidGreaterThanZero() {

        //setup
        String itemCode = "AUC";
        setItem(ModuleType.AUCTION, itemName, currentBid, startingBid, false, 1000, 2, itemCode, bidIncrement);

        getStaffItemDetails();
        staffItemDetail.setModuleType(ModuleType.AUCTION.toString());

        winner = new Winner();
        List<Winner> winnerList = new ArrayList<>();
        winnerList.add(winner);
        List<StaffItemDetail> staffItemDetailList = new ArrayList<>();
        staffItemDetailList.add(staffItemDetail);

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
        when(auctionService.findByEvent(any())).thenReturn(auction);
        when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event, itemCode, Constants.STRING_AUCTION, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemName(), item.getName());
        assertEquals(0, Double.compare(itemValues.getCurrentBid().intValue(), item.getCurrentBid()));
        assertEquals(itemValues.getBidIncrement(), bidIncrement);
        assertEquals(0, Double.compare(itemValues.getBuyItNow().intValue(), item.getBuyItNowPrice()));
        assertEquals(0, Double.compare(itemValues.getStartingBid().intValue(), item.getStartingBid()));
        assertTrue(itemValues.isBoolCurrentBid());

        verify(staffPageServiceImpl).getModuleType(anyString());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
        verify(auctionService).findByEvent(any());
        verify(itemService).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());

    }

    private void setItem(ModuleType moduleType, String itemName, Double currentBid, Double startingBid, Boolean liveAuctionItem, Integer buyItNowPrice, Integer numberOfWinners, String code, Double bidIncrement){
        item.setModuleType(moduleType);
        item.setName(itemName);
        item.setCurrentBid(currentBid);
        item.setStartingBid(startingBid);
        item.setLiveAuctionItem(liveAuctionItem);
        item.setBuyItNowPrice(buyItNowPrice);
        item.setNumberOfWinners(numberOfWinners);
        item.setCode(code);
        item.setBidIncrement(bidIncrement);
    }
    @Test
    void test_getItemByValue_withAuctionAndItemIsPurchasedTrue() {

        //setup
        String itemCode = "AUC";
        setItem(ModuleType.AUCTION, itemName, 1000d, startingBid, false, 1000, 1, itemCode, bidIncrement);

        getStaffItemDetails();
        staffItemDetail.setModuleType(ModuleType.AUCTION.toString());

        winner = new Winner();
        List<Winner> winnerList = new ArrayList<>();
        winnerList.add(winner);
        List<StaffItemDetail> staffItemDetailList = new ArrayList<>();
        staffItemDetailList.add(staffItemDetail);

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
        when(auctionService.findByEvent(any())).thenReturn(auction);
        when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event, itemCode, Constants.STRING_AUCTION, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemName(), item.getName());
        assertEquals(0, Double.compare(itemValues.getCurrentBid().intValue(), item.getCurrentBid()));
        assertEquals(itemValues.getBidIncrement(), bidIncrement);
        assertEquals(0, Double.compare(itemValues.getBuyItNow().intValue(), item.getBuyItNowPrice()));
        assertEquals(0, Double.compare(itemValues.getStartingBid().intValue(), item.getStartingBid()));
        assertTrue(itemValues.isBoolCurrentBid());

        verify(staffPageServiceImpl).getModuleType(anyString());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
        verify(auctionService).findByEvent(any());
        verify(itemService).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());

    }

    @Test
    void test_getItemByValue_withAuctionAndCurrentBidZero() {

        //setup
        String itemCode = "AUC";
        setItem(ModuleType.AUCTION, itemName, 0d, startingBid, false, 1000, 1, itemCode, bidIncrement);

        getStaffItemDetails();
        staffItemDetail.setModuleType(ModuleType.AUCTION.toString());

        winner = new Winner();
        List<Winner> winnerList = new ArrayList<>();
        winnerList.add(winner);
        List<StaffItemDetail> staffItemDetailList = new ArrayList<>();
        staffItemDetailList.add(staffItemDetail);

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
        when(auctionService.findByEvent(any())).thenReturn(auction);
        when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event, itemCode, Constants.STRING_AUCTION, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemName(), item.getName());
        assertEquals(0, Double.compare(itemValues.getCurrentBid().intValue(), item.getCurrentBid()));
        assertEquals(itemValues.getBidIncrement(), bidIncrement);
        assertEquals(0, Double.compare(itemValues.getBuyItNow(), item.getBuyItNowPrice()));
        assertEquals(0, Double.compare(itemValues.getStartingBid(), item.getStartingBid()));
        assertFalse(itemValues.isBoolCurrentBid());

        verify(staffPageServiceImpl).getModuleType(anyString());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
        verify(auctionService).findByEvent(any());
        verify(itemService).getBidIncrement(any(), any());
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
    }

    @Test
    void test_getItemByValue_withRaffle(){

        //setup
        String itemCode = "RAF";
        long totalRaffleTickets = 20L;
        item.setModuleType(ModuleType.RAFFLE);
        item.setName(itemName);
        item.setCode(itemCode);

        getStaffItemDetails();
        staffItemDetail.setModuleType(ModuleType.RAFFLE.toString());

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
        when(submittedRaffleTicketService
                .getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any())).thenReturn(totalRaffleTickets);

        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event, itemCode, Constants.STRING_RAFFLE, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemName(), staffItemDetail.getItemName());
        assertEquals(itemValues.getTotalTicketsSubmitted().longValue(), totalRaffleTickets);

        verify(staffPageServiceImpl).getModuleType(anyString());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
        verify(submittedRaffleTicketService).getRaffleTotalTicketsByRaffleIdAndItem(anyLong(), any());
    }

    @Test
    void test_getItemByValue_withCauseAuction(){

        //setup
        String itemCode = "CAU";
        item.setModuleType(ModuleType.CAUSEAUCTION);
        item.setName(itemName);
        item.setCode(itemCode);
        item.setStartingBid(startingBid);

        getStaffItemDetails();
        staffItemDetail.setModuleType(ModuleType.CAUSEAUCTION.toString());

        //mock
        when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));

        //Execution
        StaffItemDetail itemValues = staffPageServiceImpl.getBidValue(event, itemCode, Constants.STRING_FUNDANEED, EnumLabelLanguageCode.EN.name());

        //Assertion
        assertEquals(itemValues.getItemName(), staffItemDetail.getItemName());
        assertEquals(0, Double.compare(itemValues.getMinPrice().intValue(), item.getStartingBid()));

        verify(staffPageServiceImpl).getModuleType(anyString());
        verify(itemService).getItemByModuleType(any(), anyString(), any());
    }

    @Test
    void test_getUserDetailForCheckOut_withRequireUserAddressFalseAndModuleRaffle() {

        //setup
        String emailOrCell = "<EMAIL>";
        String module = Constants.STRING_RAFFLE;
        String countryCode ="US";
        Boolean cardRequired = true;
        long submittedTickets = 10;
        long purchasedTickets = 15;
        long remainingTickts = purchasedTickets - submittedTickets;
        User user = EventDataUtil.getUser();
        user.setAddress1("");
        user.setPhoneNumber(9898989898L);
        user.setEmail(emailOrCell);
        user.setCountryCode(CountryCode.US);
        StripeCreditCardDto stripeCreditCardDto = new StripeCreditCardDto();
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setCardType("VISA");
        stripeCreditCardDto.setLast4("1111");
        raffle.setRequireUserAddress(false);
        //mock
        Mockito.when(userService.getUserByEmailOrCellPhone(anyString(), any(), any())).thenReturn(user);
        Mockito.when(eventService.getLinkedCreditCard(any(), any())).thenReturn(Collections.singletonList(stripeCreditCardDto));
        Mockito.when(userService.isCCRequired( any(),  any(),  any(),  anyBoolean())).thenReturn(cardRequired);
        Mockito.when(submittedRaffleTicketService.getTotalSubmittedTicketsByUserForRaffle(any(), anyLong())).thenReturn(submittedTickets);
        Mockito.when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(any(), anyLong())).thenReturn(purchasedTickets);
        when(raffleService.findByEvent(event)).thenReturn(raffle);
        //Execution
        StaffUserDetail staffUserDetailData = staffPageServiceImpl.getUserDetailForCheckOut(emailOrCell, Optional.of(countryCode), module, event);

        //Assertion
        assertEquals(staffUserDetailData.getEmail(), user.getEmail());
        assertEquals(staffUserDetailData.getPhonenumber(), user.getPhoneNumber());
        assertEquals(staffUserDetailData.getCountryCode(), user.getCountryCode());
        assertEquals(staffUserDetailData.getFirstName(), user.getFirstName());
        assertEquals(staffUserDetailData.getLastName(), user.getLastName());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).getCardType(), stripeCreditCardDto.getCardType());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).isDefaultCard(), stripeCreditCardDto.isDefaultCard());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).getLast4(), stripeCreditCardDto.getLast4());
        assertEquals(staffUserDetailData.getCardRequired(), cardRequired);
//        assertTrue(staffUserDetailData.isAddressRequired());
        assertEquals(staffUserDetailData.getAvailableTickets().longValue(), remainingTickts);

        verify(userService).getUserByEmailOrCellPhone(anyString(), any(), any());
        verify(staffPageServiceImpl).getModuleType(module);
        verify(eventService).getLinkedCreditCard(any(), any());
        verify(userService).isCCRequired( any(),  any(),  any(),  anyBoolean());
        verify(submittedRaffleTicketService).getTotalSubmittedTicketsByUserForRaffle(any(), anyLong());
        verify(purchasedRaffleTicketService).getTotalTicketsByUserAndRaffle(any(), anyLong());
    }

    @Test
    void test_getUserDetailForCheckOut_withRequireUserAddressTrueAndModuleNotRaffle() {

        //setup
        String emailOrCell = "<EMAIL>";
        String module = STRING_FUNDANEED;
        String countryCode ="US";
        Boolean cardRequired = true;
        User user = EventDataUtil.getUser();
        user.setPhoneNumber(9898989898L);
        user.setEmail(emailOrCell);
        user.setCountryCode(CountryCode.US);
        StripeCreditCardDto stripeCreditCardDto = new StripeCreditCardDto();
        stripeCreditCardDto.setDefaultCard(true);
        stripeCreditCardDto.setCardType("VISA");
        stripeCreditCardDto.setLast4("1111");
        event = EventDataUtil.getEvent();
        causeAuction.setRequireUserAddress(false);
        //mock
        Mockito.when(userService.getUserByEmailOrCellPhone(anyString(), any(), any())).thenReturn(user);
        Mockito.when(eventService.getLinkedCreditCard(any(), any())).thenReturn(Collections.singletonList(stripeCreditCardDto));
        Mockito.when(userService.isCCRequired( any(),  any(),  any(),  anyBoolean())).thenReturn(cardRequired);
        when(causeAuctionService.findByEvent(event)).thenReturn(causeAuction);
        //Execution
        StaffUserDetail staffUserDetailData = staffPageServiceImpl.getUserDetailForCheckOut(emailOrCell, Optional.of(countryCode), module, event);

        //Assertion
        assertEquals(staffUserDetailData.getEmail(), user.getEmail());
        assertEquals(staffUserDetailData.getPhonenumber(), user.getPhoneNumber());
        assertEquals(staffUserDetailData.getCountryCode(), user.getCountryCode());
        assertEquals(staffUserDetailData.getFirstName(), user.getFirstName());
        assertEquals(staffUserDetailData.getLastName(), user.getLastName());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).getCardType(), stripeCreditCardDto.getCardType());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).isDefaultCard(), stripeCreditCardDto.isDefaultCard());
        assertEquals(staffUserDetailData.getCards().getStripeCards().get(0).getLast4(), stripeCreditCardDto.getLast4());
        assertEquals(staffUserDetailData.getCardRequired(), cardRequired);
        assertFalse(staffUserDetailData.isAddressRequired());

        verify(userService).getUserByEmailOrCellPhone(anyString(), any(), any());
        verify(staffPageServiceImpl).getModuleType(module);
        verify(eventService).getLinkedCreditCard(any(), any());
        verify(userService).isCCRequired( any(),  any(),  any(),  anyBoolean());
    }

    @Test
    void testGetUserDetailForCheckOut() {

        //setup

        //mock

        //Execution

        //Assertion

    }

    @Test
    void testGetUserDetailForCheckOut1() {

        //setup

        //mock

        //Execution

        //Assertion

    }

    @Test
    void setPurchaserInTicketingOrder() {

        //setup

        //mock

        //Execution

        //Assertion

    }
}