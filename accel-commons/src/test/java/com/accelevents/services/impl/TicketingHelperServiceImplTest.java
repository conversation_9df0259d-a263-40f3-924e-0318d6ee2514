package com.accelevents.services.impl;

import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.domain.*;
import com.accelevents.dto.*;
import com.accelevents.messages.TicketType;
import com.accelevents.registration.approval.services.RegistrationAttributeService;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.PurchaserInfo;
import com.accelevents.ticketing.dto.TicketingEmailDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.FeeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingHelperServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(TicketingOrderDetailsServiceImpl.class);

    @Spy
    @InjectMocks
    private TicketingHelperServiceImpl ticketingHelperServiceImpl = new TicketingHelperServiceImpl();

    @Mock
    private TicketHolderAttributesService ticketHolderAttributesService;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private AllRequiresAttributesService allRequiresAttributesService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private TicketingService ticketingService;
    @Mock
    private EventService eventService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;
    @Mock
    private CustomEmailService customEmailService;
    @Mock
    private SalesTaxService salesTaxService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private VatTaxService vatTaxService;
    @Mock
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Mock
    private RegistrationAttributeService registrationAttributeService;

    @Mock
    TransactionFeeConditionalLogicService TransactionFeeConditionalLogicService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    private Event event;
    private User user;
    private Ticketing ticketing;
    private EventTickets eventTickets;
    private TicketingOrder ticketingOrder;
    private TicketHolderAttributes ticketHolderAttributes;
    private TicketAttributeValueDto ticketAttributeValueDto1;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes1;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes2;
    private TicketingEmailDto ticketingEmailDto;
    private CustomEmail customEmail;
    private TicketingOrderManager ticketingOrderManager;
    private TicketingCoupon ticketingCoupon;
    private TicketingType ticketingType;
    private StripeDTO stripeDTO;
    private EventPlanConfig eventPlanConfig;

    private Long id = 1L;
    private String email = "<EMAIL>";
    private String jSonValue = EventDataUtil.getJsonValue();
    private int numberOfTicketType = 1;
    private int totalTickets = 10;
    private double totalTicketsPrice = 100.0;
    private SalesTaxFeeDto salesTaxFeeDto;


    @BeforeEach
    void setUp() throws Exception {

        MockitoAnnotations.openMocks(this);

        event = EventDataUtil.getEvent();
        eventTickets = EventDataUtil.getEventTickets();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        user = EventDataUtil.getUser();
        ticketing = EventDataUtil.getTicketing(event);
        ticketAttributeValueDto1 =  getTicketAttributeValueDTO1();
        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);
        ticketingOrderManager = EventDataUtil.getTicketingOrderManager();
        ticketingType = EventDataUtil.getTicketingType(event);
        ticketingCoupon = EventDataUtil.getTicketingCoupon();
        eventPlanConfig = EventDataUtil.getEventPlanConfig();

        salesTaxFeeDto = new SalesTaxFeeDto(true,10d,"1");

    }

   /* @Test
    void test_handleJSONValue_success() {

        //mock
        when(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto1)).thenReturn(jSonValue);

        //Execution
        ticketingHelperServiceImpl.handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto1);

        ArgumentCaptor<TicketHolderAttributes> attributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderAttributes.class);
        verify(ticketHolderAttributesService,times(1)).save(attributesArgumentCaptor.capture());

        TicketHolderAttributes ticketHolderAttributes = attributesArgumentCaptor.getValue();
        assertEquals(jSonValue, ticketHolderAttributes.getJsonValue());
    }*/

   /* @Test
    void test_handleJSONValue_throwException() {

        //mock
        when(ticketHolderAttributesService.parseToJsonString(ticketAttributeValueDto1)).thenThrow(Exception.class);

        //Execution
        try{
            ticketingHelperServiceImpl.handleJSONValue(ticketHolderAttributes, ticketAttributeValueDto1);
        }catch (Exception e){
            log.error("Error while converting to json : "+ ticketHolderAttributes.getId());
        }
    }*/

    @Test
    void test_getUnmashler_success() throws JAXBException {

        //Execution
        Unmarshaller unmarshaller = ticketingHelperServiceImpl.getUnmashler();
        assertNotNull(unmarshaller);
    }

    @Test
    void test_findTicketingByEventAndIfNotFoundCreateNew_success_with_ticketing() {

        //mock
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        Ticketing actualData = ticketingHelperServiceImpl.findTicketingByEventAndIfNotFoundCreateNew(event);
        assertEquals(ticketing.getEventid(), actualData.getEventid());
        assertTrue(actualData.getActivated());
        assertEquals(actualData.getChartKey(), ticketing.getChartKey());
        assertEquals(actualData.getCheckoutminutes(), ticketing.getCheckoutminutes());
    }

    @Test
    void test_findTicketingByEventAndIfNotFoundCreateNew_success() {

        //mock
        when(ticketingRepository.findByEventid(event)).thenReturn(null);
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(eventPlanConfig);

        doReturn(ticketing).when(ticketingService).save(any());
        //Execution
        Ticketing actualData = ticketingHelperServiceImpl.findTicketingByEventAndIfNotFoundCreateNew(event);
        assertEquals(ticketing.getEventid(), actualData.getEventid());
        assertFalse(actualData.getActivated());
        assertTrue(actualData.getCollectTicketHolderAttributes());
        assertFalse(actualData.getSocialSharing());
        assertTrue(actualData.isAllowEditingOfDisclaimer());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(Constants.TICKETING_DEFAULT_TIMEOUT, ticketingData.getCheckoutminutes().intValue());//NOSONAR
    }

    @Test
    void test_createNewTiketing_success() {

        //Execution
        Ticketing actualData = ticketingHelperServiceImpl.findTicketingByEventAndIfNotFoundCreateNew(event);
        assertEquals(ticketing.getEventid(), actualData.getEventid());
        assertFalse(actualData.getActivated());
        assertTrue(actualData.getCollectTicketHolderAttributes());
        assertFalse(actualData.getSocialSharing());
        assertTrue(actualData.isAllowEditingOfDisclaimer());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(Constants.TICKETING_DEFAULT_TIMEOUT, ticketingData.getCheckoutminutes().intValue());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
        Event eventData = eventArgumentCaptor.getValue();
        assertEquals(0L, eventData.getTicketingId().longValue());
    }

    @Test
    void test_createNewTiketing_success_with_ticketingId() {

        //setup
        event.setTicketingId(id);

        //Execution
        Ticketing actualData = ticketingHelperServiceImpl.findTicketingByEventAndIfNotFoundCreateNew(event);
        assertFalse(actualData.getSocialSharing());

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(Constants.TICKETING_DEFAULT_TIMEOUT, ticketingData.getCheckoutminutes().intValue());
        assertTrue(ticketingData.getCollectTicketHolderAttributes());
        assertFalse(ticketingData.getSocialSharing());
        assertTrue(ticketingData.isAllowEditingOfDisclaimer());
    }

    //TODO: Junit5 review test
    /*@Test
    void test_addDefaultAttributes_success() {

        //setup
        int attributeOrder = 1000;
        int orderValue = 0;
        int i = 0;
        String[] names = {Constants.PREFIX,Constants.FIRST_NAME,Constants.LAST_NAME,Constants.EMAIL,Constants.PROOF_OF_VACCINATION,
                Constants.CELL_PHONE,Constants.COUNTRY,Constants.STATE,Constants.BILLING_ADDRESS,Constants.SHIPPING_ADDRESS,Constants.GENDER,
                Constants.BIRTHDAY,Constants.AGE,Constants.ORGANIZATION,Constants.JOB_TITLE,Constants.IMAGE,Constants.UPLOAD,Constants.PRONOUNS,Constants.ABOUT_ME,Constants.FACEBOOK,Constants.INSTAGRAM,Constants.LINKED_IN,Constants.TWITTER, Constants.INTEREST_NAME};

        //Execution
        List<TicketHolderRequiredAttributes> data = ticketingHelperServiceImpl.addDefaultAttributes(event);
        for (TicketHolderRequiredAttributes data1 : data) {
            assertNotNull(data1.getName());
            assertNotNull(data1.getBuyerAttributeOrder()); //NOSONAR
        }

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(allRequiresAttributesService, times(24)).save(ticketHolderRequiredAttributesArgumentCaptor.capture());

        List<TicketHolderRequiredAttributes> actualData = ticketHolderRequiredAttributesArgumentCaptor.getAllValues();

        for (TicketHolderRequiredAttributes data1 : actualData) {
            orderValue = attributeOrder + orderValue;
            assertEquals(data1.getBuyerAttributeOrder(), orderValue);
            assertEquals(data1.getName(), names[i]);
            i++;
        }
    }*/

    @Test
    void test_getPurchaserInfo_success_with_eventTickets_empty() {

        //setup
        List<EventTickets> eventTicketList = new ArrayList<>();

        //mock


        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event);
        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
    }

    @Test
    void test_getPurchaserInfo_success_with_eventTickets() throws JAXBException{

        //setup
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes1.setAttribute(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = getTicketHolderRequiredAttributes();

        //mock




        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event);
        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_findTicketingByEvent_success() {

        //mock
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);

        //Execution
        Ticketing ticketData = ticketingHelperServiceImpl.findTicketingByEvent(event);
        assertEquals(ticketing.getEventid(), ticketData.getEventid());
    }

    @Test
    void test_getPurchaserInfo1_success() throws JAXBException{

        //setup
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes1.setAttribute(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = getTicketHolderRequiredAttributes();

        //mock




        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_success_with_ticketHolderRequiredAttribute_cellPhone() throws JAXBException{

        //setup
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);


        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_CELL_SPACE_PHONE);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = new ArrayList<>();
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes1);

        //mock




        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_success_with_ticketHolderRequiredAttribute_email() throws JAXBException{

        //setup
        User user = new User();
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);


        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes2.setAttribute(true);
        ticketHolderRequiredAttributes2.setName(Constants.STRING_LAST_SPACE_NAME);
        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_EMAIL_SPACE);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = new ArrayList<>();
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes1);
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes2);

        //mock




        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_success_with_ticketHolderRequiredAttribute_lastName() throws JAXBException{

        //setup
        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes2.setAttribute(true);
        ticketHolderRequiredAttributes2.setName(Constants.STRING_FIRST_SPACE_NAME);
        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_EMAIL_SPACE);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = new ArrayList<>();
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes1);
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes2);

        //mock




        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_success_with_enabledForTicketPurchaser_false() throws JAXBException{

        //setup
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        TicketHolderRequiredAttributes ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(false);
        ticketHolderRequiredAttributes1.setAttribute(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List =new ArrayList<>();
        ticketHolderRequiredAttributes1List.add(ticketHolderRequiredAttributes1);

        //mock


        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_success_with_eventTickets_null() throws JAXBException{

        //setup
        user.setEmail(email);

        ticketingOrder.setPurchaser(user);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = getTicketHolderRequiredAttributes();

        //Execution
        PurchaserInfo getPurchaserInfoData = ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,null);

        assertEquals(ticketingOrder.getPurchaser().getFirstName(), getPurchaserInfoData.getFirstName());
        assertEquals(ticketingOrder.getPurchaser().getLastName(), getPurchaserInfoData.getLastName());
        assertEquals(ticketingOrder.getPurchaser().getEmail(), getPurchaserInfoData.getEmail());
    }

    @Test
    void test_getPurchaserInfo1_throwException() throws JAXBException{

        //setup
        eventTickets.setTicketHolderAttributesId(ticketHolderAttributes);

        List<EventTickets> eventTicketList = new ArrayList<>();
        eventTicketList.add(eventTickets);

        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);
        ticketHolderRequiredAttributes1.setAttribute(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributes1List = getTicketHolderRequiredAttributes();

        //mock




        //Execution
        try {
            ticketingHelperServiceImpl.getPurchaserInfo(ticketingOrder, event,ticketHolderAttributes,ticketHolderRequiredAttributes1List,eventTickets);
        } catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }

    @Test
    void test_getTicketOrderHeaderText_success_with_customEmail() {

        //setup
        String ticketOrderHeaderText = "Order Confirmation for Event";

        CustomEmail customEmail = new CustomEmail();
        customEmail.setEventTicketOrderConfirmationHeader(ticketOrderHeaderText);

        //mock
        when(customEmailService.getCustomEmailByEventId(anyLong())).thenReturn(Optional.of(customEmail));

        //Execution
        String actualData = ticketingHelperServiceImpl.getTicketOrderHeaderText(event);
        assertEquals(ticketOrderHeaderText, actualData);
    }

    @Test
    void test_getTicketOrderHeaderText_success_with_customEmail_empty() {

        //mock
        when(customEmailService.getCustomEmailByEventId(event.getEventId())).thenReturn(Optional.empty());

        //Execution
        ticketingHelperServiceImpl.getTicketOrderHeaderText(event);
    }

    @Test
    void test_saveCustomHeaderForEvent_success_with_headerText_blank() {

        //setup
        String ticketOrderHeaderText = "";

        //Execution
        ticketingHelperServiceImpl.saveCustomHeaderForEvent(ticketOrderHeaderText, event);
    }

    @Test
    void test_saveTicketEmailAttributes_success() {

        //setup
        String headerText = "Hi user, you're ready to go!";

        TicketingEmailDto ticketingEmailDto = new TicketingEmailDto();
        ticketingEmailDto.setEmailHeader(headerText);

        CustomEmail customEmail = new CustomEmail();
        customEmail.setEventId(event.getEventId());

        //mock
        when(customEmailService.getCustomEmailByEventId(anyLong())).thenReturn(Optional.of(customEmail));

        //Execution
        ticketingHelperServiceImpl.saveTicketEmailAttributes(ticketingEmailDto,event);

        ArgumentCaptor<CustomEmail> customEmailArgumentCaptor = ArgumentCaptor.forClass(CustomEmail.class);
        verify(customEmailService, times(1)).save(customEmailArgumentCaptor.capture());

        CustomEmail actualData = customEmailArgumentCaptor.getValue();
        assertEquals(headerText, actualData.getEventTicketOrderConfirmationHeader());
    }

    @Test
    void test_getTicketPriceDetails_success_with_discountType_PERCENTAGE_lessThan100() {

        //setup
        long numberOfUsageLeft = 1L;
        boolean isCardPayment = true;
        double finalDiscount = (ticketingType.getPrice() * ticketingCoupon.getAmount()) / 100;

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);
        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.iterator().next().getPrice());
        assertFalse(ticketPriceDetailsData.listIterator(0).next().isDiscounted());
        assertNotEquals(finalDiscount, ticketPriceDetailsData.listIterator(0).next().getDiscountedAmount(), 0.0);
        assertTrue(ticketPriceDetailsData.listIterator(1).next().isDiscounted());
        assertEquals(finalDiscount, ticketPriceDetailsData.listIterator(1).next().getDiscountedAmount());
    }

    @Test
    void test_getTicketPriceDetails_success_with_ticketingCoupon_null() {

        //setup
        long numberOfUsageLeft = 1L;
        boolean isCardPayment = true;
        double finalDiscount = (ticketingType.getPrice() * ticketingCoupon.getAmount()) / 100;
        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, null, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);

        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.iterator().next().getPrice());
        assertFalse(ticketPriceDetailsData.listIterator(0).next().isDiscounted());
        assertNotEquals(finalDiscount, ticketPriceDetailsData.listIterator(0).next().getDiscountedAmount(), 0.0);
    }

    @Test
    void test_getTicketPriceDetails_success_with_numberOfUsageLeft_zero() {

        //setup
        long numberOfUsageLeft = 0L;
        boolean isCardPayment = true;
        double finalDiscount = (ticketingType.getPrice() * ticketingCoupon.getAmount()) / 100;

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice,0);

        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.iterator().next().getPrice());
        assertFalse(ticketPriceDetailsData.listIterator(0).next().isDiscounted());
        assertNotEquals(finalDiscount, ticketPriceDetailsData.listIterator(0).next().getDiscountedAmount(), 0.0);
    }

    @Test
    void test_getTicketPriceDetails_success_with_discountType_PERCENTAGE_equal100() {

        //setup
        long numberOfUsageLeft = 2L;
        boolean isCardPayment = true;

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingCoupon.setAmount(100d);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);
        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertTrue(ticketPriceDetailsData.iterator().next().isDiscounted());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.iterator().next().getDiscountedAmount());
    }

    @Test
    void test_getTicketPriceDetails_success_with_discountType_PERCENTAGE_equal100_and_isCardPayment_false() {

        //setup
        long numberOfUsageLeft = 1L;
        boolean isCardPayment = false;


        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingCoupon.setAmount(100d);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);
        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertFalse(ticketPriceDetailsData.listIterator(0).next().isDiscounted());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.listIterator(0).next().getPrice());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.listIterator(0).next().getPriceWithFee());
        assertTrue(ticketPriceDetailsData.listIterator(1).next().isDiscounted());
        assertEquals(ticketingType.getPrice(), ticketPriceDetailsData.listIterator(1).next().getDiscountedAmount());
    }

    @Test
    void test_getTicketPriceDetails_success_with_ticketType_Donation_and_cardPayment_false() {

        //setup
        long numberOfUsageLeft = 2L;
        boolean isCardPayment = false;
        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingCoupon.setAmount(100d);

        ticketingType.setTicketType(TicketType.DONATION);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);
        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertEquals(ticketingOrderManager.getDonationTicketAmount(), ticketPriceDetailsData.iterator().next().getPrice());
        assertEquals(ticketingOrderManager.getDonationTicketAmount(), ticketPriceDetailsData.iterator().next().getPriceWithFee());
    }

    @Test
    void test_getTicketPriceDetails_success_with_ticketType_Donation_and_cardPayment_true() {

        //setup
        long numberOfUsageLeft = 2L;
        boolean isCardPayment = true;

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);
        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingCoupon.setAmount(100d);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);

        ticketingType.setTicketType(TicketType.DONATION);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);
        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertEquals(ticketingOrderManager.getDonationTicketAmount(), ticketPriceDetailsData.iterator().next().getPrice());
    }


    @Test
    void test_getTicketPriceDetails_success_with_ticketingOrdertype_COMPLEMENTRY() {

        //setup
        long numberOfUsageLeft = 3L;
        boolean isCardPayment = true;

        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);
        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingOrder.setOrderType(TicketingOrder.OrderType.COMPLIMENTARY);

        //Execution
        List<TicketPriceDetails> ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(numberOfUsageLeft, ticketingOrderManager, isCardPayment, ticketingCoupon, ticketingType, numberOfTicketType, totalTickets,ticketingOrder,stripeDTO, false, false, salesTaxFeeDto, totalTicketsPrice, 0);

        assertEquals(ticketingOrderManager.getNumberofticket() , ticketPriceDetailsData.size());
        assertFalse(ticketPriceDetailsData.iterator().next().isDiscounted());
    }

    @Test
    void test_getTicketPriceDetails_success() {

        //setup
        int totalPurchasedTicket = 10;
        double ticketPrice = 0d;
        double capAmount = 0d;
        double vatTaxRate = 0d;

        ticketingType.setPassfeetobuyer(true);


        stripeDTO = new StripeDTO();
        stripeDTO.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
        stripeDTO.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);

        ticketingOrderManager.setNumberofticket(2);
        ticketingOrderManager.setDonationTicketAmount(100);

        List<TicketingOrderManager> ticketingOrderManagerList = new ArrayList<>();
        ticketingOrderManagerList.add(ticketingOrderManager);

        ticketingCoupon.setAmount(100d);

        //Execution
        TicketPriceDetails ticketPriceDetailsData =  ticketingHelperServiceImpl.getTicketPriceDetails(ticketingType.isPayLater(),totalPurchasedTicket, ticketingType, numberOfTicketType, ticketPrice, stripeDTO, salesTaxFeeDto,vatTaxRate,false,capAmount);

        assertNotNull(ticketPriceDetailsData);
    }

    private TicketAttributeValueDto getTicketAttributeValueDTO1() {
        AttributeKeyValueDto holderAttributeKeyValueDtoAttribute = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoAttribute.setKey(Constants.FIRST_NAME);
        String firstName = "Jon";
        holderAttributeKeyValueDtoAttribute.setValue(firstName);

        AttributeKeyValueDto holderAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        holderAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        String lastName = "Kaz";
        holderAttributeKeyValueDtoQuestion.setValue(lastName);

        AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute1 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute1.setKey(Constants.FIRST_NAME);
        purchaserAttributeKeyValueDtoAttribute1.setValue(firstName);

        AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute2 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute2.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoAttribute2.setValue(lastName);

        AttributeKeyValueDto purchaserAttributeKeyValueDtoAttribute3 = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoAttribute3.setKey(Constants.EMAIL);
        purchaserAttributeKeyValueDtoAttribute3.setValue(email);

        AttributeKeyValueDto purchaserAttributeKeyValueDtoQuestion = new AttributeKeyValueDto();
        purchaserAttributeKeyValueDtoQuestion.setKey(Constants.LAST_NAME);
        purchaserAttributeKeyValueDtoQuestion.setValue(lastName);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderQuestion = new ArrayList<>();
        attributeKeyValueDtosHolderQuestion.add(holderAttributeKeyValueDtoQuestion);

        List<AttributeKeyValueDto> attributeKeyValueDtosHolderAttribute = new ArrayList<>();
        attributeKeyValueDtosHolderAttribute.add(holderAttributeKeyValueDtoAttribute);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserAttribute = new ArrayList<>();
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute1);
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute2);
        attributeKeyValueDtosPurchaserAttribute.add(purchaserAttributeKeyValueDtoAttribute3);

        List<AttributeKeyValueDto> attributeKeyValueDtosPurchaserQuestion = new ArrayList<>();
        attributeKeyValueDtosPurchaserQuestion.add(purchaserAttributeKeyValueDtoQuestion);

        ValueDto valueDtoHolder = new ValueDto();
        valueDtoHolder.setAttributes(attributeKeyValueDtosHolderAttribute);
        valueDtoHolder.setQuestions(attributeKeyValueDtosHolderQuestion);

        ValueDto valueDtoPurchaser = new ValueDto();
        valueDtoPurchaser.setAttributes(attributeKeyValueDtosPurchaserAttribute);
        valueDtoPurchaser.setQuestions(attributeKeyValueDtosPurchaserQuestion);

        ticketAttributeValueDto1 = new TicketAttributeValueDto();
        ticketAttributeValueDto1.setHolder(valueDtoHolder);
        ticketAttributeValueDto1.setPurchaser(valueDtoPurchaser);

        return ticketAttributeValueDto1;
    }

    private List<TicketHolderRequiredAttributes> getTicketHolderRequiredAttributes() {
        ticketHolderRequiredAttributes1 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes1.setAttribute(true);
        ticketHolderRequiredAttributes1.setName(Constants.STRING_FIRST_SPACE_NAME);
        ticketHolderRequiredAttributes1.setEnabledForTicketPurchaser(true);

        ticketHolderRequiredAttributes2 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes2.setAttribute(true);
        ticketHolderRequiredAttributes2.setName(Constants.STRING_LAST_SPACE_NAME);
        ticketHolderRequiredAttributes2.setEnabledForTicketPurchaser(true);

        TicketHolderRequiredAttributes ticketHolderRequiredAttributes3 = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes3.setAttribute(true);
        ticketHolderRequiredAttributes3.setName(Constants.STRING_EMAIL_SPACE);
        ticketHolderRequiredAttributes3.setEnabledForTicketPurchaser(true);

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesList = new ArrayList<>();
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes1);
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes2);
        ticketHolderRequiredAttributesList.add(ticketHolderRequiredAttributes3);
        return ticketHolderRequiredAttributesList;
    }

    @Test
    void test_findByEventId_success() {

        //mock
        when(ticketingRepository.findByEventId(event.getEventId())).thenReturn(ticketing);

        //Execution
        Ticketing ticketingData = ticketingHelperServiceImpl.findByEventId(event.getEventId());

        assertEquals(ticketingData.getId(), ticketing.getId());
        assertEquals(ticketingData.getActivated(), ticketing.getActivated());
        assertEquals(ticketingData.getEventid(), ticketing.getEventid());
        assertEquals(ticketingData.getEventStartDate(), ticketing.getEventStartDate());
        assertEquals(ticketingData.getEventEndDate(), ticketing.getEventEndDate());
    }
}