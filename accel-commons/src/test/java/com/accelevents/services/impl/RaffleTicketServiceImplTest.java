package com.accelevents.services.impl;

import com.accelevents.domain.PurchasedRaffleTicket;
import com.accelevents.domain.Raffle;
import com.accelevents.domain.RaffleTicket;
import com.accelevents.domain.User;
import com.accelevents.repositories.RaffleTicketRepository;
import com.accelevents.services.PurchasedRaffleTicketService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RaffleTicketServiceImplTest {

	@Spy
	@InjectMocks
	private RaffleTicketServiceImpl raffleTicketServiceImpl;
	
	@Mock
	private RaffleTicketRepository raffleTicketRepository;
	
	@Mock
	private PurchasedRaffleTicketService purchasedRaffleTicketService;
	
	@Test
	void testSave(){
		RaffleTicket ticket = new RaffleTicket();
		raffleTicketServiceImpl.save(ticket);
		verify(raffleTicketRepository).save(ticket);
	}
	
	@Test
	void testDelete(){
		RaffleTicket ticket = new RaffleTicket();
		raffleTicketServiceImpl.delete(ticket);
		verify(raffleTicketRepository).delete(ticket);
	}
	
	@Test
	void testGetAllTicketsByRaffleId(){
		long raffleId = 1L;
		

		Set<RaffleTicket> tickets = new HashSet<>(2);
		when(raffleTicketRepository.findAllByRaffleId(raffleId)).thenReturn(tickets );
		
		Set<RaffleTicket> ticketsByRaffleId = raffleTicketServiceImpl.getAllTicketsByRaffleId(raffleId );
		
		assertEquals(tickets, ticketsByRaffleId);
	}
	
//	@Test
//	void testFindById(){
//		long id = 1L;
//		RaffleTicket ticket = new RaffleTicket();
//		when(raffleTicketRepository.getAuctionBidByIdAndNotRefunded(id)).thenReturn(Optional.of(ticket));
//		
//		Optional<RaffleTicket> raffleTicket = raffleTicketServiceImpl.getAuctionBidByIdAndNotRefunded(id);
//		
//		assertEquals(ticket, raffleTicket);
//	}
	
	@Test
	void testAddDefaultTicketPurchase(){
		long raffleId = 1L;
		User user = new User();
		long ticketPurchased = 1L;
		int price = 0;
		
		raffleTicketServiceImpl.addDefaultTicketPurchase(raffleId, user);
		
		ArgumentCaptor<PurchasedRaffleTicket> captor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService).save(captor.capture());
		
		PurchasedRaffleTicket purchasedRaffleTicket = captor.getValue();
		
		assertEquals(raffleId, purchasedRaffleTicket.getRaffleId());
		assertEquals(user, purchasedRaffleTicket.getUser());
		assertEquals(ticketPurchased, purchasedRaffleTicket.getTicketsPurchased());
		assertEquals(price, purchasedRaffleTicket.getPrice());
	}
	
	@Test
	void testAddDefaultTicketPrices(){
		Raffle raffleModule = new Raffle();
		raffleTicketServiceImpl.addDefaultTicketPrices(raffleModule);
		verify(raffleTicketRepository, times(6)).save(any(RaffleTicket.class));
	}
}
