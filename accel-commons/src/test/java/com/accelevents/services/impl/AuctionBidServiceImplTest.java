package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.AuctionHomeDto;
import com.accelevents.auction.dto.ItemAndBidAmount;
import com.accelevents.common.dto.HostAuctionDetail;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.domain.enums.CountryCode;
import com.accelevents.domain.enums.ModuleStatus;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.dto.AuctionBidDto;
import com.accelevents.dto.RefundInfoDto;
import com.accelevents.dto.ResponseDto;
import com.accelevents.dto.SilentActivity;
import com.accelevents.event.services.EventEndingService;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.AuctionBidRepository;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.staff.dto.LiveItemsCodeAndAmountDto;
import com.accelevents.utils.*;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static com.accelevents.utils.GeneralUtils.getCheckoutPath;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AuctionBidServiceImplTest {
    private static final Logger log = LoggerFactory.getLogger(AuctionBidServiceImpl.class);

    @Spy
    @InjectMocks
    private AuctionBidServiceImpl auctionBidServiceImpl;
    
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private AuctionBidRepository auctionBidRepository;

    @Mock
    private JoinPaymentItemService joinPaymentItemService;

    @Mock
    private AuctionService auctionService;

    @Mock
    private EventEndingService eventEndingService;

    @Mock
    private ItemService itemService;

    @Mock
    private WinnerService winnerService;

    @Mock
    private TextMessageUtils textMessageUtils;

    @Mock
    private TextMessageService textMessageService;

    @Mock
    private EventService eventService;

    @Mock
    private BidderNumberService bidderNumberService;

    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

    @Mock
    private StripeTransactionService stripeTransactionService;

    @Mock
    private RefundTransactionService refundTransactionService;

    @Mock
    private ROStripeService roStripeService;

    @Mock
    private AllPaymentService allPaymentService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    @Value("${uiBaseurl}")
    private String uiBaseurl;

    private Event event;
    private User user;
    private AuctionBid auctionBid;
    private Item item;
    private Winner winner;
    private Auction auction;
    private ItemAndBidAmount itemAndBidAmount;
    private Stripe stripe;
    private RefundInfoDto refundInfoDto;
    private Payment payment;
    private JoinPaymentItem joinPaymentItem;


    private Long auctionBidId = 1L;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        auction = new Auction();
        auction.setId(1L);
        auctionBid = new AuctionBid();
        auctionBid.setId(auctionBidId);
        item = new Item();
        item.setId(1L);
        item.setBuyItNowPrice(1000);
        itemAndBidAmount = new ItemAndBidAmount();
        stripe = new Stripe();
        refundInfoDto = new RefundInfoDto();
        payment = new Payment();
    }

    private void setAuctionBidObject(boolean bidHasPaid, Boolean bidRefunded, User user, Double amount, Item item){
        auctionBid.setUser(user);
        auctionBid.setItem(item);
        auctionBid.setAmount(amount);
        auctionBid.setBidTime(DateUtils.getCurrentDate());
        auctionBid.setHasPaid(bidHasPaid);
        auctionBid.setRefunded(bidRefunded);
    }

    @Test
    void test_deleteAuctionBidAndUpdateCurrentBidForItem_exceptionBidNotFound() {

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.deleteAuctionBidAndUpdateCurrentBidForItem(auctionBidId, event, user));

        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
    }

    @Test
    void test_findByAuctionIdAndIsConfirmed_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsConfirmedAndIsRefunded(anyLong(), anyBoolean(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndIsConfirmed(auction.getId());

        //Assertion
        assertTrue(auctionBidData.isEmpty());
        verify(auctionBidRepository).findByAuctionIdAndIsConfirmedAndIsRefunded(anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findByAuctionIdAndIsConfirmed_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsConfirmedAndIsRefunded(anyLong(), anyBoolean(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndIsConfirmed(auction.getId());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByAuctionIdAndIsConfirmedAndIsRefunded(anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findByAuctionId_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByAuctionId(anyLong())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionId(auction.getId());

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).findByAuctionId(anyLong());
    }

    @Test
    void test_findByAuctionId_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionId(anyLong())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionId(auction.getId());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByAuctionId(anyLong());
    }

    @Test
    void test_findByAuctionIdAndRefunded_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndRefunded(auction.getId(), false);

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).findByAuctionIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_findByAuctionIdAndRefunded_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(true, true, user, 400d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndRefunded(auction.getId(), true);

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByAuctionIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_findAuctionBidByAuctionIdOrderByItemAndAmountDesc_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findAuctionBidByAuctionIdOrderByItemAndAmountDesc(auction.getId());

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).findByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean());
    }

    @Test
    void test_findAuctionBidByAuctionIdOrderByItemAndAmountDesc_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);
        setAuctionBidObject(false, false, user, 400d, item);
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findAuctionBidByAuctionIdOrderByItemAndAmountDesc(auction.getId());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());
        assertEquals(auctionBidData.get(1).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(1).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(1).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(1).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(1).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(1).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(1).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean());
    }

    @Test
    void test_findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBidDto> auctionBidDtoList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean())).thenReturn(auctionBidDtoList);

        //Execution
        List<AuctionBidDto> auctionBidData = auctionBidServiceImpl.findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(auction.getId());

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean());
    }

    @Test
    void test_findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc_withReturnAuctionBidList() {

        //setup
        AuctionBidDto auctionBidDto = new AuctionBidDto(item.getId(), 3, user.getUserId(), 300d);
        List<AuctionBidDto> auctionBidDtoList = new ArrayList<>();
        auctionBidDtoList.add(auctionBidDto);

        //mock
        when(auctionBidRepository.findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean())).thenReturn(auctionBidDtoList);

        //Execution
        List<AuctionBidDto> auctionBidDtoData = auctionBidServiceImpl.findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(auction.getId());

        //Assertion
        assertEquals(auctionBidDtoData.get(0).getAmount(), auctionBidDto.getAmount());
        assertEquals(auctionBidDtoData.get(0).getItemId(), auctionBidDto.getItemId());
        assertEquals(auctionBidDtoData.get(0).getNumberOfWinners(), auctionBidDto.getNumberOfWinners());
        assertEquals(auctionBidDtoData.get(0).getUserId(), auctionBidDto.getUserId());

        verify(auctionBidRepository).findAuctionBidDtoByAuctionIdAndRefundedOrderByItemIdDescAmountDesc(anyLong(), anyBoolean());
    }

    @Test
    void test_findByUserAndBidAndAuctionIdAndItem_withsetAuctionBidObject() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByUserAndAmountAndAuctionIdAndItemAndIsRefunded(user, bidAmount, auction.getId(), item, false)).thenReturn(auctionBid);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findByUserAndBidAndAuctionIdAndItem(user, bidAmount, auction.getId(), item);

        //Assertion
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime(), auctionBid.getBidTime());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByUserAndAmountAndAuctionIdAndItemAndIsRefunded(isA(User.class), anyDouble(), anyLong(), isA(Item.class), anyBoolean());
    }

    @Test
    void test_findByUserAndBidAndAuctionIdAndItem_withsetAuctionBidObjectNull() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByUserAndAmountAndAuctionIdAndItemAndIsRefunded(isA(User.class), anyDouble(), anyLong(), isA(Item.class), anyBoolean())).thenReturn(null);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findByUserAndBidAndAuctionIdAndItem(user, 500, auction.getId(), item);

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findByUserAndAmountAndAuctionIdAndItemAndIsRefunded(isA(User.class), anyDouble(), anyLong(), isA(Item.class), anyBoolean());
    }

    @Test
    void test_findByUserAndAuctionAndItem_withsetAuctionBidObject() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByItemAndUserAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), any(), anyLong(), anyBoolean())).thenReturn(auctionBid);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findByUserAndAuctionAndItem(user, auction.getId(), item);

        //Assertion
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime(), auctionBid.getBidTime());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findFirstByItemAndUserAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), any(), anyLong(), anyBoolean());
    }

    @Test
    void test_findByUserAndAuctionAndItem_withsAuctionBidObjectNull() {

        //setup
        auction.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByItemAndUserAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), any(), anyLong(), anyBoolean())).thenReturn(null);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findByUserAndAuctionAndItem(user, auction.getId(), item);

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findFirstByItemAndUserAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), any(), anyLong(), anyBoolean());
    }

    @Test
    void test_save() {

        //setup
        setAuctionBidObject(true, false, user, 500d, item);

        //Execution
        auctionBidServiceImpl.save(auctionBid);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());
    }

    @Test
    void test_findByUser_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.getAuctionBidByUserAndIsRefundedOrderByBidTime(any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByUser(user);

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).getAuctionBidByUserAndIsRefundedOrderByBidTime(any(), anyBoolean());
    }

    @Test
    void test_findByUser_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(true, true, user, 400d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.getAuctionBidByUserAndIsRefundedOrderByBidTime(any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByUser(user);

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).getAuctionBidByUserAndIsRefundedOrderByBidTime(any(), anyBoolean());
    }

    @Test
    void test_findHighestBidConfirmedItemForAuction_withsetAuctionBidObject() {

        //setup
        Double bidAmount = 1000d;
        setAuctionBidObject(true, false, user, bidAmount, item);
        auctionBid.setConfirmed(true);

        //mock
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsConfirmedAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findHighestBidConfirmedItemForAuction(item, auction.getId());

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());
        assertTrue(auctionBidData.get().isConfirmed());

        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsConfirmedAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findHighestBidConfirmedItemForAuction_withsetAuctionBidObjectNull() {

        //setup
        auction.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);
        auctionBid.setConfirmed(false);

        //mock
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsConfirmedAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean(), anyBoolean())).thenReturn(null);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findHighestBidConfirmedItemForAuction(item, auction.getId());

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsConfirmedAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findHighestBidItemForAuction_withsetAuctionBidObject() {

        //setup
        Double bidAmount = 1000d;
        setAuctionBidObject(true, false, user, bidAmount, item);
        auctionBid.setConfirmed(false);

        //mock
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findHighestBidItemForAuction(item, auction.getId());

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());
        assertFalse(auctionBidData.get().isConfirmed());

        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());
    }

    @Test
    void test_findHighestBidItemForAuction_withsetAuctionBidObjectNull() {

        //setup
        auction.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);
        auctionBid.setConfirmed(false);

        //mock
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(null);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findHighestBidItemForAuction(item, auction.getId());

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());
    }

    @Test
    void test_findByUserAndAuctionId_withReturnEmptyAuctionBidList() {

        //setup
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByUserAndAuctionIdAndIsRefunded(any(), anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByUserAndAuctionId(user, 2L);

        //Assertion
        assertTrue(auctionBidData.isEmpty());

        verify(auctionBidRepository).findByUserAndAuctionIdAndIsRefunded(any(), anyLong(), anyBoolean());
    }

    @Test
    void test_findByUserAndAuctionId_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(true, true, user, 400d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByUserAndAuctionIdAndIsRefunded(any(), anyLong(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByUserAndAuctionId(user, auction.getId());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByUserAndAuctionIdAndIsRefunded(any(), anyLong(), anyBoolean());
    }

    @Test
    void test_findMaxBidForItemByUserAndAuctionId_withAuctionBidList() {

        //setup
        Object[] auctionBids = {"first auction", 700, 500};
        List<Object[]> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBids);

        //mock
        when(auctionBidRepository.findMaxBidForItemByUserAndAuctionId(anyLong(), anyLong())).thenReturn(auctionBidList);

        //Execution
        List<SilentActivity> silentActivityList = auctionBidServiceImpl.findMaxBidForItemByUserAndAuctionId(user.getUserId(), auction.getId());

        //Assertion
        assertEquals(silentActivityList.get(0).getItemName(), auctionBids[0]);
        assertEquals(silentActivityList.get(0).getMyBid(), ((Number) auctionBids[1]).intValue());
        assertEquals(silentActivityList.get(0).getCurrentBid(), ((Number) auctionBids[2]).intValue());

        verify(auctionBidRepository).findMaxBidForItemByUserAndAuctionId(anyLong(), anyLong());
    }

    @Test
    void test_findMaxBidForItemByUserAndAuctionId_withAuctionBidListEmpty() {

        //setup
        List<Object[]> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findMaxBidForItemByUserAndAuctionId(anyLong(), anyLong())).thenReturn(auctionBidList);

        //Execution
        List<SilentActivity> silentActivityList = auctionBidServiceImpl.findMaxBidForItemByUserAndAuctionId(3L, 2L);

        //Assertion
        assertTrue(silentActivityList.isEmpty());

        verify(auctionBidRepository).findMaxBidForItemByUserAndAuctionId(anyLong(), anyLong());
    }

    @Test
    void test_findHighestAmountBidByItem_withsetAuctionBidObject() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByItemAndIsRefundedOrderByAmountDesc(any(), anyBoolean())).thenReturn(auctionBid);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findHighestAmountBidByItem(item);

        //Assertion
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findFirstByItemAndIsRefundedOrderByAmountDesc(any(), anyBoolean());
    }

    @Test
    void test_findHighestAmountBidByItem_withsetAuctionBidObjectNull() {

        //setup
        item.setId(2L);
        Double bidAmount = 500d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByItemAndIsRefundedOrderByAmountDesc(any(), anyBoolean())).thenReturn(null);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findHighestAmountBidByItem(item);

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findFirstByItemAndIsRefundedOrderByAmountDesc(any(), anyBoolean());
    }

    @Test
    void test_findAuctionBidByLiveItem_withGetAuctionBid() {

        //setup
        Double bidAmount = 1000d;
        setAuctionBidObject(true, false, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByItem(any())).thenReturn(Optional.of(auctionBid));

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findAuctionBidByLiveItem(item);

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByItem(any());
    }

    @Test
    void test_findAuctionBidByLiveItem_withGetAuctionBidNull() {

        //setup
        item.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByItem(any())).thenReturn(null);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findAuctionBidByLiveItem(item);

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findByItem(any());
    }

    @Test
    void test_deleteAuctionBidsOfAuction() {

        //setup
        Mockito.doNothing().when(auctionBidRepository).deleteAuctionBidsByAuctionId(anyLong());

        //Execution
        auctionBidServiceImpl.deleteAuctionBidsOfAuction(auction.getId());

        //Assertion
        verify(auctionBidRepository).deleteAuctionBidsByAuctionId(anyLong());
    }

    @Test
    void test_deleteAuctionBidsOfItem() {

        //setup
        Mockito.doNothing().when(auctionBidRepository).deleteAuctionBidsByItem(any());

        //Execution
        auctionBidServiceImpl.deleteAuctionBidsOfItem(item);

        //Assertion
        verify(auctionBidRepository).deleteAuctionBidsByItem(any());
    }

    @Test
    void test_findUnconfirmedBidForUser_withGetAuctionBid() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);
        auctionBid.setConfirmed(false);

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(auctionBid);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findUnconfirmedBidForUser(auction.getId(), false, user, item);

        //Assertion
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());
        assertFalse(auctionBidData.isConfirmed());

        verify(auctionBidRepository).findByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean());
    }

    @Test
    void test_findUnconfirmedBidForUser_withGetAuctionBidNull() {

        //setup
        item.setId(2L);
        Double bidAmount = 500d;
        setAuctionBidObject(true, true, user, bidAmount, item);
        auctionBid.setConfirmed(true);

        //mock
        when(auctionBidRepository.findByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(null);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findUnconfirmedBidForUser(auction.getId(), false, user, item);

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean());
    }

    @Test
    void test_findAllByAuctionIdAndIsConfirmedAndUserAndItem_withReturnEmptyAuctionBidList() {

        //setup
        Item item = new Item();
        item.setId(2L);
        item.setCode("CAU");
        setAuctionBidObject(false, false, user, 500d, item);
        auctionBid.setConfirmed(false);
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findAllByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findAllByAuctionIdAndIsConfirmedAndUserAndItem(auction.getId(), true, user, item);

        //Assertion
        assertTrue(auctionBidData.isEmpty());
        verify(auctionBidRepository).findAllByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean());
    }

    @Test
    void test_findAllByAuctionIdAndIsConfirmedAndUserAndItem_withReturnAuctionBidList() {

        //setup
        item.setCode("CAU");
        setAuctionBidObject(false, false, user, 500d, item);
        auctionBid.setConfirmed(true);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findAllByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findAllByAuctionIdAndIsConfirmedAndUserAndItem(auction.getId(), true, user, item);

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());
        assertEquals(auctionBidData.get(0).getItem().getCode(), item.getCode());

        verify(auctionBidRepository).findAllByAuctionIdAndIsConfirmedAndUserAndItemAndIsRefunded(anyLong(), anyBoolean(), any(), any(), anyBoolean());
    }

    @Test
    void testSave() {

        //setup
        Double amount = 500d;
        BiddingSource bidSource = BiddingSource.ONLINE;
        Boolean isConfirmed = true;
        Boolean isHasPaid = true;
        User staffUser = new User();
        staffUser.setUserId(2L);
        staffUser.setFirstName("jon");
        staffUser.setLastName("kaj");

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.save(event,user, auction.getId(), item, amount, bidSource, true, true, staffUser);

        //Assertion
        assertEquals(Boolean.TRUE,auctionBidData.isConfirmed());
        assertEquals(auctionBidData.getAuctionId(), auction.getId());
        assertEquals(auctionBidData.getItem().getId(), item.getId());
        assertEquals(auctionBidData.getAmount(), amount);
        assertEquals(auctionBidData.getBidSource(), bidSource);
        assertEquals(auctionBidData.getStaffUserId().getUserId(), staffUser.getUserId());
        assertEquals(auctionBidData.getBidTime().toString(), new Date().toString());

        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBid = auctionBidArgumentCaptor.getValue();
        assertEquals(Boolean.TRUE,auctionBid.isConfirmed());
        assertEquals(auctionBid.getAuctionId(), auction.getId());
        assertEquals(auctionBid.getItem().getId(), item.getId());
        assertEquals(auctionBid.getAmount(), amount);
        assertEquals(auctionBid.getBidSource(), bidSource);
        assertEquals(auctionBid.getStaffUserId().getUserId(), staffUser.getUserId());
        assertEquals(auctionBid.getBidTime().toString(), new Date().toString());
    }

    @Test
    void test_payAuctionBidManually_throwException() {

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.payAuctionBidManualy(auctionBid.getId(), user, event));

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_payAuctionBidManually_withWinnerAndPaymentIsPresent() {

        //setup
        auctionBid.setUser(user);
        winner = new Winner();

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(paymentService.findByUserIdAndEventId(auctionBid.getUser().getUserId(), event.getEventId())).thenReturn(Optional.of(payment));
        when(winnerService.findWinnerByBidId(auctionBid.getId())).thenReturn(winner);

        //Execution
        auctionBidServiceImpl.payAuctionBidManualy(auctionBid.getId(), user, event);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBid = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBid.isHasPaid());
        assertEquals(auctionBid.getStaffUserId().getUserId(), user.getUserId());

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentService).save(paymentArgumentCaptor.capture());

        Payment payment = paymentArgumentCaptor.getValue();
        assertEquals(payment.getStaffUserId(), user.getUserId().longValue());

        ArgumentCaptor<JoinPaymentItem> joinPaymentItemArgumentCaptor = ArgumentCaptor.forClass(JoinPaymentItem.class);
        verify(joinPaymentItemService).save(joinPaymentItemArgumentCaptor.capture());

        JoinPaymentItem joinPaymentItem = joinPaymentItemArgumentCaptor.getValue();
        assertEquals(Constants.MANUAL_TRANSACTION_TYPE,joinPaymentItem.getTransactionType());
        assertEquals(joinPaymentItem.getItem(), auctionBid.getItem());
        assertEquals(joinPaymentItem.getPayment().getUserId(), payment.getUserId());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertTrue(winner.isHasPaid());

        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_payAuctionBidManually_withWinnerIsNullAndPaymentIsNotPresent() {

        //mock
        auctionBid.setUser(user);
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(paymentService.findByUserIdAndEventId(auctionBid.getUser().getUserId(), event.getEventId())).thenReturn(Optional.empty());
        when(winnerService.findWinnerByBidId(auctionBid.getId())).thenReturn(null);

        //Execution
        auctionBidServiceImpl.payAuctionBidManualy(auctionBid.getId(), user, event);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBid = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBid.isHasPaid());
        assertEquals(auctionBid.getStaffUserId().getUserId(), user.getUserId());

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentService, times(2)).save(paymentArgumentCaptor.capture());

        Payment payment = paymentArgumentCaptor.getValue();
        assertEquals(payment.getStaffUserId(), user.getUserId().longValue());
        assertEquals(payment.getUserId(), auctionBid.getUser().getUserId().longValue());
        assertEquals(payment.getEventId(), event.getEventId());
        assertEquals(Constants.STRING_EMPTY,payment.getStripeToken());
        assertEquals(payment.getTokenCreatedTime().toString(), new Date().toString());

        ArgumentCaptor<JoinPaymentItem> joinPaymentItemArgumentCaptor = ArgumentCaptor.forClass(JoinPaymentItem.class);
        verify(joinPaymentItemService).save(joinPaymentItemArgumentCaptor.capture());

        JoinPaymentItem joinPaymentItem = joinPaymentItemArgumentCaptor.getValue();
        assertEquals(Constants.MANUAL_TRANSACTION_TYPE,joinPaymentItem.getTransactionType());
        assertEquals(joinPaymentItem.getItem(), auctionBid.getItem());
        assertEquals(joinPaymentItem.getPayment().getUserId(), payment.getUserId());

        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_unPaidAuctionBidManually_throwExceptionbidNotFound() {

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.unPaidAuctionBidManually(auctionBid.getId(), user, event));

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_unPaidAuctionBidManually_withPaymentAndWinner() {

        //setup
        auctionBid.setUser(user);
        winner = new Winner();

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(paymentService.findByUserIdAndEventId(auctionBid.getUser().getUserId(), event.getEventId())).thenReturn(Optional.of(payment));
        when(winnerService.findWinnerByBidId(auctionBid.getId())).thenReturn(winner);
        Mockito.doNothing().when(joinPaymentItemService).deleteByPaymentIdAndTransactionType(any(), anyString(),any());

        //Execution
        auctionBidServiceImpl.unPaidAuctionBidManually(auctionBid.getId(), user, event);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBid = auctionBidArgumentCaptor.getValue();
        assertFalse(auctionBid.isHasPaid());
        assertEquals(auctionBid.getStaffUserId().getUserId(), user.getUserId());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertFalse(winner.isHasPaid());

        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_unPaidAuctionBidManually_withPaymentNotPresentAndWinnerIsNull() {

        //setup
        auctionBid.setUser(user);
        winner = new Winner();

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(paymentService.findByUserIdAndEventId(auctionBid.getUser().getUserId(), event.getEventId())).thenReturn(Optional.empty());
        when(winnerService.findWinnerByBidId(auctionBid.getId())).thenReturn(null);

        //Execution
        auctionBidServiceImpl.unPaidAuctionBidManually(auctionBid.getId(), user, event);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBid = auctionBidArgumentCaptor.getValue();
        assertFalse(auctionBid.isHasPaid());
        assertEquals(auctionBid.getStaffUserId().getUserId(), user.getUserId());

        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_updateUserByNewUser() {

        //setup
        User newUser = new User();
        newUser.setUserId(2L);
        newUser.setFirstName("jay");
        newUser.setLastName("brill");

        //mock
        Mockito.doNothing().when(auctionBidRepository).updateByNewUser(any(), any());

        //Execution
        auctionBidServiceImpl.updateUserByNewUser(user, newUser);

        //Assertion
        verify(auctionBidRepository).updateByNewUser(any(), any());
    }

    @Test
    void test_getAuctionBidByIdAndNotRefunded_withGetAuctionBid() {

        //setup
        Double bidAmount = 1000d;
        setAuctionBidObject(true, false, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.getAuctionBidByIdAndNotRefunded(auctionBid.getId());

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_getAuctionBidByIdAndNotRefunded_withGetAuctionBidNull() {

        //setup
        auctionBid.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(null);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.getAuctionBidByIdAndNotRefunded(auctionBid.getId());

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_getSumOfAuctionBids_throwExceptionBidNOtFound() {

        //setup
        List<Long> bidIds = new ArrayList<>();

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.getSumOfAuctionBids(bidIds));
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getSumOfAuctionBids_withAmountNotZero() {

        //setup
        BigDecimal amount = BigDecimal.ONE;
        List<Long> bidIds = new ArrayList<>();
        bidIds.add(auctionBid.getId());

        //mock
        when(auctionBidRepository.getSumOfAuctionBids(anyList())).thenReturn(amount);

        //Execution
        double bidAmount = auctionBidServiceImpl.getSumOfAuctionBids(bidIds);

        //Assertion
        assertEquals(amount.doubleValue(),bidAmount ,0.0);

        verify(auctionBidRepository).getSumOfAuctionBids(anyList());
    }

    static Object[] bidAmount(){
        return new Object[]{
                new Object[]{null},
                new Object[]{BigDecimal.ZERO},
        };
    }

    @ParameterizedTest
    @MethodSource("bidAmount")
    void test_getSumOfAuctionBids_throwExceptionAmountNotValid(BigDecimal amount) {

        //setup
        List<Long> bidIds = new ArrayList<>();
        bidIds.add(auctionBid.getId());

        //mock
        when(auctionBidRepository.getSumOfAuctionBids(anyList())).thenReturn(amount);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> auctionBidServiceImpl.getSumOfAuctionBids(bidIds));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.AMOUNT_NOT_VALID.getDeveloperMessage(), exception.getMessage());
    }


    @Test
    void test_getByBidId_withGetAuctionBid() {

        //setup
        Double bidAmount = 1000d;
        setAuctionBidObject(true, false, user, bidAmount,item);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.getByBidId(auctionBid.getId());

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findById(anyLong());
    }

    @Test
    void test_getByBidId_withGetAuctionBidNull() {

        //setup
        auctionBid.setId(2L);
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(null);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.getByBidId(auctionBid.getId());

        //Assertion
        assertNull(auctionBidData);
        verify(auctionBidRepository).findById(anyLong());
    }

    @Test
    void test_findValidAuctionBid_withAuctionBid(){

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findValidAuctionBid(auctionBid.getId());

        //Assertion
        assertEquals(auctionBidData.getId(), auctionBid.getId());
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_findValidAuctionBid_throwExceptionBidNotFound(){

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.findValidAuctionBid(auctionBid.getId()));

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_findByItem_withReturnEmptyAuctionBidList() {

        //setup
        Item item = new Item();
        item.setId(2L);
        item.setCode("CAU");
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findAllByItemAndIsRefunded(any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByItem(item);

        //Assertion
        assertTrue(auctionBidData.isEmpty());
        verify(auctionBidRepository).findAllByItemAndIsRefunded(any(), anyBoolean());
    }

    @Test
    void test_findByItem_withReturnAuctionBidList() {

        //setup
        item.setCode("CAU");
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findAllByItemAndIsRefunded(any(), anyBoolean())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByItem(item);

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());
        assertEquals(auctionBidData.get(0).getItem().getCode(), item.getCode());

        verify(auctionBidRepository).findAllByItemAndIsRefunded(any(), anyBoolean());
    }

    @Test
    void test_findAllAuctionUsersByItem_withUserList() {

        //setup
        List<User> userList = new ArrayList<>();
        userList.add(user);

        //mock
        when(auctionBidRepository.findAllAuctionUsersByItemAndIsRefunded(any())).thenReturn(userList);

        //Execution
        List<User> userData = auctionBidServiceImpl.findAllAuctionUsersByItem(item);

        //Assertion
        assertEquals(userData.get(0).getUserId(), user.getUserId());
        assertEquals(userData.get(0).getFirstName(), user.getFirstName());
        assertEquals(userData.get(0).getLastName(), user.getLastName());
    }

    @Test
    void test_findAllAuctionUsersByItem_withUserListEmpty() {

        //setup
        List<User> userList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findAllAuctionUsersByItemAndIsRefunded(any())).thenReturn(userList);

        //Execution
        List<User> userData = auctionBidServiceImpl.findAllAuctionUsersByItem(item);

        //Assertion
        assertTrue(userData.isEmpty());
    }
    @Test
    void test_resentAuctionBidPaymentConfirmationLink_successMessage() {

        //setup
        setAuctionBidObject(true, false, user, 500d, item);
        item.setCode("AUC");
        item.setItemShortName("auction");
        String message = Constants.SUCCESS;
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(textMessageUtils.getPaymentConfirmationRequiredMessage(isA(Event.class), anyDouble(), isA(User.class), isA(Item.class),anyString(),anyMap())).thenReturn(message);
        Mockito.doNothing().when(textMessageService).sendText(any(), any(), anyString());

        //Execution
        String successMessage = auctionBidServiceImpl.resentAuctionBidPaymentConfirmationLink(auctionBid.getId(), event,languageCode,languageMap);

        //Assertion
        assertEquals(successMessage, message);
        verify(auctionBidRepository) .findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(textMessageUtils).getPaymentConfirmationRequiredMessage(isA(Event.class), anyDouble(), isA(User.class), isA(Item.class),anyString(),anyMap());
        verify(textMessageService).sendText(any(), any(), anyString());
    }

    @Test
    void test_sendMessageToConfirmPurchaseWithBidderNameResponse() {
        //setup
        setAuctionBidObject(true, false, user, 500d, item);
        item.setCode("AUC");
        item.setItemShortName("auction");
        String message = Constants.SUCCESS;
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(textMessageUtils.getConfirmPurchasePaymentDisabledMessage(any(), any(),anyString(),anyMap())).thenReturn(message);
        Mockito.doNothing().when(textMessageService).sendText(any(), any(), anyString());

        //Execution
        String successMessage = auctionBidServiceImpl.sendMessageToConfirmPurchaseWithBidderNameResponse(auctionBid.getId(), event,languageCode,languageMap);

        //Assertion
        assertEquals(successMessage, message);
        verify(auctionBidRepository) .findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(textMessageUtils).getConfirmPurchasePaymentDisabledMessage(any(), any(),anyString(),anyMap());
        verify(textMessageService).sendText(any(), any(), anyString());
    }

    static Object[] userEmail(){
        return new Object[]{
                new Object[]{null},
                new Object[]{""},
        };
    }
    @ParameterizedTest
    @MethodSource("userEmail")
    void test_sendMailAndMessageToAuctionBidWinner_withuserEmailNullAndEmpty(String email) {

        //setup
        user.setEmail(email);
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        auctionBidServiceImpl.sendMailAndMessageToAuctionBidWinner(auctionBid.getId(), event);

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_sendMailAndMessageToAuctionBidWinner() throws IOException {

        //setup
        user.setEmail("<EMAIL>");
        user.setPhoneNumber(9898989898L);
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(itemService.isBuyItNowItemAndHasBeenPaid(any())).thenReturn(false);
        Mockito.doNothing().when(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        Mockito.doNothing().when(winnerService).sendAuctionSingleItemWinnerText(any(), any(), any());

        //Execution
        auctionBidServiceImpl.sendMailAndMessageToAuctionBidWinner(auctionBid.getId(), event);

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(itemService).isBuyItNowItemAndHasBeenPaid(any());
        verify(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
    }

    @Test
    void test_sendMailAndMessageToAuctionBidWinner_withIsBuyItNowItemAndHasBeenPaidTrue() throws IOException {

        //setup
        user.setEmail("<EMAIL>");
        user.setPhoneNumber(9898989898L);
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(itemService.isBuyItNowItemAndHasBeenPaid(any())).thenReturn(true);
        Mockito.doNothing().when(winnerService).sendAuctionSingleItemWinnerText(any(), any(), any());

        //Execution
        auctionBidServiceImpl.sendMailAndMessageToAuctionBidWinner(auctionBid.getId(), event);

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(itemService).isBuyItNowItemAndHasBeenPaid(any());
    }

    @Test
    void test_sendMailAndMessageToAuctionBidWinner_throwException() throws IOException {

        //setup
        user.setEmail("<EMAIL>");
        user.setPhoneNumber(9898989898L);
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(itemService.isBuyItNowItemAndHasBeenPaid(any())).thenReturn(false);
        Mockito.doThrow(new RuntimeException()).when(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        Mockito.doThrow(new RuntimeException()).when(winnerService).sendAuctionSingleItemWinnerText(any(), any(), any());

        //Execution
        auctionBidServiceImpl.sendMailAndMessageToAuctionBidWinner(auctionBid.getId(), event);

        //Assertion
        verify(auctionBidRepository).findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(itemService).isBuyItNowItemAndHasBeenPaid(any());
        verify(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
    }

    @Test
    void test_sendMailAndMessageToAuctionBidder_winnersAndUserEmailAndAuctionBid() throws IOException {

        //setup
        AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(CountryCode.US, 9898989898L);
        event.setPhoneNumberAndCountryCode(accelEventsPhoneNumber);
        item.setItemShortName("first auction");
        item.setCode("AUC");
        user.setEmail("<EMAIL>");
        setAuctionBidObject(true, false, user, 500d, item);
        String link = uiBaseurl + getCheckoutPath() + event.getEventURL() + "/A/"
                + SecurityUtils.encodeUserid(user.getUserId());
        String message = "Thank you for purchasing "+item.getItemShortName()+" ("+item.getCode()+"), please complete your purchase by clicking this link "+link+" ";
        winner = new Winner();
        winner.setWinnerId(1L);
        winner.setItemId(item.getId());
        winner.setBidId(auctionBid.getId());
        List<Winner> winners = new ArrayList<>();
        winners.add(winner);
        List<Item> items = new ArrayList<>();
        items.add(item);
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        //mock
        when(auctionService.findByEvent(any())).thenReturn(auction);
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));
        when(textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(any(), any(), any(), any(),anyString(),anyMap())).thenReturn(message);
        when(winnerService.findByEventIdAndModuleTypeAndUserIdAndHasPaid(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(winners);
        when(itemService.getByItemIds(anyList())).thenReturn(items);
        Mockito.doThrow(IOException.class).when(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        Mockito.doNothing().when(textMessageService).sendText(any(), any(), anyString());

        auctionBid.setAuctionId(1L);
        event.setAuctionId(1L);
        //Execution
        ResponseDto responseDtoData = auctionBidServiceImpl.sendMailAndMessageToAuctionBidder(auctionBid.getId(), event,languageCode,languageMap);

        //Assertion
        assertTrue(responseDtoData.getType().contains(Constants.SUCCESS));
        assertTrue(responseDtoData.getMessage().contains(Constants.SUCCESS));

        verify(auctionService).findByEvent(any());
        verify(auctionBidRepository, times(2)).findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(textMessageUtils).getNotifyNotPaidAuctionBidWinnerMessage(any(), any(), any(), any(),anyString(),anyMap());
        verify(winnerService).findByEventIdAndModuleTypeAndUserIdAndHasPaid(anyLong(), any(), anyLong(), anyBoolean());
        verify(itemService).getByItemIds(anyList());
        verify(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        verify(textMessageService).sendText(any(), any(), anyString());
    }

    @Test
    void test_sendMailAndMessageToAuctionBidder_winnersAndUserEmailAndAuctionBidEmpty() throws IOException {

        //setup
        AccelEventsPhoneNumber accelEventsPhoneNumber = new AccelEventsPhoneNumber(CountryCode.US, 9898989898L);
        event.setPhoneNumberAndCountryCode(accelEventsPhoneNumber);
        item.setItemShortName("first auction");
        item.setCode("AUC");
        user.setEmail("");
        setAuctionBidObject(true, false, user, 500d, item);
        String link = uiBaseurl + getCheckoutPath() + event.getEventURL() + "/A/"
                + SecurityUtils.encodeUserid(user.getUserId());
        String message = "Thank you for purchasing "+item.getItemShortName()+" ("+item.getCode()+"), please complete your purchase by clicking this link "+link+" ";
        winner = new Winner();
        winner.setWinnerId(1L);
        winner.setItemId(item.getId());
        winner.setBidId(auctionBid.getId());
        List<Winner> winners = new ArrayList<>();
        winners.add(winner);
        List<Item> items = new ArrayList<>();
        items.add(item);
        String languageCode = "EN";
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

        //mock
        when(auctionService.findByEvent(any())).thenReturn(auction);
        Mockito.doReturn(auctionBid).when(auctionBidServiceImpl).findValidAuctionBid(anyLong());
        when(auctionBidRepository.findByIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(Optional.empty());
        when(textMessageUtils.getNotifyNotPaidAuctionBidWinnerMessage(any(), any(), any(), any(),anyString(),anyMap())).thenReturn(message);
        when(winnerService.findByEventIdAndModuleTypeAndUserIdAndHasPaid(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(winners);
        when(itemService.getByItemIds(anyList())).thenReturn(items);
        Mockito.doNothing().when(textMessageService).sendText(any(), any(), anyString());

        auctionBid.setAuctionId(1L);
        event.setAuctionId(1L);
        //Execution
        ResponseDto responseDtoData = auctionBidServiceImpl.sendMailAndMessageToAuctionBidder(auctionBid.getId(), event,languageCode,languageMap);

        //Assertion
        assertTrue(responseDtoData.getType().contains(Constants.SUCCESS));
        assertTrue(responseDtoData.getMessage().contains(Constants.SUCCESS));

        verify(auctionService).findByEvent(any());
        verify(auctionBidRepository, times(1)).findByIdAndIsRefunded(anyLong(), anyBoolean());
        verify(textMessageUtils).getNotifyNotPaidAuctionBidWinnerMessage(any(), any(), any(), any(),anyString(),anyMap());
        verify(winnerService).findByEventIdAndModuleTypeAndUserIdAndHasPaid(anyLong(), any(), anyLong(), anyBoolean());
        verify(itemService).getByItemIds(anyList());
        verify(textMessageService).sendText(any(), any(), anyString());

    }

    @Test
    void test_findAuctionBidsByAuctionAndBidderNumber() {

        //setup
        item.setCode("AUC");
        setAuctionBidObject(false, false, user, 500d, item);
        BidderNumber bidderNumber = new BidderNumber();
        bidderNumber.setBidderNumber(1L);
        bidderNumber.setEventId(event.getEventId());
        bidderNumber.setStaffUserId(user);
        bidderNumber.setUserId(user.getUserId());
        bidderNumber.setRegistrationTime(new Date());
        bidderNumber.setSource("ONLINE");

        //mock
        when(bidderNumberService.getBidderByBidderNumber(any(), anyLong())).thenReturn(Optional.of(bidderNumber));
        when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
        when(auctionBidRepository.findByUserAndAuctionIdAndHasPaidAndIsLiveAuctionItem(any(), anyLong(), anyBoolean(), anyBoolean())).thenReturn(Collections.singletonList(auctionBid));

        //Execution
        List<LiveItemsCodeAndAmountDto> liveItemsCodeAndAmountDtoData = auctionBidServiceImpl.findAuctionBidsByAuctionAndBidderNumber(event, bidderNumber.getBidderNumber());

        //Assertion
        assertEquals(0, Double.compare(liveItemsCodeAndAmountDtoData.get(0).getAmount(), auctionBid.getAmount().intValue()));
        assertEquals(liveItemsCodeAndAmountDtoData.get(0).getItemCode(), auctionBid.getItem().getCode());

        verify(bidderNumberService).getBidderByBidderNumber(any(), anyLong());
        verify(roUserService).getUserById(anyLong());
        verify(auctionBidRepository).findByUserAndAuctionIdAndHasPaidAndIsLiveAuctionItem(any(), anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findAuctionBidsByAuctionAndBidderNumber_throwExceptionBidNotFound() {

        //setup
        item.setCode("AUC");
        setAuctionBidObject(false, false, user, 500d, item);
        BidderNumber bidderNumber = new BidderNumber();
        bidderNumber.setBidderNumber(1L);
        bidderNumber.setEventId(event.getEventId());
        bidderNumber.setStaffUserId(user);
        bidderNumber.setUserId(user.getUserId());
        bidderNumber.setRegistrationTime(new Date());
        bidderNumber.setSource("ONLINE");

        //mock
        when(bidderNumberService.getBidderByBidderNumber(any(), anyLong())).thenReturn(Optional.of(bidderNumber));
        when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
        when(auctionBidRepository.findByUserAndAuctionIdAndHasPaidAndIsLiveAuctionItem(any(), anyLong(), anyBoolean(), anyBoolean())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.findAuctionBidsByAuctionAndBidderNumber(event, bidderNumber.getBidderNumber()));

        //Assertion
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

        verify(bidderNumberService).getBidderByBidderNumber(any(), anyLong());
        verify(roUserService).getUserById(anyLong());
        verify(auctionBidRepository).findByUserAndAuctionIdAndHasPaidAndIsLiveAuctionItem(any(), anyLong(), anyBoolean(), anyBoolean());
    }

    @Test
    void test_findAuctionBidsByAuctionAndBidderNumber_withUserIsNotPresent() {

        //setup
        item.setCode("AUC");
        setAuctionBidObject(false, false, user, 500d, item);
        BidderNumber bidderNumber = new BidderNumber();
        bidderNumber.setBidderNumber(1L);
        bidderNumber.setEventId(event.getEventId());
        bidderNumber.setStaffUserId(user);
        bidderNumber.setUserId(user.getUserId());
        bidderNumber.setRegistrationTime(new Date());
        bidderNumber.setSource("ONLINE");

        //mock
        when(bidderNumberService.getBidderByBidderNumber(any(), anyLong())).thenReturn(Optional.of(bidderNumber));
        when(roUserService.getUserById(anyLong())).thenReturn(Optional.empty());

        //Execution
        List<LiveItemsCodeAndAmountDto> liveItemsCodeAndAmountDtoData = auctionBidServiceImpl.findAuctionBidsByAuctionAndBidderNumber(event, bidderNumber.getBidderNumber());

        //Assertion
        assertTrue(liveItemsCodeAndAmountDtoData.isEmpty());

        verify(bidderNumberService).getBidderByBidderNumber(any(), anyLong());
        verify(roUserService).getUserById(anyLong());
    }

    @Test
    void test_findAuctionBidsByAuctionAndBidderNumber_withBidderIsNotPresent() {

        //setup
        item.setCode("AUC");
        setAuctionBidObject(false, false, user, 500d, item);
        BidderNumber bidderNumber = new BidderNumber();

        //mock
        when(bidderNumberService.getBidderByBidderNumber(any(), anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.findAuctionBidsByAuctionAndBidderNumber(event, bidderNumber.getBidderNumber()));

        //Assertion
        assertEquals(NotFoundException.NotFound.BIDDER_NOT_FOUND.getDeveloperMessage(), exception.getMessage());

        verify(bidderNumberService).getBidderByBidderNumber(any(), anyLong());
    }


    @Test
    void test_findByAuctionAndItemCode_withReturnEmptyAuctionBidList() {

        //setup
        item.setCode("WRONG_ITEM_CODE");
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findbyAuctionAndItemCode(any(), anyString())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionAndItemCode(auction, item.getCode());

        //Assertion
        assertTrue(auctionBidData.isEmpty());
        verify(auctionBidRepository).findbyAuctionAndItemCode(any(), anyString());
    }

    @Test
    void test_findByAuctionAndItemCode_withReturnAuctionBidList() {

        //setup
        item.setCode("AUC");
        setAuctionBidObject(false, false, user, 500d, item);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findbyAuctionAndItemCode(any(), anyString())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionAndItemCode(auction, item.getCode());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());
        assertFalse(auctionBidData.get(0).isExportedToNeon());

        verify(auctionBidRepository).findbyAuctionAndItemCode(any(), anyString());
    }

    @Test
    void test_findByAuctionIdAndExportedToNeonFalse_withReturnEmptyAuctionBidList() {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        auctionBid.setExportedToNeon(true);
        List<AuctionBid> auctionBidList = new ArrayList<>();

        //mock
        when(auctionBidRepository.findByAuctionIdAndExportedToNeonFalse(anyLong())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndExportedToNeonFalse(auction.getId());

        //Assertion
        assertTrue(auctionBidData.isEmpty());
        verify(auctionBidRepository).findByAuctionIdAndExportedToNeonFalse(anyLong());
    }

    @Test
    void test_findByAuctionIdAndExportedToNeonFalse_withReturnAuctionBidList() {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        auctionBid.setExportedToNeon(false);
        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionIdAndExportedToNeonFalse(anyLong())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid> auctionBidData = auctionBidServiceImpl.findByAuctionIdAndExportedToNeonFalse(auction.getId());

        //Assertion
        assertEquals(auctionBidData.get(0).getId(), auctionBid.getId());
        assertEquals(auctionBidData.get(0).isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get(0).isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get(0).getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get(0).getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get(0).getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get(0).getItem().getId(), auctionBid.getItem().getId());
        assertFalse(auctionBidData.get(0).isExportedToNeon());

        verify(auctionBidRepository).findByAuctionIdAndExportedToNeonFalse(anyLong());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_throwExceptionBidNotFound() throws StripeException, ApiException {
        
        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.empty());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user));

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_throwExceptionBidAlreadyRefunded() throws StripeException, ApiException {

        //setup
        setAuctionBidObject(true, true, user, 500d, item);
        
        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user));

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        assertEquals(NotAcceptableException.AuctionExceptionMsg.BID_ALREADY_REFUNDED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_throwExceptionOnlyPaidBidCanBeRefunded() throws StripeException, ApiException {

        //setup
        setAuctionBidObject(false, false, user, 500d, item);
        
        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user));

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        assertEquals(NotAcceptableException.AuctionExceptionMsg.ONLY_PAID_BID_CAN_BE_REFUNDED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_withOrderAmountToRefundZero() throws StripeException, ApiException {

        //setup
        user.setEmail("<EMAIL>");
        setAuctionBidObject(true, false, user, 0d, item);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));

        //Execution
        auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBidData.isRefunded());
        assertEquals(auctionBidData.getStaffUserId().getUserId(), user.getUserId());

        verify(auctionBidRepository).findById(anyLong());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_withStripeTransactionNull() throws StripeException, ApiException {

        //setup
        double amount = 500d;
        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setAmount(amount);
        stripeTransaction.setUser(user);
        user.setEmail("<EMAIL>");
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));
        when(stripeTransactionService.findBySourceAndSourceId(any(), anyLong())).thenReturn(null);
        when(stripeTransactionService.getBySourceAndItemId(any(), any(), any())).thenReturn(null);

        //Execution
        auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBidData.isRefunded());
        assertEquals(auctionBidData.getStaffUserId().getUserId(), user.getUserId());

        ArgumentCaptor<RefundTransaction> refundTransactionArgumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService).save(refundTransactionArgumentCaptor.capture());

        RefundTransaction refundTransaction  =refundTransactionArgumentCaptor.getValue();
        assertEquals("SANDBOX",refundTransaction.getRefundId());
        assertEquals(refundTransaction.getCreatedDate().toString(), new Date().toString());
        assertNull(refundTransaction.getStripeTransactionId());
        assertEquals( StripeTransactionSource.STAFF_AUCTION_REFUND,refundTransaction.getSource());
        assertEquals(refundTransaction.getRefundAmount() ,amount,0.0);
        assertEquals(refundTransaction.getSourceId(), auctionBid.getId());
        assertEquals("SUCCESS",refundTransaction.getStatus());
        assertEquals(RefundTransaction.RefundType.CASH,refundTransaction.getRefundType());
        assertEquals(0,refundTransaction.getRefundedApplicationFeeAmount());

        verify(auctionBidRepository).findById(anyLong());
        verify(stripeTransactionService).findBySourceAndSourceId(any(), anyLong());
        verify(stripeTransactionService).getBySourceAndItemId(any(), any(), any());
    }

    @Test
    void test_refundAuctionBidAndUpdateCurrentBidForItem_withStripeTransactionNotNull() throws StripeException, ApiException {

        //setup
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        double amount = 500d;
        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setId(1L);
        stripeTransaction.setAmount(amount);
        stripeTransaction.setUser(user);
        user.setEmail("<EMAIL>");
        setAuctionBidObject(true, false, user, 500d, item);
        refundInfoDto.setId("1");
        refundInfoDto.setStatus("PENDING");
        refundInfoDto.setRefundedApplicationFee(1L);
        refundInfoDto.setStripeNetSale(1L);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));
        when(stripeTransactionService.findBySourceAndSourceId(any(), anyLong())).thenReturn(stripeTransaction);
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(allPaymentService.createRefund(any(), any(), anyDouble(), anyMap(), anyString(), anyBoolean(), anyString())).thenReturn(refundInfoDto);

        //Execution
        auctionBidServiceImpl.refundAuctionBidAndUpdateCurrentBidForItem(auctionBid.getId(), event, new Date(), user);

        //Assertion
        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBidData.isRefunded());
        assertEquals(auctionBidData.getStaffUserId().getUserId(), user.getUserId());

        ArgumentCaptor<RefundTransaction> refundTransactionArgumentCaptor = ArgumentCaptor.forClass(RefundTransaction.class);
        verify(refundTransactionService).save(refundTransactionArgumentCaptor.capture());

        RefundTransaction refundTransaction  =refundTransactionArgumentCaptor.getValue();
        assertEquals(refundTransaction.getRefundId(), refundInfoDto.getId());
        assertEquals(refundTransaction.getCreatedDate().toString(), new Date().toString());
        assertEquals(refundTransaction.getRefundAmount(), amount,0.0);
        assertEquals(refundTransaction.getStripeTransactionId(), stripeTransaction.getId());
        assertEquals(StripeTransactionSource.AUCTION_ONLINE,refundTransaction.getSource());
        assertEquals(refundTransaction.getRefundAmount() ,amount,0.0);
        assertEquals(refundTransaction.getSourceId(), auctionBid.getId());
        assertEquals(refundTransaction.getStatus(), refundInfoDto.getStatus());
        assertEquals(refundTransaction.getRefundType(), RefundTransaction.RefundType.valueOf(GeneralUtils.getRefundType(stripe.getPaymentGateway())));
        assertEquals(refundTransaction.getRefundedApplicationFeeAmount(), refundInfoDto.getRefundedApplicationFee());

        verify(auctionBidRepository).findById(anyLong());
        verify(stripeTransactionService).findBySourceAndSourceId(any(), anyLong());
    }

    @Test
    void test_findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc_withGetAuctionBid() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByAuctionIdAndItemAndUserAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(auctionBid);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(auction.getId(), user, item, false);

        //Assertion
        assertEquals(auctionBidData.isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.getItem().getId(), auctionBid.getItem().getId());
    }

    @Test
    void test_findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc_withGetAuctionBidNull() {

        //setup
        Double bidAmount = 400d;
        setAuctionBidObject(true, true, user, bidAmount, item);

        //mock
        when(auctionBidRepository.findFirstByAuctionIdAndItemAndUserAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(null);

        //Execution
        AuctionBid auctionBidData = auctionBidServiceImpl.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(2L, user, item, false);

        //Assertion
        assertNull(auctionBidData);
    }

    @Test
    void test_getLastBidderLesserThanBuyItNow_withBuyItNowPriceLessThanZero() {

        //setup
        item.setBuyItNowPrice(0);
        item.setCurrentHighBidder(user);

        //Execution
        User lastBidder = auctionBidServiceImpl.getLastBidderLesserThanBuyItNow(item);

        //Assertion
        assertEquals(lastBidder.getUserId(), user.getUserId());
        assertEquals(lastBidder.getFirstName(), user.getFirstName());
        assertEquals(lastBidder.getLastName(), user.getLastName());
    }

    @Test
    void test_getLastBidderLesserThanBuyItNow_withBuyItNowPriceGreaterThanZeroAndAuctionBidNotNull() {

        //setup
        item.setBuyItNowPrice(1000);
        item.setCurrentHighBidder(user);
        setAuctionBidObject(true, false, user, 500d, item);

        //mock
        when(auctionBidRepository.findTopByItemAndAmountLessThanOrderByIdDesc(item, item.getBuyItNowPrice())).thenReturn(auctionBid);

        //Execution
        User lastBidder = auctionBidServiceImpl.getLastBidderLesserThanBuyItNow(item);

        //Assertion
        assertEquals(lastBidder.getUserId(), user.getUserId());
        assertEquals(lastBidder.getFirstName(), user.getFirstName());
        assertEquals(lastBidder.getLastName(), user.getLastName());
    }

    @Test
    void test_getLastBidderLesserThanBuyItNow_withBuyItNowPriceGreaterThanZeroAndAuctionBidNull() {

        //setup
        item.setBuyItNowPrice(1000);
        item.setCurrentHighBidder(user);

        //mock
        when(auctionBidRepository.findTopByItemAndAmountLessThanOrderByIdDesc(item, item.getBuyItNowPrice())).thenReturn(null);

        //Execution
        User lastBidder = auctionBidServiceImpl.getLastBidderLesserThanBuyItNow(item);

        //Assertion
        assertNull(lastBidder);
    }

    @Test
    void test_findNextHighestBidNotAsWinner_withAuctionBid() {

        //setup
        setAuctionBidObject(true, false, user, 500d, item);
        Page<AuctionBid> auctionBidPage = new PageImpl<>(Collections.singletonList(auctionBid));
        List<Long> userIds = new ArrayList<>();
        userIds.add(1L);

        //mock
        when(auctionBidRepository.findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any())).thenReturn(auctionBidPage);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findNextHighestBidNotAsWinner(auction.getId(), item, userIds);

        //Assertion
        assertEquals(auctionBidData.get().isHasPaid(), auctionBid.isHasPaid());
        assertEquals(auctionBidData.get().isRefunded(), auctionBid.isRefunded());
        assertEquals(auctionBidData.get().getUser().getUserId(), auctionBid.getUser().getUserId());
        assertEquals(auctionBidData.get().getAmount(), auctionBid.getAmount());
        assertEquals(auctionBidData.get().getBidTime().toString(), auctionBid.getBidTime().toString());
        assertEquals(auctionBidData.get().getItem().getId(), auctionBid.getItem().getId());

        verify(auctionBidRepository).findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any());
    }

    @Test
    void test_findNextHighestBidNotAsWinner_withAuctionBidEmpty() {

        //setup
        Page<AuctionBid> auctionBidPage = new PageImpl<>(Collections.emptyList());
        List<Long> userIds = new ArrayList<>();
        userIds.add(1L);

        //mock
        when(auctionBidRepository.findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any())).thenReturn(auctionBidPage);

        //Execution
        Optional<AuctionBid> auctionBidData = auctionBidServiceImpl.findNextHighestBidNotAsWinner(auction.getId(), item, userIds);

        //Assertion
        assertFalse(auctionBidData.isPresent());

        verify(auctionBidRepository).findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any());
    }

    @Test
    void test_getAuctionSalesDetails_withGetAuctionHomeDto() {

        //setup
        AuctionHomeDto auctionHomeDto = new AuctionHomeDto();
        auctionHomeDto.setDistinctItemsWithPaidBids(2L);
        auctionHomeDto.setItemsWithBids(5L);
        auctionHomeDto.setNumberOfBidders(3L);
        auctionHomeDto.setTotalBids(10L);

        //mock
        when(auctionBidRepository.getAuctionSalesDetails(anyLong())).thenReturn(auctionHomeDto);

        //Execution
        AuctionHomeDto auctionHomeDtoData = auctionBidServiceImpl.getAuctionSalesDetails(auction.getId());

        //Assertion
        assertEquals(auctionHomeDtoData.getDistinctItemsWithPaidBids(), auctionHomeDto.getDistinctItemsWithPaidBids());
        assertEquals(auctionHomeDtoData.getItemsWithBids(), auctionHomeDto.getItemsWithBids());
        assertEquals(auctionHomeDtoData.getNumberOfBidders(), auctionHomeDto.getNumberOfBidders());
        assertEquals(auctionHomeDtoData.getTotalBids(), auctionHomeDto.getTotalBids());

        verify(auctionBidRepository).getAuctionSalesDetails(anyLong());
    }

    @Test
    void test_getAuctionSalesDetails_withGetAuctionHomeDtoNull() {

        //mock
        when(auctionBidRepository.getAuctionSalesDetails(anyLong())).thenReturn(null);

        //Execution
        AuctionHomeDto auctionHomeDtoData = auctionBidServiceImpl.getAuctionSalesDetails(auction.getId());

        //Assertion
        assertNull(auctionHomeDtoData);
    }

    @Test
    void test_setHostAuctionDetail() {

        //setup
        int itemsWithBids = 02;
        int totalBids = 10;
        int paidBidItems = 05;
        HostAuctionDetail hostAuctionDetail = new HostAuctionDetail();

        //mock
        when(auctionBidRepository.countDistinctItemsByAuctionIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(itemsWithBids);
        when(auctionBidRepository.countDistinctByAuctionIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(totalBids);
        when(auctionBidRepository.countDistinctPaidItemsByAuctionIdAndIsRefunded(anyLong(), anyBoolean())).thenReturn(paidBidItems);

        //Execution
        HostAuctionDetail hostAuctionDetailData = auctionBidServiceImpl.setHostAuctionDetail(auction.getId(), hostAuctionDetail);

        //Assertion
        assertEquals(hostAuctionDetailData.getItemsWithBids(), itemsWithBids);
        assertEquals(hostAuctionDetailData.getTotalBids(), totalBids);
        assertEquals(hostAuctionDetailData.getPaidBidItems(), paidBidItems);

        verify(auctionBidRepository).countDistinctItemsByAuctionIdAndIsRefunded(anyLong(), anyBoolean());
        verify(auctionBidRepository).countDistinctByAuctionIdAndIsRefunded(anyLong(), anyBoolean());
        verify(auctionBidRepository).countDistinctPaidItemsByAuctionIdAndIsRefunded(anyLong(), anyBoolean());
    }

    @Test
    void test_deleteAuctionBidAndUpdateCurrentBidForItem_withAuctionBidPaidAndNotRefunded() {

        //setup
        auctionBid.setHasPaid(true);
        auctionBid.setRefunded(false);

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> auctionBidServiceImpl.deleteAuctionBidAndUpdateCurrentBidForItem(auctionBidId, event, user));

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        assertEquals(NotAcceptableException.AuctionExceptionMsg.CAN_NOT_DELET_ALREADT_PAID.getDeveloperMessage(), exception.getMessage());
    }


    static Object[] getStatusForAuctionBidPaidAndRefunded() {
        return new Object[]{
                new Object[]{true, true, new Date()},
                new Object[]{false, false, null},
                new Object[]{false, true, null},
        };
    }

    @ParameterizedTest
    @MethodSource("getStatusForAuctionBidPaidAndRefunded")
    void test_deleteAuctionBidAndUpdateCurrentBidForItem_withAuctionBidPaidAndRefunded(Boolean bidHasPaid, Boolean bidRefunded, Date bidTime) throws IOException {

        //setup
        user.setPhoneNumber(9898989898L);
        user.setEmail("<EMAIL>");
        auctionBid = setAuctionBidObject(auctionBidId, bidHasPaid, bidRefunded, user, 300d, item);
        auctionBid.setBidTime(bidTime);
        winner = new Winner();
        winner.setBidId(auctionBidId);
        winner.setUserId(user.getUserId());
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);

        Page<AuctionBid> auctionBidPage = new PageImpl<>(Collections.singletonList(auctionBid));

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));
        Mockito.doNothing().when(joinPaymentItemService).deleteByItem(item);

        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any())).thenReturn(auctionBidPage);
        Mockito.doReturn(Optional.of(auctionBid)).when(auctionBidServiceImpl).findHighestBidItemForAuction(any(), anyLong());

        //Execution
        auctionBidServiceImpl.deleteAuctionBidAndUpdateCurrentBidForItem(auctionBidId, event, user);

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        verify(joinPaymentItemService).deleteByItem(item);
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any());
        verify(auctionBidServiceImpl).findHighestBidItemForAuction(any(), anyLong());

        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBidData.isDeleted());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertEquals(winner.getBidId(), auctionBid.getId());
        assertEquals(winner.getUserId(), auctionBid.getUser().getUserId().longValue());
        assertFalse(winner.isHasPaid());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());

        Item item = itemArgumentCaptor.getValue();
        assertEquals(item.getCurrentBid(), auctionBid.getAmount());
        assertEquals(item.getCurrentHighBidder(), auctionBid.getUser());
    }

    private Winner setWinnerObject(Long itemId, Long userId, Long auctionBidId, Long eventId, Boolean hasPaid) {
        winner = new Winner();
        winner.setItemId(itemId);
        winner.setUserId(userId);
        winner.setBidId(auctionBidId);
        winner.setEventId(eventId);
        winner.setHasPaid(hasPaid);
        return winner;
    }

    @Test
    void test_handleItemCurrentBidChange_withAuctionBidMapEmptyAndModuleStatusWinnerAnnounced() {

        //setup
        winner = setWinnerObject(item.getId(), user.getUserId(), auctionBid.getId(), event.getEventId(), false);
        auction = getAuctionStatusAndWinnerNotificationEnable(ModuleStatus.WINNER_ANNOUNED, true);

        Page<AuctionBid> auctionBidPage = new PageImpl<>(Collections.emptyList());

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any())).thenReturn(auctionBidPage);
        Mockito.doNothing().when(winnerService).delete(any());
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any());
        verify(winnerService).delete(any());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    @Test
    void test_handleItemCurrentBidChange_withAuctionBidMapAndModuleStatusNotWinnerAnnouncedAndWinnerNotificationEnableFalse() throws IOException {

        //setup
        winner = setWinnerObject(item.getId(), user.getUserId(), auctionBid.getId(), event.getEventId(), false);
        auction = getAuctionStatusAndWinnerNotificationEnable(ModuleStatus.EXTENDED_BIDDING, false);

        auctionBid.setAmount(100d);
        auctionBid.setUser(user);

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        Mockito.doNothing().when(winnerService).delete(any());
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.of(auctionBid));

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(winnerService).delete(any());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(updatedItem.getCurrentBid(), auctionBid.getAmount());
        assertEquals(updatedItem.getCurrentHighBidder().getFirstName(), auctionBid.getUser().getFirstName());
    }

    private Auction getAuctionStatusAndWinnerNotificationEnable(ModuleStatus moduleStatus, Boolean winnerNotificationEnable) {
        auction.setAuctionStatus(moduleStatus);
        auction.setWinnerNotificationEnable(winnerNotificationEnable);
        return auction;
    }

    @Test
    void test_handleItemCurrentBidChange_withIsDeletingBidIsWinnerBidEmptyAndAuctionStatusNotWinnerAnnouncedAndWinnersEmptyAndWinnerNotificationEnableTrue() {

        //setup
        winner = setWinnerObject(item.getId(), user.getUserId(), 2L, event.getEventId(), false);
        auction = getAuctionStatusAndWinnerNotificationEnable(ModuleStatus.EXTENDED_BIDDING, true);
        itemAndBidAmount = new ItemAndBidAmount();

        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        Map<Long, List<AuctionBid>> auctionBidMap = new HashMap<>();
        auctionBidMap.put(item.getId(), auctionBidList);

        Map<User, List<ItemAndBidAmount>> itemAndBidAmountMap = new HashMap<>();
        itemAndBidAmountMap.put(user, Collections.singletonList(itemAndBidAmount));

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    @Test
    void test_handleItemCurrentBidChange_withIsDeletingBidIsWinnerBidEmptyAndAuctionStatusWinnerAnnouncedAndWinnerNotificationEnableTrueAndItemAndBidAmountEmpty() throws IOException {

        //setup
        winner = setWinnerObject(item.getId(), user.getUserId(), 2L, event.getEventId(), false);
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
        auction.setWinnerNotificationEnable(true);

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    @Test
    void test_handleItemCurrentBidChange_withIsDeletingBidIsWinnerBidEmptyAndAuctionStatusWinnerAnnouncedAndWinnerNotificationEnableTrueAndItemAndBidAmount() {

        //setup
        winner = setWinnerObject(item.getId(), user.getUserId(), 2L, event.getEventId(), false);
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
        auction.setWinnerNotificationEnable(true);
        itemAndBidAmount = new ItemAndBidAmount();

        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        Map<Long, List<AuctionBid>> auctionBidMap = new HashMap<>();
        auctionBidMap.put(item.getId(), auctionBidList);

        Map<User, List<Item>> winners = new HashMap<>();
        winners.put(user, Collections.singletonList(item));
        Map<User, List<ItemAndBidAmount>> itemAndBidAmountMap = new HashMap<>();
        itemAndBidAmountMap.put(user, Collections.singletonList(itemAndBidAmount));

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());


        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    private AuctionBid setAuctionBidObject(Long id, boolean bidHasPaid, Boolean bidRefunded, User user, Double amount, Item item) {
        auctionBid = new AuctionBid();
        auctionBid.setId(id);
        auctionBid.setUser(user);
        auctionBid.setItem(item);
        auctionBid.setAmount(amount);
        auctionBid.setBidTime(DateUtils.getCurrentDate());
        auctionBid.setHasPaid(bidHasPaid);
        auctionBid.setRefunded(bidRefunded);

        return auctionBid;
    }

    @Test
    void test_findByItemOrderByAmountDesc(){

        //setup
        Pageable pageable = PageRequest.of(0, 10);
        List<Long> auctionBidIds = new ArrayList<>();
        auctionBidIds.add(auctionBid.getId());

        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);
        List<AuctionBid> auctionBidPage = new ArrayList<>(auctionBidList);

        //mock
        when(auctionBidRepository.findByItemOrderByAmountDesc(any(), anyList())).thenReturn(auctionBidPage);

        //Execution
        List<AuctionBid> auctionBids = auctionBidServiceImpl.findByItemOrderByAmountDesc(item, auctionBidIds);

        //Assertion
        assertEquals(auctionBids.get(0).getId(), auctionBidIds.get(0).longValue());
        verify(auctionBidRepository).findByItemOrderByAmountDesc(any(), anyList());
    }

    @Test
    void test_handleItemCurrentBidChange_withWinnerNotificationEnableAndThrowExceptionForUnableSendMailAndMessageToWinner() throws IOException {

        //setup
        user.setEmail("<EMAIL>");
        user.setPhoneNumber(9898989898L);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);
        auctionBid.setItem(item);
        winner = setWinnerObject(item.getId(), user.getUserId(), auctionBid.getId(), event.getEventId(), false);
        auction = getAuctionStatusAndWinnerNotificationEnable(ModuleStatus.WINNER_ANNOUNED, true);

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());
        Mockito.doReturn(Optional.of(auctionBid)).when(auctionBidServiceImpl).findNextHighestBidNotAsWinner(anyLong(), any(), anyList());
        Mockito.doThrow(new RuntimeException()).when(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        Mockito.doThrow(new RuntimeException()).when(winnerService).sendAuctionItemsWinnerText(anyList(), any(), any());

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertFalse(winner.isHasPaid());
        assertEquals(winner.getBidId(), auctionBid.getId());
        assertEquals(winner.getUserId(), user.getUserId().longValue());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    @Test
    void test_handleItemCurrentBidChange_withWinnerNotificationEnableAndUserEmailBlank() throws IOException {

        //setup
        user.setEmail("");
        user.setPhoneNumber(0L);
        auctionBid.setUser(user);
        auctionBid.setAmount(100d);
        auctionBid.setItem(item);
        winner = setWinnerObject(item.getId(), user.getUserId(), auctionBid.getId(), event.getEventId(), false);
        auction = getAuctionStatusAndWinnerNotificationEnable(ModuleStatus.WINNER_ANNOUNED, true);

        //mock
        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean())).thenReturn(Optional.empty());
        Mockito.doReturn(Optional.of(auctionBid)).when(auctionBidServiceImpl).findNextHighestBidNotAsWinner(anyLong(), any(), anyList());
        /*Mockito.doThrow(Exception.class).when(winnerService).sendWinningBidderNotificationMail(any(), any(), anyList(), anyBoolean(), any(), anyMap());
        Mockito.doThrow(Exception.class).when(winnerService).sendAuctionItemsWinnerText(anyList(), any(), any());*/

        //Execution
        auctionBidServiceImpl.handleItemCurrentBidChange(auctionBid.getId(), event, item);

        //Assertion
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemAndAuctionIdAndIsRefundedOrderByAmountDesc(any(), anyLong(), anyBoolean());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertFalse(winner.isHasPaid());
        assertEquals(winner.getBidId(), auctionBid.getId());
        assertEquals(winner.getUserId(), user.getUserId().longValue());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());
        Item updatedItem = itemArgumentCaptor.getValue();
        assertEquals(0,updatedItem.getCurrentBid().intValue());
        assertNull(updatedItem.getCurrentHighBidder());
    }

    @Test
    void test_deleteAuctionBidAndUpdateCurrentBidForItem_withAuctionBidPaidFalseAndRefundedFalseAndWinnerNotificationDisable() throws IOException {

        //setup
        user.setPhoneNumber(9898989898L);
        user.setEmail("<EMAIL>");
        auctionBid = setAuctionBidObject(auctionBidId, false, false, user, 300d, item);
        winner = new Winner();
        winner.setBidId(auctionBidId);
        winner.setUserId(user.getUserId());
        auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
        auction.setWinnerNotificationEnable(false);

        Page<AuctionBid> auctionBidPage = new PageImpl<>(Collections.singletonList(auctionBid));

        //mock
        when(auctionBidRepository.findById(anyLong())).thenReturn(Optional.of(auctionBid));
        Mockito.doNothing().when(joinPaymentItemService).deleteByItem(item);

        when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(Collections.singletonList(winner));
        when(auctionService.find(anyLong())).thenReturn(auction);
        when(auctionBidRepository.findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any())).thenReturn(auctionBidPage);
        Mockito.doReturn(Optional.of(auctionBid)).when(auctionBidServiceImpl).findHighestBidItemForAuction(any(), anyLong());

        //Execution
        auctionBidServiceImpl.deleteAuctionBidAndUpdateCurrentBidForItem(auctionBidId, event, user);

        //Assertion
        verify(auctionBidRepository).findById(anyLong());
        verify(joinPaymentItemService).deleteByItem(item);
        verify(winnerService).findByModuleTypeAndItemId(any(), anyLong());
        verify(auctionService).find(anyLong());
        verify(auctionBidRepository).findFirstByItemIdAndBidIdNotInAndIsNotRefunded(anyLong(), anyLong(), anyList(), any());
        verify(auctionBidServiceImpl).findHighestBidItemForAuction(any(), anyLong());

        ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
        verify(auctionBidRepository).save(auctionBidArgumentCaptor.capture());

        AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBidData.isDeleted());

        ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
        verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());

        Winner winner = winnerArgumentCaptor.getValue();
        assertEquals(winner.getBidId(), auctionBid.getId());
        assertEquals(winner.getUserId(), auctionBid.getUser().getUserId().longValue());
        assertFalse(winner.isHasPaid());

        ArgumentCaptor<Item> itemArgumentCaptor = ArgumentCaptor.forClass(Item.class);
        verify(itemService).update(anyObject(),itemArgumentCaptor.capture());

        Item item = itemArgumentCaptor.getValue();
        assertEquals(item.getCurrentBid(), auctionBid.getAmount());
        assertEquals(item.getCurrentHighBidder(), auctionBid.getUser());
    }

    @Test
    void test_getAuctionBidByIdsAndNotRefunded(){
        //setup
        List<Long> bidIds = new ArrayList<>();
        bidIds.add(1L);

        List<AuctionBid> auctionBidList = new ArrayList<>();
        auctionBidList.add(auctionBid);

        //mock
        when(auctionBidRepository.findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList())).thenReturn(auctionBidList);

        //Execution
        List<AuctionBid>  auctionBids = auctionBidServiceImpl.getAuctionBidByIdsAndNotRefunded(bidIds);

        //Assertion
        assertEquals(auctionBids.get(0).getId(), auctionBidId.longValue());
    }

    @Test
    void test_payAuctionBidsManually_throwException(){
        
        //mock
        when(auctionBidRepository.findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList())).thenReturn(Collections.emptyList());

        //Execution
        Exception exception = assertThrows(NotFoundException.class,
                () -> auctionBidServiceImpl.payAuctionBidsManualy(Collections.emptyList(), user, event));

        //Assertion
        verify(auctionBidRepository).findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList());
        assertEquals(NotFoundException.NotFound.BID_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_payAuctionBidsManually_withPaymentIsPresentAndWinnerListEmpty(){
        //setup
        auctionBid.setUser(user);
        auctionBid.setItem(item);
        winner = new Winner();
        winner.setItemId(1L);
        List<Winner> winnerList = new ArrayList<>();
        Payment payment = new Payment();

        //mock
        when(auctionBidRepository.findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList())).thenReturn(Collections.singletonList(auctionBid));
        when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
        when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(winnerList);

        //Execution
        auctionBidServiceImpl.payAuctionBidsManualy(Collections.emptyList(), user, event);

        //Assertion
        verify(auctionBidRepository).findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList());
        verify(paymentService).findByUserIdAndEventId(anyLong(), anyLong());
        verify(winnerService).findAllWinnerByBidIds(anyList());

        ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
        verify(paymentService).save(paymentArgumentCaptor.capture());

        Payment paymentData = paymentArgumentCaptor.getValue();
        assertEquals(paymentData.getStaffUserId(), user.getUserId().longValue());

        ArgumentCaptor<JoinPaymentItem> joinPaymentItemArgumentCaptor = ArgumentCaptor.forClass(JoinPaymentItem.class);
        verify(joinPaymentItemService).save(joinPaymentItemArgumentCaptor.capture());

        JoinPaymentItem joinPaymentItem = joinPaymentItemArgumentCaptor.getValue();
        assertEquals(Constants.MANUAL_TRANSACTION_TYPE,joinPaymentItem.getTransactionType());
        assertEquals(joinPaymentItem.getItem().getId(), auctionBid.getItem().getId());
        assertEquals(joinPaymentItem.getPayment().getStaffUserId(), user.getUserId().longValue());

        Class<ArrayList<AuctionBid>> auctionBid = (Class<ArrayList<AuctionBid>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<AuctionBid>> auctionBidArgumentCaptor = ArgumentCaptor.forClass(auctionBid);
        verify(auctionBidRepository).saveAll(auctionBidArgumentCaptor.capture());

        List<AuctionBid> auctionBids = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBids.get(0).isHasPaid());
        assertEquals(auctionBids.get(0).getStaffUserId().getUserId(), user.getUserId());
    }

    @Test
    void test_payAuctionBidsManually_withPaymentIsNotPresentAndWinnerAndAuctionBid(){

        //setup
        auctionBid.setUser(user);
        auctionBid.setItem(item);
        winner = new Winner();
        winner.setItemId(1L);
        List<Winner> winnerList = new ArrayList<>();
        winnerList.add(winner);

        //mock
        when(auctionBidRepository.findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList())).thenReturn(Collections.singletonList(auctionBid));
        when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());
        when(winnerService.findAllWinnerByBidIds(anyList())).thenReturn(winnerList);

        //Execution
        auctionBidServiceImpl.payAuctionBidsManualy(Collections.emptyList(), user, event);

        //Assertion
        verify(auctionBidRepository).findByAuctionIdsAndRefundedOrderByItemIdDescAmountDesc(anyList());
        verify(paymentService).findByUserIdAndEventId(anyLong(), anyLong());
        verify(winnerService).findAllWinnerByBidIds(anyList());

        ArgumentCaptor<Payment> paymentArgumentCapto = ArgumentCaptor.forClass(Payment.class);
        verify(paymentService, times(2)).save(paymentArgumentCapto.capture());

        Payment paymentData = paymentArgumentCapto.getValue();
        assertEquals(paymentData.getStaffUserId(), user.getUserId().longValue());

        ArgumentCaptor<JoinPaymentItem> joinPaymentItemArgumentCaptor = ArgumentCaptor.forClass(JoinPaymentItem.class);
        verify(joinPaymentItemService).save(joinPaymentItemArgumentCaptor.capture());

        JoinPaymentItem joinPaymentItem = joinPaymentItemArgumentCaptor.getValue();
        assertEquals(Constants.MANUAL_TRANSACTION_TYPE,joinPaymentItem.getTransactionType());
        assertEquals(joinPaymentItem.getItem().getId(), auctionBid.getItem().getId());
        assertEquals(joinPaymentItem.getPayment().getStaffUserId(), user.getUserId().longValue());

        Class<ArrayList<AuctionBid>> auctionBid = (Class<ArrayList<AuctionBid>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<AuctionBid>> auctionBidArgumentCaptor = ArgumentCaptor.forClass(auctionBid);
        verify(auctionBidRepository).saveAll(auctionBidArgumentCaptor.capture());

        List<AuctionBid> auctionBids = auctionBidArgumentCaptor.getValue();
        assertTrue(auctionBids.get(0).isHasPaid());
        assertEquals(auctionBids.get(0).getStaffUserId().getUserId(), user.getUserId());

        Class<ArrayList<Winner>> winner = (Class<ArrayList<Winner>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<Winner>> winnerArgumentCaptor = ArgumentCaptor.forClass(winner);
        verify(winnerService).saveAllWinner(winnerArgumentCaptor.capture());

        List<Winner> winners = winnerArgumentCaptor.getValue();
        assertTrue(winners.get(0).isHasPaid());
    }
}