package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.billing.chargebee.dto.BillingSummerDto;
import com.accelevents.billing.chargebee.dto.EventCreditDistributionDto;
import com.accelevents.billing.chargebee.repo.ChargebeeEventCreditsRepoService;
import com.accelevents.billing.chargebee.repo.ChargebeeEventUsagesRepoService;
import com.accelevents.billing.chargebee.repo.ChargebeePlanRepoService;
import com.accelevents.billing.chargebee.repo.EventPlanConfigRepoService;
import com.accelevents.billing.chargebee.repositories.ChargebeeEventCreditsService;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.common.dto.OrganizerDto;
import com.accelevents.common.dto.WhiteLabelDto;
import com.accelevents.configuration.ChargebeeConfiguration;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.ProcessingStatus;
import com.accelevents.domain.enums.SSOType;
import com.accelevents.domain.enums.UsagesType;
import com.accelevents.dto.*;
import com.accelevents.enums.BillingType;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.enums.StaffRole;
import com.accelevents.exceptions.ConflictException;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.repositories.EventTicketsRepository;
import com.accelevents.repositories.PayFlowConfigurationRepository;
import com.accelevents.repositories.TicketingRepository;
import com.accelevents.repositories.WhiteLabelRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROWhiteLabelService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.repo.CacheService;
import com.accelevents.services.repo.helper.ChangeHistoryRepoService;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.services.repo.helper.WhiteLabelRepoService;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.ticketing.dto.TicketingModuleDTO;
import com.accelevents.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.client.AutoConfigureWebClient;
import org.springframework.data.domain.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@AutoConfigureWebClient
public class WhiteLabelServiceImplTest {

    @Spy
    @InjectMocks
    private WhiteLabelServiceImpl whiteLabelService = new WhiteLabelServiceImpl();
    @Mock
    private WhiteLabelRepository whiteLabelRepository;
    @Mock
    private EventService eventService;
    @Mock
    private EventDesignDetailService eventDesignDetailService;
    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;
    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
    @Mock
    private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private ROEventService roEventService;
    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
    @Mock
    private AllRequiresAttributesService allRequiresAttributesService;
    @Mock
    private CustomEmailService customEmailService;
    @Mock
    private PayFlowConfigurationRepository payFlowConfigurationRepository;
    @Mock
    private ChargebeeService chargebeeService;
    @Mock
    private TicketingService ticketingService;
    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;
    @Mock
    private ImageConfiguration imageConfiguration;
    @Mock
    private ChargebeePlanService chargebeePlanService;
    @Mock
    private TicketingHelperService ticketingHelperService;
    @Mock
    private TicketingStatisticsService ticketingStatisticsService;
    @Mock
    private CacheService cacheService;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private SSOUserService ssoUserService;
    @Mock
    private StripeConfiguration stripeConfiguration;
    @Mock
    private WhiteLabelTransferService whiteLabelTransferService;
    @Mock
    private WhiteLabelRepoService whiteLabelRepoService;
    @Mock
    private ROWhiteLabelService roWhiteLabelService;
    @Mock
    private CacheStoreService cacheStoreService;
    @Mock
    private MuxService muxService;
    @Mock
    private ChargebeeEventCreditsService chargebeeEventCreditsService;
    @Mock
    private ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;
    @Mock
    private ChangeHistoryRepoService changeHistoryRepoService;
    @Mock
    private ChargebeePaymentService chargebeePaymentService;
    @Mock
    private ChargebeePlanRepoService chargebeePlanRepoService;
    @Mock
    private ChargebeeEventUsagesRepoService chargebeeEventUsagesRepoService;
    @Mock
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Mock
    private TicketingRepository ticketingRepository;
    @Mock
    private CheckInAuditLogRepoService checkInAuditLogRepoService;
    @Mock
    private ChargebeeConfiguration chargebeeConfiguration;
    @Mock
    private ApiKeyService apiKeyService;
    @Mock
    private ChargebeeBillingSummeryService chargebeeBillingSummeryService;
    @Mock
    private OrganizerService organizerService;
    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private EventPlanConfigService eventPlanConfigService;

    @Mock
    private WhiteLabelSettingsService whiteLabelSettingsService;
    @Mock
    private ROWhiteLabelSettingsService roWhiteLabelSettingsService;

    @Mock
    IntegrationService integrationService;

    @Mock
    TrayIntegrationService trayIntegrationService;

    private WhiteLabel whiteLabel;
    private Event event;
	private User user;
	private TransactionFeeConditionalLogic transactionFeeConditionalLogic;
	private Stripe stripe;
	private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
	private CustomEmail customEmail;
	private PayFlowConfigurations payFlowConfigurations;
	private Staff staff;
	private Timestamp timestamp;
	private Ticketing ticketing;
	private PlanConfig planConfig;
	private EventDesignDetail eventDesignDetail;
	private TicketingModuleDTO ticketingModuleDTO;
	private EventTicketRevenueDto eventTicketRevenueDto;
	private WhiteLabelSuperAdminSettingsDTO whiteLabelSuperAdminSettingsDTO;
	private List<WhitelabelSSOConfigurationDTO> ssoConfigurations;
	private WLBasicDetailsDto wlBasicDetailsDto;
	private WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto;
	private ChargebeeEventCredits chargebeeEventCredits;
	private ChargebeeEventUsages chargebeeEventUsages;
	private EventPlanConfig eventPlanConfig;
	private ClientApiKey clientApiKey;
    private PageSizeSearchObj pageSizeSearchObj;
    Map<Long, TicketingModuleDTO> ticketingModuleDtosMap=new HashMap<>();

    @BeforeEach
    void setUp() throws Exception{

        event = EventDataUtil.getEvent();
        event.setSilentAuctionEnabled(true);
        event.setRaffleEnabled(true);
        event.setTicketingEnabled(true);
        event.setDonationEnabled(false);
        event.setCauseAuctionEnabled(true);
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        ticketing = EventDataUtil.getTicketing(event);

        whiteLabel = new WhiteLabel(1L, "test-wl");
        whiteLabel.setDefaultItemImage("default_item_image");
        whiteLabel.setLogoImage("logo_image");
        whiteLabel.setHeaderLogoImage("header_logo_image");
        whiteLabel.setHeaderColor("header_color");
        whiteLabel.setHeaderFontColor("header_font_color");
        whiteLabel.setBannerImage("banner_image");
        whiteLabel.setFirmName("firm_name");
        whiteLabel.setSupportEmail("support_email");
        whiteLabel.setIntercomActivated(false);
        whiteLabel.setHelpCenterActivated(false);
        whiteLabel.setPoweredByAeActivated(false);
        whiteLabel.setMarketingOptInChecked(false);
        whiteLabel.setMarketingOptInHidden(false);
        whiteLabel.setBiillingPageEnabled(false);
        whiteLabel.setAuctionsActivated(false);
        whiteLabel.setRafflesActivated(false);
        whiteLabel.setCauseAuctionActivated(false);
        whiteLabel.setTicketingActivated(true);
        whiteLabel.setDonationAmountA(5);
        whiteLabel.setDonationAmountB(10);
        whiteLabel.setDonationAmountC(15);
        whiteLabel.setDonationAmountD(20);
        whiteLabel.setGetStarted("get_started");
        whiteLabel.setFacebookShare("facebook_share");
        whiteLabel.setTwitterShare("twitter_share");
        whiteLabel.setInstagramShare("instagram_share");
        whiteLabel.setHideFundRaisingModuleToggle(false);
        whiteLabel.setSubscriptionId("test_subScriptionId");
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName(PlanConfigNames.FREE_PLAN.getName());
        whiteLabel.setPlanConfig(planConfig);

        wlBasicDetailsDto = new WLBasicDetailsDto();
        wlBasicDetailsDto.setWlId(1L);
        wlBasicDetailsDto.setHostBaseURL("host_url");
        wlBasicDetailsDto.setPlaybackRestrictionID("playback_id");
        wlBasicDetailsDto.setWlURL("test-wl");

        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();
        whiteLabelBillingSettingsDto.setWhiteLabelURL("test-wl");

        whiteLabelSuperAdminSettingsDTO = new WhiteLabelSuperAdminSettingsDTO();
        whiteLabelSuperAdminSettingsDTO.setDefaultItemImage("default_item_image");
        whiteLabelSuperAdminSettingsDTO.setLogoImage("logo_image");
        whiteLabelSuperAdminSettingsDTO.setHeaderLogoImage("header_logo_image");
        whiteLabelSuperAdminSettingsDTO.setHeaderColor("header_color");
        whiteLabelSuperAdminSettingsDTO.setHeaderFontColor("header_font_color");
        whiteLabelSuperAdminSettingsDTO.setBannerImage("banner_image");
        whiteLabelSuperAdminSettingsDTO.setFirmName("firm_name");
        whiteLabelSuperAdminSettingsDTO.setEmail("support_email");
        whiteLabelSuperAdminSettingsDTO.setIntercomActivated(false);
        whiteLabelSuperAdminSettingsDTO.setHelpCenterActivated(false);
        whiteLabelSuperAdminSettingsDTO.setPoweredByAeActivated(false);
        whiteLabelSuperAdminSettingsDTO.setMarketingOptInChecked(false);
        whiteLabelSuperAdminSettingsDTO.setMarketingOptInEnable(false);
        whiteLabelSuperAdminSettingsDTO.setBillingPageEnable(false);
        whiteLabelSuperAdminSettingsDTO.setGetStarted("get_started");
        whiteLabelSuperAdminSettingsDTO.setFacebookShare("facebook_share");
        whiteLabelSuperAdminSettingsDTO.setTwitterShare("twitter_share");
        whiteLabelSuperAdminSettingsDTO.setInstagramShare("instagram_share");
        whiteLabelSuperAdminSettingsDTO.setHideFundRaisingModuleToggle(false);
        whiteLabelSuperAdminSettingsDTO.setSubscriptionId("test_subScriptionId");
        whiteLabelSuperAdminSettingsDTO.setWhiteLabelUrl("wl_url");

        WhitelabelSSOConfigurationDTO ssoConfigurationDTO1 = new WhitelabelSSOConfigurationDTO();
        ssoConfigurationDTO1.setId(1L);
        ssoConfigurationDTO1.setActive(true);
        ssoConfigurationDTO1.setClientId("client-id");
        ssoConfigurationDTO1.setSsoType(SSOType.SAML);
        ssoConfigurationDTO1.setTargetUrl("target-url");
        ssoConfigurationDTO1.setWhiteLabelUrl("test-wl");


        WhitelabelSSOConfigurationDTO ssoConfigurationDTO2 = new WhitelabelSSOConfigurationDTO();
        ssoConfigurationDTO2.setId(2L);
        ssoConfigurationDTO2.setActive(false);
        ssoConfigurationDTO2.setClientId("client-id");
        ssoConfigurationDTO2.setSsoType(SSOType.SAML);
        ssoConfigurationDTO2.setTargetUrl("target-url");
        ssoConfigurationDTO2.setWhiteLabelUrl("test-wl");

        ssoConfigurations = Arrays.asList(ssoConfigurationDTO1, ssoConfigurationDTO2);

        timestamp = new Timestamp(1);

        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        transactionFeeConditionalLogic.setEvent(event);
        transactionFeeConditionalLogic.setWhiteLabel(whiteLabel);

        stripe = new Stripe();
        stripe.setEvent(event);
        stripe.setAccessToken("sk_test_abSrkfVraozPahMjTuzWM0ya");
        stripe.setLivemode(false);
        stripe.setRefreshToken("rt_9nlm7w616mnKAvHg7akrapUVJljLNbab0Tlvx0atLgfQtQ1c");
        stripe.setTokenType("bearer");
        stripe.setStripePublishableKey("pk_test_Ifs6ZfB0PKZz1qRVW5HmYZTL");
        stripe.setStripeUserId("acct_19UFGpE8xoUg8dJF");
        stripe.setScope("read_write");
        stripe.setCode("ac_9nsFr39zhFLhT56HXxB1dNqop4HgFHnn");
        stripe.setAccountSource("Oauth");
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketHolderRequiredAttributes.setName("Milan");
        ticketHolderRequiredAttributes.setEventid(event);
        ticketHolderRequiredAttributes.setId(1L);

        customEmail = new CustomEmail();

        payFlowConfigurations = new PayFlowConfigurations();
        this.planConfig = new PlanConfig();
        this.planConfig.setId(10);

        staff = new Staff();
        staff.setUser(user);
        staff.setEvent(event);
        staff.setRole(StaffRole.whitelabeladmin);
        staff.setId(1);
        staff.setEventId(1L);
        staff.setUserId(1L);

        eventDesignDetail = new EventDesignDetail();

        ticketingModuleDTO = new TicketingModuleDTO();
        ticketingModuleDTO.setEventStartDate(new Date());
        ticketingModuleDtosMap.put(1L,ticketingModuleDTO);
        eventTicketRevenueDto = new EventTicketRevenueDto();

        ticketing = new Ticketing();

        chargebeeEventCredits = new ChargebeeEventCredits();
        chargebeeEventCredits.setRemainingFreeCredits(100L);

        chargebeeEventUsages = new ChargebeeEventUsages();
        chargebeeEventUsages.setEventId(1L);
        chargebeeEventUsages.setEvent(event);
        chargebeeEventUsages.setChargebeeUsagesId("chargebee-id");
        chargebeeEventUsages.setPlanId(1L);
        chargebeeEventUsages.setPlanConfig(this.planConfig);
        chargebeeEventUsages.setWhiteLabel(whiteLabel);
        chargebeeEventUsages.setQuantity(100L);
        chargebeeEventUsages.setProcessingStatus(ProcessingStatus.SUCCESS);

        eventPlanConfig = new EventPlanConfig();
        eventPlanConfig.setPlanConfig(this.planConfig);
        eventPlanConfig.setEventId(1L);
        eventPlanConfig.setEvent(event);
        eventPlanConfig.setId(1);

        clientApiKey = new ClientApiKey();
        clientApiKey.setUserId(1L);
        clientApiKey.setUser(user);
        clientApiKey.setApiKey("test-api-key");
        clientApiKey.setAccessToken("access-tocken");
        clientApiKey.setWhiteLabel(whiteLabel);
        clientApiKey.setWhiteLabelId(1L);

    }

    private void addNewWhiteLableEventSetUps(){
        EventDesignDetail eventDesignDetail = new EventDesignDetail();
    }

    @Test
    void test_addNewWhiteLabelEvent_createEventWithCorrectParams(){

        whiteLabel.setManualPayout(true);
        whiteLabel.setEventTicketOrderConfirmation("test");
        whiteLabel.setEventTicketOrderConfirmationHeader("test");
        event.setTicketingEnabled(true);

        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        doReturn(whiteLabel).when(whiteLabelService).findWhiteLabel(anyString());
        doReturn(eventDesignDetail).when(eventDesignDetailService).findByEvent(any());
        doNothing().when(eventDesignDetailService).save(any());
        doNothing().when(userService).addUserAsWhiteLabelStaff(any(), any(), any());
        when(stripeService.findByWhiteLabelWithConnectedAccountEmail(any())).thenReturn(stripe);
        when(ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(any(), any())).thenReturn(ticketHolderRequiredAttributes);
        doNothing().when(allRequiresAttributesService).save(any());
        when(customEmailService.getCustomEmailByEventId(anyLong())).thenReturn(Optional.ofNullable(customEmail));
        when(payFlowConfigurationRepository.findPayFlowConfigurationsByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(payFlowConfigurations));
        when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(roStaffService.findByEvent(any())).thenReturn(Collections.singletonList(staff));
        doNothing().when(stripeService).save(any());
        doNothing().when(eventRepoService).save(any());
       
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(chargebeeService.getSubscriptionEndDate(anyString())).thenReturn(timestamp);
        when(roUserService.getUserRoles(any())).thenReturn(Collections.singletonList("ROLE_ADMIN"));
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(stripe);
        when(eventService.createEventAndCloneStripeOfWL(anyBoolean(), anyBoolean(), anyBoolean(), anyString(), isA(WhiteLabel.class), anyList(), isA(Stripe.class), nullable(String.class))).thenReturn(event);
        when(transactionFeeConditionalLogicService.createRecordForWhiteLabel(any())).thenReturn(true);
        when(ticketingService.findByEvent(any())).thenReturn(ticketing);
        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));

        when(roWhiteLabelSettingsService.isEnabledCaptchaSettingForWhiteLabel(anyLong())).thenReturn(Boolean.FALSE);



        whiteLabelService.addNewEvent(whiteLabel, user);

        ArgumentCaptor<EventDesignDetail> captor = ArgumentCaptor.forClass(EventDesignDetail.class);

        verify(eventDesignDetailService).save(captor.capture());
        verify(whiteLabelService, times(1)).findWhiteLabel("test-wl");
        verify(eventService).createEventAndCloneStripeOfWL(false, false, false, user.getEmail(), whiteLabel, Collections.singletonList("ROLE_ADMIN"), stripe, null);
        verify(userService).addUserAsWhiteLabelStaff(user, whiteLabel, event);

        EventDesignDetail edd = captor.getValue();
        assertEquals(this.whiteLabel.getLogoImage(), edd.getLogoImage());
        assertEquals(this.whiteLabel.getHeaderLogoImage(), edd.getHeaderLogoImage());
        assertEquals(this.whiteLabel.getBannerImage(), edd.getBannerImage());
        assertEquals(this.whiteLabel.isPoweredByAeActivated(), edd.isPoweredByAeActivated());
        assertEquals(this.whiteLabel.isMarketingOptInChecked(), edd.isMarketingOptInChecked());
        assertEquals(this.whiteLabel.isMarketingOptInHidden(), edd.isMarketingOptInHidden());
        assertEquals(this.whiteLabel.isBiillingPageEnabled(), edd.isBiillingPageEnabled());
        assertEquals(this.whiteLabel.isIntercomActivated(), edd.isIntercomActivated());
        assertEquals(this.whiteLabel.isHelpCenterActivated(), edd.isHelpCenterActivated());
        assertEquals(this.whiteLabel.getHeaderColor(), edd.getHeaderColor());
        assertEquals(this.whiteLabel.getHeaderFontColor(), edd.getHeaderFontColor());
        assertEquals(this.whiteLabel.isHideFundRaisingModuleToggle(), edd.isHideFundRaisingModuleToggle());
        assertEquals(this.whiteLabel.isShowEnableModulePopup(), edd.isShowEnableModulePopup());
    }

    @Test
    void test_addNewWhiteLabelEvent_updateEventDesignDetailsAsPerWL(){

        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        doReturn(whiteLabel).when(whiteLabelService).findWhiteLabel(anyString());
        when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
        when(ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(any(), any())).thenReturn(ticketHolderRequiredAttributes);
        when(customEmailService.getCustomEmailByEventId(anyLong())).thenReturn(Optional.ofNullable(customEmail));
        List<String> roles = Collections.singletonList("ROLE_ADMIN");
        when(roUserService.getUserRoles(any())).thenReturn(roles);
        when(eventService.createEvent(false, false, false, user.getEmail(), whiteLabel, roles, user.getUserId(), null)).thenReturn(event);

        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(roWhiteLabelSettingsService.isEnabledCaptchaSettingForWhiteLabel(anyLong())).thenReturn(Boolean.FALSE);

        whiteLabelService.addNewEvent(whiteLabel, user);

        ArgumentCaptor<EventDesignDetail> captor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService).save(captor.capture());

        verify(eventService).createEvent(false, false, false, user.getEmail(), whiteLabel, roles, user.getUserId(), null);
        EventDesignDetail edd = captor.getValue();
        assertEquals(this.whiteLabel.getLogoImage(), edd.getLogoImage());
        assertEquals(this.whiteLabel.getHeaderLogoImage(), edd.getHeaderLogoImage());
        assertEquals(this.whiteLabel.getBannerImage(), edd.getBannerImage());
        assertEquals(this.whiteLabel.isPoweredByAeActivated(), edd.isPoweredByAeActivated());
        assertEquals(this.whiteLabel.isMarketingOptInChecked(), edd.isMarketingOptInChecked());
        assertEquals(this.whiteLabel.isMarketingOptInHidden(), edd.isMarketingOptInHidden());
        assertEquals(this.whiteLabel.isBiillingPageEnabled(), edd.isBiillingPageEnabled());
        assertEquals(this.whiteLabel.isIntercomActivated(), edd.isIntercomActivated());
        assertEquals(this.whiteLabel.isHelpCenterActivated(), edd.isHelpCenterActivated());
        assertEquals(this.whiteLabel.getHeaderColor(), edd.getHeaderColor());
        assertEquals(this.whiteLabel.getHeaderFontColor(), edd.getHeaderFontColor());
        assertEquals(this.whiteLabel.isHideFundRaisingModuleToggle(), edd.isHideFundRaisingModuleToggle());
        assertEquals(this.whiteLabel.isShowEnableModulePopup(), edd.isShowEnableModulePopup());

    }

    @Test
    void test_addNewWhiteLabelEvent_TRANSACTION_CONFIG_NOT_FOUND(){

        Exception exception = assertThrows(NotFoundException.class,
                () -> whiteLabelService.addNewEvent(whiteLabel, user));

        assertEquals(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_addNewWhiteLabelEvent_WHITELABEL_STRIPE_NOT_CONFIGURED(){

        whiteLabel.setManualPayout(true);
        
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> whiteLabelService.addNewEvent(whiteLabel, user));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.WHITELABEL_STRIPE_NOT_CONFIGURED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_addNewWhiteLabelEvent_WHITE_LABEL_NOT_FOUND(){

        EventDesignDetail eventDesignDetail = new EventDesignDetail();
        doReturn(whiteLabel).when(whiteLabelService).findWhiteLabel(anyString());
        doReturn(eventDesignDetail).when(eventDesignDetailService).findByEvent(any());
        doNothing().when(eventDesignDetailService).save(any());

        when(eventService.createEvent(anyBoolean(), anyBoolean(), anyBoolean(), anyString(), any(), any(), any(), nullable(String.class))).thenReturn(event);
        doNothing().when(userService).addUserAsWhiteLabelStaff(any(), any(), any());
        when(stripeService.findByWhiteLabelWithConnectedAccountEmail(any())).thenReturn(stripe);
        when(ticketHolderRequiredAttributesService.findByAttributeValueTypeAndEventIdAndRecurringEventIdIsNull(any(), any())).thenReturn(ticketHolderRequiredAttributes);
        doNothing().when(allRequiresAttributesService).save(any());
        when(customEmailService.getCustomEmailByEventId(anyLong())).thenReturn(Optional.ofNullable(customEmail));




        doNothing().when(eventRepoService).save(any());

        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(null));
        when(roWhiteLabelSettingsService.isEnabledCaptchaSettingForWhiteLabel(anyLong())).thenReturn(Boolean.FALSE);

        Exception exception = assertThrows(NotFoundException.class,
                () -> whiteLabelService.addNewEvent(whiteLabel, user));

        assertEquals(NotFoundException.WhiteLabelNotFound.WHITE_LABEL_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createWhiteLabel_WHITE_LABEL_URL_ALREADY_EXIST(){
        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl("test-wl")).thenReturn(Optional.of(whiteLabel));

        Exception exception = assertThrows(ConflictException.class,
                () -> whiteLabelService.createWhiteLabel("test-wl"));

        assertEquals(ConflictException.ConflictExceptionMsg.WHITE_LABEL_URL_ALREADY_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createWhiteLabel(){

        //setUp
        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(null));
        when(imageConfiguration.getBlackLogo()).thenReturn("test-image");
        when(imageConfiguration.getDefaultItem()).thenReturn("test-default-item");
        when(chargebeePlanService.getFreePlanConfig()).thenReturn(planConfig);

        //assertion
        whiteLabelService.createWhiteLabel("test-wl");
    }

    @Test
    void test_findWhiteLabel_staff_null(){

        //assertion
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(user);
        assertNull(whiteLabel);

    }

    @Test
    void test_findWhiteLabel_whiteLabel_null(){

        when(roStaffService.findByUserAndWhiteLabelIsNotNull(any(), anyList())).thenReturn(Collections.EMPTY_LIST);

        //assertion
        WhiteLabel whiteLabel = whiteLabelService.findWhiteLabel(user);
        assertNull(whiteLabel);
    }

    @Test
    void test_findWhiteLabel(){

        staff.setWhiteLabel(whiteLabel);
        when(roStaffService.findByUserAndWhiteLabelIsNotNull(any(), anyList())).thenReturn(Collections.singletonList(staff));

        //assertion
        WhiteLabel whiteLabelResponse = whiteLabelService.findWhiteLabel(user);
        assertEquals(staff.getWhiteLabel(),whiteLabelResponse);
    }

    @Test
    void test_getFavDirectoryName(){

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));

        //assertion
        String whiteLabelResponse = whiteLabelService.getFavDirectoryName("test-wl");
        assertEquals(whiteLabel.getFaviconDirectory(),whiteLabelResponse);
    }

    @Test
    void test_createNewWLEvent_white_lable_event_not_found(){

        doReturn(null).when(whiteLabelService).findWhiteLabel(anyString());

        Exception exception = assertThrows(NotFoundException.class,
                () -> whiteLabelService.createNewWLEvent("test-wl", user, "test-org", Boolean.FALSE));

        assertEquals(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createNewWLEvent_transaction_config_not_found(){

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.emptyList());

        //assertion
        Exception exception = assertThrows(NotFoundException.class,
                () -> whiteLabelService.createNewWLEvent("test-wl", user, "test-org", Boolean.FALSE));

        assertEquals(NotFoundException.NotFound.TRANSACTION_CONFIG_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createNewWLEvent_whitelable_stipe_not_connected(){

        List<String> staffRoles = Arrays.asList("staff", "admin", "whitelabeladmin");
        whiteLabel.setManualPayout(true);

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(roUserService.getUserRoles(any())).thenReturn(staffRoles);
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(null);

        //assertion
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> whiteLabelService.createNewWLEvent("test-wl", user, "test-org", Boolean.FALSE));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.WHITELABEL_STRIPE_NOT_CONFIGURED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createNewWLEvent_manualPayout(){

        List<String> staffRoles = Arrays.asList("staff", "admin", "whitelabeladmin");
        whiteLabel.setManualPayout(true);

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(roUserService.getUserRoles(any())).thenReturn(staffRoles);
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(stripe);
        when(eventService.createEventAndCloneStripeOfWL(anyBoolean(), anyBoolean(), anyBoolean(),anyString(), any(), anyList(), any(), anyString())).thenReturn(event);

        doReturn(event).when(whiteLabelService).createConditionalLogicRecordAndAttachUser(any(), any(), any(), anyList());

        //assertion
        Event newWLEvent = whiteLabelService.createNewWLEvent("test-wl", user, "test-org", Boolean.FALSE);
        assertEquals(event,newWLEvent);
    }

    @Test
    void test_createNewWLEvent_manualPayout_false(){

        List<String> staffRoles = Arrays.asList("staff", "admin", "whitelabeladmin");
        whiteLabel.setManualPayout(false);

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(transactionFeeConditionalLogicService.getBaseRecordByWhiteLabel(any())).thenReturn(Collections.singletonList(transactionFeeConditionalLogic));
        when(roUserService.getUserRoles(any())).thenReturn(staffRoles);
        when(stripeService.findByWhiteLabelAndDefaultAccount(any(), anyBoolean())).thenReturn(stripe);
        when(eventService.createEventAndCloneStripeOfWL(anyBoolean(), anyBoolean(), anyBoolean(),anyString(), any(), anyList(), any(), anyString())).thenReturn(event);

        doReturn(event).when(whiteLabelService).createConditionalLogicRecordAndAttachUser(any(), any(), any(), anyList());

        //assertion
        Event newWLEvent = whiteLabelService.createNewWLEvent("test-wl", user, "test-org", Boolean.FALSE);
        Event newWLEvent1 = whiteLabelService.createNewWLEvent("test-wl", user, null, Boolean.FALSE);
        assertEquals(event,newWLEvent);
        assertEquals(event,newWLEvent1);
    }

    @Test
    void test_getWhiteLabelEventDetails_futureEvent() {
        // --- arrange ------------------------------------------------------------
        PageSizeSearchObj pageSizeSearchObj = new PageSizeSearchObj(
                0, 10, "", "event_end_date", Sort.Direction.DESC
        );
        Boolean isPastEvent = false;
        Long wlId = whiteLabel.getId();

        Date now       = new Date();
        Date futureDay = new Date(now.getTime() + 24L * 60 * 60 * 1000);

        // now only 12 columns (0–11)
        Object[] row = new Object[12];
        row[0]  = event.getName();
        row[1]  = event.getEventURL();
        row[2]  = futureDay;                          // event_end_date
        row[3]  = "logo.png";                         // logo
        row[4]  = now;                                // event_start_date
        row[5]  = event.getEventId();                 // event_id
        row[6]  = event.getEventFormat().name();      // event_format
        row[7]  = event.getEventListingStatus().name(); // listing_status
        row[8]  = event.getCreatedDate();             // created_date
        row[9]  = event.getBudgetSpent();             // budget_spent
        row[10] = event.getClosingNote();             // closing_note
        row[11] = event.getOrganizerId();             // organizer_id

        Page<Object[]> page = new PageImpl<>(
                Collections.singletonList(row),
                PageRequest.of(0, 10),
                1L
        );

        when(eventService.findEventsByIsPastAndWhiteLabel(
                eq(wlId),
                eq(isPastEvent),
                eq(""),
                any(Pageable.class))
        ).thenReturn(page);

        EventPlanConfig cfg = new EventPlanConfig();
        cfg.setPlanConfig(new PlanConfig());
        when(eventPlanConfigService.findEventPlanConfigMapByEventIds(
                List.of(event.getEventId()))
        ).thenReturn(Map.of(event.getEventId(), cfg));

        when(ticketingStatisticsService.getEventsTicketingRevenue(
                List.of(event.getEventId()))
        ).thenReturn(Map.of(event.getEventId(), eventTicketRevenueDto));

        // --- act ----------------------------------------------------------------
        DataTableResponse resp = whiteLabelService.getWhiteLabelEventDetails(
                whiteLabel,
                isPastEvent,
                pageSizeSearchObj
        );

        // --- assert -------------------------------------------------------------
        assertEquals(1, resp.getRecordsTotal());
        assertEquals(1, resp.getRecordsFiltered());
        assertEquals(1, resp.getData().size());

        EventBasicInfoDto dto = (EventBasicInfoDto) resp.getData().get(0);
        assertEquals(event.getName(),      dto.getEventName());
        assertEquals(futureDay,           dto.getEventEndDate());
        assertEquals("logo.png",          dto.getLogoImage());
        assertEquals(now,                 dto.getEventStartDate());
        assertEquals(event.getCreatedDate(),    dto.getEventCreateDate());
        assertEquals(event.getEventFormat(),    dto.getEventFormat());
        assertEquals(event.getEventListingStatus(), dto.getEventListingStatus());
        assertEquals(event.getBudgetSpent(),     dto.getBudgetSpent());
        assertEquals(event.getClosingNote(),     dto.getClosingNote());
        assertEquals(event.getOrganizerId(),     dto.getOrganizerId());
    }



    @Test
    void test_getWhiteLabelEventDetails_pastEvent() {
        // --- arrange ------------------------------------------------------------
        PageSizeSearchObj pageSizeSearchObj = new PageSizeSearchObj(
                0, 10, "", "event_end_date", Sort.Direction.DESC
        );
        Boolean isPastEvent = true;
        Long wlId = whiteLabel.getId();

        Date now       = new Date();
        Date yesterday = new Date(now.getTime() - 24L * 60 * 60 * 1000);

        // only 12 columns now
        Object[] row = new Object[12];
        row[0]  = event.getName();
        row[1]  = event.getEventURL();
        row[2]  = yesterday;                          // event_end_date
        row[3]  = "logo.png";                         // logoImage
        row[4]  = now;                                // event_start_date
        row[5]  = event.getEventId();                 // event_id
        row[6]  = event.getEventFormat().name();      // event_format
        row[7]  = event.getEventListingStatus().name(); // listing_status
        row[8]  = event.getCreatedDate();             // created_date
        row[9]  = event.getBudgetSpent();             // budget_spent
        row[10] = event.getClosingNote();             // closing_note
        row[11] = event.getOrganizerId();             // organizer_id

        Page<Object[]> page = new PageImpl<>(
                Collections.singletonList(row),
                PageRequest.of(0, 10),
                1L
        );

        when(eventService.findEventsByIsPastAndWhiteLabel(
                eq(wlId),
                eq(isPastEvent),
                eq(""),
                any(Pageable.class))
        ).thenReturn(page);

        EventPlanConfig cfg = new EventPlanConfig();
        cfg.setPlanConfig(new PlanConfig());
        when(eventPlanConfigService.findEventPlanConfigMapByEventIds(
                List.of(event.getEventId()))
        ).thenReturn(Map.of(event.getEventId(), cfg));

        when(ticketingStatisticsService.getEventsTicketingRevenue(
                List.of(event.getEventId()))
        ).thenReturn(Map.of(event.getEventId(), eventTicketRevenueDto));

        // --- act ----------------------------------------------------------------
        DataTableResponse resp = whiteLabelService.getWhiteLabelEventDetails(
                whiteLabel, isPastEvent, pageSizeSearchObj
        );

        // --- assert -------------------------------------------------------------
        assertEquals(1, resp.getRecordsTotal());
        assertEquals(1, resp.getRecordsFiltered());
        assertEquals(1, resp.getData().size());

        EventBasicInfoDto dto = (EventBasicInfoDto) resp.getData().get(0);
        assertEquals(event.getName(),      dto.getEventName());
        assertEquals(yesterday,           dto.getEventEndDate());
        assertEquals("logo.png",          dto.getLogoImage());
        assertEquals(now,                 dto.getEventStartDate());
        assertEquals(event.getCreatedDate(),    dto.getEventCreateDate());
        assertEquals(event.getEventFormat(),    dto.getEventFormat());
        assertEquals(event.getEventListingStatus(), dto.getEventListingStatus());
        assertEquals(event.getBudgetSpent(),     dto.getBudgetSpent());
        assertEquals(event.getClosingNote(),     dto.getClosingNote());
        assertEquals(event.getOrganizerId(),     dto.getOrganizerId());
    }



    @Test
    void test_saveSettings(){

        whiteLabel.setHostBaseUrl("host-url");

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        doNothing().when(cacheService).evictSingleCacheValue(anyString(),anyString());
        when(roEventService.getWhiteLabelEvents(any())).thenReturn(Collections.singletonList(event));
        doNothing().when(clearAPIGatewayCache).clearAPIGwEventCache(any());
        doNothing().when(clearAPIGatewayCache).clearAPIGwWLSettingsHostBaseUrlCache(any());
        when(eventDesignDetailService.findByEventList(anyList())).thenReturn(Collections.singletonList(eventDesignDetail));
        doNothing().when(whiteLabelSettingsService).updateCaptchaSettingForWhiteLabel(anyBoolean(),any(),any());

        whiteLabelService.saveSettings(whiteLabelSuperAdminSettingsDTO,true, user);
        verify(roEventService).getWhiteLabelEvents(whiteLabel);
    }

    @Test
    void test_getAllWhiteLabelUrls(){

        Page<WhiteLabel> pageWhiteLabels = new PageImpl<>(Collections.singletonList(whiteLabel));

        when(whiteLabelRepository.getAllWhiteLabels(anyString(), any())).thenReturn(pageWhiteLabels);

        DataTableResponse allWhiteLabelUrls = whiteLabelService.getAllWhiteLabelUrls(0, 10, "", "", "");
        DataTableResponse allWhiteLabelUrls1 = whiteLabelService.getAllWhiteLabelUrls(0, 10, "", "billingType", "ASC");
        DataTableResponse allWhiteLabelUrls2 = whiteLabelService.getAllWhiteLabelUrls(0, 10, "", "id", "");

        assertEquals(1,allWhiteLabelUrls.getRecordsTotal());
        assertEquals(1,allWhiteLabelUrls1.getRecordsTotal());
        assertEquals(1,allWhiteLabelUrls2.getRecordsTotal());

    }

    @Test
    void test_getWhiteLabelDetailDto_whiteLabel_URL_not_valid(){

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> whiteLabelService.getWhiteLabelDetailDto("&copy@asd;\uF14B"));

        assertEquals("White Label URL is not valid.", exception.getMessage());
    }

    @Test
    void test_getWhiteLabelDetailDto(){

        when(whiteLabelRepository.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));
        when(ssoUserService.getSSOWhiteLabelConfiguration(anyString(), any())).thenReturn(ssoConfigurations);

        WhiteLabelDto whiteLabelDetailDto = whiteLabelService.getWhiteLabelDetailDto("wl-url");

        assertEquals(whiteLabel.getId(),whiteLabelDetailDto.getWhiteLabelId().longValue());
        assertEquals(whiteLabel.getWhiteLabelUrl(),whiteLabelDetailDto.getWhiteLabelUrl());
        assertEquals(whiteLabel.getFirmName(),whiteLabelDetailDto.getFirmName());
    }

    @Test
    void test_connectStripe(){

        when(stripeConfiguration.getAuthorize_URI()).thenReturn("test");
        when(stripeConfiguration.getClientID()).thenReturn("test");

        String s = whiteLabelService.connectStripe(user, whiteLabel, "test-access-token");
        assertTrue(s.contains("test?response_type=code&scope=read_write&client_id=test&state=test-access-token"));
    }

    @Test
    void test_disconnectStripe(){

        when(stripeService.findByWhiteLabel(any())).thenReturn(stripe);
        when(whiteLabelTransferService.isAnyAmountTransferredToWhiteLabel(any())).thenReturn(false);
        doNothing().when(stripeService).save(any());

        whiteLabelService.disconnectStripe( user, whiteLabel);
        verify(stripeService).save(any());

    }

    @Test
    void test_disconnectStripe_can_not_disconnect(){

        when(stripeService.findByWhiteLabel(any())).thenReturn(stripe);
        when(whiteLabelTransferService.isAnyAmountTransferredToWhiteLabel(any())).thenReturn(true);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> whiteLabelService.disconnectStripe( user, whiteLabel));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.WL_FEE_TRANSFERRED_CANNOT_DISCONNECT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_disconnectStripe_stripe_not_connected(){

        stripe.setAccessToken(null);

        when(stripeService.findByWhiteLabel(any())).thenReturn(stripe);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> whiteLabelService.disconnectStripe( user, whiteLabel));

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.STRIPE_ACCOUNT_IS_NOT_CONNECTED_TO_EVENT.getDeveloperMessage(), exception.getMessage());

    }

    @Test
    void test_sendNotificationEmailForCreateDuplicateEvent(){

        List<String> emails = new ArrayList(Collections.singleton("<EMAIL>"));
        whiteLabel.setNotificationEmail("<EMAIL>");

        doNothing().when(sendGridMailPrepareService).whiteLabelEventCreateDuplicatedAdminEmail(any(), any(), anySet(),
                anyString(), anyBoolean(), any(),any());

        whiteLabelService.sendNotificationEmailForCreateDuplicateEvent(whiteLabel,event,event,user);

        assertTrue(true);
    }

    @Test
    void test_getWhiteLabelDetailDataDto(){

        when(whiteLabelRepoService.findByHostBaseUrlIfNotFoundThrowError(anyString())).thenReturn(whiteLabel);

        WhiteLabelDto test = whiteLabelService.getWhiteLabelDtoByHostBaseUrl("test");

        assertEquals(whiteLabel.getFirmName(),test.getFirmName());
        assertEquals(whiteLabel.getWhiteLabelUrl(),test.getWhiteLabelUrl());
    }

    @Test
    void test_getWhiteLabelBasicDetails(){

        when(whiteLabelRepository.getWhiteLabelBasicDetailsDto()).thenReturn(Collections.singletonList(wlBasicDetailsDto));
        when(muxService.getDomainPlayBackRestrictionList(anyList())).thenReturn(Collections.singletonList(wlBasicDetailsDto));
        List<WLBasicDetailsDto> whiteLabelBasicDetails = whiteLabelService.getWhiteLabelBasicDetails();

        whiteLabelBasicDetails.forEach(e->{
            assertEquals(wlBasicDetailsDto,e);
        });
    }

    @Test
    void test_getWhiteLabelBasicDetails_empty(){

        when(whiteLabelRepository.getWhiteLabelBasicDetailsDto()).thenReturn(Collections.emptyList());
        List<WLBasicDetailsDto> whiteLabelBasicDetails = whiteLabelService.getWhiteLabelBasicDetails();

        assertTrue(whiteLabelBasicDetails.isEmpty());
    }

    @Test
    void test_updateWhiteLabelBillingSettings(){

        whiteLabel.setOverageCharge(100.0);
        whiteLabel.setMonthlySubscriptionId("1223");
        whiteLabel.setAttendeeUploadCharge(100.0);
        whiteLabel.setExpoBoothCharge(100.0);
        whiteLabel.setAutoBilling(false);
        whiteLabel.setBillingType(BillingType.Paid);
        whiteLabelBillingSettingsDto.setAttendeeUploadCharge(110.0);
        whiteLabelBillingSettingsDto.setExpoBoothCharge(110.0);
        whiteLabelBillingSettingsDto.setAutoBilling(true);
        whiteLabelBillingSettingsDto.setBillingType(BillingType.Comp);

        when(whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl("test-wl")).thenReturn(Optional.ofNullable(whiteLabel));

//        doNothing().when(changeHistoryRepoService).save(any());
//        doNothing().when(chargebeePaymentService).updateAttendeesOverageCharge(anyString(), anyDouble());
        doNothing().when(userService).checkIsBillingTypeAdminOrThrowError(any());
        doNothing().when(transactionFeeConditionalLogicService).updateTransCondLogic(whiteLabel, user);
        when(whiteLabelRepoService.save(any())).thenReturn(whiteLabel);

        whiteLabelService.updateWhiteLabelBillingSettings(user,whiteLabelBillingSettingsDto,true,user);

        assertTrue(true);

    }

    @Test
    void test_findByWhiteLabelURLThrowException(){

//        when(whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(null));

        try {
            Method findByWhiteLabelURLThrowException = WhiteLabelService.class
                    .getDeclaredMethod("findWhiteLabelByWhiteLabelUrl", String.class);
            findByWhiteLabelURLThrowException.setAccessible(true);
            findByWhiteLabelURLThrowException.invoke(whiteLabelService, "Milan");
        } catch (InvocationTargetException e) {
            assertEquals(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND.getDeveloperMessage(),e.getTargetException().getMessage());
        } catch (Exception e){
            //Ignore
        }
    }

    @Test
    void test_getWhiteLabelBillingSettings_white_lable_not_found(){
        when(whiteLabelRepository.getWhiteLabelBillingSettings(anyString())).thenReturn(whiteLabelBillingSettingsDto);
        when(whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(null));

        Exception exception = assertThrows(NotFoundException.class,
                () -> whiteLabelService.getWhiteLabelBillingSettings("test-wl",true,user));

        assertEquals(NotFoundException.NotFound.WHITE_LABEL_URL_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getWhiteLabelBillingSettings(){


        when(whiteLabelRepository.getWhiteLabelBillingSettings(anyString())).thenReturn(whiteLabelBillingSettingsDto);
        when(whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));

        WhiteLabelBillingSettingsDto whiteLabelBillingSettings1 = whiteLabelService.getWhiteLabelBillingSettings("test-wl", true, user);

        assertEquals(whiteLabelBillingSettingsDto.getBillingType(),whiteLabelBillingSettings1.getBillingType());
    }

    @Test
    void test_getWhiteLabelBillingSettings_chargebeeEventCredits_null(){

        planConfig.setFreeQuantity(100);
        whiteLabel.setPlanConfig(planConfig);

        when(whiteLabelRepository.getWhiteLabelBillingSettings(anyString())).thenReturn(whiteLabelBillingSettingsDto);
        when(whiteLabelRepoService.findWhiteLabelByWhiteLabelUrl(anyString())).thenReturn(Optional.ofNullable(whiteLabel));

        WhiteLabelBillingSettingsDto whiteLabelBillingSettings1 = whiteLabelService.getWhiteLabelBillingSettings("test-wl", true, user);

        assertEquals(whiteLabelBillingSettingsDto.getBillingType(),whiteLabelBillingSettings1.getBillingType());
    }

    @Test
    void test_whitelabelCreditDistribution(){

        planConfig.setPlanName("WhiteLabel");
        planConfig.setFreeQuantity(100);

        chargebeeEventUsages.setCreditType(UsagesType.PAID);
        chargebeeEventUsages.setInvoice("test-invoice");

        whiteLabel.setFreeQuantity(100);

        List<Object[]> object = new ArrayList<>();
        Object[] obj = new Object[] { 100,20 };
        object.add(obj);

        when(chargebeePlanRepoService.findAll()).thenReturn(Collections.singletonList(planConfig));
        when(roEventService.findEventsByWhiteLabel(any())).thenReturn(Collections.singletonList(event));

        List<EventCreditDistributionDto> eventCreditDistributionDtos = whiteLabelService.whitelabelCreditDistributionBasedOnEventDays(whiteLabel);

        eventCreditDistributionDtos.forEach(e->{
            assertEquals(Long.valueOf(1),e.getEventId());
        });
    }

    @Test
    void test_retrieveApiKeyAndInsertAuditLog(){

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(clientApiKey));
        doNothing().when(apiKeyService).saveApiKeyAuditLog(any(), any(), any());

        String s = whiteLabelService.retrieveApiKeyAndInsertAuditLog(1L, user);

        assertEquals(clientApiKey.getApiKey(),s);
    }

    @Test
    void test_generateAccessKey(){

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(null));
        when(apiKeyService.save(any())).thenReturn(clientApiKey);
        doNothing().when(apiKeyService).saveApiKeyAuditLog(any(), any(), any());

        String token = whiteLabelService.generateAccessKey(whiteLabel, "token", 1L, user);

        assertEquals(clientApiKey.getApiKey(),token);
    }

    @Test
    void test_generateAccessTokenModelForApiUser(){

        AccessTokenModel accessTokenModel = new AccessTokenModel();
        accessTokenModel.setUser(user);
        accessTokenModel.setEventId(1L);

        when(roEventService.findEventsByWhiteLabel(any())).thenReturn(Collections.singletonList(event));
        when(userService.signUpApiUserAndReturnAccessTokenModel(any(),any(),any(), any())).thenReturn(accessTokenModel);

        AccessTokenModel accessTokenModel1 = whiteLabelService.generateAccessTokenModelForApiUser(whiteLabel);

        assertEquals(user,accessTokenModel1.getUser());

    }

    @Test
    void test_rotateApiKeyForWhiteLabel(){

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(clientApiKey));
        when(apiKeyService.rotateApiKey(any(), anyString(), any())).thenReturn("test");

        String s = whiteLabelService.rotateApiKeyForWhiteLabel(whiteLabel, user);
        assertEquals("test",s);
    }

    @Test
    void test_deactivateApiKeyForWhiteLabel(){

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(clientApiKey));
        doNothing().when(apiKeyService).deactivateApiKey(any(), any());

        whiteLabelService.deactivateApiKeyForWhiteLabel(1L,user);

        assertTrue(true);
    }

    @Test
    void test_getApiKeyAuditDetails(){

        ApiKeyDto apiKeyDto = new ApiKeyDto();
        apiKeyDto.setApiKeyActivated(true);

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(clientApiKey));
        when(apiKeyService.getApiKeyAndAuditDetails(anyLong(), anyLong(), any())).thenReturn(apiKeyDto);

        ApiKeyDto apiKeyAuditDetails = whiteLabelService.getApiKeyAuditDetails(1L);
        assertEquals(apiKeyDto,apiKeyAuditDetails);
    }

    @Test
    void test_retrieveApiKeyIfExistForWhiteLabel(){

        when(apiKeyService.findApiKeyByWhiteLabelId(anyLong())).thenReturn(Optional.ofNullable(clientApiKey));

        String s = whiteLabelService.retrieveApiKeyIfExistForWhiteLabel(1L);
        assertEquals(clientApiKey.getApiKey(),s);
    }

    @Test
    void test_getBillingSummeryForWhiteLabel(){

        BillingSummerDto billingSummerDto = new BillingSummerDto();
        when(chargebeeBillingSummeryService.getBillingSummeryForWhiteLabel(whiteLabel)).thenReturn(billingSummerDto);

        BillingSummerDto billingSummeryForWhiteLabel = whiteLabelService.getBillingSummeryForWhiteLabel(whiteLabel);
        assertEquals(billingSummerDto,billingSummeryForWhiteLabel);
    }

    @Test
    void test_createWLOrganizer(){
        OrganizerDto organizerDto = new OrganizerDto();
        when(organizerService.createWLOrganizer(anyString(), any(), any(), any(), anyBoolean())).thenReturn(organizerDto);

        OrganizerDto test = whiteLabelService.createWLOrganizer("test", whiteLabel, organizerDto, user, Boolean.FALSE);
        assertEquals(organizerDto,test);
    }

    @Test
    void test_findEvents(){

        when(eventService.getEventByWhiteLabelOwner(any())).thenReturn(Collections.emptySet());
        Set<Event> events = whiteLabelService.findEvents(user);

        assertTrue(events.isEmpty());
    }

}
