package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.RecordStatus;
import com.accelevents.domain.enums.StripeTransactionSource;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.TicketType;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.services.repo.helper.EventTicketsRepoService;
import com.accelevents.ticketing.dto.RecurringEventsTicketingTypeAndSoldCountDTO;
import com.accelevents.ticketing.dto.TicketingOrderDto;
import com.accelevents.utils.Constants;
import com.accelevents.utils.StringTools;
import com.accelevents.utils.TimeZoneUtil;
import com.itextpdf.text.DocumentException;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RecurringEventsScheduleBRServiceImplTest {

    @Spy
    @InjectMocks
    private RecurringEventsScheduleBRServiceImpl recurringEventsScheduleBRServiceImpl = new RecurringEventsScheduleBRServiceImpl();

    @Mock
    private RecurringEventsMainScheduleService recurringEventsMainScheduleService;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private EventTicketsCommonRepo eventTicketsCommonRepo;

    @Mock
    private EventTicketsService eventTicketsService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private RecurringEventsScheduleRepository recurringEventsScheduleRepository;

    @Mock
    private TicketingTypeRepository ticketingTypeRepository;

    @Mock
    private TicketingTypeCommonRepo ticketingTypeCommonRepo;

    @Mock
    private TicketingTypeRepository ticketingtypeRepository;

    @Mock
    private TicketingOrderManagerRepository ticketingOrderManagerRepository;

    @Mock
    private EventTicketsService eventTicketService;

    @Mock
    private CommonEventService commonEventService;

    @Mock
    private EventTicketsRepoService eventTicketsRepoService;

    @Mock
    private EventLimitRegistrationDomainService eventLimitRegistrationDomainService;

    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private RecurringEventsRepository recurringEventsRepository;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private SeatsIoForMultipleEventsImpl seatsIoForMultipleEventsImpl;

    @Mock
    private StripeTransactionService stripeTransactionService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TicketingOrderService ticketingOrderService;

    @Mock
    private SendGridMailPrepareService sendGridMailPrepareService;

    @Mock
    private TicketingStatisticsService ticketingStatisticsService;

    @Mock
    private SeatsIoService seatsIoService;

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private  TicketingRepository ticketingRepository;

    @Mock
    private TicketingCouponService ticketingCouponService;

    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Mock
    private EmbedWidgetSettingService embedWidgetSettingService;

    @Mock
    private ResendTicketingEmailRepository resendTicketingEmailService;

    @Mock
    private RegistrationAttributeRepository registrationAttributeRepository;

    @Mock
    private TicketingLimitedDisplayCodeService ticketingLimitedDisplayCodeService;

    private Event event;
    private EventTickets eventTickets;
    private Ticketing ticketing;
    private RecurringEvents recurringEvents, oldRecurringEvents, newRecurringEvents;
    private TicketingOrder ticketingOrder;
    private TicketingType ticketingType;
    private StripeTransaction stripeTransaction;
    private RecurringIdAndDateDto recurringIdAndDateDto;
    private RecurringEventResDto recurringEventResDto;
    private RecurringScheduleResDto recurringScheduleResDto;

    private Long id = 1L;
    private BigInteger count = BigInteger.ONE;
    private static String startDate = "2019/01/01 05:30";
    private static String endDate = "2019/04/04 05:30";
    private static Date startDate1 = new Date(startDate);
    private static Date endDate1 = new Date(endDate);
    private static String success = "";
    private static String newStartDates = "2019/02/01 05:30";
    private static String newEndDates = "2019/05/04 05:30";
    private static Date newStartDate1 = new Date(newStartDates);
    private static Date newEndDate1 = new Date(newEndDates);

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        ticketing = EventDataUtil.getTicketing(event);
        User user = EventDataUtil.getUser();
        eventTickets = EventDataUtil.getEventTickets();
        recurringEvents = EventDataUtil.getRecurringEvents();
        ticketingOrder = EventDataUtil.getTicketingOrder();
        ticketingType = EventDataUtil.getTicketingType(event);
        recurringIdAndDateDto = new RecurringIdAndDateDto(id, startDate1);
        recurringEventResDto = new RecurringEventResDto(recurringEvents);
        recurringScheduleResDto = new RecurringScheduleResDto();

        oldRecurringEvents = new RecurringEvents();
        oldRecurringEvents.setId(id);
        oldRecurringEvents.setCheckInXMinutesBefore(60);
        oldRecurringEvents.setRecurringEventStartDate(startDate1);
        oldRecurringEvents.setRecurringEventEndDate(endDate1);

        newRecurringEvents = new RecurringEvents();
        oldRecurringEvents.setId(id);
        oldRecurringEvents.setCheckInXMinutesBefore(60);
        oldRecurringEvents.setRecurringEventStartDate(newStartDate1);
        oldRecurringEvents.setRecurringEventEndDate(newEndDate1);
    }

    @Test
    void test_getRecurringEventsByScheduleId_success(){

        //setup
        Long recurringEventScheduleId = 1L;
        List<RecurringEventResDto> recurringEventResDtoList = new ArrayList<>();
        recurringEventResDtoList.add(recurringEventResDto);

        //mock
        Mockito.doReturn(recurringEventResDtoList).when(recurringEventsScheduleBRServiceImpl).getRecurringEventsByScheduleId(event, recurringEventScheduleId, null);

        //Execution
        List<RecurringEventResDto> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventsByScheduleId(event, recurringEventScheduleId);

        assertEquals(actualData.iterator().next().getId(), recurringEventResDto.getId());
    }

    @Test
    void test_getRecurringEventResDtos_success(){

        //setup
        List<Long> soldTicketsByRecurringEventIdList = new ArrayList<>();
        soldTicketsByRecurringEventIdList.add(id);

        List<RecurringEvents> recurringEventsList =   new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<RecurringEventResDto> recurringEventResDtoList = new ArrayList<>();
        recurringEventResDtoList.add(recurringEventResDto);

        recurringScheduleResDto.setSoldTickets(true);
        recurringScheduleResDto.setId(id);
        recurringScheduleResDto.setCheckInXMinutesBefore(60);

        Object[] soldMinDate = {startDate1};

        //mock
        when(recurringEventsRepository.findMinRecurringEventSoldDate(soldTicketsByRecurringEventIdList)).thenReturn(soldMinDate);

        //Execution
        List<RecurringEventResDto> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventResDtos(event, recurringScheduleResDto, recurringEventsList, soldTicketsByRecurringEventIdList, Collections.emptyList());

        assertEquals(actualData.iterator().next().getId(), recurringScheduleResDto.getId());
        assertEquals(actualData.iterator().next().getCheckInXMinutesBefore(), recurringScheduleResDto.getCheckInXMinutesBefore());
        assertEquals(actualData.iterator().next().isSoldTickets(), recurringScheduleResDto.isSoldTickets());
    }

    @Test
    void test_getRecurringEventResDtos_success_with_scheduleResDto_null(){

        //setup
        List<Long> soldTicketsByRecurringEventIdList = new ArrayList<>();
        soldTicketsByRecurringEventIdList.add(id);

        List<RecurringEvents> recurringEventsList =   new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<RecurringEventResDto> recurringEventResDtoList = new ArrayList<>();
        recurringEventResDtoList.add(recurringEventResDto);

        Object[] soldMinDate = {null};

        //mock
        when(recurringEventsRepository.findMinRecurringEventSoldDate(soldTicketsByRecurringEventIdList)).thenReturn(soldMinDate);

        //Execution
        List<RecurringEventResDto> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventResDtos(event, null, recurringEventsList, soldTicketsByRecurringEventIdList, Collections.emptyList());

        assertEquals(actualData.iterator().next().getId().longValue(), recurringEvents.getId());
        assertEquals(actualData.iterator().next().getCheckInXMinutesBefore(), recurringEvents.getCheckInXMinutesBefore());
    }

    @Test
    void test_getRecurringEventResDtos_success_with_soldMinDate_null(){

        //setup
        List<Long> soldTicketsByRecurringEventIdList = new ArrayList<>();
        soldTicketsByRecurringEventIdList.add(id);

        List<RecurringEvents> recurringEventsList =   new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<RecurringEventResDto> recurringEventResDtoList = new ArrayList<>();
        recurringEventResDtoList.add(recurringEventResDto);

        recurringScheduleResDto.setSoldTickets(true);
        recurringScheduleResDto.setId(id);
        recurringScheduleResDto.setCheckInXMinutesBefore(60);

        //mock
        when(recurringEventsRepository.findMinRecurringEventSoldDate(soldTicketsByRecurringEventIdList)).thenReturn(null);

        //Execution
        List<RecurringEventResDto> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventResDtos(event, recurringScheduleResDto, recurringEventsList, soldTicketsByRecurringEventIdList, Collections.emptyList());

        assertEquals(actualData.iterator().next().getId().longValue(), recurringEvents.getId());
        assertEquals(actualData.iterator().next().getCheckInXMinutesBefore(), recurringEvents.getCheckInXMinutesBefore());
    }

    @Test
    void test_getRecurringEvents_success_throwException_RECURRING_EVENT_NOT_FOUND() {

        //Mock
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.empty());

        //Execute
        Exception exception = assertThrows(NotFoundException.class,
                () -> recurringEventsScheduleBRServiceImpl.getRecurringEvents(id)
        );
        assertEquals(NotFoundException.EventNotFound.RECURRING_EVENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteRecurringEventById_throwException_CAN_NOT_DELETE_EVENT() {

        //Mock
        doReturn(recurringEvents).when(recurringEventsScheduleBRServiceImpl).getRecurringEvents(id);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(id)).thenReturn(BigInteger.ONE);

        //Execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.deleteRecurringEventById(id, event)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_DELETE_EVENT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteRecurringEventById_throwException_CAN_NOT_DELETE_EVENT_ORDER_IS_IN_PROGRESS() {

        //Mock
        doReturn(recurringEvents).when(recurringEventsScheduleBRServiceImpl).getRecurringEvents(id);
        when(ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(id)).thenReturn(Arrays.asList(1L, 2L));
        when(eventTicketsRepoService.getRecurringHavingTicketSold(id)).thenReturn(null);
        when(ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventId(id)).thenReturn(Collections.singletonList(id));
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(true);
        doNothing().when(eventLimitRegistrationDomainService).updateEventLimitRegistrationDomainRecStatusByRecurringEventIds(Collections.singletonList(id), RecordStatus.DELETE);

        //Execute
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.deleteRecurringEventById(recurringEvents.getId(), event)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_DELETE_EVENT_ORDER_IS_IN_PROGRESS_BUT_YOU_CAN_CANCEL_EVENT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_deleteRecurringEventById_success_with_ticketSold_null_IsNeedToCheckHavingDeleteOrdersFALSE() {

        //Mock
        doReturn(recurringEvents).when(recurringEventsScheduleBRServiceImpl).getRecurringEvents(id);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(any())).thenReturn(null);


        Mockito.doNothing().when(recurringEventsScheduleBRServiceImpl).checkDisableRecurringEvent(any());


        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());




        //Execution
        recurringEventsScheduleBRServiceImpl.deleteRecurringEventById(id, event);

        verify(recurringEventsRepository, Mockito.times(0)).delete(recurringEvents);
        verify(recurringEventsScheduleBRServiceImpl, Mockito.times(1)).cancelEventAndUpdateCancelStatusToRefundedDeletedOrders(anyLong(), any(),any());
        verify(recurringEventsScheduleBRServiceImpl, Mockito.times(0)).deleteRecEvents(anyLong(), any(),any());
    }

    @Test
    void test_deleteRecurringEventById_success_with_ticketSold_null_IsNeedToCheckHavingDeleteOrdersTRUE() {

        //Mock
        doReturn(recurringEvents).when(recurringEventsScheduleBRServiceImpl).getRecurringEvents(id);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(any())).thenReturn(null);


        Mockito.doNothing().when(recurringEventsScheduleBRServiceImpl).checkDisableRecurringEvent(any());


        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());




        //Execution
        recurringEventsScheduleBRServiceImpl.deleteRecurringEventById(id, event);

        verify(recurringEventsRepository, Mockito.times(0)).delete(recurringEvents);
        verify(recurringEventsScheduleBRServiceImpl, Mockito.times(1)).cancelEvent(anyLong(), any());
        verify(recurringEventsScheduleBRServiceImpl, Mockito.times(1)).updateRecStatusToCancelInAllRelatedTables(any(), anyList());
    }

    @Test
    void test_deleteRecurringEventById_success_with_ticketSold_0() {

        //setup
        BigInteger ticketSold = BigInteger.valueOf(0L);

        //Mock
        doReturn(recurringEvents).when(recurringEventsScheduleBRServiceImpl).getRecurringEvents(id);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(any())).thenReturn(null);




        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());
        when(eventTicketsRepoService.getRecurringHavingTicketSold(recurringEvents.getId())).thenReturn(ticketSold);

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteRecurringEventById(id, event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, Mockito.times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketing1 = ticketingArgumentCaptor.getValue();
        assertFalse(ticketing1.isRecurringEvent());

        verify(recurringEventsRepository, Mockito.times(0)).delete(recurringEvents);
    }

    @Test
    void test_deleteRecurringEventsScheduleById_success_With_UpdateRecurringEvent() {

        //setup
        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(1L);

        //Mock
        when(recurringEventsScheduleRepository.findById(id)).thenReturn(Optional.of(recurringEventSchedule));
        when(ticketingOrderManagerService.getSoldRecurringEventIdsByScheduleId(id)).thenReturn(Collections.emptyList());
        when(recurringEventsRepository.findByRecurringEventIdScheduleId(id)).thenReturn(Arrays.asList(1L, 2L));
        doNothing().when(ticketingTypeService).deleteByRecurringEventIdIn(any());
        doNothing().when(recurringEventsRepository).deleteByRecurringEventScheduleId(any());
        doNothing().when(recurringEventsScheduleRepository).delete(any());
        when(recurringEventsRepository.countByEventId(event)).thenReturn(count);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);


        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteRecurringEventsScheduleById(id, event);

        verify(recurringEventsScheduleRepository, Mockito.times(1)).delete(recurringEventSchedule);
    }

    @Test
    void test_deleteRecurringEventsScheduleById_success() {

        //setup
        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(1L);

        //Mock
        when(ticketingOrderManagerService.getSoldRecurringEventIdsByScheduleId(id)).thenReturn(Collections.singletonList(1L));
        doNothing().when(recurringEventsRepository).updateRecurringEventsToCustom(any());
        when(recurringEventsRepository.findByRecurringEventIdScheduleId(id)).thenReturn(Collections.emptyList());
        when(recurringEventsScheduleRepository.findById(id)).thenReturn(Optional.of(recurringEventSchedule));
        doNothing().when(recurringEventsRepository).deleteByRecurringEventScheduleId(any());
        doNothing().when(recurringEventsScheduleRepository).delete(any());
        when(recurringEventsRepository.countByEventId(event)).thenReturn(count);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);


        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteRecurringEventsScheduleById(id, event);

        verify(seatsIoForMultipleEventsImpl, Mockito.times(1)).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());
    }

    @Test
    void test_checkDisableRecurringEvent_success() {

        //Mock
        when(recurringEventsRepository.countByEventId(event)).thenReturn(null);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        //Execution
        recurringEventsScheduleBRServiceImpl.checkDisableRecurringEvent(event);

        ArgumentCaptor<Ticketing> argumentCaptorSchedule = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, Mockito.times(1)).save(argumentCaptorSchedule.capture());

        Ticketing ticketing1 = argumentCaptorSchedule.getValue();
        assertFalse(ticketing1.isRecurringEvent());
    }

    @Test
    void test_checkDisableRecurringEvent_success_with_countEvent() {

        //Mock
        when(recurringEventsRepository.countByEventId(event)).thenReturn(BigInteger.valueOf(-1L));
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        //Execution
        recurringEventsScheduleBRServiceImpl.checkDisableRecurringEvent(event);

        ArgumentCaptor<Ticketing> argumentCaptorSchedule = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, Mockito.times(1)).save(argumentCaptorSchedule.capture());
        Ticketing ticketing1 = argumentCaptorSchedule.getValue();

        assertFalse(ticketing1.isRecurringEvent());
    }

    @Test
    void test_disableRecurringEventTest_with_recurringEvent() {

        //Mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        //Execution
        recurringEventsScheduleBRServiceImpl.disableRecurringEvent(event);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService, Mockito.times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketing1 = ticketingArgumentCaptor.getValue();

        assertFalse(ticketing1.isRecurringEvent());
    }

    @Test
    void test_disableRecurringEventTest_alredyDisable() {

        //setUp
        ticketing.setRecurringEvent(false);

        //Mock
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        //Execution
        recurringEventsScheduleBRServiceImpl.disableRecurringEvent(event);
    }


    @Test
    void test_cancelEvent_success_with_RecurringEventStatus_CREATE() {

        //setup
        recurringEvents.setStatus(RecurringEvents.RecurringEventStatus.CREATE);

        //Mock
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(true);
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.of(recurringEvents));

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelEvent(id);

        ArgumentCaptor<RecurringEvents> recurringEventsArgumentCaptor = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(recurringEventsArgumentCaptor.capture());

        RecurringEvents actual = recurringEventsArgumentCaptor.getValue();
        assertEquals(null, actual.getRecurringEventScheduleId());
        assertEquals("CREATE", actual.getStatus().toString());
    }

    @Test
    void test_cancelEvent_success_with_RecurringEventStatus_CANCEL() {

        //setup
        recurringEvents.setStatus(RecurringEvents.RecurringEventStatus.CANCEL);

        //Mock
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(false);
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.of(recurringEvents));

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelEvent(id);

        ArgumentCaptor<RecurringEvents> recurringEventsArgumentCaptorStatus = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(recurringEventsArgumentCaptorStatus.capture());

        RecurringEvents actualStatus = recurringEventsArgumentCaptorStatus.getValue();
        assertEquals("CANCEL", actualStatus.getStatus().toString());
    }

    @Test
    void test_cancelEvent_success_with_recurringEvents_empty() {

        //Mock
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.empty());

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelEvent(id);
    }

    private TicketingType getTicketingType() {
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(1L);
        ticketingType.setCreatedFrom(12L);
        ticketingType.setTicketType(TicketType.PAID);
        ticketingType.setPrice(100);
        ticketingType.setRecurringEventSalesEndTime(60);
        return ticketingType;
    }

  //  @Test

    void test_updateRecurringEventTest_success_recurringTicketSold() {
        //setup
        RecurringEventDto recurringEventDto = new RecurringEventDto();
        recurringEventDto.setStartTime("07:00:00");
        recurringEventDto.setEndTime("10:00:00");
        recurringEventDto.setOccursFrom("20/04/2019");
        recurringEventDto.setOccursUntil("20/04/2019");

        recurringEvents.setRecurringEventScheduleId(10L);

        LocalDate occursFrom = LocalDate.parse(recurringEventDto.getOccursFrom(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));
        LocalDate occursUntil = LocalDate.parse(recurringEventDto.getOccursUntil(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));

        LocalTime startTime = LocalTime.parse(recurringEventDto.getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime endTime = LocalTime.parse(recurringEventDto.getEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));

        LocalDateTime startDateTimeL = LocalDateTime.of(occursFrom, startTime);
        LocalDateTime endDateTimeL = LocalDateTime.of(occursUntil, endTime);
        Date startDateTime = Date.from(startDateTimeL.toInstant(ZoneOffset.UTC));
        Date endDateTime = Date.from(endDateTimeL.toInstant(ZoneOffset.UTC));

        //Mock
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.of(recurringEvents));
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(true);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(recurringEventsRepository.findMaxRecurringEventDate(event, RecurringEvents.RecurringEventStatus.CANCEL)).thenReturn(null);

        //Execution
        recurringEventsScheduleBRServiceImpl.updateRecurringEvent(event, recurringEventDto, id);

        ArgumentCaptor<RecurringEvents> argumentCaptor1 = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(argumentCaptor1.capture());

        RecurringEvents actuallRecurringEvents1 = argumentCaptor1.getValue();
        assertEquals(actuallRecurringEvents1.getRecurringEventStartDate(), startDateTime);
        assertEquals(actuallRecurringEvents1.getRecurringEventEndDate(), endDateTime);
        //set Not null because we have removed functionality to making custom event.
    }


  //  @Test
    void test_updateRecurringEventTest_success_recurringTicketNotSold() {

        //setup
        RecurringEventDto recurringEventDto = new RecurringEventDto();
        recurringEventDto.setStartTime("07:00:00");
        recurringEventDto.setEndTime("10:00:00");
        recurringEventDto.setOccursFrom("20/04/2019");
        recurringEventDto.setOccursUntil("20/04/2019");

        recurringEvents.setRecurringEventScheduleId(10L);

        LocalDate occursFrom = LocalDate.parse(recurringEventDto.getOccursFrom(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));
        LocalDate occursUntil = LocalDate.parse(recurringEventDto.getOccursUntil(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));

        LocalTime startTime = LocalTime.parse(recurringEventDto.getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime endTime = LocalTime.parse(recurringEventDto.getEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));

        LocalDateTime startDateTimeL = LocalDateTime.of(occursFrom, startTime);
        LocalDateTime endDateTimeL = LocalDateTime.of(occursUntil, endTime);
        Date startDateTime = Date.from(startDateTimeL.toInstant(ZoneOffset.UTC));
        Date endDateTime = Date.from(endDateTimeL.toInstant(ZoneOffset.UTC));

        //Mock
        when(recurringEventsRepository.findById(id)).thenReturn(Optional.of(recurringEvents));
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(false);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(recurringEventsRepository.findMaxRecurringEventDate(event, RecurringEvents.RecurringEventStatus.CANCEL)).thenReturn(null);

        //Execution
        recurringEventsScheduleBRServiceImpl.updateRecurringEvent(event, recurringEventDto, id);

        ArgumentCaptor<RecurringEvents> argumentCaptor1 = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(argumentCaptor1.capture());

        RecurringEvents actuallRecurringEvents1 = argumentCaptor1.getValue();
        assertEquals(actuallRecurringEvents1.getRecurringEventStartDate(), startDateTime);
        assertEquals(actuallRecurringEvents1.getRecurringEventEndDate(), endDateTime);
    }

   // @Test
    void test_updateRecurringEventTest_success_without_recurringEventsDb() {

        //setup
        RecurringEventDto recurringEventDto = new RecurringEventDto();
        recurringEventDto.setStartTime("07:00:00");
        recurringEventDto.setEndTime("10:00:00");
        recurringEventDto.setOccursFrom("20/04/2019");
        recurringEventDto.setOccursUntil("20/04/2019");

        LocalDate occursFrom = LocalDate.parse(recurringEventDto.getOccursFrom(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));
        LocalDate occursUntil = LocalDate.parse(recurringEventDto.getOccursUntil(), DateTimeFormatter.ofPattern(ONLY_DATE_FORMAT));
        LocalTime startTime = LocalTime.parse(recurringEventDto.getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime endTime = LocalTime.parse(recurringEventDto.getEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalDateTime startDateTimeL = LocalDateTime.of(occursFrom, startTime);
        LocalDateTime endDateTimeL = LocalDateTime.of(occursUntil, endTime);
        Date startDateTime = Date.from(startDateTimeL.toInstant(ZoneOffset.UTC));
        Date endDateTime = Date.from(endDateTimeL.toInstant(ZoneOffset.UTC));

        recurringEvents.setRecurringEventScheduleId(null);

        //mock
        when(recurringEventsRepository.findById(1L)).thenReturn(Optional.of(recurringEvents));
        doNothing().when(commonEventService).updateEventTicketsOrAddOnWithDbRecurringEvent(anyLong(), anyLong());
        when(recurringEventsRepository.findByStartDateAndEndDate(any(), any(), any())).thenReturn(null);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(recurringEventsRepository.findMaxRecurringEventDate(event, RecurringEvents.RecurringEventStatus.CANCEL)).thenReturn(null);

        //Execution
        recurringEventsScheduleBRServiceImpl.updateRecurringEvent(event, recurringEventDto, id);

        ArgumentCaptor<RecurringEvents> argumentCaptor = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(argumentCaptor.capture());

        RecurringEvents actuallRecurringEvents2 = argumentCaptor.getValue();
        assertEquals(startDateTime, actuallRecurringEvents2.getRecurringEventStartDate());
        assertEquals(endDateTime, actuallRecurringEvents2.getRecurringEventEndDate());
    }

 //   @Test
    void test_updateRecurringEventTest_success_with_recurringEventsDb() {

        //setup
        RecurringEventDto recurringEventDto = new RecurringEventDto();
        recurringEventDto.setStartTime("07:00:00");
        recurringEventDto.setEndTime("10:00:00");
        recurringEventDto.setOccursFrom("20/04/2019");
        recurringEventDto.setOccursUntil("20/04/2019");

        recurringEvents.setRecurringEventScheduleId(null);

        //mock
        when(recurringEventsRepository.findById(1L)).thenReturn(Optional.of(recurringEvents));
        doNothing().when(commonEventService).updateEventTicketsOrAddOnWithDbRecurringEvent(anyLong(), anyLong());
        when(recurringEventsRepository.findByStartDateAndEndDate(any(), any(), any())).thenReturn(recurringEvents);
        when(ticketingRepository.findByEventid(event)).thenReturn(ticketing);
        when(recurringEventsRepository.findMaxRecurringEventDate(event, RecurringEvents.RecurringEventStatus.CANCEL)).thenReturn(null);

        //Execution
        recurringEventsScheduleBRServiceImpl.updateRecurringEvent(event, recurringEventDto, id);

        ArgumentCaptor<RecurringEvents> argumentCaptor1 = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).delete(argumentCaptor1.capture());
    }

    @Test
    void test_updateRecurringEventTicket_success() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        long orderId = id;
        long newRecurringEventId = id;

        List<TicketingOrderDto> ticketingDtoList = new ArrayList<>();

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID, 1, orderId, newRecurringEventId, 1L, 1559992034425L, "");
        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        success = getSuccessMessage();

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(stripeTransaction);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingType);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(10L);

        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("TEST");
        doNothing().when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(),eq(null));
        when(recurringEventsRepository.findById(newRecurringEventId)).thenReturn(Optional.of(recurringEvents));
        doNothing().when(ticketingOrderManagerRepository).updateTicketTypeOfTicketingOrderManager(any(), any(), any(), any(), any(),any());
        doNothing().when(eventCommonRepoService).updateTypeOfEventTicketsOrAddOn(anyLong(), any(), anyLong(), anyLong(), anyLong());

        //Execution
        String successMsg = recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDtoList);

        assertNotEquals(success, successMsg);
    }

    @Test
    void test_updateRecurringEventTicket_success_with_stripeTransaction_null() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        long orderId = id;
        long newRecurringEventId = id;

        List<TicketingOrderDto> ticketingDtoList = new ArrayList<>();

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L,  TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");
        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        success = getSuccessMessage();

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(null);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingType);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(10L);

        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("TEST");
        doNothing().when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(),eq(null));
        when(recurringEventsRepository.findById(newRecurringEventId)).thenReturn(Optional.of(recurringEvents));

        //Execution
        String successMsg = recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDtoList);

        assertNotEquals(success, successMsg);
    }

    @Test
    void test_updateRecurringEventTicket_success_with_ticketTypeId_zero() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        long orderId = id;
        long newRecurringEventId = id;

        TicketingOrderDto ticketingOrderDto = new TicketingOrderDto();
        ticketingOrderDto.setTicketTypeId(0L);

        List<TicketingOrderDto> ticketingDtoList = new ArrayList<>();
        ticketingDtoList.add(ticketingOrderDto);

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");
        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        success = getSuccessMessage();

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(null);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingType);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(10L);

        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("TEST");
        doNothing().when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(),eq(null));
        when(recurringEventsRepository.findById(newRecurringEventId)).thenReturn(Optional.of(recurringEvents));

        //Execution
        String successMsg = recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDtoList);

        assertNotEquals(success, successMsg);
    }

    public static Object[] getUpdateRecurringEventTicketParameters() {
        return new Object[]{
                new Object[]{new JAXBException("JAXBException"), 1},
                new Object[]{new IOException(), 2},
                new Object[]{new DocumentException(), 3},
                new Object[]{new TemplateException("TemplateException", null), 4},
        };
    }

    @ParameterizedTest
    @MethodSource("getUpdateRecurringEventTicketParameters")
    void test_updateRecurringEventTicket_throwExeption(Exception expExeption, int order) throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        long orderId = 1L;
        long newRecurringEventId = 1L;

        List<TicketingOrderDto> ticketingDtoList = new ArrayList<>();

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");

        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);
        TicketingType ticketingType = getTicketingType();

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(stripeTransaction);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingType);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(10L);
        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("TEST");
        doThrow(expExeption).when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(),eq(null));
        when(recurringEventsRepository.findById(newRecurringEventId)).thenReturn(Optional.of(recurringEvents));

        //Execution
        recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDtoList);
    }

    private StripeTransaction getStripeTransaction() {
        StripeTransaction stripeTransaction = new StripeTransaction();
        stripeTransaction.setLastFour("1111");
        stripeTransaction.setCardType("CREDIT");
        return stripeTransaction;
    }

    @Test
    void test_updateRecurringEventTicket_throwNotAcceptableException() {

        //setup
        long orderId = 1L;
        long newRecurringEventId = 1L;

        List<TicketingOrderDto> ticketingDtoList = new ArrayList<>();

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");
        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(stripeTransaction);

        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(null);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);


        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDtoList)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_TYPE_NOT_EXIST.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_checkQuantityAvailableToTransfer_throwNotAcceptableException() {

        //setup
        long orderId = 1L;
        long newRecurringEventId = 1L;

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");

        //Mock
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(0L);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.checkQuantityAvailableToTransfer(ticketingType, ticketDetailDto)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.TICKET_NOT_AVAILABLE.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getRecurringEventSchedule_throwException_RECURRING_EVENT_SCHEDULE_NOT_FOUND() {

        //setup
        long RecurringEventScheduleId = 1L;

        //Mock
        when(recurringEventsScheduleRepository.findById(RecurringEventScheduleId)).thenReturn(Optional.empty());

        Exception exception = assertThrows(NotFoundException.class,
                () -> recurringEventsScheduleBRServiceImpl.getRecurringEventSchedule(RecurringEventScheduleId)
        );

        assertEquals(NotFoundException.EventNotFound.RECURRING_EVENT_SCHEDULE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getRecurringEventSchedule_success_with_recurringEventSchedule_frequency_DAILY() {

        //setup
        Long recurringEventScheduleId = id;
		Object[] obj = {BigInteger.ONE, BigInteger.TEN};

        List<Object[]> objects = new ArrayList<>();
        objects.add(obj);

        RecurringEventSchedule recurringEventSchedule = getRecurringEventSchedule(DAILY);
        List<RecurringEventSchedule> recurringEventSchedules = new ArrayList<>();
        recurringEventSchedules.add(recurringEventSchedule);

        List<RecurringEvents> customRecurringEvents = new ArrayList<>();
        customRecurringEvents.add(recurringEvents);

        //Mock
        when(recurringEventsRepository.getCountOfRecurringEventBySchedule(event.getEventId())).thenReturn(objects);
        when(recurringEventsScheduleRepository.findByEventId(event)).thenReturn(recurringEventSchedules);
        when(recurringEventsRepository.findByEventIdAndRecurringEventScheduleIdIsNullOrderByRecurringEventStartDateAsc(event)).thenReturn(customRecurringEvents);
        when(recurringEventsRepository.findByRecurringEventScheduleId(recurringEventScheduleId)).thenReturn(customRecurringEvents);
        when(eventTicketsRepoService.getRecurringIdsForScheduleWithSoldTickets(recurringEventScheduleId)).thenReturn(Collections.singletonList(id));

        //Execution
        List<RecurringScheduleResDto> recurringEventScheduleDto = recurringEventsScheduleBRServiceImpl.getRecurringEventSchedule(event);

        assertEquals(recurringEventScheduleDto.iterator().next().getCheckInXMinutesBefore(), recurringEvents.getCheckInXMinutesBefore());
        assertEquals(recurringEventScheduleDto.listIterator(0).next().getFrequency(), DAILY);
        assertEquals(recurringEventScheduleDto.listIterator(1).next().getFrequency(), CUSTOM);
    }

    @Test
    void test_getRecurringEventSchedule_success_with_recurringEventSchedule_frequency_null() {

        //setup
        Long recurringEventScheduleId = id;
		Object[] obj = {BigInteger.ONE, BigInteger.TEN};

        List<Object[]> objects = new ArrayList<>();
        objects.add(obj);

        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(id);
        recurringEventSchedule.setEndTime(endDate);
        List<RecurringEventSchedule> recurringEventSchedules = new ArrayList<>();
        recurringEventSchedules.add(recurringEventSchedule);

        List<RecurringEvents> customRecurringEvents = new ArrayList<>();
        customRecurringEvents.add(recurringEvents);

        //Mock
        when(recurringEventsRepository.getCountOfRecurringEventBySchedule(event.getEventId())).thenReturn(objects);
        when(recurringEventsScheduleRepository.findByEventId(any())).thenReturn(recurringEventSchedules);
        when(recurringEventsRepository.findByEventIdAndRecurringEventScheduleIdIsNullOrderByRecurringEventStartDateAsc(event)).thenReturn(customRecurringEvents);
        when(recurringEventsRepository.findByRecurringEventScheduleId(recurringEventScheduleId)).thenReturn(customRecurringEvents);
        when(eventTicketsRepoService.getRecurringIdsForScheduleWithSoldTickets(recurringEventScheduleId)).thenReturn(Collections.singletonList(id));

        //Execution
        List<RecurringScheduleResDto> recurringEventScheduleDto = recurringEventsScheduleBRServiceImpl.getRecurringEventSchedule(event);

        assertEquals(recurringEventScheduleDto.iterator().next().getCheckInXMinutesBefore(), recurringEvents.getCheckInXMinutesBefore());
        assertEquals(recurringEventScheduleDto.listIterator(0).next().getFrequency(), null);
        assertEquals(recurringEventScheduleDto.listIterator(1).next().getFrequency(), CUSTOM);
    }

    private RecurringEventSchedule getRecurringEventSchedule(String frequency) {
        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(1L);
        recurringEventSchedule.setFrequency(frequency);
        recurringEventSchedule.setStartTime("08:00:00");
        recurringEventSchedule.setEndTime("10:00:00");
        recurringEventSchedule.setOccursFrom(new Date("20/04/2019"));
        recurringEventSchedule.setOccursUntil(new Date("20/04/2019"));
        return recurringEventSchedule;
    }

    @Test
    void test_getAllRecurringEventsByEvent_successForOnlyFutureDates() {

        //setup
        boolean onlyFutureDates = true;

        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        //Mock
        when(recurringEventsRepository.findByEventIdOrderByRecurringEventEndDateAscOnlyFutureRecurringEvent(any(), any())).thenReturn(recurringEventsList);

        //Execution
        List<RecurringEventResDto> recurringEvents = recurringEventsScheduleBRServiceImpl.getAllRecurringEventsByEvent(event, onlyFutureDates);
        for (RecurringEventResDto actualData : recurringEvents) {
            assertEquals(id , actualData.getId());
        }
    }

    @Test
    void test_getAllRecurringEventsByEvent_success() {

        //setup
        boolean onlyFutureDates = false;

        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<RecurringEventsTicketingTypeAndSoldCountDTO> typeAndSoldCountDTOS = new ArrayList<>();
        RecurringEventsTicketingTypeAndSoldCountDTO typeAndSoldCountDTO = new RecurringEventsTicketingTypeAndSoldCountDTO();
        typeAndSoldCountDTO.setRecurringEventId(recurringEvents.getId());
        typeAndSoldCountDTO.setSoldTicketExcludingAddOns(10L);
        typeAndSoldCountDTO.setTotalNumberOfTickets(ticketingType.getNumberOfTickets());
        typeAndSoldCountDTOS.add(typeAndSoldCountDTO);

        //Mock
        doReturn(recurringEventsList).when(recurringEventsScheduleBRServiceImpl).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event);
        when(eventTicketsRepoService.getSoldCountByListOfRecurringList(anyList())).thenReturn(typeAndSoldCountDTOS);
        //Execution
        List<RecurringEventResDto> recurringEventsData = recurringEventsScheduleBRServiceImpl.getAllRecurringEventsByEvent(event, onlyFutureDates);
        for (RecurringEventResDto actualData : recurringEventsData) {
            assertEquals(recurringEvents.getCheckInXMinutesBefore() , actualData.getCheckInXMinutesBefore());
            assertEquals(Long.valueOf(10), actualData.getSoldTicketCount());
            assertEquals(ticketingType.getNumberOfTickets(), actualData.getSoldTicketCount()+actualData.getRemainingTicketCount());
        }
        verify(eventTicketsRepoService, times(1)).getSoldCountByListOfRecurringList(anyList());
    }

    @Test
    void test_getAllRecurringEventsByEvent_success_ListOfTicketingTypeAndSoldCountDTO_IsEmpty() {

        //setup
        boolean onlyFutureDates = false;

        List<RecurringEvents> recurringEventsList = new ArrayList<>();
        recurringEventsList.add(recurringEvents);

        List<RecurringEventsTicketingTypeAndSoldCountDTO> typeAndSoldCountDTOS = new ArrayList<>();
        RecurringEventsTicketingTypeAndSoldCountDTO typeAndSoldCountDTO = new RecurringEventsTicketingTypeAndSoldCountDTO();
        typeAndSoldCountDTO.setRecurringEventId(recurringEvents.getId());
        typeAndSoldCountDTO.setSoldTicketExcludingAddOns(0L);
        typeAndSoldCountDTO.setTotalNumberOfTickets(ticketingType.getNumberOfTickets());
        typeAndSoldCountDTOS.add(typeAndSoldCountDTO);

        //Mock
        doReturn(recurringEventsList).when(recurringEventsScheduleBRServiceImpl).getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event);
        when(eventTicketsRepoService.getSoldCountByListOfRecurringList(anyList())).thenReturn(typeAndSoldCountDTOS);
        //Execution
        List<RecurringEventResDto> recurringEventsData = recurringEventsScheduleBRServiceImpl.getAllRecurringEventsByEvent(event, onlyFutureDates);
        for (RecurringEventResDto actualData : recurringEventsData) {
            assertEquals(recurringEvents.getCheckInXMinutesBefore() , actualData.getCheckInXMinutesBefore());
            assertEquals(Long.valueOf(0), actualData.getSoldTicketCount());
            assertEquals(ticketingType.getNumberOfTickets(), actualData.getSoldTicketCount()+actualData.getRemainingTicketCount());
        }
        verify(eventTicketsRepoService, times(1)).getSoldCountByListOfRecurringList(anyList());
    }

    @Test
    void test_updateRecurringEventTicket_successWithUpdateCountSeatNumbers() throws TemplateException, IOException, DocumentException, JAXBException {

        //setup
        long orderId = 1L;
        long newRecurringEventId = 1L;

        TicketingOrderDto ticketingOrderDto = new TicketingOrderDto();
        ticketingOrderDto.setNumberOfTicket(1);
        ticketingOrderDto.setTicketTypeId(1L);
        ticketingOrderDto.setSeatNumbers(Arrays.asList("1", "2"));

        List<TicketingOrderDto> ticketingDto = new ArrayList<>();
        ticketingDto.add(ticketingOrderDto);
        ticketingDto.add(ticketingOrderDto);

        stripeTransaction = getStripeTransaction();

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");

        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);
        TicketingType ticketingType = getTicketingType();

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);
        eventTicketsListForEmail.add(eventTickets);

        success = getSuccessMessage();

        //Mock
        when(stripeTransactionService.findBySourceAndSourceId(StripeTransactionSource.EVENT_TICKETING, orderId)).thenReturn(stripeTransaction);
        when(ticketingOrderService.findByidAndEventid(orderId, event)).thenReturn(ticketingOrder);
        when(ticketingtypeRepository.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(), anyLong())).thenReturn(ticketingType);
        when(ticketingOrderManagerRepository.getTickeDetail(orderId)).thenReturn(oldRecurringEventOrderTicketType);
        when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);

        doNothing().when(seatsIoService).bookTicketsInSeatsIo(any(), any());
        doNothing().when(seatsIoService).changeStatus(any(), any(), any());
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(10L);

        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("TEST");
        doNothing().when(sendGridMailPrepareService).sendTicketingPurchaseEmail(any(), any(), anyList(), anyLong(), anyString(), anyString(), any(), anyString(), any(), any(), anyString(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyBoolean(),anyBoolean(),anyLong(),anyBoolean(),eq(null));
        when(recurringEventsRepository.findById(newRecurringEventId)).thenReturn(Optional.of(recurringEvents));

        //Execution
        String successMsg = recurringEventsScheduleBRServiceImpl.transferOrderToNewRecurringEvent(orderId, newRecurringEventId, event, ticketingDto);

        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<EventTickets>> recurringEventsArgumentCaptor1 = ArgumentCaptor.forClass(listClass);
        verify(eventTicketsRepository, times(1)).saveAll(recurringEventsArgumentCaptor1.capture());

        assertNotEquals(success, successMsg);
    }

    private static Object[] getOrderIds(){
        List<Long> orderIds = new ArrayList<>();
        orderIds.add(1L);
        return new Object[]{
                new Object[]{Collections.emptyList()},
                new Object[]{orderIds},
        };
    }

    @ParameterizedTest
    @MethodSource("getOrderIds")
    void test_cancelSchedule_success(List<Long> orderIds) {

        //setup
        Long recurringEventsScheduleId = id;

        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(id);

        List<RecurringEvents> recurringEventList = new ArrayList<>();
        recurringEventList.add(recurringEvents);

        List<Long> recurringEventIds = new ArrayList<>();
        recurringEventIds.add(1L);


        //mock
        when(eventTicketsRepoService.countSoldTicketForSchedule(id)).thenReturn(false);
        when(recurringEventsRepository.findByRecurringEventScheduleId(anyLong())).thenReturn(recurringEventList);
        when(recurringEventsScheduleRepository.findById(recurringEventsScheduleId)).thenReturn(Optional.of(recurringEventSchedule));
        when(recurringEventsRepository.findById(anyLong())).thenReturn(Optional.of(recurringEvents));
        when(eventTicketsRepoService.checkRecurringTicketSold(id)).thenReturn(false);
        when(recurringEventsRepository.countByEventId(event)).thenReturn(BigInteger.ONE);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);


        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());
        when(ticketingOrderManagerService.getAllOrderIdsByRecurringEventIds(recurringEventIds)).thenReturn(orderIds);

        doNothing().when(ticketingOrderManagerService).updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        doNothing().when(ticketingCouponService).updateTicketCouponRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        doNothing().when(ticketingLimitedDisplayCodeService).updateTicketDisplayCodeRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        doNothing().when(ticketingAccessCodeService).updateAccessCodeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        doNothing().when(ticketHolderRequiredAttributesService).updateTicketHolderAttributeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        doNothing().when(ticketingTypeTicketService).updateTicketingTypeRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        doNothing().when(eventTicketService).updateEventTicketsRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        doNothing().when(embedWidgetSettingService).updateEmbedwidgetSettingRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelSchedule(id, event);
        verify(seatsIoForMultipleEventsImpl, Mockito.times(1)).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());

        ArgumentCaptor<RecurringEvents> recurringEventsArgumentCaptor = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(recurringEventsArgumentCaptor.capture());
        verify(eventTicketsRepoService).countSoldTicketForSchedule(id);
        verify(ticketingOrderManagerService).getAllOrderIdsByRecurringEventIds(anyList());
        verify(ticketingOrderManagerService).updateTicketingOrderManagerRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        verify(ticketingCouponService).updateTicketCouponRecStatusByRecurringEventIds(recurringEventIds,event.getEventId(), RecordStatus.CANCEL);
        verify(ticketingAccessCodeService).updateAccessCodeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        verify(ticketHolderRequiredAttributesService).updateTicketHolderAttributeRecStatusByRecurringEventIds(recurringEventIds,event, RecordStatus.CANCEL);
        verify(ticketingTypeTicketService).updateTicketingTypeRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        verify(eventTicketService).updateEventTicketsRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);
        verify(embedWidgetSettingService).updateEmbedwidgetSettingRecStatusByRecurringEventIds(recurringEventIds, RecordStatus.CANCEL);

        RecurringEvents actual = recurringEventsArgumentCaptor.getValue();
        assertEquals(null, actual.getRecurringEventScheduleId());
        assertEquals(RecurringEvents.RecurringEventStatus.CANCEL, actual.getStatus());
    }

    @Test
    void test_cancelScheduleThrowExceptionCanNotCancelEventSchedule() {

        //setup
        List<Long> soldTicket = new ArrayList<>();
        soldTicket.add(1L);

        //mock
        when(eventTicketsRepoService.countSoldTicketForSchedule(id)).thenReturn(true);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.cancelSchedule(id, event)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CANCEL_SCHEDULE_EVENT.getDeveloperMessage(), exception.getMessage());

        //assert
        verify(eventTicketsRepoService).countSoldTicketForSchedule(id);
    }

    private static Object[] getTicketSold(){
        return new Object[]{
                new Object[]{null},
                new Object[]{(BigInteger.valueOf(-1))},
        };
    }

    @ParameterizedTest
    @MethodSource("getTicketSold")
    void test_cancelRecurringEvent_success(BigInteger ticketSold) {

        //setup
        List<Long> soldTicket = new ArrayList<>();
        soldTicket.add(1L);

        //mock
        when(recurringEventsRepository.findById(anyLong())).thenReturn(Optional.of(recurringEvents));
        when(recurringEventsRepository.countByEventId(any())).thenReturn(BigInteger.ONE);
        when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        doNothing().when(seatsIoForMultipleEventsImpl).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());
        doNothing().when(recurringEventsScheduleBRServiceImpl).updateRecStatusToCancelInAllRelatedTables(any(),anyList());
        when(ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(anyLong())).thenReturn(soldTicket);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(anyLong())).thenReturn(ticketSold);

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelRecurringEvent(id, event);

        //assert
        verify(seatsIoForMultipleEventsImpl, Mockito.times(1)).createOrUpdateMultipleEvent(ticketing.getChartKey(), event.getEventId());

        ArgumentCaptor<RecurringEvents> recurringEventsArgumentCaptor = ArgumentCaptor.forClass(RecurringEvents.class);
        verify(recurringEventsRepository, Mockito.times(1)).save(recurringEventsArgumentCaptor.capture());

        RecurringEvents actual = recurringEventsArgumentCaptor.getValue();
        assertEquals(null, actual.getRecurringEventScheduleId());
        assertEquals(RecurringEvents.RecurringEventStatus.CANCEL, actual.getStatus());
        verify(recurringEventsScheduleBRServiceImpl).updateRecStatusToCancelInAllRelatedTables(any(),anyList());
        verify(ticketingOrderManagerService).getSoldOrderIdsByRecurringEventId(anyLong());
        verify(eventTicketsRepoService).getRecurringHavingTicketSold(anyLong());
        verify(recurringEventsRepository).findById(anyLong());
        verify(recurringEventsRepository).countByEventId(any());
        verify(ticketingHelperService).findTicketingByEvent(any());
//        verify(ticketingRepository).findByEventid(any());
//        verify(recurringEventsRepository).findMaxRecurringEventDate(any(),any());
    }

    @Test
    void test_cancelRecurringEvent_ThrowErrorCanNotCancelEvent() {

        //setup
        List<Long> soldTicket = new ArrayList<>();
        soldTicket.add(1L);

        //mock
        when(ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(anyLong())).thenReturn(soldTicket);
        when(eventTicketsRepoService.getRecurringHavingTicketSold(anyLong())).thenReturn((BigInteger.valueOf(1)));

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> recurringEventsScheduleBRServiceImpl.cancelRecurringEvent(id, event)
        );
        assertEquals(NotAcceptableException.TicketingExceptionMsg.CAN_NOT_CANCEL_RECURRING_EVENT.getDeveloperMessage(), exception.getMessage());

        //assert
        verify(ticketingOrderManagerService).getSoldOrderIdsByRecurringEventId(anyLong());
        verify(eventTicketsRepoService).getRecurringHavingTicketSold(anyLong());
    }

    @Test
    void test_cancelRecurringEvent_SoldTicketEmpty() {

        //SetUp
        RecurringEvents recurringEvent = new RecurringEvents();
        recurringEvent.setId(1L);
        //mock
        when(ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(anyLong())).thenReturn(Collections.emptyList());

        when(recurringEventsRepository.findById(1L)).thenReturn(Optional.of(recurringEvent));
        doNothing().when(recurringEventsScheduleBRServiceImpl).updateSeatsIo(any());

        //Execution
        recurringEventsScheduleBRServiceImpl.cancelRecurringEvent(id, event);

        //assert
        verify(recurringEventsScheduleBRServiceImpl).deleteRecEvents(anyLong(),any(), any());
    }

    @Test
    void test_getRecurringEventById_success() {

        //Setup
        Long recurringEventId = id;

        recurringEvents.setLocation("Ahm");

        //Mock
        when(recurringEventsRepository.findById(recurringEventId)).thenReturn(Optional.of(recurringEvents));

        //Execution
        Optional<RecurringEvents> recurringData = recurringEventsScheduleBRServiceImpl.getRecurringEventById(recurringEventId);

        assertEquals(recurringData.get().getLocation(), recurringEvents.getLocation());

        verify(recurringEventsRepository, Mockito.times(1)).findById(recurringEventId);
    }

    @Test
    void test_getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc_success() {

        //setup
        List<RecurringEvents> recurringEventList = new ArrayList<>();
        recurringEventList.add(recurringEvents);

        //Mock
        when(recurringEventsRepository.findByEventIdOrderByRecurringEventStartDateAsc(event)).thenReturn(recurringEventList);

        //Execution
        List<RecurringEvents> recurringEventData = recurringEventsScheduleBRServiceImpl.getRecurringEventsByEventIdOrderByRecurringEventStartDateAsc(event);

        assertEquals(recurringEventData.iterator().next().getId(), recurringEvents.getId());

        verify(recurringEventsRepository, Mockito.times(1)).findByEventIdOrderByRecurringEventStartDateAsc(event);
    }

    @Test
    void test_getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc_success() {

        //Mock
        when(recurringEventsRepository.findRecurringEventIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId(), RecurringEvents.RecurringEventStatus.CANCEL)).thenReturn(Collections.singletonList(1L));

        //Execution
        List<Long> recurringEventIds = recurringEventsScheduleBRServiceImpl.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId());

        assertEquals(recurringEventIds.iterator().next().longValue(), event.getEventId());

        verify(recurringEventsRepository, Mockito.times(1)).findRecurringEventIdByEventIdOrderByRecurringEventStartDateAsc(event.getEventId(), RecurringEvents.RecurringEventStatus.CANCEL);
    }

    @Test
    void test_findRecurringEventByDateAndTime_success() {

        //mock
        when(recurringEventsRepository.findByStartDateAndEndDate(startDate1, endDate1, event)).thenReturn(recurringEvents);

        //Execution
        RecurringEvents recurringEventsData = recurringEventsScheduleBRServiceImpl.findRecurringEventByDateAndTime(startDate1, endDate1, event);

        assertEquals(recurringEventsData.getId(), recurringEvents.getId());
        assertEquals(recurringEventsData.getCheckInXMinutesBefore(), recurringEvents.getCheckInXMinutesBefore());
        assertEquals(recurringEventsData.getRecurringEventStartDate(), recurringEvents.getRecurringEventStartDate());
    }

    @Test
    void test_getUpcomingRecurringEvents_success() {

        //setup
        int page = 1;
        int size = 5;

        List<RecurringEvents> recurringEventList = new ArrayList<>();
        recurringEventList.add(recurringEvents);

        PageRequest recurringEventStartDate = PageRequest.of(page, size, Sort.Direction.ASC, "recurringEventStartDate");

        //mock
        Page<RecurringEvents> recurringEventPage = Mockito.mock(Page.class);
        recurringEventPage.getTotalElements();
        when(recurringEventsRepository.getUpcomingRecurringEvents(any(), any(), any())).thenReturn(recurringEventPage);

        //Execution
        Page<RecurringEvents> upcomingRecurringEventsData = recurringEventsScheduleBRServiceImpl.getUpcomingRecurringEvents(event, page, size);

        assertNotNull(upcomingRecurringEventsData);
    }

    @Test
    void test_getPastRecurringEvents_success() {

        //setup
        int page = 1;
        int size = 5;

        List<RecurringEvents> recurringEventList = new ArrayList<>();
        recurringEventList.add(recurringEvents);

        //mock
        Page<RecurringEvents> recurringEventPage = Mockito.mock(Page.class);
        when(recurringEventsRepository.getPastRecurringEvents(any(), any(), any())).thenReturn(recurringEventPage);

        //Execution
        Page<RecurringEvents> upcomingRecurringEventsData = recurringEventsScheduleBRServiceImpl.getPastRecurringEvents(event, page, size);

        assertNotNull(upcomingRecurringEventsData);
    }

    @Test
    void test_deleteTicketingTypesByListoFRecurringEvents_success() {

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteTicketingTypesByfRecurringEvent(recurringEvents.getId());

        verify(ticketingTypeService, Mockito.times(1)).deleteByRecurringEventId(recurringEvents.getId());
    }

    @Test
    void test_getRecurringEventByIdsIn_success_with_recurringEventIdList_empty(){

        //setup
        Set<Long> recurringEventIds = new HashSet<>();

        //Execution
        Map<Long, String> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventByIdsIn(recurringEventIds);

        assertTrue(actualData.isEmpty());
    }

    @Test
    void test_getRecurringEventByIdsIn_success(){

        //setup
        Set<Long> recurringEventIds = new HashSet<>();
        recurringEventIds.add(id);

        List<RecurringIdAndDateDto> recurringIdAndDateDtoList =  new ArrayList<>();
        recurringIdAndDateDtoList.add(recurringIdAndDateDto);

        //mock
        when(recurringEventsRepository.getrecurringEventByRecurringEventIds(recurringEventIds)).thenReturn(recurringIdAndDateDtoList);

        //Execution
        Map<Long, String> actualData = recurringEventsScheduleBRServiceImpl.getRecurringEventByIdsIn(recurringEventIds);

        assertTrue(actualData.containsValue(recurringIdAndDateDto.getStartDateStr()));
    }

    @Test
    void test_sendEmailDuringTransferOrder_success(){

        //setup
        String lastFour = "1111";
        String cardType = CC;

        List<EventTickets> eventTicketsList = new ArrayList<>();

        //mock
        when(eventCommonRepoService.findByTicketingOrder(anyLong())).thenReturn(eventTicketsList);
        when(ticketingHelperService.getTicketOrderHeaderText(event)).thenReturn("headerText");

        //Execution
        recurringEventsScheduleBRServiceImpl.sendEmailDuringTransferOrder(id, event, lastFour, cardType, ticketingOrder, ticketing);
    }

    @Test
    void test_getSuccessMessage_success_with_empty() {

        //setup
        long newRecurringEventId = id;
        long oldRecurringEventId = id;
        long orderId = id;

        TicketDetailDto ticketDetailDto = new TicketDetailDto(1L, TicketType.PAID,1, orderId, newRecurringEventId, 1L, 1559992034425L, "");

        List<TicketDetailDto> oldRecurringEventOrderTicketType = new ArrayList<>();
        oldRecurringEventOrderTicketType.add(ticketDetailDto);

        List<EventTickets> eventTicketsListForEmail = new ArrayList<>();
        eventTicketsListForEmail.add(eventTickets);

        //mock
        when(recurringEventsRepository.findById(oldRecurringEventId)).thenReturn(Optional.empty());

        //Execution
        String successMsg = recurringEventsScheduleBRServiceImpl.getSuccessMessage(newRecurringEventId, event, ticketingOrder, oldRecurringEventOrderTicketType);

        //Assertion
        assertEquals("", successMsg);
    }

    private String getSuccessMessage() {
        String oldStartDate = StringTools.formatCalanderDate(TimeZoneUtil.getDateInLocal(newRecurringEvents.getRecurringEventStartDate(), event.getEquivalentTimeZone()), true, true);
        String oldStartTime = StringTools.formatCalanderTime(TimeZoneUtil.getDateInLocal(newRecurringEvents.getRecurringEventStartDate(), event.getEquivalentTimeZone()), false);
        String newStartDate = StringTools.formatCalanderDate(TimeZoneUtil.getDateInLocal(oldRecurringEvents.getRecurringEventStartDate(), event.getEquivalentTimeZone()), true, true);
        String newStartTime = StringTools.formatCalanderTime(TimeZoneUtil.getDateInLocal(oldRecurringEvents.getRecurringEventStartDate(), event.getEquivalentTimeZone()), false);
        success = Constants.ORDER_TRANSFER_SUCCESSFULLY.replace("${order_id}", String.valueOf(ticketingOrder.getId()))
                .replace("${old_event_date}", oldStartDate.concat(" ").concat(oldStartTime))
                .replace("${new_event_date}", newStartDate.concat(" ").concat(newStartTime));

        return success;
    }

    @Test
    void test_deleteOrderAndOrderManager_success() {

        //setup
        List<Long> orderIdsToBeDelete = new ArrayList<>();
        orderIdsToBeDelete.add(1L);
        orderIdsToBeDelete.add(2L);

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteOrderAndOrderManager(orderIdsToBeDelete);

        Class<ArrayList<Long>> listClass = (Class<ArrayList<Long>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<Long>> argumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketingOrderManagerRepository, Mockito.times(1)).deleteTicketingOrderManagerByListOfOrderIds(argumentCaptor.capture());
        verify(ticketingOrderRepository, Mockito.times(1)).deleteByIdIn(argumentCaptor.capture());
    }

    @Test
    void test_deleteOrderAndOrderManager_success_with_emptyOrderId() {

        //setup
        List<Long> orderIdsToBeDelete = new ArrayList<>();

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteOrderAndOrderManager(orderIdsToBeDelete);
    }

    @Test
    void test_deleteOrderAndOrderManagerByRecurringEventId_success_with_orderIdsInProgress_null() {

        //setup
        long recurringEventId = id;

        //mock
        when(ticketingOrderManagerService.getSoldOrderIdsByRecurringEventId(id)).thenReturn(Arrays.asList(1L, 2L));
        when(ticketingOrderManagerService.getOrderIdsAreInProgressByRecurringEventId(id)).thenReturn(null);

        //Execution
        recurringEventsScheduleBRServiceImpl.deleteOrderAndOrderManagerByRecurringEventId(recurringEventId);

        Class<ArrayList<Long>> listClass = (Class<ArrayList<Long>>)(Class)ArrayList.class;
        ArgumentCaptor<ArrayList<Long>> argumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketingOrderManagerRepository, Mockito.times(1)).deleteTicketingOrderManagerByListOfOrderIds(argumentCaptor.capture());
        verify(ticketingOrderRepository, Mockito.times(1)).deleteByIdIn(argumentCaptor.capture());
    }

    @Test
    void test_getFirstRecId_STRING_EMPTY(){
        ticketing.setRecurringEvent(false);
        String expected = recurringEventsScheduleBRServiceImpl.getFirstRecId(event, ticketing);
        assertEquals(expected, STRING_EMPTY);
    }

    @Test
    void test_getFirstRecId_Recurring_True(){
        ticketing.setRecurringEvent(true);
        String expected = recurringEventsScheduleBRServiceImpl.getFirstRecId(event, ticketing);
        assertEquals(expected, STRING_EMPTY);
    }

    @Test
    void test_getFirstRecIdRecurringTrueWithFirstRecIdPresent(){
        ticketing.setRecurringEvent(true);
        List<Long> listOfLong = new ArrayList<>();
        listOfLong.add(1L);
        listOfLong.add(2L);
        when(recurringEventsScheduleBRServiceImpl.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(1L)).thenReturn(listOfLong);
        String expected = recurringEventsScheduleBRServiceImpl.getFirstRecId(event, ticketing);
        assertEquals(expected, listOfLong.get(0).toString());
    }

    @Test
    void test_getFirstRecIdRecurringTrueWithFirstRecIdNotPresentPresent(){
        ticketing.setRecurringEvent(true);
        when(recurringEventsScheduleBRServiceImpl.getRecurringEventsIdByEventIdOrderByRecurringEventStartDateAsc(1L)).thenReturn(Collections.EMPTY_LIST);
        String expected = recurringEventsScheduleBRServiceImpl.getFirstRecId(event, ticketing);
        assertEquals(expected, StringUtils.EMPTY);
    }
}