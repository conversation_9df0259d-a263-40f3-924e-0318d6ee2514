package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.ItemInstructionDto;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.BiddingSource;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.ModuleStatus;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.dto.DataTableResponse;
import com.accelevents.event.EventModules;
import com.accelevents.event.services.EventEndingService;
import com.accelevents.helpers.CalculateFees;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.perfomance.dto.RaffleItemData;
import com.accelevents.perfomance.dto.RaffleWinnerDto;
import com.accelevents.raffle.dto.RaffleTicketPurchaseDto;
import com.accelevents.repositories.RaffleRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.FeeConstants;
import com.cloudinary.utils.StringUtils;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.*;

import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RaffleServiceImplTest {

	@Spy
	@InjectMocks
	private RaffleServiceImpl raffleServiceImpl;
	
	@Mock
	private TextMessageUtils textMessageUtils;
	@Mock
	private ItemService itemService;
	@Mock
	private PurchasedRaffleTicketService purchasedRaffleTicketService;
	@Mock
	private EventService eventService;
	@Mock
	private SubmittedRaffleTicketService submittedRaffleTicketService;
	@Mock
	private RaffleRepository raffleRepository;
	@Mock
	private ModuleService moduleService;
	@Mock
	private ROStripeService roStripeService;
	@Mock
	private RaffleTicketService raffleTicketService;
	@Mock
	private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;
	@Mock
	private PaymentHandlerService paymentHandlerService;
	@Mock
	private EventEndingService eventEndingService;
	@Mock
	private SendGridMailPrepareService sendGridMailPrepareService;
	@Mock
	private ItemDistributionService itemDistributionService;
	@Mock
	private PhoneNumberService phoneNumberService;
	@Mock
	private ROUserService roUserService;
	@Mock
	private TextMessageService textMessageService;
	@Mock
	private ClearAPIGatewayCache clearAPIGatewayCache;
    @Mock
    private ROEventService roEventService;

	private Event event;
	private User user;


	@Test
	void testServiceRequestInCaseItsCompCodeForCompTicketCodeNotAvailable(){
		
		Raffle raffle = new Raffle();
		raffle.setCompTicketCode(null);
		Event event = new Event();
		User user = new User();
		String messageBody = "Message Body";
		
		String expectedMessage=null;
		

		
		String message = raffleServiceImpl.serviceRequestInCaseItsCompCode(raffle, event, user, messageBody);
		
		assertEquals(expectedMessage, message);
		
		raffle.setCompTicketCode("");

		message = raffleServiceImpl.serviceRequestInCaseItsCompCode(raffle, event, user, messageBody);
		assertEquals(expectedMessage, message);
	}
	
	@Test
	void testServiceRequestInCaseItsCompCodeForValidCompTicketCode(){
		
		Raffle raffle = new Raffle();
		raffle.setId(1L);
		raffle.setCompTicketCode("Purchasing,Something");
		Event event = new Event();
		event.setRaffleId(1L);
		User user = new User();
		String messageBody = "Purchasing";
		
		String expectedMessage="Message";
		
		boolean isComplimentaryTicketUsed = true;
		when(purchasedRaffleTicketService.isComplimentaryTicketUsed(user, raffle.getId())).thenReturn(isComplimentaryTicketUsed);
		when(textMessageUtils.getComplimentaryTicketMessage(event, user, isComplimentaryTicketUsed)).thenReturn(expectedMessage);
		
		String message = raffleServiceImpl.serviceRequestInCaseItsCompCode(raffle, event, user, messageBody);
		assertEquals(expectedMessage, message);
		
		raffle.setCompTicketCode("Anything");
		message = raffleServiceImpl.serviceRequestInCaseItsCompCode(raffle, event, user, messageBody);
		assertNull(message);
	}
	
	@Test
	void testServiceRequestInCaseItsCompCodeForValidCompTicketCodeWithCompTicketUsed(){
		
		Raffle raffle = new Raffle();
		raffle.setId(1L);
		raffle.setCompTicketCode("Purchasing,Something");
		Event event = new Event();
		event.setRaffleId(1L);
		User user = new User();
		String messageBody = "Purchasing";
		
		String expectedMessage="Message";
		
		boolean isComplimentaryTicketUsed = false;
		when(textMessageUtils.getComplimentaryTicketMessage(event, user, isComplimentaryTicketUsed)).thenReturn(expectedMessage);
		when(purchasedRaffleTicketService.isComplimentaryTicketUsed(user, raffle.getId())).thenReturn(isComplimentaryTicketUsed);
		String message = raffleServiceImpl.serviceRequestInCaseItsCompCode(raffle, event, user, messageBody);
		
		assertEquals(expectedMessage, message);
		
		ArgumentCaptor<PurchasedRaffleTicket> captor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService).save(captor.capture());
		
		PurchasedRaffleTicket value = captor.getValue();
		assertEquals(value.getRaffleId(), raffle.getId());
		assertEquals(value.getUser(), user);
		assertEquals(value.getTicketsPurchased(), 1);
		assertEquals(value.getPrice(), 0);
	}
	
	@Test
	void testBuyForRaffleDisabled() {
		Event event = new Event();
		event.setRaffleEnabled(false);
		boolean isStaff = false;
		User user = null;
		long attemptedSubmittedTickets = 0;
		Item item = null;
		String actualMessage = "This is a raffle item. Raffles are not configured yet.";
		String message = raffleServiceImpl.buy(event, attemptedSubmittedTickets, item, user, isStaff);
		assertEquals(actualMessage, message);
	}
	
	@Test
	void testBuyForRaffleEnabledBeforeEndDate() {
		Event event = new Event();
		event.setRaffleEnabled(true);
		boolean isStaff = false;
		User user = null;
		long attemptedSubmittedTickets = 0;
		Item item = null;

		Raffle raffle = new Raffle();
		final Calendar cal = Calendar.getInstance();
	    cal.add(Calendar.DATE, -1);
		raffle.setEndDate(cal.getTime());
		when(raffleServiceImpl.findByEvent(event)).thenReturn(raffle);
		
		raffleServiceImpl.buy(event, attemptedSubmittedTickets, item, user, isStaff);
		
		verify(textMessageUtils).getRaffleModuleEndedMessage();
	}
	
	@Test
	void testBuyForRaffleEnabledAfterEndDateAndRaffleDeactivatedAndNotStaff() {
		Event event = new Event();
		event.setRaffleEnabled(true);
		User user = null;
		long attemptedSubmittedTickets = 0;
		Item item = null;

		Raffle raffle = new Raffle();
		final Calendar cal = Calendar.getInstance();
	    cal.add(Calendar.DATE, 1);
		raffle.setEndDate(cal.getTime());
		when(raffleServiceImpl.findByEvent(event)).thenReturn(raffle);
		
		raffle.setActivated(false);
		boolean isStaff = false;
		
		raffleServiceImpl.buy(event, attemptedSubmittedTickets, item, user, isStaff);
		
		verify(textMessageUtils).getRaffleModuleNotActivatedMessage();
	}
	
	@Test
	void testBuyForRaffleEnabledAfterEndDateAndRaffleActivatedAndNotStaff() {
		Event event = new Event();
		event.setRaffleEnabled(true);
		User user = null;
		long attemptedSubmittedTickets = 1;
		Item item = null;

		Raffle raffle = new Raffle();
		final Calendar cal = Calendar.getInstance();
	    cal.add(Calendar.DATE, 1);
		raffle.setEndDate(cal.getTime());
		when(raffleServiceImpl.findByEvent(event)).thenReturn(raffle);
		
		raffle.setActivated(true);
		boolean isStaff = false;
		
		long submittedTickets = 1L;
		when(submittedRaffleTicketService.getTotalSubmittedTicketsByUserForRaffle(user,
						raffle.getId())).thenReturn(submittedTickets);
		
		long purchasedTickets = 10L;
		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user,
				raffle.getId())).thenReturn(purchasedTickets);
		
		SubmittedRaffleTicket ticketData = new SubmittedRaffleTicket(raffle.getId(), user, item,
				attemptedSubmittedTickets, BiddingSource.SMS, new Date());

		
		raffleServiceImpl.buy(event, attemptedSubmittedTickets, item, user, isStaff);
		
		verify(submittedRaffleTicketService).getTotalSubmittedTicketsByUserForRaffle(user, raffle.getId());
		verify(purchasedRaffleTicketService).getTotalTicketsByUserAndRaffle(user, raffle.getId());
		verify(textMessageUtils).getSuccessfulTicketSubmissionMessage(event, user, item, submittedTickets, purchasedTickets, attemptedSubmittedTickets);
	}
	
	@Test
	void testBuyForRaffleEnabledAfterEndDateAndRaffleDeactivatedAndStaff() {
		Event event = new Event();
		event.setRaffleEnabled(true);
		User user = null;
		long attemptedSubmittedTickets = 0;
		Item item = null;

		Raffle raffle = new Raffle();
		final Calendar cal = Calendar.getInstance();
	    cal.add(Calendar.DATE, 1);
		raffle.setEndDate(cal.getTime());
		when(raffleServiceImpl.findByEvent(event)).thenReturn(raffle);
		
		raffle.setActivated(false);
		boolean isStaff = true;
		
		
		long submittedTickets = 1L;
		when(submittedRaffleTicketService.getTotalSubmittedTicketsByUserForRaffle(user,
						raffle.getId())).thenReturn(submittedTickets);
		
		long purchasedTickets = 0L;
		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user,
				raffle.getId())).thenReturn(purchasedTickets);
		
		raffleServiceImpl.buy(event, attemptedSubmittedTickets, item, user, isStaff);
		
		verify(submittedRaffleTicketService).getTotalSubmittedTicketsByUserForRaffle(user, raffle.getId());
		verify(purchasedRaffleTicketService).getTotalTicketsByUserAndRaffle(user, raffle.getId());
		verify(textMessageUtils).getInsufficentTicketMessage(event, user, submittedTickets, purchasedTickets);
	}
	
	@Test
	void testSave(){
		Raffle raffle = new Raffle(1);

        when(raffleRepository.save(raffle)).thenReturn(raffle);
		raffleServiceImpl.save(raffle);
		verify(raffleRepository).save(raffle);
	}
	
	@Test
	void testFindById(){
		long raffleId = 1;
		Raffle raffle = new Raffle();
		raffle.setId(raffleId);
		
		when(raffleRepository.findById(raffleId)).thenReturn(Optional.of(raffle));
		
		Raffle findById = raffleServiceImpl.find(raffleId);
		
		assertEquals(raffle, findById);
	}
	
	@Test
	void testFindByEvent(){
		Event event = new Event();
		event.setEventId(1);
		
		Raffle raffle = new Raffle();
		raffle.setEventId(event.getEventId());
		
		when(raffleRepository.findByEventId(event.getEventId())).thenReturn(raffle);
		
		Raffle findByEvent = raffleServiceImpl.findByEvent(event);
		assertEquals(raffle, findByEvent);
	}
	
	@Test
	void testGetRafflesByRaffleStatus(){
		ModuleStatus raffleStatus = ModuleStatus.EVENT_HAPPENING;
		
		List<Raffle> raffle = new ArrayList<Raffle>();
		when(raffleRepository.findByRaffleStatus(raffleStatus)).thenReturn(raffle);
		
		List<Raffle> rafflesByRaffleStatus = raffleServiceImpl.getRafflesByRaffleStatus(raffleStatus);
		assertEquals(rafflesByRaffleStatus, raffle);
	}
	
	@Test
	void testGetUnprocessedRaffles(){
		List<Raffle> raffle = new ArrayList<Raffle>();

		
		List<Raffle> raffles = raffleServiceImpl.getUnprocessedRaffles();
		assertEquals(raffles, raffle);
	}
	
	@Test
	void testGetAvailableTicketsForUser(){
		User user = new User();
		long raffleId = 1;
		
		long submittedTickets = 1;
		when(submittedRaffleTicketService.getTotalSubmittedTicketsByUserForRaffle(user, raffleId)).thenReturn(submittedTickets);
		
		long purchasedTickets = 1;
		when(purchasedRaffleTicketService.getTotalTicketsByUserAndRaffle(user, raffleId)).thenReturn(purchasedTickets);
		
		long availableTickets = raffleServiceImpl.getAvailableTicketsForUser(raffleId, user);
		assertEquals((purchasedTickets - submittedTickets), availableTickets);
	}
	
	@Test
	void testGetRaffleItemCategoriesWithItemCount(){
		long raffleId = 1;
		List<Map<String, Object>> raffleItemCat = new ArrayList<>();
		when(moduleService.getItemCategoriesWithItemCount(raffleId, ModuleType.RAFFLE)).thenReturn(raffleItemCat);
		
		List<Map<String, Object>> raffleItemCats = raffleServiceImpl.getRaffleItemCategoriesWithItemCount(raffleId);
		assertEquals(raffleItemCat, raffleItemCats);
	}

	@Test
	void test_purchaseTickets_withAutoSubmitOnPurchaseTrueAndStaffUserNotPresent() throws StripeException, ApiException {

		//setup
		double paidAmount = 100.00d;
		Raffle raffle = new Raffle();
		raffle.setId(1L);
		raffle.setAutoSubmitOnPurchase(true);
		event = EventDataUtil.getEvent();
		event.setRaffleId(raffle.getId());
		event.setCreditCardEnabled(true);
		user = EventDataUtil.getUser();
		user.setLanguageCode("EN");
		user.setPhoneNumber(1234567890);
		RaffleTicketPurchaseDto ticketPurchaseDto = new RaffleTicketPurchaseDto();
		ticketPurchaseDto.setRaffleTicketId(1L);
        ticketPurchaseDto.setTokenOrIntentId("tokenIntent");
		String paymentType = Constants.CC;
		boolean isTerminalPayment = true;
		Stripe stripe = new Stripe();
		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
		RaffleTicket raffleTicket = new RaffleTicket();
		raffleTicket.setPrice(100);
		raffleTicket.setNumOfTicket(50);
		int numberOfTicket = raffleTicket.getNumOfTicket();
		String ticket = numberOfTicket >1 ? "tickets" : "ticket";
		String message = "Thank you for purchasing "+ numberOfTicket +" "+ticket+" for "+ event.getName();
		Staff staff = new Staff();
		staff.setId(1L);
		double stripeFee = CalculateFees.getStripeFee(paidAmount, raffleTicket.getPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1);

		//mock
		Mockito.doNothing().when(raffleServiceImpl).checkIsRaffleActive(any(), any());
		when(raffleRepository.findByEventId(anyLong())).thenReturn(raffle);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));

		when(paymentHandlerService.getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class))).thenReturn(paidAmount);
		Mockito.doNothing().when(eventEndingService).submitRaffleTickets(anyLong());
		Mockito.doNothing().when(sendGridMailPrepareService).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
		raffle.setActivated(true);
        when(roEventService.getLanguageCodeByUserOrEvent(any(),any())).thenReturn("EN");

		//Execution
		String rafflePurchaseMessage = raffleServiceImpl.purchaseTickets(user, event, ticketPurchaseDto, null, paymentType, isTerminalPayment);

		//Assertion
		assertEquals(rafflePurchaseMessage, message);

		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService).save(purchasedRaffleTicketArgumentCaptor.capture());

		PurchasedRaffleTicket purchasedRaffleTicket = purchasedRaffleTicketArgumentCaptor.getValue();
		assertEquals(purchasedRaffleTicket.getRaffleId(), event.getRaffleId());
		assertEquals(purchasedRaffleTicket.getTicketsPurchased(), raffleTicket.getNumOfTicket());
		assertEquals(purchasedRaffleTicket.getUser().getUserId(), user.getUserId());
		assertEquals(purchasedRaffleTicket.getPrice(), raffleTicket.getPrice());
		assertTrue(purchasedRaffleTicket.getStripePaidAmount() == raffleTicket.getPrice());
		assertTrue(purchasedRaffleTicket.getStripeFee() == stripeFee);

		verify(textMessageService,times(0)).sendText(any());
		verify(raffleServiceImpl).checkIsRaffleActive(any(), any());
		verify(raffleRepository).findByEventId(anyLong());
		verify(roStripeService).findByEvent(any());
		verify(raffleTicketService).findByIdAndRaffleId(anyLong(), anyLong());
		verify(paymentHandlerService).getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class));
		verify(eventEndingService).submitRaffleTickets(anyLong());
		verify(sendGridMailPrepareService).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
	}

	@Test
	void test_purchaseTickets_withAutoSubmitOnPurchaseTrue() throws StripeException, ApiException {

		//setup
		double paidAmount = 100.00d;
		Raffle raffle = new Raffle();
		raffle.setId(1L);
		raffle.setAutoSubmitOnPurchase(true);
		event = EventDataUtil.getEvent();
		event.setRaffleId(raffle.getId());
		event.setCreditCardEnabled(true);
		user = EventDataUtil.getUser();
		user.setLanguageCode("EN");
		user.setPhoneNumber(1234567890);
		RaffleTicketPurchaseDto ticketPurchaseDto = new RaffleTicketPurchaseDto();
		ticketPurchaseDto.setRaffleTicketId(1L);
        ticketPurchaseDto.setTokenOrIntentId("tokenIntent");
		User staffUser = new User();
		staffUser.setUserId(2L);
		String paymentType = Constants.CC;
		boolean isTerminalPayment = true;
		Stripe stripe = new Stripe();
		stripe.setCCFlatFee(FeeConstants.CREDIT_CARD_PROCESSING_FLAT);
		stripe.setCCPercentageFee(FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE);
		RaffleTicket raffleTicket = new RaffleTicket();
		raffleTicket.setPrice(100);
		raffleTicket.setNumOfTicket(50);
		int numberOfTicket = raffleTicket.getNumOfTicket();
		String ticket = numberOfTicket >1 ? "tickets" : "ticket";
		String message = "Thank you for purchasing "+ numberOfTicket +" "+ticket+" for "+ event.getName();
		Staff staff = new Staff();
		staff.setId(1L);
		double stripeFee = CalculateFees.getStripeFee(paidAmount, raffleTicket.getPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1);

		//mock
		Mockito.doNothing().when(raffleServiceImpl).checkIsRaffleActive(any(), any());
		when(raffleRepository.findByEventId(anyLong())).thenReturn(raffle);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
		when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(staff);
		when(paymentHandlerService.getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class))).thenReturn(paidAmount);
		Mockito.doNothing().when(eventEndingService).submitRaffleTickets(anyLong());
		Mockito.doNothing().when(sendGridMailPrepareService).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
        raffle.setActivated(true);
        when(roEventService.getLanguageCodeByUserOrEvent(any(),any())).thenReturn("EN");

		//Execution
		String rafflePurchaseMessage = raffleServiceImpl.purchaseTickets(user, event, ticketPurchaseDto, staffUser, paymentType, isTerminalPayment);

		//Assertion
		assertEquals(rafflePurchaseMessage, message);

		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService).save(purchasedRaffleTicketArgumentCaptor.capture());

		PurchasedRaffleTicket purchasedRaffleTicket = purchasedRaffleTicketArgumentCaptor.getValue();
		assertEquals(purchasedRaffleTicket.getRaffleId(), event.getRaffleId());
		assertEquals(purchasedRaffleTicket.getTicketsPurchased(), raffleTicket.getNumOfTicket());
		assertEquals(purchasedRaffleTicket.getUser().getUserId(), user.getUserId());
		assertEquals(purchasedRaffleTicket.getPrice(), raffleTicket.getPrice());
		assertEquals(purchasedRaffleTicket.getStaffId(), staff.getId());
		assertEquals(purchasedRaffleTicket.getStaffUserId().getUserId(), staffUser.getUserId());
		assertTrue(purchasedRaffleTicket.getStripePaidAmount() == raffleTicket.getPrice());
		assertTrue(purchasedRaffleTicket.getStripeFee() == stripeFee);

		verify(textMessageService).sendText(any());
		verify(raffleServiceImpl).checkIsRaffleActive(any(), any());
		verify(raffleRepository).findByEventId(anyLong());
		verify(roStripeService).findByEvent(any());
		verify(raffleTicketService).findByIdAndRaffleId(anyLong(), anyLong());
		verify(roStaffService).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
		verify(paymentHandlerService).getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class));
		verify(eventEndingService).submitRaffleTickets(anyLong());
		verify(sendGridMailPrepareService).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
	}

	@Test
	void test_purchaseTickets_withAutoSubmitOnPurchaseFalse() throws StripeException, ApiException {

		//setup
		double paidAmount = 100.00d;
		Raffle raffle = new Raffle();
		raffle.setId(1L);
		raffle.setAutoSubmitOnPurchase(false);
		event = EventDataUtil.getEvent();
		event.setRaffleId(raffle.getId());
		event.setCreditCardEnabled(true);
		user = EventDataUtil.getUser();
		user.setPhoneNumber(0L);
		RaffleTicketPurchaseDto ticketPurchaseDto = new RaffleTicketPurchaseDto();
		ticketPurchaseDto.setRaffleTicketId(1L);
        ticketPurchaseDto.setTokenOrIntentId("tokenIntent");
		User staffUser = new User();
		staffUser.setUserId(2L);
		String paymentType = Constants.CC;
		boolean isTerminalPayment = true;
		Stripe stripe = new Stripe();
		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
		RaffleTicket raffleTicket = new RaffleTicket();
		raffleTicket.setPrice(100);
		raffleTicket.setNumOfTicket(50);
		Staff staff = new Staff();
		staff.setId(1L);
		double stripeFee = CalculateFees.getStripeFee(paidAmount, raffleTicket.getPrice(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), 1);
		String message = raffleTicket.getNumOfTicket() + Constants.TICKETS_ADDED;

		//mock
		Mockito.doNothing().when(raffleServiceImpl).checkIsRaffleActive(any(), any());
		when(raffleRepository.findByEventId(anyLong())).thenReturn(raffle);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
		when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(staff);
		when(paymentHandlerService.getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class))).thenReturn(paidAmount);
		Mockito.doNothing().when(sendGridMailPrepareService).sendBuyerReceiptRaffle(any(), any(), any(), anyDouble(), anyString());
        raffle.setActivated(true);


		//Execution
		String rafflePurchaseMessage = raffleServiceImpl.purchaseTickets(user, event, ticketPurchaseDto, staffUser, paymentType, isTerminalPayment);

		//Assertion
		assertEquals(rafflePurchaseMessage, message);

		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService).save(purchasedRaffleTicketArgumentCaptor.capture());

		PurchasedRaffleTicket purchasedRaffleTicket = purchasedRaffleTicketArgumentCaptor.getValue();
		assertEquals(purchasedRaffleTicket.getRaffleId(), event.getRaffleId());
		assertEquals(purchasedRaffleTicket.getTicketsPurchased(), raffleTicket.getNumOfTicket());
		assertEquals(purchasedRaffleTicket.getUser().getUserId(), user.getUserId());
		assertEquals(purchasedRaffleTicket.getPrice(), raffleTicket.getPrice());
		assertEquals(purchasedRaffleTicket.getStaffId(), staff.getId());
		assertEquals(purchasedRaffleTicket.getStaffUserId().getUserId(), staffUser.getUserId());
		assertTrue(purchasedRaffleTicket.getStripePaidAmount() == raffleTicket.getPrice());
		assertTrue(purchasedRaffleTicket.getStripeFee() == stripeFee);

		verify(raffleServiceImpl).checkIsRaffleActive(any(), any());
		verify(raffleRepository).findByEventId(anyLong());
		verify(roStripeService).findByEvent(any());
		verify(raffleTicketService).findByIdAndRaffleId(anyLong(), anyLong());
		verify(roStaffService).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
		verify(paymentHandlerService).getPaidAmountForRaffleAfterStripeTransaction(isA(User.class), isA(Event.class), isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), anyString(), anyBoolean(), nullable(User.class), isA(Stripe.class));
	}

	@Test
	void test_updateInstructionForRaffleInstructionNOTFound(){

		Event event = new Event();
		event.setRaffleId(1);

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = null;
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//mock
		when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getRaffleId(),  ModuleType.RAFFLE)).thenReturn(itemInstructionDto);

		//execution
		String actualResult = raffleServiceImpl.updateInstructionForRaffle(event, ph, user, languageMap);
		assertTrue(Constants.STRING_EMPTY.equalsIgnoreCase(actualResult));
	}

	@Test
	void test_updateInstructionForRaffle_ItemInstructionFound(){

		Event event = new Event();
		event.setRaffleId(1);
		event.setCurrency(Currency.USD);
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		String ph = "123123123";
		ItemInstructionDto itemInstructionDto = new ItemInstructionDto();
		itemInstructionDto.setCode("AAA");
		itemInstructionDto.setStartingBid(50);
		//mock
		when(itemService.getItemCodeAndStartingBidOfItemWithLowestPosition(event.getRaffleId(),  ModuleType.RAFFLE)).thenReturn(itemInstructionDto);

		//execution
		String actualResult = raffleServiceImpl.updateInstructionForRaffle(event, ph, user, languageMap);
		assertTrue("Submit your raffle tickets here or submit via text message to: 123123123 with the item's three letter code and number of raffle tickets ex. AAA10".equalsIgnoreCase(actualResult));
	}

	@Test
	void test_getRafflePerformancePageData(){

		//SetUp
		Event event = new Event();
		event.setRaffleId(1);
		event.setEventId(11L);

		EventModules eventModules = new EventModules();

		//Mock
		when(raffleRepository.findRaffleModuleByEventId(anyLong())).thenReturn(eventModules);
		doReturn(Collections.EMPTY_SET).when(raffleServiceImpl).getAllRaffleItemAndTotalTicket(event.getRaffleId());

		//execute
		raffleServiceImpl.getRafflePerformancePageData(event);

		Mockito.verify(raffleRepository, Mockito.times(1)).findRaffleModuleByEventId(event.getEventId());
	}

	@Test
	void test_getRaffleWinnerData(){

		//SetUp
		Event event = new Event();
		event.setRaffleId(1);
		event.setEventId(11L);
		User user = new User("<EMAIL>", 0L);
		user.setUserId(1L);
		SubmittedRaffleTicket submittedRaffleTicket = new SubmittedRaffleTicket();
		submittedRaffleTicket.setId(1L);
		submittedRaffleTicket.setUser(user);
		List<SubmittedRaffleTicket> raffleWinners = new ArrayList<>();
		raffleWinners.add(submittedRaffleTicket);
        Page<SubmittedRaffleTicket> pageImpl = new PageImpl<>(raffleWinners);
		//Mock
		when(submittedRaffleTicketService.getAllRaffleWinners(anyLong(), anyInt(), anyInt(), anyString())).thenReturn(pageImpl);
		when(phoneNumberService.getDisplayNumber(any())).thenReturn("1");
		when(itemDistributionService.isAllItemDistributed(any(), any())).thenReturn(true);


		//execute
        String searchString = StringUtils.EMPTY;
        int size = 10, page = 0;
        List<Boolean> distributed = null;
        DataTableResponse raffleWinnerData = raffleServiceImpl.getRaffleWinnerData(event, page, size, searchString, distributed);
		assertFalse(raffleWinnerData.getData().isEmpty());
		assertTrue(((RaffleWinnerDto)raffleWinnerData.getData().get(0)).isItemsDistributed());
		assertEquals(1, ((RaffleWinnerDto)raffleWinnerData.getData().get(0)).getTotalItemsWon());
	}

	@Test
	void test_getItemsDataByWinnerId_itemDistributionDetails_NotNull(){

		//SetUp
		Event event = new Event();
		event.setRaffleId(1);
		event.setEventId(11L);
		User user = new User("<EMAIL>", 0L);
		user.setUserId(1L);
		Item item = new Item();
		ItemDistributionDetails itemDistributionDetails = new ItemDistributionDetails();
		itemDistributionDetails.setDistributed(true);
		SubmittedRaffleTicket submittedRaffleTicket = new SubmittedRaffleTicket();
		submittedRaffleTicket.setId(1L);
		submittedRaffleTicket.setUser(user);
		submittedRaffleTicket.setItem(item);
		List<SubmittedRaffleTicket> submittedRaffleTickets = new ArrayList<>();
		submittedRaffleTickets.add(submittedRaffleTicket);

		//Mock
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
		when(submittedRaffleTicketService.findAllWinningItemsByWinnerAndRaffle(any(), anyLong())).thenReturn(submittedRaffleTickets);
		when(submittedRaffleTicketService.getRaffleTotalTicketsByUserAndRaffleIdAndItem(any(), anyLong(), any())).thenReturn(1L);
		when(itemDistributionService.getItemDistributionDetails(anyLong(), any())).thenReturn(itemDistributionDetails);


		//execute
		List<RaffleItemData> raffleItemDataList = raffleServiceImpl.getItemsDataByWinnerId(event, 1L);
		assertFalse(raffleItemDataList.isEmpty());
		assertTrue(raffleItemDataList.get(0).isDistributed());
		assertEquals(1, raffleItemDataList.get(0).getTickets());
	}

	@Test
	void test_getItemsDataByWinnerId_itemDistributionDetails_Null(){

		//SetUp
		Event event = new Event();
		event.setRaffleId(1);
		event.setEventId(11L);
		User user = new User("<EMAIL>", 0L);
		user.setUserId(1L);
		user.setFirstName("test");
		user.setLastName("test");
		Item item = new Item();
		SubmittedRaffleTicket submittedRaffleTicket = new SubmittedRaffleTicket();
		submittedRaffleTicket.setId(1L);
		submittedRaffleTicket.setUser(user);
		submittedRaffleTicket.setItem(item);
		List<SubmittedRaffleTicket> submittedRaffleTickets = new ArrayList<>();
		submittedRaffleTickets.add(submittedRaffleTicket);

		//Mock
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.of(user));
		when(submittedRaffleTicketService.findAllWinningItemsByWinnerAndRaffle(any(), anyLong())).thenReturn(submittedRaffleTickets);
		when(submittedRaffleTicketService.getRaffleTotalTicketsByUserAndRaffleIdAndItem(any(), anyLong(), any())).thenReturn(1L);
		when(itemDistributionService.getItemDistributionDetails(anyLong(), any())).thenReturn(null);


		//execute
		List<RaffleItemData> raffleItemDataList = raffleServiceImpl.getItemsDataByWinnerId(event, 1L);
		assertFalse(raffleItemDataList.isEmpty());
		assertFalse(raffleItemDataList.get(0).isDistributed());
		assertEquals(1, raffleItemDataList.get(0).getTickets());
	}

	@Test
	void test_getItemsDataByWinnerId_user_notPresent(){

		//SetUp
		Event event = new Event();
		event.setRaffleId(1);
		event.setEventId(11L);

		//Mock
		when(roUserService.getUserById(anyLong())).thenReturn(Optional.empty());

		//execute
		List<RaffleItemData> raffleItemDataList = raffleServiceImpl.getItemsDataByWinnerId(event, 1L);
		assertTrue(raffleItemDataList.isEmpty());
	}
}
