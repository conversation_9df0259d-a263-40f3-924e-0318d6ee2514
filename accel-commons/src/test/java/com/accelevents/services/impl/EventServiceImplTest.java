package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.auction.dto.*;
import com.accelevents.billing.chargebee.dto.EventChargebeePlanConfigDto;
import com.accelevents.billing.chargebee.dto.PlatformConfigDto;
import com.accelevents.billing.chargebee.repo.*;
import com.accelevents.billing.chargebee.service.*;
import com.accelevents.common.dto.ContactDto;
import com.accelevents.common.dto.*;
import com.accelevents.configuration.ImageConfiguration;
import com.accelevents.configuration.SquareConfiguration;
import com.accelevents.configuration.StripeConfiguration;
import com.accelevents.domain.Event;
import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.domain.exhibitors.ExhibitorSetting;
import com.accelevents.domain.virtual.VirtualEventSettings;
import com.accelevents.dto.*;
import com.accelevents.enums.PlanConfigNames;
import com.accelevents.enums.StaffRole;
import com.accelevents.event.DonationButtonDesignDto;
import com.accelevents.event.EventDesignSettingsDto;
import com.accelevents.event.EventModules;
import com.accelevents.events.templates.repositories.EventsTemplatesRepository;
import com.accelevents.exceptions.*;
import com.accelevents.exhibitors.services.ExhibitorSettingsRepoService;
import com.accelevents.helpers.CalculateFees;
import com.accelevents.helpers.StripeUtil;
import com.accelevents.helpers.TemplateId;
import com.accelevents.helpers.TextMessageUtils;
import com.accelevents.hubspot.service.HubspotContactService;
import com.accelevents.hubspot.service.HubspotEventService;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.EventSearchFilter;
import com.accelevents.notification.services.SendGridMailPrepareService;
import com.accelevents.notification.services.TwilioTextMessagePrepareService;
import com.accelevents.payment.HostEventOfflinePaymentConfigService;
import com.accelevents.registration.approval.repositories.RegistrationAttributeRepository;
import com.accelevents.repositories.*;
import com.accelevents.repository.ROWhiteLabelSettingRepository;
import com.accelevents.ro.common.ROBeeFreePagesService;
import com.accelevents.ro.event.service.ROConfirmationEmailService;
import com.accelevents.ro.event.service.ROEventDesignDetailService;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.event.service.ROVirtualEventService;
import com.accelevents.ro.eventTicket.ROTicketingTypeTicketService;
import com.accelevents.ro.payment.ROPayFlowConfigService;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.ro.user.service.ROPhoneNumberService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.*;
import com.accelevents.services.neptune.NeptuneInterestService;
import com.accelevents.services.repo.helper.*;
import com.accelevents.services.repo.helper.impl.EventRepoServiceImpl;
import com.accelevents.services.tray.io.TrayIntegrationService;
import com.accelevents.session_speakers.services.ConfirmationPagesService;
import com.accelevents.session_speakers.services.CustomTicketInvoiceDesignService;
import com.accelevents.session_speakers.services.SessionRepoService;
import com.accelevents.staff.dto.LiveItemsCodeAndAmountDto;
import com.accelevents.staff.dto.StaffLiveAuctionPurchaseDto;
import com.accelevents.ticketing.dto.TicketAnalytics;
import com.accelevents.ticketing.dto.TicketAnalyticsDto;
import com.accelevents.utils.CommonUtil;
import com.accelevents.utils.Constants;
import com.accelevents.utils.DateUtils;
import com.accelevents.utils.TimeZoneUtil;
import com.accelevents.virtualevents.dto.VirtualEventSettingsDTO;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.models.Location;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.twilio.rest.api.v2010.account.IncomingPhoneNumber;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Instant;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.accelevents.enums.UserRole.*;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.CONNECTED_TO_OTHER_PAYMENT_GATEWAY;
import static com.accelevents.exceptions.NotAcceptableException.PaymentCreationExceptionMsg.*;
import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static com.accelevents.utils.GeneralUtils.getEventPath;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
public class EventServiceImplTest {

	@Spy
	@InjectMocks
	private EventServiceImpl eventServiceImpl;

	@Mock
	private ImageConfiguration imageConfiguration1 = new ImageConfiguration();

	@Mock
	private SquareConfiguration squareConfiguration;

	@Mock
	private StripeConfiguration stripeConfiguration;

	@Mock
	private EventRepository eventRepository;
	@Mock
	private GetStreamService getStreamService;
	@Mock
	private DeletedEventsRepository deletedEventsRepository;
	@Mock
	private EventChecklistService eventChecklistService;
	@Mock
	private TextMessageService textMessageService;
	@Mock
	private RaffleServiceImpl raffleServiceImpl;
	@Mock
	private AuctionServiceImpl auctionServiceImpl;
	@Mock
	private CauseAuctionServiceImpl causeAuctionServiceImpl;
	@Mock
	private TicketingHelperServiceImpl ticketingHelperService;
    @Mock
    private ROWhiteLabelSettingRepository roWhiteLabelSettingRepository;
    @Mock
    private ConfirmationPagesService confirmationPagesService;
    @Mock
    private ConfirmationEmailService confirmationEmailService;
    @Mock
    private ROConfirmationEmailService roConfirmationEmailService;
	@Mock
	private DonationSettingsService donationSettingsService;
	@Mock
	private EventInteractionRepository eventInteractionRepository;
	@Mock
	private StaffService staffService;
    @Mock
    private ROStaffService roStaffService;
	@Mock
	private JoinEventWithPDProductService joinEventWithPDProductService;
	@Mock
	private EventDesignDetailService eventDesignDetailService;
    @Mock
    private ROEventDesignDetailService roEventDesignDetailService;
	@Mock
	private AuctionService auctionService;
	@Mock
	private ItemCategoryService itemCategoryService;
	@Mock
	private RaffleService raffleService;
	@Mock
	private RaffleTicketService raffleTicketService;
	@Mock
	private TicketingService ticketingService;
	@Mock
	private CauseAuctionService causeAuctionService;
	@Mock
	private ItemService itemService;
	@Mock
	private ItemImgLocationsService itemImgLocationsService;
	@Mock
	private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;
    @Mock
    private ROPhoneNumberService roPhoneNumberService;
    @Mock
    private ROPayFlowConfigService roPayFlowConfigService;
	@Mock
	private SendGridMailPrepareService sendGridMailPrepareService;
	@Mock
	private PhoneNumberService phoneNumberService;
	@Mock
	private StripePaymentService stripePaymentService;
	@Mock
	private SquarePaymentService squarePaymentService;
	@Mock
	private TextMessageUtils textMessageUtils;
	@Mock
	private StripeTransactionService stripeTransactionService;
	@Mock
	private TicketingTypeService ticketingTypeService;
	@Mock
	private TicketingTypeTicketService ticketingTypeTicketService;
    @Mock
    private ROTicketingTypeTicketService roTicketingTypeTicketService;
	@Mock
	private PaymentService paymentService;
	@Mock
	private AllPaymentService allPaymentService;
	@Mock
	private TicketingStatisticsService ticketingStatisticsService;
	@Mock
	private AuctionBidService auctionBidService;
	@Mock
	private WinnerService winnerService;
	@Mock
	private JoinPaymentItemService joinPaymentItemService;
	@Mock
	private PaymentHandlerService paymentHandlerService;
	@Mock
	private TwilioTextMessagePrepareService twilioTextMessagePrepareService;
	@Mock
	private PledgeService pledgeService;
	@Mock
	private UserService userService;
    @Mock
    private ROUserService roUserService;
	@Mock
	private PurchasedRaffleTicketService purchasedRaffleTicketService;
	@Mock
	private DonationService donationService;
	@Mock
	private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;
	@Mock
	private TicketingOrderRepoService ticketingOrderRepoService;
	@Mock
	private EventTicketsRepository eventTicketsRepository;
	@Mock
	private RaffleCustomSmsService raffleCustomSmsService;
	@Mock
	private AuctionCustomSmsService auctionCustomSmsService;
	@Mock
	private CauseAuctionCustomSmsService causeAuctionCustomSmsService;
	@Mock
	private DonationCustomSmsService donationCustomSmsService;
	@Mock
	private BidderNumberService bidderNumberService;
	@Mock
	private OrganizerService organizerService;
	@Mock
	private AutoAssignedAttendeeNumbersService autoAssignedAttendeeNumbersService;
	@Mock
	private EventQuestionRepository eventQuestionRepository;
	@Mock
	private WaitListSettingService waitListSettingService;
	@Mock
	private WaitListService waitListService;
	@Mock
	private IntercomDetailsService intercomDetailsService;
	@Mock
	private VirtualEventService virtualEventService;
	@Mock
	private ClearAPIGatewayCache clearAPIGatewayCache;
	@Mock
	private ExhibitorRepoService exhibitorRepoService;
	@Mock
	private HubspotContactService hubspotContactService;
	@Mock
	private HubspotEventService hubspotEventService;
	@Mock
    private EventRepoService eventRepoService;
	@Mock
    private EventRepoServiceImpl eventRepoServiceImpl;
	@Mock
    private ChargebeeService chargebeeService;
    @Mock
    private ChargebeePlanService chargebeePlanService;
    @Mock
    private EventPlanConfigService eventPlanConfigService;
    @Mock
    private JoinUsersWithOrganizersRepository joinUsersWithOrganizersRepository;
    @Mock
    private SessionRepoService sessionRepoService;
    @Mock
    private NeptuneInterestService neptuneInterestService;
    @Mock
    private EventDesignDetailRepoService eventDesignDetailRepoServices;
    @Mock
    private ChargesPurchasedRepoService chargesPurchasedRepoService;
    @Mock
    private ChargebeeEventCreditsRepoService chargebeeEventCreditsRepoService;
    @Mock
    private EventPlanConfigRepoService eventPlanConfigRepoService;
    @Mock
    private ChargeConfigRepoService chargeConfigRepoService;
    @Mock
    private ChargebeeAttendeeUploadService chargebeeAttendeeUploadService;
    @Mock
	private TicketingRepository ticketingRepository;
	@Mock
	private RecurringEventsScheduleBRService recurringEventsScheduleService;
	@Mock
	private EventTicketsRepoService eventTicketsRepoService;
	@Mock
	private TicketingOrderManagerService ticketingOrderManagerService;
	@Mock
	private TicketingCouponService ticketingCouponService;
	@Mock
	private TicketingAccessCodeService ticketingAccessCodeService;
	@Mock
	private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;
	@Mock
	private RecurringEventsRepository recurringEventsRepository;
	@Mock
    private EmbedWidgetSettingService embedWidgetSettingService;
	@Mock
    private ChargebeeBillingService chargebeeBillingService;
    @Mock
    private BeeFreeRepository beeFreeRepository;
    @Mock
    private VirtualEventPortalService virtualEventPortalService;
    @Mock
    private ApiKeyService apiKeyService;
    @Mock
    private OrganizerRepoService organizerRepoService;
    @Mock
    private WhiteLabelRepoService whiteLabelRepoService;
    @Mock
    private SSOUserService ssoUserService;
    @Mock
    private ExhibitorSettingsRepoService exhibitorSettingsRepoService;
    @Mock
    private EventChallengeRepoService eventChallengeRepoService;
    @Mock
    private PayFlowConfigServiceImpl payFlowConfigService;
    @Mock
    private CheckInAuditLogRepoService checkInAuditLogRepoService;
    @Mock
    private RegistrationAttributeRepository registrationAttributeRepository;
    @Mock
    private ItemOutbidGraphQLHandler itemOutbidGraphQLHandler;
    @Mock
    private VirtualEventSettingsRepoService virtualEventSettingsRepoService;
    @Mock
    private  ChargeBeeHelperService chargeBeeHelperService;
    @Mock
    private HostEventOfflinePaymentConfigService hostEventOfflinePaymentConfigService;
    @Mock
    private EventsTemplatesRepository eventTemplateRepository;
    @Mock
    private BeeFreePageRepoService beeFreePageRepoService;
    @Mock
    private ROVirtualEventService roVirtualEventService;

    @Mock
    private WhiteLabelSettingsService whiteLabelSettingsService;

    @Mock
    private StripeCustomerService stripeCustomerService;
    @Mock
    private ContactModuleSettingsService contactModuleSettingsService;

    @Value("${uiBaseurl}")
	private String uiBaseurl;

    @Mock
    private IntegrationService integrationService;
    @Mock
    private TrayIntegrationService trayIntegrationService;

    @Mock
    private MeetingOptionsRepoService meetingOptionsRepoService;

    @Mock
    private  KioskRegistrationSettingService kioskRegistrationSettingService;

    @Mock
    private EventLevelSettingService eventLevelSettingService;
    @Mock
    private EventBadgeSettingRepository eventBadgeSettingRepository;
    @Mock
    private EventCECriteriaRepoService eventCECriteriaRepoService;
    @Mock
    private ConfirmationEmailRepository confirmationEmailRepository;
    @Mock
    private ROEventService roEventService;
    @Mock
    private ROBeeFreePagesService roBeeFreePagesService;
    @Mock
    private WhiteLabelSettingRepository whiteLabelSettingRepository;
    @Mock
    private CustomTicketInvoiceDesignService customTicketInvoiceDesignService;
	@Mock
	private EntryExitSettingService entryExitSettingService;

	private Event event;
	private User user, staffUser;
	private Staff staff;
	private JoinEventWithDealProduct joinEventWithDealProduct;
	private WhiteLabel whiteLabel;
	private Stripe stripe;
	private Ticketing ticketing;
	private TemplateId templateId;
	private EmailMessage emailMessage;
	private ContactDto contactDto;
	private IncomingPhoneNumber incomingPhoneNumber;
	private TwilioNumberHistory twilioNumberHistory;
	private AccelEventsPhoneNumber accelEventsPhoneNumber;
	private Item item;
	private EventChecklist eventChecklist;
	private Account account;
	private Location location;
	private JSONObject response;
	private StripeTransaction stripeTransaction;
	private EventDesignDetail eventDesignDetail;
	private DonationSettings donationSetting;
	private Auction auction;
	private Raffle raffle;
	private CauseAuction causeAuction;
	private Payment payment;
	private StripeCreditCardDto stripeCreditCardDto;
	private AuctionPurchaseDto auctionPurchaseDto;
	private Winner winner;
	private AuctionBid auctionBid;
	private FundANeedPurchaseDto fundANeedPurchaseDto;
	private BidCheckOutDto bidCheckOutDto;
	private PledgeCheckoutDto pledgeCheckoutDto;
	private Pledge pledge;
	private RaffleCheckoutDto raffleCheckoutDto;
	private RaffleTicket raffleTicket;
	private PurchasedRaffleTicket purchasedRaffleTicket;
	private HostGeneralSettingsGet hostGeneralSettingsGet;
	private Subscription subscription;
	private HostEventDetailsDto hostEventDetailsDto;
	private Organizer organizer;
	private HostAuctionDetail hostAuctionDetail;
	private TicketingType ticketingType;
	private EventChecklistItem eventChecklistItem;
	private HostCreditCardSettings hostCreditCardSettings;
	private HostBillingSettings hostBillingSettings;
	private Plan plan;
	private EventDesignSettingsDto eventDesignSettingsDto;
	private TicketingOrder ticketingOrder;
	private TicketAnalytics ticketAnalytics;
	private StripeSubscriptionDto stripeSubscriptionDto;
	private BidderNumber bidderNumber;
	private StaffDetailDto staffDetailDto;
	private DonationDisplaySetting donationDisplaySetting;
	private DeletedEventsDto deletedEventsDto;
	private VirtualEventSettingsDTO virtualEventSetting;
	private ImageConfiguration imageConfiguration;
	private PlanConfig chargebeePlan;
	private EventChargebeePlanConfigDto eventChargebeePlanConfigDto;
    private RecurringEvents recurringEvents;
    private RecurringEventSchedule recurringEventSchedule;
	private EventTickets eventTickets;
	private TicketingOrderManager ticketingOrderManager;
	private TicketingCoupon ticketingCoupon;
	private WhiteLabelBillingSettingsDto whiteLabelBillingSettingsDto;
	private VirtualEventSettings virtualEventSettings;
	private ExhibitorSetting exhibitorSetting;
	private PlatformConfigDto platformConfigDto;
    private EventsTemplates eventsTemplates;

	private Long phoneNumber = 9898989898L;
	private Long id = 1L;
	private String url = "testEvent1";
	private String email = "<EMAIL>";

	@BeforeEach
	public void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);
		event = EventDataUtil.getEvent();
		user = EventDataUtil.getUser();
		ticketing = EventDataUtil.getTicketing(event);

		staff = new Staff();
		staff.setUser(user);
		staff.setEvent(event);

        whiteLabel = new WhiteLabel();
		stripe = new Stripe();
		stripe.setStripePublishableKey("stripePublishableKey");
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.toString());
		stripe.isProcessingFeesToPurchaser();

		imageConfiguration = new ImageConfiguration();
		imageConfiguration.setBlackLogo("black logo");
        chargebeePlan = new PlanConfig();
        chargebeePlan.setChargebeePlanId(PlanConfigNames.FREE_PLAN.getName());

        recurringEvents = new RecurringEvents();
        recurringEvents.setId(id);

        eventTickets = new EventTickets();

        ticketingOrderManager = new TicketingOrderManager();

        ticketingType = new TicketingType();

        ticketingOrder = new TicketingOrder();

        ticketingCoupon = new TicketingCoupon();

        recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(id);

        eventChargebeePlanConfigDto =new EventChargebeePlanConfigDto();
        eventChargebeePlanConfigDto.setPlatformPlanCredits(0);
        platformConfigDto = new PlatformConfigDto();

        platformConfigDto.setGamification("");
        eventChargebeePlanConfigDto.setPlatformConfigDto(platformConfigDto);



        eventDesignDetail = new EventDesignDetail();
        eventDesignDetail.setEvent(event);
        squareConfiguration.setAppSecretKey("sandbox-sq0atb-b6mRO-_m87eg_erMk2AN_w");
        squareConfiguration.setClientID("sandbox-sq0idp-A57Oi3xFvkk3-3fNftKu6w");
        squareConfiguration.setAuthorize_URI("https://connect.squareup.com/oauth2/authorize");
        squareConfiguration.setToken_URI("https://connect.squareup.com/oauth2/token");
        squareConfiguration.setSquareSandBoMode(false);
	}

	@Test
	void test_getAllEvents_success() {

		//setup
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(eventRepository.findAll()).thenReturn(eventList);

		//Execution
		Iterable<Event> eventData = eventServiceImpl.getAllEvents();

		//Assertion
		assertEquals(eventData.iterator().next().getTimezoneId(), event.getTimezoneId());
		assertEquals(eventData.iterator().next().getEquivalentTimeZone(),event.getEquivalentTimeZone());
		assertEquals(eventData.iterator().next().getCurrency(), event.getCurrency());
		assertEquals(eventData.iterator().next().getEventId(), event.getEventId());

		verify(eventRepository, times(1)).findAll();
	}

	@Test
	void test_getAllAttachedPhoneNumberEvents_success() {

		//setup
		event.setPhoneNumber(phoneNumber);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(eventRepository.getAllAttachedPhonenumberEvents()).thenReturn(eventList);

		//Execution
		List<Event> eventData = eventServiceImpl.getAllAttachedPhoneNumberEvents();

		//Assertion
		assertEquals(eventData.iterator().next().getPhoneNumber(), event.getPhoneNumber());

		verify(eventRepository, times(1)).getAllAttachedPhonenumberEvents();
	}


	@Test
	void test_save_success() {

		//setup
		getJoinEventWithDealProductData();

		//mock


		//Execution
		eventRepoService.save(event);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventId(), event.getEventId());
	}

	@Test
	void test_save_joinEventWithPDProductOptional_empty() {

		//mock


		//Execution
		eventRepoService.save(event);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventId(), event.getEventId());
	}

	@Test
	void test_getEventByFromTo(){

		//setup
		long from = 1;
		long to = 5;
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(eventRepository.getEventByFromTo(from,to)).thenReturn(eventList);

		//execute
		List<Event> expected = eventServiceImpl.getEventByFromTo(from,to);

		//Assertion
		assertEquals(expected.size(), eventList.size());
	}

	@Test
	void test_getEventUrl_throwException_URL_NOT_SUPPORT_SPECIAL_CHART() {

		//setup
		String url = "url-123&";
		int count = 1;
		int urlLength = url.length();
		boolean updateUrl = true;

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.getEventUrl(url, count, urlLength, updateUrl, null)
        );

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.URL_NOT_SUPPORT_SPECIAL_CHART.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_getEventUrl_withEventNull() {

		//setup
		String url = "test-event";
		int count = 1;
		int urlLength = url.length();
		boolean updateUrl = true;

		//Execution
		String eventUrl = eventServiceImpl.getEventUrl(url, count, urlLength, updateUrl, null);

		//Assertion
		assertEquals(eventUrl, url);
	}

	@Test
	void test_getEventUrl_throwException_EVENT_URL_ALREADY_EXIST() {

		//setup
		int count = 1;
		int urlLength = url.length();
		boolean updateUrl = true;

		//mock
		when(roEventService.getEventByURL(url)).thenReturn(event);

		//Execution
		Exception exception = assertThrows(ConflictException.class,
                () -> eventServiceImpl.getEventUrl(url, count, urlLength, updateUrl, null)
        );

		//Assertion
		verify(roEventService, times(1)).getEventByURL(anyString());
        assertEquals(ConflictException.UserExceptionConflictMsg.EVENT_URL_ALREADY_EXIST.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_getEventUrl_WithEvent() {

		//setup
		int count = 1;
		int urlLength = url.length();
		boolean updateUrl = false;

		//mock
        when(roEventService.getEventByURL(url)).thenReturn(event);

		//Execution
		String eventUrl = eventServiceImpl.getEventUrl(url, count, urlLength, updateUrl, null);

		//Assertion
		assertEquals(eventUrl, url+count);
	}

	@Test
	void test_createEventAndCloneStripeOfWL_success() {

		//setup
		whiteLabel.setRaffleEnabled(true);
		whiteLabel.setCauseAuctionEnabled(true);
		whiteLabel.setTicketingEnabled(true);
		whiteLabel.setDonationEnabled(true);
		whiteLabel.setShowEnableModulePopup(true);
		whiteLabel.setSilentAuctionEnabled(true);
		whiteLabel.setBiillingPageEnabled(true);
		whiteLabel.setAuctionsActivated(true);
		whiteLabel.setManualPayout(true);
		whiteLabel.setRafflesActivated(true);
		whiteLabel.setCauseAuctionActivated(true);
		whiteLabel.setCustomDisclaimer("customer Disclaimer");
		whiteLabel.setDonationAmountA(10);
		whiteLabel.setDonationAmountB(10);
		whiteLabel.setDonationAmountC(10);
		whiteLabel.setDonationAmountD(10);
		whiteLabel.setDefaultItemImage("image");
		whiteLabel.setWhiteLabelUrl("test123");
		boolean silentAuctionEnabled = true;
		boolean causeAuctionEnabled = true;
		boolean raffleEnabled = true;
		String userEmail = "<EMAIL>";
		List<String> userRoles = new ArrayList<>();
		userRoles.add(ROLE_SUPERADMIN.name());

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, 30);

		getJoinEventWithDealProductData();

		//mock

		doNothing().when(eventChecklistService).save(any());
		doNothing().when(eventDesignDetailService).save(any());
		doNothing().when(auctionService).save(any());
		doNothing().when(itemCategoryService).savewithsequence(any());
		doNothing().when(raffleService).save(any());
		doNothing().when(raffleTicketService).addDefaultTicketPrices(any());
		doReturn(ticketing).when(ticketingService).save(any());
		doNothing().when(causeAuctionService).save(any());
		doNothing().when(donationSettingsService).save(any());

		when(ticketingHelperService.createNewTicketing(any())).thenReturn(ticketing);
		doNothing().when(stripeService).save(any());



        when(roUserService.getUserByEmail(any())).thenReturn(Optional.of(user));

		//Execution
		Event eventData = eventServiceImpl.createEventAndCloneStripeOfWL(silentAuctionEnabled, causeAuctionEnabled, raffleEnabled, userEmail, whiteLabel, userRoles, stripe, null);

		//Assertion
		assertEquals(eventData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.getCurrency(), Currency.USD);
		assertTrue(eventData.isCreditCardEnabled());
		assertEquals(eventData.getGoalStartingAmount(), 0);

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
		Event eventsData = eventArgumentCaptor.getValue();
		assertEquals(eventsData.isSilentAuctionEnabled(), whiteLabel.isSilentAuctionEnabled());
		assertEquals(eventsData.isCauseAuctionEnabled(),whiteLabel.isCauseAuctionEnabled());
		assertEquals(eventsData.isRaffleEnabled(), whiteLabel.isRaffleEnabled());
		assertFalse(eventsData.isTicketingEnabled());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
		EventChecklist eventCheckListData = eventChecklistArgumentCaptor.getValue();
		assertEquals(eventCheckListData.getEventId(), 0);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailData.getEvent().getEventId(), 0);
		assertEquals(eventDesignDetailData.getDesc(), WL_EVENT_DEFAULT_DESC);
		assertEquals(eventDesignDetailData.getSponsorSection(), SPONSORS_DEFAULT_SECT);
		assertEquals(eventDesignDetailData.getTicketingTabTitle(),BUY_TICKET_BUTTON_TEXT);
		assertEquals(eventDesignDetailData.getLogoImage(), imageConfiguration1.getBlackLogo());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), imageConfiguration1.getDefaultHeader());
		assertEquals(eventDesignDetailData.getReplyToEmail(), userEmail);
		assertEquals(eventDesignDetailData.getThemeId().longValue(), 3L);

		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
		Auction auctionData = auctionArgumentCaptor.getValue();
		assertEquals(auctionData.getEventId().longValue(), 0);
		assertEquals(auctionData.getEndDate().toString(), calendar.getTime().toString());
		assertEquals(auctionData.isActivated(), whiteLabel.isAuctionsActivated());
		assertEquals(auctionData.getEventPayoutStatus(), EnumEventPayoutStatus.INITIALIZED);

		ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleService, times(1)).save(raffleArgumentCaptor.capture());
		Raffle raffleData = raffleArgumentCaptor.getValue();
		assertEquals(raffleData.getEventId(), 0);
		assertEquals(raffleData.getEndDate().toString(), calendar.getTime().toString());
		assertFalse(raffleData.isSweepstakes());
		assertEquals(raffleData.getAvailableTickets(), 0);
		assertFalse(raffleData.isAutoSubmitOnPurchase());
		assertEquals(raffleData.isActivated(), whiteLabel.isAuctionsActivated());
		assertEquals(raffleData.getEventPayoutStatus(), EnumEventPayoutStatus.INITIALIZED);

		ArgumentCaptor<Raffle> raffleArgumentCaptor1 = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleTicketService, times(1)).addDefaultTicketPrices(raffleArgumentCaptor1.capture());
		Raffle raffleData1 = raffleArgumentCaptor.getValue();
		assertEquals(raffleData1.getId(), 0);

		ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
		verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
		Ticketing ticketingData = ticketingArgumentCaptor.getValue();
		assertTrue(ticketingData.getActivated());
		assertEquals(ticketingData.getEventPayoutStatus(), EnumEventPayoutStatus.INITIALIZED);

		ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
		verify(causeAuctionService, times(1)).save(causeAuctionArgumentCaptor.capture());
		CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
		assertEquals(causeAuctionData.getEventId(), 0);
		assertEquals(causeAuctionData.getEndDate().toString(), calendar.getTime().toString());
		assertEquals(causeAuctionData.isActivated(), whiteLabel.isCauseAuctionActivated());
		assertEquals(causeAuctionData.getEventPayoutStatus(), EnumEventPayoutStatus.INITIALIZED);

		ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingsArgumentCaptor.capture());
		DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
		assertEquals(donationSettingsData.getDonationAmountA(), whiteLabel.getDonationAmountA());
		assertEquals(donationSettingsData.getDonationAmountB(), whiteLabel.getDonationAmountB());
		assertEquals(donationSettingsData.getDonationAmountC(), whiteLabel.getDonationAmountC());
		assertEquals(donationSettingsData.getDonationAmountD(), whiteLabel.getDonationAmountD());
		assertEquals(donationSettingsData.getFee(), 1);
		assertEquals(donationSettingsData.getEventId(),0);
		assertFalse(donationSettingsData.isAbsorbFee());
		assertFalse(donationSettingsData.isTextToGiveActivated());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());
		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getId().longValue(), 0L);
		assertEquals(stripeData.getEvent().getEventId(), 0);
		assertTrue(stripeData.isDefaultAccount());
	}

	@Test
	void test_createEvent_withWhiteLabelNull() {

		//setup
		boolean silentAuctionEnabled = true;
		boolean causeAuctionEnabled = true;
		boolean raffleEnabled = true;
		String userEmail = "<EMAIL>";
		List<String> userRoles = new ArrayList<>();
		userRoles.add(ROLE_SUPERADMIN.name());
		userRoles.add(ROLE_WHITELABELADMIN.name());
		userRoles.add(ROLE_ADMIN.name());

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, 30);

		getJoinEventWithDealProductData();

		//mock
		doNothing().when(eventChecklistService).save(any());
		doNothing().when(eventDesignDetailService).save(any());
		doNothing().when(auctionService).save(any());
		doNothing().when(itemCategoryService).savewithsequence(any());
		doNothing().when(raffleService).save(any());
		doNothing().when(raffleTicketService).addDefaultTicketPrices(any());

		doNothing().when(causeAuctionService).save(any());
		doNothing().when(donationSettingsService).save(any());

		when(ticketingHelperService.createNewTicketing(any())).thenReturn(ticketing);




        when(roUserService.getUserByEmail(any())).thenReturn(Optional.of(user));

		//Execution
		Event eventData = eventServiceImpl.createEvent(silentAuctionEnabled, causeAuctionEnabled, raffleEnabled, userEmail, null, userRoles, null, null);

		//Assertion
		assertEquals(eventData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.getCurrency(), Currency.USD);
		assertFalse(eventData.isCreditCardEnabled());
		assertEquals(eventData.getGoalStartingAmount(), 0);

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
		Event eventsData = eventArgumentCaptor.getValue();
		assertEquals(eventsData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventsData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventsData.isRaffleEnabled(), raffleEnabled);
		assertFalse(eventsData.isTicketingEnabled());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
		EventChecklist eventCheckListData = eventChecklistArgumentCaptor.getValue();
		assertEquals(eventCheckListData.getEventId(), 0);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailData.getEvent().getEventId(), 0);
		assertEquals(eventDesignDetailData.getDesc(), EVENT_DEFAULT_DESC);
		assertEquals(eventDesignDetailData.getSponsorSection(), SPONSORS_DEFAULT_SECT);
		assertEquals(eventDesignDetailData.getTicketingTabTitle(),BUY_TICKET_BUTTON_TEXT);
		assertEquals(eventDesignDetailData.getLogoImage(), imageConfiguration1.getBlackLogo());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), imageConfiguration1.getDefaultHeader());
		assertEquals(eventDesignDetailData.getReplyToEmail(), userEmail);
		assertEquals(eventDesignDetailData.getThemeId().longValue(), 3L);
		assertEquals(eventDesignDetailData.getHeaderColor(), "#FFFFFF");
		assertEquals(eventDesignDetailData.getHeaderFontColor(), "#6D6F7D");

		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
		Auction auctionData = auctionArgumentCaptor.getValue();
		assertEquals(auctionData.getEventId().longValue(), 0);

		ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleService, times(1)).save(raffleArgumentCaptor.capture());
		Raffle raffleData = raffleArgumentCaptor.getValue();
		assertEquals(raffleData.getEventId(), 0);
		assertFalse(raffleData.isSweepstakes());
		assertEquals(raffleData.getAvailableTickets(), 0);
		assertFalse(raffleData.isAutoSubmitOnPurchase());

		ArgumentCaptor<Raffle> raffleArgumentCaptor1 = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleTicketService, times(1)).addDefaultTicketPrices(raffleArgumentCaptor1.capture());
		Raffle raffleData1 = raffleArgumentCaptor.getValue();
		assertEquals(raffleData1.getId(), 0);

		ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
		verify(causeAuctionService, times(1)).save(causeAuctionArgumentCaptor.capture());
		CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
		assertEquals(causeAuctionData.getEventId(), 0);

		ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingsArgumentCaptor.capture());
		DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
		assertEquals(donationSettingsData.getDonationAmountA(), 5);
		assertEquals(donationSettingsData.getDonationAmountB(), 15);
		assertEquals(donationSettingsData.getDonationAmountC(), 30);
		assertEquals(donationSettingsData.getDonationAmountD(), 50);
		assertEquals(donationSettingsData.getFee(), 1);
		assertEquals(donationSettingsData.getEventId(),0);
		assertFalse(donationSettingsData.isAbsorbFee());
		assertFalse(donationSettingsData.isTextToGiveActivated());

	}

	@Test
	void test_createEvent_withWhiteLabel() {

		//setup
		boolean silentAuctionEnabled = true;
		boolean causeAuctionEnabled = true;
		boolean raffleEnabled = true;
		long userId = 1L;
		String userEmail = "<EMAIL>";
		List<String> userRoles = new ArrayList<>();
		userRoles.add(ROLE_SUPERADMIN.name());
		userRoles.add(ROLE_WHITELABELADMIN.name());
		userRoles.add(ROLE_ADMIN.name());
		whiteLabel = new WhiteLabel();
		whiteLabel.setManualPayout(false);
		whiteLabel.setWhiteLabelUrl("whiteLabelEventUrl");
		whiteLabel.setSilentAuctionEnabled(true);
		whiteLabel.setCauseAuctionEnabled(true);
		whiteLabel.setRaffleEnabled(true);
		whiteLabel.setTicketingEnabled(true);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, 30);

		getJoinEventWithDealProductData();

		//mock
		doNothing().when(eventChecklistService).save(any());
		doNothing().when(eventDesignDetailService).save(any());
		doNothing().when(auctionService).save(any());
		doNothing().when(itemCategoryService).savewithsequence(any());
		doNothing().when(raffleService).save(any());
		doNothing().when(raffleTicketService).addDefaultTicketPrices(any());
		doReturn(ticketing).when(ticketingService).save(any());
		doNothing().when(causeAuctionService).save(any());
		doNothing().when(donationSettingsService).save(any());

		when(ticketingHelperService.createNewTicketing(any())).thenReturn(ticketing);



        when(roUserService.getUserById(userId)).thenReturn(Optional.of(user));

		//Execution
		Event eventData = eventServiceImpl.createEvent(silentAuctionEnabled, causeAuctionEnabled, raffleEnabled, userEmail, whiteLabel, userRoles, userId, null);

		//Assertion
		assertEquals(eventData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.getCurrency(), Currency.USD);
		assertFalse(eventData.isCreditCardEnabled());
		assertEquals(eventData.getGoalStartingAmount(), 0);

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
		Event eventsData = eventArgumentCaptor.getValue();
		assertEquals(eventsData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventsData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventsData.isRaffleEnabled(), raffleEnabled);
		assertFalse(eventsData.isTicketingEnabled());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
		EventChecklist eventCheckListData = eventChecklistArgumentCaptor.getValue();
		assertEquals(eventCheckListData.getEventId(), 0);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailData.getEvent().getEventId(), 0);
		assertEquals(eventDesignDetailData.getDesc(), WL_EVENT_DEFAULT_DESC);
		assertEquals(eventDesignDetailData.getSponsorSection(), SPONSORS_DEFAULT_SECT);
		assertEquals(eventDesignDetailData.getTicketingTabTitle(),BUY_TICKET_BUTTON_TEXT);
		assertEquals(eventDesignDetailData.getLogoImage(), imageConfiguration1.getBlackLogo());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), imageConfiguration1.getDefaultHeader());
		assertEquals(eventDesignDetailData.getReplyToEmail(), userEmail);
		assertEquals(eventDesignDetailData.getThemeId().longValue(), 3L);
		assertEquals(eventDesignDetailData.getHeaderColor(), "#1e1e4e");

		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
		Auction auctionData = auctionArgumentCaptor.getValue();
		assertEquals(auctionData.getEventId().longValue(), 0);

		ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleService, times(1)).save(raffleArgumentCaptor.capture());
		Raffle raffleData = raffleArgumentCaptor.getValue();
		assertEquals(raffleData.getEventId(), 0);
		assertFalse(raffleData.isSweepstakes());
		assertEquals(raffleData.getAvailableTickets(), 0);
		assertFalse(raffleData.isAutoSubmitOnPurchase());

		ArgumentCaptor<Raffle> raffleArgumentCaptor1 = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleTicketService, times(1)).addDefaultTicketPrices(raffleArgumentCaptor1.capture());
		Raffle raffleData1 = raffleArgumentCaptor.getValue();
		assertEquals(raffleData1.getId(), 0);

		ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
		verify(causeAuctionService, times(1)).save(causeAuctionArgumentCaptor.capture());
		CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
		assertEquals(causeAuctionData.getEventId(), 0);

		ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingsArgumentCaptor.capture());
		DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
		assertEquals(donationSettingsData.getDonationAmountA(), 0);
		assertEquals(donationSettingsData.getDonationAmountB(), 0);
		assertEquals(donationSettingsData.getDonationAmountC(), 0);
		assertEquals(donationSettingsData.getDonationAmountD(), 0);
		assertEquals(donationSettingsData.getFee(), 1);
		assertEquals(donationSettingsData.getEventId(),0);
		assertFalse(donationSettingsData.isAbsorbFee());
		assertFalse(donationSettingsData.isTextToGiveActivated());

	}

	@Test
	void test_createEvent_withWhiteLabelAndcopyWhiteLabelModuleActivationsFalse() {

		//setup
		boolean silentAuctionEnabled = true;
		boolean causeAuctionEnabled = true;
		boolean raffleEnabled = true;
		long userId = 1L;
		String userEmail = "<EMAIL>";
		List<String> userRoles = new ArrayList<>();
		whiteLabel = new WhiteLabel();
		whiteLabel.setManualPayout(false);
		whiteLabel.setWhiteLabelUrl("whiteLabelEventUrl");
		whiteLabel.setSilentAuctionEnabled(true);
		whiteLabel.setCauseAuctionEnabled(true);
		whiteLabel.setRaffleEnabled(true);
		whiteLabel.setTicketingEnabled(true);

		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, 30);

		getJoinEventWithDealProductData();

		//mock
		doNothing().when(eventChecklistService).save(any());
		doNothing().when(eventDesignDetailService).save(any());
		doNothing().when(auctionService).save(any());
		doNothing().when(itemCategoryService).savewithsequence(any());
		doNothing().when(raffleService).save(any());
		doNothing().when(raffleTicketService).addDefaultTicketPrices(any());
		doReturn(ticketing).when(ticketingService).save(any());
		doNothing().when(causeAuctionService).save(any());
		doNothing().when(donationSettingsService).save(any());

		when(ticketingHelperService.createNewTicketing(any())).thenReturn(ticketing);


        when(roUserService.getUserById(userId)).thenReturn(Optional.of(user));

		//Execution
		Event eventData = eventServiceImpl.createEvent(silentAuctionEnabled, causeAuctionEnabled, raffleEnabled, userEmail, whiteLabel, userRoles, userId, null);

		//Assertion
		assertEquals(eventData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.getCurrency(), Currency.USD);
		assertFalse(eventData.isCreditCardEnabled());
		assertEquals(eventData.getGoalStartingAmount(), 0);

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
		Event eventsData = eventArgumentCaptor.getValue();
		assertEquals(eventsData.isSilentAuctionEnabled(), silentAuctionEnabled);
		assertEquals(eventsData.isCauseAuctionEnabled(), causeAuctionEnabled);
		assertEquals(eventsData.isRaffleEnabled(), raffleEnabled);
		assertFalse(eventsData.isTicketingEnabled());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
		EventChecklist eventCheckListData = eventChecklistArgumentCaptor.getValue();
		assertEquals(eventCheckListData.getEventId(), 0);

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailData.getEvent().getEventId(), 0);
		assertEquals(eventDesignDetailData.getDesc(), WL_EVENT_DEFAULT_DESC);
		assertEquals(eventDesignDetailData.getSponsorSection(), SPONSORS_DEFAULT_SECT);
		assertEquals(eventDesignDetailData.getTicketingTabTitle(),BUY_TICKET_BUTTON_TEXT);
		assertEquals(eventDesignDetailData.getLogoImage(), imageConfiguration1.getBlackLogo());
		assertEquals(eventDesignDetailData.getHeaderLogoImage(), imageConfiguration1.getDefaultHeader());
		assertEquals(eventDesignDetailData.getReplyToEmail(), userEmail);
		assertEquals(eventDesignDetailData.getThemeId().longValue(), 3L);
		assertEquals(eventDesignDetailData.getHeaderColor(), "#1e1e4e");

		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
		Auction auctionData = auctionArgumentCaptor.getValue();
		assertEquals(auctionData.getEventId().longValue(), 0);

		ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleService, times(1)).save(raffleArgumentCaptor.capture());
		Raffle raffleData = raffleArgumentCaptor.getValue();
		assertEquals(raffleData.getEventId(), 0);
		assertFalse(raffleData.isSweepstakes());
		assertEquals(raffleData.getAvailableTickets(), 0);
		assertFalse(raffleData.isAutoSubmitOnPurchase());

		ArgumentCaptor<Raffle> raffleArgumentCaptor1 = ArgumentCaptor.forClass(Raffle.class);
		verify(raffleTicketService, times(1)).addDefaultTicketPrices(raffleArgumentCaptor1.capture());
		Raffle raffleData1 = raffleArgumentCaptor.getValue();
		assertEquals(raffleData1.getId(), 0);

		ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
		verify(causeAuctionService, times(1)).save(causeAuctionArgumentCaptor.capture());
		CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
		assertEquals(causeAuctionData.getEventId(), 0);

		ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingsArgumentCaptor.capture());
		DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
		assertEquals(donationSettingsData.getDonationAmountA(), 0);
		assertEquals(donationSettingsData.getDonationAmountB(), 0);
		assertEquals(donationSettingsData.getDonationAmountC(), 0);
		assertEquals(donationSettingsData.getDonationAmountD(), 0);
		assertEquals(donationSettingsData.getFee(), 1);
		assertEquals(donationSettingsData.getEventId(),0);
		assertFalse(donationSettingsData.isAbsorbFee());
		assertFalse(donationSettingsData.isTextToGiveActivated());
	}

	@Test
	void test_sendEmail_withWhitelabel() {

		//setup
		emailMessage = new EmailMessage(TemplateId.SUPPORT_QUESTION);
		templateId = TemplateId.SUPPORT_QUESTION;
		String replyToEmail = "<EMAIL>";
		String subject = "EventNotification";
		String message = "message";
		whiteLabel.setSupportEmail("<EMAIL>");
		whiteLabel.setNotificationEmail("<EMAIL>");
		event.setWhiteLabel(whiteLabel);

		//Execution
		Set<String> stringSet = eventServiceImpl.sendEmail(event.getWhiteLabel(), replyToEmail, subject, message, templateId, emailMessage, null, whiteLabel.getFirmName());

		//Assertion
		assertTrue(stringSet.contains(whiteLabel.getSupportEmail()));
	}

	@Test
	void test_sendEmail_withTemplateIdPARTICIPANT_QUESTION() {

		//setup
		emailMessage = new EmailMessage(TemplateId.PARTICIPANT_QUESTION);
		templateId = TemplateId.PARTICIPANT_QUESTION;
		whiteLabel.setNotificationEmail("");
		event.setWhiteLabel(whiteLabel);
		String replyToEmail = "<EMAIL>";
		String subject = "EventNotification";
		String message = "message";

		//Execution
		Set<String> stringSet = eventServiceImpl.sendEmail(event.getWhiteLabel(), replyToEmail, subject, message, templateId, emailMessage, null, whiteLabel.getFirmName());

		//Assertion
		assertTrue(stringSet.isEmpty());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_sendContactEmail_throwException() {
//
//		//setup
//		contactDto = new ContactDto();
//		user.setEmail("<EMAIL>");
//
//		//mock
//		Mockito.doReturn(event).when(eventServiceImpl).getEventByURL(event.getEventURL());
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
//        when(eventDesignDetailRepoServices.findByEvent(event)).thenReturn(eventDesignDetail);
//
//		//Execution
//		eventServiceImpl.sendContactEmail(user, contactDto, event.getEventURL());
//
//		//Assertion
//		verify(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
//		(eventServiceImpl).getEventByURL(event.getEventURL());
//	}

	@Test
	void test_sendContactEmail_withUser() {

		//setup
		contactDto = new ContactDto();
		contactDto.setName("Jon");
		contactDto.setEmail(email);
		contactDto.setMessage("message");
		contactDto.setPageUrl("http://localhost:3000/e/mucm31Event9#Donate");
		user.setEmail("<EMAIL>");
		user.setFirstName("");

		//mock
        when(roEventService.getEventByURL(event.getEventURL())).thenReturn(event);
		Mockito.doNothing().when(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
        when(eventDesignDetailRepoServices.findByEvent(event)).thenReturn(eventDesignDetail);

		//Execution
		eventServiceImpl.sendContactEmail(user, contactDto, event.getEventURL());

		//Assertion
		ArgumentCaptor<EventQuestion> eventQuestionArgumentCaptor = ArgumentCaptor.forClass(EventQuestion.class);
		verify(eventQuestionRepository).save(eventQuestionArgumentCaptor.capture());

		EventQuestion eventQuestion = eventQuestionArgumentCaptor.getValue();
		assertEquals(eventQuestion.getQuestionBody(), contactDto.getMessage());
		assertEquals(eventQuestion.getQuestionAskerName(), STRING_DASH);
		assertEquals(eventQuestion.getQuestionAskerEmail(), user.getEmail());
		assertEquals(eventQuestion.getPageURL(), contactDto.getPageUrl());
		assertEquals(eventQuestion.getEventId().getEventId(), event.getEventId());
		assertEquals(eventQuestion.getUserId().getUserId(), user.getUserId());

		verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMail(any(), any(), anySet());
		(roEventService).getEventByURL(event.getEventURL());
	}

	@Test
	void test_sendContactEmail_withUserNull() {

		//setup
		contactDto = new ContactDto();
		contactDto.setName("Jon");
		contactDto.setEmail(email);
		contactDto.setMessage("message");
		contactDto.setPageUrl("http://localhost:3000/e/mucm31Event9#Donate");

		//mock
        when(roEventService.getEventByURL(event.getEventURL())).thenReturn(event);
		Mockito.doNothing().when(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
        when(eventDesignDetailRepoServices.findByEvent(event)).thenReturn(eventDesignDetail);

		//Execution
		eventServiceImpl.sendContactEmail(null, contactDto, event.getEventURL());

		//Assertion
		ArgumentCaptor<EventQuestion> eventQuestionArgumentCaptor = ArgumentCaptor.forClass(EventQuestion.class);
		verify(eventQuestionRepository).save(eventQuestionArgumentCaptor.capture());

		EventQuestion eventQuestion = eventQuestionArgumentCaptor.getValue();
		assertEquals(eventQuestion.getQuestionBody(), contactDto.getMessage());
		assertEquals(eventQuestion.getTime().toString(), DateUtils.getCurrentDate().toString());
		assertEquals(eventQuestion.getQuestionAskerName(), contactDto.getName());
		assertEquals(eventQuestion.getQuestionAskerEmail(), contactDto.getEmail());
		assertEquals(eventQuestion.getPageURL(), contactDto.getPageUrl());
		assertEquals(eventQuestion.getEventId().getEventId(), event.getEventId());
		assertNull(eventQuestion.getUserId());

		verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMail(any(), any(), anySet());
		verify(roEventService).getEventByURL(event.getEventURL());
	}

	@Test
	void test_sendContactEmail_throwExceptionNOT_AUTHORIZE() {

		//setup
		contactDto = new ContactDto();
		contactDto.setName("Jon");
		contactDto.setEmail(email);
		contactDto.setMessage("message");
		contactDto.setEmail("");
		contactDto.setPageUrl("http://localhost:3000/e/mucm31Event9#Donate");

		//Execution
		Exception exception = assertThrows(AuthorizationException.class,
                () -> eventServiceImpl.sendContactEmail(null, contactDto, event.getEventURL())
        );

		//Assertion
		verify(sendGridMailPrepareService, times(0)).sendSupportOrContactMail(any(), any(), anySet());
        assertEquals(NOT_AUTHORIZE, exception.getMessage());
	}


    //TODO: Mockito ReWrite
//	@Test
//	void test_sendSupportEmail_Exception() {
//
//		//setup
//		user.setEmail("");
//		user.setFirstName("");
//		whiteLabel.setHostBaseUrl("hostBaseUrl");
//		whiteLabel.setSupportEmail("<EMAIL>");
//		event.setWhiteLabel(whiteLabel);
//		String message = "message";
//
//		//mock
//		Mockito.doReturn(event).when(eventServiceImpl).getEventByURL(anyString());
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
//        when(eventDesignDetailRepoServices.findByEvent(event)).thenReturn(eventDesignDetail);
//
//		//Execution
//		eventServiceImpl.sendSupportEmail(user, message, event);
//
//		//Assertion
//		verify(sendGridMailPrepareService, times(1)).sendSupportOrContactMail(any(), any(), anySet());
//	}

	public static Object[] sendSupportEmailSuccessParams(){
		WhiteLabel whiteLabel = new WhiteLabel();
		return new Object[]{
				new Object[]{whiteLabel, null, "hostUrl"},
				new Object[]{whiteLabel, "<EMAIL>", "hostUrl"},
				new Object[]{whiteLabel, "<EMAIL>", ""},
				new Object[]{null, null, "hostUrl"},
		};
	}

	@ParameterizedTest
	@MethodSource("sendSupportEmailSuccessParams")
	void test_sendSupportEmail(WhiteLabel whiteLabel, String supportEmail, String hostBaseUrl) {

		//setup
		String message = "message";
		if(whiteLabel!=null){
			whiteLabel.setSupportEmail(supportEmail);
			whiteLabel.setHostBaseUrl(hostBaseUrl);
		}

		event.setWhiteLabel(whiteLabel);
		user.setEmail("<EMAIL>");

		//mock

		Mockito.doNothing().when(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
        when(eventDesignDetailRepoServices.findByEvent(event)).thenReturn(eventDesignDetail);

		//Execution
		eventServiceImpl.sendSupportEmail(user, message, event);

		//Assertion
		verify(sendGridMailPrepareService).sendSupportOrContactMail(any(), any(), anySet());
	}

	@Test
	void test_sendSupportEmail_throwException_NOT_AUTHORIZE() {

		//setup
		String message = "message";

		//Execution
		Exception exception = assertThrows(AuthorizationException.class,
                () -> eventServiceImpl.sendSupportEmail(null, message, event)
        );

        assertEquals(NOT_AUTHORIZE, exception.getMessage());
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedAndTextToGiveIsFalseAndDonationDateIsNull() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(false, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active but end dates are in past and 'text to give' is false so event is ready to release");
		verify(ticketingHelperService, times(2)).findTicketingByEventAndIfNotFoundCreateNew(event);
		verify(donationSettingsService, times(1)).getByEventId(event.getEventId());
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedAndTextToGiveIsTrueAndDonationDateIsNull() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(true, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, false);

		//Assertion
		assertFalse(result, "All modules are active but end dates are in past and text to give is false so event is not ready to release");
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(event);
		verify(donationSettingsService, times(1)).getByEventId(event.getEventId());
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedAndTextToGiveIsFalseAndDonationDateAfterCurrentDate() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		Date donationDate = new Date();
		DonationSettings donationSettings = new DonationSettings(false, donationDate);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active but end dates are in past and text to give is date is > current date so event is not ready to release");
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(event);
		verify(donationSettingsService, times(1)).getByEventId(event.getEventId());
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedAndTextToGiveIsFalseAndDonationDateAfterCurrentDateAndIsAllEventModuleDatesBeforeGivenDateTrue() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		Date donationDate = new Date();
		DonationSettings donationSettings = new DonationSettings(false, donationDate);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);

		Mockito.doReturn(true).when(eventServiceImpl).isAllEventModuleDatesBeforeGivenDate(any(), any());
		Mockito.doReturn(false).when(eventServiceImpl).isTextToGiveActive(any(), anyInt());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertTrue(result);
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(event);
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFalseAndTextToGiveIsFalseAndIsAllEventModuleDatesBeforeGivenDateTrue() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		Date donationDate = new Date();
		DonationSettings donationSettings = new DonationSettings(false, donationDate);

		//mock





		Mockito.doReturn(true).when(eventServiceImpl).isAllEventModuleDatesBeforeGivenDate(any(), any());
		Mockito.doReturn(false).when(eventServiceImpl).isTextToGiveActive(any(), anyInt());
		Mockito.doReturn(false).when(eventServiceImpl).isAnyEventModuleActivated(any());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertTrue(result);
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFalseAndTextToGiveIsFalseAndIsAllEventModuleDatesBeforeGivenDateFalse() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		Date donationDate = new Date();
		DonationSettings donationSettings = new DonationSettings(false, donationDate);

		//mock





		Mockito.doReturn(false).when(eventServiceImpl).isAllEventModuleDatesBeforeGivenDate(any(), any());
		Mockito.doReturn(true).when(eventServiceImpl).isAllEventModuleDatesAfterGivenDate(any(), any());
		Mockito.doReturn(false).when(eventServiceImpl).isTextToGiveActive(any(), anyInt());
		Mockito.doReturn(false).when(eventServiceImpl).isAnyEventModuleActivated(any());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertTrue(result);
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedAndModuleDateAfterGivenDateAndTextToGiveIsTrueAndDonationDateAfterCurrentDate() {

		//setup
		int daysBack = 10;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = true;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(false, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active but end dates are in past and text to give is date is > current date so event is not ready to release");
	}


	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFalseAndTextToGiveIsFalseAndDonationDateIsNull() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = false;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(false, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active false and 'text to give' is false so event is ready to release");
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFlaseAndModuleDateAfterAndTextToGiveIsFalseAndDonationDateIsNull() {

		//setup
		int daysBack = 50;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = false;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(false, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active false and 'text to give' is false so event is not ready to release");
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFlaseAndModuleDateAfterAndTextToGiveIsflaseAndDonationDateIsNull() {

		//setup
		int daysBack = 100;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = false;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(false, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active false and date is > daysForward and 'text to give' is false so event is ready to release");
	}

	@Test
	void test_IsElidgableForRelease_withIsAnyEventModuleActivatedFalseAndTextToGiveIsTrueAndDonationDateIsNull() {

		//setup
		int daysBack = -15;
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, daysBack);
		Date date = calendar.getTime();

		boolean isModuleActive = false;

		Event event = new Event();
		Auction auction = new Auction(isModuleActive, date);
		CauseAuction causeAuction = new CauseAuction(isModuleActive, date);
		Raffle raffle = new Raffle(isModuleActive, date);
		Ticketing ticketing = new Ticketing(isModuleActive, date);

		DonationSettings donationSettings = new DonationSettings(true, null);

		//mock



		doReturn(ticketing).when(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(event);
		doReturn(donationSettings).when(donationSettingsService).getByEventId(event.getEventId());

		//Execution
		boolean result = eventServiceImpl.isEligibleForRelease(event, true);

		//Assertion
		assertFalse(result, "All modules are active false and 'text to give' is true so event is not ready to release");
	}

	public static Object[] getModuleActivate(){
		return new Object[]{
				new Object[]{true, true, true, true, true},
				new Object[]{false, false, false, false, false},
				new Object[]{true, false, false, false, true},
				new Object[]{false, true, false, false, true},
				new Object[]{false, false, true, false, true},
				new Object[]{false, false, false, true, true},
		};
	}
	@ParameterizedTest
	@MethodSource("getModuleActivate")
	void isAnyEventModuleActivated(boolean auctionActive, boolean raffleActive, boolean causeAuctionActive, boolean ticketingActive, boolean activated){

		//setup
		auction = new Auction();
		auction.setActivated(auctionActive);
		raffle = new Raffle();
		raffle.setActivated(raffleActive);
		causeAuction = new CauseAuction();
		causeAuction.setActivated(causeAuctionActive);
		ticketing = new Ticketing();
		ticketing.setActivated(ticketingActive);

		//mock
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

		//Execution
		boolean moduleActivated = eventServiceImpl.isAnyEventModuleActivated(event);

		//Assertion
		assertEquals(moduleActivated, activated);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
	}

	@Test
	void test_isAnyEventModuleActivated_allmoduleNull(){

		//mock
		when(auctionService.findByEvent(any())).thenReturn(null);
		when(raffleService.findByEvent(any())).thenReturn(null);
		when(causeAuctionService.findByEvent(any())).thenReturn(null);
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(null);

		//Execution
		boolean moduleActivated = eventServiceImpl.isAnyEventModuleActivated(event);

		//Assertion
		assertFalse(moduleActivated);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
	}

	public static Object[] getBeforeEndDate(){
		String date = "2019/04/04 05:30";
		Date endDate = new Date(date);
		String date1 = "2050/04/04 05:30";
		Date endDate1 = new Date(date1);
		return new Object[]{
				new Object[]{endDate, endDate, endDate, endDate, true},
				new Object[]{endDate1, endDate1, endDate1, endDate1, false},
				new Object[]{endDate, endDate1, endDate1, endDate1, false},
				new Object[]{endDate, endDate, endDate1, endDate1, false},
				new Object[]{endDate, endDate, endDate, endDate1, false},
				new Object[]{endDate, endDate, endDate, endDate1, false},
		};
	}
	@ParameterizedTest
	@MethodSource("getBeforeEndDate")
	void test_isAllEventModuleDatesBeforeGivenDate(Date auctionEndDate, Date raffleEndDate, Date causeAuctionEndDate, Date ticketingEndDate, boolean isAllEventModuleDatesBeforeGivenDate){

		//setup
		auction = new Auction();
		auction.setEndDate(auctionEndDate);
		raffle = new Raffle();
		raffle.setEndDate(raffleEndDate);
		causeAuction = new CauseAuction();
		causeAuction.setEndDate(causeAuctionEndDate);
		ticketing = new Ticketing();
		ticketing.setEventEndDate(ticketingEndDate);

		//mock
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		Mockito.doReturn(ticketing).when(eventServiceImpl).getTicketing(any());

		//Execution
		boolean allEventModuleDatesBeforeGivenDate = eventServiceImpl.isAllEventModuleDatesBeforeGivenDate(event, new Date());

		//Assertion
		assertEquals(allEventModuleDatesBeforeGivenDate, isAllEventModuleDatesBeforeGivenDate);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
	}

	@Test
	void test_isAllEventModuleDatesBeforeGivenDate_allmoduleNull(){

		//mock
		when(auctionService.findByEvent(any())).thenReturn(null);
		when(raffleService.findByEvent(any())).thenReturn(null);
		when(causeAuctionService.findByEvent(any())).thenReturn(null);
		Mockito.doReturn(null).when(eventServiceImpl).getTicketing(any());

		//Execution
		boolean moduleActivated = eventServiceImpl.isAllEventModuleDatesBeforeGivenDate(event, new Date());

		//Assertion
		assertFalse(moduleActivated);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
	}

	public static Object[] getAfterEndDate(){
		String date = "2019/04/04 05:30";
		Date endDate = new Date(date);
		String date1 = "2050/04/04 05:30";
		Date endDate1 = new Date(date1);
		return new Object[]{
				new Object[]{endDate1, endDate1, endDate1, endDate1, true},
				new Object[]{endDate, endDate, endDate, endDate, false},
				new Object[]{endDate1, endDate, endDate, endDate, false},
				new Object[]{endDate1, endDate1, endDate, endDate, false},
				new Object[]{endDate1, endDate1, endDate1, endDate, false},
				new Object[]{endDate1, endDate1, endDate1, endDate, false},
		};
	}
	@ParameterizedTest
	@MethodSource("getAfterEndDate")
	void isAllEventModuleDatesAfterGivenDate(Date auctionEndDate, Date raffleEndDate, Date causeAuctionEndDate, Date ticketingEndDate, boolean isAllEventModuleDatesBeforeGivenDate){

		//setup
		auction = new Auction();
		auction.setEndDate(auctionEndDate);
		raffle = new Raffle();
		raffle.setEndDate(raffleEndDate);
		causeAuction = new CauseAuction();
		causeAuction.setEndDate(causeAuctionEndDate);
		ticketing = new Ticketing();
		ticketing.setEventEndDate(ticketingEndDate);

		//mock
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		Mockito.doReturn(ticketing).when(eventServiceImpl).getTicketing(any());

		//Execution
		boolean allEventModuleDatesBeforeGivenDate = eventServiceImpl.isAllEventModuleDatesAfterGivenDate(event, new Date());

		//Assertion
		assertEquals(allEventModuleDatesBeforeGivenDate, isAllEventModuleDatesBeforeGivenDate);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
	}

	@Test
	void test_isAllEventModuleDatesAfterGivenDate_allmoduleNull(){

		//mock
		String date1 = "2050/04/04 05:30";
		Date endDate1 = new Date(date1);
		when(auctionService.findByEvent(any())).thenReturn(null);
		when(raffleService.findByEvent(any())).thenReturn(null);
		when(causeAuctionService.findByEvent(any())).thenReturn(null);
		Mockito.doReturn(null).when(eventServiceImpl).getTicketing(any());

		//Execution
		boolean moduleActivated = eventServiceImpl.isAllEventModuleDatesAfterGivenDate(event, endDate1);

		//Assertion
		assertFalse(moduleActivated);
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
	}

	@Test
	void test_getStackTrace(){

		//Execution
		String stackTrace = eventServiceImpl.getStackTrace();

		//Assertion
		assertNotNull(stackTrace);
	}

	@Test
	void test_getSampleBidAmount_item_empty() {

		//Execution
		double amount = eventServiceImpl.getSampleBidAmount(event, Optional.empty());

		//Assertion
		assertEquals(0, Double.compare(amount , 400));
	}

	@Test
	void test_getSampleBidAmount_item_currentBid_0() {

		//setup
		item = new Item();
		Double bidIncreament = 50d;
		int startingBid = 100;
		item.setCurrentBid(0d);
		item.setStartingBid(startingBid);

		//mock
		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);

		//Execution
		double amount = eventServiceImpl.getSampleBidAmount(event, Optional.of(item));

		//Assertion
		assertEquals(0, Double.compare(amount , startingBid));
		verify(itemService, times(1)).getBidIncrement(any(), any());
	}

	@Test
	void test_getSampleBidAmount_itemCurrentBidNotEqualZero() {

		//setup
		item = new Item();
		Double bidIncrement = 50d;
		int startingBid = 100;
		item.setCurrentBid(10d);
		item.setStartingBid(startingBid);
		double amount = item.getCurrentBid() + bidIncrement;

		//mock
		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncrement);

		//Execution
		double amountData = eventServiceImpl.getSampleBidAmount(event, Optional.of(item));

		//Assertion
		assertEquals(0, Double.compare(amountData , amount));
		verify(itemService, times(1)).getBidIncrement(any(), any());
	}

	public static Object[] getEventStatus(){
		return new Object[]{
				new Object[]{EventStatus.EVENT_HAPPENING},
				new Object[]{EventStatus.EVENT_ENDED},
				new Object[]{EventStatus.EVENT_WINNER_PROCESSING},
				new Object[]{EventStatus.EVENT_WINNER_ANNOOUNCED},
				new Object[]{EventStatus.EVENT_DELETED},
				new Object[]{EventStatus.NULL},
		};
	}

	@ParameterizedTest
	@MethodSource("getEventStatus")
	void test_getEventsByEventStatus(EventStatus eventStatus) {

		//setup
		event.setEventStatus(eventStatus);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(eventRepository.findByEventStatus(any())).thenReturn(eventList);

		//Execution
		List<Event> eventData = eventServiceImpl.getEventsByEventStatus(eventStatus);

		//Assertion
		for (Event actualData: eventData) {
			assertEquals(actualData.getEventId(), event.getEventId());
			assertEquals(actualData.getEventStatus(), event.getEventStatus());
		}
		verify(eventRepository, times(1)).findByEventStatus(any());
	}

	public static Object[] getStaffEventList(){
		Event event = new Event();
		event.setEventId(1);
		Staff staff = new Staff();
		staff.setEvent(event);

		Event event1 = new Event();
		event1.setEventId(2);
		Staff staff1 = new Staff();
		staff1.setEvent(event1);

		List<Staff> staffList = new ArrayList<>();
		staffList.add(staff);

		List<Staff> staffList1 = new ArrayList<>();
		staffList1.add(staff);
		staffList1.add(staff1);

		List<Staff> staffListEmpty = new ArrayList<>();

		return new Object[]{
				new Object[]{staffList},
				new Object[]{staffList1},
				new Object[]{staffListEmpty},
		};
	}

	@ParameterizedTest
	@MethodSource("getStaffEventList")
	void test_getEventsByUser_success(List<Staff> staffList) {

		//mock
		when(staffService.findByUserAndEventIsNotNull(any())).thenReturn(staffList);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventsByUser(user);

		//Assertion
		assertTrue(eventData.size() == staffList.size());
		verify(staffService, times(1)).findByUserAndEventIsNotNull(any());
	}

	@Test
	void test_getEventsByAdminUser_success() {

		//setup
		staff.setEvent(event);
		List<Staff> staffList = new ArrayList<>();
		staffList.add(staff);

		//mock
		when(roStaffService.findByUserAndRoleAndEventIsNotNull(any(), any())).thenReturn(staffList);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventsByAdminUser(user);

		//Assertion
		assertEquals(eventData.iterator().next().getEventId(), staff.getEvent().getEventId());
		verify(roStaffService, times(1)).findByUserAndRoleAndEventIsNotNull(any(), any());
	}

	@Test
	void test_getEventsByStaffUser_success() {
		//setup
		staff.setEvent(event);
		List<Staff> staffList = new ArrayList<>();
		staffList.add(staff);

		//mock
		when(roStaffService.findByUserAndRoleAndEventIsNotNull(any(), any())).thenReturn(staffList);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventsByStaffUser(user);

		//Assertion
		assertEquals(eventData.iterator().next().getEventId(), staff.getEvent().getEventId());
	}

	@Test
	void test_getEventByAuctionBidUser_success() {

		//setup
		event.setEventStatus(EventStatus.EVENT_DELETED);
		Set<Event> eventSet = new HashSet<>();
		eventSet.add(event);

		//mock
		when(eventRepository.findEventByAuctionBidUser(any(), any())).thenReturn(eventSet);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventByAuctionBidUser(user);

		//Assertion
		assertEquals(eventData.iterator().next().getEventStatus(), event.getEventStatus());
	}

	@Test
	void test_getEventByCauseAuctionPledgeUser_success() {

		//setup
		event.setEventStatus(EventStatus.EVENT_DELETED);
		Set<Event> eventSet = new HashSet<>();
		eventSet.add(event);

		//mock
		when(eventRepository.findEventByCauseAuctionPledgeUser(any(), any())).thenReturn(eventSet);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventByCauseAuctionPledgeUser(user);

		//Assertion
		assertEquals(eventData.iterator().next().getEventStatus(), event.getEventStatus());
	}

	@Test
	void test_getEventByRaffleSubmittedTicketUser_success() {

		//setup
		event.setEventStatus(EventStatus.EVENT_DELETED);
		Set<Event> eventSet = new HashSet<>();
		eventSet.add(event);

		//mock
		when(eventRepository.findEventByRaffleSubmittedTicketUser(any(), any())).thenReturn(eventSet);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventByRaffleSubmittedTicketUser(user);

		//Assertion
		assertEquals(eventData.iterator().next().getEventStatus(), event.getEventStatus());
	}

	@Test
	void test_createSquareRecord() {

		//setup
		eventChecklist = new EventChecklist();
		staff.setId(id);

		getJoinEventWithDealProductData();

		//mock

		when(eventChecklistService.findByEvent(event)).thenReturn(eventChecklist);

		//Execution
		eventServiceImpl.createSquareRecord(event, staff, stripe);

		//Assertion
		verify(eventChecklistService, times(1)).findByEvent(event);
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), squareConfiguration.getAppSecretKey());
		assertEquals(stripeData.getStripePublishableKey(), squareConfiguration.getClientID());
		assertEquals(stripeData.getStripeUserId(), SANDBOX);
		assertEquals(stripeData.getAccountSource(), "CONNECTED EVENTS");
		assertEquals(stripeData.getStaff().getId(), staff.getId());
		assertTrue(stripeData.isActivated());
		assertEquals(stripeData.getPaymentGateway(), EnumPaymentGateway.SQUARE.value());
		assertEquals(stripeData.getSquareMerchantId(),SANDBOX);
		assertEquals(stripeData.getSquareMerchantLocation(), SANDBOX);

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isActivatePaymentProcessing());
	}

	@Test
	void test_saveStripeAccountOfCustomerForEvent_withPaymentGateWayStripe() throws JSONException, StripeException {

		//setup
		account = new Account();
		eventChecklist = new EventChecklist();
		String code = "code";
		user.setEmail(email);
		staff.setId(id);
		stripe.setId(id);
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
		getJsonObjData();

		account.setDetailsSubmitted(true);

		getJoinEventWithDealProductData();

		//mock

		when(stripePaymentService.retrieveAccount(anyString())).thenReturn(account);
		when(eventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
		when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(staff);

		//Execution
		eventServiceImpl.saveStripeAccountOfCustomerForEvent(stripe, code, response, event, user);

		//Assertion
		verify(stripePaymentService, times(1)).retrieveAccount(anyString());
		verify(eventChecklistService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(event, user, false);

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isActivatePaymentProcessing());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), response.get("access_token"));
		assertEquals(stripeData.getTokenType(), 	 response.get("token_type"));
		assertEquals(stripeData.getRefreshToken(), response.get("refresh_token"));
		assertEquals(stripeData.getStripePublishableKey(), response.get("stripe_publishable_key"));
		assertEquals(stripeData.getStripeUserId(), response.get("stripe_user_id"));
		assertEquals(stripeData.getScope(), response.get("scope"));
		assertEquals(stripeData.getLivemode(), response.get("livemode"));
		assertEquals(stripeData.getCode(), code);
		assertEquals(stripeData.getAccountSource(), "Oauth");
		assertEquals(stripeData.getStaff().getId(), staff.getId());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());
	}

	@Test
	void test_saveStripeAccountOfCustomerForEvent_withPaymentGateWayStripeAndAccountDetailsSubmittedFalse() throws JSONException, StripeException {

		//setup
		account = new Account();
		String code = "code";
		user.setEmail(email);
		staff.setId(id);
		stripe.setId(id);
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
		getJsonObjData();

		account.setDetailsSubmitted(false);

		getJoinEventWithDealProductData();

		//mock

		when(stripePaymentService.retrieveAccount(anyString())).thenReturn(account);
		when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(staff);

		//Execution
		eventServiceImpl.saveStripeAccountOfCustomerForEvent(stripe, code, response, event, user);

		//Assertion
		verify(stripePaymentService, times(1)).retrieveAccount(anyString());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(event, user, false);

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), response.get("access_token"));
		assertEquals(stripeData.getTokenType(), 	 response.get("token_type"));
		assertEquals(stripeData.getRefreshToken(), response.get("refresh_token"));
		assertEquals(stripeData.getStripePublishableKey(), response.get("stripe_publishable_key"));
		assertEquals(stripeData.getStripeUserId(), response.get("stripe_user_id"));
		assertEquals(stripeData.getScope(), response.get("scope"));
		assertEquals(stripeData.getLivemode(), response.get("livemode"));
		assertEquals(stripeData.getCode(), code);
		assertEquals(stripeData.getAccountSource(), "Oauth");
		assertEquals(stripeData.getStaff().getId(), staff.getId());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());
	}

	@Test
	void test_saveStripeAccountOfCustomerForEvent_withPaymentGateWaySquareAndLocationStatusACTIVE() throws JSONException, StripeException {

		//setup
		String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
        List<String> capabilitiesEnumList = new ArrayList<>();
        capabilitiesEnumList.add("PROCESSING");
		location = new Location("1", null, null, null, capabilitiesEnumList, "ACTIVE", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
		eventChecklist = new EventChecklist();
		String code = "code";
		user.setEmail(email);
		staff.setId(id);
		stripe.setId(id);
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		getJsonObjData();

		Instant instant = Instant.parse((String) response.get("expires_at"));

		getJoinEventWithDealProductData();

		//mock

		when(squarePaymentService.getLocationId(anyString())).thenReturn(location);
		when(eventChecklistService.findByEvent(event)).thenReturn(eventChecklist);
		when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(staff);

		//Execution
		eventServiceImpl.saveStripeAccountOfCustomerForEvent(stripe, code, response, event, user);

		//Assertion
		verify(squarePaymentService, times(1)).getLocationId(anyString());
		verify(eventChecklistService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(event, user, false);

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isActivatePaymentProcessing());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), response.get("access_token"));
		assertEquals(stripeData.getTokenType(), 	 response.get("token_type"));
		assertEquals(stripeData.getRefreshToken(), response.get("refresh_token"));
		assertEquals(stripeData.getSquareMerchantLocation(), location.getId());
		assertEquals(stripeData.getStripePublishableKey(), squareConfiguration.getClientID());
		assertEquals(stripeData.getStripeUserId(), response.get("merchant_id"));
		assertEquals(stripeData.getScope(), squareScope);
		assertTrue(stripeData.getLivemode());
		assertEquals(stripeData.getTokenExpiracyDate(), new Date(instant.getMillis()));
		assertEquals(stripeData.getSquareMerchantId(),  response.get("merchant_id"));
		assertEquals(stripeData.getCode(), code);
		assertEquals(stripeData.getAccountSource(), "Oauth");
		assertEquals(stripeData.getStaff().getId(), staff.getId());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());
	}

    @Test
    void test_getEventByRafflePurchasedTicketUser_success() {

        //setup
        event.setEventStatus(EventStatus.EVENT_DELETED);
        Set<Event> eventSet = new HashSet<>();
        eventSet.add(event);

        //mock
        when(eventRepository.findEventByRafflePurchasedTicketUser(any(), any())).thenReturn(eventSet);

        //Execution
        Set<Event> eventData = eventServiceImpl.getEventByRafflePurchasedTicketUser(user);

        //Assertion
        assertEquals(eventData.iterator().next().getEventStatus(), event.getEventStatus());
    }

	@Test
	void test_saveStripeAccountOfCustomerForEvent_withPaymentGateWaySquareAndLocationStatusINACTIVE() throws JSONException, StripeException {

		//setup
		String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
        List<String> capabilitiesEnumList = new ArrayList<>();
        capabilitiesEnumList.add("");
		location = new Location("1", null, null, null, capabilitiesEnumList, "INACTIVE", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
		String code = "code";
		user.setEmail(email);
		staff.setId(id);
		stripe.setId(id);
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		getJsonObjData();

		Instant instant = Instant.parse((String) response.get("expires_at"));

		getJoinEventWithDealProductData();

		//mock

		when(squarePaymentService.getLocationId(anyString())).thenReturn(location);
		when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(staff);

		//Execution
		eventServiceImpl.saveStripeAccountOfCustomerForEvent(stripe, code, response, event, user);

		//Assertion
		verify(squarePaymentService, times(1)).getLocationId(anyString());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(event, user, false);

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), response.get("access_token"));
		assertEquals(stripeData.getTokenType(), 	 response.get("token_type"));
		assertEquals(stripeData.getRefreshToken(), response.get("refresh_token"));
		assertEquals(stripeData.getSquareMerchantLocation(), location.getId());
		assertEquals(stripeData.getStripePublishableKey(), squareConfiguration.getClientID());
		assertEquals(stripeData.getStripeUserId(), response.get("merchant_id"));
		assertEquals(stripeData.getScope(), squareScope);
		assertTrue(stripeData.getLivemode());
		assertEquals(stripeData.getTokenExpiracyDate(), new Date(instant.getMillis()));
		assertEquals(stripeData.getSquareMerchantId(),  response.get("merchant_id"));
		assertEquals(stripeData.getCode(), code);
		assertEquals(stripeData.getAccountSource(), "Oauth");
		assertEquals(stripeData.getStaff().getId(), staff.getId());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());
	}

	@Test
	void test_saveStripeAccountOfCustomerForEvent_withPaymentGateWaySquareAndLocationStatusINACTIVEAndLocationCapabilitiesPROCESSING() throws JSONException, StripeException {

		//setup
		String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
        List<String> capabilitiesEnumList = new ArrayList<>();
        capabilitiesEnumList.add("PROCESSING");
        location = new Location("1", null, null, null, capabilitiesEnumList, "INACTIVE", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
        String code = "code";
		user.setEmail(email);
		staff.setId(id);
		stripe.setId(id);
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		getJsonObjData();

		Instant instant = Instant.parse((String) response.get("expires_at"));

		getJoinEventWithDealProductData();

		//mock

		when(squarePaymentService.getLocationId(anyString())).thenReturn(location);
		when(roStaffService.findByEventAndUserNotExhibitor(event, user, false)).thenReturn(staff);

		//Execution
		eventServiceImpl.saveStripeAccountOfCustomerForEvent(stripe, code, response, event, user);

		//Assertion
		verify(squarePaymentService, times(1)).getLocationId(anyString());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(event, user, false);

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.getAccessToken(), response.get("access_token"));
		assertEquals(stripeData.getTokenType(), 	 response.get("token_type"));
		assertEquals(stripeData.getRefreshToken(), response.get("refresh_token"));
		assertEquals(stripeData.getSquareMerchantLocation(), location.getId());
		assertEquals(stripeData.getStripePublishableKey(), squareConfiguration.getClientID());
		assertEquals(stripeData.getStripeUserId(), response.get("merchant_id"));
		assertEquals(stripeData.getScope(), squareScope);
		assertTrue(stripeData.getLivemode());
		assertEquals(stripeData.getTokenExpiracyDate(), new Date(instant.getMillis()));
		assertEquals(stripeData.getSquareMerchantId(),  response.get("merchant_id"));
		assertEquals(stripeData.getCode(), code);
		assertEquals(stripeData.getAccountSource(), "Oauth");
		assertEquals(stripeData.getStaff().getId(), staff.getId());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		//Assertion
		Event eventData = eventArgumentCaptor.getValue();
		assertTrue(eventData.isCreditCardEnabled());
	}

	@Test
	void test_enablePaymentProcessing() {

		//setup
		eventChecklist = new EventChecklist();

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		eventServiceImpl.enablePaymentProcessing(stripe, event);

		//Assertion
		verify(eventChecklistService, times(1)).findByEvent(any());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isActivatePaymentProcessing());
	}

	@Test
	void test_bidCalculator_throwException(){

		Item item = new Item();
		item.setCurrentBid(10d);
		item.setBuyItNowPrice(0);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languagecode = "EN";

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.bidCalculator(item, 20, 15, event,languageMap,languagecode)
        );

        assertEquals("The minimum bid increment for this item is $15.0. Please bid at least $25.0.", exception.getMessage());
	}

	@Test
	void test_bidCalculator_withCurrentBidGreaterThanBuyItNowPrice(){

		//setup
		Item item = new Item();
		item.setCurrentBid(100d);
		item.setBuyItNowPrice(10);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languagecode = "EN";

		//Execution
		eventServiceImpl.bidCalculator(item, 20, 15, event,languageMap,languagecode);
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_bidCalculator_WithisvalidTrue(){
//
//		//setup
//		Item item = new Item();
//		item.setCurrentBid(10d);
//		item.setBuyItNowPrice(0);
//
//		//mock
//		Mockito.doReturn(true).when(eventServiceImpl).isValidBid(any(), anyInt(), anyInt());
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//        String languagecode = "EN";
//
//		//Execution
//		eventServiceImpl.bidCalculator(item, 20, 15, event,languageMap,languagecode);
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_bidCalculator_WithisvalidTrueAndBuyItNowPriceNotEqualZero(){
//
//		//setup
//		Item item = new Item();
//		item.setCurrentBid(10d);
//		item.setBuyItNowPrice(100);
//
//		//mock
//		Mockito.doReturn(true).when(eventServiceImpl).isValidBid(any(), anyInt(), anyInt());
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//        String languagecode = "EN";
//
//		//Execution
//		eventServiceImpl.bidCalculator(item, 20, 15, event,languageMap,languagecode);
//	}

	@Test
	void test_bidCalculatorElse_withBuyItNowFalse(){

		Item item = new Item();
		item.setCurrentBid(10d);
		item.setBuyItNowPrice(100);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languagecode = "EN";

		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.bidCalculator(item, 20, 15, event,languageMap,languagecode)
        );

        assertEquals("The minimum bid increment for this item is $15.0. Please bid at least $25.0.", exception.getMessage());
	}

	@Test
	void test_throwExceptionIfFirstBidIsLessThanStartingBid(){

		Item item = new Item();
		item.setStartingBid(10);
		item.setCurrentBid(0d);

        Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.throwExceptionIfFirstBidIsLessThanStartingBid(item, 5)
        );

        assertEquals(NotAcceptableException.AuctionExceptionMsg.BID_SHOULD_BE_GREATER_THAN_STARTING_BID.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_isValidBid(){

		//setup
		item = new Item();
		item.setCurrentBid(50d);
		int itemBid = 100;
		int bidIncrement = 10;

		//Execution
		boolean validBid = eventServiceImpl.isValidBid(item, itemBid, bidIncrement);

		//Assertion
		assertTrue(validBid);
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withUserAndIsEventStaffOrAdminTrue(){

		//mock
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(true);

		//Execution
		eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.AUCTION, event, DateUtils.getCurrentDate());

		//Assertion
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
	}

	// For Auction

	@Test
	void test_checkModuleActivateAndNotExpired_withAuctionEnddatePast() throws ParseException {

		//setup
		Auction auction = new Auction();
		auction.setActivated(false);
		auction.setEndDate(new Date("01/09/2019 08:00"));

		//mock
		when(auctionService.findByEvent(event)).thenReturn(auction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.AUCTION, event, new Date())
        );

		//Assertion
		verify(auctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.AuctionExceptionMsg.AUCTION_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withAuctionModuleActivateTrueEndDatePast() throws ParseException {

		//setup
		Date endDate = new SimpleDateFormat("dd/MM/yyyy hh:mm").parse("01/09/2019 08:00");
		Auction auction = new Auction();
		auction.setActivated(true);
		auction.setEndDate(endDate);

		//mock
		when(auctionService.findByEvent(event)).thenReturn(auction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.AUCTION, event, DateUtils.getCurrentDate())
        );

		//Assertion
		verify(auctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.AuctionExceptionMsg.AUCTION_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withAuctionModuleActivateTrueEndDateFuture() throws ParseException {

		//setup
		Date endDate = new SimpleDateFormat("dd/MM/yyyy hh:mm").parse("01/09/2060 08:00");
		Auction auction = new Auction();
		auction.setActivated(true);
		auction.setEndDate(endDate);

		//mock
		when(auctionService.findByEvent(event)).thenReturn(auction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.AUCTION, event, DateUtils.getCurrentDate());

		//Assertion
		verify(auctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
	}

	// for Raffle

	@Test
	void test_checkModuleActivateAndNotExpired_Raffle() throws ParseException {

		//setup
		Raffle raffle = new Raffle();
		raffle.setActivated(false);
		raffle.setEndDate(DateUtils.addDaysToNow(5));

		//mock
		when(raffleService.findByEvent(event)).thenReturn(raffle);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.RAFFLE, event, DateUtils.getCurrentDate())
        );

		//Assertion
		verify(raffleService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.RaffleExceptionMsg.RAFFLE_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withRaffleModuleActivateTrueEndDatePast() throws ParseException {

		//setup
		Raffle raffle = new Raffle();
		raffle.setActivated(true);
		raffle.setEndDate(new Date("01/09/2019 08:00"));

		//mock
		when(raffleService.findByEvent(event)).thenReturn(raffle);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.RAFFLE, event, new Date())
        );

		//Assertion
		verify(raffleService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.RaffleExceptionMsg.RAFFLE_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withRaffleModuleActivateTrueEndDateFuture() throws ParseException {

		//setup
		Date endDate = new SimpleDateFormat("dd/MM/yyyy hh:mm").parse("01/09/2060 08:00");
		Raffle raffle = new Raffle();
		raffle.setActivated(true);
		raffle.setEndDate(endDate);

		//mock
		when(raffleService.findByEvent(event)).thenReturn(raffle);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.RAFFLE, event, DateUtils.getCurrentDate());

		//Assertion
		verify(raffleService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
	}

	// for Cause Auction / FundANeed

	@Test
	void test_checkModuleActivateAndNotExpired_CauseAuction() throws ParseException {

		//setup
		CauseAuction causeAuction = new CauseAuction();
		causeAuction.setActivated(false);
		causeAuction.setEndDate(DateUtils.addDaysToNow(5));

		//mock
		when(causeAuctionService.findByEvent(event)).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.CAUSEAUCTION, event, DateUtils.getCurrentDate())
        );

		//Assertion
		verify(causeAuctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.FundANeedExceptionMsg.PLEDGE_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_withCauseAuctionModuleActivateTrueEndDatePast() throws ParseException {

		//setup
		CauseAuction causeAuction = new CauseAuction();
		causeAuction.setActivated(true);
		causeAuction.setEndDate(new Date("01/09/2019 08:00"));

		//mock
		when(causeAuctionService.findByEvent(event)).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.CAUSEAUCTION, event, new Date())
        );

		//Assertion
		verify(causeAuctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
        assertEquals(NotAcceptableException.FundANeedExceptionMsg.PLEDGE_NOT_ACTIVE.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_checkModuleActivateAndNotExpired_CauseAuction_Module_Activate_true_End_date_Future() throws ParseException {

		//setup
		Date endDate = new SimpleDateFormat("dd/MM/yyyy hh:mm").parse("01/09/2060 08:00");
		CauseAuction causeAuction = new CauseAuction();
		causeAuction.setActivated(true);
		causeAuction.setEndDate(endDate);

		//mock
		when(causeAuctionService.findByEvent(event)).thenReturn(causeAuction);
		when(roStaffService.isEventStaffOrAdmin(event, user)).thenReturn(false);

		//Execution
		eventServiceImpl.checkModuleActivateAndNotExpired(user, ModuleType.CAUSEAUCTION, event, DateUtils.getCurrentDate());

		//Assertion
		verify(causeAuctionService, times(1)).findByEvent(event);
		verify(roStaffService, times(1)).isEventStaffOrAdmin(event, user);
	}

	@Test
	void test_checkModuleActivateAndNotExpired_moduleTypeDonationAndUserNull() throws ParseException {

		//Execution
		eventServiceImpl.checkModuleActivateAndNotExpired(null, ModuleType.DONATION, event, DateUtils.getCurrentDate());
	}

	@Test
	void test_getCustomerTransaction_withStripeTransactionEVENT_TICKETING() {

		//setup
		stripeTransaction = new StripeTransaction();
		String phoneNumber = "9898989898";
		stripeTransaction.setSource(StripeTransactionSource.EVENT_TICKETING);
		stripeTransaction.setAmount(100d);
		stripeTransaction.setUser(user);

		List<StripeTransaction> stripeTransactionList = new ArrayList<>();
		stripeTransactionList.add(stripeTransaction);

		//mock
		when(stripeTransactionService.findAllByEvent(event)).thenReturn(stripeTransactionList);
		when(phoneNumberService.getDisplayNumber(any())).thenReturn(phoneNumber);

		//Execution
		List<StripeTransactionDto> customerTransactionData = eventServiceImpl.getCustomerTransaction(event);

		//Assertion
		assertEquals(customerTransactionData.get(0).getAmount(), stripeTransaction.getAmount());
		assertEquals(customerTransactionData.get(0).getSource(), stripeTransaction.getSource().toString());
		assertEquals(customerTransactionData.get(0).getName(), stripeTransaction.getUser().getFirstName());
		assertEquals(customerTransactionData.get(0).getStatus(), SUCCESS);
		assertEquals(customerTransactionData.get(0).getPhoneNumber(), phoneNumber);

		verify(stripeTransactionService, times(1)).findAllByEvent(event);
		verify(phoneNumberService, times(1)).getDisplayNumber(any());
	}

	@Test
	void test_getCustomerTransaction_withStripeTransactionMODULE_ACTIVE() {

		//setup
		stripeTransaction = new StripeTransaction();
		stripeTransaction.setSource(StripeTransactionSource.MODULE_ACTIVE);

		List<StripeTransaction> stripeTransactionList = new ArrayList<>();
		stripeTransactionList.add(stripeTransaction);

		//mock
		when(stripeTransactionService.findAllByEvent(event)).thenReturn(stripeTransactionList);

		//Execution
		List<StripeTransactionDto> customerTransactionData = eventServiceImpl.getCustomerTransaction(event);

		//Assertion
		assertTrue(customerTransactionData.isEmpty());

		verify(stripeTransactionService, times(1)).findAllByEvent(event);
	}

	@Test
	void test_updateUserByNewUser() {

		//setup
		User newUser = new User();
		newUser.setUserId(id);

		//mock
		Mockito.doNothing().when(eventInteractionRepository).updateByNewUser(anyLong(), anyLong());

		//Execution
		eventServiceImpl.updateUserByNewUser(user, newUser);

		//Assertion
		verify(eventInteractionRepository, times(1)).updateByNewUser(anyLong(), anyLong());
	}

	@Test
	void test_updateGeneralSetting_withTicketing() {

		//setup
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

		getJoinEventWithDealProductData();

		//mock


		//Execution
		eventServiceImpl.updateGeneralSetting(event);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getTicketingId().longValue(), ticketing.getId());
	}

	@Test
	void test_updateGeneralSetting_withTicketingNull() {

		//setup
		getJoinEventWithDealProductData();

		//mock
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(null);
		when(ticketingHelperService.createNewTicketing(event)).thenReturn(ticketing);


		//Execution
		eventServiceImpl.updateGeneralSetting(event);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(event);
		verify(ticketingHelperService, times(1)).createNewTicketing(event);

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getTicketingId().longValue(), ticketing.getId());
	}

	@Test
	void test_getTicketing() {

		//mock
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

		//Execution
		Ticketing ticketingData = eventServiceImpl.getTicketing(event);

		//Assertion
		assertEquals(ticketingData.getId(), ticketing.getId());
		assertEquals(ticketingData.getEventAddress(), ticketing.getEventAddress());
		assertEquals(ticketingData.getActivated(), ticketing.getActivated());

		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(event);
	}

	@Test
	void test_getEventByWhiteLabelOwner() {

		//setup
		whiteLabel.setId(id);
		event.setWhiteLabel(whiteLabel);
		Set<Event> eventSet = new HashSet<>();
		eventSet.add(event);

		//mock
		when(eventRepository.findEventsByWhiteLabelUser(any(), any())).thenReturn(eventSet);

		//Execution
		Set<Event> eventData = eventServiceImpl.getEventByWhiteLabelOwner(user);

		//Assertion
		assertEquals(eventData.iterator().next().getTimezoneId(), event.getTimezoneId());
		assertEquals(eventData.iterator().next().getEquivalentTimeZone(), event.getEquivalentTimeZone());
		assertEquals(eventData.iterator().next().getWhiteLabel().getId(), event.getWhiteLabel().getId());

		verify(eventRepository, times(1)).findEventsByWhiteLabelUser(any(), any());
	}

	@Test
	void test_GetWhiteLabelEvents_withSearchString() {

		//setup
		String searchString = "searchString";
		whiteLabel.setId(id);
		event.setWhiteLabel(whiteLabel);
		event.setEventStatus(null);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(eventRepository.findEventsByWhiteLabelAndEventStatusIsNullOrEventStatusNotWithSearch(any(), anyString(), any())).thenReturn(eventList);

		//Execution
		List<Event> eventData = eventServiceImpl.getWhiteLabelEvents(whiteLabel, searchString);

		//Assertion
		assertEquals(eventData.get(0).getTimezoneId(), event.getTimezoneId());
		assertEquals(eventData.get(0).getEquivalentTimeZone(), event.getEquivalentTimeZone());
		assertEquals(eventData.get(0).getWhiteLabel().getId(), event.getWhiteLabel().getId());

		verify(eventRepository, times(1)).findEventsByWhiteLabelAndEventStatusIsNullOrEventStatusNotWithSearch(any(), anyString(), any());
	}

	@Test
	void test_GetWhiteLabelEvents_withSearchStringEmpty() {

		//setup
		String searchString = "";
		whiteLabel.setId(id);
		event.setWhiteLabel(whiteLabel);
		event.setEventStatus(null);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(roEventService.getWhiteLabelEvents(any())).thenReturn(eventList);

		//Execution
		List<Event> eventData = eventServiceImpl.getWhiteLabelEvents(whiteLabel, searchString);

		//Assertion
		assertEquals(eventData.get(0).getTimezoneId(), event.getTimezoneId());
		assertEquals(eventData.get(0).getEquivalentTimeZone(), event.getEquivalentTimeZone());
		assertEquals(eventData.get(0).getWhiteLabel().getId(), event.getWhiteLabel().getId());

		verify(roEventService, times(1)).getWhiteLabelEvents(any());
	}

	@Test
	void test_getAllEventsDto_success_withSearchString() {

		//setup
		int page = 1;
		int size =10;
		String searchString = "test";
		boolean pastEvent = true;
		boolean upcomingEvent = false;
		boolean activeEvent = false;
		String sortField = "sortField";
		String sortDirection = Sort.Direction.ASC.name();
        organizer = new Organizer();
        organizer.setName("Organizer Name");
        organizer.setOrganizerPageURL("organizerPageUrl");
        whiteLabel.setFirmName("whitelabelFirmName");
        whiteLabel.setWhiteLabelUrl("whitelabelUrl");

		//mock
        Object[] data = {BigInteger.valueOf(event.getEventId()), event.getName(), event.getEventURL(), "US", BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date(), "Paid","0","0",new Date("2022/09/10 09:31:00"),organizer.getName(),organizer.getOrganizerPageURL(),whiteLabel.getFirmName(),whiteLabel.getWhiteLabelUrl()};
        List<Object[]> eventPageDataList = new ArrayList<>();
        eventPageDataList.add(data);
        Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
		when(eventRepository.findAllEventByNameOrEventURLAndEventStatusIsNullOrEventStatusIsNot(anyString(),anyBoolean(),anyBoolean(), anyBoolean(), any())).thenReturn(pageData);


		//Execution
		DataTableResponse eventDtoData = eventServiceImpl.getAllEventsDto(page, size, searchString, pastEvent, upcomingEvent, activeEvent, sortField, sortDirection, EventSearchFilter.EVENT_NAME_URL);

		//Assertion
		assertEquals(eventDtoData.getRecordsTotal(), pageData.getTotalElements());
		assertEquals(eventDtoData.getRecordsFiltered(), pageData.getNumberOfElements());

		verify(eventRepository, times(1)).findAllEventByNameOrEventURLAndEventStatusIsNullOrEventStatusIsNot(anyString(), anyBoolean(), anyBoolean(), anyBoolean(), any());
	}

	@Test
	void test_getAllEventsDto_withSearchStringEmptyAndPastEventTrueAnWwithCountryCode() {

		//setup
		int page = 1;
		int size =10;
		String searchString = "";
		boolean pastEvent = true;
		boolean upcomingEvent = false;
        boolean activeEvent = false;
		String sortField = "eventName";
		String sortDirection = Sort.Direction.ASC.name();
		event.setName("TestEvent");
		event.setCountryCode(CountryCode.US);
        organizer = new Organizer();
        organizer.setName("Organizer Name");
        organizer.setOrganizerPageURL("organizerPageUrl");
        whiteLabel.setFirmName("whitelabelFirmName");
        whiteLabel.setWhiteLabelUrl("whitelabelUrl");

		Object[] data = {BigInteger.valueOf(event.getEventId()), event.getName(), event.getEventURL(), "US", BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date(), "Paid","0","0",new Date("2022/09/10 09:31:00"),organizer.getName(),organizer.getOrganizerPageURL(),whiteLabel.getFirmName(),whiteLabel.getWhiteLabelUrl()};
		List<Object[]> eventPageDataList = new ArrayList<>();
		eventPageDataList.add(data);
		Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
		pageData.getTotalElements();
		pageData.getTotalPages();

		//mock
		Page<Event> eventPage = getEventpage();
		when(eventRepository.getPastEvents(any())).thenReturn(pageData);


		//Execution
		DataTableResponse eventDtoData = eventServiceImpl.getAllEventsDto(page, size, searchString, pastEvent, upcomingEvent, activeEvent, sortField, sortDirection, EventSearchFilter.EVENT_NAME_URL);

		//Assertion
		assertEquals(eventDtoData.getRecordsTotal(), eventPage.getTotalElements());
		assertEquals(eventDtoData.getRecordsFiltered(), eventPage.getNumberOfElements());

		verify(eventRepository, times(1)).getPastEvents(any());
	}

	@Test
	void test_getAllEventsDto_success_withSearchStringEmptyAndPastEventFalseAndWithCountryCodeNull() {

		//setup
		int page = 1;
		int size =10;
		String searchString = "";
		boolean pastEvent = false;
		boolean upcomingEvent = false;
        boolean activeEvent = true;
		String sortField = "eventName";
		String sortDirection = Sort.Direction.ASC.name();
		event.setName("TestEvent");
		event.setCountryCode(CountryCode.US);
        organizer = new Organizer();
        organizer.setName("Organizer Name");
        organizer.setOrganizerPageURL("organizerPageUrl");
        whiteLabel.setFirmName("whitelabelFirmName");
        whiteLabel.setWhiteLabelUrl("whitelabelUrl");

		Object[] data = {BigInteger.valueOf(event.getEventId()), event.getName(), event.getEventURL(), null, BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date(), "Paid","0","0",new Date("2022/09/10 09:31:00"),organizer.getName(),organizer.getOrganizerPageURL(),whiteLabel.getFirmName(),whiteLabel.getWhiteLabelUrl()};
		List<Object[]> eventPageDataList = new ArrayList<>();
		eventPageDataList.add(data);
		Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
		pageData.getTotalElements();
		pageData.getTotalPages();

		//mock
		Page<Event> eventPage = getEventpage();
		when(eventRepository.getActiveEvents(any())).thenReturn(pageData);


		//Execution
		DataTableResponse eventDtoData = eventServiceImpl.getAllEventsDto(page, size, searchString, pastEvent, upcomingEvent, activeEvent, sortField, sortDirection, EventSearchFilter.EVENT_NAME_URL);

		//Assertion
		assertEquals(eventDtoData.getRecordsTotal(), eventPage.getTotalElements());
		assertEquals(eventDtoData.getRecordsFiltered(), eventPage.getNumberOfElements());

		verify(eventRepository, times(1)).getActiveEvents(any());
	}

	public static Object[] getSearchString(){

		return new Object[]{
				new Object[]{""},
				new Object[]{"eventURL"},
		};
	}
	@ParameterizedTest
	@MethodSource("getSearchString")
	void test_getSortablePage(String searchString){

		//setup
		int page = 1;
		int size = 10;
		String sortField = "";
		String sortDirection = Sort.Direction.ASC.name();

		Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "created_date");

		//Execution
		Pageable pageableData = eventServiceImpl.getSortablePage(page, size, sortField, sortDirection);

		//Assertion
		assertEquals(pageableData, pageable);
	}

    @ParameterizedTest
	@MethodSource("getSearchString")
	void test_getSortablePageWithDefaultSortField(String searchString){

		//setup
		int page = 1;
		int size = 10;
		String sortField = "eventAddress";
		String sortDirection = Sort.Direction.DESC.name();

		Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;

		Pageable pageable = PageRequest.of(page, size, direction, "created_date");

		//Execution
		Pageable pageableData = eventServiceImpl.getSortablePage(page, size, sortField, sortDirection);

		//Assertion
		assertEquals(pageableData, pageable);
	}

	@ParameterizedTest
	@MethodSource("getSearchString")
	void test_getSortablePageWithEventUrlSortField(String searchString){

		//setup
		int page = 1;
		int size = 10;
		String sortField = "eventURL";
		String sortDirection = Sort.Direction.DESC.name();

		Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;

		Pageable pageable = PageRequest.of(page, size, direction, "eventurl");

		//Execution
		Pageable pageableData = eventServiceImpl.getSortablePage(page, size, sortField, sortDirection);

		//Assertion
		assertEquals(pageableData, pageable);
	}

	public static Object[] getModuleEnables(){
		return new Object[]{
				new Object[]{false, false, false, true, true},
				new Object[]{true, true, true, false, false},
				new Object[]{false, true, false, true, false},
				new Object[]{false, false, true, true, false},
				new Object[]{false, false, false, false, false},
		};
	}
	@ParameterizedTest
	@MethodSource("getModuleEnables")
	void isTicketingTheOnlyEnabledModule(boolean auctionEnable, boolean raffleEnable, boolean causeAuctionEnable, boolean ticketingEnable, boolean enabled){

		//setup
		event.setSilentAuctionEnabled(auctionEnable);
		event.setRaffleEnabled(raffleEnable);
		event.setCauseAuctionEnabled(causeAuctionEnable);
		event.setTicketingEnabled(ticketingEnable);

		//Execution
		boolean moduleActivated = eventServiceImpl.isTicketingTheOnlyEnabledModule(event);

		//Assertion
		assertEquals(moduleActivated, enabled);
	}

	@Test
	void test_getEventEndDate() {

		//setup
		Date eventTicketingEndDate = new Date("2019/12/01 08:30:00");
		Date eventAuctionEndDate = new Date("2018/09/09 09:31:00");
		Date eventCauseAuctionEndDate = new Date("2018/10/11 15:44:00");
		Date eventRaffleEndDate = new Date("2019/07/15 04:11:03");

		Object[] ticketingDates = {eventTicketingEndDate, ModuleType.TICKETING.name()};
		Object[] auctionDates = {eventAuctionEndDate, ModuleType.AUCTION.name()};
		Object[] causeAuctionDates = {eventCauseAuctionEndDate, ModuleType.CAUSEAUCTION.name()};
		Object[] raffleDates = {eventRaffleEndDate, ModuleType.RAFFLE.name()};

		List<Object[]> moduleEndDates = new ArrayList<>();
		moduleEndDates.add(ticketingDates);
		moduleEndDates.add(auctionDates);
		moduleEndDates.add(causeAuctionDates);
		moduleEndDates.add(raffleDates);

		event.setTicketingEnabled(true);
		event.setSilentAuctionEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setRaffleEnabled(true);

		List<Date> eventModuleDates = new ArrayList<>();
		eventModuleDates.add(eventTicketingEndDate);
		eventModuleDates.add(eventAuctionEndDate);
		eventModuleDates.add(eventCauseAuctionEndDate);
		eventModuleDates.add(eventRaffleEndDate);

		//mock
		when(eventRepository.getEventEndDate(anyLong(), anyBoolean())).thenReturn(moduleEndDates);
		Mockito.doReturn(true).when(eventServiceImpl).isTicketingTheOnlyEnabledModule(any());

		//Execution
		Date date = eventServiceImpl.getEventEndDate(event);

		//Assertion
		assertEquals(date , Collections.max(eventModuleDates));

		verify(eventRepository, times(1)).getEventEndDate(anyLong(), anyBoolean());
	}

	@Test
	void test_getEventEndDate_withAllModuleDisableInEvent() {

		//setup
		Date eventAuctionEndDate = new Date("2018/09/09 09:31:00");
		Date eventCauseAuctionEndDate = new Date("2018/10/11 15:44:00");
		Date eventRaffleEndDate = new Date("2019/07/15 04:11:03");

		Object[] auctionDates = {eventAuctionEndDate, ModuleType.AUCTION.name()};
		Object[] causeAuctionDates = {eventCauseAuctionEndDate, ModuleType.CAUSEAUCTION.name()};
		Object[] raffleDates = {eventRaffleEndDate, ModuleType.RAFFLE.name()};

		List<Object[]> moduleEndDates = new ArrayList<>();
		moduleEndDates.add(auctionDates);
		moduleEndDates.add(causeAuctionDates);
		moduleEndDates.add(raffleDates);

		event.setTicketingEnabled(false);
		event.setSilentAuctionEnabled(false);
		event.setCauseAuctionEnabled(false);
		event.setRaffleEnabled(false);

		List<Date> eventModuleDates = new ArrayList<>();
		eventModuleDates.add(eventAuctionEndDate);
		eventModuleDates.add(eventCauseAuctionEndDate);
		eventModuleDates.add(eventRaffleEndDate);

		//mock
		when(eventRepository.getEventEndDate(anyLong(), anyBoolean())).thenReturn(moduleEndDates);

		//Execution
		Date date = eventServiceImpl.getEventEndDate(event);

		//Assertion
		assertEquals(date , Collections.max(eventModuleDates));

		verify(eventRepository, times(1)).getEventEndDate(anyLong(), anyBoolean());
	}

	@Test
	void test_addToList(){

		//setup
		List<Date> eventModuleDates = new ArrayList<>();
		Map<String, Date> moduleEndDateMap = new HashMap<>();

		//Execution
		eventServiceImpl.addToList(eventModuleDates, ModuleType.AUCTION, moduleEndDateMap);

		//Assertion
		assertTrue(eventModuleDates.isEmpty());
	}

	@Test
	void test_findOldEventsForCurrentUser() {

		//setup
		Long moduleId = 1L;
		event.setAuctionId(2L);
		staff.setEvent(event);

		Event event1 = new Event();
		event1.setAuctionId(1L);
		Staff staff1 = new Staff();
		staff1.setEvent(event1);

		Staff staff2 = new Staff();
		staff2.setEvent(null);

		List<Staff> staffList = new ArrayList<>();
		staffList.add(staff);
		staffList.add(staff1);
		staffList.add(staff2);

		//mock
		when(staffService.findByUserAndEventIsNotNull(user)).thenReturn(staffList);


		//Execution
		Set<Event> oldEvents = eventServiceImpl.findOldEventsForCurrentUser(moduleId, user);

		//Assertion
		assertEquals(oldEvents.iterator().next().getTimezoneId(), event.getTimezoneId());
		assertEquals(oldEvents.iterator().next().getEquivalentTimeZone(), event.getEquivalentTimeZone());
		assertEquals(oldEvents.iterator().next().getEventId(), event.getEventId());

		verify(staffService, times(1)).findByUserAndEventIsNotNull(user);
	}

	@Test
	void test_findOldEventsForCurrentUser_withStaffListEmpty() {

		//setup
		Long moduleId = 1L;

		List<Staff> staffList = new ArrayList<>();

		//mock
		when(staffService.findByUserAndEventIsNotNull(user)).thenReturn(staffList);

		//Execution
		Set<Event> oldEvents = eventServiceImpl.findOldEventsForCurrentUser(moduleId, user);

		//Assertion
		assertTrue(oldEvents.isEmpty());

		verify(staffService, times(1)).findByUserAndEventIsNotNull(user);
	}

	@Test
	void test_findOldEventDtosForCurrentUser() {

		//setup
		Long moduleId = 1L;
		event.setAuctionId(2L);
		event.setTicketingId(id);
		event.setFundRaisingGoal(100);
		event.setGoalStartingAmount(10);
		event.setPhoneNumber(phoneNumber);
		event.setTaxId("taxId");
		event.setCountryCode(CountryCode.US);
		event.setEventStatus(EventStatus.EVENT_HAPPENING);
		event.setAnalyticsId("analyticId");
		event.setTrackinPixelId("trackinPixelId");
		staff.setEvent(event);
        staff.setRole(StaffRole.admin);

		Event event1 = new Event();
		event1.setAuctionId(1L);
		Staff staff1 = new Staff();
		staff1.setEvent(event1);
		staff1.setRole(StaffRole.admin);

		Staff staff2 = new Staff();
		staff2.setEvent(null);
        staff2.setRole(StaffRole.admin);

		List<Staff> staffList = new ArrayList<>();
		staffList.add(staff);
		staffList.add(staff1);
		staffList.add(staff2);

		//mock
		when(staffService.findByUserAndEventIsNotNull(user)).thenReturn(staffList);

		//Execution
		Set<EventDto> oldEvents = eventServiceImpl.findOldEventDtosForCurrentUser(moduleId, user);

		//Assertion
		assertEquals(oldEvents.iterator().next().getTimezoneId(), event.getTimezoneId());
		assertEquals(oldEvents.iterator().next().getEquivalentTimezone(),event.getEquivalentTimeZone());
		assertEquals(oldEvents.iterator().next().getEventId(), event.getEventId());
		assertEquals(oldEvents.iterator().next().getFundRaisingGoal(), event.getFundRaisingGoal());
		assertEquals(oldEvents.iterator().next().getPhoneNumber(), event.getPhoneNumber());
		assertEquals(oldEvents.iterator().next().getEventStatus(), event.getEventStatus());
		assertEquals(oldEvents.iterator().next().getTicketingId(), event.getTicketingId());

		verify(staffService, times(1)).findByUserAndEventIsNotNull(user);
	}

	@Test
	void test_findOldEventDtosForCurrentUser_withStaffListEmpty() {
		//setup
		Long moduleId = 1L;

		List<Staff> staffList = new ArrayList<>();

		//mock
		when(staffService.findByUserAndEventIsNotNull(user)).thenReturn(staffList);

		//Execution
		Set<EventDto> oldEvents = eventServiceImpl.findOldEventDtosForCurrentUser(moduleId, user);

		//Assertion
		assertTrue(oldEvents.isEmpty());

		verify(staffService, times(1)).findByUserAndEventIsNotNull(user);
	}

	@Test
	void test_getStripeKey() {

		//setup
		stripe.setStripePublishableKey("pk_test_x9BXBdwFPlxaTKaWz1iv8Jzz");

		getJoinEventWithDealProductData();

		//mock

		when(roEventService.getEventByURL(url)).thenReturn(event);
		when(roStripeService.findByEvent(event)).thenReturn(stripe);

		//Execution
		String stripeKey = eventServiceImpl.getStripeKey(url);

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(roEventService, times(1)).getEventByURL(url);
		assertEquals(stripeKey, stripe.getStripePublishableKey());
	}

	@Test
	void test_getStripeKey_throwExceptionSTRIPE_NOT_FOUND() {

		//setup
		getJoinEventWithDealProductData();

		//mock

		when(roEventService.getEventByURL(url)).thenReturn(event);
		when(roStripeService.findByEvent(event)).thenReturn(null);

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.getStripeKey(url));

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(roEventService, times(1)).getEventByURL(url);
        assertEquals(NotFoundException.NotFound.STRIPE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_GetStripeKey_withEvent_throwExceptionSTRIPE_NOT_FOUND() {

		//setup
		getJoinEventWithDealProductData();

		//mock

		when(roStripeService.findByEvent(event)).thenReturn(null);

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.getStripeKey(event)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(roStripeService, times(1)).findByEvent(event);
        assertEquals(NotFoundException.NotFound.STRIPE_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_GetStripeKey_withEvent() {

		//setup
		stripe.setStripePublishableKey("pk_test_x9BXBdwFPlxaTKaWz1iv8Jzz");

		//mock
		when(roStripeService.findByEvent(event)).thenReturn(stripe);

		//Execution
		String stripeKey = eventServiceImpl.getStripeKey(event);

		//Assertion
		assertEquals(stripeKey, stripe.getStripePublishableKey());

		verify(roStripeService, times(1)).findByEvent(event);
	}

	@Test
	void test_getLinkedCreditCardFromGateway_withStripeAndpayment() throws StripeException, ApiException {

		//setup
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		payment = new Payment();
		payment.setStripeToken(stripe.getAccessToken());
		stripeCreditCardDto = new StripeCreditCardDto();
		stripeCreditCardDto.setId("1");

		//mock
		when(allPaymentService.getAllCardsForCustomer(anyString(), anyInt(), any(), any(), any())).thenReturn(Collections.singletonList(stripeCreditCardDto));

		//Execution
		List<StripeCreditCardDto> stripeCreditCardDtoData = eventServiceImpl.getLinkedCreditCardFromGateway(event, user, stripe, Optional.of(payment));

		//Assertion
		assertEquals(stripeCreditCardDtoData.get(0).getId(), stripeCreditCardDto.getId());

		verify(allPaymentService, times(1)).getAllCardsForCustomer(anyString(), anyInt(), any(), any(), any());
	}

	@Test
	void test_getLinkedCreditCardFromGateway_withStripeAndpaymentEmpty() throws StripeException, ApiException {

		//setup
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");

		//Execution
		List<StripeCreditCardDto> stripeCreditCardDtoData = eventServiceImpl.getLinkedCreditCardFromGateway(event, user, stripe, Optional.empty());

		//Assertion
		assertTrue(stripeCreditCardDtoData.isEmpty());
	}

	@Test
	void test_getLinkedCreditCardFromGateway_withStripeNullAndpayment() throws StripeException, ApiException {

		//setup
		payment = new Payment();
		payment.setStripeToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");

		//Execution
		List<StripeCreditCardDto> stripeCreditCardDtoData = eventServiceImpl.getLinkedCreditCardFromGateway(event, user, null, Optional.of(payment));

		//Assertion
		assertTrue(stripeCreditCardDtoData.isEmpty());
	}

	public static Object[] getStripeToken(){

		return new Object[]{

				new Object[]{"sk_test_FStPmAcubsrj3FP1hRS2Ge6w", ""},
				new Object[]{"", "sk_test_FStPmAcubsrj3FP1hRS2Ge6w"},
				new Object[]{"", ""},
		};
	}

	@ParameterizedTest
	@MethodSource("getStripeToken")
	void test_getLinkedCreditCardFromGateway_withStripeAndpayment(String customerid, String apiKey) throws StripeException, ApiException {

		//setup
		stripe.setAccessToken(apiKey);
		payment = new Payment();
		payment.setStripeToken(customerid);

		//Execution
		List<StripeCreditCardDto> stripeCreditCardDtoData = eventServiceImpl.getLinkedCreditCardFromGateway(event, user, stripe, Optional.of(payment));

		//Assertion
		assertTrue(stripeCreditCardDtoData.isEmpty());
	}

	@Test
	void test_getLinkedCreditCard_withStripeAndPaymentAndStripeCreditCardDtoListEmpty() {

		//setup
		payment = new Payment();
		List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();

		//mock
		when(roStripeService.findByEvent(event)).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
		when(allPaymentService.getAllCardsForCustomer(user, event, 30, stripe)).thenReturn(stripeCreditCardDtoList);

		//Execution
		List<StripeCreditCardDto> linkedCreditCardData = eventServiceImpl.getLinkedCreditCard(event, user);

		//Assertion
		assertTrue(linkedCreditCardData.isEmpty());

		verify(roStripeService, times(1)).findByEvent(event);
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
		verify(allPaymentService, times(1)).getAllCardsForCustomer(user, event, 30, stripe);
	}

	@Test
	void test_getLinkedCreditCard_withStripeAndPaymentAndStripeCreditCardDtoListNull() {

		//setup
		payment = new Payment();
		stripeCreditCardDto = new StripeCreditCardDto();
        stripeCreditCardDto.setDefaultCard(false);
		List<StripeCreditCardDto> stripeCreditCardDtos = new ArrayList<>();
		stripeCreditCardDtos.add(stripeCreditCardDto);


		//mock
		when(roStripeService.findByEvent(event)).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
		when(allPaymentService.getAllCardsForCustomer(user, event, 30, stripe)).thenReturn(stripeCreditCardDtos);

		//Execution
		List<StripeCreditCardDto> linkedCreditCardData = eventServiceImpl.getLinkedCreditCard(event, user);

		//Assertion
		assertTrue(linkedCreditCardData.isEmpty());

		verify(roStripeService, times(1)).findByEvent(event);
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
		verify(allPaymentService, times(1)).getAllCardsForCustomer(user, event, 30, stripe);
	}

	@Test
	void test_getLinkedCreditCard_withStripeNullAndPayment() {

		//setup
		payment = new Payment();

		//mock
		when(roStripeService.findByEvent(event)).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		List<StripeCreditCardDto> linkedCreditCardData = eventServiceImpl.getLinkedCreditCard(event, user);

		//Assertion
		assertTrue(linkedCreditCardData.isEmpty());

		verify(roStripeService, times(1)).findByEvent(event);
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}

	@Test
	void test_getLinkedCreditCard_withStripeNullAndPaymentEmpty() {

		//mock
		when(roStripeService.findByEvent(event)).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		List<StripeCreditCardDto> linkedCreditCardData = eventServiceImpl.getLinkedCreditCard(event, user);

		//Assertion
		assertTrue(linkedCreditCardData.isEmpty());

		verify(roStripeService, times(1)).findByEvent(event);
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}



	@Test
	void test_getLikedCard() {

		//setup
		stripeCreditCardDto = new StripeCreditCardDto();
		List<Long> eventIdList = new ArrayList<>();
		eventIdList.add(1L);
		eventIdList.add(2L);

		stripeCreditCardDto.setCardType("VISA");
		List<StripeCreditCardDto> stripeCreditCardDtoList = new ArrayList<>();
		stripeCreditCardDtoList.add(stripeCreditCardDto);

		getJoinEventWithDealProductData();

		//mock

		when(paymentService.findDistinctEventIdByUserId(anyLong())).thenReturn(eventIdList);
        when(roEventService.getEventByIdIfPresentOrNull(1L)).thenReturn(event);
		doReturn(stripeCreditCardDtoList).when(eventServiceImpl).getLinkedCreditCard(any(), any());

		//Execution
		List<EventLinkedCard> linkedCardData = eventServiceImpl.getLikedCard(user);

		//Assertion
		assertEquals(linkedCardData.get(0).getEventUrl(), event.getEventURL());
		assertEquals(linkedCardData.get(0).getStripeCards().iterator().next().getCardType(), stripeCreditCardDtoList.iterator().next().getCardType());

		verify(paymentService, times(1)).findDistinctEventIdByUserId(anyLong());
		verify(paymentService, times(1)).findDistinctEventIdByUserId(anyLong());
	}

	@Test
	void test_getLikedCard_withEventIdListNull() {

		//mock
		when(paymentService.findDistinctEventIdByUserId(anyLong())).thenReturn(null);

		//Execution
		List<EventLinkedCard> linkedCardData = eventServiceImpl.getLikedCard(user);

		//Assertion
		assertTrue(linkedCardData.isEmpty());
	}

	//@Test
	void test_getEventEndInfoForUser_withSourceMOBILE_APP_AndSearchString() {

		//setup
		String source = MOBILE_APP;
		String searchString = "search";
		String sequence = "1";
		Date searchDate = DateUtils.getCurrentDate();

		Object[] events =  {event.getEventId(), event.getName(), event.getEventURL(), ONLINE, sequence, "eventLogo", "Ahmedabad", EventType.MUSIC.name()};
		Object[] events1 =  {event.getEventId(), event.getName(), event.getEventURL(), ONLINE, sequence, "eventLogo", "Mumbai", EventType.SPORTS_AND_FITNESS.name()};

		List<Object[]> eventList = new ArrayList<>();
		eventList.add(events);
		eventList.add(events1);

		Object[] endDates =  {1,new Date("2018/10/11 15:44:00")};
		List<Object[]> endDateList = new ArrayList<>();
		endDateList.add(endDates);

		Object[] startDates =  {1, new Date("2017/10/11 15:44:00")};
		List<Object[]> startDateList = new ArrayList<>();
		startDateList.add(startDates);

		Object[] soldCount =  {1L,10L};
		List<Object[]> soldCountList = new ArrayList<>();
		soldCountList.add(soldCount);

		Object[] numberOfTotalTickets =  {1L,100L};
		List<Object[]> numberOfTotalTicketList = new ArrayList<>();
		numberOfTotalTicketList.add(numberOfTotalTickets);

		//mock
		when(eventRepository.findAllEventsForUserWithStaffOrAdmin(anyLong(), anyString(), any())).thenReturn(eventList);
		when(eventRepository.findEventsEndDate(anySet())).thenReturn(endDateList);
		when(eventRepository.findEventsStartDate(anySet())).thenReturn(startDateList);
		when(ticketingStatisticsService.getTicketsSoldCount(anySet())).thenReturn(soldCountList);
		when(ticketingTypeTicketService.getNumberOfTotalTickets(anySet())).thenReturn(numberOfTotalTicketList);
		when(stripeService.getByEventId(anyLong())).thenReturn(stripe);

		//Execution
		Set<EventEndInfoDto> eventEndInfoForUserData = eventServiceImpl.getEventEndInfoForUser(user, source, searchString, searchDate, null);

		//Assertion
		assertEquals(eventEndInfoForUserData.iterator().next().getName(), events[1]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventURL(), events[2]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventType(), events[3]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventLogo(), events[5]);
		assertEquals(eventEndInfoForUserData.iterator().next().getAddress(), events[6]);
		assertEquals(eventEndInfoForUserData.iterator().next().getType(), events[7]);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTicketSold().longValue(), soldCount[1]);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTotalTickets().longValue(), numberOfTotalTickets[1]);

		verify(eventRepository, times(1)).findAllEventsForUserWithStaffOrAdmin(anyLong(), anyString(), any());
		verify(eventRepository, times(1)).findEventsEndDate(anySet());
		verify(eventRepository, times(1)).findEventsStartDate(anySet());
		verify(ticketingStatisticsService, times(1)).getTicketsSoldCount(anySet());
		verify(ticketingTypeTicketService, times(1)).getNumberOfTotalTickets(anySet());
		verify(stripeService, times(1)).getByEventId(anyLong());
	}

	//@Test
	void test_getEventEndInfoForUser_withSourceMOBILE_APP_AndSearchStringEmptyAndStripeNull() {

		//setup
		String source = MOBILE_APP;
		String searchString = "";
		String sequence = "1";
		Date searchDate = DateUtils.getCurrentDate();

		Object[] events =  {event.getEventId(), event.getName(), event.getEventURL(), ONLINE, sequence, "eventLogo", "Ahmedabad", EventType.MUSIC.name()};

		List<Object[]> eventList = new ArrayList<>();
		eventList.add(events);

		Object[] endDates =  {1,new Date("2018/10/11 15:44:00")};
		List<Object[]> endDateList = new ArrayList<>();
		endDateList.add(endDates);

		Object[] startDates =  {1, new Date("2017/10/11 15:44:00")};
		List<Object[]> startDateList = new ArrayList<>();
		startDateList.add(startDates);

		Object[] soldCount =  {1L,10L};
		List<Object[]> soldCountList = new ArrayList<>();
		soldCountList.add(soldCount);

		Object[] numberOfTotalTickets =  {1L,100L};
		List<Object[]> numberOfTotalTicketList = new ArrayList<>();
		numberOfTotalTicketList.add(numberOfTotalTickets);

		//mock
		when(eventRepository.findAllEventsForUserWithStaffOrAdmin(anyLong(), any())).thenReturn(eventList);
		when(eventRepository.findEventsEndDate(anySet())).thenReturn(endDateList);
		when(eventRepository.findEventsStartDate(anySet())).thenReturn(startDateList);
		when(ticketingStatisticsService.getTicketsSoldCount(anySet())).thenReturn(soldCountList);
		when(ticketingTypeTicketService.getNumberOfTotalTickets(anySet())).thenReturn(numberOfTotalTicketList);
		when(stripeService.getByEventId(anyLong())).thenReturn(null);

		//Execution
		Set<EventEndInfoDto> eventEndInfoForUserData = eventServiceImpl.getEventEndInfoForUser(user, source, searchString, searchDate, null);

		//Assertion
		assertEquals(eventEndInfoForUserData.iterator().next().getName(), events[1]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventURL(), events[2]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventType(), events[3]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventLogo(), events[5]);
		assertEquals(eventEndInfoForUserData.iterator().next().getAddress(), events[6]);
		assertEquals(eventEndInfoForUserData.iterator().next().getType(), events[7]);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTicketSold().longValue(), soldCount[1]);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTotalTickets().longValue(), numberOfTotalTickets[1]);

		verify(eventRepository, times(1)).findAllEventsForUserWithStaffOrAdmin(anyLong(), any());
		verify(eventRepository, times(1)).findEventsEndDate(anySet());
		verify(eventRepository, times(1)).findEventsStartDate(anySet());
		verify(ticketingStatisticsService, times(1)).getTicketsSoldCount(anySet());
		verify(ticketingTypeTicketService, times(1)).getNumberOfTotalTickets(anySet());
		verify(stripeService, times(1)).getByEventId(anyLong());
	}

	//@Test
	void test_getEventEndInfoForUser_withSourceWEBSITEAndStripe() {

		//setup
		stripe.setStripePublishableKey("pk_test_x9BXBdwFPlxaTKaWz1iv8Jzz");
		String source = "WEBSITE";
		String searchString = "";
		String sequence = "1";
		Date searchDate = DateUtils.getCurrentDate();

		Object[] events =  {event.getEventId(), event.getName(), event.getEventURL(), ONLINE, sequence, "eventLogo", "Ahmedabad", EventType.MUSIC.name()};

		List<Object[]> eventList = new ArrayList<>();
		eventList.add(events);

		Object[] endDates =  {2,new Date("2018/10/11 15:44:00")};
		List<Object[]> endDateList = new ArrayList<>();
		endDateList.add(endDates);

		Object[] startDates =  {2, new Date("2017/10/11 15:44:00")};
		List<Object[]> startDateList = new ArrayList<>();
		startDateList.add(startDates);

		Object[] soldCount =  {2L,10L};
		List<Object[]> soldCountList = new ArrayList<>();
		soldCountList.add(soldCount);

		Object[] numberOfTotalTickets =  {2L,100L};
		List<Object[]> numberOfTotalTicketList = new ArrayList<>();
		numberOfTotalTicketList.add(numberOfTotalTickets);

		//mock
		when(eventRepository.findAllEventsForUser(anyLong(), anyString())).thenReturn(eventList);
		when(eventRepository.findEventsEndDate(anySet())).thenReturn(endDateList);
		when(eventRepository.findEventsStartDate(anySet())).thenReturn(startDateList);
		when(ticketingStatisticsService.getTicketsSoldCount(anySet())).thenReturn(soldCountList);
		when(ticketingTypeTicketService.getNumberOfTotalTickets(anySet())).thenReturn(numberOfTotalTicketList);
		when(stripeService.getByEventId(anyLong())).thenReturn(stripe);

		//Execution
		Set<EventEndInfoDto> eventEndInfoForUserData = eventServiceImpl.getEventEndInfoForUser(user, source, searchString, searchDate, null);

		//Assertion
		assertEquals(eventEndInfoForUserData.iterator().next().getName(), events[1]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventURL(), events[2]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventType(), events[3]);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventLogo(), events[5]);
		assertEquals(eventEndInfoForUserData.iterator().next().getAddress(), events[6]);
		assertEquals(eventEndInfoForUserData.iterator().next().getType(), events[7]);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTicketSold().longValue(), 0);
		assertEquals(eventEndInfoForUserData.iterator().next().getNumberOfTotalTickets().longValue(), 0);
		assertEquals(eventEndInfoForUserData.iterator().next().getEventStripePublishableKey(), stripe.getStripePublishableKey());

		verify(eventRepository, times(1)).findEventsEndDate(anySet());
		verify(eventRepository, times(1)).findEventsStartDate(anySet());
		verify(ticketingStatisticsService, times(1)).getTicketsSoldCount(anySet());
		verify(ticketingTypeTicketService, times(1)).getNumberOfTotalTickets(anySet());
		verify(stripeService, times(1)).getByEventId(anyLong());
	}

	@Test
	void test_getAllEventsForUser() {

		//setup
		String sequence = "1";

		Object[] events =  {event.getEventId(), event.getName(), event.getEventURL(), ONLINE, sequence, "eventLogo"};

		List<Object[]> eventList = new ArrayList<>();
		eventList.add(events);

		//mock
		when(eventRepository.findAllEventsForUser(anyLong(), anyString())).thenReturn(eventList);

		//Execution
		List<Object[]> eventData = eventServiceImpl.getAllEventsForUser(user, STRING_EMPTY);

		//Assertion
		verify(eventRepository, times(1)).findAllEventsForUser(anyLong(), anyString());

		assertFalse(eventData.isEmpty());
	}

	@Test
	void test_getFavDirectoryName_withEventAndWhiteLabelNull() {

		//Execution
		String favDirectoryName = eventServiceImpl.getFavDirectoryName(event);

		//Assertion
		assertNull(favDirectoryName);
	}

	@Test
	void test_getFavDirectoryName_withEventAndWhiteLabel() {

		//setup
		whiteLabel.setFaviconDirectory("FaviconDirectory");
		event.setWhiteLabel(whiteLabel);

		//Execution
		String favDirectoryName = eventServiceImpl.getFavDirectoryName(event);

		//Assertion
		assertEquals(favDirectoryName, event.getWhiteLabel().getFaviconDirectory());
	}

	@Test
	void test_getFavDirectoryName_withEventNull() {

		//Execution
		String favDirectoryName = eventServiceImpl.getFavDirectoryName(null);

		//Assertion
		assertNull(favDirectoryName);
	}

	@Test
	void test_getEventEndDateMap_withEventIdSetEmpty() {

		//setup
		Set<Long> eventIds = new HashSet<>();

		//Execution
		Map<Long, Date> eventEndDateMap = eventServiceImpl.getEventEndDateMap(eventIds);

		//Assertion
		assertTrue(eventEndDateMap.isEmpty());
	}

	@Test
	void test_getEventStartDateMap_withEventIdSetEmpty() {

		//setup
		Set<Long> eventIds = new HashSet<>();

		//Execution
		Map<Long, Date> eventEndDateMap = eventServiceImpl.getEventStartDateMap(eventIds);

		//Assertion
		assertTrue(eventEndDateMap.isEmpty());
	}

	@Test
	void test_addUserCard() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException);

		//Assertion
		verify(allPaymentService, times(1)).addCardToCustomer(anyString(), any(), any(), any(), any());
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}

	@Test
	void test_addUserCard_throwException() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
		Mockito.doThrow(com.squareup.square.exceptions.ApiException.class).when(allPaymentService).addCardToCustomer(anyString(), any(), any(), any(), any());

		//Execution
		assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(allPaymentService, times(1)).addCardToCustomer(anyString(), any(), any(), any(), any());
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_STRIPE_CUSTOMER_withAccessTokenNullAndPaymentStripeTokenNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken("");
		stripe.setAccessToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_STRIPE_CUSTOMER_withAccessTokenNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_STRIPE_CUSTOMER_withPaymentStripeTokenNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken("");
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception  = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_PAYMENT_DETAIL_withStripeNullAndPaymentNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_PAYMENT_DETAIL_withStripeNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		payment = new Payment();
		payment.setStripeToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_addUserCard_throw_NotFoundException_PAYMENT_DETAIL_WithPaymentNull()  {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		boolean isDefault = true;
		boolean absorbCardFoundException = true;

		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.addUserCard(event, user, tokenOrCardNonce, isDefault, absorbCardFoundException)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard() {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		eventServiceImpl.setDefaultCard(event, user, cardId);

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}

	@Test
	void test_setDefaultCard_throwException() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
		Mockito.doThrow(com.squareup.square.exceptions.ApiException.class).when(allPaymentService).updateDefaultCard(anyString(), anyString(), anyString(), any(), any(), any());

		//Execution
		assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_STRIPE_CUSTOMER_withAccessTokenNullAndPaymentStripeTokenNull() {

		//setup
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken("");
		stripe.setAccessToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_STRIPE_CUSTOMER_withAccessTokenNull() {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken(tokenOrCardNonce);
		stripe.setAccessToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_STRIPE_CUSTOMER_withPaymentStripeTokenNull() {

		//setup
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken("");
		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.STRIPE_CUSTOMER.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_PAYMENT_DETAIL_withStripeNullAndPaymentNull() {

		//setup
		String cardId = "acct_19KYL3I9f3YkXHM0";

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_PAYMENT_DETAIL_withStripeNull() {

		//setup
		String cardId = "acct_19KYL3I9f3YkXHM0";

		payment = new Payment();
		payment.setStripeToken("");

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_setDefaultCard_throw_NotFoundException_PAYMENT_DETAIL_withPaymentNull() {

		//setup
		String cardId = "acct_19KYL3I9f3YkXHM0";
		String tokenOrCardNonce = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";

		stripe.setAccessToken(tokenOrCardNonce);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.setDefaultCard(event, user, cardId)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
        assertEquals(NotFoundException.NotFound.PAYMENT_DETAIL.getDeveloperMessage(), exception.getMessage());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges_withEventCreditCardEnablTrue() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(2, false, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(0L);
//		event.setCreditCardEnabled(true);
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//        String languagecode = "EN";
//
//		boolean isStaffBid = true;
//		String paymentType = CC;
//		Double bidIncreament = 1d;
//		Date lastExtendedEndTime = new Date();
//		String smsText = "sms";
//		double paidAmount = 100d;
//		String userMessageText = THANK_YOU_FOR_YOUR_PURCHASE;
//		double stripeFee = CalculateFees.getStripeFee(paidAmount, auctionBid.getAmount(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(true);
//		when(auctionService.addExtendedBiddingTime(any(), any())).thenReturn(lastExtendedEndTime);
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		doNothing().when(joinPaymentItemService).save(any());
//		when(paymentHandlerService.getPaidAmountForAuctionAfterStripeTransaction(anyString(), any(), any(), any(), any(), anyInt(), any(), anyString(), any())).thenReturn(paidAmount);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//
//		//Execution
//		String auctionChargeData = eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser,false, languageMap);
//
//		//Assertion
//		assertEquals(auctionChargeData, userMessageText);
//
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(auctionBidService, times(1)).getLastBidderLesserThanBuyItNow(any());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(itemService ,times(1)).getBidIncrement(any(), any());
//		verify(itemService ,times(1)).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		verify(itemService, times(2)).update(any());
//		verify(auctionBidService, times(1)).findHighestBidItemForAuction(any(), anyLong());
//		verify(auctionService, times(1)).isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString());
//		verify(auctionService, times(1)).addExtendedBiddingTime(any(), any());
//
//		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
//		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
//
//		Auction auctionData = auctionArgumentCaptor.getValue();
//		assertEquals(auctionData.getAuctionStatus(), ModuleStatus.EXTENDED_BIDDING);
//		assertEquals(auctionData.getLastExtendedEndTime(), lastExtendedEndTime);
//
//		verify(roStripeService, times(2)).findByEvent(any());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(textMessageUtils, times(1)).getSuccessfulAuctionBidMessage(any(), any(), anyInt());
//		verify(auctionBidService, times(1)).save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any());
//		verify(joinPaymentItemService, times(1)).save(any());
//
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(2)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getUserId(), user.getUserId().longValue());
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(winnerService, times(2)).findByModuleTypeAndItemId(any(), anyLong());
//		verify(paymentHandlerService, times(1)).getPaidAmountForAuctionAfterStripeTransaction(anyString(), any(), any(), any(), any(), anyInt(), any(), anyString(), any());
//		verify(twilioTextMessagePrepareService, times(1)).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges_withEventCreditCardEnablFalse() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(1, false, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		user.setPhoneNumber(0L);
//
//		event.setCreditCardEnabled(false);
//
//		boolean isStaffBid = true;
//		String paymentType = CC;
//		Double bidIncreament = 1d;
//		Date lastExtendedEndTime = new Date();
//		String smsText = "sms";
//		String userMessageText = "You are submitting a Buy it Now bid. You will be contacted to collect payment.";
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//        String languagecode = "EN";
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(false);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());
//
//		//Execution
//		String auctionChargeData = eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, null,false, languageMap);
//
//		//Assertion
//		assertEquals(auctionChargeData, userMessageText);
//
//		verify(textMessageService, times(0)).sendText(any());
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(auctionBidService, times(1)).getLastBidderLesserThanBuyItNow(any());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(itemService ,times(1)).getBidIncrement(any(), any());
//		verify(itemService ,times(1)).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		verify(itemService, times(2)).update(any());
//		verify(auctionBidService, times(1)).findHighestBidItemForAuction(any(), anyLong());
//		verify(auctionService, times(1)).isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString());
//
//		verify(textMessageUtils, times(1)).getSuccessfulAuctionBidMessage(any(), any(), anyInt());
//		verify(auctionBidService, times(1)).save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any());
//
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertFalse(auctionBidData.isHasPaid());
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getUserId(), user.getUserId().longValue());
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(winnerService, times(1)).findByModuleTypeAndItemId(any(), anyLong());
//		verify(twilioTextMessagePrepareService, times(1)).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//	}

	@Test
	void test_auctionCharges_throwException() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		item = getItem(1, false, 100, "ItemCode");
		auction = new Auction();
		auction.setId(id);
		auctionPurchaseDto = getAuctionPurchaseDto();
		staffUser = new User();
		auctionBid = getAuctionBid(id, 100);
		payment = new Payment();
		stripe = getStripe("STRIPE", false);
		user.setPhoneNumber(0L);

		event.setCreditCardEnabled(false);
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
        String languagecode = "EN";

		boolean isStaffBid = true;
		String paymentType = CC;
		Double bidIncreament = 1d;
		Date lastExtendedEndTime = new Date();
		String smsText = "sms";
		double paidAmount = 100d;

		List<Winner> winnerList = new ArrayList<>();
		winnerList.add(winner);

		//mock
		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
		when(auctionService.findByEvent(event)).thenReturn(auction);
		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);

		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(item, auctionPurchaseDto.getAmount(), user);
		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
        when(roEventService.getLanguageCodeByUserOrEvent(any(User.class),any(Event.class))).thenReturn("EN");
		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(false);

		when(textMessageUtils.getSuccessfulAuctionBidMessage(event, item, auctionPurchaseDto.getAmount())).thenReturn(smsText);
		when(auctionBidService.save(any(),any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(event, item, user, auctionPurchaseDto.getAmount(), true);
		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());

		//Execution
		eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser,false, languageMap);
	}


    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_withEventCreditCardEnablTrue() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(2, false, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(9898989898L);
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//
//		event.setCreditCardEnabled(true);
//
//		boolean isStaffBid = true;
//		String paymentType = CC;
//		Double bidIncreament = 1d;
//		Date lastExtendedEndTime = new Date();
//		String smsText = "sms";
//		double paidAmount = 100d;
//		String userMessageText = THANK_YOU_FOR_YOUR_PURCHASE;
//		double stripeFee = CalculateFees.getStripeFee(paidAmount, auctionBid.getAmount(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(),anyString())).thenReturn(true);
//		when(auctionService.addExtendedBiddingTime(any(), any())).thenReturn(lastExtendedEndTime);
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		doNothing().when(joinPaymentItemService).save(any());
//		when(paymentHandlerService.getPaidAmountForAuctionAfterStripeTransaction(anyString(), any(), any(), any(), any(), anyInt(), any(), anyString(), any())).thenReturn(paidAmount);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//		doNothing().when(textMessageService).sendText(any());
//
//		//Execution
//		String auctionChargeData = eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//
//		//Assertion
//		assertEquals(auctionChargeData, userMessageText);
//
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(auctionBidService, times(1)).getLastBidderLesserThanBuyItNow(any());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(itemService ,times(1)).getBidIncrement(any(), any());
//		verify(itemService ,times(1)).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		verify(itemService, times(2)).update(any());
//		verify(auctionBidService, times(1)).findHighestBidItemForAuction(any(), anyLong());
//		verify(auctionService, times(1)).isApplicableForExtend(any(), any(), anyString(), any(), anyMap(),anyString());
//		verify(auctionService, times(1)).addExtendedBiddingTime(any(), any());
//
//		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
//		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
//
//		Auction auctionData = auctionArgumentCaptor.getValue();
//		assertEquals(auctionData.getAuctionStatus(), ModuleStatus.EXTENDED_BIDDING);
//		assertEquals(auctionData.getLastExtendedEndTime(), lastExtendedEndTime);
//
//		verify(roStripeService, times(2)).findByEvent(any());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(textMessageUtils, times(1)).getSuccessfulAuctionBidMessage(any(), any(), anyInt());
//		verify(auctionBidService, times(1)).save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any());
//		verify(joinPaymentItemService, times(1)).save(any());
//
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(2)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getUserId(), user.getUserId().longValue());
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(winnerService, times(2)).findByModuleTypeAndItemId(any(), anyLong());
//		verify(paymentHandlerService, times(1)).getPaidAmountForAuctionAfterStripeTransaction(anyString(), any(), any(), any(), any(), anyInt(), any(), anyString(), any());
//		verify(twilioTextMessagePrepareService, times(1)).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//		verify(textMessageService, times(1)).sendText(any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_withEventCreditCardEnablTrue1() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(2, false, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(9898989898L);
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//
//		event.setCreditCardEnabled(true);
//
//		boolean isStaffBid = true;
//		String paymentType = CASH;
//		Double bidIncreament = 1d;
//		Date lastExtendedEndTime = new Date();
//		String smsText = "sms";
//		double paidAmount = 100d;
//		String userMessageText = THANK_YOU_FOR_YOUR_PURCHASE;
//		double stripeFee = CalculateFees.getStripeFee(paidAmount, auctionBid.getAmount(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(true);
//		when(auctionService.addExtendedBiddingTime(any(), any())).thenReturn(lastExtendedEndTime);
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		doNothing().when(joinPaymentItemService).save(any());
//		when(paymentHandlerService.getPaidAmountForAuctionAfterStripeTransaction(anyString(), any(), any(), any(), any(), anyInt(), any(), anyString(), any())).thenReturn(paidAmount);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//		doNothing().when(textMessageService).sendText(any());
//		Mockito.doReturn(payment).when(eventServiceImpl).handlePayment(any(), any(), any(), anyString(), any(), any(), any(), anyInt(), any());
//
//		//Execution
//		String auctionChargeData = eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//
//		//Assertion
//		assertEquals(auctionChargeData, userMessageText);
//
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(auctionBidService, times(1)).getLastBidderLesserThanBuyItNow(any());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(itemService ,times(1)).getBidIncrement(any(), any());
//		verify(itemService ,times(1)).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		verify(itemService, times(2)).update(any());
//		verify(auctionBidService, times(1)).findHighestBidItemForAuction(any(), anyLong());
//		verify(auctionService, times(1)).isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString());
//		verify(auctionService, times(1)).addExtendedBiddingTime(any(), any());
//
//		ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
//		verify(auctionService, times(1)).save(auctionArgumentCaptor.capture());
//
//		Auction auctionData = auctionArgumentCaptor.getValue();
//		assertEquals(auctionData.getAuctionStatus(), ModuleStatus.EXTENDED_BIDDING);
//		assertEquals(auctionData.getLastExtendedEndTime(), lastExtendedEndTime);
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(textMessageUtils, times(1)).getSuccessfulAuctionBidMessage(any(), any(), anyInt());
//		verify(auctionBidService, times(1)).save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any());
//		verify(joinPaymentItemService, times(1)).save(any());
//
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertFalse(auctionBidData.getStripePaidAmount() == paidAmount);
//		assertFalse(auctionBidData.getStripeFee() == stripeFee);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getUserId(), user.getUserId().longValue());
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(winnerService, times(2)).findByModuleTypeAndItemId(any(), anyLong());
//		verify(twilioTextMessagePrepareService, times(1)).sendOutBidNotificationForExtendedWaitTime(any(), any(), any(), anyInt(), anyBoolean(), anyBoolean(), anyLong());
//		verify(textMessageService, times(1)).sendText(any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_withEventCreditCardEnablFalse() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(1, false, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		user.setPhoneNumber(9898989898L);`
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//
//		event.setCreditCardEnabled(false);
//
//		boolean isStaffBid = true;
//		String paymentType = CC;
//		Double bidIncreament = 1d;
//		String smsText = "sms";
//		String userMessageText = "You are submitting a Buy it Now bid. You will be contacted to collect payment.";
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(),anyString())).thenReturn(false);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		doNothing().when(textMessageService).sendText(any());
//		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());
//
//		//Execution
//		String auctionChargeData = eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//
//		//Assertion
//		assertEquals(auctionChargeData, userMessageText);
//
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(auctionBidService, times(1)).getLastBidderLesserThanBuyItNow(any());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(itemService ,times(1)).getBidIncrement(any(), any());
//		verify(itemService ,times(1)).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		verify(itemService, times(2)).update(any());
//		verify(auctionBidService, times(1)).findHighestBidItemForAuction(any(), anyLong());
//		verify(auctionService, times(1)).isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString());
//
//		verify(textMessageUtils, times(1)).getSuccessfulAuctionBidMessage(any(), any(), anyInt());
//		verify(auctionBidService, times(1)).save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any());
//
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertFalse(auctionBidData.isHasPaid());
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getUserId(), user.getUserId().longValue());
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(winnerService, times(1)).findByModuleTypeAndItemId(any(), anyLong());
//		verify(twilioTextMessagePrepareService, times(1)).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		verify(textMessageService, times(1)).sendText(any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_throwException() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(1, false, 0, "ItemCode");
//		item.setNumberOfWinners(1);
//		item.setLiveAuctionItem(false);
//		item.setBuyItNowPrice(0);
//		item.setCode("ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(0L);
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//        String languagecode = "EN";
//
//		event.setCreditCardEnabled(true);
//
//		boolean isStaffBid = true;
//		String paymentType = "";
//		Double bidIncreament = 1d;
//		String smsText = "sms";
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(),anyString())).thenReturn(false);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		doThrow(Exception.class).when(textMessageService).sendText(any());
//		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());
//		Mockito.doReturn(payment).when(eventServiceImpl).handlePayment(any(), any(), any(), anyString(), any(), any(), any(), anyInt(), any());
//
//		//Execution
//		eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//	}

	@Test
	void test_handlePayment_withPaymentTypeChequeAndStaffNull() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = "Cheque";
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);
		auctionPurchaseDto = new AuctionPurchaseDto();
		item = new Item();
		int bidAmount = 100;

		//Execution
		Payment paymentData = eventServiceImpl.handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, new Date(), bidAmount, payment);

		//Assertion
		assertEquals(paymentData.getStripeToken(), payment.getStripeToken());
	}

	@Test
	void test_handlePayment_withPaymentTypeChequeAndCCRequiredForBidConfirmTrue() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = "Cheque";
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);
		auctionPurchaseDto = new AuctionPurchaseDto();
		auctionPurchaseDto.setTokenOrIntentId(tokenOrIntentId);
		item = new Item();
		int bidAmount = 100;
		event.setCcRequiredForBidConfirm(true);

		//mock
		when(paymentService.createOrGetPaymentFromPaymentMethod(anyString(), any(), any(), any())).thenReturn(payment);

		//Execution
		Payment paymentData = eventServiceImpl.handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, new Date(), bidAmount, payment);

		//Assertion
		assertEquals(paymentData.getStripeToken(), payment.getStripeToken());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_throwException1() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(1, true, 100, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		auctionPurchaseDto.setAmount(10);
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(0L);
//
//
//		event.setCreditCardEnabled(true);
//
//		boolean isStaffBid = false;
//		String paymentType = "";
//		Double bidIncreament = 1d;
//		String smsText = "sms";
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(false);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		doThrow(Exception.class).when(textMessageService).sendText(any());
//		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());
//		Mockito.doReturn(payment).when(eventServiceImpl).handlePayment(any(), any(), any(), anyString(), any(), any(), any(), anyInt(), any());
//
//		//Execution
//		eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_auctionCharges1_throwException2() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		item = getItem(1, false, 10, "ItemCode");
//		auction = new Auction();
//		auction.setId(id);
//		auctionPurchaseDto = getAuctionPurchaseDto();
//		staffUser = new User();
//		auctionBid = getAuctionBid(id, 100);
//		payment = new Payment();
//		stripe = getStripe("STRIPE", false);
//		user.setPhoneNumber(0L);
//        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
//
//		event.setCreditCardEnabled(true);
//
//		boolean isStaffBid = false;
//		String paymentType = "";
//		Double bidIncreament = 1d;
//		String smsText = "sms";
//
//		List<Winner> winnerList = new ArrayList<>();
//		winnerList.add(winner);
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(auctionBidService.getLastBidderLesserThanBuyItNow(item)).thenReturn(user);
//		when(auctionService.findByEvent(event)).thenReturn(auction);
//		when(winnerService.findByModuleTypeAndItemId(ModuleType.AUCTION, item.getId())).thenReturn(winnerList);
//		Mockito.doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(itemService.getBidIncrement(any(), any())).thenReturn(bidIncreament);
//		doNothing().when(itemService).handleItemCurrentBidAndHighestBidder(any(), anyInt(), any());
//		when(auctionBidService.findHighestBidItemForAuction(any(), anyLong())).thenReturn(Optional.of(auctionBid));
//		when(auctionService.isApplicableForExtend(any(), any(), anyString(), any(), anyMap(), anyString())).thenReturn(false);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(textMessageUtils.getSuccessfulAuctionBidMessage(any(), any(), anyInt())).thenReturn(smsText);
//		when(auctionBidService.save(any(), anyLong(), any(), anyDouble(), any(), anyBoolean(), anyBoolean(), any())).thenReturn(auctionBid);
//		Mockito.doNothing().when(eventServiceImpl).sendBuyerReciept(any(), any(), any(), any(), anyBoolean(), anyDouble(), any());
//		doNothing().when(twilioTextMessagePrepareService).sendOutBidNotification(any(), any(), any(), anyInt(), anyBoolean());
//		doThrow(Exception.class).when(textMessageService).sendText(any());
//		Mockito.doNothing().when(eventServiceImpl).validateItemAuctionPurchase(any(), any(), anyString(), any());
//		Mockito.doReturn(payment).when(eventServiceImpl).handlePayment(any(), any(), any(), anyString(), any(), any(), any(), anyInt(), any());
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		doNothing().when(joinPaymentItemService).save(any());
//
//		//Execution
//		eventServiceImpl.auctionCharges(user, event, auctionPurchaseDto, isStaffBid, paymentType, staffUser, false,languageMap);
//	}

	@Test
	void test_handlePayment_withPaymentTypeCashAndStaffNull() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = CASH;
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);
		auctionPurchaseDto = new AuctionPurchaseDto();
		item = new Item();
		int bidAmount = 100;
		event.setCreditCardEnabled(true);

		//mock
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());
		when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(null);

		//Execution
		Payment paymentData = eventServiceImpl.handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, new Date(), bidAmount, payment);

		//Assertion
		assertEquals(paymentData.getUserId(), user.getUserId().longValue());

		ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
		verify(paymentService, times(1)).save(paymentArgumentCaptor.capture());

		Payment payment = paymentArgumentCaptor.getValue();
		assertEquals(payment.getUserId(), user.getUserId().longValue());
		assertEquals(payment.getEventId(), event.getEventId());
		assertEquals(payment.getStripeToken(), STRING_EMPTY);

		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
	}

	@Test
	void test_handlePayment_withPaymentTypeCashAndStaff() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = CASH;
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);
		auctionPurchaseDto = new AuctionPurchaseDto();
		item = new Item();
		int bidAmount = 100;
		staff = new Staff();
		staff.setUser(user);

		//mock
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());
		when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(staff);

		//Execution
		Payment paymentData = eventServiceImpl.handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, new Date(), bidAmount, payment);

		//Assertion
		assertEquals(paymentData.getUserId(), user.getUserId().longValue());

		ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
		verify(paymentService, times(1)).save(paymentArgumentCaptor.capture());

		Payment payment = paymentArgumentCaptor.getValue();
		assertEquals(payment.getUserId(), user.getUserId().longValue());
		assertEquals(payment.getEventId(), event.getEventId());
		assertEquals(payment.getStripeToken(), STRING_EMPTY);
		assertEquals(payment.getStaffUserId(), staff.getUser().getUserId().longValue());

		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
	}

	@Test
	void test_handlePayment() throws StripeException, ApiException {

		//setup
		String paymentType = CC;
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);
		auctionPurchaseDto = new AuctionPurchaseDto();
		item = new Item();
		int bidAmount = 100;
		event.setCreditCardEnabled(true);

		//Execution
		Payment paymentData = eventServiceImpl.handlePayment(user, event, auctionPurchaseDto, paymentType, staffUser, item, new Date(), bidAmount, payment);

		//Assertion
		assertEquals(paymentData.getStripeToken(), tokenOrIntentId);
	}


    //TODO: Mockito ReWrite
//	@Test
//	void test_sendBuyerReciept_throwException() throws IOException {
//
//		//setup
//		boolean isOutBidBuyitNowMessage = true;
//		double paidAmout = 100;
//		item = new Item();
//
//		//mock
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//
//		//Execution
//		eventServiceImpl.sendBuyerReciept(user, event, item, null, isOutBidBuyitNowMessage, paidAmout, stripe);
//
//		//Assertion
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//	}

	@Test
	void test_validateItemAuctionPurchase_throwExceptionITEM_ALREADY_PURCHASED(){

		//setup
		item = new Item();
		item.setNumberOfWinners(0);
		auction = new Auction();
		String paymentType = CASH;
		winner = new Winner();
		List<Winner> winnerList = new ArrayList<>();

		//mock
		when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.validateItemAuctionPurchase(user, event, paymentType, item)
        );

		//Assertion
		verify(winnerService, times(1)).findByModuleTypeAndItemId(any(), anyLong());
        assertEquals(NotAcceptableException.AuctionExceptionMsg.ITEM_ALREADY_PURCHASED.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_validateItemAuctionPurchase_throwExceptionNOT_VALID_PAYMENT(){

		//setup
		item = new Item();
		item.setNumberOfWinners(2);
		item.setLiveAuctionItem(true);
		auction = new Auction();
		String paymentType = "CHEQUE";
		winner = new Winner();
		List<Winner> winnerList = new ArrayList<>();
		winnerList.add(winner);

		//mock
		when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.validateItemAuctionPurchase(user, event, paymentType, item)
        );

		//Assertion
		verify(winnerService, times(1)).findByModuleTypeAndItemId(any(), anyLong());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.NOT_VALID_PAYMENT.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_validateItemAuctionPurchase(){

		//setup
		item = new Item();
		item.setNumberOfWinners(2);
		item.setLiveAuctionItem(true);
		auction = new Auction();
		String paymentType = CASH;
		winner = new Winner();
		List<Winner> winnerList = new ArrayList<>();
		winnerList.add(winner);

		//mock
		when(winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(winnerList);

		//Execution
		eventServiceImpl.validateItemAuctionPurchase(user, event, paymentType, item);

		//Assertion
		verify(winnerService, times(1)).findByModuleTypeAndItemId(any(), anyLong());
	}

	@Test
	void test_sendBuyerReciept() throws IOException {

		//setup
		boolean isOutBidBuyitNowMessage = true;
		double paidAmout = 100;
		item = new Item();
		auction = new Auction();
		auction.setEnableMarketValue(true);
		stripe = getStripe("STRIPE", false);

		//mock
		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());

		//Execution
		eventServiceImpl.sendBuyerReciept(user, event, item, auction, isOutBidBuyitNowMessage, paidAmout, stripe);

		//Assertion
		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(), any());
	}

	@Test
	void test_sendBuyerReciept_withEnableMarketValueNull() throws IOException {

		//setup
		boolean isOutBidBuyitNowMessage = true;
		double paidAmout = 100;
		item = new Item();
		auction = new Auction();
		auction.setEnableMarketValue(null);
		stripe = getStripe("STRIPE", false);

		//mock
		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());

		//Execution
		eventServiceImpl.sendBuyerReciept(user, event, item, auction, isOutBidBuyitNowMessage, paidAmout,stripe);

		//Assertion
		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
	}

	@Test
	void test_getPaymentForCheckOut_withPaymentTypeCashAndStaffNull() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = CASH;
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);

		//mock
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());
		when(roStaffService.findByEventAndUserNotExhibitor(any(), any(), anyBoolean())).thenReturn(null);

		//Execution
		Payment paymentData = eventServiceImpl.getPaymentForCheckOut(event, paymentType, tokenOrIntentId, user, stripe, staffUser);

		//Assertion
		assertEquals(paymentData.getUserId(), user.getUserId().longValue());

		ArgumentCaptor<Payment> paymentArgumentCaptor = ArgumentCaptor.forClass(Payment.class);
		verify(paymentService, times(1)).save(paymentArgumentCaptor.capture());

		Payment payment = paymentArgumentCaptor.getValue();
		assertEquals(payment.getUserId(), user.getUserId().longValue());
		assertEquals(payment.getEventId(), event.getEventId());
		assertEquals(payment.getStripeToken(), STRING_EMPTY);

		verify(paymentService, times(1)).findByUserIdAndEventId(anyLong(), anyLong());
		verify(roStaffService, times(1)).findByEventAndUserNotExhibitor(any(), any(), anyBoolean());
	}

	@Test
	void test_getPaymentForCheckOut_withPaymentTypeChequeAndStaffNull() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		String paymentType = "Cheque";
		String tokenOrIntentId = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		staffUser = new User();
		payment = new Payment();
		payment.setStripeToken(tokenOrIntentId);

		//Execution
		Payment paymentData = eventServiceImpl.getPaymentForCheckOut(event, paymentType, tokenOrIntentId, user, stripe, staffUser);

		//Assertion
		assertNull(paymentData);
	}

	@Test
	void test_submitPledge_throwException_ITEM_NOT_FOUND() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {

		//setup
		boolean isStaff = true;
		String paymentType = CC;
		fundANeedPurchaseDto = new FundANeedPurchaseDto();
        fundANeedPurchaseDto.setItemCode("AAA");
		staffUser = new User();

		//mock
		when(itemService.getItemByModuleType(event, "AAA", ModuleType.CAUSEAUCTION)).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser)
        );

		//Assertion
		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
        assertEquals(NotFoundException.ItemNotFound.ITEM_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_throwException_MORE_PLEDGES_SUBMITTED() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//
//		boolean isStaff = true;
//		String paymentType = CC;
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		staffUser = new User();
//		item = new Item();
//		item.setNumberOfWinners(2);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//		String maxPledgeReached = EnumCauseSMS.MAX_PLEDGES_REACHED.getValue(params);
//
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		when(textMessageUtils.getMaxPledgeReachedMessage(anyString())).thenReturn(maxPledgeReached);
//		doThrow(Exception.class).when(textMessageService).sendText(any());
//
//		//Execution
//    Exception exception = assertThrows(NotFoundException.class,
//            () -> eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser)
//    );
//
//		//Assertion
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//      assertEquals(NotFoundException.ItemNotFound.ITEM_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_throwException_NOT_VALID_PAYMENT() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		boolean isStaff = true;
//		String paymentType = "CHEQUE";
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		staffUser = new User();
//		item = new Item();
//		item.setNumberOfWinners(0);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		doNothing().when(userService).saveName(anyString(), anyString(), any());
//		doNothing().when(userService).saveAddress(any(), any());
//		doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//
//		//Execution
//		eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser);
//
//		//Assertion
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//		verify(userService, times(1)).saveName(anyString(), anyString(), any());
//		verify(userService, times(1)).saveAddress(any(), any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_throwException_PLEDGE_SHOULD_GREATER_EQUAL() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//
//		boolean isStaff = true;
//		String paymentType = "";
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		fundANeedPurchaseDto.setAmount(90);
//		staffUser = new User();
//		item = new Item();
//		item.setNumberOfWinners(10);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		doNothing().when(userService).saveName(anyString(), anyString(), any());
//		doNothing().when(userService).saveAddress(any(), any());
//		doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//
//		//Execution
//		eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser);
//
//		//Assertion
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//		verify(userService, times(1)).saveName(anyString(), anyString(), any());
//		verify(userService, times(1)).saveAddress(any(), any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_withPaymentTypeCASHAndIsStaffTrueAndWithStripeAndEventCreditCardEnableTrue() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		user.setPhoneNumber(9898989898L);
//
//		event.setAuctionId(id);
//		event.setCreditCardEnabled(false);
//		boolean isStaff = true;
//		String paymentType = CASH;
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		fundANeedPurchaseDto.setAmount(100);
//		fundANeedPurchaseDto.setNote("note");
//		staffUser = new User();
//		staffUser.setUserId(id);
//		staff = new Staff();
//		staff.setUser(user);
//		item = new Item();
//		item.setNumberOfWinners(0);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//		stripe = getStripe("", false);
//		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		payment = new Payment();
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//		/*Map<String, String> params1 = new HashMap<>();
//		params1.put("currency_symbol", event.getCurrency().getSymbol());
//		params1.put("amount", String.valueOf(fundANeedPurchaseDto.getAmount()));
//		params1.put("item_code", item.getCode());*/
//		String winnerMsg = EnumCauseSMS.PLEDGE_STAFF_SUCCESS.getValue(params);
//		String successMessage = STAFF_CASH_PLEDGE;
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		doNothing().when(userService).saveName(anyString(), anyString(), any());
//		doNothing().when(userService).saveAddress(any(), any());
//		doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
//		when(staffService.findByEventAndUser(any(), any(), anyBoolean())).thenReturn(staff);
//		when(textMessageUtils.getPledgeStaffSuccessMessage(any(), any(), anyInt(),any(),anyMap())).thenReturn(winnerMsg);
//        when(roEventService.getLanguageCodeByUserOrEvent(user,event)).thenReturn("EN");
//
//		//Execution
//		String submitpledgeSuccessMessage = eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser);
//
//		//Assertion
//		assertEquals(submitpledgeSuccessMessage, successMessage);
//
//		verify(textMessageService, times(1)).sendText(any());
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//		verify(userService, times(1)).saveName(anyString(), anyString(), any());
//		verify(userService, times(1)).saveAddress(any(), any());
//		verify(roStripeService, times(1)).findByEvent(any());
//
//		ArgumentCaptor<Pledge> pledgeArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
//		verify(pledgeService, times(1)).save(pledgeArgumentCaptor.capture());
//
//		Pledge pledgeData = pledgeArgumentCaptor.getValue();
//		assertEquals(0, Double.compare(pledgeData.getAmount(), fundANeedPurchaseDto.getAmount()));
//		assertEquals(pledgeData.getPledgeSource(), BiddingSource.STAFF);
//		assertEquals(pledgeData.getCauseAuctionId(), event.getCauseAuctionId());
//		assertTrue(pledgeData.isHasPaid());
//		assertTrue(pledgeData.isConfirmed());
//		assertEquals(pledgeData.getItem().getId(), item.getId());
//		assertEquals(pledgeData.getUser().getUserId(), user.getUserId());
//		assertEquals(pledgeData.getNote(), fundANeedPurchaseDto.getNote());
//		assertEquals(pledgeData.getStaffUserId().getUserId(), staffUser.getUserId());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_withPaymentTypeCCAndIsStaffFalseAndWithStripeAndEventCreditCardEnableTrue() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		user.setPhoneNumber(0L);
//
//		event.setAuctionId(id);
//		event.setCreditCardEnabled(true);
//		boolean isStaff = false;
//		String paymentType = CC;
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		fundANeedPurchaseDto.setAmount(100);
//		fundANeedPurchaseDto.setNote("note");
//        fundANeedPurchaseDto.setItemCode("ABC");
//		staffUser = new User();
//		staffUser.setUserId(id);
//		staff = new Staff();
//		staff.setUser(user);
//		item = new Item();
//		item.setNumberOfWinners(0);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//		stripe = getStripe("STRIPE", false);
//		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		payment = new Payment();
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//		String successMessage = PLEDGE_SUBMIT_MSG;
//		double paidAmount = 100d;
//		double stripeFee = CalculateFees.getStripeFee(paidAmount, fundANeedPurchaseDto.getAmount(), stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//
//		//mock
//		when(itemService.getItemByModuleType(isA(Event.class), anyString(), isA(ModuleType.class))).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		doNothing().when(userService).saveName(fundANeedPurchaseDto.getFirstName(), fundANeedPurchaseDto.getLastName(), user);
//		doNothing().when(userService).saveAddress(any(), any());
//		doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
//		when(staffService.findByEventAndUser(any(), any(), anyBoolean())).thenReturn(staff);
//		when(paymentHandlerService.getPaidAmountForPledgeAfterStripeTransaction(event, user, isStaff, new Date(), stripe, pledge, payment, fundANeedPurchaseDto.getTokenOrIntentId())).thenReturn(paidAmount);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		//Execution
//		String submitpledgeSuccessMessage = eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, staffUser);
//
//		//Assertion
//		assertEquals(submitpledgeSuccessMessage, successMessage);
//
//		verify(textMessageService, times(0)).sendText(any());
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//		//TODO: Mockito
//        //verify(userService, times(1)).saveName(anyString(), anyString(), any());
//		verify(userService, times(1)).saveAddress(any(), any());
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		ArgumentCaptor<Pledge> pledgeArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
//		verify(pledgeService, times(2)).save(pledgeArgumentCaptor.capture());
//
//		Pledge pledgeData = pledgeArgumentCaptor.getValue();
//		assertEquals(0, Double.compare(pledgeData.getAmount(), fundANeedPurchaseDto.getAmount()));
//		assertEquals(pledgeData.getPledgeSource(), BiddingSource.ONLINE);
//		assertEquals(pledgeData.getCauseAuctionId(), event.getCauseAuctionId());
//	//	assertEquals(pledgeData.getPledgeTime().toString(), new Date().toString());
//		assertEquals(pledgeData.isHasPaid(), event.isCreditCardEnabled());
//		assertEquals(pledgeData.isConfirmed(), event.isCreditCardEnabled());
//		assertEquals(pledgeData.getItem().getId(), item.getId());
//		assertEquals(pledgeData.getUser().getUserId(), user.getUserId());
//		assertEquals(pledgeData.getNote(), fundANeedPurchaseDto.getNote());
//		assertEquals(pledgeData.getStaffUserId().getUserId(), staffUser.getUserId());
//		//assertTrue(pledgeData.getStripePaidAmount() == paidAmount);
//		//assertTrue(pledgeData.getStripeFee() == stripeFee);
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_submitPledge_withPaymentTypeCCAndIsStaffFalseAndWithStripeAndEventCreditCardEnableFalse() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		user.setPhoneNumber(0L);
//
//		event.setAuctionId(id);
//		event.setCreditCardEnabled(false);
//		boolean isStaff = false;
//		String paymentType = CC;
//		Integer submittedPledgeCount = 5;
//		fundANeedPurchaseDto = new FundANeedPurchaseDto();
//		fundANeedPurchaseDto.setAmount(100);
//		fundANeedPurchaseDto.setNote("note");
//		staffUser = new User();
//		staffUser.setUserId(id);
//		staff = new Staff();
//		staff.setUser(user);
//		item = new Item();
//		item.setNumberOfWinners(0);
//		item.setCode("AUC");
//		item.setModuleType(ModuleType.AUCTION);
//		item.setStartingBid(100);
//		stripe = getStripe("", false);
//		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		payment = new Payment();
//
//		Map<String, String> params = new HashMap<>();
//		params.put("item_code", item.getCode());
//
//		String successMessage =  "Thank you for submitting your pledge for item " + item.getCode()
//				+ ", please confirm your pledge by responding with your first name and last name.";
//
//		//mock
//		when(itemService.getItemByModuleType(any(), anyString(), any())).thenReturn(Optional.of(item));
//		when(pledgeService.getPledgeCountForItem(item)).thenReturn(submittedPledgeCount);
//		doNothing().when(userService).saveName(anyString(), anyString(), any());
//		doNothing().when(userService).saveAddress(any(), any());
//		doNothing().when(eventServiceImpl).checkModuleActivateAndNotExpired(any(), any(), any(), any());
//		when(roStripeService.findByEvent(event)).thenReturn(stripe);
//		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
//		when(staffService.findByEventAndUser(any(), any(), anyBoolean())).thenReturn(staff);
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		//Execution
//		String submitpledgeSuccessMessage = eventServiceImpl.submitPledge(event, user, fundANeedPurchaseDto, isStaff, paymentType, null);
//
//		//Assertion
//		assertEquals(submitpledgeSuccessMessage, successMessage);
//
//		verify(textMessageService, times(0)).sendText(any());
//		verify(itemService, times(1)).getItemByModuleType(any(), anyString(), any());
//		verify(pledgeService, times(1)).getPledgeCountForItem(any());
//		verify(userService, times(1)).saveName(anyString(), anyString(), any());
//		verify(userService, times(1)).saveAddress(any(), any());
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		ArgumentCaptor<Pledge> pledgeArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
//		verify(pledgeService, times(1)).save(pledgeArgumentCaptor.capture());
//
//		Pledge pledgeData = pledgeArgumentCaptor.getValue();
//		assertEquals(0, Double.compare(pledgeData.getAmount(), fundANeedPurchaseDto.getAmount()));
//		assertEquals(pledgeData.getPledgeSource(), BiddingSource.ONLINE);
//		assertEquals(pledgeData.getCauseAuctionId(), event.getCauseAuctionId());
//		assertEquals(pledgeData.getPledgeTime().toString(), new Date().toString());
//		assertFalse(pledgeData.isHasPaid());
//		assertFalse(pledgeData.isConfirmed());
//		assertEquals(pledgeData.getItem().getId(), item.getId());
//		assertEquals(pledgeData.getUser().getUserId(), user.getUserId());
//		assertEquals(pledgeData.getNote(), fundANeedPurchaseDto.getNote());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_bidCheckout_withWinner() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		List<Long> itemIdList = new ArrayList<>();
//		itemIdList.add(id);
//		itemIdList.add(2L);
//		user.setPhoneNumber(0L);
//		user.setEmail(email);
//		item = new Item();
//		item.setId(id);
//		bidCheckOutDto = new BidCheckOutDto();
//		bidCheckOutDto.setTokenOrIntentId("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		bidCheckOutDto.setItemIds(itemIdList);
//		auctionBid = getAuctionBid(id, 100);
//		auctionBid.setStripePaidAmount(100);
//		auctionBid.setUser(user);
//		staffUser = new User();
//		payment = new Payment();
//		stripe = getStripe("", true);
//		double paidAmount = 100;
//		winner = new Winner();
//		auction = new Auction();
//		auction.setEnableMarketValue(true);
//
//
//		//mock
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
//		Mockito.doReturn(paidAmount).when(eventServiceImpl).getStripePaidAmount(any(), anyDouble(), anyList());
//		doNothing().when(joinPaymentItemService).save(any());
//		when(winnerService.findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong())).thenReturn(winner);
//		when(paymentHandlerService.getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString())).thenReturn(paidAmount);
//		when(auctionService.findByEvent(any())).thenReturn(auction);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		doNothing().when(textMessageService).sendText(any());
//		when(auctionBidService.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(auctionBid);
//
//		//Execution
//		eventServiceImpl.bidCheckout(user, event, bidCheckOutDto, staffUser);
//
//		//Assertion
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(2)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertTrue(auctionBidData.isConfirmed());
//		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(2)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		//assertEquals(winnerData.getPaidTime().toString(), new Date().toString());
//		assertTrue(winnerData.isHasPaid());
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(itemService, times(2)).getItem(anyLong());
//		verify(joinPaymentItemService, times(2)).save(any());
//		verify(winnerService, times(2)).findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong());
//		verify(paymentHandlerService, times(1)).getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		verify(textMessageService, times(1)).sendText(any());
//		verify(auctionBidService, times(2)).findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean());
//	}

	@Test
	void test_bidCheckout_withWinnerNullThrowException_ITEM_ALREADY_PURCHASED() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {

		//setup
		List<Long> itemIdList = new ArrayList<>();
		itemIdList.add(id);
		user.setPhoneNumber(0L);
		user.setEmail(email);
		item = new Item();
		item.setId(id);
		item.setNumberOfWinners(0);
		bidCheckOutDto = new BidCheckOutDto();
		bidCheckOutDto.setTokenOrIntentId("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		bidCheckOutDto.setItemIds(itemIdList);
		auctionBid = getAuctionBid(id, 100);
		auctionBid.setStripePaidAmount(100);
		auctionBid.setUser(user);
		staffUser = new User();
		payment = new Payment();
		stripe = getStripe("", true);
		double paidAmount = 100;
		winner = new Winner();
		auction = new Auction();

		List<Winner> declaredWinner = new ArrayList<>();
		declaredWinner.add(winner);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.createOrGetPayment(bidCheckOutDto.getTokenOrIntentId(), user, event, null, stripe, staffUser, false)).thenReturn(payment);
		when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
		Mockito.doReturn(paidAmount).when(eventServiceImpl).getStripePaidAmount(any(), anyDouble(), anyList());
		doNothing().when(joinPaymentItemService).save(any());
		when(winnerService.findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong())).thenReturn(null);




		when(auctionBidService.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(auctionBid);
		when( winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(declaredWinner);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.bidCheckout(user, event, bidCheckOutDto, staffUser)
        );

		//Assertion
		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture(),eq(event.getEventURL()));
        assertEquals(NotAcceptableException.ItemExceptionMsg.ITEM_ALREADY_PURCHASED.getDeveloperMessage(), exception.getMessage());

		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
		assertTrue(auctionBidData.isHasPaid());
		assertTrue(auctionBidData.isConfirmed());
		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);

		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentService, times(1)).createOrGetPayment(bidCheckOutDto.getTokenOrIntentId(), user, event, null, stripe, staffUser, false);
		verify(itemService, times(1)).getItem(anyLong());
		verify(joinPaymentItemService, times(1)).save(any());
		verify(winnerService, times(1)).findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		verify(textMessageService, times(1)).sendText(any());
//		verify(auctionBidService, times(2)).findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_bidCheckout_withWinnerNull() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		List<Long> itemIdList = new ArrayList<>();
//		itemIdList.add(id);
//		user.setPhoneNumber(0L);
//		user.setEmail(email);
//		item = new Item();
//		item.setId(id);
//		item.setNumberOfWinners(2);
//		bidCheckOutDto = new BidCheckOutDto();
//		bidCheckOutDto.setTokenOrIntentId("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		bidCheckOutDto.setItemIds(itemIdList);
//		auctionBid = getAuctionBid(id, 100);
//		auctionBid.setStripePaidAmount(100);
//		auctionBid.setUser(user);
//		staffUser = new User();
//		payment = new Payment();
//		stripe = getStripe("", true);
//		double paidAmount = 100;
//		winner = new Winner();
//
//		List<Winner> declaredWinner = new ArrayList<>();
//		declaredWinner.add(winner);
//
//		//mock
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
//		Mockito.doReturn(paidAmount).when(eventServiceImpl).getStripePaidAmount(any(), anyDouble(), anyList());
//		doNothing().when(joinPaymentItemService).save(any());
//		when(winnerService.findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong())).thenReturn(null);
//		when(paymentHandlerService.getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString())).thenReturn(paidAmount);
//		when(auctionService.findByEvent(any())).thenReturn(null);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		doNothing().when(textMessageService)  .sendText(any());
//		when(auctionBidService.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(auctionBid);
//		when( winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(declaredWinner);
//
//		//Execution
//		eventServiceImpl.bidCheckout(user, event, bidCheckOutDto, staffUser);
//
//		//Assertion
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertTrue(auctionBidData.isConfirmed());
//		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(itemService, times(1)).getItem(anyLong());
//		verify(joinPaymentItemService, times(1)).save(any());
//		verify(winnerService, times(1)).findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong());
//		verify(paymentHandlerService, times(1)).getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		verify(textMessageService, times(1)).sendText(any());
//		verify(auctionBidService, times(1)).findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_bidCheckout_withWinnerNullAndThrowException() throws StripeException, com.squareup.square.exceptions.ApiException, IOException {
//
//		//setup
//		List<Long> itemIdList = new ArrayList<>();
//		itemIdList.add(id);
//		user.setPhoneNumber(0L);
//		user.setEmail(email);
//		item = new Item();
//		item.setId(id);
//		item.setNumberOfWinners(2);
//		bidCheckOutDto = new BidCheckOutDto();
//		bidCheckOutDto.setTokenOrIntentId("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		bidCheckOutDto.setItemIds(itemIdList);
//		auctionBid = getAuctionBid(id, 100);
//		auctionBid.setStripePaidAmount(100);
//		auctionBid.setUser(user);
//		staffUser = new User();
//		payment = new Payment();
//		stripe = getStripe("", true);
//		double paidAmount = 100;
//		winner = new Winner();
//		auction = new Auction();
//		auction.setEnableMarketValue(null);
//
//		List<Winner> declaredWinner = new ArrayList<>();
//		declaredWinner.add(winner);
//
//		//mock
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(itemService.getItem(anyLong())).thenReturn(Optional.of(item));
//		Mockito.doReturn(paidAmount).when(eventServiceImpl).getStripePaidAmount(any(), anyDouble(), anyList());
//		doNothing().when(joinPaymentItemService).save(any());
//		when(winnerService.findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong())).thenReturn(null);
//		when(paymentHandlerService.getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString())).thenReturn(paidAmount);
//		when(auctionService.findByEvent(any())).thenReturn(auction);
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		Mockito.doThrow(Exception.class).when(textMessageService).sendText(any());
//		when(auctionBidService.findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean())).thenReturn(auctionBid);
//		when( winnerService.findByModuleTypeAndItemId(any(), anyLong())).thenReturn(declaredWinner);
//
//		//Execution
//		eventServiceImpl.bidCheckout(user, event, bidCheckOutDto, staffUser);
//
//		//Assertion
//		ArgumentCaptor<AuctionBid> auctionBidArgumentCaptor = ArgumentCaptor.forClass(AuctionBid.class);
//		verify(auctionBidService, times(1)).save(auctionBidArgumentCaptor.capture());
//
//		AuctionBid auctionBidData = auctionBidArgumentCaptor.getValue();
//		assertTrue(auctionBidData.isHasPaid());
//		assertTrue(auctionBidData.isConfirmed());
//		assertTrue(auctionBidData.getStripePaidAmount() == paidAmount);
//
//		ArgumentCaptor<Winner> winnerArgumentCaptor = ArgumentCaptor.forClass(Winner.class);
//		verify(winnerService, times(1)).save(winnerArgumentCaptor.capture());
//
//		Winner winnerData = winnerArgumentCaptor.getValue();
//		assertEquals(winnerData.getEventId(), event.getEventId());
//		assertEquals(winnerData.getModuleType(), ModuleType.AUCTION);
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(itemService, times(1)).getItem(anyLong());
//		verify(joinPaymentItemService, times(1)).save(any());
//		verify(winnerService, times(1)).findWinnersByModuleAndItemAndBid(any(), anyLong(), anyLong());
//		verify(paymentHandlerService, times(1)).getPaidAmountBidCheckout(any(), any(), any(), any(), anyMap(), anyDouble(),anyLong(), anyString());
//		verify(auctionService, times(1)).findByEvent(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptAuction(any(), any(), anyList(), anyBoolean(), anyDouble(), anyBoolean(),any());
//		verify(textMessageService, times(1)).sendText(any());
//		verify(auctionBidService, times(1)).findAuctionBidByAuctionIdAndUserIdAndItemIdAndHasPaidOrderByAmountDesc(anyLong(), any(), any(), anyBoolean());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_pledgeCheckoutPayment() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		Double amount = 100d;
//		item = new Item();
//		pledgeCheckoutDto = new PledgeCheckoutDto();
//		pledgeCheckoutDto.setPldegeIds(Collections.singletonList(id));
//		payment = new Payment();
//		pledge = new Pledge();
//		pledge.setItem(item);
//		pledge.setAmount(amount);
//		double stripePaidAmount = pledge.getAmount();
//		pledge.setStripePaidAmount(stripePaidAmount);
//		stripe = getStripe("", false);
//		double stripeFee = CalculateFees.getStripeFee(pledge.getStripePaidAmount(), pledge.getAmount(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), pledgeCheckoutDto.getPldegeIds().size());
//		double paidAmount = 100;
//
//		//mock
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(pledgeService.findById(anyLong())).thenReturn(Optional.of(pledge));
//		doNothing().when(joinPaymentItemService).save(any());
//		when(paymentHandlerService.getPaidAmountPledgeCheckout(any(), any(), any(), anyDouble(), anyString())).thenReturn(paidAmount);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		//Execution
//		eventServiceImpl.pledgeCheckoutPayment(event, user, pledgeCheckoutDto);
//
//		//Assertion
//		ArgumentCaptor<Pledge> pledgeArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
//		verify(pledgeService, times(1)).save(pledgeArgumentCaptor.capture());
//
//		Pledge pledgeData = pledgeArgumentCaptor.getValue();
//		assertTrue(pledgeData.isHasPaid());
//		assertTrue(pledgeData.isConfirmed());
//		assertTrue(pledgeData.getStripePaidAmount() == paidAmount);
//		assertTrue(pledgeData.getStripeFee() == stripeFee);
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(pledgeService, times(1)).save(any());
//		verify(joinPaymentItemService, times(1)).save(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//		verify(paymentHandlerService, times(1)).getPaidAmountPledgeCheckout(any(), any(), any(), anyDouble(), anyString());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void pledgeCheckoutPayment_throwException() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		Double amount = 100d;
//		item = new Item();
//		pledgeCheckoutDto = new PledgeCheckoutDto();
//		pledgeCheckoutDto.setPldegeIds(Collections.singletonList(id));
//		payment = new Payment();
//		pledge = new Pledge();
//		pledge.setItem(item);
//		pledge.setAmount(amount);
//		double stripePaidAmount = pledge.getAmount();
//		pledge.setStripePaidAmount(stripePaidAmount);
//		stripe = getStripe("", false);
//		double stripeFee = CalculateFees.getStripeFee(pledge.getStripePaidAmount(), pledge.getAmount(), stripe.getCCPercentageFee(), stripe.getCCFlatFee(), pledgeCheckoutDto.getPldegeIds().size());
//		double paidAmount = 100;
//
//		//mock
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		when(pledgeService.findById(anyLong())).thenReturn(Optional.of(pledge));
//		doNothing().when(joinPaymentItemService).save(any());
//		when(paymentHandlerService.getPaidAmountPledgeCheckout(any(), any(), any(), anyDouble(), anyString())).thenReturn(paidAmount);
//		Mockito.doThrow(Exception.class).when(sendGridMailPrepareService).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//
//		//Execution
//		eventServiceImpl.pledgeCheckoutPayment(event, user, pledgeCheckoutDto);
//
//		//Assertion
//		ArgumentCaptor<Pledge> pledgeArgumentCaptor = ArgumentCaptor.forClass(Pledge.class);
//		verify(pledgeService, times(1)).save(pledgeArgumentCaptor.capture());
//
//		Pledge pledgeData = pledgeArgumentCaptor.getValue();
//		assertTrue(pledgeData.isHasPaid());
//		assertTrue(pledgeData.isConfirmed());
//		assertTrue(pledgeData.getStripePaidAmount() == paidAmount);
//		assertTrue(pledgeData.getStripeFee() == stripeFee);
//
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(pledgeService, times(1)).save(any());
//		verify(joinPaymentItemService, times(1)).save(any());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptCauseAuction(any(), any(), anyMap(), anyDouble());
//		verify(paymentHandlerService, times(1)).getPaidAmountPledgeCheckout(any(), any(), any(), anyDouble(), anyString());
//	}

	@Test
	void test_getStripePaidAmount_withProcessingFeesToPurchaserTrue(){

		//setup
		Double amount = 100d;
		List<Long> pldegeIds = new ArrayList<>();
		pldegeIds.add(id);
		stripe = getStripe("", true);
		double paidAmount = StripeUtil.getAmountToCharge(amount, stripe.getCCPercentageFee(), (stripe.getCCFlatFee() / pldegeIds.size()));

		//Execution
		double stripePaidAmount = eventServiceImpl.getStripePaidAmount(stripe, amount, pldegeIds);

		//Assertion
		assertTrue(stripePaidAmount == paidAmount);
	}
	@Test
	void test_buyRaffleTickets_throwException_RAFFLE_TICKET_PKG_NOT_FOUND() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		raffle = new Raffle();
		raffle.setId(id);
		raffleTicket = new RaffleTicket();
		raffleTicket.setId(id);
		raffleCheckoutDto = new RaffleCheckoutDto();
		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto)
        );

		verify(raffleService, times(1)).findByEvent(any());
		verify(raffleTicketService, times(1)).findByIdAndRaffleId(anyLong(), anyLong());
        assertEquals(NotFoundException.ModuleNotFound.RAFFLE_TICKET_PKG_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_buyRaffleTickets_raffle_limitTotalTicket_true_and_autoSubmitOnPurchase_true_throwException_RAFFLE_TICKETS_ARE_SOLD_OUT() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		long availableTicket = 10L;
		raffle = new Raffle();
		raffle.setId(id);
		raffle.setAvailableTickets(availableTicket);
		raffle.setAutoSubmitOnPurchase(true);
		raffleTicket = new RaffleTicket();
		raffleTicket.setId(id);
		raffleCheckoutDto = new RaffleCheckoutDto();
		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());
		long totalTicketsSale = 10L;

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
		when(purchasedRaffleTicketService.getTotalTicketsByRaffle(anyLong())).thenReturn(totalTicketsSale);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto)
        );

		verify(raffleService, times(1)).findByEvent(any());
		verify(raffleTicketService, times(1)).findByIdAndRaffleId(anyLong(), anyLong());
		verify(purchasedRaffleTicketService, times(1)).getTotalTicketsByRaffle(anyLong());
        assertEquals(NotAcceptableException.RaffleExceptionMsg.RAFFLE_TICKETS_ARE_SOLD_OUT.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_buyRaffleTickets_raffle_limitTotalTicket_true_and_autoSubmitOnPurchase_true_throwException_LIMITTED_TICKET() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		long availableTicket = 20L;
		raffle = new Raffle();
		raffle.setId(id);
		raffle.setAvailableTickets(availableTicket);
		raffle.setAutoSubmitOnPurchase(true);
		raffleTicket = new RaffleTicket();
		raffleTicket.setId(id);
		raffleCheckoutDto = new RaffleCheckoutDto();
		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());
		long totalTicketsSale = 10L;
		long remaningTickets = raffle.getAvailableTickets() - totalTicketsSale;

		NotAcceptableException.RaffleExceptionMsg exceptionMessage = NotAcceptableException.RaffleExceptionMsg.LIMITTED_TICKET;
		exceptionMessage.setDeveloperMessage(
				String.format(exceptionMessage.getDeveloperMessage(), remaningTickets, remaningTickets));

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
		when(purchasedRaffleTicketService.getTotalTicketsByRaffle(anyLong())).thenReturn(totalTicketsSale);
		when(raffleService.getRaffleNumberOfTickets(anyLong(), anyLong())).thenReturn(11);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto)
        );
        assertEquals(exceptionMessage.getDeveloperMessage(), exception.getMessage());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_buyRaffleTickets_withRaffleLimitTotalTicketTrueAndAutoSubmitOnPurchaseTrue() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		double paidAmount = 100;
//		long availableTicket = 20L;
//		raffle = new Raffle();
//		raffle.setId(id);
//		raffle.setAvailableTickets(availableTicket);
//		raffle.setAutoSubmitOnPurchase(true);
//		raffleTicket = new RaffleTicket();
//		raffleTicket.setId(id);
//		raffleTicket.setNumOfTicket(100);
//		raffleTicket.setPrice(100);
//		raffleCheckoutDto = new RaffleCheckoutDto();
//		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());
//		long totalTicketsSale = 10L;
//		long remaningTickets = raffle.getAvailableTickets() - totalTicketsSale;
//		event.setRaffleId(raffle.getId());
//		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
//		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
//		payment = new Payment();
//		double stripeFee = CalculateFees.getStripeFee(paidAmount,raffleTicket.getPrice(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//		String body = "body";
//		String message = "Thank you for purchasing tickets!";
//
//		//mock
//		when(raffleService.findByEvent(any())).thenReturn(raffle);
//		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
//		when(purchasedRaffleTicketService.getTotalTicketsByRaffle(anyLong())).thenReturn(totalTicketsSale);
//		when(raffleService.getRaffleNumberOfTickets(anyLong(), anyLong())).thenReturn(10);
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentHandlerService.getPaidAmountForRaffleTicketsAfterStripeTransaction(any(), any(), any(), any(), any(), anyString())).thenReturn(paidAmount);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
//		when(textMessageUtils.getRaffleTicketPurchase(anyInt(), any(), any(),anyMap())).thenReturn(body);
//		doNothing().when(textMessageService).sendText(any());
//
//		//Execution
//		String finalMessage = eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto);
//
//		//Assertion
//		assertEquals(finalMessage, message);
//		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
//		verify(purchasedRaffleTicketService, times(2)).save(purchasedRaffleTicketArgumentCaptor.capture());
//
//		PurchasedRaffleTicket purchasedRaffleTicketData = purchasedRaffleTicketArgumentCaptor.getValue();
//		assertEquals(purchasedRaffleTicketData.getRaffleId(), event.getRaffleId());
//		assertEquals(purchasedRaffleTicketData.getTicketsPurchased(), raffleTicket.getNumOfTicket());
//		assertEquals(purchasedRaffleTicketData.getUser().getUserId(), user.getUserId());
//		assertEquals(purchasedRaffleTicketData.getPrice(), raffleTicket.getPrice());
//		assertEquals(purchasedRaffleTicketData.getTransactionType(), TransactionType.STRIPE);
//		assertTrue(purchasedRaffleTicketData.getStripePaidAmount() == paidAmount);
//		assertTrue(purchasedRaffleTicketData.getStripeFee() == stripeFee);
//
//		verify(raffleService, times(1)).findByEvent(any());
//		verify(raffleTicketService, times(1)).findByIdAndRaffleId(anyLong(), anyLong());
//		verify(purchasedRaffleTicketService, times(1)).getTotalTicketsByRaffle(anyLong());
//		verify(raffleService, times(1)).getRaffleNumberOfTickets(anyLong(), anyLong());
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(paymentHandlerService, times(1)).getPaidAmountForRaffleTicketsAfterStripeTransaction(any(), any(), any(), any(), any(), anyString());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptRaffleTicketPurchase(any(), any(), any(), anyDouble(), anyString());
//		verify(textMessageUtils, times(1)).getRaffleTicketPurchase(anyInt(), any(), any(),anyMap());
//		verify(textMessageService, times(1)).sendText(any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_buyRaffleTickets_withRaffleLimitTotalTicketFalseAndAutoSubmitOnPurchaseFalse() throws StripeException, com.squareup.square.exceptions.ApiException {
//
//		//setup
//		double paidAmount = 100;
//		long availableTicket = 20L;
//		raffle = new Raffle();
//		raffle.setId(id);
//		raffle.setAvailableTickets(availableTicket);
//		raffle.setAutoSubmitOnPurchase(false);
//		raffleTicket = new RaffleTicket();
//		raffleTicket.setId(id);
//		raffleTicket.setNumOfTicket(100);
//		raffleTicket.setPrice(100);
//		raffleCheckoutDto = new RaffleCheckoutDto();
//		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());
//		event.setRaffleId(raffle.getId());
//		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
//		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
//		payment = new Payment();
//		double stripeFee = CalculateFees.getStripeFee(paidAmount,raffleTicket.getPrice(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
//		String body = "body";
//		String message = String.format(THANKS_FOR_RAFFLE_MSG, raffleTicket.getNumOfTicket());
//
//		//mock
//		when(raffleService.findByEvent(any())).thenReturn(raffle);
//		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//		when(paymentHandlerService.getPaidAmountForRaffleTicketsAfterStripeTransaction(any(), any(), any(), any(), any(), anyString())).thenReturn(paidAmount);
//		when(paymentService.createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean())).thenReturn(payment);
//		doNothing().when(sendGridMailPrepareService).sendBuyerReceiptRaffle(any(), any(), any(), anyDouble(), anyString());
//		when(textMessageUtils.getRaffleOnlinePurchaseMessage(anyInt(), any(), any(),anyMap())).thenReturn(body);
//		doNothing().when(textMessageService).sendText(any());
//
//		//Execution
//		String finalMessage = eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto);
//
//		//Assertion
//		assertEquals(finalMessage, message);
//		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
//		verify(purchasedRaffleTicketService, times(2)).save(purchasedRaffleTicketArgumentCaptor.capture());
//
//		PurchasedRaffleTicket purchasedRaffleTicketData = purchasedRaffleTicketArgumentCaptor.getValue();
//		assertEquals(purchasedRaffleTicketData.getRaffleId(), event.getRaffleId());
//		assertEquals(purchasedRaffleTicketData.getTicketsPurchased(), raffleTicket.getNumOfTicket());
//		assertEquals(purchasedRaffleTicketData.getUser().getUserId(), user.getUserId());
//		assertEquals(purchasedRaffleTicketData.getPrice(), raffleTicket.getPrice());
//		assertEquals(purchasedRaffleTicketData.getTransactionType(), TransactionType.STRIPE);
//		assertTrue(purchasedRaffleTicketData.getStripePaidAmount() == paidAmount);
//		assertTrue(purchasedRaffleTicketData.getStripeFee() == stripeFee);
//
//		verify(raffleService, times(1)).findByEvent(any());
//		verify(raffleTicketService, times(1)).findByIdAndRaffleId(anyLong(), anyLong());
//		verify(roStripeService, times(1)).findByEvent(any());
//		verify(paymentHandlerService, times(1)).getPaidAmountForRaffleTicketsAfterStripeTransaction(any(), any(), any(), any(), any(), anyString());
//		verify(paymentService, times(1)).createOrGetPayment(anyString(), any(), any(), anyString(), any(), any(), anyBoolean());
//		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptRaffle(any(), any(), any(), anyDouble(), anyString());
//		verify(textMessageUtils, times(1)).getRaffleOnlinePurchaseMessage(anyInt(), any(), any(),anyMap());
//		verify(textMessageService, times(1)).sendText(any());
//	}

	@Test
	void test_buyRaffleTickets_withRaffleLimitTotalTicketFalseAndAutoSubmitOnPurchaseFalseAndThrowException() throws StripeException, com.squareup.square.exceptions.ApiException {

		//setup
		double paidAmount = 100;
		long availableTicket = 20L;
		raffle = new Raffle();
		raffle.setId(id);
		raffle.setAvailableTickets(availableTicket);
		raffle.setAutoSubmitOnPurchase(false);
		raffleTicket = new RaffleTicket();
		raffleTicket.setId(id);
		raffleTicket.setNumOfTicket(100);
		raffleTicket.setPrice(100);
		raffleCheckoutDto = new RaffleCheckoutDto();
		raffleCheckoutDto.setRaffleTicketId(raffleTicket.getId());
        raffleCheckoutDto.setTokenOrIntentId("token_intent");

		event.setRaffleId(raffle.getId());
		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
		payment = new Payment();
		double stripeFee = CalculateFees.getStripeFee(paidAmount,raffleTicket.getPrice(),stripe.getCCPercentageFee(),stripe.getCCFlatFee(), 1);
		String body = "body";
		String message = String.format(THANKS_FOR_RAFFLE_MSG, raffleTicket.getNumOfTicket());

		//mock
		when(raffleService.findByEvent(isA(Event.class))).thenReturn(raffle);
		when(raffleTicketService.findByIdAndRaffleId(anyLong(), anyLong())).thenReturn(Optional.of(raffleTicket));
		when(roStripeService.findByEvent(isA(Event.class))).thenReturn(stripe);
		when(paymentHandlerService.getPaidAmountForRaffleTicketsAfterStripeTransaction(isA(Event.class), isA(User.class),
                isA(RaffleTicket.class), isA(PurchasedRaffleTicket.class), isA(Payment.class), anyString())).thenReturn(paidAmount);
		when(paymentService.createOrGetPayment(anyString(), isA(User.class), isA(Event.class), nullable(String.class), isA(Stripe.class), nullable(User.class), anyBoolean())).thenReturn(payment);
		Mockito.doThrow(new RuntimeException()).when(sendGridMailPrepareService).sendBuyerReceiptRaffle(isA(User.class), isA(Event.class), isA(RaffleTicket.class), anyDouble(), anyString());
		when(textMessageUtils.getRaffleOnlinePurchaseMessage(anyInt(), isA(Event.class), isA(User.class),anyMap())).thenReturn(body);
		Mockito.doThrow(new RuntimeException()).when(textMessageService).sendText(isA(TextMessage.class));

		//Execution
		String finalMessage = eventServiceImpl.buyRaffleTickets(event, user, raffleCheckoutDto);

		//Assertion
		assertEquals(finalMessage, message);
		ArgumentCaptor<PurchasedRaffleTicket> purchasedRaffleTicketArgumentCaptor = ArgumentCaptor.forClass(PurchasedRaffleTicket.class);
		verify(purchasedRaffleTicketService, times(2)).save(purchasedRaffleTicketArgumentCaptor.capture());

		PurchasedRaffleTicket purchasedRaffleTicketData = purchasedRaffleTicketArgumentCaptor.getValue();
		assertEquals(purchasedRaffleTicketData.getRaffleId(), event.getRaffleId());
		assertEquals(purchasedRaffleTicketData.getTicketsPurchased(), raffleTicket.getNumOfTicket());
		assertEquals(purchasedRaffleTicketData.getUser().getUserId(), user.getUserId());
		assertEquals(purchasedRaffleTicketData.getPrice(), raffleTicket.getPrice());
		assertEquals(purchasedRaffleTicketData.getTransactionType(), TransactionType.STRIPE);
		assertTrue(purchasedRaffleTicketData.getStripePaidAmount() == paidAmount);
		assertTrue(purchasedRaffleTicketData.getStripeFee() == stripeFee);

		verify(raffleService, times(1)).findByEvent(any());
		verify(raffleTicketService, times(1)).findByIdAndRaffleId(anyLong(), anyLong());
		verify(roStripeService, times(1)).findByEvent(any());
		verify(paymentHandlerService, times(1)).getPaidAmountForRaffleTicketsAfterStripeTransaction(any(), any(), any(), any(), any(), anyString());
		verify(paymentService, times(1)).createOrGetPayment(anyString(), isA(User.class), isA(Event.class), nullable(String.class), isA(Stripe.class), nullable(User.class), anyBoolean());
		verify(sendGridMailPrepareService, times(1)).sendBuyerReceiptRaffle(any(), any(), any(), anyDouble(), anyString());
		verify(textMessageUtils, times(1)).getRaffleOnlinePurchaseMessage(anyInt(), any(), any(),anyMap());
		verify(textMessageService, times(1)).sendText(any());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostGeneralSettings_withStripeTransactionAndEventTextToGiveEnableTrueAndSubscriptionCancelAtperiodEndTrue() throws StripeException {
//
//		//setup
//		ReflectionTestUtils.setField(stripeConfiguration, "API_KEY", "sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		hostGeneralSettingsGet = new HostGeneralSettingsGet();
//		eventDesignDetail = new EventDesignDetail();
//		eventDesignDetail.setTotalFundRaisedShow(true);
//		eventDesignDetail.setSocialSharingEnabled(true);
//		eventDesignDetail.setReplyToEmail(email);
//		eventDesignDetail.setTrackingScript("TrackingScript");
//		stripeTransaction = new StripeTransaction();
//		stripeTransaction.setTextToGiveSubscriptionId("1");
//
//		event.setFundRaisingGoal(1000);
//		event.setGoalStartingAmount(100);
//		event.setSilentAuctionEnabled(true);
//		event.setRaffleEnabled(true);
//		event.setCauseAuctionEnabled(true);
//		event.setDonationEnabled(true);
//		event.setTicketingEnabled(true);
//		event.setAnalyticsId("1");
//		event.setTextToGiveEnabled(true);
//		event.setTrackinPixelId("1");
//        event.setWhiteLabel(new WhiteLabel(0L,"test"));
//
//		subscription = new Subscription();
//		subscription.setCancelAtPeriodEnd(true);
//		subscription.setCurrentPeriodEnd(1567234258631L);
//
//		String textToGiveAvailableUntil = Constants.AVAILABLE_UNTIL_DATE.replace("{SUBSCRIPTION_END_DATE}", new SimpleDateFormat(Constants.DATE_FORMAT_ONLY_MONTH).format(new Date(subscription.getCurrentPeriodEnd() * 1000)));
//
//		//mock
//		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);
//		when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);
//
//		//Execution
//		HostGeneralSettingsGet hostGeneralSettingsGetData = eventServiceImpl.getHostGeneralSettings(event);
//
//		//Assertion
//		assertEquals(hostGeneralSettingsGetData.getCurrency(), event.getCurrency());
//		assertEquals(hostGeneralSettingsGetData.getCurrencySymbol(), event.getCurrency().getSymbol());
//		assertEquals(hostGeneralSettingsGetData.getFundRaisingGoal(), event.getFundRaisingGoal());
//		assertEquals(hostGeneralSettingsGetData.getGoalStartingAmount(), event.getGoalStartingAmount());
//		assertEquals(hostGeneralSettingsGetData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
//		assertEquals(hostGeneralSettingsGetData.isRaffleEnabled(), event.isRaffleEnabled());
//		assertEquals(hostGeneralSettingsGetData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
//		assertEquals(hostGeneralSettingsGetData.isDonationEnabled(), event.isDonationEnabled());
//		assertEquals(hostGeneralSettingsGetData.isTicketingEnabled(), event.isTicketingEnabled());
//		assertEquals(hostGeneralSettingsGetData.getAnalyticsId(), event.getAnalyticsId());
//		assertEquals(hostGeneralSettingsGetData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
//		assertEquals(hostGeneralSettingsGetData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
//		assertEquals(hostGeneralSettingsGetData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
//		assertEquals(hostGeneralSettingsGetData.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
//		assertEquals(hostGeneralSettingsGetData.getTrackingScript(), eventDesignDetail.getTrackingScript());
//		assertEquals(hostGeneralSettingsGetData.getTrackinPixelId(), event.getTrackinPixelId());
//		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinPartnerId(), eventDesignDetail.getLinkedinTrackinPartnerId());
//		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinConversionId(), eventDesignDetail.getLinkedinTrackinConversionId());
//		assertEquals(hostGeneralSettingsGetData.getTextToGiveAvailableUntil(), textToGiveAvailableUntil);
//
//		verify(stripePaymentService, times(1)).getSubscription(anyString(), anyString());
//		verify(eventDesignDetailService, times(1)).findByEvent(any());
//		verify(stripeTransactionService, times(1)).getTextToGiveSubscriptionId(any());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostGeneralSettings_withStripeTransactionAndEventTextToGiveEnableTrueAndSubscriptionCancelAtperiodEndFalse() throws StripeException {
//
//		//setup
//		ReflectionTestUtils.setField(stripeConfiguration, "API_KEY", "sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
//		hostGeneralSettingsGet = new HostGeneralSettingsGet();
//		eventDesignDetail = new EventDesignDetail();
//		eventDesignDetail.setTotalFundRaisedShow(true);
//		eventDesignDetail.setSocialSharingEnabled(true);
//		eventDesignDetail.setReplyToEmail(email);
//		eventDesignDetail.setTrackingScript("TrackingScript");
//		stripeTransaction = new StripeTransaction();
//		stripeTransaction.setTextToGiveSubscriptionId("1");
//
//		event.setFundRaisingGoal(1000);
//		event.setGoalStartingAmount(100);
//		event.setSilentAuctionEnabled(true);
//		event.setRaffleEnabled(true);
//		event.setCauseAuctionEnabled(true);
//		event.setDonationEnabled(true);
//		event.setTicketingEnabled(true);
//		event.setAnalyticsId("1");
//		event.setTextToGiveEnabled(true);
//		event.setTrackinPixelId("1");
//        event.setWhiteLabel(new WhiteLabel(0L,"test"));
//
//		subscription = new Subscription();
//		subscription.setCancelAtPeriodEnd(false);
//		subscription.setCurrentPeriodEnd(1567234258631L);
//
//		//mock
//		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);
//		when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);
//
//		//Execution
//		HostGeneralSettingsGet hostGeneralSettingsGetData = eventServiceImpl.getHostGeneralSettings(event);
//
//		//Assertion
//		assertEquals(hostGeneralSettingsGetData.getCurrency(), event.getCurrency());
//		assertEquals(hostGeneralSettingsGetData.getCurrencySymbol(), event.getCurrency().getSymbol());
//		assertEquals(hostGeneralSettingsGetData.getFundRaisingGoal(), event.getFundRaisingGoal());
//		assertEquals(hostGeneralSettingsGetData.getGoalStartingAmount(), event.getGoalStartingAmount());
//		assertEquals(hostGeneralSettingsGetData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
//		assertEquals(hostGeneralSettingsGetData.isRaffleEnabled(), event.isRaffleEnabled());
//		assertEquals(hostGeneralSettingsGetData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
//		assertEquals(hostGeneralSettingsGetData.isDonationEnabled(), event.isDonationEnabled());
//		assertEquals(hostGeneralSettingsGetData.isTicketingEnabled(), event.isTicketingEnabled());
//		assertEquals(hostGeneralSettingsGetData.getAnalyticsId(), event.getAnalyticsId());
//		assertEquals(hostGeneralSettingsGetData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
//		assertEquals(hostGeneralSettingsGetData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
//		assertEquals(hostGeneralSettingsGetData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
//		assertEquals(hostGeneralSettingsGetData.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
//		assertEquals(hostGeneralSettingsGetData.getTrackingScript(), eventDesignDetail.getTrackingScript());
//		assertEquals(hostGeneralSettingsGetData.getTrackinPixelId(), event.getTrackinPixelId());
//        assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinPartnerId(), eventDesignDetail.getLinkedinTrackinPartnerId());
//        assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinConversionId(), eventDesignDetail.getLinkedinTrackinConversionId());
//
//		verify(stripePaymentService, times(1)).getSubscription(anyString(), anyString());
//		verify(eventDesignDetailService, times(1)).findByEvent(any());
//		verify(stripeTransactionService, times(1)).getTextToGiveSubscriptionId(any());
//	}

	@Test
	void test_getHostGeneralSettings_withStripeTransactionAndEventTextToGiveEnableFalse() throws StripeException {

		//setup
		hostGeneralSettingsGet = new HostGeneralSettingsGet();
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setReplyToEmail(email);
		eventDesignDetail.setTrackingScript("TrackingScript");
		stripeTransaction = new StripeTransaction();
		stripeTransaction.setTextToGiveSubscriptionId("1");

		event.setFundRaisingGoal(1000);
		event.setGoalStartingAmount(100);
		event.setSilentAuctionEnabled(true);
		event.setRaffleEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setDonationEnabled(true);
		event.setTicketingEnabled(true);
		event.setAnalyticsId("1");
		event.setTextToGiveEnabled(false);
		event.setTrackinPixelId("1");
		event.setWhiteLabel(new WhiteLabel(0L,"test"));

		/*subscription = new Subscription();
		subscription.setCancelAtPeriodEnd(false);
		subscription.setCurrentPeriodEnd(1567234258631L);*/

		//mock
		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);

		//when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);

		//Execution
		HostGeneralSettingsGet hostGeneralSettingsGetData = eventServiceImpl.getHostGeneralSettings(event);

		//Assertion
		assertEquals(hostGeneralSettingsGetData.getCurrency(), event.getCurrency());
		assertEquals(hostGeneralSettingsGetData.getCurrencySymbol(), event.getCurrency().getSymbol());
		assertEquals(hostGeneralSettingsGetData.getFundRaisingGoal(), event.getFundRaisingGoal());
		assertEquals(hostGeneralSettingsGetData.getGoalStartingAmount(), event.getGoalStartingAmount());
		assertEquals(hostGeneralSettingsGetData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isRaffleEnabled(), event.isRaffleEnabled());
		assertEquals(hostGeneralSettingsGetData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(hostGeneralSettingsGetData.isTicketingEnabled(), event.isTicketingEnabled());
		assertEquals(hostGeneralSettingsGetData.getAnalyticsId(), event.getAnalyticsId());
		assertEquals(hostGeneralSettingsGetData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
		assertEquals(hostGeneralSettingsGetData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(hostGeneralSettingsGetData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(hostGeneralSettingsGetData.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
		assertEquals(hostGeneralSettingsGetData.getTrackingScript(), eventDesignDetail.getTrackingScript());
		assertEquals(hostGeneralSettingsGetData.getTrackinPixelId(), event.getTrackinPixelId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinPartnerId(), eventDesignDetail.getLinkedinTrackinPartnerId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinConversionId(), eventDesignDetail.getLinkedinTrackinConversionId());

		verify(eventDesignDetailService, times(1)).findByEvent(any());
		verify(stripeTransactionService, times(1)).getTextToGiveSubscriptionId(any());
	}

	@Test
	void test_getHostGeneralSettings_withStripeTransactionNullAndEventTextToGiveEnableTrue() throws StripeException {

		//setup
		hostGeneralSettingsGet = new HostGeneralSettingsGet();
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setReplyToEmail(email);
		eventDesignDetail.setTrackingScript("TrackingScript");

		event.setFundRaisingGoal(1000);
		event.setGoalStartingAmount(100);
		event.setSilentAuctionEnabled(true);
		event.setRaffleEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setDonationEnabled(true);
		event.setTicketingEnabled(true);
		event.setAnalyticsId("1");
		event.setTextToGiveEnabled(true);
		event.setTrackinPixelId("1");
        event.setWhiteLabel(new WhiteLabel(0L,"test"));

		/*subscription = new Subscription();
		subscription.setCancelAtPeriodEnd(false);
		subscription.setCurrentPeriodEnd(1567234258631L);
*/
		//mock
		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(null);

		//when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);

		//Execution
		HostGeneralSettingsGet hostGeneralSettingsGetData = eventServiceImpl.getHostGeneralSettings(event);

		//Assertion
		assertEquals(hostGeneralSettingsGetData.getCurrency(), event.getCurrency());
		assertEquals(hostGeneralSettingsGetData.getCurrencySymbol(), event.getCurrency().getSymbol());
		assertEquals(hostGeneralSettingsGetData.getFundRaisingGoal(), event.getFundRaisingGoal());
		assertEquals(hostGeneralSettingsGetData.getGoalStartingAmount(), event.getGoalStartingAmount());
		assertEquals(hostGeneralSettingsGetData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isRaffleEnabled(), event.isRaffleEnabled());
		assertEquals(hostGeneralSettingsGetData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(hostGeneralSettingsGetData.isTicketingEnabled(), event.isTicketingEnabled());
		assertEquals(hostGeneralSettingsGetData.getAnalyticsId(), event.getAnalyticsId());
		assertEquals(hostGeneralSettingsGetData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
		assertEquals(hostGeneralSettingsGetData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(hostGeneralSettingsGetData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(hostGeneralSettingsGetData.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
		assertEquals(hostGeneralSettingsGetData.getTrackingScript(), eventDesignDetail.getTrackingScript());
		assertEquals(hostGeneralSettingsGetData.getTrackinPixelId(), event.getTrackinPixelId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinPartnerId(), eventDesignDetail.getLinkedinTrackinPartnerId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinConversionId(), eventDesignDetail.getLinkedinTrackinConversionId());

		verify(eventDesignDetailService, times(1)).findByEvent(any());
		verify(stripeTransactionService, times(1)).getTextToGiveSubscriptionId(any());
	}

	@Test
	void test_getHostGeneralSettings_withStripeTransactionNullAndEventTextToGiveEnableFalse() throws StripeException {

		//setup
		hostGeneralSettingsGet = new HostGeneralSettingsGet();
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setReplyToEmail(email);
		eventDesignDetail.setTrackingScript("TrackingScript");

		event.setFundRaisingGoal(1000);
		event.setGoalStartingAmount(100);
		event.setSilentAuctionEnabled(true);
		event.setRaffleEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setDonationEnabled(true);
		event.setTicketingEnabled(true);
		event.setAnalyticsId("1");
		event.setTextToGiveEnabled(false);
		event.setTrackinPixelId("1");
        event.setWhiteLabel(new WhiteLabel(0L,"test"));

		/*subscription = new Subscription();
		subscription.setCancelAtPeriodEnd(false);
		subscription.setCurrentPeriodEnd(1567234258631L);*/

		//mock
		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(null);

		//when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);

		//Execution
		HostGeneralSettingsGet hostGeneralSettingsGetData = eventServiceImpl.getHostGeneralSettings(event);

		//Assertion
		assertEquals(hostGeneralSettingsGetData.getCurrency(), event.getCurrency());
		assertEquals(hostGeneralSettingsGetData.getCurrencySymbol(), event.getCurrency().getSymbol());
		assertEquals(hostGeneralSettingsGetData.getFundRaisingGoal(), event.getFundRaisingGoal());
		assertEquals(hostGeneralSettingsGetData.getGoalStartingAmount(), event.getGoalStartingAmount());
		assertEquals(hostGeneralSettingsGetData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isRaffleEnabled(), event.isRaffleEnabled());
		assertEquals(hostGeneralSettingsGetData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
		assertEquals(hostGeneralSettingsGetData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(hostGeneralSettingsGetData.isTicketingEnabled(), event.isTicketingEnabled());
		assertEquals(hostGeneralSettingsGetData.getAnalyticsId(), event.getAnalyticsId());
		assertEquals(hostGeneralSettingsGetData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
		assertEquals(hostGeneralSettingsGetData.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(hostGeneralSettingsGetData.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(hostGeneralSettingsGetData.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
		assertEquals(hostGeneralSettingsGetData.getTrackingScript(), eventDesignDetail.getTrackingScript());
		assertEquals(hostGeneralSettingsGetData.getTrackinPixelId(), event.getTrackinPixelId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinPartnerId(), eventDesignDetail.getLinkedinTrackinPartnerId());
		assertEquals(hostGeneralSettingsGetData.getLinkedinTrackinConversionId(), eventDesignDetail.getLinkedinTrackinConversionId());

		verify(eventDesignDetailService, times(1)).findByEvent(any());
		verify(stripeTransactionService, times(1)).getTextToGiveSubscriptionId(any());
	}

	@Test
	void Test_setHostGeneralSettings() throws StripeException {

		//setup
		HostGeneralSettings hostGeneralSettings = new HostGeneralSettings();
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setReplyToEmail(email);
		eventDesignDetail.setTrackingScript("TrackingScript");

		event.setFundRaisingGoal(1000);
		event.setGoalStartingAmount(100);
		event.setSilentAuctionEnabled(true);
		event.setRaffleEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setDonationEnabled(true);
		event.setTicketingEnabled(true);
		event.setAnalyticsId("1");
		event.setTextToGiveEnabled(true);
		event.setTrackinPixelId("1");
		getJoinEventWithDealProductData();
		ticketing.setActivated(true);

		//mock

		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);



		//Execution
		eventServiceImpl.setHostGeneralSettings(user, event, hostGeneralSettings);

		//Assertion
		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());

		EventDesignDetail eventDesignDetailDAta = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailDAta.isTotalFundRaisedShow(), eventDesignDetail.isTotalFundRaisedShow());
		assertEquals(eventDesignDetailDAta.isSocialSharingEnabled(), eventDesignDetail.isSocialSharingEnabled());
		assertEquals(eventDesignDetailDAta.getReplyToEmail(), eventDesignDetail.getReplyToEmail());
		assertEquals(eventDesignDetailDAta.getTrackingScript(), eventDesignDetail.getTrackingScript());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();

		assertEquals(eventData.getCurrency(), event.getCurrency());
		assertEquals(eventData.getFundRaisingGoal(), event.getFundRaisingGoal());
		assertEquals(eventData.getGoalStartingAmount(), event.getGoalStartingAmount());
		assertEquals(eventData.isSilentAuctionEnabled(), event.isSilentAuctionEnabled());
		assertEquals(eventData.isRaffleEnabled(), event.isRaffleEnabled());
		assertEquals(eventData.isCauseAuctionEnabled(), event.isCauseAuctionEnabled());
		assertEquals(eventData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(eventData.isTicketingEnabled(), event.isTicketingEnabled());
		assertEquals(eventData.getAnalyticsId(), event.getAnalyticsId());
		assertEquals(eventData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
		assertEquals(eventData.getTrackinPixelId(), event.getTrackinPixelId());

		verify(eventDesignDetailService).findByEvent(any());
	}

	public static Object[] getModuleEnables1(){

		return new Object[]{
				new Object[]{true, true, true, true, true},
				new Object[]{false, true, false, false, false},
				new Object[]{false, false, true, false, false},
				new Object[]{false, false, false, true, false},
				new Object[]{false, false, false, false, true},
		};
	}
	@ParameterizedTest
	@MethodSource("getModuleEnables1")
	void test_getHostEventDetails1(boolean isCauseAuctionEnabled, boolean isSilentAuctionEnabled, boolean isRaffleEnabled, boolean isTicketingEnabled, boolean isTextToGiveEnabled) {

		//setup
		auction = new Auction();
		auction.setModuleShow(true);
		raffle = new Raffle();
		raffle.setModuleShow(true);
		causeAuction = new CauseAuction();
		causeAuction.setModuleShow(true);
		whiteLabel = new WhiteLabel();
		organizer = new Organizer();
		organizer.setOrganizerPageURL("organizerEventUrlPage");
		event.setCauseAuctionEnabled(isCauseAuctionEnabled);
		event.setSilentAuctionEnabled(isSilentAuctionEnabled);
		event.setRaffleEnabled(isRaffleEnabled);
		event.setTicketingEnabled(isTicketingEnabled);
		event.setTextToGiveEnabled(isTextToGiveEnabled);
		event.setName("TestEvent");
		event.setCountryCode(CountryCode.US);
		event.setOrganizer(organizer);
		event.setDonationEnabled(true);
		event.setEnableBidderRegistration(true);
		event.setCreditCardEnabled(true);
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setLogoEnabled(true);
		eventDesignDetail.setHeaderLogoImage("headerLogoImage");
		eventDesignDetail.setHeaderColor("headerColor");
		eventDesignDetail.setHeaderFontColor("headerFontColor");
		eventDesignDetail.setBannerImageEnabled(true);
		eventDesignDetail.setDesc("desc");
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setIntercomActivated(true);
		eventDesignDetail.setHelpCenterActivated(true);
		eventDesignDetail.setPoweredByAeActivated(true);
		eventDesignDetail.setBiillingPageEnabled(true);
		eventDesignDetail.setMarketingOptInHidden(true);
		eventDesignDetail.setMarketingOptInChecked(true);
		eventDesignDetail.setHideFundRaisingModuleToggle(true);
		eventDesignDetail.setAuctionTabTitle("Auction");
		eventDesignDetail.setRaffleTabTitle("Raffle");
		eventDesignDetail.setFundANeedTabTitle("FundANeed");
		eventDesignDetail.setTicketingTabTitle("Ticketing");
		eventDesignDetail.setDonationTabTitle("Donation");
		eventDesignDetail.setDisplayBackgroundColor("#D3D3D3");
		eventDesignDetail.setDisplayTextColor("#D1D1D1");
		eventDesignDetail.setHideSponsorSection(true);
		eventDesignDetail.setLogoImage("logoImage");
		eventDesignDetail.setBannerImage("bannerImage");
		eventDesignDetail.setShowEnableModulePopup(true);
		eventDesignDetail.setSponsorSection("sponserSection");
		eventDesignDetail.setEventTagLine("EVentTagLine");
		eventDesignDetail.setTrackingScript("TrackingScript");
		eventDesignDetail.setThemeId(id);
		eventDesignDetail.setOrderConfirmationText("OrderConfirmationText");
		eventDesignDetail.setEnableSessionsSpeakers(true);
		eventDesignDetail.setEnableAutoAssignedSequence(true);
		eventDesignDetail.setEvent(event);
		String displayNumber = "9898989898";
		boolean moduleActive = !(event.isCauseAuctionEnabled() || event.isSilentAuctionEnabled()
				|| event.isRaffleEnabled() || event.isTicketingEnabled() || event.isTextToGiveEnabled());

		SquareConfiguration squareConfiguration = new SquareConfiguration();
		squareConfiguration.setAeLocationId("Location Id");
        EventModules eventModulesStatus = new EventModules(auction.getModuleShow(), causeAuction.getModuleShow(), raffle.getModuleShow(), ticketing.isRecurringEvent());

		//mock
		when(phoneNumberService.getDisplayNumber(any())).thenReturn(displayNumber);
		when(roEventDesignDetailService.findByEventOrThrowError(any())).thenReturn(eventDesignDetail);
		//when(auctionService.findByEvent(any())).thenReturn(auction);
		//when(raffleService.findByEvent(any())).thenReturn(raffle);
		//when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(eventRepository.getStatusOfAllTheModulesByEvent(event.getEventId())).thenReturn(eventModulesStatus);
        when(eventTemplateRepository.findByEventId(anyLong())).thenReturn(Optional.ofNullable(eventsTemplates));

		//Execution
		HostEventDetailsDto hostEventDetailsDtoData = eventServiceImpl.getHostEventDetails(event);

		//Assertion
		assertEquals(hostEventDetailsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(hostEventDetailsDtoData.getEventNamel(), event.getName());
		assertEquals(hostEventDetailsDtoData.getEventPhoneNumber(), displayNumber);
		assertEquals(hostEventDetailsDtoData.getCountryCode(), event.getCountryCode());
		assertEquals(hostEventDetailsDtoData.getOrganizerURL(), event.getOrganizer().getOrganizerPageURL());
		assertEquals(hostEventDetailsDtoData.isNoModuleActivate(), moduleActive);
		assertEquals(hostEventDetailsDtoData.isAuctionEnabled(), isSilentAuctionEnabled);
		assertEquals(hostEventDetailsDtoData.isFundANeedEnabled(), isCauseAuctionEnabled);
		assertEquals(hostEventDetailsDtoData.isRaffleEnabled(), isRaffleEnabled);
		assertEquals(hostEventDetailsDtoData.isTicketingEnabled(), isTicketingEnabled);
		assertEquals(hostEventDetailsDtoData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(hostEventDetailsDtoData.isTextToGiveEnabled(), isTextToGiveEnabled);
		assertEquals(hostEventDetailsDtoData.getCurrencySymbol(), event.getCurrency().getSymbol());
		assertEquals(hostEventDetailsDtoData.isEnableBidderRegistration(), event.getEnableBidderRegistration());
		assertEquals(hostEventDetailsDtoData.getEventId().longValue(), event.getEventId());
		assertEquals(hostEventDetailsDtoData.getCountryCode(), event.getCountryCode());
		assertEquals(hostEventDetailsDtoData.getEquivalentTimezone(), event.getEquivalentTimeZone());
		assertEquals(hostEventDetailsDtoData.isAuctionModuleShow(), auction.getModuleShow());
		assertEquals(hostEventDetailsDtoData.isRaffleModuleShow(), raffle.getModuleShow());
		assertEquals(hostEventDetailsDtoData.getStripeKey(),stripe.getStripePublishableKey());
		assertEquals(hostEventDetailsDtoData.isCreditCardEnabled(),event.isCreditCardEnabled());

		verify(phoneNumberService).getDisplayNumber(any());
		verify(roEventDesignDetailService).findByEventOrThrowError(any());
		verify(roStripeService).findByEvent(any());
		verify(eventRepository,Mockito.times(1)).getStatusOfAllTheModulesByEvent(anyLong());
	}

	@Test
	void getHostEventDetails_withCountryCodeNull() {

		//setup
		auction = new Auction();
		auction.setModuleShow(true);
		raffle = new Raffle();
		raffle.setModuleShow(true);
		causeAuction = new CauseAuction();
		causeAuction.setModuleShow(true);
		whiteLabel = new WhiteLabel();
		organizer = new Organizer();
		organizer.setOrganizerPageURL("organizerEventUrlPage");
		event.setName("TestEvent");
		event.setSilentAuctionEnabled(false);
		event.setRaffleEnabled(false);
		event.setCauseAuctionEnabled(false);
		event.setDonationEnabled(true);
		event.setTicketingEnabled(false);
		event.setTextToGiveEnabled(false);
		event.setEnableBidderRegistration(true);
		eventDesignDetail = new EventDesignDetail();
		eventDesignDetail.setLogoEnabled(true);
		eventDesignDetail.setHeaderLogoImage("headerLogoImage");
		eventDesignDetail.setHeaderColor("headerColor");
		eventDesignDetail.setHeaderFontColor("headerFontColor");
		eventDesignDetail.setBannerImageEnabled(true);
		eventDesignDetail.setDesc("desc");
		eventDesignDetail.setTotalFundRaisedShow(true);
		eventDesignDetail.setSocialSharingEnabled(true);
		eventDesignDetail.setIntercomActivated(true);
		eventDesignDetail.setHelpCenterActivated(true);
		eventDesignDetail.setPoweredByAeActivated(true);
		eventDesignDetail.setBiillingPageEnabled(true);
		eventDesignDetail.setMarketingOptInHidden(true);
		eventDesignDetail.setMarketingOptInChecked(true);
		eventDesignDetail.setHideFundRaisingModuleToggle(true);
		eventDesignDetail.setAuctionTabTitle("Auction");
		eventDesignDetail.setRaffleTabTitle("Raffle");
		eventDesignDetail.setFundANeedTabTitle("FundANeed");
		eventDesignDetail.setTicketingTabTitle("Ticketing");
		eventDesignDetail.setDonationTabTitle("Donation");
		eventDesignDetail.setDisplayBackgroundColor("#D3D3D3");
		eventDesignDetail.setDisplayTextColor("#D1D1D1");
		eventDesignDetail.setHideSponsorSection(true);
		eventDesignDetail.setLogoImage("logoImage");
		eventDesignDetail.setBannerImage("bannerImage");
		eventDesignDetail.setShowEnableModulePopup(true);
		eventDesignDetail.setSponsorSection("sponserSection");
		eventDesignDetail.setEventTagLine("EVentTagLine");
		eventDesignDetail.setTrackingScript("TrackingScript");
		eventDesignDetail.setThemeId(id);
		eventDesignDetail.setOrderConfirmationText("OrderConfirmationText");
		eventDesignDetail.setEnableSessionsSpeakers(true);
		eventDesignDetail.setEnableAutoAssignedSequence(true);
		eventDesignDetail.setEvent(event);
		String displayNumber = "9898989898";
		boolean moduleActive = !(event.isCauseAuctionEnabled() || event.isSilentAuctionEnabled()
				|| event.isRaffleEnabled() || event.isTicketingEnabled() || event.isTextToGiveEnabled());

		EventModules eventModules = new EventModules(auction.getModuleShow(), causeAuction.getModuleShow(), raffle.getModuleShow(), ticketing.isRecurringEvent());
		//mock
		when(phoneNumberService.getDisplayNumber(any())).thenReturn(displayNumber);
		when(roEventDesignDetailService.findByEventOrThrowError(any())).thenReturn(eventDesignDetail);
		when(eventRepository.getStatusOfAllTheModulesByEvent(anyLong())).thenReturn(eventModules);
		when(this.roStripeService.findByEvent(event)).thenReturn(stripe);
        when(eventTemplateRepository.findByEventId(anyLong())).thenReturn(Optional.ofNullable(eventsTemplates));

		//Execution
		HostEventDetailsDto hostEventDetailsDtoData = eventServiceImpl.getHostEventDetails(event);

		//Assertion
		assertEquals(hostEventDetailsDtoData.getEventUrl(), event.getEventURL());
		assertEquals(hostEventDetailsDtoData.getEventNamel(), event.getName());
		assertEquals(hostEventDetailsDtoData.getEventPhoneNumber(), displayNumber);
		assertEquals(hostEventDetailsDtoData.getCountryCode(), event.getCountryCode());
		assertEquals(hostEventDetailsDtoData.isNoModuleActivate(), moduleActive);
		assertEquals(hostEventDetailsDtoData.isAuctionEnabled(), event.isSilentAuctionEnabled());
		assertEquals(hostEventDetailsDtoData.isFundANeedEnabled(), event.isCauseAuctionEnabled());
		assertEquals(hostEventDetailsDtoData.isRaffleEnabled(), event.isRaffleEnabled());
		assertEquals(hostEventDetailsDtoData.isTicketingEnabled(), event.isTicketingEnabled());
		assertEquals(hostEventDetailsDtoData.isDonationEnabled(), event.isDonationEnabled());
		assertEquals(hostEventDetailsDtoData.isTextToGiveEnabled(), event.isTextToGiveEnabled());
		assertEquals(hostEventDetailsDtoData.getCurrencySymbol(), event.getCurrency().getSymbol());
		assertEquals(hostEventDetailsDtoData.isEnableBidderRegistration(), event.getEnableBidderRegistration());
		assertEquals(hostEventDetailsDtoData.getEventId().longValue(), event.getEventId());
		assertEquals(hostEventDetailsDtoData.getCountryCode(), event.getCountryCode());
		assertEquals(hostEventDetailsDtoData.getEquivalentTimezone(), event.getEquivalentTimeZone());
		assertEquals(hostEventDetailsDtoData.isAuctionModuleShow(), auction.getModuleShow());
		assertEquals(hostEventDetailsDtoData.isRaffleModuleShow(), raffle.getModuleShow());

		verify(phoneNumberService).getDisplayNumber(any());
		verify(roEventDesignDetailService).findByEventOrThrowError(any());
		verify(eventRepository).getStatusOfAllTheModulesByEvent(anyLong());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostHomeDetail() {
//
//		//setup
//		event.setGoalStartingAmount(100);
//		event.setTextToGiveEnabled(true);
//		event.setDonationEnabled(true);
//		event.setCreditCardEnabled(true);
//		event.setWhiteLabel(null);
//		Long recurringEventId = id;
//		auction = new Auction();
//		auction.setId(id);
//		auction.setActivated(true);
//		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//		auction.setEndDate(new Date());
//
//		raffle = new Raffle();
//		raffle.setId(id);
//		raffle.setEndDate(new Date());
//		raffle.setActivated(true);
//		raffle.setRaffleStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		purchasedRaffleTicket = new PurchasedRaffleTicket();
//		List<PurchasedRaffleTicket> purchasedRaffleTickets = new ArrayList<>();
//		purchasedRaffleTickets.add(purchasedRaffleTicket);
//
//		ticketing = new Ticketing();
//		ticketing.setEventEndDate(new Date());
//		ticketing.setEventStartDate(new Date());
//		ticketing.setActivated(true);
//		ticketingType = new TicketingType();
//
//		List<TicketingType> ticketingTypes = new ArrayList<>();
//		ticketingTypes.add(ticketingType);
//
//		causeAuction = new CauseAuction();
//		causeAuction.setId(id);
//		causeAuction.setEndDate(new Date());
//		causeAuction.setActivated(true);
//		causeAuction.setCauseAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		donationSetting = new DonationSettings();
//		donationSetting.setTextToGiveActivated(true);
//
//		donationDisplaySetting = new DonationDisplaySetting(10L,1000d);
//		String displayNumber = "9898989898";
//		event.setGoalStartingAmount(100);
//
//		int collectedAmout = 100;
//		double collectedAmountForBid = 100d;
//		double collectedAmountforTicketing = 100;
//		int numberOfBidder = 10;
//		int totalItems = 5;
//		int numberOfTicketPurchased = 20;
//		int totalTicketPurchased = 20;
//		int itemsWithTickets = 10;
//		int totalBuyers = 12;
//		int donor = 30;
//		int itemsWithDonations = 20;
//		int totalDonations = 30;
//		Long numberOfTicketSold = 10L;
//		Long paidNumberOfTicketSold = 10L;
//		Long freeNumberOfTicketSold = 10L;
//		int numberOfDonationTickets = 10;
//		double totalFundRaised = 1000;
//		int numberOfDonors = 10;
//
//		hostAuctionDetail = new HostAuctionDetail();
//		hostAuctionDetail.setActive(true);
//		hostAuctionDetail.setCollectedAmout(event.getGoalStartingAmount()+collectedAmout);
//		hostAuctionDetail.setEndDate(auction.getActualEndDate());
//		hostAuctionDetail.setNumberOfBidder(numberOfBidder);
//		hostAuctionDetail.setTotalItems(totalItems);
//		hostAuctionDetail.setActive(auction.isActivated());
//		hostAuctionDetail.setStatus(auction.getAuctionStatus());
//
//		eventChecklistItem = new EventChecklistItem();
//		List<EventChecklistItem> eventChecklistItemList = new ArrayList<>();
//		eventChecklistItemList.add(eventChecklistItem);
//
//		stripe = new Stripe();
//		stripe.setProcessingFeesToPurchaser(true);
//		stripe.setActivated(true);
//		stripe.setAccessToken("accessToken");
//		stripe.setEmail(email);
//		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//
//		//mock
//		when(auctionService.findByEvent(any())).thenReturn(auction);
//		when(raffleService.findByEvent(any())).thenReturn(raffle);
//		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
//		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
//		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
//		when(phoneNumberService.getDisplayNumber(anyLong())).thenReturn(displayNumber);
//		when(auctionService.getSumOfHighestBidAmountForEachItem(anyLong())).thenReturn(collectedAmountForBid);
//		when(auctionService.getTotalBidSubmittedUserCount(anyLong())).thenReturn(numberOfBidder);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(auctionBidService.setHostAuctionDetail(anyLong(), any())).thenReturn(hostAuctionDetail);
//
//		when(purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(anyLong())).thenReturn(purchasedRaffleTickets);
//		when(raffleService.getSumOfAllPurchasedRaffleTicketPriceForEachItem(anyList())).thenReturn(collectedAmout);
//		when(raffleService.getTotalTicketSubmittedUserCount(anyList())).thenReturn(numberOfTicketPurchased);
//		when(raffleService.geTotalTicketPurchased(anyList())).thenReturn(totalTicketPurchased);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(raffleService.getItemsWithTickets(anyLong())).thenReturn(itemsWithTickets);
//		when(raffleService.getTotalBuyers(anyLong())).thenReturn(totalBuyers);
//
//		when(causeAuctionService.getTotalProceedsCauseAuctionAmount(any())).thenReturn(collectedAmout);
//		when(causeAuctionService.getTotalPledgeSubmittedUserCount(anyLong())).thenReturn(donor);
//		when(pledgeService.countOfItemsWithPledge(anyLong())).thenReturn(itemsWithDonations);
//		when(pledgeService.countDistinctByCauseAuctionId(anyLong())).thenReturn(totalDonations);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//
//		when(ticketingTypeTicketService.findAllByTicketingAndRecuringEvent(anyLong(), any())).thenReturn(ticketingTypes);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(paidNumberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(freeNumberOfTicketSold);
//		when(ticketingStatisticsService.getTotalTicketsSoldCountByTicketType(any())).thenReturn(numberOfDonationTickets);
//		when(ticketingStatisticsService.getNetSale(any(), anyLong(), anyBoolean())).thenReturn(collectedAmountforTicketing);
//
//		when(donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(any())).thenReturn(donationDisplaySetting);
//
//
//		when(eventRepository.findEventByEventId(anyLong())).thenReturn(event);
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//
//		Mockito.doReturn(eventChecklistItemList).when(eventServiceImpl).getChecklist(any());
//
//		//Execution
//		HostHomeDto hostHomeDtoData = eventServiceImpl.getHostHomeDetail(event, recurringEventId);
//
//		//Assertion
//		assertEquals(hostHomeDtoData.getAuctionDetail().isActive(), hostAuctionDetail.isActive());
//		assertEquals(hostHomeDtoData.getAuctionDetail().getStatus(), auction.getAuctionStatus());
//		assertTrue(hostHomeDtoData.getAuctionDetail().getCollectedAmout() == event.getGoalStartingAmount()+collectedAmout);
//		assertEquals(hostHomeDtoData.getAuctionDetail().getEndDate(), auction.getActualEndDate());
//		assertEquals(hostHomeDtoData.getAuctionDetail().getNumberOfBidder(),numberOfBidder);
//		assertEquals(hostHomeDtoData.getAuctionDetail().getTotalItems(), totalItems);
//
//		assertTrue(hostHomeDtoData.getRaffleDetail().getCollectedAmout() == event.getGoalStartingAmount()+collectedAmout);
//		assertEquals(hostHomeDtoData.getRaffleDetail().getNumberOfTicketPurchased(), numberOfTicketPurchased);
//		assertEquals(hostHomeDtoData.getRaffleDetail().getTotalTicketPurchased(), totalTicketPurchased);
//		assertEquals(hostHomeDtoData.getRaffleDetail().getTotalItems(), totalItems);
//		assertEquals(hostHomeDtoData.getRaffleDetail().getItemsWithTickets(), itemsWithTickets);
//		assertEquals(hostHomeDtoData.getRaffleDetail().getTotalBuyers(), totalBuyers);
//		assertEquals(hostHomeDtoData.getRaffleDetail().isActive(), raffle.isActivated());
//		assertEquals(hostHomeDtoData.getRaffleDetail().getStatus(), raffle.getRaffleStatus());
//
//		assertTrue(hostHomeDtoData.getFundANeedDetail().getCollectedAmout() == event.getGoalStartingAmount()+collectedAmout);
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getDonors(), donor);
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getItemsWithDonations(), itemsWithDonations);
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getTotalItems(), totalItems);
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getTotalDonations(), totalDonations);
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getEndDate(), causeAuction.getEndDate());
//		assertEquals(hostHomeDtoData.getFundANeedDetail().isActive(), causeAuction.isActivated());
//		assertEquals(hostHomeDtoData.getFundANeedDetail().getStatus(), causeAuction.getCauseAuctionStatus());
//
//		verify(auctionService).findByEvent(any());
//		verify(raffleService).findByEvent(any());
//		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
//		verify(causeAuctionService).findByEvent(any());
//		verify(donationSettingsService).getByEventId(anyLong());
//		verify(phoneNumberService).getDisplayNumber(anyLong());
//		verify(auctionService).getSumOfHighestBidAmountForEachItem(anyLong());
//		verify(auctionService).getTotalBidSubmittedUserCount(anyLong());
//		verify(itemService, times(3)).countItemByModuleIdAndModuleType(anyLong(), any());
//		verify(auctionBidService).setHostAuctionDetail(anyLong(), any());
//
//		verify(purchasedRaffleTicketService).getAllPurchasedTicketsForRaffle(anyLong());
//		verify(raffleService).getSumOfAllPurchasedRaffleTicketPriceForEachItem(anyList());
//		verify(raffleService).getTotalTicketSubmittedUserCount(anyList());
//		verify(raffleService).geTotalTicketPurchased(anyList());
//		verify(itemService, times(3)).countItemByModuleIdAndModuleType(anyLong(), any());
//		verify(raffleService).getItemsWithTickets(anyLong());
//		verify(raffleService).getTotalBuyers(anyLong());
//
//		verify(causeAuctionService).getTotalProceedsCauseAuctionAmount(any());
//		verify(causeAuctionService).getTotalPledgeSubmittedUserCount(anyLong());
//		verify(pledgeService).countOfItemsWithPledge(anyLong());
//		verify(pledgeService).countDistinctByCauseAuctionId(anyLong());
//		verify(itemService, times(3)).countItemByModuleIdAndModuleType(anyLong(), any());
//
//		verify(ticketingTypeTicketService).findAllByTicketingAndRecuringEvent(anyLong(), any());
//		verify(ticketingStatisticsService).soldTicketCount(any());
//		verify(ticketingStatisticsService).soldTicketCount(any());
//		verify(ticketingStatisticsService).soldTicketCount(any());
//		verify(ticketingStatisticsService).getTotalTicketsSoldCountByTicketType(any());
//		verify(ticketingStatisticsService).getNetSale(any(), anyLong(), anyBoolean());
//
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostHomeDetail_withAllModuleNull() {
//
//		//setup
//		event.setGoalStartingAmount(100);
//		event.setTextToGiveEnabled(true);
//		event.setDonationEnabled(true);
//		event.setCreditCardEnabled(true);
//		event.setWhiteLabel(null);
//		Long recurringEventId = id;
//
//		String displayNumber = "9898989898";
//		event.setGoalStartingAmount(100);
//
//		eventChecklistItem = new EventChecklistItem();
//		List<EventChecklistItem> eventChecklistItemList = new ArrayList<>();
//		eventChecklistItemList.add(eventChecklistItem);
//
//		//mock
//		when(auctionService.findByEvent(any())).thenReturn(null);
//		when(raffleService.findByEvent(any())).thenReturn(null);
//		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(null);
//		when(causeAuctionService.findByEvent(any())).thenReturn(null);
//		when(donationSettingsService.getByEventId(anyLong())).thenReturn(null);
//		when(phoneNumberService.getDisplayNumber(anyLong())).thenReturn(displayNumber);
//		Mockito.doReturn(eventChecklistItemList).when(eventServiceImpl).getChecklist(any());
//
//		//Execution
//		HostHomeDto hostHomeDtoData = eventServiceImpl.getHostHomeDetail(event, recurringEventId);
//
//		//Assertion
//		assertNotNull(hostHomeDtoData);
//		assertEquals(hostHomeDtoData.getEventUrl(), event.getEventURL());
//		assertEquals(hostHomeDtoData.getEventPhoneNumber(), displayNumber);
//		assertEquals(hostHomeDtoData.getEventCountryCode(), "");
//		assertNull(hostHomeDtoData.getRaffleDetail());
//		assertNull(hostHomeDtoData.getAuctionDetail());
//		assertNull(hostHomeDtoData.getFundANeedDetail());
//		assertNull(hostHomeDtoData.getTextToDonateDetail());
//		assertNull(hostHomeDtoData.getTicketingDetail());
//
//		verify(auctionService).findByEvent(any());
//		verify(raffleService).findByEvent(any());
//		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
//		verify(causeAuctionService).findByEvent(any());
//		verify(donationSettingsService).getByEventId(anyLong());
//		verify(phoneNumberService).getDisplayNumber(anyLong());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostHomeDetail1() {
//
//		//setup
//		event.setGoalStartingAmount(100);
//		event.setTextToGiveEnabled(false);
//		event.setDonationEnabled(true);
//		event.setCreditCardEnabled(true);
//		event.setWhiteLabel(null);
//		event.setCountryCode(CountryCode.US);
//		Long recurringEventId = id;
//		auction = new Auction();
//		auction.setId(id);
//		auction.setActivated(true);
//		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		raffle = new Raffle();
//		raffle.setId(id);
//		raffle.setActivated(true);
//		raffle.setRaffleStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		purchasedRaffleTicket = new PurchasedRaffleTicket();
//		List<PurchasedRaffleTicket> purchasedRaffleTickets = new ArrayList<>();
//		purchasedRaffleTickets.add(purchasedRaffleTicket);
//
//		ticketing = new Ticketing();
//		ticketing.setEventEndDate(new Date());
//		ticketing.setEventStartDate(new Date());
//		ticketing.setActivated(true);
//		ticketingType = new TicketingType();
//
//		List<TicketingType> ticketingTypes = new ArrayList<>();
//		ticketingTypes.add(ticketingType);
//
//		causeAuction = new CauseAuction();
//		causeAuction.setId(id);
//		causeAuction.setActivated(true);
//		causeAuction.setCauseAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		donationSetting = new DonationSettings();
//		donationSetting.setTextToGiveActivated(false);
//		String displayNumber = "9898989898";
//		event.setGoalStartingAmount(100);
//
//		int collectedAmout = 100;
//		double collectedAmountForBid = 100d;
//		double collectedAmountforTicketing = 100;
//		int numberOfBidder = 10;
//		int totalItems = 5;
//		int numberOfTicketPurchased = 20;
//		int totalTicketPurchased = 20;
//		int itemsWithTickets = 10;
//		int totalBuyers = 12;
//		int donor = 30;
//		int itemsWithDonations = 20;
//		int totalDonations = 30;
//		Long numberOfTicketSold = 10L;
//		Long paidNumberOfTicketSold = 10L;
//		Long freeNumberOfTicketSold = 10L;
//		int numberOfDonationTickets = 10;
//		double totalFundRaised = 1000;
//		int numberOfDonors = 10;
//
//		hostAuctionDetail = new HostAuctionDetail();
//		hostAuctionDetail.setActive(true);
//		hostAuctionDetail.setCollectedAmout(event.getGoalStartingAmount()+collectedAmout);
//		hostAuctionDetail.setEndDate(auction.getActualEndDate());
//		hostAuctionDetail.setNumberOfBidder(numberOfBidder);
//		hostAuctionDetail.setTotalItems(totalItems);
//		hostAuctionDetail.setActive(auction.isActivated());
//		hostAuctionDetail.setStatus(auction.getAuctionStatus());
//
//		eventChecklistItem = new EventChecklistItem();
//		List<EventChecklistItem> eventChecklistItemList = new ArrayList<>();
//		eventChecklistItemList.add(eventChecklistItem);
//
//		stripe = new Stripe();
//		stripe.setProcessingFeesToPurchaser(true);
//		stripe.setActivated(true);
//		stripe.setAccessToken("accessToken");
//		stripe.setEmail(email);
//		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//
//		donationDisplaySetting = new DonationDisplaySetting(10L,1000d);
//
//		//mock
//		when(auctionService.findByEvent(any())).thenReturn(auction);
//		when(raffleService.findByEvent(any())).thenReturn(raffle);
//		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(null);
//		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
//		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
//		when(phoneNumberService.getDisplayNumber(anyLong())).thenReturn(displayNumber);
//		when(auctionService.getSumOfHighestBidAmountForEachItem(anyLong())).thenReturn(collectedAmountForBid);
//		when(auctionService.getTotalBidSubmittedUserCount(anyLong())).thenReturn(numberOfBidder);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(auctionBidService.setHostAuctionDetail(anyLong(), any())).thenReturn(hostAuctionDetail);
//
//		when(purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(anyLong())).thenReturn(purchasedRaffleTickets);
//		when(raffleService.getSumOfAllPurchasedRaffleTicketPriceForEachItem(anyList())).thenReturn(collectedAmout);
//		when(raffleService.getTotalTicketSubmittedUserCount(anyList())).thenReturn(numberOfTicketPurchased);
//		when(raffleService.geTotalTicketPurchased(anyList())).thenReturn(totalTicketPurchased);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(raffleService.getItemsWithTickets(anyLong())).thenReturn(itemsWithTickets);
//		when(raffleService.getTotalBuyers(anyLong())).thenReturn(totalBuyers);
//
//		when(causeAuctionService.getTotalProceedsCauseAuctionAmount(any())).thenReturn(collectedAmout);
//		when(causeAuctionService.getTotalPledgeSubmittedUserCount(anyLong())).thenReturn(donor);
//		when(pledgeService.countOfItemsWithPledge(anyLong())).thenReturn(itemsWithDonations);
//		when(pledgeService.countDistinctByCauseAuctionId(anyLong())).thenReturn(totalDonations);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//
//		when(ticketingTypeTicketService.findAllByTicketingAndRecuringEvent(anyLong(), any())).thenReturn(ticketingTypes);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(paidNumberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(freeNumberOfTicketSold);
//		when(ticketingStatisticsService.getTotalTicketsSoldCountByTicketType(any())).thenReturn(numberOfDonationTickets);
//		when(ticketingStatisticsService.getNetSale(any(), anyLong(), anyBoolean())).thenReturn(collectedAmountforTicketing);
//
//		when(donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(any())).thenReturn(donationDisplaySetting);
//
//		when(eventRepository.findEventByEventId(anyLong())).thenReturn(event);
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//
//		Mockito.doReturn(eventChecklistItemList).when(eventServiceImpl).getChecklist(any());
//
//		//Execution
//		HostHomeDto hostHomeDtoData = eventServiceImpl.getHostHomeDetail(event, recurringEventId);
//
//		//Assertion
//		assertEquals(hostHomeDtoData.getEventUrl(), event.getEventURL());
//		assertEquals(hostHomeDtoData.getEventPhoneNumber(), displayNumber);
//		assertEquals(hostHomeDtoData.getEventCountryCode(), "1");
//		assertNull(hostHomeDtoData.getRaffleDetail());
//		assertNull(hostHomeDtoData.getAuctionDetail());
//		assertNull(hostHomeDtoData.getFundANeedDetail());
//		assertEquals(hostHomeDtoData.getTextToDonateDetail().getNumberOfDonors(), numberOfDonors);
//		assertNull(hostHomeDtoData.getTicketingDetail());
//
//		verify(auctionService).findByEvent(any());
//		verify(raffleService).findByEvent(any());
//		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
//		verify(causeAuctionService).findByEvent(any());
//		verify(donationSettingsService).getByEventId(anyLong());
//		verify(phoneNumberService).getDisplayNumber(anyLong());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void getHostHomeDetail_withIsDonationEnableFalseAndTextToGiveEnableFalse() {
//
//		//setup
//		event.setGoalStartingAmount(100);
//		event.setTextToGiveEnabled(false);
//		event.setDonationEnabled(false);
//		event.setCreditCardEnabled(true);
//		event.setWhiteLabel(null);
//		event.setCountryCode(CountryCode.US);
//		Long recurringEventId = id;
//		auction = new Auction();
//		auction.setId(id);
//		auction.setActivated(true);
//		auction.setAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		raffle = new Raffle();
//		raffle.setId(id);
//		raffle.setActivated(true);
//		raffle.setRaffleStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		purchasedRaffleTicket = new PurchasedRaffleTicket();
//		List<PurchasedRaffleTicket> purchasedRaffleTickets = new ArrayList<>();
//		purchasedRaffleTickets.add(purchasedRaffleTicket);
//
//		ticketing = new Ticketing();
//		ticketing.setEventEndDate(new Date());
//		ticketing.setEventStartDate(new Date());
//		ticketing.setActivated(true);
//		ticketingType = new TicketingType();
//
//		List<TicketingType> ticketingTypes = new ArrayList<>();
//		ticketingTypes.add(ticketingType);
//
//		causeAuction = new CauseAuction();
//		causeAuction.setId(id);
//		causeAuction.setActivated(true);
//		causeAuction.setCauseAuctionStatus(ModuleStatus.WINNER_ANNOUNED);
//
//		donationSetting = new DonationSettings();
//		donationSetting.setTextToGiveActivated(false);
//		String displayNumber = "9898989898";
//		event.setGoalStartingAmount(100);
//
//		int collectedAmout = 100;
//		double collectedAmountForBid = 100;
//		double collectedAmountforTicketing = 100;
//		int numberOfBidder = 10;
//		int totalItems = 5;
//		int numberOfTicketPurchased = 20;
//		int totalTicketPurchased = 20;
//		int itemsWithTickets = 10;
//		int totalBuyers = 12;
//		int donor = 30;
//		int itemsWithDonations = 20;
//		int totalDonations = 30;
//		Long numberOfTicketSold = 10L;
//		Long paidNumberOfTicketSold = 10L;
//		Long freeNumberOfTicketSold = 10L;
//		int numberOfDonationTickets = 10;
//		double totalFundRaised = 1000;
//		int numberOfDonors = 10;
//		donationDisplaySetting =new DonationDisplaySetting((long) numberOfDonors,totalFundRaised);
//		hostAuctionDetail = new HostAuctionDetail();
//		hostAuctionDetail.setActive(true);
//		hostAuctionDetail.setCollectedAmout(event.getGoalStartingAmount()+collectedAmout);
//		hostAuctionDetail.setEndDate(auction.getActualEndDate());
//		hostAuctionDetail.setNumberOfBidder(numberOfBidder);
//		hostAuctionDetail.setTotalItems(totalItems);
//		hostAuctionDetail.setActive(auction.isActivated());
//		hostAuctionDetail.setStatus(auction.getAuctionStatus());
//
//		eventChecklistItem = new EventChecklistItem();
//		List<EventChecklistItem> eventChecklistItemList = new ArrayList<>();
//		eventChecklistItemList.add(eventChecklistItem);
//
//		stripe = new Stripe();
//		stripe.setProcessingFeesToPurchaser(true);
//		stripe.setActivated(true);
//		stripe.setAccessToken("accessToken");
//		stripe.setEmail(email);
//		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
//
//		//mock
//		when(auctionService.findByEvent(any())).thenReturn(auction);
//		when(raffleService.findByEvent(any())).thenReturn(raffle);
//		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(null);
//		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
//		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
//		when(phoneNumberService.getDisplayNumber(anyLong())).thenReturn(displayNumber);
//		when(auctionService.getSumOfHighestBidAmountForEachItem(anyLong())).thenReturn(collectedAmountForBid);
//		when(auctionService.getTotalBidSubmittedUserCount(anyLong())).thenReturn(numberOfBidder);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(auctionBidService.setHostAuctionDetail(anyLong(), any())).thenReturn(hostAuctionDetail);
//
//		when(purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(anyLong())).thenReturn(purchasedRaffleTickets);
//		when(raffleService.getSumOfAllPurchasedRaffleTicketPriceForEachItem(anyList())).thenReturn(collectedAmout);
//		when(raffleService.getTotalTicketSubmittedUserCount(anyList())).thenReturn(numberOfTicketPurchased);
//		when(raffleService.geTotalTicketPurchased(anyList())).thenReturn(totalTicketPurchased);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//		when(raffleService.getItemsWithTickets(anyLong())).thenReturn(itemsWithTickets);
//		when(raffleService.getTotalBuyers(anyLong())).thenReturn(totalBuyers);
//
//		when(causeAuctionService.getTotalProceedsCauseAuctionAmount(any())).thenReturn(collectedAmout);
//		when(causeAuctionService.getTotalPledgeSubmittedUserCount(anyLong())).thenReturn(donor);
//		when(pledgeService.countOfItemsWithPledge(anyLong())).thenReturn(itemsWithDonations);
//		when(pledgeService.countDistinctByCauseAuctionId(anyLong())).thenReturn(totalDonations);
//		when(itemService.countItemByModuleIdAndModuleType(anyLong(), any())).thenReturn(totalItems);
//
//		when(ticketingTypeTicketService.findAllByTicketingAndRecuringEvent(anyLong(), any())).thenReturn(ticketingTypes);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(numberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(paidNumberOfTicketSold);
//		when(ticketingStatisticsService.soldTicketCount(any())).thenReturn(freeNumberOfTicketSold);
//		when(ticketingStatisticsService.getTotalTicketsSoldCountByTicketType(any())).thenReturn(numberOfDonationTickets);
//		when(ticketingStatisticsService.getNetSale(any(), anyLong(), anyBoolean())).thenReturn(collectedAmountforTicketing);
//
//		when(donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(any())).thenReturn(donationDisplaySetting);
//
//
//		when(eventRepository.findEventByEventId(anyLong())).thenReturn(event);
//		when(roStripeService.findByEvent(any())).thenReturn(stripe);
//
//		Mockito.doReturn(eventChecklistItemList).when(eventServiceImpl).getChecklist(any());
//
//		//Execution
//		HostHomeDto hostHomeDtoData = eventServiceImpl.getHostHomeDetail(event, recurringEventId);
//
//		//Assertion
//		assertEquals(hostHomeDtoData.getEventUrl(), event.getEventURL());
//		assertEquals(hostHomeDtoData.getEventPhoneNumber(), displayNumber);
//		assertEquals(hostHomeDtoData.getEventCountryCode(), "1");
//		assertNull(hostHomeDtoData.getRaffleDetail());
//		assertNull(hostHomeDtoData.getAuctionDetail());
//		assertNull(hostHomeDtoData.getFundANeedDetail());
//		assertEquals(hostHomeDtoData.getTextToDonateDetail().getNumberOfDonors(), numberOfDonors);
//		assertNull(hostHomeDtoData.getTicketingDetail());
//
//		verify(auctionService).findByEvent(any());
//		verify(raffleService).findByEvent(any());
//		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
//		verify(causeAuctionService).findByEvent(any());
//		verify(donationSettingsService).getByEventId(anyLong());
//		verify(phoneNumberService).getDisplayNumber(anyLong());
//	}

	@Test
	void test_getChecklist_allModuleEnabled(){

		//setup
		eventChecklist = new EventChecklist();
		eventChecklist.setNamingCompleted(true);
		eventChecklist.setAuctionItemAdded(true);
		eventChecklist.setAuctionDateTimeSet(true);
		eventChecklist.setRaffleItemAdded(true);
		eventChecklist.setRaffleDateTimeSet(true);
		eventChecklist.setCauseAuctionDateTimeSet(true);
		eventChecklist.setActivatePaymentProcessing(true);
		eventChecklist.setEventURLCustomized(true);
		event.setSilentAuctionEnabled(true);
		event.setRaffleEnabled(true);
		event.setCauseAuctionEnabled(true);
		event.setTextToGiveEnabled(true);
		auction = new Auction();
		auction.setActivated(true);
		raffle = new Raffle();
		raffle.setActivated(true);
		causeAuction = new CauseAuction();
		causeAuction.setActivated(true);
		DonationSettings donationSettings = new DonationSettings();
		donationSettings.setTextToGiveActivated(true);

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSettings);
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(any())).thenReturn(true);

		when(stripeService.isPresentStripeByEventAndDefaultAccount(any(), anyBoolean())).thenReturn(true);

		//Execution
		List<EventChecklistItem> eventChecklistItemData = eventServiceImpl.getChecklist(event);

		//Assertion
		verify(eventChecklistService, times(1)).findByEvent(any());
		verify(auctionService).findByEvent(any());
		verify(raffleService).findByEvent(any());
		verify(causeAuctionService).findByEvent(any());
		verify(donationSettingsService).getByEventId(anyLong());

		assertEquals(eventChecklistItemData.get(0).getLabel(), "Submit a sample bid for your Auction");
		assertEquals(eventChecklistItemData.get(0).getDescription(), "Give your silent auction a try to see how it works for free!");
		assertEquals(eventChecklistItemData.get(1).getLabel(), "Submit a sample ticket for your Raffle");
		assertEquals(eventChecklistItemData.get(1).getDescription(), "Submit a sample ticket for your Raffle to see how it works for free!");
		assertEquals(eventChecklistItemData.get(2).getLabel(), "Submit a sample pledge for your fund a need");
		assertEquals(eventChecklistItemData.get(2).getDescription(), "Submit a sample pledge for your fund a need to see how it works for free!");
		assertEquals(eventChecklistItemData.get(3).getLabel(), "Submit a sample Text to Give");
		assertEquals(eventChecklistItemData.get(3).getDescription(), "Enter your cell phone number. A text message will be sent to you with instructions on how to 'Text to Give'");
		assertEquals(eventChecklistItemData.get(4).getLabel(), "Publish your event");
		assertEquals(eventChecklistItemData.get(4).getDescription(), "Publish your event");
		assertEquals(eventChecklistItemData.get(5).getLabel(), "Name your event");
		assertEquals(eventChecklistItemData.get(5).getDescription(), "Your event name will be used through the system.");
		assertEquals(eventChecklistItemData.get(6).getLabel(), "Add auction items");
		assertEquals(eventChecklistItemData.get(6).getDescription(), "Add the Auction Items your guests will bid for.");
		assertEquals(eventChecklistItemData.get(7).getLabel(), "Set auction date and ending time");
		assertEquals(eventChecklistItemData.get(7).getDescription(), "Specify when the silent auction winners will be selected.");
		assertEquals(eventChecklistItemData.get(8).getLabel(), "Activate Auction");
		assertEquals(eventChecklistItemData.get(8).getDescription(), "Activate your auction to accept bids from your guests.");
		assertEquals(eventChecklistItemData.get(9).getLabel(), "Add raffle items");
		assertEquals(eventChecklistItemData.get(9).getDescription(), "Add the Raffle Items your guests will bid for.");
		assertEquals(eventChecklistItemData.get(10).getLabel(), "Set raffle date and ending time");
		assertEquals(eventChecklistItemData.get(10).getDescription(), "Specify when the raffle winners will be selected.");
		assertEquals(eventChecklistItemData.get(11).getLabel(), "Activate Raffle");
		assertEquals(eventChecklistItemData.get(11).getDescription(), "Activate your raffle to sell tickets to your guests.");
		assertEquals(eventChecklistItemData.get(12).getLabel(), "Set fund a need date and ending time");
		assertEquals(eventChecklistItemData.get(12).getDescription(), "Specify when the fund a need winners will be selected.");
		assertEquals(eventChecklistItemData.get(13).getLabel(), "Activate Fund a Need Campaign");
		assertEquals(eventChecklistItemData.get(13).getDescription(), "Activate your fund a need campaign to accept pledges from your guests.");
		assertEquals(eventChecklistItemData.get(14).getLabel(), "Activate Payment Processing");
		assertEquals(eventChecklistItemData.get(14).getDescription(), "Activate Payment Processing");
		assertEquals(eventChecklistItemData.get(15).getLabel(), "Customize Event URL");
		assertEquals(eventChecklistItemData.get(15).getDescription(), "Customize Event URL");
		assertEquals(eventChecklistItemData.get(16).getLabel(), "Activate Text to Give Campaign");
		assertEquals(eventChecklistItemData.get(16).getDescription(), "Activate your subscription of text to give campaign to accept donations.");

	}

	@Test
	void test_getChecklist_allModuleDisabled(){

		//setup
		eventChecklist = new EventChecklist();
		eventChecklist.setNamingCompleted(true);
		eventChecklist.setAuctionItemAdded(true);
		eventChecklist.setAuctionDateTimeSet(true);
		eventChecklist.setRaffleItemAdded(true);
		eventChecklist.setRaffleDateTimeSet(true);
		eventChecklist.setCauseAuctionDateTimeSet(true);
		eventChecklist.setActivatePaymentProcessing(true);
		eventChecklist.setEventURLCustomized(true);
		event.setSilentAuctionEnabled(false);
		event.setRaffleEnabled(false);
		event.setCauseAuctionEnabled(false);
		event.setTextToGiveEnabled(false);

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		List<EventChecklistItem> eventChecklistItemData = eventServiceImpl.getChecklist(event);

		//Assertion
		verify(eventChecklistService, times(1)).findByEvent(any());

		assertEquals(eventChecklistItemData.get(0).getLabel(), "Publish your event");
		assertEquals(eventChecklistItemData.get(0).getDescription(), "Publish your event");
		assertEquals(eventChecklistItemData.get(1).getLabel(), "Name your event");
		assertEquals(eventChecklistItemData.get(1).getDescription(), "Your event name will be used through the system.");
		assertEquals(eventChecklistItemData.get(2).getLabel(), "Customize Event URL");
		assertEquals(eventChecklistItemData.get(2).getDescription(), "Customize Event URL");
	}

	@Test
	void test_getPopUpDetailsForAccountActivationRequired_withEventSilentAuctionEnableTrue() {

		//setup
		event.setSilentAuctionEnabled(true);
		eventChecklist = new EventChecklist();
		eventChecklist.setAuctionBidSubmitted(true);
		String label = "Submit a sample bid for your Auction";
		String Description = "Give your silent auction a try to see how it works for free!";
		String todoLink = "#sample-bid";
		String todoButtonText = "Submit Bid";
		String todoDonationText = "Submit Bid";

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		EventChecklistItem eventChecklistItemData = eventServiceImpl.getPopUpDetailsForAccountActivationRequired(event);

		//Assertion
		assertEquals(eventChecklistItemData.isComplete(), eventChecklist.isAuctionBidSubmitted());
		assertEquals(eventChecklistItemData.getLabel(), label);
		assertEquals(eventChecklistItemData.getDescription(),Description);
		assertEquals(eventChecklistItemData.getTodoLink(), todoLink);
		assertTrue(eventChecklistItemData.isDialog());
		assertEquals(eventChecklistItemData.getTodoButtonText(), todoButtonText);
		assertEquals(eventChecklistItemData.getDoneButtonText(), todoDonationText);

		verify(eventChecklistService).findByEvent(any());
	}

	@Test
	void test_getPopUpDetailsForAccountActivationRequired_withEventRaffleEnableTrue() {

		//setup
		event.setRaffleEnabled(true);
		event.setSilentAuctionEnabled(false);
		eventChecklist = new EventChecklist();
		eventChecklist.setAuctionBidSubmitted(true);
		String label = "Submit a sample ticket for your Raffle";
		String Description = "Submit a sample ticket for your Raffle to see how it works for free!";
		String todoLink = "#sample-ticket";
		String todoButtonText = "Submit Raffle Ticket";
		String todoDonationText = "Submit Raffle Ticket";

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		EventChecklistItem eventChecklistItemData = eventServiceImpl.getPopUpDetailsForAccountActivationRequired(event);

		//Assertion
		assertEquals(eventChecklistItemData.isComplete(), eventChecklist.isRaffleTicketSubmitted());
		assertEquals(eventChecklistItemData.getLabel(), label);
		assertEquals(eventChecklistItemData.getDescription(),Description);
		assertEquals(eventChecklistItemData.getTodoLink(), todoLink);
		assertTrue(eventChecklistItemData.isDialog());
		assertEquals(eventChecklistItemData.getTodoButtonText(), todoButtonText);
		assertEquals(eventChecklistItemData.getDoneButtonText(), todoDonationText);

		verify(eventChecklistService).findByEvent(any());
	}

	@Test
	void test_getPopUpDetailsForAccountActivationRequired_with_event_causeAuctionEnabled_true() {

		//setup
		event.setRaffleEnabled(false);
		event.setSilentAuctionEnabled(false);
		event.setCauseAuctionEnabled(true);
		eventChecklist = new EventChecklist();
		eventChecklist.setCauseAuctionPledgeSubmitted(true);
		String label = "Submit a sample pledge for your fund a need";
		String Description = "Submit a sample pledge for your fund a need to see how it works for free!";
		String todoLink = "#sample-cause-bid";
		String todoButtonText = "Submit Pledge";
		String todoDonationText = "Submit Pledge";

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		EventChecklistItem eventChecklistItemData = eventServiceImpl.getPopUpDetailsForAccountActivationRequired(event);

		//Assertion
		assertEquals(eventChecklistItemData.isComplete(), eventChecklist.isCauseAuctionPledgeSubmitted());
		assertEquals(eventChecklistItemData.getLabel(), label);
		assertEquals(eventChecklistItemData.getDescription(),Description);
		assertEquals(eventChecklistItemData.getTodoLink(), todoLink);
		assertTrue(eventChecklistItemData.isDialog());
		assertEquals(eventChecklistItemData.getTodoButtonText(), todoButtonText);
		assertEquals(eventChecklistItemData.getDoneButtonText(), todoDonationText);

		verify(eventChecklistService).findByEvent(any());
	}

	@Test
	void test_getPopUpDetailsForAccountActivationRequired_withEventTextToGiveEnabledTrue() {

		//setup
		event.setRaffleEnabled(false);
		event.setSilentAuctionEnabled(false);
		event.setCauseAuctionEnabled(false);
		event.setTextToGiveEnabled(true);
		eventChecklist = new EventChecklist();
		eventChecklist.setTextToDonate(true);
		String label = "Submit a sample Text to Give";
		String Description = "Enter your cell phone number. A text message will be sent to you with instructions on how to 'Text to Give'";
		String todoLink = "#sample-texttogive";
		String todoButtonText = "Sample Text to Give";
		String todoDonationText = "Sample Text to Give";

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		EventChecklistItem eventChecklistItemData = eventServiceImpl.getPopUpDetailsForAccountActivationRequired(event);

		//Assertion
		assertEquals(eventChecklistItemData.isComplete(), eventChecklist.isTextToDonate());
		assertEquals(eventChecklistItemData.getLabel(), label);
		assertEquals(eventChecklistItemData.getDescription(),Description);
		assertEquals(eventChecklistItemData.getTodoLink(), todoLink);
		assertTrue(eventChecklistItemData.isDialog());
		assertEquals(eventChecklistItemData.getTodoButtonText(), todoButtonText);
		assertEquals(eventChecklistItemData.getDoneButtonText(), todoDonationText);
	}

	@Test
	void test_getHostCreditCardSettings_withStripeNull() {

		//setup
		event.setCreditCardEnabled(true);

		//mock
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(null);

		//Execution
		GetHostCreditCardSettings getHostCreditCardSettingsData = eventServiceImpl.getHostCreditCardSettings(event);

		verify(roEventService).getEventById(anyLong());
		verify(stripeService).findByEventWithEmailDisplayName(any());
	}

	@Test
	void test_getHostCreditCardSettings_withStripeAndEventWhiteLabelNullAndIsWlAFeeExistsFalse() {

		//setup
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(null);
		event.setCcRequiredForBidConfirm(true);
		event.setTaxId("taxId");
        user.setUserId(1L);
        staff.setUser(user);

		stripe.setProcessingFeesToPurchaser(true);
		stripe.setActivated(true);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		stripe.setEmail(email);
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setStaff(staff);

		//mock
		when(roEventService.getEventById(anyLong())).thenReturn(event);
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);


		//Execution
		GetHostCreditCardSettings getHostCreditCardSettingsData = eventServiceImpl.getHostCreditCardSettings(event);

		//Assertion
		assertEquals(getHostCreditCardSettingsData.isProcessingFeesToPurchaser(), stripe.isProcessingFeesToPurchaser());
		assertEquals(getHostCreditCardSettingsData.isCcRequireForBidConfirm(), event.isCcRequiredForBidConfirm());
		assertEquals(getHostCreditCardSettingsData.getTaxId(), event.getTaxId());
		assertTrue(getHostCreditCardSettingsData.isStripeConnected());
		assertEquals(getHostCreditCardSettingsData.isStripeActivated(), stripe.isActivated());
		assertEquals(getHostCreditCardSettingsData.isConnectButtonEnabled(), StringUtils.isBlank(stripe.getAccessToken()));
		assertEquals(getHostCreditCardSettingsData.getStripeAccountEmail(), stripe.getEmail());
		assertEquals(getHostCreditCardSettingsData.getPaymentGateway(), stripe.getPaymentGateway());
		assertTrue(getHostCreditCardSettingsData.isAllowConnectingSquarePayments());

		verify(roEventService).getEventById(anyLong());
		verify(stripeService).findByEventWithEmailDisplayName(any());
	}

	@Test
	void test_getHostCreditCardSettings_withStripeAndEventWhiteLabelNullAndIsWlAFeeExistsTrue() {

		//setup
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(null);
		event.setCcRequiredForBidConfirm(true);
		event.setTaxId("taxId");
        user.setUserId(1L);
        staff.setUser(user);

		stripe.setProcessingFeesToPurchaser(true);
		stripe.setActivated(true);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		stripe.setEmail(email);
		stripe.setAccountDisplayName("Local");
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setStaff(staff);

		//mock
		when(roEventService.getEventById(anyLong())).thenReturn(event);
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);


		//Execution
		GetHostCreditCardSettings getHostCreditCardSettingsData = eventServiceImpl.getHostCreditCardSettings(event);

		//Assertion
		assertEquals(getHostCreditCardSettingsData.isProcessingFeesToPurchaser(), stripe.isProcessingFeesToPurchaser());
		assertEquals(getHostCreditCardSettingsData.isCcRequireForBidConfirm(), event.isCcRequiredForBidConfirm());
		assertEquals(getHostCreditCardSettingsData.getTaxId(), event.getTaxId());
		assertTrue(getHostCreditCardSettingsData.isStripeConnected());
		assertEquals(getHostCreditCardSettingsData.isStripeActivated(), stripe.isActivated());
		assertEquals(getHostCreditCardSettingsData.isConnectButtonEnabled(), StringUtils.isBlank(stripe.getAccessToken()));
		assertEquals(getHostCreditCardSettingsData.getStripeAccountEmail(), stripe.getEmail());
		assertEquals(getHostCreditCardSettingsData.getPaymentGateway(), stripe.getPaymentGateway());
		assertTrue(getHostCreditCardSettingsData.isAllowConnectingSquarePayments());

		verify(roEventService).getEventById(anyLong());
		verify(stripeService).findByEventWithEmailDisplayName(any());
	}

	@Test
	void test_getHostCreditCardSettings_withStripeAndEventWhiteLabelAndIsWlAFeeExistsTrue() {

		//setup
		whiteLabel = new WhiteLabel();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
		event.setCcRequiredForBidConfirm(true);
		event.setTaxId("taxId");
        user.setUserId(1L);
        staff.setUser(user);

		stripe.setProcessingFeesToPurchaser(true);
		stripe.setActivated(true);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		stripe.setEmail(email);
		stripe.setAccountDisplayName("Local");
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setStaff(staff);

		//mock
		when(roEventService.getEventById(anyLong())).thenReturn(event);
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);
		when(transactionFeeConditionalLogicService.isWlAFeeExists(any())).thenReturn(true);

		//Execution
		GetHostCreditCardSettings getHostCreditCardSettingsData = eventServiceImpl.getHostCreditCardSettings(event);

		//Assertion
		assertEquals(getHostCreditCardSettingsData.isProcessingFeesToPurchaser(), stripe.isProcessingFeesToPurchaser());
		assertEquals(getHostCreditCardSettingsData.isCcRequireForBidConfirm(), event.isCcRequiredForBidConfirm());
		assertEquals(getHostCreditCardSettingsData.getTaxId(), event.getTaxId());
		assertTrue(getHostCreditCardSettingsData.isStripeConnected());
		assertEquals(getHostCreditCardSettingsData.isStripeActivated(), stripe.isActivated());
		assertEquals(getHostCreditCardSettingsData.isConnectButtonEnabled(), StringUtils.isBlank(stripe.getAccessToken()));
		assertEquals(getHostCreditCardSettingsData.getStripeAccountEmail(), stripe.getEmail());
		assertEquals(getHostCreditCardSettingsData.getPaymentGateway(), stripe.getPaymentGateway());
		assertEquals(getHostCreditCardSettingsData.getStripeAccountName(), stripe.getAccountDisplayName());
		assertFalse(getHostCreditCardSettingsData.isAllowConnectingSquarePayments());

		verify(roEventService).getEventById(anyLong());
		verify(stripeService).findByEventWithEmailDisplayName(any());
	}

	@Test
	void test_getHostCreditCardSettings_withStripeAndEventWhiteLabelAndIsWlAFeeExistsFalse() {

		//setup
		whiteLabel = new WhiteLabel();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
		event.setCcRequiredForBidConfirm(true);
		event.setTaxId("taxId");
        user.setUserId(1L);
        staff.setUser(user);

		stripe.setProcessingFeesToPurchaser(true);
		stripe.setActivated(true);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		stripe.setEmail(email);
		stripe.setAccountDisplayName("Local");
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        stripe.setStaff(staff);

		//mock
		when(roEventService.getEventById(anyLong())).thenReturn(event);
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);
		when(transactionFeeConditionalLogicService.isWlAFeeExists(any())).thenReturn(false);

		//Execution
		GetHostCreditCardSettings getHostCreditCardSettingsData = eventServiceImpl.getHostCreditCardSettings(event);

		//Assertion
		assertEquals(getHostCreditCardSettingsData.isProcessingFeesToPurchaser(), stripe.isProcessingFeesToPurchaser());
		assertEquals(getHostCreditCardSettingsData.isCcRequireForBidConfirm(), event.isCcRequiredForBidConfirm());
		assertEquals(getHostCreditCardSettingsData.getTaxId(), event.getTaxId());
		assertTrue(getHostCreditCardSettingsData.isStripeConnected());
		assertEquals(getHostCreditCardSettingsData.isStripeActivated(), stripe.isActivated());
		assertEquals(getHostCreditCardSettingsData.isConnectButtonEnabled(), StringUtils.isBlank(stripe.getAccessToken()));
		assertEquals(getHostCreditCardSettingsData.getStripeAccountEmail(), stripe.getEmail());
		assertEquals(getHostCreditCardSettingsData.getPaymentGateway(), stripe.getPaymentGateway());
		assertEquals(getHostCreditCardSettingsData.getStripeAccountName(), stripe.getAccountDisplayName());
		assertTrue(getHostCreditCardSettingsData.isAllowConnectingSquarePayments());

		verify(roEventService).getEventById(anyLong());
		verify(stripeService).findByEventWithEmailDisplayName(any());
	}

	@Test
	void test_setHostCreditCardSettings_isCreditCardEnabledTrueAndStripeServiceIsStripeConnectedFalse() {

		//setup
		String ipAddress = "*************";
		hostCreditCardSettings = new HostCreditCardSettings();
		hostCreditCardSettings.setCcRequireForBidConfirm(true);
		hostCreditCardSettings.setTaxId("TaxId");
		hostCreditCardSettings.setProcessingFeesToPurchaser(true);
		getJoinEventWithDealProductData();

		//mock

		when(stripeService.isStripeConnected(any())).thenReturn(false);

		//Execution
		eventServiceImpl.setHostCreditCardSettings(event, hostCreditCardSettings, user, ipAddress);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventId(), event.getEventId());
		assertEquals(eventData.isCcRequiredForBidConfirm(), hostCreditCardSettings.isCcRequireForBidConfirm());
		assertEquals(eventData.getTaxId(), hostCreditCardSettings.getTaxId());

		verify(stripeService).isStripeConnected(any());
	}

	public static Object[] getProcessingFeesToPurchaser(){
		return new Object[]{
				new Object[]{true},
				new Object[]{false},
		};
	}

	@ParameterizedTest
	@MethodSource("getProcessingFeesToPurchaser")
	void test_setHostCreditCardSettings_isCreditCardEnabledFalseAndStripeServiceIsStripeConnectedTrue(boolean ProcessingFeesToPurchaser) {

		//setup
		String ipAddress = "*************";
		hostCreditCardSettings = new HostCreditCardSettings();
		hostCreditCardSettings.setCcRequireForBidConfirm(true);
		hostCreditCardSettings.setTaxId("TaxId");
		hostCreditCardSettings.setProcessingFeesToPurchaser(ProcessingFeesToPurchaser);
		getJoinEventWithDealProductData();

		donationSetting = new DonationSettings();

		//mock

		when(stripeService.isStripeConnected(any())).thenReturn(true);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);

		//Execution
		eventServiceImpl.setHostCreditCardSettings(event, hostCreditCardSettings, user, ipAddress);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventId(), event.getEventId());
		assertEquals(eventData.isCcRequiredForBidConfirm(), hostCreditCardSettings.isCcRequireForBidConfirm());
		assertEquals(eventData.getTaxId(), hostCreditCardSettings.getTaxId());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());

		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertEquals(stripeData.isProcessingFeesToPurchaser(), hostCreditCardSettings.isProcessingFeesToPurchaser());

		ArgumentCaptor<DonationSettings> donationSettingArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingArgumentCaptor.capture());

		DonationSettings donationSettingsData = donationSettingArgumentCaptor.getValue();
		assertEquals(donationSettingsData.isAbsorbFee(), !ProcessingFeesToPurchaser);
	}

	@Test
	void test_getWhiteLabelEventUrlOrEventUrl_withEventWhiteLabelNullAndEventOldUITrue() {

		//setup
		String eventUrl = uiBaseurl + getEventPath() + event.getEventURL();

		//Execution
		String whiteLabelEventUrlOrEventUrl = eventServiceImpl.getWhiteLabelEventUrlOrEventUrl(event);

		//Assertion
		assertEquals(whiteLabelEventUrlOrEventUrl, eventUrl);
	}

	@Test
	void test_getWhiteLabelEventUrlOrEventUrl_withEventWhiteLabelNullAndEventOldUIFalse() {

		//setup
		String eventUrl = uiBaseurl + getEventPath() + event.getEventURL();

		//Execution
		String whiteLabelEventUrlOrEventUrl = eventServiceImpl.getWhiteLabelEventUrlOrEventUrl(event);

		//Assertion
		assertEquals(whiteLabelEventUrlOrEventUrl, eventUrl);
	}

	@Test
	void test_getWhiteLabelEventUrlOrEventUrl_withEventWhiteLabelAndWhiteLabelHostUrlEmpty() {

		//setup
		whiteLabel = new WhiteLabel();
		whiteLabel.setHostBaseUrl("");
		event.setWhiteLabel(whiteLabel);
		String eventUrl = uiBaseurl + getEventPath() + event.getEventURL();

		//Execution
		String whiteLabelEventUrlOrEventUrl = eventServiceImpl.getWhiteLabelEventUrlOrEventUrl(event);

		//Assertion
		assertEquals(whiteLabelEventUrlOrEventUrl, eventUrl);
	}

	@Test
	void test_getWhiteLabelEventUrlOrEventUrl_withEventWhiteLabelAndWhiteLabelHostUrlAndEventOldUIFalse() {

		//setup
		whiteLabel = new WhiteLabel();
		whiteLabel.setHostBaseUrl("nareshtest");
		event.setWhiteLabel(whiteLabel);
		String eventUrl = event.getWhiteLabel().getHostBaseUrl() + getEventPath() + event.getEventURL();

		//Execution
		String whiteLabelEventUrlOrEventUrl = eventServiceImpl.getWhiteLabelEventUrlOrEventUrl(event);

		//Assertion
		assertEquals(whiteLabelEventUrlOrEventUrl, eventUrl);
	}

	@Test
	void test_getWhiteLabelEventUrlOrEventUrl_withEventWhiteLabelAndWhiteLabelHostUrlAndEventOldUITrue() {

		//setup
		whiteLabel = new WhiteLabel();
		whiteLabel.setHostBaseUrl("nareshtest");
		event.setWhiteLabel(whiteLabel);
		String eventUrl = event.getWhiteLabel().getHostBaseUrl() + getEventPath() + event.getEventURL();

		//Execution
		String whiteLabelEventUrlOrEventUrl = eventServiceImpl.getWhiteLabelEventUrlOrEventUrl(event);

		//Assertion
		assertEquals(whiteLabelEventUrlOrEventUrl, eventUrl);
	}

	@Test
	void test_getHostBillingSettings_withStripeTransactionNull() {

		//setup
		boolean isActivated = true;
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		ticketing.setActivated(true);
		donationSetting =getDonationSetting(true);

		event.setTicketingEnabled(true);

        organizer = new Organizer();
        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();

		//mock
        when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(null);



		//Execution
		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);

		//Assertion
		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());

		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getHostBillingSettings_withStripeTransactionAndSubscriptionPlanAndPlanAmount() throws StripeException {
//
//		//setup
//		ReflectionTestUtils.setField(stripeConfiguration, "PUBLIC_KEY", "pk_test_GHXcXTCaR8n2BoabLKblDpXY");
//		boolean isActivated = true;
//		auction = getAuction(true);
//		raffle = getRaffle(true);
//		causeAuction = getCauseAuction(true);
//		ticketing.setActivated(false);
//		donationSetting =getDonationSetting(true);
//
//		event.setTicketingEnabled(false);
//
//		stripeTransaction = new StripeTransaction();
//		stripeTransaction.setTextToGiveSubscriptionId("sub_Cfejtxk5NGos3Q");
//
//        organizer = new Organizer();
//        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();
//
//		plan = new Plan();
//		plan.setAmount(1000L);
//
//		subscription = new Subscription();
//        SubscriptionItem subscriptionItem = new SubscriptionItem();
//        subscriptionItem.setPlan(plan);
//        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
//        subscriptionItemCollection.setData(Arrays.asList(subscriptionItem));
//        subscription.setItems(subscriptionItemCollection);
//
//		double textToGiveAmount = (subscription.getItems().getData().get(0).getPlan().getAmount()) / 100;
//
//		//mock
//        when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
//        when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
//        when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
//        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
//        when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
//		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);
//		when(stripePaymentService.getSubscription(anyString(), anyString())).thenReturn(subscription);
//        when(organizerRepoService.findByIdThrowException(anyLong())).thenReturn(organizer);
//        when(whiteLabelRepoService.findWhiteLabelSettingsById(anyLong())).thenReturn(whiteLabelBillingSettingsDto);
//
//		//Execution
//		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);
//
//		//Assertion
//		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
//		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
//		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
//		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
//		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
//		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());
//		assertTrue(hostBillingSettingsData.getTextToGiveAmount() == textToGiveAmount);
//
//		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
//	}

	@Test
	void test_getHostBillingSettings_withStripeTransactionAndSubscriptionPlanAndPlanAmountNull() throws StripeException {

		//setup
		boolean isActivated=true;
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		ticketing.setActivated(false);
		donationSetting =getDonationSetting(true);

		event.setTicketingEnabled(true);

		stripeTransaction = new StripeTransaction();
		stripeTransaction.setTextToGiveSubscriptionId("sub_Cfejtxk5NGos3Q");

        organizer = new Organizer();
        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();

		plan = new Plan();

		subscription = new Subscription();
        SubscriptionItem subscriptionItem = new SubscriptionItem();
        subscriptionItem.setPlan(plan);
        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
        subscriptionItemCollection.setData(Arrays.asList(subscriptionItem));
        subscription.setItems(subscriptionItemCollection);

		//mock
        when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);
        when(stripeConfiguration.getAPI_KEY()).thenReturn("stripe_key");
		when(stripePaymentService.getSubscription("stripe_key", stripeTransaction.getTextToGiveSubscriptionId())).thenReturn(subscription);
		when(virtualEventService.getTheNumberOfExhibitorsPurchased(event)).thenReturn(0L);
		when(exhibitorRepoService.findNumberOfExhibitorsByEventId(event)).thenReturn(0L);



		//Execution
		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);

		//Assertion
		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());

		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
	}

	@Test
	void test_getHostBillingSettings_withStripeTransactionAndSubscriptionNull() throws StripeException {

		//setup
		auction = getAuction(true);
		boolean isActivated = true;
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		ticketing.setActivated(false);
		donationSetting =getDonationSetting(true);

		event.setTicketingEnabled(true);

		stripeTransaction = new StripeTransaction();
		stripeTransaction.setTextToGiveSubscriptionId("sub_Cfejtxk5NGos3Q");
		organizer = new Organizer();
		whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();

		plan = new Plan();

		subscription = new Subscription();
        SubscriptionItem subscriptionItem = new SubscriptionItem();
        subscriptionItem.setPlan(plan);
        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
        subscriptionItemCollection.setData(Arrays.asList(subscriptionItem));
        subscription.setItems(subscriptionItemCollection);

		//mock
		when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
		when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);



		//Execution
		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);

		//Assertion
		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());

		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
	}

	@Test
	void test_getHostBillingSettings_withStripeTransactionAndSubscriptionPlanNull() throws StripeException {

		//setup
		boolean isActivated = true;
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		ticketing.setActivated(true);
		donationSetting =getDonationSetting(true);

		event.setTicketingEnabled(true);

		stripeTransaction = new StripeTransaction();
		stripeTransaction.setTextToGiveSubscriptionId("sub_Cfejtxk5NGos3Q");

        organizer = new Organizer();
        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();

		subscription = new Subscription();
        SubscriptionItem subscriptionItem = new SubscriptionItem();
        subscriptionItem.setPlan(null);
        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
        subscriptionItemCollection.setData(Arrays.asList(subscriptionItem));
        subscription.setItems(subscriptionItemCollection);

		//mock
        when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);




		//Execution
		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);

		//Assertion
		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());

		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
	}

	@Test
	void test_getHostBillingSettings_withStripeTransactionAndThrowApiException() throws StripeException {

		//setup
		boolean isActivated = true;
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		ticketing.setActivated(true);
		donationSetting =getDonationSetting(true);

		event.setTicketingEnabled(true);

		stripeTransaction = new StripeTransaction();
		stripeTransaction.setTextToGiveSubscriptionId("sub_Cfejtxk5NGos3Q");

        organizer = new Organizer();
        whiteLabelBillingSettingsDto = new WhiteLabelBillingSettingsDto();

		subscription = new Subscription();
        SubscriptionItem subscriptionItem = new SubscriptionItem();
        subscriptionItem.setPlan(null);
        SubscriptionItemCollection subscriptionItemCollection = new SubscriptionItemCollection();
        subscriptionItemCollection.setData(Arrays.asList(subscriptionItem));
        subscription.setItems(subscriptionItemCollection);

		//mock
        when(auctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(causeAuctionService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(raffleService.findIsActivatedByEventId(anyLong())).thenReturn(isActivated);
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);
        when(donationSettingsService.findTextToGiveActivatedByEventId(anyLong())).thenReturn(isActivated);
		when(stripeTransactionService.getTextToGiveSubscriptionId(any())).thenReturn(stripeTransaction);




		//Execution
		HostBillingSettings hostBillingSettingsData = eventServiceImpl.getHostBillingSettings(event);

		//Assertion
		assertEquals(hostBillingSettingsData.isSlientAuctionActivated(), auction.isActivated());
		assertEquals(hostBillingSettingsData.isCauseAuctionActivated(), causeAuction.isActivated());
		assertEquals(hostBillingSettingsData.isRaffleActivated(), raffle.isActivated());
		assertEquals(hostBillingSettingsData.isTicketingActivated(), ticketing.getActivated());
		assertEquals(hostBillingSettingsData.isTextToGiveActivated(), donationSetting.isTextToGiveActivated());
		assertEquals(hostBillingSettingsData.getPublicKey(), stripeConfiguration.getPUBLIC_KEY());

		verify(ticketingHelperService).findTicketingByEventAndIfNotFoundCreateNew(any());
		verify(stripeTransactionService).getTextToGiveSubscriptionId(any());
	}

	@Test
	void  test_updateDesign_eventCheckList_withNamingCompletedTrue_NoExceptionThrown() {

		//setup
		event.setName("testEVent1");
        eventDesignDetail.setEvent(event);
		boolean generateUrlByName = true;

        virtualEventSetting = new VirtualEventSettingsDTO();
        virtualEventSetting.setStageEnabled(false);

        eventDesignSettingsDto = new EventDesignSettingsDto();
		eventDesignSettingsDto.setEventName("testEvent");
        eventDesignSettingsDto.setEventUrl("testing-event");
        eventDesignSettingsDto.setOrganizerPageURL("test-organizer");

        eventChecklist = new EventChecklist();
		eventChecklist.setNamingCompleted(false);

		//mock
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
        when(eventDesignDetailService.findByEvent(event)).thenReturn(eventDesignDetail);
        when(virtualEventService.getVirtualEventSettingsById(event)).thenReturn(virtualEventSetting);
        //Execution
        assertDoesNotThrow(() -> eventServiceImpl.updateDesign(event, eventDesignSettingsDto, generateUrlByName, user));

		//Assertion
		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(2)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isNamingCompleted());

		verify(eventChecklistService).findByEvent(any());
	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_updateDesign_withEventCheckListNamingCompletedTrueAndEventUrlCustomizedFalseAndOrganizerPageURLEmpty() {
//
//		//setup
//		event.setName("testEvent1");
//		boolean generateUrlByName = true;
//		eventDesignSettingsDto = getEventDesignSettingsDto("testEvent", "");
//        eventDesignSettingsDto.setOrganizerId(1L);
//        eventDesignSettingsDto.setOrganizerName("dummy");
//        eventDesignSettingsDto.setOrganizerPageURL("dummy");
//
//		eventChecklist = new EventChecklist();
//		eventChecklist.setNamingCompleted(true);
//		eventChecklist.setEventURLCustomized(false);
//		long eventCount = 0L;
//		eventDesignDetail = new EventDesignDetail();
//		virtualEventSetting =new VirtualEventSettingsDTO();
//		getJoinEventWithDealProductData();
//        eventDesignDetail.setThemeId(id);
//
//		//mock
//		when(joinEventWithPDProductService.findByEvent(any())).thenReturn(Optional.of(joinEventWithDealProduct));
//		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
//		when(eventRepository.findCountOfEventName(anyString(),anyLong())).thenReturn(eventCount);
//		when(autoAssignedAttendeeNumbersService.isAutoAttendeeSequenceAvailable(event)).thenReturn(true);
//		Mockito.doReturn(url).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
//		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//		when(virtualEventService.getVirtualEventSettingsById(event)).thenReturn(virtualEventSetting);
//        doNothing().when(eventRepoService).save(event);
//        when(organizerService.getOrganizerByURL(eventDesignSettingsDto.getOrganizerPageURL())).thenReturn(organizer);
//
//		//Execution
//		eventServiceImpl.updateDesign(event, eventDesignSettingsDto, generateUrlByName, user);
//
//		//Assertion
//		verify(eventRepository, times(1)).findCountOfEventName(anyString(),anyLong());
//
//		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
//		verify(eventChecklistService, times(2)).save(eventChecklistArgumentCaptor.capture());
//
//		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
//		assertTrue(eventChecklistData.isEventURLCustomized());
//
//		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
//		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
//
//		Event eventData = eventArgumentCaptor.getValue();
//		assertEquals(eventData.getEventURL(), eventDesignSettingsDto.getEventUrl().concat("1"));
//		assertNull(eventData.getOrganizer());
//		assertEquals(eventData.getName(), eventDesignSettingsDto.getEventName());
//
//		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
//		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
//
//		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
//		assertEquals(eventDesignDetailData.getDesc(), eventDesignSettingsDto.getDesc());
//		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignSettingsDto.getLogoImage());
//		assertEquals(eventDesignDetailData.getSponsorSection(), eventDesignSettingsDto.getSponsorSection());
//		assertEquals(eventDesignDetailData.getEventTagLine(), eventDesignSettingsDto.getEventTagLine());
//		assertEquals(eventDesignDetailData.getDisplayBackgroundColor(), eventDesignSettingsDto.getDisplayBackgroundColor());
//		assertEquals(eventDesignDetailData.getTrackingScript(), eventDesignSettingsDto.getTrackingScript());
//		assertEquals(eventDesignDetailData.getThemeId(), eventDesignSettingsDto.getThemeId());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_updateDesign_withEventCheckListNamingCompletedTrueAndEventUrlCustomizedFalseAndOrganizerPageURL() {
//
//		//setup
//		whiteLabel = new WhiteLabel();
//		User user1 = new User();
//		user1.setUserId(id);
//		Organizer organizer1 = new Organizer();
//		organizer1.setCreatedBy(user1);
//		organizer1.setOrganizerPageURL("testUrl");
//		organizer = new Organizer();
//		organizer.setCreatedBy(user);
//		event.setName("testEvent1");
//		event.setWhiteLabel(whiteLabel);
//		event.setOrganizer(organizer1);
//		boolean generateUrlByName = true;
//		eventDesignSettingsDto = getEventDesignSettingsDto("testEvent1", "TestEventOrganizer");
//        eventDesignSettingsDto.setOrganizerId(1L);
//        eventDesignSettingsDto.setOrganizerName("dummy");
//        eventDesignSettingsDto.setOrganizerPageURL("dummy");
//
//		eventChecklist = new EventChecklist();
//		eventChecklist.setNamingCompleted(true);
//		eventChecklist.setEventURLCustomized(false);
//		long eventCount = 0L;
//		eventDesignDetail = new EventDesignDetail();
//        eventDesignDetail.setThemeId(id);
//		staff = new Staff();
//		staff.setRole(StaffRole.staff);
//		staffDetailDto = new StaffDetailDto(user, staff);
//		staffDetailDto.setRole(StaffRole.whitelabeladmin.name());
//		List<StaffDetailDto> staffDetailDtoList = new ArrayList<>();
//		staffDetailDtoList.add(staffDetailDto);
//		virtualEventSetting =new VirtualEventSettingsDTO();
//        ClientApiKey clientApiKey = new ClientApiKey();
//		getJoinEventWithDealProductData();
//
//		//mock
//		when(joinEventWithPDProductService.findByEvent(any())).thenReturn(Optional.of(joinEventWithDealProduct));
//		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
//		Mockito.doReturn(url).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
//		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//		when(organizerService.getOrganizerByURL(anyString())).thenReturn(organizer);
//		when(staffService.getStaffList(any())).thenReturn(staffDetailDtoList);
//		when(staffService.findStaffDtoByWhiteLabel(any())).thenReturn(staffDetailDtoList);
//		when(virtualEventService.getVirtualEventSettingsById(event)).thenReturn(virtualEventSetting);
//		doNothing().when(organizerService).deleteJoinUsersWithOrganizers(any(), any());
//		doNothing().when(chargebeeService).setOrganizerInEventPlanConfig(event, organizer);
//        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
//        when(apiKeyService.findApiKeyByOrganizerId(organizer.getId())).thenReturn(Optional.of(clientApiKey));
//        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(anyLong(), any(), any())).thenReturn(Optional.empty());
//        doNothing().when(eventRepoService).save(event);
//
//		//Execution
//		eventServiceImpl.updateDesign(event, eventDesignSettingsDto, generateUrlByName, user);
//
//		//Assertion
//		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
//		verify(eventChecklistService, times(2)).save(eventChecklistArgumentCaptor.capture());
//
//		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
//		assertTrue(eventChecklistData.isEventURLCustomized());
//
//		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
//		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
//
//		Event eventData = eventArgumentCaptor.getValue();
//		assertEquals(eventData.getEventURL(), eventDesignSettingsDto.getEventUrl().concat("1"));
//		assertEquals(eventData.getOrganizer().getCreatedBy().getUserId(), user.getUserId());
//		assertEquals(eventData.getName(), eventDesignSettingsDto.getEventName());
//
//		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
//		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
//
//		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
//		assertEquals(eventDesignDetailData.getDesc(), eventDesignSettingsDto.getDesc());
//		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignSettingsDto.getLogoImage());
//		assertEquals(eventDesignDetailData.getSponsorSection(), eventDesignSettingsDto.getSponsorSection());
//		assertEquals(eventDesignDetailData.getEventTagLine(), eventDesignSettingsDto.getEventTagLine());
//		assertEquals(eventDesignDetailData.getDisplayBackgroundColor(), eventDesignSettingsDto.getDisplayBackgroundColor());
//		assertEquals(eventDesignDetailData.getTrackingScript(), eventDesignSettingsDto.getTrackingScript());
//		assertEquals(eventDesignDetailData.getThemeId(), eventDesignSettingsDto.getThemeId());
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_updateDesign_withEventCheckListNamingCompletedTrueAndEventUrlCustomizedFalseAndOrganizerPageURL1() {
//
//		//setup
//		organizer = new Organizer();
//		organizer.setCreatedBy(user);
//		organizer.setOrganizerPageURL("TestUrl");
//		event.setName("testEvent1");
//		event.setOrganizer(organizer);
//		boolean generateUrlByName = true;
//		eventDesignSettingsDto = getEventDesignSettingsDto("newName", "TestEventOrganizer");
//        eventDesignSettingsDto.setOrganizerId(1L);
//        eventDesignSettingsDto.setOrganizerName("dummy");
//        eventDesignSettingsDto.setOrganizerPageURL("dummy");
//
//		eventChecklist = new EventChecklist();
//		eventChecklist.setNamingCompleted(true);
//		eventChecklist.setEventURLCustomized(true);
//		eventDesignDetail = new EventDesignDetail();
//        eventDesignDetail.setThemeId(id);
//		staff = new Staff();
//		staff.setId(id);
//		staff.setRole(StaffRole.admin);
//		StaffDetailDto staffDetailDto = new StaffDetailDto(user, staff);
//		List<StaffDetailDto> staffDetailDtoList = new ArrayList<>();
//		staffDetailDtoList.add(staffDetailDto);
//
//		virtualEventSetting =new VirtualEventSettingsDTO();
//
//
//		getJoinEventWithDealProductData();
//
//		//mock
//		when(joinEventWithPDProductService.findByEvent(any())).thenReturn(Optional.of(joinEventWithDealProduct));
//		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
//		Mockito.doReturn(url).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
//		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
//		when(organizerService.getOrganizerByURL(anyString())).thenReturn(organizer);
//		when(staffService.getStaffList(any())).thenReturn(staffDetailDtoList);
//		when(virtualEventService.getVirtualEventSettingsById(event)).thenReturn(virtualEventSetting);
//		doNothing().when(organizerService).addJoinUsersWithOrganizers(any(), any(), any(), anyBoolean());doNothing().when(chargebeeService).setOrganizerInEventPlanConfig(event, organizer);
//        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
//        doNothing().when(eventRepoService).save(event);
//        when(integrationService.getByIntegrationSourceIdAndSourceTypeAndIntegrationTypeAndEnabled(anyLong(), any(), any())).thenReturn(Optional.empty());
//
//		//Execution
//		eventServiceImpl.updateDesign(event, eventDesignSettingsDto, generateUrlByName, user);
//
//		//Assertion
//		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
//		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
//
//		Event eventData = eventArgumentCaptor.getValue();
//		assertEquals(eventData.getEventURL(), eventDesignSettingsDto.getEventUrl().concat("1"));
//		assertEquals(eventData.getOrganizer().getCreatedBy().getUserId(), user.getUserId());
//		assertEquals(eventData.getName(), eventDesignSettingsDto.getEventName());
//
//		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
//		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());
//
//		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
//		assertEquals(eventDesignDetailData.getDesc(), eventDesignSettingsDto.getDesc());
//		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignSettingsDto.getLogoImage());
//		assertEquals(eventDesignDetailData.getSponsorSection(), eventDesignSettingsDto.getSponsorSection());
//		assertEquals(eventDesignDetailData.getEventTagLine(), eventDesignSettingsDto.getEventTagLine());
//		assertEquals(eventDesignDetailData.getDisplayBackgroundColor(), eventDesignSettingsDto.getDisplayBackgroundColor());
//		assertEquals(eventDesignDetailData.getTrackingScript(), eventDesignSettingsDto.getTrackingScript());
//		assertEquals(eventDesignDetailData.getThemeId(), eventDesignSettingsDto.getThemeId());
//	}

//	@Test
	void test_updateDesign_throwExceptionFOUND_UNKNOWN_ORGANIZER() {

        virtualEventSetting =new VirtualEventSettingsDTO();

		//setup
		whiteLabel = new WhiteLabel();
		User user1 = new User();
		user1.setUserId(id);
		Organizer organizer1 = new Organizer();
		organizer1.setCreatedBy(user1);
		organizer = new Organizer();
		organizer.setCreatedBy(user1);
		event.setName("testEvent1");
		event.setEventURL("testEvent");
		event.setWhiteLabel(whiteLabel);
		event.setOrganizer(organizer1);
		boolean generateUrlByName = false;
		eventDesignSettingsDto = getEventDesignSettingsDto("", "TestEventOrganizer");
        eventDesignSettingsDto.setOrganizerId(1L);
        eventDesignSettingsDto.setOrganizerName("dummy");
        eventDesignSettingsDto.setOrganizerPageURL("dummy");

		eventChecklist = new EventChecklist();
		eventChecklist.setNamingCompleted(true);
		eventChecklist.setEventURLCustomized(false);
		long eventCount = 0L;
		eventDesignDetail = new EventDesignDetail();
		staff = new Staff();
		staff.setRole(StaffRole.staff);
		staffDetailDto = new StaffDetailDto(user, staff);
		staffDetailDto.setRole(StaffRole.whitelabeladmin.name());
		List<StaffDetailDto> staffDetailDtoList = new ArrayList<>();
		staffDetailDtoList.add(staffDetailDto);

		getJoinEventWithDealProductData();

		//mock
		when(joinEventWithPDProductService.findByEvent(any())).thenReturn(Optional.of(joinEventWithDealProduct));
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
		Mockito.doReturn(url).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
		when(eventDesignDetailService.findByEvent(any())).thenReturn(eventDesignDetail);
		when(organizerService.getOrganizerByURL(anyString())).thenReturn(organizer);
		when(staffService.getStaffList(any())).thenReturn(staffDetailDtoList);
		when(staffService.findStaffDtoByWhiteLabel(any())).thenReturn(staffDetailDtoList);
		doNothing().when(organizerService).addJoinUsersWithOrganizers(any(), any(), any(), anyBoolean());
        doNothing().when(chargebeeService).setOrganizerInEventPlanConfig(event, organizer);
        when(chargebeePlanService.findByPlanName(any())).thenReturn(Optional.of(chargebeePlan));
        when(virtualEventService.getVirtualEventSettingsById(any())).thenReturn(virtualEventSetting);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.updateDesign(event, eventDesignSettingsDto, generateUrlByName, user)
        );

		//Assertion
		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.FOUND_UNKNOWN_ORGANIZER.getDeveloperMessage(), exception.getMessage());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isEventURLCustomized());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepository, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventURL(), eventDesignSettingsDto.getEventUrl().concat("1"));
		assertEquals(eventData.getOrganizer().getCreatedBy().getUserId(), user.getUserId());
		assertEquals(eventData.getName(), eventDesignSettingsDto.getEventName());

		ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
		verify(eventDesignDetailService, times(1)).save(eventDesignDetailArgumentCaptor.capture());

		EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
		assertEquals(eventDesignDetailData.getDesc(), eventDesignSettingsDto.getDesc());
		assertEquals(eventDesignDetailData.getLogoImage(), eventDesignSettingsDto.getLogoImage());
		assertEquals(eventDesignDetailData.getSponsorSection(), eventDesignSettingsDto.getSponsorSection());
		assertEquals(eventDesignDetailData.getEventTagLine(), eventDesignSettingsDto.getEventTagLine());
		assertEquals(eventDesignDetailData.getDisplayBackgroundColor(), eventDesignSettingsDto.getDisplayBackgroundColor());
		assertEquals(eventDesignDetailData.getTrackingScript(), eventDesignSettingsDto.getTrackingScript());
		assertEquals(eventDesignDetailData.getThemeId(), eventDesignSettingsDto.getThemeId());
	}

	@Test
	void test_getCountryCode(){

		//setup
		String countryCode = CountryCode.US.name();

		//Execution
		CountryCode countryCodeData = eventServiceImpl.getCountryCode(countryCode);

		//Assertion
		assertEquals(countryCodeData.toString(), countryCode);
	}

	@Test
	void test_getCountryCode_throwExceptionNOT_VALID_COUNTRY_CODE(){

		//setup
		String countryCode = "INDIA";

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.getCountryCode(countryCode)
        );

        assertEquals(NotFoundException.NotFound.NOT_VALID_COUNTRY_CODE.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_disconnectPaymentGateway_throwExceptionSTRIPE_ACCOUNT_IS_NOT_CONNECTED_TO_EVENT() {

		//setup
        user.setUserId(1L);
        staff.setUser(user);
        staff.setUserId(user.getUserId());
		stripe.setId(id);
		stripe.setAccessToken("");
        stripe.setStaff(staff);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.disconnectPaymentGateway(event, false, user)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.STRIPE_ACCOUNT_IS_NOT_CONNECTED_TO_EVENT.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_disconnectPaymentGateway_throwExceptionYOU_ARE_NOT_AUTHORIZED_DISCONNECT_PAYMENT_PROCESSOR() {

		//setup
        staffUser = new User();
        staffUser.setUserId(2L);
        staff.setUser(staffUser);
        staff.setUserId(staffUser.getUserId());
		stripe.setId(id);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
        stripe.setStaff(staff);
        user.setUserId(1L);

		donationSetting = new DonationSettings();
		eventChecklist = new EventChecklist();

		getJoinEventWithDealProductData();

		//mock

		when(roStripeService.findByEvent(any())).thenReturn(stripe);

		//Execution
		Exception exception = assertThrows(ForbiddenException.class,
                () -> eventServiceImpl.disconnectPaymentGateway(event, false, user)
        );

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
        assertEquals(ForbiddenException.UserForbiddenExceptionMsg.YOU_ARE_NOT_AUTHORIZED_DISCONNECT_PAYMENT_PROCESSOR.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_disconnectPaymentGateway_withAccessToken() {

		//setup
        user.setUserId(1L);
        staff.setUser(user);
        staff.setUserId(user.getUserId());
		stripe.setId(id);
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
        stripe.setStaff(staff);

		donationSetting = new DonationSettings();
		eventChecklist = new EventChecklist();

		getJoinEventWithDealProductData();

		//mock

		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);
		when(paymentService.isCustomerCreatedForEvent(any())).thenReturn(false);
        doNothing().when(stripeCustomerService).updateStripeCustomerRecStatus(any(), any());

		//Execution
		eventServiceImpl.disconnectPaymentGateway(event, true, user);

		//Assertion
		verify(roStripeService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());
		verify(eventChecklistService, times(1)).findByEvent(any());

		ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
		verify(stripeService, times(1)).save(stripeArgumentCaptor.capture());
		Stripe stripeData = stripeArgumentCaptor.getValue();
		assertFalse(stripeData.isDefaultAccount());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());
		Event eventData = eventArgumentCaptor.getValue();
		assertFalse(eventData.isCreditCardEnabled());

		ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
		verify(donationSettingsService, times(1)).save(donationSettingsArgumentCaptor.capture());
		DonationSettings donationSettingData = donationSettingsArgumentCaptor.getValue();
		assertTrue(donationSettingData.isAbsorbFee());

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());
		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertFalse(eventChecklistData.isActivatePaymentProcessing());
	}

	@Test
	void test_connectStripe() {

		//setup
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		String url = stripeConfiguration.getAuthorize_URI() + "?response_type=code&scope=read_write&client_id="
				+ stripeConfiguration.getClientID() + "&state=" + accessToken;
		String response = "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";

		//Execution
		String responseData = eventServiceImpl.connectStripe(event, user,accessToken);

		//Assertion
		assertEquals(responseData, response);
	}

	@Test
	void test_connectSquare_throwExceptionSQUARE_CONNECT_NOT_AVAILABLE_FOR_WL() {

		//setup
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		whiteLabel = new WhiteLabel();
		event.setWhiteLabel(whiteLabel);

        //mock
        when(transactionFeeConditionalLogicService.isWlAFeeExists(event)).thenReturn(true);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.connectSquare(event, user, accessToken)
        );
        assertEquals(SQUARE_CONNECT_NOT_AVAILABLE_FOR_WL.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_connectSquare_throwExceptionCONNECTED_TO_OTHER_PAYMENT_GATEWAY() {

		//setup
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		stripe.setEmail(email);
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

		//mock
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.connectSquare(event, user, accessToken)
        );

		//Assertion
		verify(stripeService, times(1)).findByEventWithEmailDisplayName(any());
        assertEquals(CONNECTED_TO_OTHER_PAYMENT_GATEWAY.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_connectSquare_withSquare() throws UnsupportedEncodingException {

		//setup
		String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());

		String url = squareConfiguration.getAuthorize_URI() + "?client_id=" + squareConfiguration.getClientID()
				+ "&scope=" + URLEncoder.encode(squareScope, "UTF-8") + "&state=" + accessToken;

		String response = "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";

		//mock
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);

		//Execution
		String responseData = eventServiceImpl.connectSquare(event, user, accessToken);

		//Assertion
		verify(stripeService, times(1)).findByEventWithEmailDisplayName(any());

		assertEquals(responseData, response);
	}

	@Test
	void test_connectSquare_withSquareAndStripeIdNull() throws UnsupportedEncodingException {

		//setup
        String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());
        String url = squareConfiguration.getAuthorize_URI() + "?client_id=" + squareConfiguration.getClientID()
                + "&scope=" + URLEncoder.encode(squareScope, "UTF-8") + "&state=" + accessToken;
        String response = "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";

		//mock
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);



		//Execution
		String responseData = eventServiceImpl.connectSquare(event, user, accessToken);

		//Assertion
		verify(stripeService, times(1)).findByEventWithEmailDisplayName(any());

		assertEquals(responseData, response);
	}

	@Test
	void test_connectSquare_withSquareAndStripeId() throws UnsupportedEncodingException {

		//setup
        String squareScope = "MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE SETTLEMENTS_READ PAYMENTS_WRITE_IN_PERSON PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS";
		String accessToken = "sk_test_FStPmAcubsrj3FP1hRS2Ge6w";
		stripe.setPaymentGateway(EnumPaymentGateway.SQUARE.value());
		stripe.setId(id);
		stripe.setEmail(email);
        String url = squareConfiguration.getAuthorize_URI() + "?client_id=" + squareConfiguration.getClientID()
                + "&scope=" + URLEncoder.encode(squareScope, "UTF-8") + "&state=" + accessToken;
        String response = "{\"stripeConnectUrl\":\"" + url + "\",\"redirecttostripe\":\"true\"}";

		//mock
		when(stripeService.findByEventWithEmailDisplayName(any())).thenReturn(stripe);


		//Execution
		String responseData = eventServiceImpl.connectSquare(event, user, accessToken);

		//Assertion
		verify(stripeService, times(1)).findByEventWithEmailDisplayName(any());

		assertEquals(responseData, response);
	}

	@Test
	void test_authorizeStripe_throwExceptionSTRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT() throws IOException, URISyntaxException {

		//setup
		String code = "ac_9m0C5qgFhTuuy8rLCjUVmoWX3r7LRWGn";

		//mock
		when(stripeService.findByCode(anyString())).thenReturn(null);

		//Execution
		try {
			eventServiceImpl.authorizeStripe(code, event, user, stripe);
		}catch (Exception e){
			assertEquals(e.getMessage(), "Error during connecting please try after some time!");
		}
	}

	@Test
	void test_authorizeStripe_throwExceptionEVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED() throws IOException, URISyntaxException {

		//setup
		String code = "ac_9m0C5qgFhTuuy8rLCjUVmoWX3r7LRWGn";

		//mock
		when(stripeService.findByCode(anyString())).thenReturn(stripe);

		//Execution
		try {
			eventServiceImpl.authorizeStripe(code, event, user, stripe);
		}catch (Exception e){
			assertEquals(e.getMessage(), "Token is already used");
		}
	}

	//@Test
	void test_authorizeSquare_throwExceptionSTRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT() throws IOException, URISyntaxException, com.squareup.square.exceptions.ApiException, UnirestException, JSONException {

		//setup
		String code = "sq0cgp-H8kKMUZrbgM5OOZnRPfIZw";

		//mock
		when(stripeService.findByCode(anyString())).thenReturn(null);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.authorizeSquare(code, event, user)
        );

        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.STRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_authorizeSquare_throwExceptionEVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED() throws IOException, URISyntaxException, com.squareup.square.exceptions.ApiException, UnirestException, JSONException {

		//setup
		String code = "sq0cgp-H8kKMUZrbgM5OOZnRPfIZw";

		//mock
		when(stripeService.findByCode(anyString())).thenReturn(stripe);

		//Execution
		Exception exception = assertThrows(ConflictException.class,
                () -> eventServiceImpl.authorizeSquare(code, event, user)
        );
        assertEquals(ConflictException.UserExceptionConflictMsg.EVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_autoActivateAndEnableTicketing_withActivatedFalseAndTicketingEnabledTrue(){

		//setup
		ticketing.setActivated(false);
		event.setTicketingEnabled(true);
		getJoinEventWithDealProductData();

		//mock

		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

		//Execution
		eventServiceImpl.autoActivateAndEnableTicketing(event);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
		ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
		verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

		Ticketing ticketingData = ticketingArgumentCaptor.getValue();
		assertTrue(ticketingData.getActivated());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData  =eventArgumentCaptor.getValue();
		assertTrue(eventData.isTicketingEnabled());
	}

	public static Object[] isTicketingActivatedAndTicketingEnabled(){
		return new Object[]{
				new Object[]{true, true},
				new Object[]{false, false},
				new Object[]{false, true},
		};
	}

	@ParameterizedTest
	@MethodSource("isTicketingActivatedAndTicketingEnabled")
	void test_autoActivateAndEnableTicketing_withActivatedTrueAndTicketingEnabledTrue(boolean activated, boolean ticketingEnable){

		//setup
		ticketing.setActivated(activated);
		event.setTicketingEnabled(ticketingEnable);

		//mock
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

		//Execution
		eventServiceImpl.autoActivateAndEnableTicketing(event);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());
	}

	@Test
	void test_updateEventUrl_withEventURLCustomizedFalseAndEventNameAndEventUrlAreDifferent() {

		//setup
		event.setName("TestEvent1");
		String eventUrl = "TestEvent";
		getJoinEventWithDealProductData();
		eventChecklist = new EventChecklist();
		eventChecklist.setEventURLCustomized(false);

		//mock

		Mockito.doReturn(eventUrl).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		Event eventUrlData = eventServiceImpl.updateEventUrl(eventUrl, event, null);

		//Assertion
		assertEquals(eventUrlData.getEventURL(), eventUrl);
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventURL(), eventUrl);

		ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
		verify(eventChecklistService, times(1)).save(eventChecklistArgumentCaptor.capture());

		EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
		assertTrue(eventChecklistData.isEventURLCustomized());
	}

	@Test
	void test_updateEventUrl_withEventURLCustomizedTrueAndEventNameAndEventUrlAreDifferent() {

		//setup
		event.setName("TestEvent1");
		String eventUrl = "TestEvent";
		getJoinEventWithDealProductData();
		eventChecklist = new EventChecklist();
		eventChecklist.setEventURLCustomized(true);

		//mock

		Mockito.doReturn(eventUrl).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());
		when(eventChecklistService.findByEvent(any())).thenReturn(eventChecklist);

		//Execution
		Event eventUrlData = eventServiceImpl.updateEventUrl(eventUrl, event, null);

		//Assertion
		assertEquals(eventUrlData.getEventURL(), eventUrl);
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventURL(), eventUrl);
	}

	@Test
	void test_updateEventUrl_withEventNameAndEventUrlAreSame() {

		//setup
		event.setName("test-event");
		String eventUrl = "test-event";
		getJoinEventWithDealProductData();

		//mock

		Mockito.doReturn(eventUrl).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());

		//Execution
		Event eventUrlData = eventServiceImpl.updateEventUrl(eventUrl, event, null);

		//Assertion
		assertEquals(eventUrlData.getEventURL(), eventUrl);
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventURL(), eventUrl);
	}

	@Test
	void test_updateEventUrl_withEventUrlEmpty() {

		//setup
		event.setName("test-event");
		String eventUrl = "";
		getJoinEventWithDealProductData();

		//mock

		Mockito.doReturn(eventUrl).when(eventServiceImpl).getEventUrl(anyString(), anyInt(), anyInt(), anyBoolean(), any());

		//Execution
		Event eventUrlData = eventServiceImpl.updateEventUrl(eventUrl, event, null);

		//Assertion
		assertEquals(eventUrlData.getEventURL(), eventUrl);
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventURL(), eventUrl);
	}

	@Test
	void test_isRaffleTicketAvailble_withRaffleNull() {

		//mock
		when(raffleService.findByEvent(any())).thenReturn(null);

		//Execution
		boolean raffleTicketAvailable = eventServiceImpl.isRaffleTicketAvailble(event);

		//Assertion
		assertFalse(raffleTicketAvailable);
	}

	@Test
	void test_isRaffleTicketAvailble_withRaffleAndAvailableTicketsLessThanZero() {

		//setup
		raffle = new Raffle();
		raffle.setAvailableTickets(0L);

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);

		//Execution
		boolean raffleTicketAvailable = eventServiceImpl.isRaffleTicketAvailble(event);

		//Assertion
		assertTrue(raffleTicketAvailable);
	}

	@Test
	void test_isRaffleTicketAvailble_withRaffleAndAvailableTicketsGreaterThanZero() {

		//setup
		raffle = new Raffle();
		raffle.setAvailableTickets(10L);
		long totalTicketsSale = 5L;

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(purchasedRaffleTicketService.getTotalTicketsByRaffle(anyLong())).thenReturn(totalTicketsSale);

		//Execution
		boolean raffleTicketAvailable = eventServiceImpl.isRaffleTicketAvailble(event);

		//Assertion
		assertTrue(raffleTicketAvailable);
	}

	@Test
	void test_isRaffleTicketAvailble_withRaffleAndAvailableTicketsGreaterThanZero1() {

		//setup
		raffle = new Raffle();
		raffle.setAvailableTickets(10L);
		long totalTicketsSale = 10L;

		//mock
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(purchasedRaffleTicketService.getTotalTicketsByRaffle(anyLong())).thenReturn(totalTicketsSale);

		//Execution
		boolean raffleTicketAvailable = eventServiceImpl.isRaffleTicketAvailble(event);

		//Assertion
		assertFalse(raffleTicketAvailable);
	}

	@Test
	void test_enableBidderCreditCardRegistration_withEnablefalse() {

		//setup
		boolean enableBidderRegistration = false;
		getJoinEventWithDealProductData();

		//mock


		//Execution
		eventServiceImpl.enableBidderCreditCardRegistration(event, enableBidderRegistration);
	}

	@Test
	void test_enableBidderCreditCardRegistration_withEnableTrueAndPaymentAndFindByEventAndUserNull() {

		//setup
		boolean enableBidderRegistration = true;
		getJoinEventWithDealProductData();
		payment = new Payment();
		payment.setUserId(id);
		payment.setTokenCreatedTime(new Date());
		List<Payment> paymentList =  new ArrayList<>();
		paymentList.add(payment);
		bidderNumber = new BidderNumber();
		bidderNumber.setBidderNumber(1L);

		//mock

		when(paymentService.findByEventIdOrderByUserIdAsc(anyLong())).thenReturn(paymentList);
		when(bidderNumberService.findOneByEventIdDesc(any())).thenReturn(bidderNumber);
		when(bidderNumberService.findByEventAndUser(any(), anyLong())).thenReturn(null);

		//Execution
		eventServiceImpl.enableBidderCreditCardRegistration(event, enableBidderRegistration);
	}

	@Test
	void test_enableBidderCreditCardRegistration_withEnableTrueAndPaymentAndLastBidderNumberNullAndFindByEventAndUser() {

		//setup
		boolean enableBidderRegistration = true;
		getJoinEventWithDealProductData();
		payment = new Payment();
		payment.setUserId(id);
		payment.setTokenCreatedTime(new Date());
		List<Payment> paymentList =  new ArrayList<>();
		paymentList.add(payment);
		bidderNumber = new BidderNumber();

		//mock

		when(paymentService.findByEventIdOrderByUserIdAsc(anyLong())).thenReturn(paymentList);
		when(bidderNumberService.findOneByEventIdDesc(any())).thenReturn(null);
		when(bidderNumberService.findByEventAndUser(any(), anyLong())).thenReturn(bidderNumber);

		//Execution
		eventServiceImpl.enableBidderCreditCardRegistration(event, enableBidderRegistration);
	}

	@Test
	void test_enableBidderCreditCardRegistration_withEnableTrueAndPaymentEmpty() {

		//setup
		boolean enableBidderRegistration = true;
		getJoinEventWithDealProductData();
		List<Payment> paymentList =  new ArrayList<>();

		//mock

		when(paymentService.findByEventIdOrderByUserIdAsc(anyLong())).thenReturn(paymentList);

		//Execution
		eventServiceImpl.enableBidderCreditCardRegistration(event, enableBidderRegistration);
	}

	@Test
	void test_enableBidderCreditCardRegistration_withEnableTrueAndPaymentNull() {

		//setup
		boolean enableBidderRegistration = true;
		getJoinEventWithDealProductData();

		//mock

		when(paymentService.findByEventIdOrderByUserIdAsc(anyLong())).thenReturn(null);

		//Execution
		eventServiceImpl.enableBidderCreditCardRegistration(event, enableBidderRegistration);
	}

	@Test
	void test_enableCustomMessaging_withEnableTrue() {

		//setup
		boolean enable = true;
		getJoinEventWithDealProductData();

		//mock

		when(raffleCustomSmsService.saveEventMessages(any())).thenReturn(true);
		when(auctionCustomSmsService.saveEventMessages(any())).thenReturn(true);
		when(causeAuctionCustomSmsService.saveEventMessages(any())).thenReturn(true);
		when(donationCustomSmsService.saveEventMessages(any())).thenReturn(true);

		//Execution
		eventServiceImpl.enableCustomMessaging(event, enable);

		//Assertion
		verify(raffleCustomSmsService, times(1)).saveEventMessages(any());
		verify(auctionCustomSmsService, times(1)).saveEventMessages(any());
		verify(causeAuctionCustomSmsService, times(1)).saveEventMessages(any());
		verify(donationCustomSmsService, times(1)).saveEventMessages(any());

	}

	@Test
	void test_enableCustomMessaging_withEnableFalse() {

		//setup
		boolean enable = false;
		getJoinEventWithDealProductData();

		//mock

		doNothing().when(raffleCustomSmsService).removeEventFromCache(anyLong());
		doNothing().when(auctionCustomSmsService).removeEventFromCache(anyLong());
		doNothing().when(causeAuctionCustomSmsService).removeEventFromCache(anyLong());
		doNothing().when(donationCustomSmsService).removeEventFromCache(anyLong());

		//Execution
		eventServiceImpl.enableCustomMessaging(event, enable);

		//Assertion
		verify(raffleCustomSmsService, times(1)).removeEventFromCache(anyLong());
		verify(auctionCustomSmsService, times(1)).removeEventFromCache(anyLong());
		verify(causeAuctionCustomSmsService, times(1)).removeEventFromCache(anyLong());
		verify(donationCustomSmsService, times(1)).removeEventFromCache(anyLong());

	}

	@Test
	void test_getEventsBillingInfo_withBillingList() {

		//setup
		Object[] billingDetails = {BigInteger.ONE, event.getEventURL(), ModuleType.AUCTION.name(), BigInteger.TEN, new Date(), true};
		List<Object[]> billingList = new ArrayList<>();
		billingList.add(billingDetails);

		//mock
		when(eventRepository.findBillingForEvent(any())).thenReturn(billingList);

		//Execution
		List<EventBillingDto> eventBillingDtoData = eventServiceImpl.getEventsBillingInfo(new Date());

		//Assertion
		verify(eventRepository, times(1)).findBillingForEvent(any());
		assertEquals(eventBillingDtoData.get(0).getActivated(), billingDetails[5]);
		assertEquals(eventBillingDtoData.get(0).getEndDate(), billingDetails[4]);
		assertEquals(eventBillingDtoData.get(0).getUserCount(), billingDetails[3]);
		assertEquals(eventBillingDtoData.get(0).getModuleType(), billingDetails[2]);
		assertEquals(eventBillingDtoData.get(0).getEventUrl(), billingDetails[1]);
		assertEquals(eventBillingDtoData.get(0).getEventId(),  ((BigInteger)(billingDetails[0])).longValue());
	}

	@Test
	void test_getEventsBillingInfo_withBillingListEmpty() {

		//setup
		List<Object[]> billingList = new ArrayList<>();

		//mock
		when(eventRepository.findBillingForEvent(any())).thenReturn(billingList);

		//Execution
		List<EventBillingDto> eventBillingDtoData = eventServiceImpl.getEventsBillingInfo(new Date());

		//Assertion
		verify(eventRepository, times(1)).findBillingForEvent(any());
		assertTrue(eventBillingDtoData.isEmpty());
	}

	@Test
	void test_getEventsBillingInfo_withBillingListNull() {

		//mock
		when(eventRepository.findBillingForEvent(any())).thenReturn(null);

		//Execution
		List<EventBillingDto> eventBillingDtoData = eventServiceImpl.getEventsBillingInfo(new Date());

		//Assertion
		verify(eventRepository, times(1)).findBillingForEvent(any());
		assertTrue(eventBillingDtoData.isEmpty());
	}

	public static Object[] getModuleEnable1(){
		return new Object[]{
				new Object[]{true, true, true, true, true},
				new Object[]{false, false, false, false, false},
				new Object[]{true, false, false, true, false},
				new Object[]{false, true, false, true, false},
				new Object[]{false, false, true, true, true},
				new Object[]{false, false, false, true, true},
		};
	}

	@ParameterizedTest
	@MethodSource("getModuleEnable1")
	void test_updateEventModules_withTicketingFalse(Boolean auctionEnabled, Boolean raffleEnabled, Boolean causeEnabled, Boolean textToGiveEnabled, Boolean donationEnabled){

		//setup
		getJoinEventWithDealProductData();
		Boolean ticketingEnabled = false;

		//mock


		//Execution
		eventServiceImpl.updateEventModules(event, auctionEnabled, raffleEnabled, causeEnabled, ticketingEnabled, textToGiveEnabled, donationEnabled,user);

		//Assertion
		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.isSilentAuctionEnabled(), auctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.isCauseAuctionEnabled(), causeEnabled);
		assertEquals(eventData.isTicketingEnabled(), ticketingEnabled);
		assertEquals(eventData.isTextToGiveEnabled(), textToGiveEnabled);
	}

	@Test
	void test_updateEventModules_withTicketingEnableTrue() {

		//setup
		Boolean auctionEnabled = true;
		Boolean raffleEnabled = true;
		Boolean causeEnabled = true;
		Boolean ticketingEnabled = true;
		Boolean textToGiveEnabled = true;
		Boolean donationEnabled = true;

		getJoinEventWithDealProductData();

		//mock

		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
        when(confirmationPagesService.createConfirmationPage(event,user.getUserId(),null,true)).thenReturn(new ResponseDto(SUCCESS, SUCCESS));
        when(confirmationEmailService.createCustomEmailTemplate(event,user.getUserId(),null,true,TemplateType.CONFIRMATION_EMAIL)).thenReturn(new ResponseDto(SUCCESS, SUCCESS));
        when(customTicketInvoiceDesignService.createCustomDesign( eq(event),eq(user.getUserId()),any(CustomTemplatesDto.class),eq(true),eq(TemplateType.TICKET_PDF_DESIGN))).thenReturn(new ResponseDetailDto(SUCCESS, SUCCESS,1L));
		//Execution
		eventServiceImpl.updateEventModules(event, auctionEnabled, raffleEnabled, causeEnabled, ticketingEnabled, textToGiveEnabled, donationEnabled, user);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.isSilentAuctionEnabled(), auctionEnabled);
		assertEquals(eventData.isRaffleEnabled(), raffleEnabled);
		assertEquals(eventData.isCauseAuctionEnabled(), causeEnabled);
		assertEquals(eventData.isTicketingEnabled(), ticketingEnabled);
		assertEquals(eventData.isTextToGiveEnabled(), textToGiveEnabled);
	}

	@Test
	void test_getSubscribedStripePlans_withEventIds() {

		//setup
		List<Long> eventIds = new ArrayList<>();
		eventIds.add(id);
		stripeSubscriptionDto = new StripeSubscriptionDto();
		stripeSubscriptionDto.setAmount(100d);
		List<StripeSubscriptionDto> stripeSubscriptionDtoList = new ArrayList<>();
		stripeSubscriptionDtoList.add(stripeSubscriptionDto);

		//mock
		when(paymentService.findDistinctEventIdByUserId(anyLong())).thenReturn(eventIds);
        when(roEventService.getEventById(anyLong())).thenReturn(event);
		Mockito.doReturn(stripeSubscriptionDtoList).when(eventServiceImpl).getSubscriptionPlansForUser(any(), any());

		//Execution
		List<UserStripeSubscription> userStripeSubscriptionData = eventServiceImpl.getSubscribedStripePlans(user);

		//Assertion
		verify(paymentService, times(1)).findDistinctEventIdByUserId(anyLong());
		assertEquals(userStripeSubscriptionData.get(0).getEventUrl(), event.getEventURL());
		assertTrue(userStripeSubscriptionData.get(0).getSubscriptions().get(0).getAmount() == stripeSubscriptionDto.getAmount());
	}

	@Test
	void test_getSubscribedStripePlans_withEventIdsEmpty() {

		//mock
		when(paymentService.findDistinctEventIdByUserId(anyLong())).thenReturn(null);

		//Execution
		List<UserStripeSubscription> userStripeSubscriptionData = eventServiceImpl.getSubscribedStripePlans(user);

		//Assertion
		verify(paymentService, times(1)).findDistinctEventIdByUserId(anyLong());
		assertTrue(userStripeSubscriptionData.isEmpty());
	}

	@Test
	void test_getSubscriptionPlansForUser() throws StripeException {

		//setup
		stripe.setAccessToken("sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		payment = new Payment();
		payment.setStripeToken(stripe.getAccessToken());
		stripeSubscriptionDto = new StripeSubscriptionDto();
		stripeSubscriptionDto.setAmount(100d);
		List<StripeSubscriptionDto> stripeSubscriptionDtoList = new ArrayList<>();
		stripeSubscriptionDtoList.add(stripeSubscriptionDto);

        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.value());

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));
		when(stripePaymentService.getCustomerSubscriptions(anyString(), anyString(), anyString())).thenReturn(stripeSubscriptionDtoList);

		//Execution
		List<StripeSubscriptionDto> stripeSubscriptionDtoData = eventServiceImpl.getSubscriptionPlansForUser(event, user);

		//Assertion
		assertTrue(stripeSubscriptionDtoData.get(0).getAmount() == stripeSubscriptionDto.getAmount());
	}

	@Test
	void test_getSubscriptionPlansForUser_withStripeNullAndPaymentOptionalEmpty() {

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		List<StripeSubscriptionDto> stripeSubscriptionDtoData = eventServiceImpl.getSubscriptionPlansForUser(event, user);

		//Assertion
		assertTrue(stripeSubscriptionDtoData.isEmpty());
	}

	@Test
	void test_getSubscriptionPlansForUser_withStripeAndPaymentOptionalEmpty() {

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.empty());

		//Execution
		List<StripeSubscriptionDto> stripeSubscriptionDtoData = eventServiceImpl.getSubscriptionPlansForUser(event, user);

		//Assertion
		assertTrue(stripeSubscriptionDtoData.isEmpty());
	}

	@Test
	void test_getSubscriptionPlansForUser_withStripeNullAndPaymentOptional() {

		//setup
		payment = new Payment();
		payment.setStripeToken(stripe.getAccessToken());

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		List<StripeSubscriptionDto> stripeSubscriptionDtoData = eventServiceImpl.getSubscriptionPlansForUser(event, user);

		//Assertion
		assertTrue(stripeSubscriptionDtoData.isEmpty());
	}

	public static Object[]  getAccessTokenAndStripeToken(){
		return new Object[]{
				new Object[]{"", ""},
				new Object[]{"sk_test_FStPmAcubsrj3FP1hRS2Ge6w", ""},
				new Object[]{"", "sk_test_FStPmAcubsrj3FP1hRS2Ge6w"},
		};
	}
	@ParameterizedTest
	@MethodSource("getAccessTokenAndStripeToken")
	void test_getSubscriptionPlansForUser_withApiKeyEmptyAndCustomerIdEmpty(String accessToken, String stripeToken) {

		//setup
		stripe.setAccessToken(accessToken);
		payment = new Payment();
		payment.setStripeToken(stripeToken);

		//mock
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
		when(paymentService.findByUserIdAndEventId(anyLong(), anyLong())).thenReturn(Optional.of(payment));

		//Execution
		List<StripeSubscriptionDto> stripeSubscriptionDtoData = eventServiceImpl.getSubscriptionPlansForUser(event, user);

		//Assertion
		assertTrue(stripeSubscriptionDtoData.isEmpty());
	}

	@Test
	void test_unSubscribeTextToGive() throws StripeException {

		//setup
		String subscriptionId = "1";
		String unSubscribeMessage = Constants.STRIPE_PLAN_UNSUBSCRIBED;

		//mock
		when(stripeService.unSubscribeStripePlan(anyString())).thenReturn(unSubscribeMessage);

		//Execution
		String unsubscribeMessage = eventServiceImpl.unSubscribeTextToGive(subscriptionId, event);

		//Assertion
		assertEquals(unsubscribeMessage, unSubscribeMessage);
	}

	@Test
	void test_getShowButtonSetting_AllModuleActivatedExceptDonation() {

		//setup
		ticketing = getTicketing(true);
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, true, true, true, true, false);
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
		when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		assertNotNull(hostActiveButtonSettingData);
		assertFalse(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenCreditCardPopup());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertNull(hostActiveButtonSettingData.getPopupMessage());
		assertNull(hostActiveButtonSettingData.getRedirectURL());

		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());
	}

	@Test
	void test_getShowButtonSetting_AllModuleActivatedExceptDonation12() {

		//setup
		ticketing = getTicketing(true);
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, true, true, true, true, false);

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		assertNotNull(hostActiveButtonSettingData);
		assertFalse(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenCreditCardPopup());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertNull(hostActiveButtonSettingData.getPopupMessage());
		assertNull(hostActiveButtonSettingData.getRedirectURL());

		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

	}

	@Test
	void test_getShowButtonSetting_withDonationEnabledAndStripeNull() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(false, false, false, false, false, true);
		String message = "Please finish setting up Stripe payment processing to start collecting donations.";

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
		when(roStripeService.findByEvent(any())).thenReturn(null);
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertEquals(hostActiveButtonSettingData.getRedirectURL(), "/host/settings/credit-card");
		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertTrue(hostActiveButtonSettingData.isOpenCreditCardPopup());
		assertEquals(hostActiveButtonSettingData.getPopupMessage(), message);
	}

	@Test
	void test_getShowButtonSetting_withDonationEnabledAndStripe() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(false, false, false, false, false, true);
		stripe.setId(id);

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
		when(roStripeService.findByEvent(any())).thenReturn(stripe);
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertFalse(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenCreditCardPopup());
	}

	@Test
	void test_getShowButtonSetting_withAllModuleDisabledExceptTicketingWithFreeTicketType() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, false, false, false, false, false);

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);

        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
	}

	@Test
	void test_getShowButtonSetting_withAllModuleDisabledExceptTicketingAndIsAnyTicketExistsWithAmountGreaterThanZeroTrue() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, false, false, false, false, false);
		String message = "Please finish setting up Stripe payment processing to start selling tickets.";

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);
		when(roStripeService.findByEvent(any())).thenReturn(null);
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(any())).thenReturn(true);
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertEquals(hostActiveButtonSettingData.getPopupMessage(), message);
	}

	@Test
	void test_getShowButtonSetting_withAllModuleDisabledExceptTicketingAndIsAnyBaseModuleEnabledTrue() {

		//setup
		ticketing = getTicketing(true);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, false, false, false, false, true);

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);


		Mockito.doReturn(true).when(eventServiceImpl).isAnyBaseModuleEnabled(any());
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		assertNull(hostActiveButtonSettingData.getRedirectURL());
		assertNull(hostActiveButtonSettingData.getPopupMessage());
		assertFalse(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertFalse(hostActiveButtonSettingData.isOpenCreditCardPopup());

		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

	}

	@Test
	void test_getShowButtonSetting1() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(false);
		causeAuction = getCauseAuction(false);
		donationSetting = getDonationSetting(false);
		event = getEventData(true, true, true, true, true, true);

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);

        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertEquals(hostActiveButtonSettingData.getRedirectURL(), "/host/settings/account");
	}

	@Test
	void test_getShowButtonSetting_withAllModuleDisabledExceptTicketingAndIsAnyBaseModuleEnabledTrue1() {

		//setup
		ticketing = getTicketing(false);
		auction = getAuction(false);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, false, false, false, false, false);
		String message = "Please confirm to activate Ticketing module.";

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);

		Mockito.doReturn(true).when(eventServiceImpl).isAnyBaseModuleEnabled(any());
        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertTrue(hostActiveButtonSettingData.isShowActivateButton());
		assertTrue(hostActiveButtonSettingData.isOpenTicketingPopup());
		assertEquals(hostActiveButtonSettingData.getPopupMessage(), message);
	}

	@Test
	void test_getShowButtonSetting() {

		//setup
		ticketing = getTicketing(true);
		auction = getAuction(true);
		raffle = getRaffle(true);
		causeAuction = getCauseAuction(true);
		donationSetting = getDonationSetting(true);
		event = getEventData(true, true, true, true, true, true);

		String message = "Please finish setting up Stripe payment processing to start collecting donations.";

		//mock
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(donationSettingsService.getByEventId(anyLong())).thenReturn(donationSetting);

        when(roEventService.getLanguageCodeByUserOrEvent(any(), any())).thenReturn("EN");
        Map<String,String> languageMap = CommonUtil.getMessageLanguageMap();

		//Execution
		HostActiveButtonSettings hostActiveButtonSettingData = eventServiceImpl.getShowButtonSetting(event,user,languageMap);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(donationSettingsService, times(1)).getByEventId(anyLong());

		assertNull(hostActiveButtonSettingData.getRedirectURL());
		assertFalse(hostActiveButtonSettingData.isShowActivateButton());
		assertFalse(hostActiveButtonSettingData.isOpenCreditCardPopup());
	}

	public static Object[] getModuleEnable(){
		return new Object[]{
				new Object[]{true, true, true, true, true},
				new Object[]{false, false, false, false, false},
				new Object[]{true, false, false, false, true},
				new Object[]{false, true, false, false, true},
				new Object[]{false, false, true, false, true},
				new Object[]{false, false, false, true, true},
		};
	}

	@ParameterizedTest
	@MethodSource("getModuleEnable")
	void isAnyBaseModuleEnabled(boolean isSilentAuctionEnabled, boolean isRaffleEnabled, boolean isCauseAuctionEnabled, boolean isTextToGiveEnabled, boolean anyBaseModuleEnable){

		//setup
		event.setSilentAuctionEnabled(isSilentAuctionEnabled);
		event.setRaffleEnabled(isRaffleEnabled);
		event.setCauseAuctionEnabled(isCauseAuctionEnabled);
		event.setTextToGiveEnabled(isTextToGiveEnabled);

		//Execution
		boolean anyBaseModuleEnabled = eventServiceImpl.isAnyBaseModuleEnabled(event);

		//assertion
		assertEquals(anyBaseModuleEnabled, anyBaseModuleEnable);
	}

	@Test
	void test_activateTicketing() {

		//setup
		boolean activate = true;

		//mock
		when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(any())).thenReturn(ticketing);

		//Execution
		eventServiceImpl.activateTicketing(event, activate);

		//Assertion
		verify(ticketingHelperService, times(1)).findTicketingByEventAndIfNotFoundCreateNew(any());

		ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
		verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

		Ticketing ticketingData = ticketingArgumentCaptor.getValue();
		assertEquals(ticketingData.getActivated(), activate);
	}

	@Test
	void test_deleteEvent() {

		//setup
		deletedEventsDto = new DeletedEventsDto();
		deletedEventsDto.setReasonForDeletingEvent("Site was too confusing");
		deletedEventsDto.setComment("cant umderstand flow of site");

		//mock
		when(roEventService.findEventByEventId(anyLong())).thenReturn(event);
		doNothing().when(waitListSettingService).updateStatusDeleted(anyLong(),any());
		doNothing().when(waitListService).updateStatusDeleted(anyLong(), any());
        doNothing().when(eventDesignDetailRepoServices).updateRecStatus(any(), any());
        doNothing().when(intercomDetailsService).updateEventToDeletedInIntercom(any(), any());
        when(virtualEventPortalService.getStreamChannelIds(any(), any(), anyBoolean(), anyBoolean(), anyBoolean())).thenReturn(Arrays.asList());
        doNothing().when(getStreamService).deleteEventChannels(anyList(), anyLong());
        doNothing().when(kioskRegistrationSettingService).deleteKioskRegistrationSetting(anyLong(),any());

        //Execution
		ResponseDto responseDtoData = eventServiceImpl.deleteEvent(id, user, deletedEventsDto);

		//Assertion
		assertEquals(responseDtoData.getType(), SUCCESS);
		assertEquals(responseDtoData.getMessage(), "Event is deleted successfully");

		ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
		verify(eventRepoService, times(1)).save(eventArgumentCaptor.capture());

		Event eventData = eventArgumentCaptor.getValue();
		assertEquals(eventData.getEventStatus(), EventStatus.EVENT_DELETED);

		ArgumentCaptor<DeletedEvents> deletedEventsArgumentCaptor = ArgumentCaptor.forClass(DeletedEvents.class);
		verify(deletedEventsRepository).save(deletedEventsArgumentCaptor.capture());
        verify(kioskRegistrationSettingService, times(1)).deleteKioskRegistrationSetting(Mockito.anyLong(), Mockito.any());


        DeletedEvents deletedEvents = deletedEventsArgumentCaptor.getValue();
		assertEquals(deletedEvents.getTimeDeleted().toString(), DateUtils.getCurrentDate().toString());
		assertEquals(deletedEvents.getComment(), deletedEventsDto.getComment());
		assertEquals(deletedEvents.getReasonForDeletingEvent(), deletedEventsDto.getReasonForDeletingEvent());
		assertEquals(deletedEvents.getUserId().getUserId(), user.getUserId());
		assertEquals(deletedEvents.getEventId().getEventId(), event.getEventId());

		verify(roEventService).findEventByEventId(anyLong());
		verify(waitListSettingService).updateStatusDeleted(anyLong(),any());
	}

	@Test
	void test_deleteEvent_throwExceptionEventNotFound() {

		//setup
		deletedEventsDto = new DeletedEventsDto();

		//mock
		when(roEventService.findEventByEventId(anyLong())).thenReturn(null);

		//Execution
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.deleteEvent(id, user, deletedEventsDto)
        );

		verify(roEventService).findEventByEventId(anyLong());
        assertEquals(NotFoundException.EventNotFound.EVENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

	@Test
	void test_getGraphDetailsForTicketSoldBetweenDates() {

		//setup
		Date from = new Date();
		Date to = new Date();
		Date saleDate = new Date();
		long numberOfTicketSold = 1;
		double totalSale = 100d;
		TicketGraphDetail ticketGraphDetail = new TicketGraphDetail(saleDate, numberOfTicketSold, totalSale);
		List<TicketGraphDetail> ticketGraphDetailList = new ArrayList<>();
		ticketGraphDetailList.add(ticketGraphDetail);

		//mock
		when(ticketingStatisticsService.getGraphDetailsForTicketSoldBetweenDates(any(), anyLong(), any(), any())).thenReturn(ticketGraphDetailList);

		//Execution
		List<TicketGraphDetail> ticketGraphDetailData = eventServiceImpl.getGraphDetailsForTicketSoldBetweenDates(event, id, from, to);

		//Assertion
		assertEquals(ticketGraphDetailData.get(0).getSaleDate(), saleDate);
		assertEquals(ticketGraphDetailData.get(0).getNumberOfTicketSold(), numberOfTicketSold);
		assertTrue(ticketGraphDetailData.get(0).getTotalSale() == totalSale);
	}

	@Test
	void test_changeSeat() {

		//setup
		Long eventId = id;
		Long eventTicketingId = id;
		String newSeatNumber = "A-15";

		//mock
		doNothing().when(ticketingService).changeSeat(anyLong(), anyLong(), anyString());

		//Execution
		eventServiceImpl.changeSeat(eventId, eventTicketingId, newSeatNumber);

		//Assertion
		verify(ticketingService, times(1)).changeSeat(anyLong(), anyLong(), anyString());
	}

    //TODO: Mockito ReWrite
//	@Test
//	public  void  test_getAllEventAnalytics_withSearchString(){
//
//		//setup
//		int page = 0;
//		int size = 1;
//		String searchString = "eventURL";
//		boolean pastEvent = true;
//		String sortField = "eventURL";
//		String sortDirection = Sort.Direction.ASC.name();
//		Date from = new Date();
//		Date to = new Date();
//		Object[] data = {BigInteger.ONE, event.getName(), event.getEventURL(), new Date("2022/09/09 09:31:00"), new Date()};
//		List<Object[]> eventPageDataList = new ArrayList<>();
//		eventPageDataList.add(data);
//		ticketAnalytics = getTicketAnalytics(1L);
//		List<TicketAnalytics> analyticsList = new ArrayList<>();
//		analyticsList.add(ticketAnalytics);
//
//		//mock
//		when(eventRepository.getEventsByEventNameAndEventEndDateForAnalytic(anyString(), anyBoolean(),anyString())).thenReturn(eventPageDataList);
//		when(eventTicketsRepository.getTicketingAnalyticsData(any(), any(), any(), any(), any(), anyList())).thenReturn(analyticsList);
//
//		//Execution
//		DataTableResponse dataTableResponseData = eventServiceImpl.getAllEventAnalytics(page, size, searchString, sortField, sortDirection, from, to, pastEvent, null);
//
//		//Assertion
//		assertEquals(dataTableResponseData.getRecordsFiltered(), 1);
//		assertEquals(dataTableResponseData.getRecordsTotal(), 1);
//	}

	@Test
	public  void  test_getAllEventAnalytics_withSearchStringAndEventPageEmpty(){

		//setup
		int page = 1;
		int size = 1;
		String searchString = "eventURL";
		boolean pastEvent = true;
		String sortField = "eventURL";
		String sortDirection = Sort.Direction.ASC.name();
		Date from = new Date();
		Date to = new Date();
		ticketAnalytics = getTicketAnalytics(1L);
		List<Event> eventList = new ArrayList<>();
		Page<Event> eventPage = new PageImpl<>(eventList);

		//mock
        Object[] data = {event.getEventId(), event.getName(), event.getEventURL(), "US", BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date(), "Paid"};
        List<Object[]> eventPageDataList = new ArrayList<>();
        eventPageDataList.add(data);
        Page<Object[]> pageData = new PageImpl<>(eventPageDataList);



		//Execution
		DataTableResponse dataTableResponseData = eventServiceImpl.getAllEventAnalytics(page, size, searchString, sortField, sortDirection, from, to, pastEvent, null);

		//Assertion
		assertEquals(dataTableResponseData.getRecordsFiltered(), 0);
		assertEquals(dataTableResponseData.getRecordsTotal(), 0);
	}

    //TODO: Mockito ReWrite
//	@Test
//	void getAllEventAnalytics_withSearchStringEmptyAndPastEventTrue(){
//
//		//setup
//		int page = 0;
//		int size = 1;
//		String searchString = "";
//		boolean pastEvent = true;
//		String sortField = "eventURL";
//		String sortDirection = Sort.Direction.ASC.name();
//		Date from = new Date();
//		Date to = new Date();
//		Object[] data = {BigInteger.ONE, event.getName(), event.getEventURL(), new Date("2022/09/09 09:31:00"), new Date()};
//		List<Object[]> eventPageDataList = new ArrayList<>();
//		eventPageDataList.add(data);
//		ticketAnalytics = getTicketAnalytics(1L);
//		List<TicketAnalytics> analyticsList = new ArrayList<>();
//		analyticsList.add(ticketAnalytics);
//
//		//mock
//		when(eventRepository.getEventsByEventEndDateForAnalytic(anyBoolean(),anyString())).thenReturn(eventPageDataList);
//		when(eventTicketsRepository.getTicketingAnalyticsData(any(), any(), any(), any(), any(), anyList())).thenReturn(analyticsList);
//
//		//Execution
//		DataTableResponse dataTableResponseData = eventServiceImpl.getAllEventAnalytics(page, size, searchString, sortField, sortDirection, from, to, pastEvent, null);
//
//		//Assertion
//		assertEquals(dataTableResponseData.getRecordsFiltered(), 1);
//		assertEquals(dataTableResponseData.getRecordsTotal(), 1);
//	}

    //TODO: Mockito ReWrite
//	@Test
//	void test_getAllEventAnalytics_withSearchStringEmptyAndPastEventFalse(){
//
//		//setup
//		int page = 0;
//		int size = 1;
//		String searchString = "";
//		boolean pastEvent = false;
//		String sortField = "eventURL";
//		String sortDirection = Sort.Direction.ASC.name();
//		Date from = new Date();
//		Date to = new Date();
//		Object[] data = {BigInteger.ONE, event.getName(), event.getEventURL(), new Date("2022/09/09 09:31:00"), new Date()};
//		List<Object[]> eventPageDataList = new ArrayList<>();
//		eventPageDataList.add(data);
//		Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
//		pageData.getTotalElements();
//		pageData.getTotalPages();
//		ticketAnalytics = getTicketAnalytics(1L);
//		List<TicketAnalytics> analyticsList = new ArrayList<>();
//		analyticsList.add(ticketAnalytics);
//
//		//mock
//		when(eventRepository.getEventsByEventEndDateForAnalytic(anyBoolean(),anyString())).thenReturn(eventPageDataList);
//		when(eventTicketsRepository.getTicketingAnalyticsData(any(), any(), any(), any(), any(), anyList())).thenReturn(analyticsList);
//
//		//Execution
//		DataTableResponse dataTableResponseData = eventServiceImpl.getAllEventAnalytics(page, size, searchString, sortField, sortDirection, from, to, pastEvent, null);
//
//		//Assertion
//		assertEquals(dataTableResponseData.getRecordsFiltered(), 1);
//		assertEquals(dataTableResponseData.getRecordsTotal(), 1);
//	}

	@Test
	void test_getAllEventAnalytics_withSearchStringEmptyAndPastEventFalseAndPageDataEmpty(){

		//setup
		int page = 1;
		int size = 1;
		String searchString = "";
		boolean pastEvent = false;
		String sortField = "eventURL";
		String sortDirection = Sort.Direction.ASC.name();
		Date from = new Date();
		Date to = new Date();
		Object[] data = {BigInteger.ONE, event.getName(), event.getEventURL(), "US", BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date()};
		List<Object[]> eventPageDataList = new ArrayList<>();
		Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
		pageData.getTotalElements();
		pageData.getTotalPages();
		ticketAnalytics = getTicketAnalytics(1L);

		//mock


		//Execution
		DataTableResponse dataTableResponseData = eventServiceImpl.getAllEventAnalytics(page, size, searchString, sortField, sortDirection, from, to, pastEvent, null);

		//Assertion
		assertEquals(dataTableResponseData.getRecordsFiltered(), 0);
		assertEquals(dataTableResponseData.getRecordsTotal(), 0);
	}

	@Test
	void test_addTicketAnalytics_WithSameEventId() {

		//setup
		ticketAnalytics = getTicketAnalytics(1L);
		List<TicketAnalytics> analytics = new ArrayList<>();
		analytics.add(ticketAnalytics);
		Long eventId = id;
		TicketAnalyticsDto ticketAnalyticsDto = new TicketAnalyticsDto();

		//Execution
		eventServiceImpl.addTicketAnalytics(analytics, ticketAnalyticsDto);
	}

	@Test
	void test_addTicketAnalytics_withAnalyticsEmpty_withDifferentEventId() {

		//setup
		ticketAnalytics = getTicketAnalytics(5L);
		List<TicketAnalytics> analytics = new ArrayList<>();
		analytics.add(ticketAnalytics);
		Long eventId = id;
		TicketAnalyticsDto ticketAnalyticsDto = new TicketAnalyticsDto();

		//Execution
		eventServiceImpl.addTicketAnalytics(analytics, ticketAnalyticsDto);
	}

	@Test
	void test_currentDateInUTC(){
		Date date = eventServiceImpl.currentDateInUTC();
		assertTrue(null != date);
	}

	@Test
	void test_liveAuctionCharges() throws StripeException, ApiException, IOException {

		//setup
		boolean isVounteerbid = true;
		String paymentType = CC;
		String userMessageText = THANK_YOU_FOR_YOUR_PURCHASE;
        Map<String, String> languageMap = CommonUtil.getMessageLanguageMap();
		LiveItemsCodeAndAmountDto liveItemsCodeAndAmountDto = new LiveItemsCodeAndAmountDto();
		liveItemsCodeAndAmountDto.setAmount(100);
		liveItemsCodeAndAmountDto.setItemCode("MUS");
		List<LiveItemsCodeAndAmountDto> liveItemsCodeAndAmountDtoList = new ArrayList<>();
		liveItemsCodeAndAmountDtoList.add(liveItemsCodeAndAmountDto);

		StaffLiveAuctionPurchaseDto staffLiveAuctionPurchaseDto = new StaffLiveAuctionPurchaseDto();
		staffLiveAuctionPurchaseDto.setCountryCode(CountryCode.US.name());
		staffLiveAuctionPurchaseDto.setCellNumber("4844559090");
		staffLiveAuctionPurchaseDto.setTokenOrIntentId("1");
		staffLiveAuctionPurchaseDto.setLiveItemsCodeAndAmountDtos(liveItemsCodeAndAmountDtoList);

		//mock
		Mockito.doReturn(userMessageText).when(eventServiceImpl).auctionCharges(any(), any(), any(), anyBoolean(), anyString(), any(), anyBoolean(), anyMap());

		//Execution
		String liveAuctionCharges = eventServiceImpl.liveAuctionCharges(user, event, staffLiveAuctionPurchaseDto, isVounteerbid, paymentType, staffUser,languageMap);

		//Assertion
		assertEquals(liveAuctionCharges, userMessageText);
	}

	@Test
	void test_getTicketTypeGraphDetails(){

		when(ticketingStatisticsService.getGraphForNumberOfTicketSoldByTicketType(event, 1L, DataType.TICKET)).thenReturn(new ArrayList<>());
		List<TicketingTypeGraphDetail> listEx = eventServiceImpl.getTicketTypeGraphDetails(event, 1L, DataType.TICKET);
		assertTrue(null != listEx);
	}

	@Test
	void test_hideEvent_throwExceptionEVENT_NOT_FOUND(){

		String eventUrl = "eventUrl";
		when(eventRepository.findEventByEventURL(eventUrl)).thenReturn(null);
		//execute
		Exception exception = assertThrows(NotFoundException.class,
                () -> eventServiceImpl.hideEvent(eventUrl)
        );
        assertEquals(NotFoundException.EventNotFound.EVENT_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
	}

	public static Object[] getExceptionMessage() {
		AuctionBid auctionBid  = new AuctionBid();
		List<AuctionBid> auctionBidList = new ArrayList<>();
		auctionBidList.add(auctionBid);
		PurchasedRaffleTicket purchasedRaffleTicket = new PurchasedRaffleTicket();
		List<PurchasedRaffleTicket> purchasedRaffleTicketList = new ArrayList<>();
		purchasedRaffleTicketList.add(purchasedRaffleTicket);
		Pledge pledge = new Pledge();
		List<Pledge> pledgeList = new ArrayList<>();
		pledgeList.add(pledge);
		TicketingOrder ticketingOrder = new TicketingOrder();
		List<TicketingOrder> ticketingOrderList = new ArrayList<>();
		ticketingOrderList.add(ticketingOrder);
		return new Object[]{
				new Object[]{NotAcceptableException.EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN.getDeveloperMessage(), auctionBidList, Collections.emptyList(), Collections.emptyList(), Collections.emptyList()},
				new Object[]{NotAcceptableException.EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN.getDeveloperMessage(), Collections.emptyList(), purchasedRaffleTicketList, Collections.emptyList(), Collections.emptyList()},
				new Object[]{NotAcceptableException.EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN.getDeveloperMessage(), Collections.emptyList(), Collections.emptyList(), pledgeList, Collections.emptyList()},
				new Object[]{NotAcceptableException.EventExceptionMsg.EVENT_CAN_NOT_BE_HIDDEN.getDeveloperMessage(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), ticketingOrderList},
		};
	}

    //TODO: Revisit test with parameters and based on that hideEvent method expectations
	/*@ParameterizedTest
	@MethodSource("getExceptionMessage")
	void test_hideEvent_throwException(String exceptionMessage, List<AuctionBid> auctionBidList, List<PurchasedRaffleTicket> purchasedRaffleTicketList, List<Pledge> pledgeList, List<TicketingOrder> ticketingOrderList) {

		//setup
		event.setAuctionId(id);

		//mock
		when(eventRepository.findEventByEventURL(anyString())).thenReturn(event);
		when(auctionBidService.findByAuctionIdAndRefunded(anyLong(), anyBoolean())).thenReturn(auctionBidList);
		when(purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(anyLong())).thenReturn(purchasedRaffleTicketList);
		when(pledgeService.findAllPledgeForCauseAuction(anyLong())).thenReturn(pledgeList);
		when(ticketingOrderRepoService.findByEventId(any())).thenReturn(ticketingOrderList);

		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.hideEvent(url)
        );

		//Assertion
		verify(eventRepository, times(1)).findEventByEventURL(anyString());
		verify(auctionBidService, times(1)).findByAuctionIdAndRefunded(anyLong(), anyBoolean());
		verify(purchasedRaffleTicketService, times(1)).getAllPurchasedTicketsForRaffle(anyLong());
		verify(pledgeService, times(1)).findAllPledgeForCauseAuction(anyLong());
		verify(ticketingOrderRepoService, times(1)).findByEventId(any());
        assertEquals(exceptionMessage, exception.getMessage());
	}*/

	@Test
	void test_hideEvent_success(){

		//setup
		event.setAuctionId(id);
		List<AuctionBid> auctionBidList = new ArrayList<>();
		List<PurchasedRaffleTicket> purchasedRaffleTicketList = new ArrayList<>();
		List<Pledge> pledgeList = new ArrayList<>();
		List<TicketingOrder> ticketingOrderList = new ArrayList<>();
		auction = new Auction();
		raffle = new Raffle();
		causeAuction = new CauseAuction();
		ticketing = new Ticketing();
		Date currentDateInUTC = TimeZoneUtil.getDateInUTC(DateUtils.getCurrentDate(), "Universal Coordinated Time (UTC)");

		//mock
		when(eventRepository.findEventByEventURL(anyString())).thenReturn(event);
		when(auctionBidService.findByAuctionIdAndRefunded(anyLong(), anyBoolean())).thenReturn(auctionBidList);
		when(purchasedRaffleTicketService.getAllPurchasedTicketsForRaffle(anyLong())).thenReturn(purchasedRaffleTicketList);
		when(pledgeService.findAllPledgeForCauseAuction(anyLong())).thenReturn(pledgeList);
		when(ticketingOrderRepoService.findByEventId(any())).thenReturn(ticketingOrderList);
		when(auctionService.findByEvent(any())).thenReturn(auction);
		when(raffleService.findByEvent(any())).thenReturn(raffle);
		when(causeAuctionService.findByEvent(any())).thenReturn(causeAuction);
		when(ticketingHelperService.findTicketingByEvent(any())).thenReturn(ticketing);

		//Execution
		ResponseDto dtoData = eventServiceImpl.hideEvent(url);

		//Assertion
		verify(eventRepository, times(1)).findEventByEventURL(anyString());
		verify(auctionBidService, times(1)).findByAuctionIdAndRefunded(anyLong(), anyBoolean());
		verify(purchasedRaffleTicketService, times(1)).getAllPurchasedTicketsForRaffle(anyLong());
		verify(pledgeService, times(1)).findAllPledgeForCauseAuction(anyLong());
		verify(ticketingOrderRepoService, times(1)).findByEventId(any());
		verify(auctionService, times(1)).findByEvent(any());
		verify(raffleService, times(1)).findByEvent(any());
		verify(causeAuctionService, times(1)).findByEvent(any());
		verify(ticketingHelperService, times(1)).findTicketingByEvent(any());

		assertEquals(auction.getEndDate().toString(), currentDateInUTC.toString());
		assertEquals(raffle.getEndDate().toString(), currentDateInUTC.toString());
		assertEquals(causeAuction.getEndDate().toString(), currentDateInUTC.toString());

		assertEquals(dtoData.getMessage(), "Event successfully marked as hidden");
		assertEquals(dtoData.getType(), Constants.SUCCESS);

		ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
		verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());

		Ticketing ticketingData = ticketingArgumentCaptor.getValue();
		//assertEquals(ticketingData. ().toString(), currentDateInUTC.toString());
	}

	@Test
	void test_findTicketingByEvent(){
		when(ticketingHelperService.findTicketingByEvent(event)).thenReturn(ticketing);
		Ticketing ticketingEx = eventServiceImpl.findTicketingByEvent(event);
		assertTrue(null != ticketingEx);
	}

	@Test
	void test_findMostRecentEventByUser_withEventListEmpty(){

		//setup
		List<Event> eventList = new ArrayList<>();

		//mock
		when(staffService.findEventsByUserAndRole(any(), any())).thenReturn(eventList);

		//Execution
		Event eventData = eventServiceImpl.findMostRecentEventByUser(user);

		//Assertion
		assertNull(eventData);
	}

	@Test
	void test_findMostRecentEventByUser_withEventStatusDeleted(){

		//setup
		event.setEventStatus(EventStatus.EVENT_DELETED);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(staffService.findEventsByUserAndRole(any(), any())).thenReturn(eventList);

		//Execution
		Event eventData = eventServiceImpl.findMostRecentEventByUser(user);

		//Assertion
		assertNull(eventData);
	}

	@Test
	void test_findMostRecentEventByUser_withEventStatusHappening(){

		//setup
		event.setEventStatus(EventStatus.EVENT_HAPPENING);
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);

		//mock
		when(staffService.findEventsByUserAndRole(any(), any())).thenReturn(eventList);

		//Execution
		Event eventData = eventServiceImpl.findMostRecentEventByUser(user);

		//Assertion
		assertEquals(eventData.getEventStatus(), event.getEventStatus());
		assertEquals(eventData.getEventId(), event.getEventId());
	}

	@Test
	void test_changeEventListingMode_creditCardEnabledFalse_throw_ACTIVATE_PAYMENT_PROCESSING_FOR_FUNDRAISER_MODULE() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(false);
		event.setSilentAuctionEnabled(true);
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

        //mock
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event)).thenReturn(false);
		when(ticketingTypeTicketService.isAnyTicketExistsWithDonationType(event)).thenReturn(false);



		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin)
        );
        assertEquals(ACTIVATE_PAYMENT_PROCESSING_FOR_FUNDRAISER_MODULE.getErrorMessage(), exception.getMessage());
    }

	@Test
	void test_changeEventListingMode_freeEventFalse_throw_ACTIVATE_PAYMENT_PROCESSING_FOR_PAID_TICKETTYPES() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(false);
		event.setSilentAuctionEnabled(true);
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

        //mock
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event)).thenReturn(true);




		//Execution
		Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin)
        );
        assertEquals(ACTIVATE_PAYMENT_PROCESSING_FOR_PAID_TICKETTYPES.getErrorMessage(), exception.getMessage());
	}

	@Test
	void test_changeEventListingMode_whiteLabelNull() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(null);
		event.setSilentAuctionEnabled(true);
        event.setSubscriptionId(STRING_EMPTY);
        Organizer organizer = new Organizer();
        organizer.setId(1L);
        event.setOrganizer(organizer);
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

		//mock
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event)).thenReturn(true);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(new EventChargebeePlanConfigDto());
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(new EventPlanConfig());
        when(virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSetting);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

		//Execution
		eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin);

	}

	@Test
	void test_changeEventListingMode_EventListingModePrivateAndWhiteLabelNotNull() {

		//setup
		String eventListingStatus = EventListingStatus.PRIVATE.toString();
       event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
        event.setSubscriptionId(STRING_EMPTY);
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

		//mock
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event)).thenReturn(false);
		when(ticketingTypeTicketService.isAnyTicketExistsWithDonationType(event)).thenReturn(true);
        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(new EventChargebeePlanConfigDto());
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(new EventPlanConfig());
        when(virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSetting);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);
        //when(chargebeeEventCreditsRepoService.save(chargebeeEventCredits,user)).thenReturn(chargebeeEventCredits);
		//Execution
		eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin);

	}

	@Test
	void test_changeEventListingMode_whiteLabelNotNullAndEmailsNull() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
		event.setSilentAuctionEnabled(true);
        event.setSubscriptionId(STRING_EMPTY);
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;


        //mock
		when(roTicketingTypeTicketService.isAnyTicketExistsWithAmountGreaterThanZero(event)).thenReturn(false);
		when(ticketingTypeTicketService.isAnyTicketExistsWithDonationType(event)).thenReturn(false);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(new EventChargebeePlanConfigDto());
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(new EventPlanConfig());
        when(virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSetting);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

		//Execution
		eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin);

	}

	@Test
	void test_changeEventListingMode_whiteLabelNotNullAndEmailsIsEmpty() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
        event.setSubscriptionId(STRING_EMPTY);
		List<String> email = new ArrayList<>();
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

		//mock

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(new EventChargebeePlanConfigDto());
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(new EventPlanConfig());
        when(virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSetting);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

		//Execution
		eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin);

	}

	@Test
	void test_changeEventListingMode_whiteLabelNotNullAndEmailsNotNull() {

		//setup
		String eventListingStatus = EventListingStatus.PUBLISHED.toString();
		event.setCreditCardEnabled(true);
		event.setWhiteLabel(whiteLabel);
        event.setSubscriptionId(STRING_EMPTY);
		List<String> email = Collections.singletonList("<EMAIL>");
		stripe.setEmail("<EMAIL>");
        virtualEventSettings = new VirtualEventSettings();
        exhibitorSetting = new ExhibitorSetting();
        virtualEventSettings.setLobbyLiveStream(false);
        exhibitorSetting.setExpoLiveStream(false);
        boolean isSuperAdmin = false;

		//mock


        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(new EventChargebeePlanConfigDto());
        when(eventPlanConfigRepoService.findByEventId(event.getEventId())).thenReturn(new EventPlanConfig());
        when(virtualEventService.getVirtualEventSettingsByEventIdOrThrowError(event.getEventId())).thenReturn(virtualEventSettings);
        when(exhibitorSettingsRepoService.getGlobalConfigRecordByEventId(event.getEventId())).thenReturn(exhibitorSetting);

        when(eventPlanConfigService.getPlanConfiguration(event.getEventId())).thenReturn(eventChargebeePlanConfigDto);

		//Execution
		eventServiceImpl.changeEventListingMode(eventListingStatus, event, user, isSuperAdmin);

	}

	@Test
	void test_convertDonationButtonStringToGson_donationButtonTextNull() {

		//Execution
		eventServiceImpl.convertDonationButtonStringToGson(null);
	}

	private void getJoinEventWithDealProductData() {
		joinEventWithDealProduct = new JoinEventWithDealProduct();
		joinEventWithDealProduct.setEvent(event);
		joinEventWithDealProduct.setId(id);
		joinEventWithDealProduct.setProductId(id);
		joinEventWithDealProduct.setDealProductId(id);
	}

	private void getJsonObjData() throws JSONException{
		response = new JSONObject();
		response.put("access_token", "sk_test_FStPmAcubsrj3FP1hRS2Ge6w");
		response.put("token_type", "bearer");
		response.put("refresh_token", "rt_9m0CC0dpKqQUmc8Duo2GkxiSa0r2ZbwrN8KGJsnhwEeu3bIw");
		response.put("merchant_id", "ZYB3BP52T9HJ4");
		response.put("expires_at", "2019-09-25");
		response.put("stripe_publishable_key", "pk_test_x9BXBdwFPlxaTKaWz1iv8Jzz");
		response.put("stripe_user_id", "acct_19KYL3I9f3YkXHM0");
		response.put("scope", "read_write");
		response.put("livemode", false);
	}

	private Page<Event> getEventpage() {
		List<Event> eventList = new ArrayList<>();
		eventList.add(event);
		Page<Event> eventPage = new PageImpl<>(eventList);
		eventPage.getTotalElements();
		eventPage.getTotalPages();

		return eventPage;
	}

	private Page<Object[]> getEventPageData() {
		Object[] data = {event.getEventId(), event.getName(), event.getEventURL(), "US", BigInteger.valueOf(phoneNumber), new Date("2022/09/09 09:31:00"), new Date()};
		List<Object[]> eventPageDataList = new ArrayList<>();
		eventPageDataList.add(data);
		Page<Object[]> pageData = new PageImpl<>(eventPageDataList);
		pageData.getTotalElements();
		pageData.getTotalPages();

		return pageData;
	}

	private TicketAnalytics getTicketAnalytics(Long eventId){

		return ticketAnalytics = new TicketAnalytics(eventId, 1L, 100d, 50d, 10d, 5d, 1L, 1L, 100d, 50d, 10d, 5d, 1L, 1L, 1d, 1d);
	}

	private Event getEventData(boolean ticketingEnable, boolean raffleEnable, boolean silentAuctionEnable, boolean causeAuctionEnable, boolean textToGiveEnable, boolean donationEnable){
		event.setTicketingEnabled(ticketingEnable);
		event.setRaffleEnabled(raffleEnable);
		event.setSilentAuctionEnabled(silentAuctionEnable);
		event.setCauseAuctionEnabled(causeAuctionEnable);
		event.setTextToGiveEnabled(textToGiveEnable);
		event.setDonationEnabled(donationEnable);
		return event;
	}

	private DonationSettings getDonationSetting(boolean textToGiveEnable){
		donationSetting = new DonationSettings();
		donationSetting.setTextToGiveActivated(textToGiveEnable);
		return donationSetting;
	}

	private CauseAuction getCauseAuction(boolean causeAuctionActivated){
		causeAuction = new CauseAuction();
		causeAuction.setActivated(causeAuctionActivated);
		return causeAuction;
	}

	private Raffle getRaffle(boolean raffleActivated){
		raffle = new Raffle();
		raffle.setActivated(raffleActivated);
		return raffle;
	}

	private Auction getAuction(boolean auctionActivated){
		auction = new Auction();
		auction.setActivated(auctionActivated);
		return auction;
	}

	private Ticketing getTicketing(boolean ticketingActivated){
		ticketing = new Ticketing();
		ticketing.setActivated(ticketingActivated);
		return ticketing;
	}

	private EventDesignSettingsDto getEventDesignSettingsDto(String eventName, String organizerpageUrl){

		eventDesignSettingsDto = new EventDesignSettingsDto();
		eventDesignSettingsDto.setEventName(eventName);
		eventDesignSettingsDto.setEventUrl("testEvent");
		eventDesignSettingsDto.setOrganizerPageURL(organizerpageUrl);
		eventDesignSettingsDto.setBannerImage("default_ae_banner_theme_C.jpg");
		eventDesignSettingsDto.setBannerImageEnabled(true);
		eventDesignSettingsDto.setDesc("Description");
		eventDesignSettingsDto.setLogoImage("logoImage");
		eventDesignSettingsDto.setLogoEnabled(true);
		eventDesignSettingsDto.setTotalFundRaisedShow(true);
		eventDesignSettingsDto.setSocialSharingEnabled(true);
		eventDesignSettingsDto.setSponsorSection("sponserSection");
		eventDesignSettingsDto.setEventTagLine("eventTagLine");
		eventDesignSettingsDto.setDisplayBackgroundColor("#D3D3D3");
		eventDesignSettingsDto.setDisplayTextColor("displayTextColor");
		eventDesignSettingsDto.setHideSponsorSection(true);
		eventDesignSettingsDto.setEnableSessionsSpeakers(true);
		eventDesignSettingsDto.setTrackingScript("trackingScript");
		eventDesignSettingsDto.setThemeId(1L);
		eventDesignSettingsDto.setDonationButtonText(new DonationButtonDesignDto("#ffffff", true));

		return eventDesignSettingsDto;
	}

	private AuctionPurchaseDto getAuctionPurchaseDto(){
		auctionPurchaseDto = new AuctionPurchaseDto();
		auctionPurchaseDto.setItemCode("AUC");
		auctionPurchaseDto.setAmount(100);
		return auctionPurchaseDto;
	}

	private Item getItem(int numberOfWinner, boolean liveAuctionItem, int byItNowPrice, String itemCode){
		item = new Item();
		item.setNumberOfWinners(numberOfWinner);
		item.setLiveAuctionItem(liveAuctionItem);
		item.setBuyItNowPrice(byItNowPrice);
		item.setCode(itemCode);
		return item;
	}

	private AuctionBid getAuctionBid(Long id, double amount){
		auctionBid = new AuctionBid();
		auctionBid.setId(id);
		auctionBid.setAmount(amount);
		return auctionBid;
	}

	private Stripe getStripe(String paymentGateWay, boolean processingFeesToPurchaser){
		stripe.setPaymentGateway(paymentGateWay);
		stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
		stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
		stripe.setProcessingFeesToPurchaser(processingFeesToPurchaser);
		return stripe;
	}

    @Test
    void test_switchRecurringEventToSingleDayEventAndThrowAlreadySingleDayEvent() {

        //setup
        ticketing.setRecurringEvent(false);

        //mock
        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);

        //execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> eventServiceImpl.switchRecurringEventToSingleDayEvent(event)
        );

        //assert
        verify(ticketingRepository).findByEventid(any());
        assertEquals(NotAcceptableException.NotAceptableExeceptionMSG.ALREADY_SINGLE_DAY_EVENT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_switchRecurringEventToSingleDayEventAndThrowCanNotConvertRecurringEventToSingleDayEvent() {

        //setup
        List<Object[]> countSoldTicketList = new ArrayList<>();
        countSoldTicketList.add(new Object[1]);
        countSoldTicketList.add(new Object[2]);

        ticketing.setEventEndDate(DateUtils.getAddedMinutes(DateUtils.getCurrentDate(), 60));

        //mock
        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);
        when(recurringEventsScheduleService.getRecurringEventsByEvent(any())).thenReturn(Arrays.asList());



        //execution
        eventServiceImpl.switchRecurringEventToSingleDayEvent(event);

        //assert
        verify(ticketingRepository).findByEventid(any());
        verify(recurringEventsScheduleService).getRecurringEventsByEvent(any());
    }

    @Test
    void test_switchRecurringEventToSingleDayEventAndCountSoldTicketsListSizeZero() {
        //setup
        ticketing.setEventEndDate(DateUtils.getAddedMinutes(DateUtils.getCurrentDate(), 60));

        //mock
        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);
        when(recurringEventsScheduleService.getRecurringEventsByEvent(any())).thenReturn(Arrays.asList());


        doNothing().when(recurringEventsScheduleService).deleteRecurringEventsScheduleByIdForSwichRecurringEvent(any(), any(), anyList());

        //execution
        eventServiceImpl.switchRecurringEventToSingleDayEvent(event);

        //assert
        verify(ticketingRepository).findByEventid(any());
        verify(recurringEventsScheduleService).getRecurringEventsByEvent(any());
        verify(recurringEventsScheduleService).deleteRecurringEventsScheduleByIdForSwichRecurringEvent(any(), any(), anyList());
    }

    //TODO: Mockito ReWrite
//    @Test
//    void test_switchRecurringEventToSingleDayEventAndCountSoldTicketsListSizeOne() {
//
//        //setup
//        Object[] objects = new Object[1];
//        objects[0] = 1L;
//        List<Object[]> countSoldTicketList = new ArrayList<>();
//        countSoldTicketList.add(objects);
//
//        List<RecurringEvents> recurringEventsList = new ArrayList<>();
//        recurringEventsList.add(recurringEvents);
//
//        ticketingType.setCreatedFrom(1L);
//        eventTickets.setTicketingTypeId(ticketingType);
//
//        TicketingType ticketingType1 = new TicketingType();
//
//        EventTickets eventTickets1 = new EventTickets();
//        ticketingType1.setCreatedFrom(-1L);
//        eventTickets1.setId(2L);
//        eventTickets1.setTicketingTypeId(ticketingType1);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//        eventTicketsList.add(eventTickets1);
//
//        TicketingOrderManager ticketingOrderManager1 = new TicketingOrderManager();
//        ticketingOrderManager1.setId(2L);
//
//        ticketingOrderManager.setTicketType(ticketingType);
//
//        List<TicketingOrderManager> ticketingOrderManagersList = new ArrayList<>();
//        ticketingOrderManagersList.add(ticketingOrderManager);
//        ticketingOrderManagersList.add(ticketingOrderManager1);
//
//        List<TicketingOrderManager> ticketingOrderManagersListRemove = new ArrayList<>();
//        ticketingOrderManagersListRemove.add(ticketingOrderManager1);
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//
//        List<TicketingOrder> ticketingOrderList = new ArrayList<>();
//        ticketingOrderList.add(ticketingOrder);
//
//        ticketing.setEventEndDate(DateUtils.getAddedMinutes(DateUtils.getCurrentDate(), 60));
//
//        //mock
//        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);
//        when(recurringEventsScheduleService.getRecurringEventsByEvent(any())).thenReturn(recurringEventsList);
//        when(eventTicketsRepository.findByTicketStatusRefundesAndDelete(anyList(), any())).thenReturn(Arrays.asList());
//        when(eventTicketsRepository.countSoldTicketsByRecurringEventId(any(), anyString(), any())).thenReturn(countSoldTicketList);
//        when(eventTicketsRepoService.findByRecurringEventId(anyLong())).thenReturn(eventTicketsList);
//        when(ticketingOrderManagerService.findByRecurringEventId(anyLong())).thenReturn(ticketingOrderManagersList);
//        when(ticketingOrderManagerService.findByTicketTypeId(anyList())).thenReturn(ticketingOrderManagersListRemove);
//        when(ticketingOrderRepoService.findByEventId(any())).thenReturn(ticketingOrderList);
//        when(ticketingTypeService.findByid(anyLong())).thenReturn(null);
//        when(ticketingCouponService.findById(anyLong())).thenReturn(Optional.of(ticketingCoupon));
//        doNothing().when(ticketingCouponService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketingAccessCodeService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketHolderRequiredAttributesService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(registrationAttributeRepository).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketingTypeService).deleteByRecurringEventIdIn(anyList());
//        doNothing().when(ticketingTypeService).updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(any());
//        doNothing().when(recurringEventsRepository).deleteAll(recurringEventsList);
//        doNothing().when(recurringEventsScheduleService).deleteByIdIn(anyList());
//
//        //execution
//        eventServiceImpl.switchRecurringEventToSingleDayEvent(event);
//
//        //assert
//        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
//        verify(ticketingTypeService, times(1)).save(ticketingTypeArgumentCaptor.capture());
//
//        TicketingType ticketingType = ticketingTypeArgumentCaptor.getValue();
//        assertNull(ticketingType.getCreatedFrom());
//        assertNull(ticketingType.getRecurringEventId());
//
//        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<EventTickets>> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(listClass);
//        verify(eventTicketsRepoService, times(2)).saveAll(eventTicketsArgumentCaptor.capture());
//
//        List<EventTickets> eventTicketsArgumentCaptorData = eventTicketsArgumentCaptor.getValue();
//        assertNull(eventTicketsArgumentCaptorData.get(0).getRecurringEventId());
//
//        Class<ArrayList<TicketingOrderManager>> ticketingOrderManagerlistClass = (Class<ArrayList<TicketingOrderManager>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<TicketingOrderManager>> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(ticketingOrderManagerlistClass);
//        verify(ticketingOrderManagerService, times(2)).saveAll(ticketingOrderManagerArgumentCaptor.capture());
//
//        List<TicketingOrderManager> ticketingOrderManagerArgumentCaptorData = ticketingOrderManagerArgumentCaptor.getValue();
//        assertNull(ticketingOrderManagerArgumentCaptorData.get(0).getRecurringEventId());
//
//        Class<ArrayList<TicketingOrder>> ticketingOrderlistClass = (Class<ArrayList<TicketingOrder>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<TicketingOrder>> ticketingOrderlistClassArgumentCaptor = ArgumentCaptor.forClass(ticketingOrderlistClass);
//        verify(ticketingOrderRepoService, times(1)).saveAll(ticketingOrderlistClassArgumentCaptor.capture());
//
//        List<TicketingOrder> ticketingOrderlistClassArgumentCaptorData = ticketingOrderlistClassArgumentCaptor.getValue();
//        assertEquals(ticketingOrderlistClassArgumentCaptorData.get(0).getTicketingCoupon(), ticketingOrder.getTicketingCoupon());
//
//        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
//        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
//
//        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
//        assertFalse(ticketingData.isRecurringEvent());
//        assertEquals(ticketingData.getEventStartDate(), recurringEventsList.get(0).getRecurringEventStartDate());
//        assertEquals(ticketingData.getEventEndDate(), recurringEventsList.get(0).getRecurringEventEndDate());
//
//        verify(ticketingRepository).findByEventid(any());
//        verify(recurringEventsScheduleService).getRecurringEventsByEvent(any());
//        verify(eventTicketsRepository).findByTicketStatusRefundesAndDelete(anyList(), any());
//        verify(eventTicketsRepoService).findByRecurringEventId(anyLong());
//        verify(ticketingOrderManagerService).findByRecurringEventId(anyLong());
//        verify(ticketingOrderRepoService).findByEventId(any());
//        verify(ticketingTypeService, times(2)).findByid(anyLong());
//        verify(ticketingCouponService).findById(anyLong());
//        verify(ticketingCouponService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketingAccessCodeService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketHolderRequiredAttributesService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(registrationAttributeRepository).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketingTypeService).deleteByRecurringEventIdIn(anyList());
//        //verify(ticketingTypeService).updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(any());
//        verify(recurringEventsRepository).deleteAll(recurringEventsList);
//        verify(recurringEventsScheduleService).deleteByIdIn(anyList());
//    }

    //TODO: Mockito ReWrite
//    @Test
//    void test_switchRecurringEventToSingleDayEventAndCountSoldTicketsListSizeOneAndCreatedFormGreaterThenZeroAndRefunded() {
//
//        //setup
//        Object[] objects = new Object[1];
//        objects[0] = 1L;
//        List<Object[]> countSoldTicketList = new ArrayList<>();
//        countSoldTicketList.add(objects);
//
//        recurringEvents.setRecurringEventSchedule(recurringEventSchedule);
//
//        List<RecurringEvents> recurringEventsList = new ArrayList<>();
//        recurringEventsList.add(recurringEvents);
//
//        ticketingType.setCreatedFrom(1L);
//        eventTickets.setTicketingTypeId(ticketingType);
//        eventTickets.setTicketingOrder(ticketingOrder);
//        eventTickets.setRecurringEvents(recurringEvents);
//
//        List<EventTickets> eventTicketsWithRefundAndDeleted = new ArrayList<>();
//        eventTicketsWithRefundAndDeleted.add(eventTickets);
//
//        List<EventTickets> eventTicketsList = new ArrayList<>();
//        eventTicketsList.add(eventTickets);
//
//        TicketingOrderManager ticketingOrderManager1 = new TicketingOrderManager();
//        ticketingOrderManager1.setId(2L);
//
//        ticketingOrderManager.setTicketType(ticketingType);
//
//        List<TicketingOrderManager> ticketingOrderManagersList = new ArrayList<>();
//        ticketingOrderManagersList.add(ticketingOrderManager);
//
//        List<TicketingOrderManager> ticketingOrderManagersListRemove = new ArrayList<>();
//        ticketingOrderManagersListRemove.add(ticketingOrderManager1);
//
//        ticketingOrder.setTicketingCoupon(ticketingCoupon);
//
//        List<TicketingOrder> ticketingOrderList = new ArrayList<>();
//        ticketingOrderList.add(ticketingOrder);
//
//        ticketing.setEventEndDate(DateUtils.getAddedMinutes(DateUtils.getCurrentDate(), 60));
//
//        //mock
//        when(ticketingRepository.findByEventid(any())).thenReturn(ticketing);
//        when(recurringEventsScheduleService.getRecurringEventsByEvent(any())).thenReturn(recurringEventsList);
//        when(eventTicketsRepository.findByTicketStatusRefundesAndDelete(anyList(), any())).thenReturn(eventTicketsWithRefundAndDeleted);
//        when(eventTicketsRepository.countSoldTicketsByRecurringEventId(any(), anyString(), any())).thenReturn(countSoldTicketList);
//        when(eventTicketsRepoService.findByRecurringEventId(anyLong())).thenReturn(eventTicketsList);
//        when(ticketingOrderManagerService.findByRecurringEventId(anyLong())).thenReturn(ticketingOrderManagersList);
//        when(ticketingOrderManagerService.findByTicketTypeId(anyList())).thenReturn(ticketingOrderManagersListRemove);
//        when(ticketingOrderRepoService.findByEventId(any())).thenReturn(ticketingOrderList);
//        when(ticketingTypeService.findByid(anyLong())).thenReturn(ticketingType);
//        when(ticketingCouponService.findById(anyLong())).thenReturn(Optional.of(ticketingCoupon));
//        doNothing().when(ticketingCouponService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketingAccessCodeService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketHolderRequiredAttributesService).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(registrationAttributeRepository).deleteByRecurringIdAndEventId(anyList(), any());
//        doNothing().when(ticketingTypeService).deleteByRecurringEventIdIn(anyList());
//        doNothing().when(ticketingTypeService).updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(any());
//        doNothing().when(recurringEventsRepository).deleteAll(recurringEventsList);
//        doNothing().when(recurringEventsScheduleService).deleteByIdIn(anyList());
//        doNothing().when(embedWidgetSettingService).updateEmbedwidgetSettingRecStatusByRecurringEventIds(anyList(), any());
//
//        //execution
//        eventServiceImpl.switchRecurringEventToSingleDayEvent(event);
//
//        //assert
//        Class<ArrayList<EventTickets>> listClass = (Class<ArrayList<EventTickets>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<EventTickets>> eventTicketsArgumentCaptor = ArgumentCaptor.forClass(listClass);
//        verify(eventTicketsRepoService, times(2)).saveAll(eventTicketsArgumentCaptor.capture());
//
//        List<EventTickets> eventTicketsArgumentCaptorData = eventTicketsArgumentCaptor.getValue();
//        assertNull(eventTicketsArgumentCaptorData.get(0).getRecurringEventId());
//
//        Class<ArrayList<TicketingOrderManager>> ticketingOrderManagerlistClass = (Class<ArrayList<TicketingOrderManager>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<TicketingOrderManager>> ticketingOrderManagerArgumentCaptor = ArgumentCaptor.forClass(ticketingOrderManagerlistClass);
//        verify(ticketingOrderManagerService, times(3)).saveAll(ticketingOrderManagerArgumentCaptor.capture());
//
//        List<TicketingOrderManager> ticketingOrderManagerArgumentCaptorData = ticketingOrderManagerArgumentCaptor.getValue();
//        assertNull(ticketingOrderManagerArgumentCaptorData.get(0).getRecurringEventId());
//
//        Class<ArrayList<TicketingOrder>> ticketingOrderlistClass = (Class<ArrayList<TicketingOrder>>) (Class) ArrayList.class;
//        ArgumentCaptor<ArrayList<TicketingOrder>> ticketingOrderlistClassArgumentCaptor = ArgumentCaptor.forClass(ticketingOrderlistClass);
//        verify(ticketingOrderRepoService, times(2)).saveAll(ticketingOrderlistClassArgumentCaptor.capture());
//
//        List<TicketingOrder> ticketingOrderlistClassArgumentCaptorData = ticketingOrderlistClassArgumentCaptor.getValue();
//        assertEquals(ticketingOrderlistClassArgumentCaptorData.get(0).getTicketingCoupon(), ticketingOrder.getTicketingCoupon());
//
//        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
//        verify(ticketingService, times(1)).save(ticketingArgumentCaptor.capture());
//
//        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
//        assertFalse(ticketingData.isRecurringEvent());
//        assertEquals(ticketingData.getEventStartDate(), recurringEventsList.get(0).getRecurringEventStartDate());
//        assertEquals(ticketingData.getEventEndDate(), recurringEventsList.get(0).getRecurringEventEndDate());
//
//        verify(ticketingRepository).findByEventid(any());
//        verify(recurringEventsScheduleService).getRecurringEventsByEvent(any());
//        verify(eventTicketsRepository).findByTicketStatusRefundesAndDelete(anyList(), any());
//        verify(eventTicketsRepoService).findByRecurringEventId(anyLong());
//        verify(ticketingOrderManagerService).findByRecurringEventId(anyLong());
//        verify(ticketingOrderRepoService).findByEventId(any());
//        verify(ticketingTypeService, atLeastOnce()).findByid(anyLong());
//        verify(ticketingCouponService).findById(anyLong());
//        verify(ticketingCouponService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketingAccessCodeService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketHolderRequiredAttributesService).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(registrationAttributeRepository).deleteByRecurringIdAndEventId(anyList(), any());
//        verify(ticketingTypeService).deleteByRecurringEventIdIn(anyList());
//        //verify(ticketingTypeService).updateRecurringEventSalesEndStatusAndRecurringEventSalesEndTime(any());
//        verify(recurringEventsRepository).deleteAll(recurringEventsList);
//        verify(recurringEventsScheduleService).deleteByIdIn(anyList());
//    }

}