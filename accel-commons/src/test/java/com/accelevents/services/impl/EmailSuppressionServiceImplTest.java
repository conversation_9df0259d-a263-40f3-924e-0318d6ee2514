package com.accelevents.services.impl;

import com.accelevents.domain.EmailSuppression;
import com.accelevents.domain.Event;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EmailType;
import com.accelevents.dto.UnsubscribeInfoDto;
import com.accelevents.dto.UnsubsubscribeReturnData;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.repositories.EmailSuppressionRepository;
import com.accelevents.ro.event.service.ROEventService;
import com.accelevents.ro.user.service.ROUserService;
import com.accelevents.services.EventDesignDetailService;
import com.accelevents.services.EventService;
import com.accelevents.services.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.accelevents.domain.enums.EmailType.EMAIL_INVITATIONS;
import static com.accelevents.exceptions.NotAcceptableException.NotAceptableExeceptionMSG.ALREAYD_UNSUBSCRIBED;
import static com.accelevents.utils.Constants.STRING_EMPTY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
public class EmailSuppressionServiceImplTest {

    @Spy
    @InjectMocks
    private EmailSuppressionServiceImpl emailSuppressionServiceImpl = new EmailSuppressionServiceImpl();

    @Mock
    private EmailSuppressionRepository emailSuppressionRepository;

    @Mock
    private EventService eventService;
    @Mock
    private ROEventService roEventService;

    @Mock
    private UserService userService;
    @Mock
    private ROUserService roUserService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    private EmailSuppression emailSuppression;
    private Event event;
    private User user;
    private EmailType emailType;
    private UnsubscribeInfoDto unsubscribeInfoDto;
    private UnsubsubscribeReturnData unsubsubscribeReturnData;

    private String email = "<EMAIL>";
    private String imageLocation = "8fee98ce-c777-4ae1-bf96-b83e71ae7867_jellyfish.jpg";
    private String token = "token";
	private Date date = new Date();

    @BeforeEach
    public void setUp() throws Exception {
        emailSuppression = new EmailSuppression();
        event = EventDataUtil.getEvent();
        user = EventDataUtil.getUser();
        unsubscribeInfoDto = new UnsubscribeInfoDto(event.getEventId(), email);
		String url = "DEFAULT";
		unsubsubscribeReturnData = new UnsubsubscribeReturnData(url, token, imageLocation, event.getEventId(), event.getEventURL());
    }

    @Test
    public void test_save_success() {

        //setup
		Long id = 1L;
		emailSuppression.setId(id);
        emailSuppression.setDate(date);
        emailSuppression.setEmail(email);
        emailSuppression.setEventId(event.getEventId());
        emailSuppression.setEmailType(EMAIL_INVITATIONS);

        //Execution
        emailSuppressionServiceImpl.save(emailSuppression);

        ArgumentCaptor<EmailSuppression> emailSuppressionArgumentCaptor = ArgumentCaptor.forClass(EmailSuppression.class);
        verify(emailSuppressionRepository,times(1)).save(emailSuppressionArgumentCaptor.capture());

        EmailSuppression emailSuppressionData = emailSuppressionArgumentCaptor.getValue();
        assertEquals(emailSuppressionData.getId(), emailSuppression.getId());
        assertEquals(emailSuppressionData.getDate(), emailSuppression.getDate());
        assertEquals(emailSuppressionData.getEmail(), emailSuppression.getEmail());
        assertEquals(emailSuppressionData.getEventId(), emailSuppression.getEventId());
        assertEquals(emailSuppressionData.getEmailType(), emailSuppression.getEmailType());
    }

    @Test
    public void test_addToEmailSupressionList_success() {

        //mock
        Mockito.doReturn(false).when(emailSuppressionServiceImpl).checkUserInSupressionList(email, event.getEventId(), EMAIL_INVITATIONS);

        //Execution
        emailSuppressionServiceImpl.addToEmailSupressionList(email, event.getEventId());

        ArgumentCaptor<EmailSuppression> emailSuppressionArgumentCaptor = ArgumentCaptor.forClass(EmailSuppression.class);
        verify(emailSuppressionRepository,times(1)).save(emailSuppressionArgumentCaptor.capture());

        EmailSuppression emailSuppressionData = emailSuppressionArgumentCaptor.getValue();
        assertEquals(emailSuppressionData.getEmail(), email);
        assertEquals(emailSuppressionData.getEventId().longValue(), event.getEventId());
        assertEquals(emailSuppressionData.getEmailType(), EMAIL_INVITATIONS);
    }

    @Test
    public void test_addToEmailSupressionList_throwException() {

        //setup
        emailSuppression.setEmailType(EmailType.EMAIL_INVITATIONS);

        List<EmailSuppression> emailSuppressionList = new ArrayList<>();
        emailSuppressionList.add(emailSuppression);

        //mock
        when(emailSuppressionRepository.findByEmailAndEventId(email,event.getEventId())).thenReturn(emailSuppressionList);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> emailSuppressionServiceImpl.addToEmailSupressionList(email, event.getEventId()));
        assertEquals(ALREAYD_UNSUBSCRIBED.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    public void test_getRedirectUrlOfUnsubscribeEmail_success() {

        //setup
        List<Object[]> objects = new ArrayList<>();

        //mock
        Mockito.doReturn(false).when(emailSuppressionServiceImpl).checkUserInSupressionList(email, event.getEventId(), EMAIL_INVITATIONS);
        when(roUserService.getUserByEmail(unsubscribeInfoDto.getEmail())).thenReturn(Optional.of(user));
        when(roEventService.getEventById(unsubscribeInfoDto.getEventId())).thenReturn(event);
        when(eventDesignDetailService.getEventOrWhiteLabelLogoLocationWithDefault(event,"1-400x150/")).thenReturn(imageLocation);
        when(eventService.getAllEventsForUser(Optional.of(user).get(), STRING_EMPTY)).thenReturn(objects);

        //Execution
        UnsubsubscribeReturnData redirectUrlOfUnsubscribeEmail = emailSuppressionServiceImpl.getRedirectUrlOfUnsubscribeEmail(token, unsubscribeInfoDto);

        assertNull(redirectUrlOfUnsubscribeEmail.getAccessToken());
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventUrl(), unsubsubscribeReturnData.getEventUrl());
        assertEquals(redirectUrlOfUnsubscribeEmail.getLogoLocation(), unsubsubscribeReturnData.getLogoLocation());
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventId(), 0);
        assertEquals(redirectUrlOfUnsubscribeEmail.getUrl(), "DEFAULT");
    }

    @Test
    public void test_getRedirectUrlOfUnsubscribeEmail_success_with_user_empty() {

        //setup
        List<Object[]> objects = new ArrayList<>();

        //mock

        when(roUserService.getUserByEmail(unsubscribeInfoDto.getEmail())).thenReturn(Optional.empty());
        when(roEventService.getEventById(unsubscribeInfoDto.getEventId())).thenReturn(event);
        when(eventDesignDetailService.getEventOrWhiteLabelLogoLocationWithDefault(event,"1-400x150/")).thenReturn(imageLocation);


        //Execution
        UnsubsubscribeReturnData redirectUrlOfUnsubscribeEmail = emailSuppressionServiceImpl.getRedirectUrlOfUnsubscribeEmail(token, unsubscribeInfoDto);

        assertEquals(redirectUrlOfUnsubscribeEmail.getAccessToken(), token);
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventUrl(), unsubsubscribeReturnData.getEventUrl());
        assertEquals(redirectUrlOfUnsubscribeEmail.getLogoLocation(), unsubsubscribeReturnData.getLogoLocation());
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventId(), event.getEventId());
        assertEquals(redirectUrlOfUnsubscribeEmail.getUrl(), "/u/myprofile");
    }

    @Test
    public void test_getRedirectUrlOfUnsubscribeEmail_throwException() {

        //setup
        List<Object[]> objects = new ArrayList<>();

        emailSuppression.setEmailType(EmailType.EMAIL_INVITATIONS);

        List<EmailSuppression> emailSuppressionList = new ArrayList<>();
        emailSuppressionList.add(emailSuppression);

        //mock
        when(roUserService.getUserByEmail(unsubscribeInfoDto.getEmail())).thenReturn(Optional.of(user));
        when(roEventService.getEventById(unsubscribeInfoDto.getEventId())).thenReturn(event);
        when(eventDesignDetailService.getEventOrWhiteLabelLogoLocationWithDefault(event,"1-400x150/")).thenReturn(imageLocation);
        when(eventService.getAllEventsForUser(Optional.of(user).get(), STRING_EMPTY)).thenReturn(objects);
        Mockito.doReturn(true).when(emailSuppressionServiceImpl).checkUserInSupressionList(email, event.getEventId(), EMAIL_INVITATIONS);

        //Execution
        emailSuppressionServiceImpl.getRedirectUrlOfUnsubscribeEmail(token, unsubscribeInfoDto);
    }

    @Test
    public void test_getRedirectUrlOfUnsubscribeEmail_success_with_token() {

        //setup
        Object[] objects = {user};
        List<Object[]> objectList = new ArrayList<>();
        objectList.add(objects);

        emailSuppression.setEmailType(EmailType.EMAIL_INVITATIONS);

        List<EmailSuppression> emailSuppressionList = new ArrayList<>();
        emailSuppressionList.add(emailSuppression);

        //mock
        when(roUserService.getUserByEmail(unsubscribeInfoDto.getEmail())).thenReturn(Optional.of(user));
        when(roEventService.getEventById(unsubscribeInfoDto.getEventId())).thenReturn(event);
        when(eventDesignDetailService.getEventOrWhiteLabelLogoLocationWithDefault(event,"1-400x150/")).thenReturn(imageLocation);
        when(eventService.getAllEventsForUser(Optional.of(user).get(), STRING_EMPTY)).thenReturn(objectList);



        //Execution
        UnsubsubscribeReturnData redirectUrlOfUnsubscribeEmail = emailSuppressionServiceImpl.getRedirectUrlOfUnsubscribeEmail(token, unsubscribeInfoDto);

        assertEquals(redirectUrlOfUnsubscribeEmail.getAccessToken(), token);
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventUrl(), unsubsubscribeReturnData.getEventUrl());
        assertEquals(redirectUrlOfUnsubscribeEmail.getLogoLocation(), unsubsubscribeReturnData.getLogoLocation());
        assertEquals(redirectUrlOfUnsubscribeEmail.getEventId(), event.getEventId());
        assertEquals(redirectUrlOfUnsubscribeEmail.getUrl(), "/u/myprofile");
    }

    @Test
    public void test_getEmailSuppressionList_success() {

        //setup
        emailSuppression.setDate(date);
        emailSuppression.setEmail(email);
        emailSuppression.setEventId(event.getEventId());
        //emailSuppression.setEmailType(EmailType.EMAIL_UNSUBSCRIBE);

        List<EmailSuppression> emailSuppressionList = new ArrayList<>();
        emailSuppressionList.add(emailSuppression);

        //mock
        when(emailSuppressionRepository.findByEventId(event.getEventId())).thenReturn(emailSuppressionList);

        //Execution
        List<String> emailSuppressionData = emailSuppressionServiceImpl.getEmailSuppressionList(event);

        assertTrue(emailSuppressionData.iterator().next().equalsIgnoreCase(emailSuppression.getEmail()));
    }

    @Test
    public void test_checkUserInSupressionList_success() {

        //setup
        emailSuppression.setDate(date);
        emailSuppression.setEmail(email);
        emailSuppression.setEventId(event.getEventId());
        //emailSuppression.setEmailType(EmailType.EMAIL_UNSUBSCRIBE);

        List<EmailSuppression> emailSuppressionList = new ArrayList<>();

        //mock
        when(emailSuppressionRepository.findByEmailAndEventId(email,event.getEventId())).thenReturn(emailSuppressionList);

        //Execution
        boolean userInSuppressionList = emailSuppressionServiceImpl.checkUserInSupressionList(email, event.getEventId(), emailType);

        assertFalse(userInSuppressionList);
    }

    @Test
    public void test_handleUnSubscribe_success() {

        //setup
        user.setEmail(email);

        //mock
        when(roEventService.getEventById(event.getEventId())).thenReturn(event);
        Mockito.doReturn(false).when(emailSuppressionServiceImpl).checkUserInSupressionList(email, event.getEventId(), EMAIL_INVITATIONS);

        //Execution
        emailSuppressionServiceImpl.handleUnSubscribe(user, event.getEventId());

        ArgumentCaptor<EmailSuppression> emailSuppressionArgumentCaptor = ArgumentCaptor.forClass(EmailSuppression.class);
        verify(emailSuppressionRepository,times(1)).save(emailSuppressionArgumentCaptor.capture());

        EmailSuppression emailSuppressionData = emailSuppressionArgumentCaptor.getValue();
        assertEquals(emailSuppressionData.getEmail(), email);
        assertEquals(emailSuppressionData.getEventId().longValue(), event.getEventId());
        assertEquals(emailSuppressionData.getEmailType(), EMAIL_INVITATIONS);
    }
}