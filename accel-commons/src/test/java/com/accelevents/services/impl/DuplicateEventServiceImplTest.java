package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.ModuleType;
import com.accelevents.enums.StaffRole;
import com.accelevents.events.templates.dto.DuplicateEventsTemplatesConfigDto;
import com.accelevents.exceptions.ForbiddenException;
import com.accelevents.repositories.EventRepository;
import com.accelevents.repositories.SeatingCategoryRepository;
import com.accelevents.repositories.WhiteLabelRepository;
import com.accelevents.ro.staff.ROStaffService;
import com.accelevents.services.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@ExtendWith(MockitoExtension.class)
public class DuplicateEventServiceImplTest {

    @Spy
    @InjectMocks
    private DuplicateEventServiceImpl duplicateEventServiceImpl;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private WhiteLabelService whiteLabelService;

    @Mock
    private WhiteLabelRepository whiteLabelRepository;

    @Mock
    private EventChecklistService eventChecklistService;

    @Mock
    private EventDesignDetailService eventDesignDetailService;

    @Mock
    private AuctionService auctionService;

    @Mock
    private ItemCategoryService itemCategoryService;

    @Mock
    private RaffleService raffleService;

    @Mock
    private RaffleTicketService raffleTicketService;

    @Mock
    private ItemService itemService;

    @Mock
    private ItemImgLocationsService itemImageLocationService;

    @Mock
    private  AuctionCustomSmsService auctionCustomSmsService;

    @Mock
    private TicketingService ticketingService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private TicketingTypeTicketService ticketingTypeTicketService;

    @Mock
    private RaffleCustomSmsService raffleCustomSmsService;

    @Mock
    private CauseAuctionService causeAuctionService;

    @Mock
    private DonationSettingsService donationSettingsService;

    @Mock
    private StripeService stripeService;

    @Mock
    private StaffService staffService;

    @Mock
    private WaitListSettingService waitListSettingService;

    @Mock
    private TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private CustomEmailService customEmailService;

    @Mock
    private TicketHolderRequiredAttributesService ticketHolderRequiredAttributesService;

    @Mock
    private TicketingCouponService ticketingCouponService;

    @Mock
    private TicketingAccessCodeService ticketingAccessCodeService;

    @Mock
    private RecurringEventsScheduleBRService recurringEventsScheduleService;

    @Mock
    private SeatingCategoryRepository seatingCategoryRepository;
    @Mock
    private ROStaffService roStaffService;

    private Event oldEvent, newEvent;
    private User hostUser;
    private Staff oldAdmin, newAdmin;
    private Auction oldAuction;
    private Raffle oldRaffle;
	private Ticketing oldTicketing, newTicketing;
    private CauseAuction oldCauseAuction, newCauseAuction;
    private WhiteLabel whiteLabel, newWL;
    private EventDesignDetail eventDesignDetail;
    private TransactionFeeConditionalLogic transactionFeeConditionalLogic;
    private CustomEmail customEmail;
    private Stripe oldStripe;
	private Item oldItem, newItem;
    private ItemCategory itemCategory;
    private AuctionCustomSms auctionCustomSms;
    private TicketHolderRequiredAttributes ticketHolderRequiredAttributes;
    private TicketingType ticketingType;
    private RaffleTicket raffleTicket;
    private RaffleCustomSms raffleCustomSms;
    private DonationSettings donationSettings;
    private WaitListSettings waitListSettings;
    private  ModuleType moduleType;
    private ItemImgLocations itemImgLocations;
    private Auction newAuction;
    private TicketingCoupon ticketingCoupon;
    private TicketingAccessCode ticketingAccessCode;
    private EventPlanConfig planConfig;

    private String customDisclaimer = "Custom Disclaimer";
    private String stripeCustomerId = "cus_FSjuTT9VAXQn8x";
    private Long id = 1L;
    private Long countNumberOfEvents = 10L;
    private long oldModuleId = 1L;
    private int numOfTicket = 10;
    private int price = 50;

    @BeforeEach
    void setUp() throws Exception {
        oldEvent = EventDataUtil.getEvent();
        newEvent = new Event();
        hostUser = EventDataUtil.getUser();
        oldStripe = new Stripe();
		Stripe newStripe = new Stripe();
        oldAdmin = new Staff();
        newAdmin = new Staff();
        whiteLabel = new WhiteLabel();
        newWL = new WhiteLabel();
        oldAuction = new Auction();
        oldRaffle = new Raffle();
		Raffle newRaffle = new Raffle();
        oldTicketing = new Ticketing();
        newTicketing = new Ticketing();
        oldCauseAuction = new CauseAuction();
        newCauseAuction = new CauseAuction();
        newAuction = new Auction();
        eventDesignDetail = new EventDesignDetail();
        transactionFeeConditionalLogic = new TransactionFeeConditionalLogic();
        customEmail = new CustomEmail();
        oldItem = new Item();
        newItem = new Item();
        itemCategory = new ItemCategory();
        auctionCustomSms = new AuctionCustomSms();
        ticketHolderRequiredAttributes = new TicketHolderRequiredAttributes();
        ticketingType = new TicketingType();
        raffleTicket = new RaffleTicket();
        raffleCustomSms = new RaffleCustomSms();
        donationSettings = new DonationSettings();
        waitListSettings = new WaitListSettings();
        itemImgLocations = new ItemImgLocations();
        ticketingAccessCode = new TicketingAccessCode();
        ticketingCoupon = new TicketingCoupon();
        planConfig = new EventPlanConfig();
    }

    //@Test
    void test_duplicateEvent_success() {

        //setup
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+oldEvent.getName();

        whiteLabel.setId(id);

        oldEvent.setWhiteLabel(whiteLabel);

        oldAdmin.setEvent(oldEvent);
        oldAdmin.setRole(StaffRole.admin);
        oldAdmin.setUser(hostUser);
        oldAdmin.setWhiteLabel(whiteLabel);
        oldAdmin.setId(id);
        oldAdmin.setStripeCustomerId(stripeCustomerId);

        oldAuction.setId(id);

        oldRaffle.setId(id);

        oldTicketing.setCollectTicketHolderAttributes(true);

        oldCauseAuction.setId(id);

        newWL.setSilentAuctionEnabled(true);
        newWL.setCauseAuctionEnabled(true);
        newWL.setRaffleEnabled(true);
        newWL.setTicketingEnabled(true);
        newWL.setAllowEditingOfDisclaimer(true);
        newWL.setCustomDisclaimer(customDisclaimer);

        eventDesignDetail.setEvent(oldEvent);

        List<TransactionFeeConditionalLogic> oldConditionalLogic = new ArrayList<>();
        oldConditionalLogic.add(transactionFeeConditionalLogic);

        oldStripe.setStaff(oldAdmin);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);
        oldItem.setItemCategory(itemCategory);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);

        List<TicketHolderRequiredAttributes> ticketingAttribute = new ArrayList<>();
        ticketingAttribute.add(ticketHolderRequiredAttributes);

        List<TicketingType> oldEventTickets = new ArrayList<>();
        oldEventTickets.add(ticketingType);

        List<Item> raffleItems = new ArrayList<>();
        raffleItems.add(oldItem);

        raffleTicket.setId(id);
        raffleTicket.setNumOfTicket(numOfTicket);
        raffleTicket.setPrice(price);
        raffleTicket.setRaffleId(0);

        Set<RaffleTicket> oldRaffleTicket = new HashSet<>();
        oldRaffleTicket.add(raffleTicket);

        List<Item> causeAuctionItemList = new ArrayList<>();

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(ticketingCouponService.getAllByEventId(oldEvent.getEventId())).thenReturn(Collections.emptyList());
        when(ticketingAccessCodeService.findByEvent(oldEvent)).thenReturn(Collections.emptyList());
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(oldAdmin);
        when(auctionService.find(oldEvent.getAuctionId())).thenReturn(oldAuction);
        when(raffleService.find(oldEvent.getRaffleId())).thenReturn(oldRaffle);
        when(ticketingHelperService.findTicketingByEvent(oldEvent)).thenReturn(oldTicketing);
        when(causeAuctionService.findByEvent(oldEvent)).thenReturn(oldCauseAuction);
        when(staffService.countNumberOfEventsByUser(hostUser)).thenReturn(countNumberOfEvents);
        when(whiteLabelRepository.findById(oldEvent.getWhiteLabel().getId())).thenReturn(Optional.of(newWL));
        when(eventDesignDetailService.findByEvent(oldEvent)).thenReturn(eventDesignDetail);
        when(transactionFeeConditionalLogicService.getRecordByEvent(oldEvent)).thenReturn(oldConditionalLogic);
        when(customEmailService.getCustomEmailByEventId(oldEvent.getEventId())).thenReturn(Optional.of(customEmail));
        when(stripeService.findByEventAndDefaultAccount(oldEvent, true)).thenReturn(oldStripe);
        when(itemService.getAllItems(oldAuction.getId(), ModuleType.AUCTION)).thenReturn(auctionItemList);
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);
        Mockito.doNothing().when(itemCategoryService).savewithsequence(itemCategory);
        when(itemService.savewithsequenceWithoutCacheClear(oldItem)).thenReturn(oldItem);
        when(auctionCustomSmsService.findByEventId(oldEvent)).thenReturn(auctionCustomSms);
        when(ticketHolderRequiredAttributesService.findByEventId(oldEvent)).thenReturn(ticketingAttribute);
        when(ticketHolderRequiredAttributesService.saveDefaultTicketAttributes(any())).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingTypeService.findAllByTicketing(0L, oldEvent)).thenReturn(oldEventTickets);
        when(itemService.getAllItems(oldRaffle.getId(), ModuleType.RAFFLE)).thenReturn(raffleItems);
        when(itemCategoryService.findByModuleIdAndType(oldModuleId, moduleType)).thenReturn(oldItemCategoryList);
        when(raffleCustomSmsService.findByEventId(oldEvent.getEventId())).thenReturn(raffleCustomSms);
        when(raffleTicketService.getAllTicketsByRaffleId(oldRaffle.getId())).thenReturn(oldRaffleTicket);
        when(itemService.getAllItems(oldCauseAuction.getId(), ModuleType.CAUSEAUCTION)).thenReturn(causeAuctionItemList);
        when(donationSettingsService.getByEventId(oldEvent.getEventId())).thenReturn(donationSettings);
        when(waitListSettingService.getWaitListSettingsByEventId(oldEvent.getEventId())).thenReturn(waitListSettings);
        Mockito.doNothing().when(whiteLabelService).sendNotificationEmailForCreateDuplicateEvent(newWL,oldEvent, newEvent, hostUser);

        //Execution
        duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), anyBoolean());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor.capture());

        Event newEventData = eventArgumentCaptor.getValue();
        assertEquals(newEventData.getName(), nameOrUrl);
        assertEquals(newEventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(newEventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(newEventData.getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(newEventData.getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(newEventData.getCreatedFrom().longValue(), oldEvent.getEventId());

        ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService,times(1)).save(eventDesignDetailArgumentCaptor.capture());

        EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
        assertEquals(eventDesignDetailData.getId(), 0);
        assertEquals(eventDesignDetailData.getEvent().getName(), nameOrUrl);
        assertTrue(eventDesignDetailData.getEvent().isSilentAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isCauseAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isRaffleEnabled());
        assertTrue(eventDesignDetailData.getEvent().isTicketingEnabled());
        assertEquals(eventDesignDetailData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(eventDesignDetailData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventDesignDetailData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(eventDesignDetailData.getEvent().getEventStatus());

        ArgumentCaptor<TransactionFeeConditionalLogic> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(TransactionFeeConditionalLogic.class);
        verify(transactionFeeConditionalLogicService,times(1)).save(transactionFeeConditionalLogicArgumentCaptor.capture());

        TransactionFeeConditionalLogic transactionFeeConditionalLogicData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(transactionFeeConditionalLogicData.getId(), 0);
        assertEquals(transactionFeeConditionalLogicData.getEvent().getName(), nameOrUrl);
        assertTrue(transactionFeeConditionalLogicData.getEvent().isSilentAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isCauseAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isRaffleEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isTicketingEnabled());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(transactionFeeConditionalLogicData.getEvent().getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getEvent().getName(), nameOrUrl);
        assertTrue(staffData.getEvent().isSilentAuctionEnabled());
        assertTrue(staffData.getEvent().isCauseAuctionEnabled());
        assertTrue(staffData.getEvent().isRaffleEnabled());
        assertTrue(staffData.getEvent().isTicketingEnabled());
        assertEquals(staffData.getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(staffData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(staffData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(staffData.getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<CustomEmail> customEmailArgumentCaptor = ArgumentCaptor.forClass(CustomEmail.class);
        verify(customEmailService,times(1)).save(customEmailArgumentCaptor.capture());

        CustomEmail customEmailData = customEmailArgumentCaptor.getValue();
        assertEquals(customEmailData.getId(), 0);
        assertEquals(customEmailData.getEventId(), 0);

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe stripeData = stripeArgumentCaptor.getValue();
        assertEquals(stripeData.getId().longValue(), 0);
        assertTrue(stripeData.isDefaultAccount());
        assertEquals(stripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(stripeData.getEvent().getName(), nameOrUrl);
        assertTrue(stripeData.getEvent().isSilentAuctionEnabled());
        assertTrue(stripeData.getEvent().isCauseAuctionEnabled());
        assertTrue(stripeData.getEvent().isRaffleEnabled());
        assertTrue(stripeData.getEvent().isTicketingEnabled());
        assertEquals(stripeData.getStaff().getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(stripeData.getStaff().getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(stripeData.getStaff().getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
        verify(auctionService,times(1)).save(auctionArgumentCaptor.capture());

        Auction auctionData = auctionArgumentCaptor.getValue();
        assertEquals(auctionData.getId(), 0);
        assertEquals(auctionData.getEventId().longValue(), 0);
        assertFalse(auctionData.isActivated());
        assertNull(auctionData.getAuctionStatus());

        ArgumentCaptor<AuctionCustomSms> auctionCustomSmsArgumentCaptor = ArgumentCaptor.forClass(AuctionCustomSms.class);
        verify(auctionCustomSmsService,times(1)).save(auctionCustomSmsArgumentCaptor.capture());

        AuctionCustomSms auctionCustomSmsData = auctionCustomSmsArgumentCaptor.getValue();
        assertEquals(auctionCustomSmsData.getId(), 0);
        assertEquals(auctionCustomSmsData.getEventId(), 0);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(2)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(ticketingData.getEventid().getEventId(), 0);
//        assertTrue(ticketingData.getActivated());
        assertEquals(ticketingData.isAllowEditingOfDisclaimer(), newWL.isAllowEditingOfDisclaimer());
        assertEquals(ticketingData.getCustomDisclaimer(), newWL.getCustomDisclaimer());
        assertEquals(ticketingData.getId(), 0);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(ticketHolderRequiredAttributesService,times(1)).saveDefaultTicketAttributes(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes ticketHolderRequiredAttributesData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributesData.getEventid().getEventId(), 0);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService,times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType ticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketingTypeData.getTicketing().getEventid().getName(), nameOrUrl);
        assertTrue(ticketingTypeData.getTicketing().getEventid().isSilentAuctionEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isCauseAuctionEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isRaffleEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isTicketingEnabled());
        assertEquals(ticketingTypeData.getId(), 0);

        ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
        verify(raffleService,times(1)).save(raffleArgumentCaptor.capture());

        Raffle raffleData = raffleArgumentCaptor.getValue();
        assertEquals(raffleData.getId(), 0);
        assertFalse(raffleData.isActivated());
        assertEquals(raffleData.getEventId(), 0);

        ArgumentCaptor<RaffleTicket> raffleTicketArgumentCaptor = ArgumentCaptor.forClass(RaffleTicket.class);
        verify(raffleTicketService,times(1)).save(raffleTicketArgumentCaptor.capture());

        RaffleTicket raffleTicketData = raffleTicketArgumentCaptor.getValue();
        assertEquals(raffleTicketData.getNumOfTicket(), raffleTicket.getNumOfTicket());
        assertEquals(raffleTicketData.getPrice(), raffleTicket.getPrice());
        assertEquals(raffleTicketData.getRaffleId(), 0);

        ArgumentCaptor<RaffleCustomSms> raffleCustomSmsArgumentCaptor = ArgumentCaptor.forClass(RaffleCustomSms.class);
        verify(raffleCustomSmsService,times(1)).save(raffleCustomSmsArgumentCaptor.capture());

        RaffleCustomSms raffleCustomSmsData = raffleCustomSmsArgumentCaptor.getValue();
        assertEquals(raffleCustomSmsData.getId(), 0);
        assertEquals(raffleCustomSmsData.getEventId(), 0);

        ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
        verify(causeAuctionService,times(1)).save(causeAuctionArgumentCaptor.capture());

        CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
        assertEquals(causeAuctionData.getId(), 0);
        assertEquals(causeAuctionData.getEventId(), 0);
        assertFalse(causeAuctionData.isActivated());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService,times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
        assertEquals(donationSettingsData.getId(), 0);
        assertEquals(donationSettingsData.isTextToGiveActivated(), newWL.isDonationEnabled());
        assertEquals(causeAuctionData.getEventId(), 0);

        ArgumentCaptor<Event> eventArgumentCaptor1 = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor1.capture());

        Event eventData1 = eventArgumentCaptor1.getValue();
        assertEquals(eventData1.getCauseAuctionId(), 0);

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingService,times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettingsData = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettingsData.getEventId(), 0);
        assertEquals(waitListSettingsData.getId(), 0);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventChecklistData.getEventId(), 0);
        assertEquals(eventChecklistData.isActivatePaymentProcessing(), oldStripe.isActivated());
        assertTrue(eventChecklistData.isAuctionItemAdded());
        assertTrue(eventChecklistData.isRaffleItemAdded());
        assertTrue(eventChecklistData.isCauseAuctionItemAdded());
    }
    //@Test
    void test_duplicateEvent_successWithRecurringeventTrue() {

        //setup
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+oldEvent.getName();

        whiteLabel.setId(id);

        oldEvent.setWhiteLabel(whiteLabel);

        oldAdmin.setEvent(oldEvent);
        oldAdmin.setRole(StaffRole.admin);
        oldAdmin.setUser(hostUser);
        oldAdmin.setWhiteLabel(whiteLabel);
        oldAdmin.setId(id);
        oldAdmin.setStripeCustomerId(stripeCustomerId);

        oldAuction.setId(id);

        oldRaffle.setId(id);

        oldTicketing.setCollectTicketHolderAttributes(true);
        oldTicketing.setRecurringEvent(true);

        oldCauseAuction.setId(id);

        newWL.setSilentAuctionEnabled(true);
        newWL.setCauseAuctionEnabled(true);
        newWL.setRaffleEnabled(true);
        newWL.setTicketingEnabled(true);
        newWL.setAllowEditingOfDisclaimer(true);
        newWL.setCustomDisclaimer(customDisclaimer);

        eventDesignDetail.setEvent(oldEvent);

        List<TransactionFeeConditionalLogic> oldConditionalLogic = new ArrayList<>();
        oldConditionalLogic.add(transactionFeeConditionalLogic);

        oldStripe.setStaff(oldAdmin);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);
        oldItem.setItemCategory(itemCategory);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);

        List<TicketHolderRequiredAttributes> ticketingAttribute = new ArrayList<>();
        ticketHolderRequiredAttributes.setRecurringEventId(0L);
        ticketHolderRequiredAttributes.setCreatedFrom(0L);
        ticketingAttribute.add(ticketHolderRequiredAttributes);

        List<TicketingType> oldEventTickets = new ArrayList<>();
        ticketingType.setRecurringEventId(0L);
        ticketingType.setCreatedFrom(0L);
        oldEventTickets.add(ticketingType);

        List<Item> raffleItems = new ArrayList<>();
        raffleItems.add(oldItem);

        raffleTicket.setId(id);
        raffleTicket.setNumOfTicket(numOfTicket);
        raffleTicket.setPrice(price);
        raffleTicket.setRaffleId(0);

        Set<RaffleTicket> oldRaffleTicket = new HashSet<>();
        oldRaffleTicket.add(raffleTicket);

        List<Item> causeAuctionItemList = new ArrayList<>();

        RecurringEventSchedule recurringEventSchedule = new RecurringEventSchedule();
        recurringEventSchedule.setId(1L);

        List<RecurringEvents> oldRecurringEventsListFromDb = new ArrayList<>();
        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventSchedule(recurringEventSchedule);

        oldRecurringEventsListFromDb.add(recurringEvents);

        RecurringEventSchedule oldRES = new RecurringEventSchedule();
        oldRES.setId(0L);

        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();
        ticketingCoupon.setRecurringEventId(0L);
        ticketingCoupon.setCreatedFrom(0L);
        ticketingCouponList.add(ticketingCoupon);

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCode.setRecurringEventId(0L);
        ticketingAccessCode.setCreatedFrom(0L);
        ticketingAccessCodeList.add(ticketingAccessCode);

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.getAllByEvent(any())).thenReturn(ticketingAccessCodeList);
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(oldAdmin);
        when(auctionService.find(oldEvent.getAuctionId())).thenReturn(oldAuction);
        when(raffleService.find(oldEvent.getRaffleId())).thenReturn(oldRaffle);
        when(ticketingHelperService.findTicketingByEvent(oldEvent)).thenReturn(oldTicketing);
        when(causeAuctionService.findByEvent(oldEvent)).thenReturn(oldCauseAuction);
        when(staffService.countNumberOfEventsByUser(hostUser)).thenReturn(countNumberOfEvents);
        when(whiteLabelRepository.findById(oldEvent.getWhiteLabel().getId())).thenReturn(Optional.of(newWL));
        when(eventDesignDetailService.findByEvent(oldEvent)).thenReturn(eventDesignDetail);
        when(transactionFeeConditionalLogicService.getRecordByEvent(oldEvent)).thenReturn(oldConditionalLogic);
        when(customEmailService.getCustomEmailByEventId(oldEvent.getEventId())).thenReturn(Optional.of(customEmail));
        when(stripeService.findByEventAndDefaultAccount(oldEvent, true)).thenReturn(oldStripe);
        when(itemService.getAllItems(oldAuction.getId(), ModuleType.AUCTION)).thenReturn(auctionItemList);
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);
        Mockito.doNothing().when(itemCategoryService).savewithsequence(itemCategory);
        when(itemService.savewithsequenceWithoutCacheClear(oldItem)).thenReturn(oldItem);
        when(auctionCustomSmsService.findByEventId(oldEvent)).thenReturn(auctionCustomSms);
        when(recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(any())).thenReturn(oldRecurringEventsListFromDb);
        when(recurringEventsScheduleService.findRecurringEventScheduleById(anyLong())).thenReturn(oldRES);
        when(recurringEventsScheduleService.save((RecurringEventSchedule) any())).thenReturn(oldRES);
        when(recurringEventsScheduleService.saveDuplicateRecurringEvent(any())).thenReturn(recurringEvents);
        when(ticketingTypeTicketService.getTicketingTypesByRecurringList(anyList())).thenReturn(oldEventTickets);
        when(ticketingTypeService.setPositionForTicketingTypeAndsaveTicketingType(any())).thenReturn(ticketingType);
        when(ticketHolderRequiredAttributesService.getTicketingAttributesByRecurringEventList(anyList())).thenReturn(ticketingAttribute);
        when(ticketingCouponService.save(any())).thenReturn(ticketingCoupon);
        when(ticketingCouponService.getTicketingCouponByRecurringEventList(anyList())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.save(any())).thenReturn(ticketingAccessCode);
        when(ticketingAccessCodeService.getTicketingAccessCodeByRecurringEventList(anyList())).thenReturn(ticketingAccessCodeList);
        when(ticketHolderRequiredAttributesService.findByEventId(oldEvent)).thenReturn(ticketingAttribute);
        when(ticketHolderRequiredAttributesService.saveDefaultTicketAttributes(any())).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingTypeService.findAllByTicketing(0L, oldEvent)).thenReturn(oldEventTickets);
        when(itemService.getAllItems(oldRaffle.getId(), ModuleType.RAFFLE)).thenReturn(raffleItems);
        when(itemCategoryService.findByModuleIdAndType(oldModuleId, moduleType)).thenReturn(oldItemCategoryList);
        when(raffleCustomSmsService.findByEventId(oldEvent.getEventId())).thenReturn(raffleCustomSms);
        when(raffleTicketService.getAllTicketsByRaffleId(oldRaffle.getId())).thenReturn(oldRaffleTicket);
        when(itemService.getAllItems(oldCauseAuction.getId(), ModuleType.CAUSEAUCTION)).thenReturn(causeAuctionItemList);
        when(donationSettingsService.getByEventId(oldEvent.getEventId())).thenReturn(donationSettings);
        when(waitListSettingService.getWaitListSettingsByEventId(oldEvent.getEventId())).thenReturn(waitListSettings);
        Mockito.doNothing().when(whiteLabelService).sendNotificationEmailForCreateDuplicateEvent(newWL,oldEvent, newEvent, hostUser);
        when(seatingCategoryRepository.findByEventId(oldEvent.getEventId())).thenReturn(Collections.EMPTY_LIST);
        //Execution
        duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), anyBoolean());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor.capture());

        Event newEventData = eventArgumentCaptor.getValue();
        assertEquals(newEventData.getName(), nameOrUrl);
        assertEquals(newEventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(newEventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(newEventData.getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(newEventData.getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(newEventData.getCreatedFrom().longValue(), oldEvent.getEventId());

        ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService,times(1)).save(eventDesignDetailArgumentCaptor.capture());

        EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
        assertEquals(eventDesignDetailData.getId(), 0);
        assertEquals(eventDesignDetailData.getEvent().getName(), nameOrUrl);
        assertTrue(eventDesignDetailData.getEvent().isSilentAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isCauseAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isRaffleEnabled());
        assertTrue(eventDesignDetailData.getEvent().isTicketingEnabled());
        assertEquals(eventDesignDetailData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(eventDesignDetailData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventDesignDetailData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(eventDesignDetailData.getEvent().getEventStatus());

        ArgumentCaptor<TransactionFeeConditionalLogic> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(TransactionFeeConditionalLogic.class);
        verify(transactionFeeConditionalLogicService,times(1)).save(transactionFeeConditionalLogicArgumentCaptor.capture());

        TransactionFeeConditionalLogic transactionFeeConditionalLogicData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(transactionFeeConditionalLogicData.getId(), 0);
        assertEquals(transactionFeeConditionalLogicData.getEvent().getName(), nameOrUrl);
        assertTrue(transactionFeeConditionalLogicData.getEvent().isSilentAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isCauseAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isRaffleEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isTicketingEnabled());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(transactionFeeConditionalLogicData.getEvent().getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getEvent().getName(), nameOrUrl);
        assertTrue(staffData.getEvent().isSilentAuctionEnabled());
        assertTrue(staffData.getEvent().isCauseAuctionEnabled());
        assertTrue(staffData.getEvent().isRaffleEnabled());
        assertTrue(staffData.getEvent().isTicketingEnabled());
        assertEquals(staffData.getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(staffData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(staffData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(staffData.getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<CustomEmail> customEmailArgumentCaptor = ArgumentCaptor.forClass(CustomEmail.class);
        verify(customEmailService,times(1)).save(customEmailArgumentCaptor.capture());

        CustomEmail customEmailData = customEmailArgumentCaptor.getValue();
        assertEquals(customEmailData.getId(), 0);
        assertEquals(customEmailData.getEventId(), 0);

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe stripeData = stripeArgumentCaptor.getValue();
        assertEquals(stripeData.getId().longValue(), 0);
        assertTrue(stripeData.isDefaultAccount());
        assertEquals(stripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(stripeData.getEvent().getName(), nameOrUrl);
        assertTrue(stripeData.getEvent().isSilentAuctionEnabled());
        assertTrue(stripeData.getEvent().isCauseAuctionEnabled());
        assertTrue(stripeData.getEvent().isRaffleEnabled());
        assertTrue(stripeData.getEvent().isTicketingEnabled());
        assertEquals(stripeData.getStaff().getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(stripeData.getStaff().getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(stripeData.getStaff().getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
        verify(auctionService,times(1)).save(auctionArgumentCaptor.capture());

        Auction auctionData = auctionArgumentCaptor.getValue();
        assertEquals(auctionData.getId(), 0);
        assertEquals(auctionData.getEventId().longValue(), 0);
        assertFalse(auctionData.isActivated());
        assertNull(auctionData.getAuctionStatus());

        ArgumentCaptor<AuctionCustomSms> auctionCustomSmsArgumentCaptor = ArgumentCaptor.forClass(AuctionCustomSms.class);
        verify(auctionCustomSmsService,times(1)).save(auctionCustomSmsArgumentCaptor.capture());

        AuctionCustomSms auctionCustomSmsData = auctionCustomSmsArgumentCaptor.getValue();
        assertEquals(auctionCustomSmsData.getId(), 0);
        assertEquals(auctionCustomSmsData.getEventId(), 0);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(2)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(ticketingData.getEventid().getEventId(), 0);
//        assertTrue(ticketingData.getActivated());
        assertEquals(ticketingData.isAllowEditingOfDisclaimer(), newWL.isAllowEditingOfDisclaimer());
        assertEquals(ticketingData.getCustomDisclaimer(), newWL.getCustomDisclaimer());
        assertEquals(ticketingData.getId(), 0);


        Class<ArrayList<TicketHolderRequiredAttributes>> listClass = (Class<ArrayList<TicketHolderRequiredAttributes>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketHolderRequiredAttributes>> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketHolderRequiredAttributesService, times(1)).saveAll(ticketHolderRequiredAttributesArgumentCaptor.capture());

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributesData.get(0).getEventid().getEventId(), 0);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributeArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(ticketHolderRequiredAttributesService,times(1)).saveDefaultTicketAttributes(ticketHolderRequiredAttributeArgumentCaptor.capture());

        TicketHolderRequiredAttributes ticketHolderRequiredAttributeData = ticketHolderRequiredAttributeArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributeData.getEventid().getEventId(), 0);

        Class<ArrayList<TicketingCoupon>> listClass1 = (Class<ArrayList<TicketingCoupon>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingCoupon>> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(listClass1);
        verify(ticketingCouponService, times(1)).saveAll(ticketingCouponArgumentCaptor.capture());

        List<TicketingCoupon> ticketingCouponData = ticketingCouponArgumentCaptor.getValue();
        assertEquals(ticketingCouponData.get(0).getEventId(), 0);

        Class<ArrayList<TicketingAccessCode>> listClass2 = (Class<ArrayList<TicketingAccessCode>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingAccessCode>> ticketingAccessCodeArgumentCaptor = ArgumentCaptor.forClass(listClass2);
        verify(ticketingAccessCodeService, times(1)).saveAll(ticketingAccessCodeArgumentCaptor.capture());

        List<TicketingAccessCode> ticketingAccessCodeData = ticketingAccessCodeArgumentCaptor.getValue();
        assertEquals(ticketingAccessCodeData.get(0).getEventId().getEventId(), 0);

        ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
        verify(raffleService,times(1)).save(raffleArgumentCaptor.capture());

        Raffle raffleData = raffleArgumentCaptor.getValue();
        assertEquals(raffleData.getId(), 0);
        assertFalse(raffleData.isActivated());
        assertEquals(raffleData.getEventId(), 0);

        ArgumentCaptor<RaffleTicket> raffleTicketArgumentCaptor = ArgumentCaptor.forClass(RaffleTicket.class);
        verify(raffleTicketService,times(1)).save(raffleTicketArgumentCaptor.capture());

        RaffleTicket raffleTicketData = raffleTicketArgumentCaptor.getValue();
        assertEquals(raffleTicketData.getNumOfTicket(), raffleTicket.getNumOfTicket());
        assertEquals(raffleTicketData.getPrice(), raffleTicket.getPrice());
        assertEquals(raffleTicketData.getRaffleId(), 0);

        ArgumentCaptor<RaffleCustomSms> raffleCustomSmsArgumentCaptor = ArgumentCaptor.forClass(RaffleCustomSms.class);
        verify(raffleCustomSmsService,times(1)).save(raffleCustomSmsArgumentCaptor.capture());

        RaffleCustomSms raffleCustomSmsData = raffleCustomSmsArgumentCaptor.getValue();
        assertEquals(raffleCustomSmsData.getId(), 0);
        assertEquals(raffleCustomSmsData.getEventId(), 0);

        ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
        verify(causeAuctionService,times(1)).save(causeAuctionArgumentCaptor.capture());

        CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
        assertEquals(causeAuctionData.getId(), 0);
        assertEquals(causeAuctionData.getEventId(), 0);
        assertFalse(causeAuctionData.isActivated());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService,times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
        assertEquals(donationSettingsData.getId(), 0);
        assertEquals(donationSettingsData.isTextToGiveActivated(), newWL.isDonationEnabled());
        assertEquals(causeAuctionData.getEventId(), 0);

        ArgumentCaptor<Event> eventArgumentCaptor1 = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor1.capture());

        Event eventData1 = eventArgumentCaptor1.getValue();
        assertEquals(eventData1.getCauseAuctionId(), 0);

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingService,times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettingsData = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettingsData.getEventId(), 0);
        assertEquals(waitListSettingsData.getId(), 0);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventChecklistData.getEventId(), 0);
        assertEquals(eventChecklistData.isActivatePaymentProcessing(), oldStripe.isActivated());
        assertTrue(eventChecklistData.isAuctionItemAdded());
        assertTrue(eventChecklistData.isRaffleItemAdded());
        assertTrue(eventChecklistData.isCauseAuctionItemAdded());
    }

    //@Test
    void test_duplicateEvent_successWithRecurringeventTrueAndForCustomRecurringEvent() {

        //setup
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+oldEvent.getName();

        whiteLabel.setId(id);

        oldEvent.setWhiteLabel(whiteLabel);

        oldAdmin.setEvent(oldEvent);
        oldAdmin.setRole(StaffRole.admin);
        oldAdmin.setUser(hostUser);
        oldAdmin.setWhiteLabel(whiteLabel);
        oldAdmin.setId(id);
        oldAdmin.setStripeCustomerId(stripeCustomerId);

        oldAuction.setId(id);

        oldRaffle.setId(id);

        oldTicketing.setCollectTicketHolderAttributes(true);
        oldTicketing.setRecurringEvent(true);

        oldCauseAuction.setId(id);

        newWL.setSilentAuctionEnabled(true);
        newWL.setCauseAuctionEnabled(true);
        newWL.setRaffleEnabled(true);
        newWL.setTicketingEnabled(true);
        newWL.setAllowEditingOfDisclaimer(true);
        newWL.setCustomDisclaimer(customDisclaimer);

        eventDesignDetail.setEvent(oldEvent);

        List<TransactionFeeConditionalLogic> oldConditionalLogic = new ArrayList<>();
        oldConditionalLogic.add(transactionFeeConditionalLogic);

        oldStripe.setStaff(oldAdmin);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);
        oldItem.setItemCategory(itemCategory);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);

        List<TicketHolderRequiredAttributes> ticketingAttribute = new ArrayList<>();
        ticketHolderRequiredAttributes.setRecurringEventId(0L);
        ticketHolderRequiredAttributes.setCreatedFrom(-1L);
        ticketingAttribute.add(ticketHolderRequiredAttributes);

        List<TicketingType> oldEventTickets = new ArrayList<>();
        ticketingType.setRecurringEventId(0L);
        ticketingType.setCreatedFrom(-1L);
        oldEventTickets.add(ticketingType);

        List<Item> raffleItems = new ArrayList<>();
        raffleItems.add(oldItem);

        raffleTicket.setId(id);
        raffleTicket.setNumOfTicket(numOfTicket);
        raffleTicket.setPrice(price);
        raffleTicket.setRaffleId(0);

        Set<RaffleTicket> oldRaffleTicket = new HashSet<>();
        oldRaffleTicket.add(raffleTicket);

        List<Item> causeAuctionItemList = new ArrayList<>();

        RecurringEventSchedule oldRES = new RecurringEventSchedule();
        oldRES.setId(0L);

        List<RecurringEvents> oldRecurringEventsListFromDb = new ArrayList<>();
        RecurringEvents recurringEvents = new RecurringEvents();
        recurringEvents.setRecurringEventSchedule(oldRES);
        oldRecurringEventsListFromDb.add(recurringEvents);

        List<TicketingCoupon> ticketingCouponList = new ArrayList<>();
        ticketingCoupon.setRecurringEventId(0L);
        ticketingCoupon.setCreatedFrom(-1L);
        ticketingCouponList.add(ticketingCoupon);

        List<TicketingAccessCode> ticketingAccessCodeList = new ArrayList<>();
        ticketingAccessCode.setRecurringEventId(0L);
        ticketingAccessCode.setCreatedFrom(-1L);
        ticketingAccessCodeList.add(ticketingAccessCode);

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(ticketingCouponService.getAllByEventId(anyLong())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.getAllByEvent(any())).thenReturn(ticketingAccessCodeList);
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(oldAdmin);
        when(auctionService.find(oldEvent.getAuctionId())).thenReturn(oldAuction);
        when(raffleService.find(oldEvent.getRaffleId())).thenReturn(oldRaffle);
        when(ticketingHelperService.findTicketingByEvent(oldEvent)).thenReturn(oldTicketing);
        when(causeAuctionService.findByEvent(oldEvent)).thenReturn(oldCauseAuction);
        when(staffService.countNumberOfEventsByUser(hostUser)).thenReturn(countNumberOfEvents);
        when(whiteLabelRepository.findById(oldEvent.getWhiteLabel().getId())).thenReturn(Optional.of(newWL));
        when(eventDesignDetailService.findByEvent(oldEvent)).thenReturn(eventDesignDetail);
        when(transactionFeeConditionalLogicService.getRecordByEvent(oldEvent)).thenReturn(oldConditionalLogic);
        when(customEmailService.getCustomEmailByEventId(oldEvent.getEventId())).thenReturn(Optional.of(customEmail));
        when(stripeService.findByEventAndDefaultAccount(oldEvent, true)).thenReturn(oldStripe);
        when(itemService.getAllItems(oldAuction.getId(), ModuleType.AUCTION)).thenReturn(auctionItemList);
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);
        Mockito.doNothing().when(itemCategoryService).savewithsequence(itemCategory);
        when(itemService.savewithsequenceWithoutCacheClear(oldItem)).thenReturn(oldItem);
        when(auctionCustomSmsService.findByEventId(oldEvent)).thenReturn(auctionCustomSms);
        when(recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(any())).thenReturn(oldRecurringEventsListFromDb);
        when(recurringEventsScheduleService.save((RecurringEventSchedule) any())).thenReturn(oldRES);
        when(recurringEventsScheduleService.saveDuplicateRecurringEvent(any())).thenReturn(recurringEvents);
        when(ticketingTypeTicketService.getTicketingTypesByRecurringList(anyList())).thenReturn(oldEventTickets);
        when(ticketingTypeService.setPositionForTicketingTypeAndsaveTicketingType(any())).thenReturn(ticketingType);
        when(ticketHolderRequiredAttributesService.getTicketingAttributesByRecurringEventList(anyList())).thenReturn(ticketingAttribute);
        when(ticketingCouponService.save(any())).thenReturn(ticketingCoupon);
        when(ticketingCouponService.getTicketingCouponByRecurringEventList(anyList())).thenReturn(ticketingCouponList);
        when(ticketingAccessCodeService.save(any())).thenReturn(ticketingAccessCode);
        when(ticketingAccessCodeService.getTicketingAccessCodeByRecurringEventList(anyList())).thenReturn(ticketingAccessCodeList);
        when(ticketHolderRequiredAttributesService.findByEventId(oldEvent)).thenReturn(ticketingAttribute);
        when(ticketHolderRequiredAttributesService.saveDefaultTicketAttributes(any())).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingTypeService.findAllByTicketing(0L, oldEvent)).thenReturn(oldEventTickets);
        when(itemService.getAllItems(oldRaffle.getId(), ModuleType.RAFFLE)).thenReturn(raffleItems);
        when(itemCategoryService.findByModuleIdAndType(oldModuleId, moduleType)).thenReturn(oldItemCategoryList);
        when(raffleCustomSmsService.findByEventId(oldEvent.getEventId())).thenReturn(raffleCustomSms);
        when(raffleTicketService.getAllTicketsByRaffleId(oldRaffle.getId())).thenReturn(oldRaffleTicket);
        when(itemService.getAllItems(oldCauseAuction.getId(), ModuleType.CAUSEAUCTION)).thenReturn(causeAuctionItemList);
        when(donationSettingsService.getByEventId(oldEvent.getEventId())).thenReturn(donationSettings);
        when(waitListSettingService.getWaitListSettingsByEventId(oldEvent.getEventId())).thenReturn(waitListSettings);
        Mockito.doNothing().when(whiteLabelService).sendNotificationEmailForCreateDuplicateEvent(newWL,oldEvent, newEvent, hostUser);

        //Execution
        duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), anyBoolean());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor.capture());

        Event newEventData = eventArgumentCaptor.getValue();
        assertEquals(newEventData.getName(), nameOrUrl);
        assertEquals(newEventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(newEventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(newEventData.getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(newEventData.getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(newEventData.getCreatedFrom().longValue(), oldEvent.getEventId());

        ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService,times(1)).save(eventDesignDetailArgumentCaptor.capture());

        EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
        assertEquals(eventDesignDetailData.getId(), 0);
        assertEquals(eventDesignDetailData.getEvent().getName(), nameOrUrl);
        assertTrue(eventDesignDetailData.getEvent().isSilentAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isCauseAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isRaffleEnabled());
        assertTrue(eventDesignDetailData.getEvent().isTicketingEnabled());
        assertEquals(eventDesignDetailData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(eventDesignDetailData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventDesignDetailData.getEvent().getEquivalentTimeZone(),oldEvent.getEquivalentTimeZone());
        assertNull(eventDesignDetailData.getEvent().getEventStatus());

        ArgumentCaptor<TransactionFeeConditionalLogic> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(TransactionFeeConditionalLogic.class);
        verify(transactionFeeConditionalLogicService,times(1)).save(transactionFeeConditionalLogicArgumentCaptor.capture());

        TransactionFeeConditionalLogic transactionFeeConditionalLogicData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(transactionFeeConditionalLogicData.getId(), 0);
        assertEquals(transactionFeeConditionalLogicData.getEvent().getName(), nameOrUrl);
        assertTrue(transactionFeeConditionalLogicData.getEvent().isSilentAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isCauseAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isRaffleEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isTicketingEnabled());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getEquivalentTimeZone(),oldEvent.getEquivalentTimeZone());
        assertNull(transactionFeeConditionalLogicData.getEvent().getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getEvent().getName(), nameOrUrl);
        assertTrue(staffData.getEvent().isSilentAuctionEnabled());
        assertTrue(staffData.getEvent().isCauseAuctionEnabled());
        assertTrue(staffData.getEvent().isRaffleEnabled());
        assertTrue(staffData.getEvent().isTicketingEnabled());
        assertEquals(staffData.getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(staffData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(staffData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(staffData.getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<CustomEmail> customEmailArgumentCaptor = ArgumentCaptor.forClass(CustomEmail.class);
        verify(customEmailService,times(1)).save(customEmailArgumentCaptor.capture());

        CustomEmail customEmailData = customEmailArgumentCaptor.getValue();
        assertEquals(customEmailData.getId(), 0);
        assertEquals(customEmailData.getEventId(), 0);

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe stripeData = stripeArgumentCaptor.getValue();
        assertEquals(stripeData.getId().longValue(), 0);
        assertTrue(stripeData.isDefaultAccount());
        assertEquals(stripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(stripeData.getEvent().getName(), nameOrUrl);
        assertTrue(stripeData.getEvent().isSilentAuctionEnabled());
        assertTrue(stripeData.getEvent().isCauseAuctionEnabled());
        assertTrue(stripeData.getEvent().isRaffleEnabled());
        assertTrue(stripeData.getEvent().isTicketingEnabled());
        assertEquals(stripeData.getStaff().getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(stripeData.getStaff().getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(stripeData.getStaff().getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
        verify(auctionService,times(1)).save(auctionArgumentCaptor.capture());

        Auction auctionData = auctionArgumentCaptor.getValue();
        assertEquals(auctionData.getId(), 0);
        assertEquals(auctionData.getEventId().longValue(), 0);
        assertFalse(auctionData.isActivated());
        assertNull(auctionData.getAuctionStatus());

        ArgumentCaptor<AuctionCustomSms> auctionCustomSmsArgumentCaptor = ArgumentCaptor.forClass(AuctionCustomSms.class);
        verify(auctionCustomSmsService,times(1)).save(auctionCustomSmsArgumentCaptor.capture());

        AuctionCustomSms auctionCustomSmsData = auctionCustomSmsArgumentCaptor.getValue();
        assertEquals(auctionCustomSmsData.getId(), 0);
        assertEquals(auctionCustomSmsData.getEventId(), 0);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(2)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(ticketingData.getEventid().getEventId(), 0);
//        assertTrue(ticketingData.getActivated());
        assertEquals(ticketingData.isAllowEditingOfDisclaimer(), newWL.isAllowEditingOfDisclaimer());
        assertEquals(ticketingData.getCustomDisclaimer(), newWL.getCustomDisclaimer());
        assertEquals(ticketingData.getId(), 0);


        Class<ArrayList<TicketHolderRequiredAttributes>> listClass = (Class<ArrayList<TicketHolderRequiredAttributes>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketHolderRequiredAttributes>> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(listClass);
        verify(ticketHolderRequiredAttributesService, times(1)).saveAll(ticketHolderRequiredAttributesArgumentCaptor.capture());

        List<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributesData.get(0).getEventid().getEventId(), 0);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributeArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(ticketHolderRequiredAttributesService,times(1)).saveDefaultTicketAttributes(ticketHolderRequiredAttributeArgumentCaptor.capture());

        TicketHolderRequiredAttributes ticketHolderRequiredAttributeData = ticketHolderRequiredAttributeArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributeData.getEventid().getEventId(), 0);

        Class<ArrayList<TicketingCoupon>> listClass1 = (Class<ArrayList<TicketingCoupon>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingCoupon>> ticketingCouponArgumentCaptor = ArgumentCaptor.forClass(listClass1);
        verify(ticketingCouponService, times(1)).saveAll(ticketingCouponArgumentCaptor.capture());

        List<TicketingCoupon> ticketingCouponData = ticketingCouponArgumentCaptor.getValue();
        assertEquals(ticketingCouponData.get(0).getEventId(), 0);

        Class<ArrayList<TicketingAccessCode>> listClass2 = (Class<ArrayList<TicketingAccessCode>>) (Class) ArrayList.class;
        ArgumentCaptor<ArrayList<TicketingAccessCode>> ticketingAccessCodeArgumentCaptor = ArgumentCaptor.forClass(listClass2);
        verify(ticketingAccessCodeService, times(1)).saveAll(ticketingAccessCodeArgumentCaptor.capture());

        List<TicketingAccessCode> ticketingAccessCodeData = ticketingAccessCodeArgumentCaptor.getValue();
        assertEquals(ticketingAccessCodeData.get(0).getEventId().getEventId(), 0);

        ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
        verify(raffleService,times(1)).save(raffleArgumentCaptor.capture());

        Raffle raffleData = raffleArgumentCaptor.getValue();
        assertEquals(raffleData.getId(), 0);
        assertFalse(raffleData.isActivated());
        assertEquals(raffleData.getEventId(), 0);

        ArgumentCaptor<RaffleTicket> raffleTicketArgumentCaptor = ArgumentCaptor.forClass(RaffleTicket.class);
        verify(raffleTicketService,times(1)).save(raffleTicketArgumentCaptor.capture());

        RaffleTicket raffleTicketData = raffleTicketArgumentCaptor.getValue();
        assertEquals(raffleTicketData.getNumOfTicket(), raffleTicket.getNumOfTicket());
        assertEquals(raffleTicketData.getPrice(), raffleTicket.getPrice());
        assertEquals(raffleTicketData.getRaffleId(), 0);

        ArgumentCaptor<RaffleCustomSms> raffleCustomSmsArgumentCaptor = ArgumentCaptor.forClass(RaffleCustomSms.class);
        verify(raffleCustomSmsService,times(1)).save(raffleCustomSmsArgumentCaptor.capture());

        RaffleCustomSms raffleCustomSmsData = raffleCustomSmsArgumentCaptor.getValue();
        assertEquals(raffleCustomSmsData.getId(), 0);
        assertEquals(raffleCustomSmsData.getEventId(), 0);

        ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
        verify(causeAuctionService,times(1)).save(causeAuctionArgumentCaptor.capture());

        CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
        assertEquals(causeAuctionData.getId(), 0);
        assertEquals(causeAuctionData.getEventId(), 0);
        assertFalse(causeAuctionData.isActivated());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService,times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
        assertEquals(donationSettingsData.getId(), 0);
        assertEquals(donationSettingsData.isTextToGiveActivated(), newWL.isDonationEnabled());
        assertEquals(causeAuctionData.getEventId(), 0);

        ArgumentCaptor<Event> eventArgumentCaptor1 = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor1.capture());

        Event eventData1 = eventArgumentCaptor1.getValue();
        assertEquals(eventData1.getCauseAuctionId(), 0);

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingService,times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettingsData = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettingsData.getEventId(), 0);
        assertEquals(waitListSettingsData.getId(), 0);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventChecklistData.getEventId(), 0);
        assertEquals(eventChecklistData.isActivatePaymentProcessing(), oldStripe.isActivated());
        assertTrue(eventChecklistData.isAuctionItemAdded());
        assertTrue(eventChecklistData.isRaffleItemAdded());
        assertTrue(eventChecklistData.isCauseAuctionItemAdded());
    }

    //@Test
    void test_duplicateEvent_success1() {

        //setup
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+oldEvent.getName();

        whiteLabel.setId(id);

        //oldEvent.setWhiteLabel(whiteLabel);

        oldAdmin.setEvent(oldEvent);
        oldAdmin.setRole(StaffRole.admin);
        oldAdmin.setUser(hostUser);
        oldAdmin.setId(id);
        oldAdmin.setStripeCustomerId(stripeCustomerId);

        oldAuction.setId(id);

        oldRaffle.setId(id);

        oldTicketing.setCollectTicketHolderAttributes(true);

        oldCauseAuction.setId(id);

        newWL.setSilentAuctionEnabled(true);
        newWL.setCauseAuctionEnabled(true);
        newWL.setRaffleEnabled(true);
        newWL.setTicketingEnabled(true);
        newWL.setAllowEditingOfDisclaimer(true);
        newWL.setCustomDisclaimer(customDisclaimer);

        eventDesignDetail.setEvent(oldEvent);

        List<TransactionFeeConditionalLogic> oldConditionalLogic = new ArrayList<>();
        oldConditionalLogic.add(transactionFeeConditionalLogic);

        oldStripe.setStaff(oldAdmin);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);
        oldItem.setItemCategory(itemCategory);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);
        ticketHolderRequiredAttributes.setId(1L);
        List<TicketHolderRequiredAttributes> ticketingAttribute = new ArrayList<>();
        ticketingAttribute.add(ticketHolderRequiredAttributes);

        ticketHolderRequiredAttributes.setId(2L);
        List<TicketHolderRequiredAttributes> ticketingAttribute2 = new ArrayList<>();
        ticketingAttribute2.add(ticketHolderRequiredAttributes);

        List<TicketingType> oldEventTickets = new ArrayList<>();
        oldEventTickets.add(ticketingType);

        List<Item> raffleItems = new ArrayList<>();
        raffleItems.add(oldItem);

        raffleTicket.setId(id);
        raffleTicket.setNumOfTicket(numOfTicket);
        raffleTicket.setPrice(price);
        raffleTicket.setRaffleId(0);

        Set<RaffleTicket> oldRaffleTicket = new HashSet<>();
        oldRaffleTicket.add(raffleTicket);

        List<Item> causeAuctionItemList = new ArrayList<>();

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(oldAdmin);
        when(auctionService.find(oldEvent.getAuctionId())).thenReturn(null);
        when(raffleService.find(oldEvent.getRaffleId())).thenReturn(null);
        when(causeAuctionService.findByEvent(oldEvent)).thenReturn(null);
        when(staffService.countNumberOfEventsByUser(hostUser)).thenReturn(countNumberOfEvents);
        when(whiteLabelRepository.findById(anyLong())).thenReturn(Optional.of(newWL));
        when(eventDesignDetailService.findByEvent(oldEvent)).thenReturn(eventDesignDetail);
        when(transactionFeeConditionalLogicService.getRecordByEvent(oldEvent)).thenReturn(oldConditionalLogic);
        when(customEmailService.getCustomEmailByEventId(oldEvent.getEventId())).thenReturn(Optional.empty());
        when(stripeService.findByEventAndDefaultAccount(oldEvent, true)).thenReturn(oldStripe);
        when(itemService.getAllItems(oldAuction.getId(), ModuleType.AUCTION)).thenReturn(auctionItemList);
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);
        Mockito.doNothing().when(itemCategoryService).savewithsequence(itemCategory);
        when(itemService.savewithsequenceWithoutCacheClear(oldItem)).thenReturn(oldItem);
        when(auctionCustomSmsService.findByEventId(oldEvent)).thenReturn(auctionCustomSms);
        when(ticketHolderRequiredAttributesService.findByEventId(oldEvent)).thenReturn(ticketingAttribute);
        when(ticketingTypeService.findAllByTicketing(0L, oldEvent)).thenReturn(oldEventTickets);
        when(itemService.getAllItems(oldRaffle.getId(), ModuleType.RAFFLE)).thenReturn(raffleItems);
        when(itemCategoryService.findByModuleIdAndType(oldModuleId, moduleType)).thenReturn(oldItemCategoryList);
        when(raffleCustomSmsService.findByEventId(oldEvent.getEventId())).thenReturn(raffleCustomSms);
        when(raffleTicketService.getAllTicketsByRaffleId(oldRaffle.getId())).thenReturn(oldRaffleTicket);
        when(itemService.getAllItems(oldCauseAuction.getId(), ModuleType.CAUSEAUCTION)).thenReturn(causeAuctionItemList);
        when(donationSettingsService.getByEventId(oldEvent.getEventId())).thenReturn(donationSettings);
        when(waitListSettingService.getWaitListSettingsByEventId(oldEvent.getEventId())).thenReturn(waitListSettings);
        Mockito.doNothing().when(whiteLabelService).sendNotificationEmailForCreateDuplicateEvent(newWL,oldEvent, newEvent, hostUser);
        when(ticketingHelperService.findTicketingByEvent(oldEvent)).thenReturn(oldTicketing);
        when(ticketHolderRequiredAttributesService.saveDefaultTicketAttributes(any())).thenReturn(ticketHolderRequiredAttributes);
        //Execution
        duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), anyBoolean());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor.capture());

        Event newEventData = eventArgumentCaptor.getValue();
        assertEquals(newEventData.getName(), nameOrUrl);
        assertEquals(newEventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(newEventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(newEventData.getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(newEventData.getEventStatus());

        ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService,times(1)).save(eventDesignDetailArgumentCaptor.capture());

        EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
        assertEquals(eventDesignDetailData.getId(), 0);
        assertEquals(eventDesignDetailData.getEvent().getName(), nameOrUrl);
        assertTrue(eventDesignDetailData.getEvent().isSilentAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isCauseAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isRaffleEnabled());
        assertTrue(eventDesignDetailData.getEvent().isTicketingEnabled());
        assertEquals(eventDesignDetailData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(eventDesignDetailData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventDesignDetailData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(eventDesignDetailData.getEvent().getEventStatus());
        assertEquals(newEventData.getCreatedFrom().longValue(), oldEvent.getEventId());

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getEvent().getName(), nameOrUrl);
        assertTrue(staffData.getEvent().isSilentAuctionEnabled());
        assertTrue(staffData.getEvent().isCauseAuctionEnabled());
        assertTrue(staffData.getEvent().isRaffleEnabled());
        assertTrue(staffData.getEvent().isTicketingEnabled());
        assertEquals(staffData.getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(staffData.getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe stripeData = stripeArgumentCaptor.getValue();
        assertEquals(stripeData.getId().longValue(), 0);
        assertTrue(stripeData.isDefaultAccount());
        assertEquals(stripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(stripeData.getEvent().getName(), nameOrUrl);
        assertTrue(stripeData.getEvent().isSilentAuctionEnabled());
        assertTrue(stripeData.getEvent().isCauseAuctionEnabled());
        assertTrue(stripeData.getEvent().isRaffleEnabled());
        assertTrue(stripeData.getEvent().isTicketingEnabled());
        assertEquals(stripeData.getStaff().getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(stripeData.getStaff().getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<AuctionCustomSms> auctionCustomSmsArgumentCaptor = ArgumentCaptor.forClass(AuctionCustomSms.class);
        verify(auctionCustomSmsService,times(1)).save(auctionCustomSmsArgumentCaptor.capture());

        AuctionCustomSms auctionCustomSmsData = auctionCustomSmsArgumentCaptor.getValue();
        assertEquals(auctionCustomSmsData.getId(), 0);
        assertEquals(auctionCustomSmsData.getEventId(), 0);

        ArgumentCaptor<RaffleCustomSms> raffleCustomSmsArgumentCaptor = ArgumentCaptor.forClass(RaffleCustomSms.class);
        verify(raffleCustomSmsService,times(1)).save(raffleCustomSmsArgumentCaptor.capture());

        RaffleCustomSms raffleCustomSmsData = raffleCustomSmsArgumentCaptor.getValue();
        assertEquals(raffleCustomSmsData.getId(), 0);
        assertEquals(raffleCustomSmsData.getEventId(), 0);

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService,times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
        assertEquals(donationSettingsData.getId(), 0);
        assertFalse(donationSettingsData.isTextToGiveActivated());

        ArgumentCaptor<Event> eventArgumentCaptor1 = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor1.capture());

        Event eventData1 = eventArgumentCaptor1.getValue();
        assertEquals(eventData1.getCauseAuctionId(), 0);

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingService,times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettingsData = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettingsData.getEventId(), 0);
        assertEquals(waitListSettingsData.getId(), 0);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventChecklistData.getEventId(), 0);
        assertEquals(eventChecklistData.isActivatePaymentProcessing(), oldStripe.isActivated());
    }
    //@Test
    void test_duplicateEvent_recurringEventTrueWithAllPastDates() {

        //setup
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+oldEvent.getName();

        whiteLabel.setId(id);

        oldEvent.setWhiteLabel(whiteLabel);

        oldAdmin.setEvent(oldEvent);
        oldAdmin.setRole(StaffRole.admin);
        oldAdmin.setUser(hostUser);
        oldAdmin.setWhiteLabel(whiteLabel);
        oldAdmin.setId(id);
        oldAdmin.setStripeCustomerId(stripeCustomerId);

        oldAuction.setId(id);

        oldRaffle.setId(id);

        oldTicketing.setCollectTicketHolderAttributes(true);
        oldTicketing.setRecurringEvent(true);

        oldCauseAuction.setId(id);

        newWL.setSilentAuctionEnabled(true);
        newWL.setCauseAuctionEnabled(true);
        newWL.setRaffleEnabled(true);
        newWL.setTicketingEnabled(true);
        newWL.setAllowEditingOfDisclaimer(true);
        newWL.setCustomDisclaimer(customDisclaimer);

        eventDesignDetail.setEvent(oldEvent);

        List<TransactionFeeConditionalLogic> oldConditionalLogic = new ArrayList<>();
        oldConditionalLogic.add(transactionFeeConditionalLogic);

        oldStripe.setStaff(oldAdmin);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);
        oldItem.setItemCategory(itemCategory);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);

        List<TicketHolderRequiredAttributes> ticketingAttribute = new ArrayList<>();
        ticketingAttribute.add(ticketHolderRequiredAttributes);

        List<TicketingType> oldEventTickets = new ArrayList<>();
        oldEventTickets.add(ticketingType);

        List<Item> raffleItems = new ArrayList<>();
        raffleItems.add(oldItem);

        raffleTicket.setId(id);
        raffleTicket.setNumOfTicket(numOfTicket);
        raffleTicket.setPrice(price);
        raffleTicket.setRaffleId(0);

        Set<RaffleTicket> oldRaffleTicket = new HashSet<>();
        oldRaffleTicket.add(raffleTicket);

        List<Item> causeAuctionItemList = new ArrayList<>();

        List<RecurringEvents> oldRecurringEventsListFromDb = new ArrayList<>();

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(ticketingCouponService.getAllByEventId(oldEvent.getEventId())).thenReturn(Collections.emptyList());
        when(ticketingAccessCodeService.findByEvent(oldEvent)).thenReturn(Collections.emptyList());
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(oldAdmin);
        when(auctionService.find(oldEvent.getAuctionId())).thenReturn(oldAuction);
        when(raffleService.find(oldEvent.getRaffleId())).thenReturn(oldRaffle);
        when(ticketingHelperService.findTicketingByEvent(oldEvent)).thenReturn(oldTicketing);
        when(causeAuctionService.findByEvent(oldEvent)).thenReturn(oldCauseAuction);
        when(staffService.countNumberOfEventsByUser(hostUser)).thenReturn(countNumberOfEvents);
        when(whiteLabelRepository.findById(oldEvent.getWhiteLabel().getId())).thenReturn(Optional.of(newWL));
        when(eventDesignDetailService.findByEvent(oldEvent)).thenReturn(eventDesignDetail);
        when(recurringEventsScheduleService.getRecurringEventsOnlyFutureDate(any())).thenReturn(oldRecurringEventsListFromDb);
        when(transactionFeeConditionalLogicService.getRecordByEvent(oldEvent)).thenReturn(oldConditionalLogic);
        when(customEmailService.getCustomEmailByEventId(oldEvent.getEventId())).thenReturn(Optional.of(customEmail));
        when(stripeService.findByEventAndDefaultAccount(oldEvent, true)).thenReturn(oldStripe);
        when(itemService.getAllItems(oldAuction.getId(), ModuleType.AUCTION)).thenReturn(auctionItemList);
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);
        Mockito.doNothing().when(itemCategoryService).savewithsequence(itemCategory);
        when(itemService.savewithsequenceWithoutCacheClear(oldItem)).thenReturn(oldItem);
        when(auctionCustomSmsService.findByEventId(oldEvent)).thenReturn(auctionCustomSms);
        when(ticketHolderRequiredAttributesService.findByEventId(oldEvent)).thenReturn(ticketingAttribute);
        when(ticketHolderRequiredAttributesService.saveDefaultTicketAttributes(any())).thenReturn(ticketHolderRequiredAttributes);
        when(ticketingTypeService.findAllByTicketing(0L, oldEvent)).thenReturn(oldEventTickets);
        when(itemService.getAllItems(oldRaffle.getId(), ModuleType.RAFFLE)).thenReturn(raffleItems);
        when(itemCategoryService.findByModuleIdAndType(oldModuleId, moduleType)).thenReturn(oldItemCategoryList);
        when(raffleCustomSmsService.findByEventId(oldEvent.getEventId())).thenReturn(raffleCustomSms);
        when(raffleTicketService.getAllTicketsByRaffleId(oldRaffle.getId())).thenReturn(oldRaffleTicket);
        when(itemService.getAllItems(oldCauseAuction.getId(), ModuleType.CAUSEAUCTION)).thenReturn(causeAuctionItemList);
        when(donationSettingsService.getByEventId(oldEvent.getEventId())).thenReturn(donationSettings);
        when(waitListSettingService.getWaitListSettingsByEventId(oldEvent.getEventId())).thenReturn(waitListSettings);
        Mockito.doNothing().when(whiteLabelService).sendNotificationEmailForCreateDuplicateEvent(newWL,oldEvent, newEvent, hostUser);

        //Execution
        duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), anyBoolean());

        ArgumentCaptor<Event> eventArgumentCaptor = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor.capture());

        Event newEventData = eventArgumentCaptor.getValue();
        assertEquals(newEventData.getName(), nameOrUrl);
        assertEquals(newEventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(newEventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(newEventData.getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(newEventData.getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(newEventData.getCreatedFrom().longValue(), oldEvent.getEventId());

        ArgumentCaptor<EventDesignDetail> eventDesignDetailArgumentCaptor = ArgumentCaptor.forClass(EventDesignDetail.class);
        verify(eventDesignDetailService,times(1)).save(eventDesignDetailArgumentCaptor.capture());

        EventDesignDetail eventDesignDetailData = eventDesignDetailArgumentCaptor.getValue();
        assertEquals(eventDesignDetailData.getId(), 0);
        assertEquals(eventDesignDetailData.getEvent().getName(), nameOrUrl);
        assertTrue(eventDesignDetailData.getEvent().isSilentAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isCauseAuctionEnabled());
        assertTrue(eventDesignDetailData.getEvent().isRaffleEnabled());
        assertTrue(eventDesignDetailData.getEvent().isTicketingEnabled());
        assertEquals(eventDesignDetailData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(eventDesignDetailData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventDesignDetailData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(eventDesignDetailData.getEvent().getEventStatus());

        ArgumentCaptor<TransactionFeeConditionalLogic> transactionFeeConditionalLogicArgumentCaptor = ArgumentCaptor.forClass(TransactionFeeConditionalLogic.class);
        verify(transactionFeeConditionalLogicService,times(1)).save(transactionFeeConditionalLogicArgumentCaptor.capture());

        TransactionFeeConditionalLogic transactionFeeConditionalLogicData = transactionFeeConditionalLogicArgumentCaptor.getValue();
        assertEquals(transactionFeeConditionalLogicData.getId(), 0);
        assertEquals(transactionFeeConditionalLogicData.getEvent().getName(), nameOrUrl);
        assertTrue(transactionFeeConditionalLogicData.getEvent().isSilentAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isCauseAuctionEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isRaffleEnabled());
        assertTrue(transactionFeeConditionalLogicData.getEvent().isTicketingEnabled());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getCurrency(), oldEvent.getCurrency());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(transactionFeeConditionalLogicData.getEvent().getEquivalentTimeZone(), oldEvent.getEquivalentTimeZone());
        assertNull(transactionFeeConditionalLogicData.getEvent().getEventStatus());
        assertEquals(newEventData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(newEventData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(newEventData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getEvent().getName(), nameOrUrl);
        assertTrue(staffData.getEvent().isSilentAuctionEnabled());
        assertTrue(staffData.getEvent().isCauseAuctionEnabled());
        assertTrue(staffData.getEvent().isRaffleEnabled());
        assertTrue(staffData.getEvent().isTicketingEnabled());
        assertEquals(staffData.getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(staffData.getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(staffData.getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(staffData.getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(staffData.getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<CustomEmail> customEmailArgumentCaptor = ArgumentCaptor.forClass(CustomEmail.class);
        verify(customEmailService,times(1)).save(customEmailArgumentCaptor.capture());

        CustomEmail customEmailData = customEmailArgumentCaptor.getValue();
        assertEquals(customEmailData.getId(), 0);
        assertEquals(customEmailData.getEventId(), 0);

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe stripeData = stripeArgumentCaptor.getValue();
        assertEquals(stripeData.getId().longValue(), 0);
        assertTrue(stripeData.isDefaultAccount());
        assertEquals(stripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(stripeData.getEvent().getName(), nameOrUrl);
        assertTrue(stripeData.getEvent().isSilentAuctionEnabled());
        assertTrue(stripeData.getEvent().isCauseAuctionEnabled());
        assertTrue(stripeData.getEvent().isRaffleEnabled());
        assertTrue(stripeData.getEvent().isTicketingEnabled());
        assertEquals(stripeData.getStaff().getUser().getFirstName(), oldAdmin.getUser().getFirstName());
        assertEquals(stripeData.getStaff().getWhiteLabel().isSilentAuctionEnabled(), newWL.isSilentAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isCauseAuctionEnabled(), newWL.isCauseAuctionEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isRaffleEnabled(), newWL.isRaffleEnabled());
        assertEquals(stripeData.getStaff().getWhiteLabel().isTicketingEnabled(), newWL.isTicketingEnabled());
        assertEquals(stripeData.getStaff().getStripeCustomerId(), oldAdmin.getStripeCustomerId());

        ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
        verify(auctionService,times(1)).save(auctionArgumentCaptor.capture());

        Auction auctionData = auctionArgumentCaptor.getValue();
        assertEquals(auctionData.getId(), 0);
        assertEquals(auctionData.getEventId().longValue(), 0);
        assertFalse(auctionData.isActivated());
        assertNull(auctionData.getAuctionStatus());

        ArgumentCaptor<AuctionCustomSms> auctionCustomSmsArgumentCaptor = ArgumentCaptor.forClass(AuctionCustomSms.class);
        verify(auctionCustomSmsService,times(1)).save(auctionCustomSmsArgumentCaptor.capture());

        AuctionCustomSms auctionCustomSmsData = auctionCustomSmsArgumentCaptor.getValue();
        assertEquals(auctionCustomSmsData.getId(), 0);
        assertEquals(auctionCustomSmsData.getEventId(), 0);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(2)).save(ticketingArgumentCaptor.capture());

        Ticketing ticketingData = ticketingArgumentCaptor.getValue();
        assertEquals(ticketingData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(ticketingData.getEventid().getEventId(), 0);
//        assertTrue(ticketingData.getActivated());
        assertEquals(ticketingData.isAllowEditingOfDisclaimer(), newWL.isAllowEditingOfDisclaimer());
        assertEquals(ticketingData.getCustomDisclaimer(), newWL.getCustomDisclaimer());
        assertEquals(ticketingData.getId(), 0);

        ArgumentCaptor<TicketHolderRequiredAttributes> ticketHolderRequiredAttributesArgumentCaptor = ArgumentCaptor.forClass(TicketHolderRequiredAttributes.class);
        verify(ticketHolderRequiredAttributesService,times(1)).saveDefaultTicketAttributes(ticketHolderRequiredAttributesArgumentCaptor.capture());

        TicketHolderRequiredAttributes ticketHolderRequiredAttributesData = ticketHolderRequiredAttributesArgumentCaptor.getValue();
        assertEquals(ticketHolderRequiredAttributesData.getEventid().getEventId(), 0);

        ArgumentCaptor<TicketingType> ticketingTypeArgumentCaptor = ArgumentCaptor.forClass(TicketingType.class);
        verify(ticketingTypeService,times(1)).setPositionForTicketingTypeAndsaveTicketingType(ticketingTypeArgumentCaptor.capture());

        TicketingType ticketingTypeData = ticketingTypeArgumentCaptor.getValue();
        assertEquals(ticketingTypeData.getTicketing().getEventid().getName(), nameOrUrl);
        assertTrue(ticketingTypeData.getTicketing().getEventid().isSilentAuctionEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isCauseAuctionEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isRaffleEnabled());
        assertTrue(ticketingTypeData.getTicketing().getEventid().isTicketingEnabled());
        assertEquals(ticketingTypeData.getId(), 0);

        ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
        verify(raffleService,times(1)).save(raffleArgumentCaptor.capture());

        Raffle raffleData = raffleArgumentCaptor.getValue();
        assertEquals(raffleData.getId(), 0);
        assertFalse(raffleData.isActivated());
        assertEquals(raffleData.getEventId(), 0);

        ArgumentCaptor<RaffleTicket> raffleTicketArgumentCaptor = ArgumentCaptor.forClass(RaffleTicket.class);
        verify(raffleTicketService,times(1)).save(raffleTicketArgumentCaptor.capture());

        RaffleTicket raffleTicketData = raffleTicketArgumentCaptor.getValue();
        assertEquals(raffleTicketData.getNumOfTicket(), raffleTicket.getNumOfTicket());
        assertEquals(raffleTicketData.getPrice(), raffleTicket.getPrice());
        assertEquals(raffleTicketData.getRaffleId(), 0);

        ArgumentCaptor<RaffleCustomSms> raffleCustomSmsArgumentCaptor = ArgumentCaptor.forClass(RaffleCustomSms.class);
        verify(raffleCustomSmsService,times(1)).save(raffleCustomSmsArgumentCaptor.capture());

        RaffleCustomSms raffleCustomSmsData = raffleCustomSmsArgumentCaptor.getValue();
        assertEquals(raffleCustomSmsData.getId(), 0);
        assertEquals(raffleCustomSmsData.getEventId(), 0);

        ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
        verify(causeAuctionService,times(1)).save(causeAuctionArgumentCaptor.capture());

        CauseAuction causeAuctionData = causeAuctionArgumentCaptor.getValue();
        assertEquals(causeAuctionData.getId(), 0);
        assertEquals(causeAuctionData.getEventId(), 0);
        assertFalse(causeAuctionData.isActivated());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsService,times(1)).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingsData = donationSettingsArgumentCaptor.getValue();
        assertEquals(donationSettingsData.getId(), 0);
        assertEquals(donationSettingsData.isTextToGiveActivated(), newWL.isDonationEnabled());
        assertEquals(causeAuctionData.getEventId(), 0);

        ArgumentCaptor<Event> eventArgumentCaptor1 = ArgumentCaptor.forClass(Event.class);
        verify(eventRepository,times(2)).save(eventArgumentCaptor1.capture());

        Event eventData1 = eventArgumentCaptor1.getValue();
        assertEquals(eventData1.getCauseAuctionId(), 0);

        ArgumentCaptor<WaitListSettings> waitListSettingsArgumentCaptor = ArgumentCaptor.forClass(WaitListSettings.class);
        verify(waitListSettingService,times(1)).save(waitListSettingsArgumentCaptor.capture());

        WaitListSettings waitListSettingsData = waitListSettingsArgumentCaptor.getValue();
        assertEquals(waitListSettingsData.getEventId(), 0);
        assertEquals(waitListSettingsData.getId(), 0);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();
        assertEquals(eventChecklistData.getEventId(), 0);
        assertEquals(eventChecklistData.isActivatePaymentProcessing(), oldStripe.isActivated());
        assertTrue(eventChecklistData.isAuctionItemAdded());
        assertTrue(eventChecklistData.isRaffleItemAdded());
        assertTrue(eventChecklistData.isCauseAuctionItemAdded());
    }

    @Test
    void test_duplicateEvent_throwException_NOT_AUTHORIZED_TO_DUPLICATE_EVENT() {

        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);

        //mock
        when(staffService.getByUserAndRoleAndEvent(hostUser, StaffRole.admin, oldEvent)).thenReturn(null);

        //Execution
        Exception exception = assertThrows(ForbiddenException.class,
                () -> duplicateEventServiceImpl.duplicateEvent(oldEvent, hostUser, duplicateEventsTemplatesConfigDto, new MockHttpServletRequest(), false));

        assertEquals(ForbiddenException.UserForbiddenExceptionMsg.NOT_AUTHORIZED_TO_DUPLICATE_EVENT.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_createRaffle_success_with_newwl_null() {

        //setup
        long newEventId = 0L;
        newEvent.setEventId(newEventId);

        //Execution
        Raffle raffleData = duplicateEventServiceImpl.createRaffle(oldRaffle, newEvent, null, oldTicketing, newTicketing);

        assertEquals(raffleData.getId(), 0);
        assertFalse(raffleData.isActivated());
        assertEquals(raffleData.getEventId(), newEventId);

        ArgumentCaptor<Raffle> raffleArgumentCaptor = ArgumentCaptor.forClass(Raffle.class);
        verify(raffleService,times(1)).save(raffleArgumentCaptor.capture());

        Raffle actualData = raffleArgumentCaptor.getValue();
        assertEquals(actualData.getId(), 0);
        assertFalse(actualData.isActivated());
        assertEquals(actualData.getEventId(), newEventId);
    }


    @Test
    void test_getNewStripeAndCreateNewAdminWhoConnectedStripe_success() {

        //setup
        newAdmin.setUser(hostUser);
        newAdmin.setWhiteLabel(newWL);
        newAdmin.setStripeCustomerId(stripeCustomerId);

        User user = EventDataUtil.getUser();
        user.setUserId(12L);
        Staff admin = new Staff();
        admin.setUser(user);
        admin.setWhiteLabel(newWL);
        admin.setStripeCustomerId(stripeCustomerId);
        oldStripe.setStaff(admin);

        //mock


        //Execution
        Stripe stripeData = duplicateEventServiceImpl.getNewStripeAndCreateNewAdminWhoConnectedStripe(oldAdmin, newEvent, newWL, newAdmin, oldStripe);

        assertEquals(stripeData.getId().longValue(), 0);

        ArgumentCaptor<Staff> staffArgumentCaptor = ArgumentCaptor.forClass(Staff.class);
        verify(staffService,times(1)).save(staffArgumentCaptor.capture());

        Staff staffData = staffArgumentCaptor.getValue();
        assertEquals(staffData.getRole(), StaffRole.admin);
        assertEquals(staffData.getStripeCustomerId(), stripeCustomerId);

        ArgumentCaptor<Stripe> stripeArgumentCaptor = ArgumentCaptor.forClass(Stripe.class);
        verify(stripeService,times(1)).save(stripeArgumentCaptor.capture());

        Stripe newStripeData = stripeArgumentCaptor.getValue();
        assertEquals(newStripeData.getStaff().getRole(), StaffRole.admin);
        assertEquals(newStripeData.getStaff().getStripeCustomerId(), stripeCustomerId);
        assertEquals(stripeData.getId().longValue(), 0);
        assertEquals(stripeData.getEvent().getEventId(), 0);
    }

    @Test
    void test_getNewStripeAndCreateNewAdminWhoConnectedStripe_success_with_oldStripe_null() {

        //setup
        newAdmin.setUser(hostUser);
        newAdmin.setWhiteLabel(newWL);
        newAdmin.setStripeCustomerId(stripeCustomerId);

        oldStripe.setStaff(newAdmin);

        //mock


        //Execution
        Stripe stripeData = duplicateEventServiceImpl.getNewStripeAndCreateNewAdminWhoConnectedStripe(oldAdmin, newEvent, newWL, newAdmin, null);

        assertNull(stripeData);
    }

    @Test
    void test_createCustomEmail_success_with_oldCustomEmail_null() {

        //Execution
        duplicateEventServiceImpl.createCustomEmail(null, newEvent);
    }

    @Test
    void test_createNewWaiListSettings_success_with_oldWaitListSettings_null() {

        //Execution
        WaitListSettings newWaiListSettingsData = duplicateEventServiceImpl.createNewWaiListSettings(newEvent, null);

        assertNull(newWaiListSettingsData);
    }

    @Test
    void test_createAuctionCustomSms_success_with_auctionCustomSms_null() {

        //Execution
        duplicateEventServiceImpl.createAuctionCustomSms(null, oldEvent.getEventId());
    }

    @Test
    void test_createCauseAuction_success_with_newWl_null() {

        //Execution
        CauseAuction causeAuctionData = duplicateEventServiceImpl.createCauseAuction(oldCauseAuction, newEvent, null, oldTicketing, newTicketing);

        assertEquals(causeAuctionData.getId(), 0);
        assertEquals(causeAuctionData.getEventId(), 0);
        assertFalse(causeAuctionData.isActivated());

        ArgumentCaptor<CauseAuction> causeAuctionArgumentCaptor = ArgumentCaptor.forClass(CauseAuction.class);
        verify(causeAuctionService,times(1)).save(causeAuctionArgumentCaptor.capture());

        CauseAuction actualData = causeAuctionArgumentCaptor.getValue();
        assertEquals(actualData.getId(), 0);
        assertEquals(actualData.getEventId(), 0);
        assertFalse(actualData.isActivated());
    }

    @Test
    void test_createAuction_success_with_newWl_null() {

        //setup
        long newEventId = 0L;
        newEvent.setEventId(newEventId);

        //Execution
        Auction AuctionData = duplicateEventServiceImpl.createAuction(oldAuction, newEventId, null);

        assertEquals(AuctionData.getId(), 0);
        assertFalse(AuctionData.isActivated());
        assertEquals(AuctionData.getEventId().longValue(), newEventId);
        assertNull(AuctionData.getAuctionStatus());

        ArgumentCaptor<Auction> auctionArgumentCaptor = ArgumentCaptor.forClass(Auction.class);
        verify(auctionService,times(1)).save(auctionArgumentCaptor.capture());

        Auction actualData = auctionArgumentCaptor.getValue();
        assertEquals(actualData.getId(), 0);
        assertFalse(actualData.isActivated());
        assertEquals(actualData.getEventId().longValue(), newEventId);
        assertNull(actualData.getAuctionStatus());
    }

    @Test
    void test_createEventChecklist_success_with_newWl_null() {

        //setup
        long newEventId = 0L;
        newEvent.setEventId(newEventId);

        //Execution
        duplicateEventServiceImpl.createEventChecklist(newEvent, null, newAuction, newCauseAuction, oldRaffle);

        ArgumentCaptor<EventChecklist> eventChecklistArgumentCaptor = ArgumentCaptor.forClass(EventChecklist.class);
        verify(eventChecklistService,times(1)).save(eventChecklistArgumentCaptor.capture());

        EventChecklist eventChecklistData = eventChecklistArgumentCaptor.getValue();

        assertEquals(eventChecklistData.getEventId(), 0);
        assertTrue(eventChecklistData.isAuctionItemAdded());
        assertTrue(eventChecklistData.isRaffleItemAdded());
        assertTrue(eventChecklistData.isCauseAuctionItemAdded());
    }

    @Test
    void test_createRaffleCustomSms_success_with_raffleCustomSms_null() {

        //Execution
        duplicateEventServiceImpl.createRaffleCustomSms(null, oldEvent.getEventId());
    }

    @Test
    void test_getCopyEventNameOrUrl_success() {

        //setup
        String EventName = "TestEventForJunitTestCaseWithEventLengthGreaterThanFiftyCharacter";
        String nameOrUrl = "COPY"+(countNumberOfEvents+1)+EventName;
        if(nameOrUrl.length() >= 50){
            nameOrUrl = nameOrUrl.substring(0,50);
        }

        //Execution
        String eventNameOrUrl = duplicateEventServiceImpl.getCopyEventNameOrUrl(countNumberOfEvents, EventName, false);

        assertEquals(eventNameOrUrl, nameOrUrl);
    }

    @Test
    void test_createNewTicketing_success_with_oldStripe_null() {

        //setup
        oldTicketing.setCollectTicketHolderAttributes(true);
        oldTicketing.setEventStartDate(new Date());
        oldTicketing.setEventEndDate(new Date());
        oldEvent.setTimezoneId("United States (New York) Time");
        String registrationConfigDto = "{\"attendeesIncluded\":-1,\"paidRegistrationFeePercent\":7,\"additionalAttendeesCharge\":3,\"registrationOrderForm\":\"Custom\",\n" +
                " \"orderConfirmationForm\":\"Custom\",\"maxNoOfEvents\":-1,\"maxEventDays\":-1,\"interestTagAvailable\":-1}";
        EventPlanConfig eventPlanConfig = planConfig;
        PlanConfig planConfig = new PlanConfig();
        planConfig.setPlanName("TestPlan");

        eventPlanConfig.setPlanConfig(planConfig);
        eventPlanConfig.setRegistrationConfigJson(registrationConfigDto);

        //Execution
        DuplicateEventsTemplatesConfigDto duplicateEventsTemplatesConfigDto = new DuplicateEventsTemplatesConfigDto();
        duplicateEventsTemplatesConfigDto.setCopyTicketsAndAddons(true);
        Ticketing ticketingData = duplicateEventServiceImpl.createNewTicketing(oldTicketing, newEvent, eventPlanConfig, oldEvent, duplicateEventsTemplatesConfigDto);

        assertEquals(ticketingData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(ticketingData.getEventid().getEventId(), 0);
        assertFalse(ticketingData.getActivated());
        assertEquals(ticketingData.getId(), 0);

        ArgumentCaptor<Ticketing> ticketingArgumentCaptor = ArgumentCaptor.forClass(Ticketing.class);
        verify(ticketingService,times(1)).save(ticketingArgumentCaptor.capture());

        Ticketing actualData = ticketingArgumentCaptor.getValue();
        assertEquals(actualData.getCollectTicketHolderAttributes(), oldTicketing.getCollectTicketHolderAttributes());
        assertEquals(actualData.getEventid().getEventId(), 0);
        assertFalse(actualData.getActivated());
        assertEquals(actualData.getId(), 0);
    }

    @Test
    void test_createEvent_success() {

        //setup
        String eventUrl = oldEvent.getEventURL() +"duplicate";

        //Execution
        Event eventData = duplicateEventServiceImpl.createEvent(oldEvent);

        assertEquals(eventData.getName(), oldEvent.getName());
        assertEquals(eventData.isCauseAuctionEnabled(), oldEvent.isCauseAuctionEnabled());
        assertEquals(eventData.isSilentAuctionEnabled(), oldEvent.isSilentAuctionEnabled());
        assertEquals(eventData.isRaffleEnabled(), oldEvent.isRaffleEnabled());
        assertEquals(eventData.getCurrency(), oldEvent.getCurrency());
        assertEquals(eventData.getTimezoneId(), oldEvent.getTimezoneId());
        assertEquals(eventData.getEquivalentTimeZone(),oldEvent.getEquivalentTimeZone());
        assertEquals(eventData.isCreditCardEnabled(), oldEvent.isCreditCardEnabled());
        assertEquals(eventData.getGoalStartingAmount(), oldEvent.getGoalStartingAmount());
        assertEquals(eventData.getEventURL(), eventUrl);
    }

    @Test
    void test_createNewItemImgLocations_success() {

        //setup
        oldItem.setId(id);

        newItem.setId(id);
        newItem.setActive(true);
        newItem.setModuleType(ModuleType.AUCTION);

        List<ItemImgLocations>  oldImgList = new ArrayList<>();
        oldImgList.add(itemImgLocations);

        //mock
        when(itemImageLocationService.findByItem(oldItem)).thenReturn(oldImgList);

        //Execution
        List<ItemImgLocations> itemImgLocationActualData = duplicateEventServiceImpl.createNewItemImgLocations(oldItem, newItem);

        assertEquals(itemImgLocationActualData.listIterator().next().getId(), 0);

        ArgumentCaptor<ItemImgLocations> itemImgLocationsArgumentCaptor = ArgumentCaptor.forClass(ItemImgLocations.class);
        verify(itemImageLocationService,times(1)).save(itemImgLocationsArgumentCaptor.capture());

        ItemImgLocations itemImgLocationsData = itemImgLocationsArgumentCaptor.getValue();
        assertEquals(itemImgLocationsData.getId(), 0);
        assertEquals(itemImgLocationsData.getItem().getId(), newItem.getId());
        assertEquals(itemImgLocationsData.getItem().isActive(), newItem.isActive());
        assertEquals(itemImgLocationsData.getItem().getModuleType(), newItem.getModuleType());
    }

    @Test
    void test_createNewItems_success() {

        //setup
        long newModuleId = 1L;
        long oldModuleId = 1L;
        oldAuction.setId(id);

        itemCategory.setId(id);
        itemCategory.setModuleType(ModuleType.AUCTION);
        itemCategory.setName("Item Name");

        List<ItemCategory> oldItemCategoryList = new ArrayList<>();
        oldItemCategoryList.add(itemCategory);

        oldItem.setActive(true);
        oldItem.setId(id);

        List<Item> auctionItemList = new ArrayList<>();
        auctionItemList.add(oldItem);

        //mock
        when(itemCategoryService.findByModuleIdAndType(anyLong(), any())).thenReturn(oldItemCategoryList);


        //Execution
        duplicateEventServiceImpl.createNewItems(auctionItemList, newModuleId , oldModuleId,  ModuleType.AUCTION);
    }
}