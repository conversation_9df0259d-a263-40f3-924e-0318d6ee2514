package com.accelevents.services.impl;

import com.accelevents.apigateway.ClearAPIGatewayCache;
import com.accelevents.common.dto.DonationDisplaySetting;
import com.accelevents.common.dto.HostTextToGiveSettings;
import com.accelevents.domain.DonationSettings;
import com.accelevents.domain.Event;
import com.accelevents.domain.Stripe;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.repositories.DonationSettingsRepository;
import com.accelevents.ro.payment.ROStripeService;
import com.accelevents.services.DonationService;
import com.accelevents.services.StripeService;
import com.accelevents.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DonationSettingsServiceImplTest {

    @Spy
    @InjectMocks
    private DonationSettingsServiceImpl donationSettingsServiceImpl = new DonationSettingsServiceImpl();

    @Mock
    private DonationService donationService;

    @Mock
    private StripeService stripeService;
    @Mock
    private ROStripeService roStripeService;

    @Mock
    private DonationSettingsRepository donationSettingsRepository;
    @Mock
    private ClearAPIGatewayCache clearAPIGatewayCache;

    private HostTextToGiveSettings hostTextToGiveSettings;
    private DonationSettings donationSettings;
	private Event event;
    private DonationDisplaySetting donationDisplaySetting;

    private String croppedThermometerImgUrl = "dc5d039b-d1b6-4d13-948d-c3ed5924e821";
    private String croppedDonationViewImgUrl = "cd6585ea-eec1-4b9b-9a98-bc55897de732";

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        event = EventDataUtil.getEvent();
        donationSettings = new DonationSettings();
        donationSettings.setHideDonationGoal(true);
        donationSettings.setCustomThermometerImage("dc5d039b-d1b6-4d13-948d-c3ed5924e821_original.jpg");
        donationSettings.setDonationViewBackgroundImage("cd6585ea-eec1-4b9b-9a98-bc55897de732_original.jpg");
        donationSettings.setSampleTextToGiveDate(DateUtils.getCurrentDate());
        donationSettings.setTextToGiveActivated(true);
        donationSettings.setAbsorbFee(true);
        donationSettings.setDonationAmountA(50);
        donationSettings.setDonationAmountB(100);
        donationSettings.setDonationAmountC(250);
        donationSettings.setDonationAmountD(500);
        donationSettings.setEventId(event.getEventId());
        donationSettings.setFee(1);
        donationSettings.setDonationGoalAmount(8000);
        donationSettings.setEnableScrollOnGoalPage(true);
        donationSettings.setEnableRecurringDonations(false);
        donationSettings.setShowDonorName(false);
        donationSettings.setId(1L);
        donationSettings.setTHANK_YOU_SMS("Thank you for your donation.");
        donationSettings.setDonationPageDescription("<p>Every dollar donated goes to installing aic conditioners in hot cars for dogs!&nbsp;&nbsp;</p>");
        donationDisplaySetting = new DonationDisplaySetting();
		Stripe stripe = new Stripe();
        hostTextToGiveSettings = new HostTextToGiveSettings();
        hostTextToGiveSettings.setDonationGoalAmount(10000d);
        hostTextToGiveSettings.setAbsorbAEFees(true);
        hostTextToGiveSettings.setDonationAmountA(50);
        hostTextToGiveSettings.setDonationAmountB(100);
        hostTextToGiveSettings.setDonationAmountC(250);
        hostTextToGiveSettings.setDonationAmountD(500);
        hostTextToGiveSettings.setDonationPageDescription("<p>Every dollar donated goes to installing aic conditioners in hot cars for dogs!&nbsp;&nbsp;</p>");
        hostTextToGiveSettings.setEnableScrollOnGoalPage(true);
        hostTextToGiveSettings.setDonationViewBackgroundImage("cd6585ea-eec1-4b9b-9a98-bc55897de732_original.jpg");
        hostTextToGiveSettings.setCustomThermometerImage("dc5d039b-d1b6-4d13-948d-c3ed5924e821_original.jpg");
        hostTextToGiveSettings.setHideDonationGoal(true);
        hostTextToGiveSettings.setEnableRecurringDonations(true);
    }

    @Test
    void test_save() {

        //Execution
        donationSettingsServiceImpl.save(donationSettings);

        //Assertion
        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsRepository).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingData = donationSettingsArgumentCaptor.getValue();

        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.isTextToGiveActivated(), donationSettings.isTextToGiveActivated());
        assertEquals(donationSettingData.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
        assertEquals(donationSettingData.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(donationSettingData.isEnableScrollOnGoalPage(), donationSettings.isEnableScrollOnGoalPage());
        assertEquals(donationSettingData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.getFee(), donationSettings.getFee());
        assertEquals(donationSettingData.getSampleTextToGiveDate(), donationSettings.getSampleTextToGiveDate());
        assertEquals(donationSettingData.getDonationAmountA(), donationSettings.getDonationAmountA());
        assertEquals(donationSettingData.getDonationAmountB(), donationSettings.getDonationAmountB());
        assertEquals(donationSettingData.getDonationAmountC(), donationSettings.getDonationAmountC());
        assertEquals(donationSettingData.getDonationAmountD(), donationSettings.getDonationAmountD());
        assertEquals(donationSettingData.getDonationGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(donationSettingData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(donationSettingData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(donationSettingData.getTHANK_YOU_SMS(), donationSettings.getTHANK_YOU_SMS());
    }

    @Test
    void test_getByEventId() {

        //mock
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        DonationSettings donationSettingData = donationSettingsServiceImpl.getByEventId(event.getEventId());

        //Assertion
        verify(donationSettingsRepository).getByEventId(anyLong());

        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.isTextToGiveActivated(), donationSettings.isTextToGiveActivated());
        assertEquals(donationSettingData.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
        assertEquals(donationSettingData.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(donationSettingData.isEnableScrollOnGoalPage(), donationSettings.isEnableScrollOnGoalPage());
        assertEquals(donationSettingData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.getFee(), donationSettings.getFee());
        assertEquals(donationSettingData.getSampleTextToGiveDate(), donationSettings.getSampleTextToGiveDate());
        assertEquals(donationSettingData.getDonationAmountA(), donationSettings.getDonationAmountA());
        assertEquals(donationSettingData.getDonationAmountB(), donationSettings.getDonationAmountB());
        assertEquals(donationSettingData.getDonationAmountC(), donationSettings.getDonationAmountC());
        assertEquals(donationSettingData.getDonationAmountD(), donationSettings.getDonationAmountD());
        assertEquals(donationSettingData.getDonationGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(donationSettingData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(donationSettingData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(donationSettingData.getTHANK_YOU_SMS(), donationSettings.getTHANK_YOU_SMS());
    }

    @Test
    void test_delete() {

        //Execution
        donationSettingsServiceImpl.delete(donationSettings);

        //Assertion
        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsRepository).delete(donationSettingsArgumentCaptor.capture());
    }

    static Object[] getStripe(){
        Stripe stripe = new Stripe();
        stripe.setProcessingFeesToPurchaser(false);
        Stripe stripe1 = new Stripe();
        stripe1.setProcessingFeesToPurchaser(true);
        return new Object[]{
                new Object[]{true, stripe},
                new Object[]{false, null},
                new Object[]{false, stripe1}
        };
    }
    @ParameterizedTest
    @MethodSource("getStripe")
    void test_getHostTextToGiveSettings_withStripeAndProcessingFeesToPurchaserTrue(boolean isAbsorbAEFees, Stripe stripe) {

        //mock
        when(stripeService.getByEventId(anyLong())).thenReturn(stripe);
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        HostTextToGiveSettings hostTextToGiveSettings = donationSettingsServiceImpl.getHostTextToGiveSettings(event);

        //Assertion
        verify(stripeService).getByEventId(anyLong());
        verify(donationSettingsRepository).getByEventId(anyLong());

        assertEquals(hostTextToGiveSettings.isAbsorbAEFees(), isAbsorbAEFees);
        assertEquals(hostTextToGiveSettings.getDonationAmountA(), donationSettings.getDonationAmountA());
        assertEquals(hostTextToGiveSettings.getDonationAmountB(), donationSettings.getDonationAmountB());
        assertEquals(hostTextToGiveSettings.getDonationAmountC(), donationSettings.getDonationAmountC());
        assertEquals(hostTextToGiveSettings.getDonationAmountD(), donationSettings.getDonationAmountD());
        assertEquals(hostTextToGiveSettings.getDonationGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(hostTextToGiveSettings.getDonationPageDescription(), donationSettings.getDonationPageDescription());
        assertEquals(hostTextToGiveSettings.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
        assertEquals(hostTextToGiveSettings.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(hostTextToGiveSettings.isEnableScrollOnGoalPage(), donationSettings.isEnableScrollOnGoalPage());
        assertEquals(hostTextToGiveSettings.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(hostTextToGiveSettings.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(hostTextToGiveSettings.isHideDonationGoal(), donationSettings.isHideDonationGoal());
    }

    @Test
    void test_setHostTextToGiveSettings_throwException() {

        //setup
        hostTextToGiveSettings.setDonationGoalAmount(10000d);
        donationDisplaySetting.setTotalFundRaised(20000);
        donationSettings.setDonationGoalAmount(15000);

        //mock
        when(donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(any())).thenReturn(donationDisplaySetting);
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        Exception exception = assertThrows(NotAcceptableException.class,
                () -> donationSettingsServiceImpl.setHostTextToGiveSettings(event, hostTextToGiveSettings));

        assertEquals(NotAcceptableException.DonationExceptionMsg.GOAL_AMOUNT_LESS_THAN_FUND_RAISED.getDeveloperMessage(), exception.getMessage());
    }

    public static Object[] getStripeData(){
        Stripe stripe = new Stripe();
        stripe.setPaymentGateway(EnumPaymentGateway.STRIPE.toString());
        Stripe stripe1 = new Stripe();
        stripe1.setPaymentGateway(EnumPaymentGateway.SQUARE.toString());
        return new Object[]{
                new Object[]{stripe, true},
                new Object[]{stripe1, false},
                new Object[]{null, false}
        };
    }

    @ParameterizedTest
    @MethodSource("getStripeData")
    void test_setHostTextToGiveSettings_withDonation(Stripe stripe, boolean EnableRecurringDonations) {

        //setup
        donationDisplaySetting.setTotalFundRaised(10000);

        //mock
        when(donationService.getNumberOfDonorsWithSumOfTotalPaidDonation(any())).thenReturn(donationDisplaySetting);
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);
        when(roStripeService.findByEvent(any())).thenReturn(stripe);

        //Execution
        donationSettingsServiceImpl.setHostTextToGiveSettings(event, hostTextToGiveSettings);

        //Assertion
        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsRepository).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingData = donationSettingsArgumentCaptor.getValue();

        assertEquals(donationSettingData.isAbsorbFee(), hostTextToGiveSettings.isAbsorbAEFees());
        assertEquals(donationSettingData.getDonationAmountA(), hostTextToGiveSettings.getDonationAmountA());
        assertEquals(donationSettingData.getDonationAmountB(), hostTextToGiveSettings.getDonationAmountB());
        assertEquals(donationSettingData.getDonationAmountC(), hostTextToGiveSettings.getDonationAmountC());
        assertEquals(donationSettingData.getDonationAmountD(), hostTextToGiveSettings.getDonationAmountD());
        assertEquals(donationSettingData.getDonationGoalAmount(), hostTextToGiveSettings.getDonationGoalAmount(), 0.0);
        assertEquals(donationSettingData.getDonationPageDescription(), hostTextToGiveSettings.getDonationPageDescription());
        assertEquals(donationSettingData.isEnableScrollOnGoalPage(), hostTextToGiveSettings.isEnableScrollOnGoalPage());
        assertEquals(donationSettingData.getCustomThermometerImage(), hostTextToGiveSettings.getCustomThermometerImage());
        assertEquals(donationSettingData.getDonationViewBackgroundImage(), hostTextToGiveSettings.getDonationViewBackgroundImage());
        assertEquals(donationSettingData.isHideDonationGoal(), hostTextToGiveSettings.isHideDonationGoal());
        assertEquals(donationSettingData.isEnableRecurringDonations(), EnableRecurringDonations);
        assertEquals(donationSettingData.isShowDonorName(), hostTextToGiveSettings.isShowDonorName());
    }

    @Test
    void test_updateCustomeThermometerImage_withDonationSettings() {

        //mock
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        donationSettingsServiceImpl.updateCustomeThermometerImage(event, croppedThermometerImgUrl);

        //Assertion
        verify(donationSettingsRepository).getByEventId(anyLong());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsRepository).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingData = donationSettingsArgumentCaptor.getValue();

        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.isTextToGiveActivated(), donationSettings.isTextToGiveActivated());
        assertEquals(donationSettingData.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
        assertEquals(donationSettingData.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(donationSettingData.isEnableScrollOnGoalPage(), donationSettings.isEnableScrollOnGoalPage());
        assertEquals(donationSettingData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.getFee(), donationSettings.getFee());
        assertEquals(donationSettingData.getSampleTextToGiveDate(), donationSettings.getSampleTextToGiveDate());
        assertEquals(donationSettingData.getDonationAmountA(), donationSettings.getDonationAmountA());
        assertEquals(donationSettingData.getDonationAmountB(), donationSettings.getDonationAmountB());
        assertEquals(donationSettingData.getDonationAmountC(), donationSettings.getDonationAmountC());
        assertEquals(donationSettingData.getDonationAmountD(), donationSettings.getDonationAmountD());
        assertEquals(donationSettingData.getDonationGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(donationSettingData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(donationSettingData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(donationSettingData.getTHANK_YOU_SMS(), donationSettings.getTHANK_YOU_SMS());
    }

    @Test
    void test_updateCustomeThermometerImage_withDonationSettingsNull() {

        //mock
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(null);

        //Execution
        donationSettingsServiceImpl.updateCustomeThermometerImage(event, croppedThermometerImgUrl);

        //Assertion
        verify(donationSettingsRepository).getByEventId(anyLong());
    }

    @Test
    void test_updateDonationViewBackgroundImage_withDonationSettingsNull() {

        //mock
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(null);

        //Execution
        donationSettingsServiceImpl.updateDonationViewBackgroundImage(event, croppedDonationViewImgUrl);

        //Assertion
        verify(donationSettingsRepository).getByEventId(anyLong());
    }

    @Test
    void test_updateDonationViewBackgroundImage_withDonationSettings() {

        //mock
        when(donationSettingsRepository.getByEventId(anyLong())).thenReturn(donationSettings);

        //Execution
        donationSettingsServiceImpl.updateDonationViewBackgroundImage(event, croppedDonationViewImgUrl);

        //Assertion
        verify(donationSettingsRepository).getByEventId(anyLong());

        ArgumentCaptor<DonationSettings> donationSettingsArgumentCaptor = ArgumentCaptor.forClass(DonationSettings.class);
        verify(donationSettingsRepository).save(donationSettingsArgumentCaptor.capture());

        DonationSettings donationSettingData = donationSettingsArgumentCaptor.getValue();

        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.isTextToGiveActivated(), donationSettings.isTextToGiveActivated());
        assertEquals(donationSettingData.isEnableRecurringDonations(), donationSettings.isEnableRecurringDonations());
        assertEquals(donationSettingData.isShowDonorName(), donationSettings.isShowDonorName());
        assertEquals(donationSettingData.isEnableScrollOnGoalPage(), donationSettings.isEnableScrollOnGoalPage());
        assertEquals(donationSettingData.isHideDonationGoal(), donationSettings.isHideDonationGoal());
        assertEquals(donationSettingData.isAbsorbFee(), donationSettings.isAbsorbFee());
        assertEquals(donationSettingData.getFee(), donationSettings.getFee());
        assertEquals(donationSettingData.getSampleTextToGiveDate(), donationSettings.getSampleTextToGiveDate());
        assertEquals(donationSettingData.getDonationAmountA(), donationSettings.getDonationAmountA());
        assertEquals(donationSettingData.getDonationAmountB(), donationSettings.getDonationAmountB());
        assertEquals(donationSettingData.getDonationAmountC(), donationSettings.getDonationAmountC());
        assertEquals(donationSettingData.getDonationAmountD(), donationSettings.getDonationAmountD());
        assertEquals(donationSettingData.getDonationGoalAmount(), donationSettings.getDonationGoalAmount());
        assertEquals(donationSettingData.getCustomThermometerImage(), donationSettings.getCustomThermometerImage());
        assertEquals(donationSettingData.getDonationViewBackgroundImage(), donationSettings.getDonationViewBackgroundImage());
        assertEquals(donationSettingData.getTHANK_YOU_SMS(), donationSettings.getTHANK_YOU_SMS());
    }
}