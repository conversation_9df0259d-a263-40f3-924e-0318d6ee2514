package com.accelevents.services.impl;

import com.accelevents.domain.*;
import com.accelevents.domain.enums.Currency;
import com.accelevents.domain.enums.*;
import com.accelevents.dto.*;
import com.accelevents.exceptions.NotAcceptableException;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.messages.EnumPaymentGateway;
import com.accelevents.messages.TicketType;
import com.accelevents.repositories.*;
import com.accelevents.services.*;
import com.accelevents.services.repo.helper.EventCommonRepoService;
import com.accelevents.ticketing.dto.AttendeeDto;
import com.accelevents.ticketing.dto.PurchaserInfo;
import com.accelevents.ticketing.dto.RefundInfo;
import com.itextpdf.text.DocumentException;
import com.squareup.square.exceptions.ApiException;
import com.stripe.exception.StripeException;
import freemarker.template.TemplateException;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.*;

import static com.accelevents.utils.Constants.*;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_FLAT;
import static com.accelevents.utils.FeeConstants.CREDIT_CARD_PROCESSING_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TicketingOrderTransferServiceImplTest {

    @Spy
    @InjectMocks
    private TicketingOrderTransferServiceImpl ticketingOrderTransferService;
    @Mock
    private TicketingOrderService ticketingOrderService;
    @Mock
    private EventTicketsService eventTicketsService;
    @Mock
    private EventCommonRepoService eventCommonRepoService;

    @Mock
    private EventTicketTransferDetailService eventTicketTransferDetailService;

    @Mock
    private EventTicketsRepository eventTicketsRepository;

    @Mock
    private TicketingRefundService ticketingRefundService;

    @Mock
    private TicketingTypeService ticketingTypeService;

    @Mock
    private TicketingStatisticsService ticketingStatisticsService;

    @Mock
    private TicketingOrderManagerService ticketingOrderManagerService;

    @Mock
    private TicketingService ticketingService;
    @Mock
    private AfterTaskIntegrationTriggerService afterTaskIntegrationTriggerService;

    @Mock
    private TicketingPurchaseService ticketingPurchaseService;

    @Mock
    private EventTicketTransferDetailRepository eventTicketTransferDetailRepository;

    @Mock
    private TicketingOrderRepository ticketingOrderRepository;

    @Mock
    private StripeService stripeService;

    @Mock
    private SalesTaxService salesTaxService;

    @Mock
    private VatTaxService vatTaxService;

    @Mock
    private  TransactionFeeConditionalLogicService transactionFeeConditionalLogicService;

    @Mock
    private TicketingHelperService ticketingHelperService;

    Event event;
    Ticketing ticketing;
    User user, purchaser, purchaser1;
    TicketHolderAttributes ticketHolderAttributes;
    EventTickets eventTickets, eventTickets1, eventTickets2;
    TicketingTable ticketingTable;
    RefundInfo refundInfo;
    PurchaserInfo purchaserInfo;
    TicketingOrder ticketingOrder;
    StripeTransaction stripeTransaction;
    AttendeeDto attendeeDto, attendeeDto1;
    TicketingOrderManager ticketingOrderManager;
    TicketHolderEmailDto ticketHolderEmailDto;
    EventTicketRefundTracker eventTicketRefundTracker;
    Stripe stripe;
    StripeDTO stripeDTO;
    PurchaserInfo updatedPurchaser;
    private SalesTaxFeeDto salesTaxFeeDto;

    private long numberOfTicketsWhichArePaid = 1;


    private EventTicketFeesDto eventTicketFeesDto;

    Long id = 1L;
    @BeforeEach
    void setUp() {

        // Prepare data
        event = new Event("TestEvent", true, true, true, true, AccountActivatedTriggerStatus.INITIAL);

        event.setEventId(id);
        event.setCurrency(Currency.AUD);
        event.setEventURL("asd");

        ticketing = new Ticketing();
        ticketing.setId(id);
        ticketing.setActivated(true);
        ticketing.setEventid(event);
        ticketing.setEventAddress("testAddress");
        ticketing.setShowRemainingTickets(false);
        ticketing.setEventStartDate(new Date());
        ticketing.setEventEndDate(LocalDate.now().plusDays(10).toDate());
        ticketing.setCheckoutminutes(10);
        ticketing.setAllowTicketExchange(true);

        user = new User();
        user.setEmail("<EMAIL>");
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setFirstName("Normal");
        user.setLastName("User");
        user.setMostRecentEventId(id);

        purchaser = new User();
        purchaser.setEmail("<EMAIL>");
        purchaser.setFirstName("buyer");
        purchaser.setLastName("user");

        purchaser1 = new User();
        purchaser1.setEmail("<EMAIL>");
        purchaser1.setFirstName("buyer");
        purchaser1.setLastName("user1");

        ticketHolderAttributes = new TicketHolderAttributes();
        ticketHolderAttributes.setId(id);

        eventTickets = EventDataUtil.getEventTickets();
        eventTickets1 = EventDataUtil.getEventTickets();
        eventTickets2 = EventDataUtil.getEventTickets();

        ticketingTable = new TicketingTable();
        ticketingTable.setId(1L);

        refundInfo = new RefundInfo();

        purchaserInfo = new PurchaserInfo();

        ticketingOrder = new TicketingOrder();
        ticketingOrder.setId(id);
        ticketingOrder.setEventid(event);
        ticketingOrder.setPurchaser(user);
        ticketingOrder.setOrderDate(new Date());

        stripeTransaction = new StripeTransaction();
        String cardType = "visa";
        stripeTransaction.setCardType(cardType);
        String lastFour = "1111";
        stripeTransaction.setLastFour(lastFour);

        attendeeDto = new AttendeeDto();
        attendeeDto1 = new AttendeeDto();

        ticketingOrderManager = new TicketingOrderManager();

        ticketHolderEmailDto = new TicketHolderEmailDto();

        eventTicketRefundTracker = new EventTicketRefundTracker();

        stripe = new Stripe();
        stripe.setCCFlatFee(CREDIT_CARD_PROCESSING_FLAT);
        stripe.setCCPercentageFee(CREDIT_CARD_PROCESSING_PERCENTAGE);
        stripeDTO = new StripeDTO(CREDIT_CARD_PROCESSING_FLAT, CREDIT_CARD_PROCESSING_PERCENTAGE, "US", EnumPaymentGateway.STRIPE.name());

        updatedPurchaser = new PurchaserInfo(user);

        eventTicketFeesDto = new EventTicketFeesDto();
        eventTicketFeesDto.setEventTicketId(eventTickets.getId());
        eventTicketFeesDto.setEventTicketPaidAmount(103.30);
        eventTicketFeesDto.setCcFeeAmount(3.30);

        salesTaxFeeDto = new SalesTaxFeeDto(true,10d,"1");
    }

    private  TicketExchangeDto prepareTicketExchangeDto(String paymentType) {
        TicketExchangeDto ticketExchangeDto = new TicketExchangeDto();
        ticketExchangeDto.setEventTicketId(1L);
        ticketExchangeDto.setNewTicketTypeId(2L);
        ticketExchangeDto.setOrderId(1L);
        ticketExchangeDto.setSentMailToAttendee(true);
        AttendeePartialPaymentDto ticketExchangePaymentDto = new AttendeePartialPaymentDto();
        ticketExchangePaymentDto.setPaymentType(paymentType);
        ticketExchangeDto.setPaymentDetails(ticketExchangePaymentDto);
        return ticketExchangeDto;
    }

    private  TicketingType prepareTicketType(long ticketingTypeId, String name , TicketType ticketType, double price ) {
        TicketingType ticketingType = new TicketingType();
        ticketingType.setId(ticketingTypeId);
        ticketingType.setTicketTypeName(name);
        ticketingType.setEventId(event.getEventId());
        ticketingType.setTicketType(ticketType);
        ticketingType.setTicketing(ticketing);
        ticketingType.setPrice(price);
        ticketingType.setNumberOfTickets(100);
        ticketingType.setNumberOfTicketPerTable(1);
        return ticketingType;
    }

    private TicketingOrder mockTicketingOrder(TicketingOrder ticketingOrder) {
        when(ticketingOrderService.findByid(anyLong())).thenReturn(ticketingOrder);
        return ticketingOrder;
    }


    private EventTickets mockEventTicket(EventTickets eventTickets) {
        when(eventTicketsService.findEventTicketById(anyLong())).thenReturn(Optional.ofNullable(eventTickets));
        return eventTickets;
    }

    private TicketingType mockFindTicketTypeWithSameCreatedFromAndInRecurringEventId(TicketingType ticketingType) {
        when(ticketingTypeService.findTicketTypeWithSameCreatedFromAndInRecurringEventId(anyLong(),anyLong())).thenReturn(ticketingType);
        return ticketingType;
    }

    private long mockGetRemainingTicketCount(long numberOfTicket) {
        when(ticketingStatisticsService.getRemainingTicketCount(any())).thenReturn(numberOfTicket);
        return numberOfTicket;
    }

    private TicketingOrderManager mockTicketingOrderManager(TicketingType  ticketingType,TicketingOrder ticketingOrder,TicketingOrderManager ticketingOrderManager) {
        Mockito.when(ticketingOrderManagerService.getTicketTypeByOrderIdAndTicketTypeId(ticketingType,ticketingOrder)).thenReturn(ticketingOrderManager);
        return ticketingOrderManager;
    }

    private Ticketing mockTicketing(Ticketing ticketing) {
        when(ticketingService.findByEvent(any())).thenReturn(ticketing);
        return ticketing;
    }


//    @Test
//    void test_getDueAmountForLowerToHigherTicketTransfer_with_card_payment_type_and_ticket_paid_by_cash_then_throw_exception() {
//        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);
//        // Mock
//        mockEventTicket(eventTickets);
//
//        //execute
//        Exception exception = assertThrows(NotAcceptableException.class,
//                () -> ticketingOrderTransferService.getDueAmountForLowerToHigherTicketTransfer(ticketExchangeDto, event, user));
//
//        assertEquals("Order is partially paid by Cash, please use Cash payment type.", exception.getMessage());
//    }
//
//    @Test
//    void test_getDueAmountForLowerToHigherTicketTransfer_with_cash_payment_type_and_ticket_paid_by_card_then_throw_exception() {
//        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CASH);
//        eventTickets.setChargeId("123");
//
//        // Mock
//        mockEventTicket(eventTickets);
//
//        //execute
//        Exception exception = assertThrows(NotAcceptableException.class,
//                () -> ticketingOrderTransferService.getDueAmountForLowerToHigherTicketTransfer(ticketExchangeDto, event, user));
//
//        assertEquals("Order is partially paid by Card, please use Card payment type.", exception.getMessage());
//    }

    @Test
    void test_getDueAmountForLowerToHigherTicketTransfer_with_card_payment_type() {
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);

        eventTickets.setChargeId("123");
        TicketingType newTicketingType = prepareTicketType(2, "New", TicketType.PAID, 200.0);

        // Mock
        mockEventTicket(eventTickets);
        when(ticketingPurchaseService.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, false,eventTickets.getVatTaxFee(), null)).thenReturn(eventTicketFeesDto);
        mockFindTicketTypeWithSameCreatedFromAndInRecurringEventId(newTicketingType);
        TicketTransferDueAmountDto ticketTransferDueAmountDto = ticketingOrderTransferService.getDueAmountForLowerToHigherTicketTransfer(ticketExchangeDto, event, user);

        assertEquals(EventTicketTransaction.PaymentType.CARD, ticketTransferDueAmountDto.getPaymentMethod());
        assertEquals(103.30, ticketTransferDueAmountDto.getDueAmount());
    }

    @Test
    void test_getDueAmountForLowerToHigherTicketTransfer_with_cash_payment_type() {
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CASH);
        ticketExchangeDto.getPaymentDetails().setPaymentType(CASH);

        TicketingType newTicketingType = prepareTicketType(1, "New", TicketType.PAID, 200.0);
        eventTicketFeesDto.setEventTicketPaidAmount(100.00);

        // Mock
        mockEventTicket(eventTickets);
        mockFindTicketTypeWithSameCreatedFromAndInRecurringEventId(newTicketingType);

        when(ticketingPurchaseService.calculateFeeForLowerToHigherTicketTransfer(eventTickets, newTicketingType, event, true, eventTickets.getVatTaxFee(), null)).thenReturn(eventTicketFeesDto);

        TicketTransferDueAmountDto ticketTransferDueAmountDto = ticketingOrderTransferService.getDueAmountForLowerToHigherTicketTransfer(ticketExchangeDto, event, user);

        assertEquals(EventTicketTransaction.PaymentType.CASH, ticketTransferDueAmountDto.getPaymentMethod());
        assertEquals(100.00, ticketTransferDueAmountDto.getDueAmount());
    }


    @Test
    void testTransferComplementaryEventTicket() throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        ticketingOrder.setOrderType(TicketingOrder.OrderType.COMPLIMENTARY);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(0);
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);
        TicketingType oldTicketType = prepareTicketType(1, "Old", TicketType.PAID, 100.0);
        TicketingType newTicketType = prepareTicketType(2, "New", TicketType.PAID, 200.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        List<EventTickets> allValues = eventTicketArgumentCaptor.getAllValues();
        assertEquals(newTicketType.getId(), allValues.get(0).getTicketingTypeId().getId());
    }

    @ParameterizedTest
    @CsvSource({
            "1, 1",  // oldTicketCount, newTicketCount
            "2, 0",
            "2, 2",
            "1, 0",
    })
    void testUpdateTicketTypeInTicketingOrderManager(int oldTicketCount, int newTicketCount) throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        TicketingType oldTicketType = prepareTicketType(1, "Old", TicketType.PAID, 100.0);
        TicketingType newTicketType = prepareTicketType(2, "New", TicketType.PAID, 200.0);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketingTypeId(oldTicketType);
        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(oldTicketCount);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(newTicketCount);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        // mock
        mockTicketingOrderManager(oldTicketType, oldTicketingOrderManager.getOrderId(), oldTicketingOrderManager);
        if (newTicketCount == 0) {
            mockTicketingOrderManager(newTicketType, newTicketingOrderManager.getOrderId(), null);
        } else {
            mockTicketingOrderManager(newTicketType, newTicketingOrderManager.getOrderId(), newTicketingOrderManager);
        }

        // test
        ticketingOrderTransferService.updateTicketTypeInTicketingOrderManager(ticketingOrder,newTicketType,eventTickets);
        // need to write assertion

        ArgumentCaptor<TicketingOrderManager> orderManagerArgumentCaptor = ArgumentCaptor.forClass(TicketingOrderManager.class);
        if(oldTicketCount ==1 && newTicketCount==1){
        verify(ticketingOrderManagerService, Mockito.times(1)).save(orderManagerArgumentCaptor.capture());
            List<TicketingOrderManager> allValues = orderManagerArgumentCaptor.getAllValues();
            assertEquals(newTicketCount+1, allValues.get(0).getNumberofticket());
        }else if (oldTicketCount ==2 && newTicketCount==0) {
            verify(ticketingOrderManagerService, Mockito.times(2)).save(orderManagerArgumentCaptor.capture());
            List<TicketingOrderManager> allValues = orderManagerArgumentCaptor.getAllValues();
            assertEquals(oldTicketCount - 1, allValues.get(0).getNumberofticket());
            assertEquals(newTicketCount + 1, allValues.get(1).getNumberofticket());
        } else if (oldTicketCount ==2 && newTicketCount==2) {
            verify(ticketingOrderManagerService, Mockito.times(2)).save(orderManagerArgumentCaptor.capture());
            List<TicketingOrderManager> allValues = orderManagerArgumentCaptor.getAllValues();
            assertEquals(newTicketCount + 1, allValues.get(0).getNumberofticket());
            assertEquals(newTicketCount - 1, allValues.get(1).getNumberofticket());

        }
    }

    @Test
    void testTransferFreeEventTicket() throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(0);
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-FREE", TicketType.FREE, 0.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-FREE", TicketType.FREE, 0.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(0, eventTicketCaptured.getPaidAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(null, eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.PAID, eventTicketCaptured.getTicketPaymentStatus());
    }

    @Test
    void testTransferPaidEventTicketToFreeTicket() throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(100);
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-PAID", TicketType.PAID, 100.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-FREE", TicketType.FREE, 0.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(0, eventTicketCaptured.getPaidAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(null, eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.PAID, eventTicketCaptured.getTicketPaymentStatus());
    }

    @Test
    void testTransferFreeEventTicketToPaidTicket() throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(0);
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(CARD);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-FREE", TicketType.FREE, 0.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-PAID", TicketType.PAID, 100.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(2)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.UNPAID,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(0, eventTicketCaptured.getPaidAmount());
        assertEquals(newTicketType.getPrice(), eventTicketCaptured.getTicketPrice());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(null, eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.UNPAID, eventTicketCaptured.getTicketPaymentStatus());
    }


    @ParameterizedTest
    @CsvSource({
            "CARD",
            "CASH",
    })
    void testTransferPaidEventTicketToPaidTicketLowerToHigher(String paymentMethod) throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        if(paymentMethod.equals(CARD)){
            eventTickets.setChargeId("ch_1");
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
            eventTickets.setPaidAmount(10.0);
        }else {
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
            eventTickets.setPaidAmount(10.0);
        }
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(10.0);

        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setRefundedAmount(0);
        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(paymentMethod);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-FREE", TicketType.PAID, 10.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-PAID", TicketType.PAID, 100.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        List<TicketingOrderManager> managerList = new ArrayList<>();
        managerList.add(oldTicketingOrderManager);
        managerList.add(newTicketingOrderManager);

        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(managerList);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId())).thenReturn(salesTaxFeeDto);
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),newTicketType)).thenReturn(0d);
        when(transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketType.getTicketTypeFormat(), event)).thenReturn(19.95);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(2)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PARTIAL,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(eventTickets.getPaidAmount(), eventTicketCaptured.getPaidAmount());
        assertEquals(newTicketType.getPrice(), eventTicketCaptured.getTicketPrice());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(eventTickets.getChargeId(), eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.PARTIALLY_PAID, eventTicketCaptured.getTicketPaymentStatus());
    }

    @ParameterizedTest
    @CsvSource({
            "CARD",
            "CASH",
    })
    void testTransferPaidEventTicketToPaidTicketSameToSamePrice(String paymentMethod) throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        if(paymentMethod.equals(CARD)){
            eventTickets.setChargeId("ch_1");
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
            eventTickets.setPaidAmount(103.3);
        }else {
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
            eventTickets.setPaidAmount(100.0);
        }
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(100.0);

        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setRefundedAmount(0);

        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(paymentMethod);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-FREE", TicketType.PAID, 100.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-PAID", TicketType.PAID, 100.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        List<TicketingOrderManager> managerList = new ArrayList<>();
        managerList.add(oldTicketingOrderManager);
        managerList.add(newTicketingOrderManager);

        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(managerList);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId())).thenReturn(salesTaxFeeDto);
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),newTicketType)).thenReturn(0d);
        when(transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketType.getTicketTypeFormat(), event)).thenReturn(19.95);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(eventTickets.getPaidAmount(), eventTicketCaptured.getPaidAmount());
        assertEquals(newTicketType.getPrice(), eventTicketCaptured.getTicketPrice());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(eventTickets.getChargeId(), eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.PAID, eventTicketCaptured.getTicketPaymentStatus());
    }

    @ParameterizedTest
    @CsvSource({
            "CARD",
            "CASH",
    })
    void testTransferPaidEventTicketToPaidTicketHigherToLower(String paymentMethod) throws NotFoundException, NotAcceptableException, StripeException, ApiException, IOException, JAXBException, TemplateException, DocumentException {
        // Prepare data
        if(paymentMethod.equals(CARD)){
            eventTickets.setChargeId("ch_1");
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CARD);
        }else {
            ticketingOrder.setOrderType(TicketingOrder.OrderType.CASH);
        }
        ticketingOrder.setStatus(TicketingOrder.TicketingOrderStatus.PAID);
        eventTickets.setTicketingOrder(ticketingOrder);
        eventTickets.setTicketPrice(100.0);
        eventTickets.setPaidAmount(100.0);
        eventTickets.setTicketPaymentStatus(TicketPaymentStatus.PAID);
        eventTickets.setRefundedAmount(0);

        TicketExchangeDto ticketExchangeDto = prepareTicketExchangeDto(paymentMethod);
        TicketingType oldTicketType = prepareTicketType(1, "OLD-FREE", TicketType.PAID, 100.0);
        TicketingType newTicketType = prepareTicketType(2, "NEW-PAID", TicketType.PAID, 10.0);
        eventTickets.setTicketingTypeId(oldTicketType);

        TicketingOrderManager  oldTicketingOrderManager= new TicketingOrderManager();
        oldTicketingOrderManager.setTicketType(oldTicketType);
        oldTicketingOrderManager.setNumberofticket(1);
        oldTicketingOrderManager.setOrderId(ticketingOrder);

        TicketingOrderManager  newTicketingOrderManager= new TicketingOrderManager();
        newTicketingOrderManager.setTicketType(newTicketType);
        newTicketingOrderManager.setNumberofticket(1);
        newTicketingOrderManager.setOrderId(ticketingOrder);

        List<TicketingOrderManager> managerList = new ArrayList<>();
        managerList.add(oldTicketingOrderManager);
        managerList.add(newTicketingOrderManager);

        when(ticketingOrderManagerService.getAllByOrderId(ticketingOrder)).thenReturn(managerList);

        // mock
        mockEventTicket(eventTickets);
        mockTicketingOrder(ticketingOrder);
        when(stripeService.getStripeFeesByEvent(event)).thenReturn(stripeDTO);
        when(salesTaxService.getTaxFeeAndTicketTypeId(event.getEventId())).thenReturn(salesTaxFeeDto);
        when(vatTaxService.getVatTaxByTicketTypeOrEvent(event.getEventId(),newTicketType)).thenReturn(0d);
        when(transactionFeeConditionalLogicService.getCapAmountForVirtualEvent(newTicketType.getTicketTypeFormat(), event)).thenReturn(19.95);
        when(ticketingTypeService.findById(ticketExchangeDto.getNewTicketTypeId())).thenReturn(Optional.of(newTicketType));
        mockGetRemainingTicketCount(numberOfTicketsWhichArePaid);
        mockTicketingOrderManager(oldTicketType,ticketingOrder,oldTicketingOrderManager);
        mockTicketingOrderManager(newTicketType, ticketingOrder,newTicketingOrderManager);
        mockTicketing(ticketing);
        Mockito.doNothing().when(ticketingOrderTransferService).handleEmailForTicketTransfer(any(),any(),any(),any(),any(),any(),anyBoolean(),anyBoolean(),anyString(),anyBoolean(),any(),anyBoolean(), any());
        Mockito.doNothing().when(afterTaskIntegrationTriggerService).onTicketTypeTransferEvent(any(),any());
        when(ticketingHelperService.findTicketingByEventAndIfNotFoundCreateNew(event)).thenReturn(ticketing);

        // test
        ticketingOrderTransferService.transferEventTicket(ticketingOrder.getId(),ticketExchangeDto,event, user);
        // need to write assertion
        ArgumentCaptor<TicketingOrder> orderArgumentCaptor = ArgumentCaptor.forClass(TicketingOrder.class);
        verify(ticketingOrderRepository, Mockito.times(1)).save(orderArgumentCaptor.capture());
        TicketingOrder ticketingOrder1 = orderArgumentCaptor.getValue();
        assertEquals(TicketingOrder.TicketingOrderStatus.PAID,ticketingOrder1.getStatus());

        ArgumentCaptor<EventTickets> eventTicketArgumentCaptor = ArgumentCaptor.forClass(EventTickets.class);
        verify(eventTicketsRepository, Mockito.times(1)).save(eventTicketArgumentCaptor.capture());
        EventTickets eventTicketCaptured = eventTicketArgumentCaptor.getValue();
        assertEquals(newTicketType.getId(), eventTicketCaptured.getTicketingTypeId().getId());
        assertEquals(eventTickets.getPaidAmount(), eventTicketCaptured.getPaidAmount());
        assertEquals(newTicketType.getPrice(), eventTicketCaptured.getTicketPrice());
        assertEquals(0, eventTicketCaptured.getRefundedAmount());
        assertEquals(0, eventTicketCaptured.getAeFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedAEFee());
        assertEquals(0, eventTicketCaptured.getWlAFeeAmount());
        assertEquals(0, eventTicketCaptured.getWlBFeeAmount());
        assertEquals(0, eventTicketCaptured.getRefundedWlAFee());
        assertEquals(0, eventTicketCaptured.getRefundedWlBFee());
        assertEquals(0, eventTicketCaptured.getSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedSalesTaxFee());
        assertEquals(0, eventTicketCaptured.getVatTaxFee());
        assertEquals(0, eventTicketCaptured.getRefundedVatTaxFee());
        assertEquals(eventTickets.getChargeId(), eventTicketCaptured.getChargeId());
        assertEquals(TicketPaymentStatus.PAID, eventTicketCaptured.getTicketPaymentStatus());
    }
}
