package com.accelevents.services.impl;

import com.accelevents.badges.Badges;
import com.accelevents.badges.JoinBadgeTicketTypes;
import com.accelevents.domain.Event;
import com.accelevents.domain.EventTickets;
import com.accelevents.domain.User;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.dto.BadgePrintingHistorySortDTO;
import com.accelevents.dto.BadgesPrintHistoryDTO;
import com.accelevents.exceptions.NotFoundException;
import com.accelevents.repositories.BadgePrintHistoryRepository;
import com.accelevents.services.BadgesService;
import com.accelevents.services.JoinBadgeTicketTypesService;
import com.accelevents.services.dynamodb.user.activity.UserActivityService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BadgePrintHistoryServiceImplTest {

    @Spy
    @InjectMocks
    private BadgePrintHistoryServiceImpl badgePrintHistoryService = new BadgePrintHistoryServiceImpl();
    
    @Mock
    private BadgePrintHistoryRepository badgePrintHistoryRepository;

    @Mock
    private BadgesService badgesService;
    @Mock
    private JoinBadgeTicketTypesService joinBadgeTicketTypesService;
    @Mock
    private UserActivityService userActivityService;

    private Event event;
    private User user;
    private Badges customeBadges;
    private EventTickets eventTickets;
    private BadgePrintingHistorySortDTO badgePrintingHistorySortDTO;
    private BadgesPrintHistoryDTO badgePrintHistory;


    @BeforeEach
    void setUp() throws Exception {
        event = EventDataUtil.getEvent();
        event.setEventFormat(EventFormat.VIRTUAL);

        user = EventDataUtil.getUser();
        user.setPassword("$2a$10$Et9hLralSDZjfxQ5pGaSXOqk0IQOMuhJswd3hcbda9jWe5QNqYWHm");
        user.setMostRecentEventId(event.getEventId());

        customeBadges = new Badges();
        customeBadges.setId(1L);
        customeBadges.setEventId(event.getEventId());
        customeBadges.setBadgeMasterId(1L);

        customeBadges.setBadgeName("Test Badge Save And Update");
        customeBadges.setWidth(10.0);
        customeBadges.setHeight(10.0);
        customeBadges.setBleedAreaHeight(5.0);
        customeBadges.setBleedAreaWidth(5.0);
        customeBadges.setSafeAreaHeight(5.0);
        customeBadges.setSafeAreaWidth(5.0);
        customeBadges.setNumberOfView(1);
        customeBadges.setDesignJson("{\n  \"mainStageSessionColor\": \"#2EC974\",\n  \"breakoutSessionColor\": \"#377EF9\",\n  \"" +
                "meetUpSessionColor\": \"#F0AD4E\",\n  \"workshopSessionColor\": \"#C9C12E\",\n  \"expoSessionColor\": \"#722EC9\",\n}");

        eventTickets = new EventTickets();
        eventTickets.setBadgeId(1L);
        eventTickets.setId(1L);
        eventTickets.setTicketingTypeOnlyId(1L);
        eventTickets.setHolderUserId(user);
        eventTickets.setBarcodeId("barcode");

        badgePrintingHistorySortDTO = new BadgePrintingHistorySortDTO();
        badgePrintingHistorySortDTO.setBadgeId(1L);
        badgePrintingHistorySortDTO.setBarcodeId("barcode");

        badgePrintHistory = new BadgesPrintHistoryDTO();
        badgePrintHistory.setTicketingTypeId(1L);
        badgePrintHistory.setPrintBadgeId(1L);

    }

    @Test
    void test_saveBadgePrintHistory(){
        List<JoinBadgeTicketTypes> ticketTypeIdByBadgeId = List.of(
                new JoinBadgeTicketTypes(customeBadges, 1L)
        );

        when(badgesService.getAllEventTicketDetailByBarcodeId(any(),any(),anyList())).thenReturn(Collections.singletonList(eventTickets));
        when(badgesService.getListOfBadgeUsingListOfBadgeIdAndEventId(anyList(),anyLong())).thenReturn(Collections.singletonList(customeBadges));
        when(joinBadgeTicketTypesService.findByBadgesIds(List.of(customeBadges.getId()))).thenReturn(ticketTypeIdByBadgeId);
        badgePrintHistoryService.saveBadgePrintHistory(event,user,Collections.singletonList(badgePrintingHistorySortDTO), null, null, false);

        verify(badgePrintHistoryRepository).saveAll(anyList());
    }

    @Test
    void test_saveBadgePrintHistory_badge_not_found(){
        //setup
        when(badgesService.getAllEventTicketDetailByBarcodeId(any(),any(),anyList())).thenReturn(Collections.singletonList(eventTickets));
        when(badgesService.getListOfBadgeUsingListOfBadgeIdAndEventId(anyList(),anyLong())).thenReturn(Collections.singletonList(customeBadges));

        Exception exception = assertThrows(NotFoundException.class,
                () -> badgePrintHistoryService.saveBadgePrintHistory(event,user,Collections.singletonList(badgePrintingHistorySortDTO), null, null, false));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_saveBadgePrintHistory_optional_badge_not_found(){

        customeBadges.setId(2L);
        //setup
        when(badgesService.getAllEventTicketDetailByBarcodeId(any(),any(),anyList())).thenReturn(Collections.singletonList(eventTickets));
        when(badgesService.getListOfBadgeUsingListOfBadgeIdAndEventId(anyList(),anyLong())).thenReturn(Collections.singletonList(customeBadges));

        Exception exception = assertThrows(NotFoundException.class,
                () -> badgePrintHistoryService.saveBadgePrintHistory(event,user,Collections.singletonList(badgePrintingHistorySortDTO), null, null, false));
        assertEquals(NotFoundException.NotFound.NOT_FOUND_BADGES.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_saveBadgePrintHistory_event_ticket_not_found(){

        badgePrintingHistorySortDTO.setBarcodeId("test");

        //setup
        when(badgesService.getAllEventTicketDetailByBarcodeId(any(),any(),anyList())).thenReturn(Collections.singletonList(eventTickets));
        when(badgesService.getListOfBadgeUsingListOfBadgeIdAndEventId(anyList(),anyLong())).thenReturn(Collections.singletonList(customeBadges));

        Exception exception = assertThrows(NotFoundException.class,
                () -> badgePrintHistoryService.saveBadgePrintHistory(event,user,Collections.singletonList(badgePrintingHistorySortDTO), null, null, false));
        assertEquals(NotFoundException.SessionSpeakerNotFound.EVENT_TICKET_NOT_FOUND.getDeveloperMessage(), exception.getMessage());
    }

    @Test
    void test_getBadgePrintHistory(){

//        Pageable pageable = PageRequest.of(0, 1, Sort.Direction.DESC, BADGES_UPDATED_AT);
        Page<BadgesPrintHistoryDTO> pageBadgesPrintHistoryDTO = new PageImpl(Collections.singletonList(badgePrintHistory));

        when(badgePrintHistoryRepository.findByAttendeeUserIdAndBadgeIdAndEventIdAndTicketingTypeIdOrderPage(anyLong(), anyLong(), anyLong(), anyLong(), any())).thenReturn(pageBadgesPrintHistoryDTO);

        BadgesPrintHistoryDTO badgePrintHistoryResult = badgePrintHistoryService.getBadgePrintHistory(event, user, eventTickets, 1L);

        assertEquals(badgePrintHistory,badgePrintHistoryResult);
    }



}
