package com.accelevents.services.impl;

import com.accelevents.common.dto.GetStreamUserInfoDto;
import com.accelevents.configuration.ImageConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static com.accelevents.utils.Constants.MESSAGING;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GetStreamServiceImplTest {

    @Mock
    private ImageConfiguration imageConfiguration;
	@Spy
	@InjectMocks
	private GetStreamServiceImpl getStreamService =
			new GetStreamServiceImpl("2wmm32pucsjqdj9abwxkrb53dwvcny5jhj3c67h6u2by82bg67wyeg3zkdwvgnuj","ryjkz3yn6aym");




	@Test
	void test_createChatUser(){

        when(imageConfiguration.getImagePreFixWithCloudinaryUrl()).thenReturn("cloudinary_prefix_url");
        GetStreamUserInfoDto userInfoDto = new GetStreamUserInfoDto("test", 2L, 1L, null,"test");
		assertTrue(getStreamService.createChatUser(Collections.singletonList(userInfoDto)));
	}

	@Test
	void test_Channel(){
		assertTrue(getStreamService.createChannel("testchannel", MESSAGING,2L, 1L));
	}

	@Test
	void test_ChannelWithName(){
		getStreamService.createChannelWithChannelName("testchannel", "testchannel",MESSAGING,2L, Collections.singletonList(1L), false, 1L);
	}

	@Test
	void test_addMemberToChannel(){
		assertTrue(getStreamService.addMemberToChannel("testchannel",MESSAGING,Collections.singletonList(1L)));
	}

	@Test
	void test_generateToken(){
		String token = getStreamService.generateTokenForUser(1L);
		assertNotNull(token);
		System.out.println(token);
	}

}
