package com.accelevents.services.zapier.impl;

import com.accelevents.common.dto.UploadSessionDto;
import com.accelevents.domain.Event;
import com.accelevents.domain.enums.EnumSessionFormat;
import com.accelevents.domain.enums.EventFormat;
import com.accelevents.domain.enums.SessionTypeFormat;
import com.accelevents.domain.session_speakers.Session;
import com.accelevents.services.IntegrationService;
import com.accelevents.services.impl.EventDataUtil;
import com.accelevents.services.repo.helper.EventRepoService;
import com.accelevents.session_speakers.services.SessionService;
import com.accelevents.utils.Constants;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ZapierSessionUploadServiceImplTest {

    @Spy
    @InjectMocks
    private ZapierSessionUploadServiceImpl zapierSessionUploadService;

    @Mock
    private IntegrationService integrationService;
    @Mock
    private EventRepoService eventRepoService;
    @Mock
    private SessionService sessionService;

    private Event event;
    private Session session;

    @BeforeEach
    void setUp() {
        event = EventDataUtil.getEvent();
        session = EventDataUtil.getSession();
    }

    @Test
    void testIsValidFormat() throws Exception {
        // Arrange
        String validFormat1 = "MAIN_STAGE";
        String validFormat2 = "BREAKOUT_SESSION";
        String invalidFormat = "INVALID_FORMAT";

        // Use Reflection to access the private method
        Method method = ZapierSessionUploadServiceImpl.class.getDeclaredMethod("isValidFormat", String.class);
        method.setAccessible(true); // Make the method accessible

        // Act
        boolean isValid1 = (boolean) method.invoke(zapierSessionUploadService, validFormat1);
        boolean isValid2 = (boolean) method.invoke(zapierSessionUploadService, validFormat2);
        boolean isInvalid = (boolean) method.invoke(zapierSessionUploadService, invalidFormat);

        // Assert
        assertTrue(isValid1, "Expected valid format to return true");
        assertTrue(isValid2, "Expected valid format to return true");
        assertFalse(isInvalid, "Expected invalid format to return false");
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "MAIN_STAGE_SESSION"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "MEET_UP"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "REGULAR_SESSION"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "WORKSHOP"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "EXPO"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "BREAK"));
        assertTrue((boolean) method.invoke(zapierSessionUploadService, "OTHER"));
    }

    @Test
    void testCreateUploadSessionDtoFromJsonObj() throws JSONException {
        // Arrange
//        MockitoAnnotations.openMocks(this);

        JSONObject zapierSessionUploadJson = new JSONObject();
        zapierSessionUploadJson.put(Constants.Zapier.TITLE, "Sample Title");
        zapierSessionUploadJson.put(Constants.Zapier.SESSION_FORMAT_TYPE, Constants.MAIN_STAGE_SESSION_ENUM);
        zapierSessionUploadJson.put(Constants.Zapier.SESSION_LOCATION, "Main Hall");
        zapierSessionUploadJson.put(Constants.Zapier.START_DATE_TIME, "2024-12-29T14:56:00+00:00");
        zapierSessionUploadJson.put(Constants.Zapier.END_DATE_TIME, "2024-12-29T18:00:00+00:00");
        zapierSessionUploadJson.put(Constants.Zapier.DESCRIPTION, "Sample Description");
        zapierSessionUploadJson.put(Constants.Zapier.CAPACITY, 100);
        zapierSessionUploadJson.put(Constants.Zapier.SHORT_DESC, "Short Description");
        JSONArray tagsArray = new JSONArray();
        tagsArray.put("Tag1");
        tagsArray.put("Tag2");
        zapierSessionUploadJson.put(Constants.Zapier.TAGS, tagsArray);
        JSONArray trackArray = new JSONArray();
        trackArray.put("Track1");
        zapierSessionUploadJson.put(Constants.Zapier.TRACKS, trackArray);

        event.setEventFormat(EventFormat.HYBRID);

        // Act
        UploadSessionDto uploadSessionDto = zapierSessionUploadService. createUploadSessionDtoFromJsonObj(zapierSessionUploadJson, event);

        // Assert
        assertNotNull(uploadSessionDto);
        assertEquals("Sample Title", uploadSessionDto.getTitle());
        assertEquals(SessionTypeFormat.HYBRID, uploadSessionDto.getSessionTypeFormat()); // Example enum
        assertEquals(EnumSessionFormat.MAIN_STAGE, uploadSessionDto.getFormat());
        assertEquals("Main Hall", uploadSessionDto.getSessionLocation());
        assertEquals("2024/12/29 20:26", uploadSessionDto.getStartDateTime());
        assertEquals("2024/12/29 23:30", uploadSessionDto.getEndDateTime());
        assertEquals("Sample Description", uploadSessionDto.getDescription());
        assertEquals(100, uploadSessionDto.getCapacity());
        assertEquals("Short Description", uploadSessionDto.getShortDescriptionOfSession());
        assertEquals("Tag1, Tag2", uploadSessionDto.getTags());
        assertEquals("Track1", uploadSessionDto.getTracks());
    }


}