package com.accelevents.exceptions;

public class ForbiddenException extends BaseException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public ForbiddenException(StaffExceptionMsg staffExceptionMsg) {
		super(403, staffExceptionMsg.getStatusCode(), staffExceptionMsg.getErrorMessage(),
				staffExceptionMsg.getDeveloperMessage(), null, null);
	}
	
	public ForbiddenException(UserForbiddenExceptionMsg eventExceptionMsg) {
		super(403, eventExceptionMsg.getStatusCode(), eventExceptionMsg.getErrorMessage(),
				eventExceptionMsg.getDeveloperMessage(), null, null);
	}

	public enum StaffExceptionMsg {
		NOT_STAFF_USER("4030101", "Not staff User", "Not staff User"),
		NOT_AUTHORIZE_TO_CREATE_DUPLICATE_EVENT("4030102", "Not authorize to create duplicate event.", "Not authorize to create duplicate event."),
		ALREADY_WHITELABEL_EVENT("4030103", "Already Whitelabel Event, can not move.", "Already Whitelabel Event");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		StaffExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}
	
	public enum UserForbiddenExceptionMsg {
		NOT_EVENT_HOST("4030201", "Not Event Host", "Not Event Host"),
		NOT_WHITELABEL_ADMIN("4030202", "Not WhiteLabel Admin", "Not WhiteLabel Admin"),
		NOT_SUPER_ADMIN("4030203", "Not Super Admin", "Not Super Admin"),
		NOT_AUTHORIZED_TO_DUPLICATE_EVENT("4030204","You must be an Admin of this event to clone it.","You must be an Admin of this event to clone it."),
		NOT_SALES_REP("4030204", "Not Sales Representative", "Not Sales Representative"),
		NOT_SALES_ADMIN("4030205", "Not Sales Admin", "Not Sales Admin"),
		NOT_SUPER_ADMIN_OR_WHITELABEL_ADMIN("4030205","Not Super Admin Or WhiteLabel Admin","Not Super Admin Or WhiteLabel Admin"),
        NOT_SUPER_ADMIN_OR_EVENT_ADMIN("4030206","Not Super Admin Or Event Admin","Not Super Admin Or Event Admin"),
        NOT_CUSTOMER_SUPPORT_LEAD("4030207","Not Customer Support Lead","Not Customer Support Lead"),
        NOT_BILLING_TYPE_ADMIN("4030208","Not Billing Type Admin","Not Billing Type Admin"),
        YOU_ARE_NOT_AUTHORIZED_FOR_SHOW_OR_HIDE_EVENT("4030209","You are not authorized for show or hide event option","You are not authorized for show or hide event option"),
        NOT_AUTHORIZED_TO_DUPLICATE_TEMPLATE("4030210", "You are not authorized to create template for this event.", "You are not authorized to create template for this event."),
        NOT_AUTHORIZED_TO_SPONSORS("4030211","This Sponsors is not found in current Event.","This Sponsors is not found in current Event."),
        NOT_EVENT_STAFF_OR_ADMIN("4030212","Not Event Staff Or Admin","Not Event Staff Or Admin"),

        NOT_EXHIBITOR_LEAD_OR_HIGHER_ROLE("4030213","Your do not have Exhibitor Lead or Above role.","Your do not have Exhibitor Lead or Above role."),
        YOU_ARE_NOT_AUTHORIZED_DISCONNECT_PAYMENT_PROCESSOR("4030214","Only the account admin who connected the payment processing account can disconnect it.","Only the account admin who connected the payment processing account can disconnect it."),
        NOT_EVENT_HOST_OR_STAFF("4030215","Not Event Host Or Staff","Not Event Host Or Staff"),
        NOT_VALID_WHITELABEL_USER("4030216", "Invalid WhiteLabel Role", "Invalid WhiteLabel Role");
		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		UserForbiddenExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}
}
