package com.accelevents.exceptions;

import java.util.Map;

public class ConflictException extends BaseException {

	private static final long serialVersionUID = -6259195997762206266L;

	public ConflictException() {
		this(UserExceptionConflictMsg.USER_ALREADY_EXIST);
	}

	public ConflictException(ConflictException conflictException) {
        super(409, conflictException.getErrorCode(), conflictException.getErrorMessage(),
                conflictException.getDeveloperMessage(), null, null);
    }

	public ConflictException(UserExceptionConflictMsg userExceptionMsg) {
		super(409, userExceptionMsg.getStatusCode(), userExceptionMsg.getErrorMessage(),userExceptionMsg.getDeveloperMessage(), userExceptionMsg.getDefaultMessage(),userExceptionMsg.getDefaultMessageParamMap());
	}
	public ConflictException(TrackingLinkExceptionMsg userExceptionMsg) {
		super(409, userExceptionMsg.getStatusCode(), userExceptionMsg.getErrorMessage(),
				userExceptionMsg.getDeveloperMessage(), null, null);
	}

	public ConflictException(ConflictExceptionMsg userExceptionMsg) {
		super(409, userExceptionMsg.getStatusCode(), userExceptionMsg.getErrorMessage(),
				userExceptionMsg.getDeveloperMessage(), null, null);
	}
    public ConflictException(DisplayViewConflict userExceptionMsg) {
        super(409, userExceptionMsg.getStatusCode(), userExceptionMsg.getErrorMessage(),
                userExceptionMsg.getDeveloperMessage(), null, null);
    }
	public ConflictException(ContactExceptionMsg contactExceptionMsg) {
		super(409, contactExceptionMsg.getStatusCode(), contactExceptionMsg.getErrorMessage(),
				contactExceptionMsg.getDeveloperMessage(), null, null);
	}

    public ConflictException(SurveyExceptionMsg surveyExceptionMsg) {
        super(409, surveyExceptionMsg.getStatusCode(), surveyExceptionMsg.getErrorMessage(),
                surveyExceptionMsg.getDeveloperMessage(), null, null);
    }
    public ConflictException(OfflinePaymentConfig offlinePaymentConfig) {
        super(409, offlinePaymentConfig.getStatusCode(), offlinePaymentConfig.getErrorMessage(),
                offlinePaymentConfig.getDeveloperMessage(), null, null);
    }


    public enum DisplayViewConflict{
        DISPLAY_SMART_CONTACT_LIST_USED("4190001","Smart display view contact List used In Emails Or Notifications","Smart display view contact List used In Emails Or Notifications");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;
        DisplayViewConflict(String statusCode, String errorMessage, String developerMessage)
        {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }
        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
	public enum ConflictExceptionMsg {
		WHITE_LABEL_URL_ALREADY_EXIST(
				"4090001",
				"White Label Url already exist please select another one.",
				"White Label Url already exist please select another one."),
		CARD_ALREADY_LINKED(
				"4090002",
				"This card is already linked to your account.",
				"This card is already linked to your account."),
		ACCESS_CODE_PRESENT(
				"4090003",
				"This access code already present for this event.",
				"This access code already present for this event."),
        INTEREST_ALREADY_EXISTS(
				"4090005",
                "Interest with same name already exists in event, please give a different name",
                "Interest with same name already exists in event, please give a different name"),
        INTEREST_ALREADY_EXISTS_CSV(
                "4090006",
                "Interest with same name already exists in event please check uploaded csv file",
                "Interest with same name already exists in event please check uploaded csv file");

        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ConflictExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}
	}

	public enum UserExceptionConflictMsg {
		USER_ALREADY_EXIST("4090101", "User already exist", "User already exist"),
		INCORRECT_PASSWORD("4090102", "Incorrect password, please try again.", "Incorrect password, please try again."),
		EMAIL_NOT_MATCH_PASSWORD(
				"4090103",
				"Email and password do not match",
				"Email and password do not match"),
		EMPTY_PASSWORD("4090104", "Password is Empty", "Password is Empty"),
		EMPTY_EMAIL("4090105", "Email is Empty", "Email is Empty"),
		EMAIL_NOT_MATCH_WITH_USER(
				"4090106",
				"email address not match with userd id",
				"email address not match with userd id"),
        EMAIL_NOT_MATCH_WITH_USER_ID(
                "4090123",
                "email address not match with userd id [user_id]",
                "email address not match with userd id [user_id]"),
		EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER(
				"4090107",
				"This email is already associated with another phone number",
				"This email is already associated with another phone number"),
		PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL(
				"4090108",
				"This phone number is already associated with another email address",
				"This phone number is already associated with another email address"),
		EMAIL_ALREADY_REGISTERED(
				"4090109",
				"User with email is already registered",
				"User with email is already registered"),
		ALREADY_STAFF(
				"4090110",
				"You have already added this team member.",
				"You have already added this team member."),
		EVENT_STRIPE_ACCOUNT_CONNECT_TOKEN_ALREADY_USED("4090111", "Token is already used", "Token is already used"),
		EVENT_URL_ALREADY_EXIST("4090112", "This URL is already taken. Please try another one.", "This URL is already taken. Please try another one."),
		USER_COUNTRY_ALREADY_PRESENT("4090113", "User country code is different", "User country code is different"),
		USER_BIDDERNUMBER_ALREADY_PRESENT("4090114", "Bidder number %d already registered with this email or phone number.", "Bidder number %d already registered with this email or phone number."),
		NO_CHANGES_MADE("4090115", "No changes were made.", "No changes were made."),
		ORGANIZER_URL_ALREADY_EXIST("4090116", "Organizer URL is already in use.", "Organizer URL is already in use."),
		ORGANIZER_ALREADY_CREATED("4090124", "Organizer already created", "Organizer already created"),
		EVENT_NAME_ALREADY_EXIST("4090117", "Event name is already in use. Please try another name.", "Event name is already in use. Please try another name."),
		PHONE_NUMBER_ALREADY_ATTACH_TO_DIFFERENT_ACCOUNT(
				"4090118",
						"This phone number is already associated with a different account. A number can only be associated with one account.",
						"This phone number is already associated with a different account. A number can only be associated with one account."),
        EVENT_URL_ALREADY_EXIST_AND_SAME_ADMIN("4090119",
            "It looks like you are already using that URL for a different event. If you would like to use that URL for this event then you can access the other event and change the URL in the other event before attempting to change it in this event.",
            "It looks like you are already using that URL for a different event. If you would like to use that URL for this event then you can access the other event and change the URL in the other event before attempting to change it in this event."),
        EMAIL_ALREADY_ASSIGNEDED(
                "4090120",
                "This email address is already assigned. You can not change existing staff with this email address. Please add new staff if you want to add that email as admin/staff.",
                "This email address is already assigned. You can not change existing staff with this email address. Please add new staff if you want to add that email as admin/staff."),
        USER_HAS_ALREADY_LOGGED_IN("4090121","User has already logged in at website, Can't change email now!","User has already logged in at website, Can't change email now!"),
        PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_PARAMETER(
                "4090121",
                        "This phone number [phone_number] is already associated with another email address",
                        "This phone number [phone_number] is already associated with another email address"),
        USER_ALREADY_PRESENT_WITH_COUNTRY_CODE(
                "4090122",
                        "User Already Present with [country_code] as country code",
                        "User Already Present with [country_code] as country code"),
        EMAIL_ALREADY_ATTACH_TO_PHONE_NUMBER_WITH_PARAMETER(
                "4090123",
                "This email is already associated with [phone_number] phone number",
                "This email is already associated with [phone_number] phone number"),
        PHONE_NUMBER_ALREADY_ATTACH_TO_EMAIL_WITH_EMAIL_PARAMETER(
                "4090108",
                "This phone number is already associated with  [email_address] email address",
                "This phone number is already associated with  [email_address] email address"),
        BEE_PAGE_URL_ALREADY_EXIST("4090125", "Page URL is already in use. Please try different URL.", "Page URL is already in use. Please try different URL."),
        BEE_PAGE_NAME_ALREADY_EXIST("4090126", "Page name is already in use. Please try different name.", "Page name is already in use. Please try different name."),
        ZOOM_INTEGRATION_ALREADY_AUTHORIZED("4090127", "Zoom integration already authorized.", "Zoom integration already authorized."),
        ZOOM_INTEGRATION_ALREADY_DISCONNECTED("4090128", "Zoom integration already disconnected.", "Zoom integration already disconnected.");
        private String statusCode;
		private String errorMessage;
		private String developerMessage;
        private String defaultMessage;
        private Map<String,String> defaultMessageParamMap;

		UserExceptionConflictMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

        UserExceptionConflictMsg(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage= defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }

	public enum TrackingLinkExceptionMsg {
		LINK_EXIST_FOR_EVENT("4090201", "This tracking link already exists for this event", "This tracking link already exists for this event"),
		LINK_CAN_NOT_BE_EMPTY("4090202", "Tracking link can not be empty", "Tracking link can not be empty");

		private final String statusCode;
		private final String errorMessage;
		private final String developerMessage;

		TrackingLinkExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}
	public enum ContactExceptionMsg {
		CONTACT_ALREADY_ADDED("4090301", "Contact with this email is already present in system.");

		private final String statusCode;
		private final String errorMessage;
		private final String developerMessage;

		ContactExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

    public enum SurveyExceptionMsg {
        SURVEY_NAME_ALREADY_EXIST("4090401", "Survey name already exist in this event", "Survey name already exist in this event"),
        SURVEY_RESPONSE_ALREADY_SUBMITTED("4090402", "Survey response already submitted.", "Survey response already submitted.");

        private final String statusCode;
        private final String errorMessage;
        private final String developerMessage;

        SurveyExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
        }
    public enum OfflinePaymentConfig {
        OFFLINE_PAYMENT_CONFIG_ALREADY_EXITS("4090401", "Offline payment configuration already exist in this event", "Offline payment configuration already exist in this event");

        private final String statusCode;
        private final String errorMessage;
        private final String developerMessage;

        OfflinePaymentConfig(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


}
