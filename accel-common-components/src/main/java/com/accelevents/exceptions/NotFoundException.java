package com.accelevents.exceptions;


import java.util.Map;

public class NotFoundException extends BaseException {

    private static final long serialVersionUID = 1L;

    public NotFoundException(NotFound notFound) {
        super(404, notFound.getStatusCode(), notFound.getErrorMessage(), notFound.getDeveloperMessage(), notFound.getDefaultMessage(), notFound.getDefaultMessageParamMap());
    }

    public NotFoundException(UserNotFound userNotFound) {
        super(404, userNotFound.getStatusCode(), userNotFound.getErrorMessage(), userNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(EventNotFound eventNotFound) {
        super(404, eventNotFound.getStatusCode(), eventNotFound.getErrorMessage(), eventNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ItemNotFound itemNotFound) {
        super(404, itemNotFound.getStatusCode(), itemNotFound.getErrorMessage(), itemNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(VirtualEventNotFound virtualEventNotFound) {
        super(404, virtualEventNotFound.getStatusCode(), virtualEventNotFound.getErrorMessage(), virtualEventNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(BeeFreeTemplateNotFound beeFreeTemplateNotFound) {
        super(404, beeFreeTemplateNotFound.getStatusCode(), beeFreeTemplateNotFound.getErrorMessage(), beeFreeTemplateNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ModuleNotFound moduleNotFound) {
        super(404, moduleNotFound.getStatusCode(), moduleNotFound.getErrorMessage(),
                moduleNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(TicketingOrderExceptionMsg ticketingNotFound) {
        super(404, ticketingNotFound.getStatusCode(), ticketingNotFound.getErrorMessage(),
                ticketingNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(OrganizerNotFound organizerNotFound) {
        super(404, organizerNotFound.getStatusCode(), organizerNotFound.getErrorMessage(),
                organizerNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(NeonNotFound neonNotFound) {
        super(404, neonNotFound.getStatusCode(), neonNotFound.getErrorMessage(),
                neonNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(SessionSpeakerNotFound sessionSpeaker) {
        super(404, sessionSpeaker.getStatusCode(), sessionSpeaker.getErrorMessage(),
                sessionSpeaker.getDeveloperMessage(), null, null);
    }

    public NotFoundException(NetworkingRulesNotFOUND networkRule) {
        super(404, networkRule.getStatusCode(), networkRule.getErrorMessage(),
                networkRule.getDeveloperMessage(), null, null);
    }

    public NotFoundException(NetworkingMatchNotFOUND networkMatch) {
        super(404, networkMatch.getStatusCode(), networkMatch.getErrorMessage(),
                networkMatch.getDeveloperMessage(), null, null);
    }

    public NotFoundException(SessionNotFound sessionNotFound) {
        super(404, sessionNotFound.getStatusCode(), sessionNotFound.getErrorMessage(), sessionNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(NeptuneDBNotFound neptuneDBNotFound) {
        super(404, neptuneDBNotFound.getStatusCode(), neptuneDBNotFound.getErrorMessage(), neptuneDBNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(EventBillingAddOnsNotFound eventBillingAddOnsNotFound) {
        super(404, eventBillingAddOnsNotFound.getStatusCode(), eventBillingAddOnsNotFound.getErrorMessage(), eventBillingAddOnsNotFound.getErrorMessage(), null, null);
    }

    public NotFoundException(EventChallengeNotFound eventChallengeNotFound) {
        super(404, eventChallengeNotFound.getStatusCode(), eventChallengeNotFound.getErrorMessage(), eventChallengeNotFound.getErrorMessage(), null, null);
    }

    public NotFoundException(CauseAuctionNotFound causeAuctionNotFound) {
        super(404, causeAuctionNotFound.getStatusCode(), causeAuctionNotFound.getErrorMessage(),
                causeAuctionNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(PlansNotConfigured plansNotConfigured) {
        super(404, plansNotConfigured.getStatusCode(), plansNotConfigured.getErrorMessage(), plansNotConfigured.getErrorMessage(), null, null);
    }

    public NotFoundException(ChargebeeCustomerNotFound chargebeeCustomerNotFound) {
        super(404, chargebeeCustomerNotFound.getStatusCode(), chargebeeCustomerNotFound.getErrorMessage(), chargebeeCustomerNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(BillingPlanNotFound billingPlanNotFound) {
        super(404, billingPlanNotFound.getStatusCode(), billingPlanNotFound.getErrorMessage(), billingPlanNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ChargebeeSubscriptionNotFound chargebeeSubscriptionNotFound) {
        super(404, chargebeeSubscriptionNotFound.getStatusCode(), chargebeeSubscriptionNotFound.getErrorMessage(), chargebeeSubscriptionNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ChargebeeInvoiceNotFound chargebeeInvoiceNotFound) {
        super(404, chargebeeInvoiceNotFound.getStatusCode(), chargebeeInvoiceNotFound.getErrorMessage(), chargebeeInvoiceNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(TrayIONotFound trayIONotFound) {
        super(404, trayIONotFound.getStatusCode(), trayIONotFound.getErrorMessage(), trayIONotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(LoungeDataNotFound loungeDataNotFound) {
        super(404, loungeDataNotFound.getStatusCode(), loungeDataNotFound.getErrorMessage(), loungeDataNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ChimeMeetingNotFound chimeMeetingNotFound) {
        super(404, chimeMeetingNotFound.getStatusCode(), chimeMeetingNotFound.getErrorMessage(), chimeMeetingNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(ChatNotFound chatNotFound) {
        super(404, chatNotFound.getStatusCode(), chatNotFound.getErrorMessage(), chatNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(BreakoutRoomNotFound breakoutRoomNotFound) {
        super(404, breakoutRoomNotFound.getStatusCode(), breakoutRoomNotFound.getErrorMessage(), breakoutRoomNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(WhiteLabelNotFound whiteLabelNotFound) {
        super(404, whiteLabelNotFound.getStatusCode(), whiteLabelNotFound.getErrorMessage(), whiteLabelNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(StripeSubscriptionNotFound stripeSubscriptionNotFound) {
        super(404, stripeSubscriptionNotFound.getStatusCode(), stripeSubscriptionNotFound.getErrorMessage(), stripeSubscriptionNotFound.getDeveloperMessage(), null, null);
    }

    public NotFoundException(UserColumnSelectionNotFound columnSelectionNotFound) {
        super(404, columnSelectionNotFound.getStatusCode(), columnSelectionNotFound.getErrorMessage(), columnSelectionNotFound.getErrorMessage(), null, null);
    }

    public NotFoundException(SurveyNotFound surveyNotFound) {
        super(404, surveyNotFound.getStatusCode(), surveyNotFound.errorMessage, surveyNotFound.getDeveloperMessage(), null, null);
    }
    public NotFoundException(ContactsList contactsList) {
        super(404, contactsList.getStatusCode(), contactsList.errorMessage, contactsList.getDeveloperMessage(), null, null);
    }
    public NotFoundException(MobileAppConfiguration mobileAppConfiguration) {
        super(404, mobileAppConfiguration.getStatusCode(), mobileAppConfiguration.errorMessage, mobileAppConfiguration.getDeveloperMessage(), null, null);
    }

    public NotFoundException(EventChallengeTierNotFound eventChallengeTierNotFound) {
        super(404,eventChallengeTierNotFound.getStatusCode(), eventChallengeTierNotFound.errorMessage, eventChallengeTierNotFound.developerMessage, null,null);
    }
    public NotFoundException(SessionLocationNotFound sessionLocationNotFound) {
        super(404,sessionLocationNotFound.getStatusCode(), sessionLocationNotFound.getErrorMessage(), sessionLocationNotFound.getDeveloperMessage(), null,null);
    }
    public NotFoundException(TicketingExchangeRuleExceptionMsg ticketingExchangeRuleExceptionMsg) {
        super(404,ticketingExchangeRuleExceptionMsg.getStatusCode(), ticketingExchangeRuleExceptionMsg.getErrorMessage(), ticketingExchangeRuleExceptionMsg.getDeveloperMessage(), null,null);
    }

    public NotFoundException(EventRequestFormNotFound eventRequestFormNotFound) {
        super(404, eventRequestFormNotFound.getStatusCode(), eventRequestFormNotFound.getErrorMessage(), eventRequestFormNotFound.getDeveloperMessage(), null, null);
    }


    public enum NotFound {
        STRIPE_NOT_FOUND("4040001", "Stripe public key not available", "Stripe public key not available"),
        STAFF_NOT_FOUND("4040002", "Staff detail not found", "Staff detail not found"),
        BID_NOT_FOUND("4040003", "Could not find bid.", "Could not find bid."),
        PLEDGE_NOT_FOUND("4040004", "Could not find pledge.", "Could not find pledge."),
        NOT_VALID_COUNTRY_CODE("4040005", "Not valid country code", "Not valid country code"),
        NOT_VALID_COUPON_CODE("4040006", "Not valid coupon code", "Not valid coupon code"),
        NOT_VALID_USER_PROFILE_FIELD("4040007", "Not valid user profile Field", "Not valid user profile field"),
        RAFFLE_TICKET_COMP_CODE_NOT_FOUND("4040008", "No Comp Code found for Event", "No Comp Code found for Event"),
        NOT_VALID_FACEBOOK_TOKEN("4040009", "Facebook token is not valid", "Facebook token is not valid"),
        PHONE_NOT_FOUND("4040010", "Phone number not found", "Phone number not found"),
        STRIPE_CUSTOMER("4040011", "Stripe customer not found", "Stripe customer not found"),
        PAYMENT_DETAIL("4040012", "payment detail not found", "payment detail not found"),
        TRACKING_URL_NOT_FOUND("4040013", "Tracking url not found", "Tracking url not found"),
        TRANSACTION_CONFIG_NOT_FOUND("4040014", "Transaction conditional logic configuration missing for white label", "Transaction conditional logic configuration missing for white label"),
        WHITE_LABEL_URL_NOT_FOUND("4040015", "White Label URL not found", "White Label URL not found"),
        BIDDER_NOT_FOUND("4040015", "Bidder not found", "Bidder not found"),
        RESET_TOKEN_EXPIRED("4040016", "Password reset link is not valid or expired. Please generate new link from reset password button", "Password reset link is not valid or expired. Please generate new link from reset password button"),
        MESSAGE_TO_CONTACTS_NOT_FOUND("4040017", "Message to contacts not found.", "Message to contacts not found."),
        LOCATION_NOT_FOUND("4040018", "No location present for merchant.", "No location present for merchant."),
        ACCESS_CODE_NOT_FOUND("4040019", "Access code not found.", "Access code not found."),
        NO_CURRENT_EVENT_FOUND("4040020", "No current event found.", "No current event found."),
        CUSTOM_EVENT_EMAIL_NOT_FOUND("4040021", "Custom event email not found.", "Custom event email not found."),
        BID_IS_REFUNDED("4040022", "Bid is already refunded, can not mark as distributed.", "Bid is already refunded, can not mark as distributed."),
        SALES_REPS_URL_NOT_FOUND("4040023", "Sales Representative URL not found", "Sales Representative URL not found"),
        SEATING_CATEGORY_NOT_FOUND("4040024", "Category not found for seating chart", "Category not found for seating chart, it may deleted"),
        RECURRING_EVENT_SCHEDULE_NOT_FOUND("4040025", "Resend event ticket email scheduled not found", "Resend event ticket email scheduled not found"),
        CART_NOT_FOUND("4040026", "Cart not found", "Cart not found"),
        EXHIBITOR_NOT_FOUND("4040027", "Exhibitor not found", "Exhibitor not found"),
        SPONSOR_NOT_FOUND("4040028", "Sponsor not found", "Sponsor not found"),
        ZAPIER_INTEGRATION_NOT_FOUND("4040029", "Zapier Integration not found", "Zapier Integration not found"),
        TICKET_TYPES_NOT_CONFIGURED_FOR_THIS_EVENT("4040030", "Ticketing types are not configured yet.", "Ticketing types are not configured yet."),
        TICKET_TYPE_NOT_FOUND("4040031", "Ticket type not found.", "Ticket type not found."),
        ATTENDEE_LIST_NOT_FOUND("4040032", "Attendee list not found.", "Attendee list not found."),
        EXHIBITOR_PRODUCT_NOT_FOUND("4040033", "Product not found", "Product not found"),
        MEETING_SCHEDULE_NOT_FOUND("4040034", "Meeting schedule not found", "Meeting schedule not found"),
        SSO_CONFIG_NOT_FOUND("4040035", "SSO is not configured for this whitelabel", "SSO is not configured for this whitelabel"),
        PRE_PAYMENT_NOT_FOUND("4040036", "Pre-Payment not found", "Pre-Payment not found"),
        INTEGRATION_NOT_FOUND("4040037", "Integration not found, verify AccelEvents integrations settings", "Integration not found, verify AccelEvents integrations settings"),
        INTEGRATION_TICKET_TYPE_NOT_FOUND("4040038", "Integration ticket type not found, verify AccelEvents integrations settings and select ticket type", "Integration ticket type not found, verify AccelEvents integrations settings and select ticket type"),
        CVENT_ORDER_FOR_CONFIRMATION_NOT_FOUND("4040039", "Accelevent's order not present for confirmation number", "Accelevent's order not present for confirmation number"),
        GAMIFICATION_MASTER_DATA_NOT_FOUND("4040040", "Gamification Master data not found", "Gamification Master data not found"),
        CVENT_API_USER_DETAILS_NOT_FOUND("4040041", "Valid cvent API user details not found", "Valid cvent API user details not found"),
        SUBMITTED_RAFFLE_TICKET_NOT_FOUND("4040042", "Submitted raffle ticket not found", "Submitted raffle ticket not found"),
        EXHIBITOR_DOCUMENT_NOT_FOUND("4040043", "Exhibitor Document not found", "Exhibitor Document not found"),
        TICKETING_MODULE_NOT_ENABLED("4040044", "Ticketing module not enabled For this event", "Ticketing module not enabled For this event"),
        INTEREST_NOT_FOUND("4040045", "Interest not found in this event", "Interest not found in this event"),
        CHALLENGE_CONFIG_NOT_FOUND("4040046", "Challenge config not found", "Challenge config not found"),
        LOBBY_IMAGE_NOT_FOUND("4040047", "Lobby image not found", "Lobby image not found"),
        CHARGEBEE_CHARGE_NOT_FOUND("4040048", "Charge not found in chargebee", "Charge not found in chargebee"),
        SOURCE_NOT_FOUND("4040049", "Source not found", "Pre-Source not found"),
        AUDIENCE_FILTER_NOT_FOUND("4040050", "Audience Filter not found", "Audience Filter not found"),
        TICKETING_SETTING_NOT_FOUND("4040051", "Ticketing setting not found", "Ticketing setting not found"),
        EVENT_DESIGN_SETTING_NOT_FOUND("4040052", "Event design setting not found", "Event design setting not found"),
        NOT_VALID_GOOGLE_TOKEN("4040053", "Google token is not valid", "Google token is not valid"),
        NOT_VALID_LINKEDIN_TOKEN("4040054", "Linkedin token is not valid", "Linkedin token is not valid"),
        NOT_VALID_EMAIL("4040055", "email is not valid", "email is not valid"),
        PARENT_QUESTION_NOT_FOUND("4040056", "Nested Question doesn't have any parent question", "Nested Question doesn't have any parent question"),
        INVALID_NESTED_QUESTION_ID("4040057", "Nested Question id must be greater than 0", "Nested Question id must be greater than 0"),
        WANTS_TO_LEARN_NOT_FOUND("4040058", "Wants to learn not found in this event.", "Wants to learn not found in this event."),
        NOT_VALID_APPLE_TOKEN("4040059", "Apple token is not valid", "Apple token is not valid"),
        VIRTUAL_PORTAL_IMAGE_NOT_FOUND("4040060", "Virtual portal image not found", "Virtual portal image not found"),
        TRAY_INTEGRATION_NOT_FOUND("4040062", "Tray integration not found, verify AccelEvents integrations settings", "Tray integration not found, verify AccelEvents integrations settings"),
        HUBSPOT_COMPANY_NOT_FOUND("4040063", "Hubspot company not found", "Hubspot company not found"),
        NOT_FOUND_BADGES("4040064", "Badge not found", "Badge not found"),
        NOT_FOUND_BADGES_IMAGE("4040065", "Badge image not found", "Badge image not found"),
        CAN_NOT_UPDATE_OR_RESCHEDULE_SENT_REMINDER_EMAIL("4040066", "You can not edit or rescheduled sent reminder email ", "You can not edit or rescheduled sent reminder email"),
        CAN_NOT_DELETE_SENT_ENGAGE_EMAIL("4040067", "You can not delete sent engage email", "You can not delete sent engage email"),
        MUX_ASSET_STATIC_RENDITIONS_NOT_FOUND("4040068", "Mux asset static mp4 renditions not found", "Mux asset static mp4 renditions not found"),
        CAN_NOT_DELETE_SENT_PUSH_NOTIFICATION("4040069", "You can not delete sent push notification", "You can not delete sent push notification"),
        NOT_FOUND_KIOSK_MODE_CHECK_IN_DETAIL("4040069", "Kiosk check-in detail not found", "Kiosk check-in detail not found"),
        ARCHIVE_NOT_FOUND_FOR_EVENT("4040070", "Archives not found for event", "Archives not found for event"),
        MUX_ASSET_DETAIL_NOT_FOUND("4040071", "Mux asset details not found", "Mux asset details not found"),
        HOLDER_ATTRIBUTE_NOT_FOUND("4040072", "Ticket Holder Required Attribute Not Found", "Ticket Holder Required Attribute Not Found"),
        CUSTOM_PROFILE_FIELD_NOT_FOUND("4040073", "Custom profile field not found", "Custom profile field not found"),
        MUX_ASSET_NOT_FOUND("4040074", "Mux asset not found", "Mux asset not found"),
        REGISTRATION_NOT_EXISTS("4040075", "Registration request does not exists", "Registration request does not exists"),
        NOT_FOUND_REGISTRATION_INTRO("4040076", "Registration introductory does not exists", "Registration introductory does not exists"),
        ALREADY_EXIST_REGISTRATION_INTRO("4040077", "Registration introductory already exists", "Registration introductory already exists"),
        REGISTRATION_APPROVAL_EMAIL_TYPE_NOT_EXISTS("4040078", "Registration approval email type does not exists", "Registration approval email type does not exists"),

        EXHIBITOR_STAFF_NOT_FOUND("4040079", "Exhibitor staff not found", "Exhibitor staff not found"),
        INTEGRATION_TRACKING_ACTIVITY_NOT_FOUND("4040080", "Tracking activity not Found", "Tracking activity not Found"),
        REGISTRATION_REQUEST_NOT_APPROVED("4040081", "Registration request does not approved", "Registration request does not approved"),
        REGISTRATION_REQUEST_NOT_AVAILABLE("404096", "This registration request is no longer available", "This registration request is no longer available"),

        NOT_VALID_MICROSOFT_TOKEN("4040082", "Microsoft token is not valid", "Microsoft token is not valid"),

        RESTRICTIONS_DOMAINS_NOT_FOUND("4040083", "Restrictions setting not found.", "Restrictions setting not found."),
        TEMPLATE_NOT_FOUND("4040084", "Template not found.", "Template not found."),

        VIRTUAL_IMAGE_BACKGROUND_SETTING_NOT_FOUND("4040085", "Virtual image background setting not found.", "Virtual image background setting not found."),
        KIOSK_SETTING_NOT_FOUND_FOR_CHECK_IN_TAB("4040086", "Kiosk setting not found for checkIn tab", "Kiosk setting not found for checkIn tab"),
        PASSWORD_NOT_EMPTY("4040087", "The kiosk password is not allowed empty.", "The kiosk password is not allowed empty."),
        ORDER_ID_NOT_VALID("4040088", "The encrypted order ID is not valid.", "The encrypted order ID is not valid."),
        PASSWORD_NOT_MATCH("4040089", "Password not match.", "Password not match."),

        USER_EMAIL_NOT_FOUND("4040090", "User email not found.", "User email not found."),

        NOTIFICATION_DETAILS_NOT_FOUND("4040091","Notification details not found.","Notification details not found."),
        REGISTRATION_ATTRIBUTE_NOT_FOUND("4040072", "Registration attribute not found.", "Registration attribute not found."),
        SPONSORS_SETTING_NOT_FOUND("4040092","Sponsors setting not found.","Sponsors setting not found."),
        MEETING_OPTIONS_NOT_FOUND("4040093","Meeting options not found.","Meeting options not found."),
        SESSION_BOARD_INTEGRATION_NOT_FOUND("4040095", "SessionBoard integration not found.", "SessionBoard integration not found."),
        CONFIRMATION_PAGE_NOT_FOUND("4040096", "Confirmation page not found.", "Confirmation page not found."),
        CUSTOM_EMAIL_NOT_FOUND("4040097", "Custom email not found.", "Custom email not found."),
        CADMIUM_INTEGRATION_NOT_FOUND("4040099", "Cadmium integration not found.", "Cadmium integration not found."),
        DISPLAY_CODE_NOT_FOUND("4040100", "Limited display code not found", "Limited display code not found"),
        TEAM_MEMBER_NOT_FOUND("404081","Team member not found.","Team member not found."),
        CERTIFICATE_NOT_FOUND("404082","Certificate not found." , "Certificate not found."),
        NOT_FOUND_CERTIFICATE_IMAGE("404083", "Certificate image not found.","Certificate image not found." ),
        VIEW_FILTERS_DETAILS_NOT_FOUND("404084","View filter details not found","View filter details not found"),
        DISPLAY_VIEW_NOT_FOUND("404085","Display view not found","Display view not found"),
        ORGANIZER_NOT_ATTACHED_WITH_EVENT("404086","An organizer is required to update these settings. Please add an organizer to the event first.","An organizer is required to update these settings. Please add an organizer to the event first."),
        TRACKS_NOT_FOUND("404087","Tracks not found.","Tracks not found."),
        PAYMENT_GATEWAY_AUTHMODE_NOT_FOUND("404087", "Authentication modes are not found.", "Authentication modes are not found." ),
        EVENT_DIRECTORY_PAGE_NOT_FOUND("404088","Event directory page not found.","Event directory page not found."),
        PASSWORD_REQUIRED_FOR_DISPLAY_VIEW("404089","Password required for display view.","Password required for display view."),
        SHARED_VIEW_DATA_NOT_AVAILABLE("404089","Shared view data not available.","Shared view data not available."),
        FORM_RULES_NOT_FOUND("404090","Form rule not found.","Form rule not found."),
        SSO_MAPPING_NOT_FOUND("404091","SSO mapping is not available for this attribute.","SSO mapping is not available for this attribute."),
        MOBILE_APP_VERSION_CONFIGURATIONS_NOT_FOUND("40409S","Mobile app configurations not found.","Mobile app configurations not found."),
        EVENT_EXHIBITOR_SETTINGS_NOT_FOUND("404092","Event exhibitor settings not found.","Event exhibitor settings not found."),
        PING_SSO_CREDENTIALS_NOT_FOUND("404093","PING SSO credentials not found.","PING SSO credentials not found."),
        PRE_REGISTRATION_ROUTING_RULE_NOT_FOUND("404093","Pre-registration routing rule not found.","Pre-registration routing rule not found."),
        CUSTOM_DESIGN_NOT_FOUND("404094", "Custom design not found.", "Custom design not found."),
        ASSIGNED_CUSTOM_DESIGN_NOT_FOUND("404095", "Assigned Custom design not found.", "Assigned Custom design not found.");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;
        private String defaultMessage;
        private Map<String, String> defaultMessageParamMap;

        private NotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        NotFound(String statusCode, String errorMessage, String developerMessage, String defaultMessage, Map<String, String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage = defaultMessage;
            this.defaultMessageParamMap = defaultMessageParamMap;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }

    public enum CauseAuctionNotFound {
        CAUSEAUCTION_NOT_FOUND("4040043", "CauseAuction Not Found", "CauseAuction Not Found");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        CauseAuctionNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum UserNotFound {
        USER_NOT_FOUND("4040100", "User Not Found", "No User could be found"),
        USER_NOT_FOUND_FOR_STAFF_CHECKOUT(
                "4040101",
                "User Not Found for staff checkout",
                "User Not Found for staff checkout"),
        ACCOUNT_NOT_PRESENT("4040102",
                "An account does not exist with this email address. Please check the email address entered or try a different email address.",
                "An account does not exist with this email address. Please check the email address entered or try a different email address."),
        USER_REQUIRE_TO_UPGRADE_PLAN_FOR_WHITELABEL("4040103",
                "User Not Found, Add user to upgrade plan.",
                "User Not Found, Add user to upgrade plan."),
        USER_REQUIRED_TEXT_TO_GIVE_SUBSCRIPTION_ID("4040104",
                "White Label Admin Required for Text To Give Subscription",
                "White Label Admin Required for Text To Give Subscription"),
        USER__NOT_PRESENT_FOR_EMAIL("4040105",
                "An account does not exist with this ${email} address.",
                "An account does not exist with this ${email} address."),
        USER_ROLE_NOT_FOUND("4040105", "User role not found",
                "User role not found"),
        USER_NOT_FOUND_FOR_KIOSK_CHECK_IN("4040106", "No registration was found for the information you entered.",
                                    "User not found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        UserNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    public enum EventNotFound {
        EVENT_NOT_FOUND("4040200", "Event Not Found", "No Event could be found"),
        WHITE_LABEL_EVENT_NOT_FOUND(
                "4040201",
                "This white label event does not exist",
                "This white label event does not exist"),
        RECURRING_EVENT_SCHEDULE_NOT_FOUND("4040202", "Recurring Event Schedule Not Found", "No Recurring Event Schedule could be found"),
        RECURRING_EVENT_NOT_FOUND("4040203", "Recurring Event Not Found", "No Recurring Event could be found"),
        RECURRING_EVENT_MODIFIED_BY_HOST("4040204", "Event details are modified by host. Please try again.", "Recurring event could be deleted or cancelled."),
        INTEGRATION_TYPE_NOT_FOUND("4040205", "Integration type not found fot this event", "Integration type not found fot this event"),
        EVENT_URL_NOT_FOUND("4040206", "Event Url Not Found", "No Event Url could be found");


        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ItemNotFound {
        ITEM_NOT_FOUND("4040300", "Item Not Found", "No Item could be found"),
        INVALID_ITEM_CODE("4040301", "Invalid Item Code", "Invalid Item Code");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ItemNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


    public enum BeeFreeTemplateNotFound {
        BEE_FREE_TEMPLATE_NOT_FOUND("4040801", "BeeFree template not found.", "BeeFree template not found."),
        BEE_FREE_TEMPLATE_ALREADY_EXIST("4040802", "BeeFree template already generated.", "BeeFree template already generated."),

        BEE_FREE_PAGE_ALREADY_EXIST("4040803", "BeeFree page already exist.", "BeeFree page already exist."),
        BEE_FREE_PAGE_NOT_FOUND("4040804", "Page Not Found. The page you are trying to access does not exist or may have been removed. Please contact the event organizer for assistance.", "Page Not Found. The page you are trying to access does not exist or may have been removed. Please contact the event organizer for assistance");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        BeeFreeTemplateNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum VirtualEventNotFound {
        VIRTUAL_EVENT_SETTINGS_NOT_FOUND("4040901", "Virtual event settings not found.", "Virtual event settings not found."),
        DEFAULT_PLAYBACK_NOT_FOUND_FOR_VIDEO("4040902", "No default playback found for video.", "No default playback found for video.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        VirtualEventNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


    public enum ModuleNotFound {
        MODULE_NOT_FOUND("4040400", "Module Not Found", "Module Not Found"),
        RAFFLE_TICKET_PKG_NOT_FOUND("4040401", "Ticket pkg not found", "Ticket pkg not found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ModuleNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum TicketingOrderExceptionMsg {

        ORDER_NOT_FOUND("4040501", "Order not found", "Order you are looking for is not found"),
        REFUND_ORDER_NOT_PAID("4040502", "Refund order not paid", "Order you like to refund is not paid"),
        ZEROORDERCOUNT("4040503", "order ticket count is zero", "order ticket count is zero"),
        LIMIT_TICKET_AVAILABLE("4040504"),
        TICKET_NOT_FOUND("4040505","Ticket not found","Ticket not found"),
        NOT_VALID_TICKET_TYPE_AVAILABLE("4040506"),
        NUMBER_OF_SEATS_MUST_BE_EQUAL_TO_NUMBER_OF_TICKETS("4040507", "Number of selected seats must be same as number of tickets.", "Number of selected seats must be same as number of tickets."),
        EVENT_TICKETS_NOT_FOUND("4040508", "This user has not bought any tickets yet.", "This user has not bought any tickets yet."),
        CSV_UPLOADED_ORDER_CAN_NOT_BE_REFUNDED("4040509", "Order was uploaded through CSV. can not be refunded", "Order was uploaded through CSV. can not be refunded"),
        CSV_UPLOADED_OR_COMPLIMENTARY_ORDER_NOT_ALLOWED("4040510", "The order is either complimentary or uploaded via CSV, so adding new tickets to this order is not allowed.", "The order is either complimentary or uploaded via CSV, so adding new tickets to this order is not allowed.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        TicketingOrderExceptionMsg(String statusCode) {
            this.statusCode = statusCode;
        }

        TicketingOrderExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

    }


    public enum OrganizerNotFound {
        ORGANIZER_NOT_FOUND("4040600", "Organizer Not Found", "No Organizer could be found"),
        ORGANIZER_URL_NOT_FOUND("4040601", "Organizer URL not found or Its changed.", "Organizer URL not found or Its changed."),
        USER_NOT_ALLOW_TO_UPDATE("4040602", "User not allow to update.", "User not allow to update.This Organizer is not created by this user"),
        USER_ALREADY_PRESENT_IN_TEAM("4040603", "User already present in team"),
        ORGANIZER_URL_CANNOT_BE_EMPTY("4040604", "Organizer url can't be empty", "Organizer url can't be empty"),
        ORGANIZER_NOT_MATCH("4040605", "Organizer Not Match", "This event template is not associated with the passed organizer URL"),
        ORGANIZER_NOT_ASSOCIATED("4040606", "This event is not associated with any organizer, so you can not add a member as an event and organizer admin.", "This event is not associated with any organizer, so you can not add a member as an event and organizer admin."),
        INVOICE_NOT_FOUND("40490208","Invoice not Found","Invoice not Found"),
        PAYMENT_INFO_NOT_FOUND("40490209","Organiser payment information not found","Organiser payment information not found" );

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        OrganizerNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        OrganizerNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum NeonNotFound {
        NEON_NOT_FOUND("4040700", "Neon Configuration Not Found", "Neon Configuration Not Found"),
        NEON_EVENT_NOT_FOUND("4040701", "Neon Event Not Found", "Neon Event Not Found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        NeonNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum SessionSpeakerNotFound {
        //		SESSION_NOT_FOUND("404800", "Session not found!"),
        SPEAKER_NOT_FOUND("404801", "Speaker not found!"),
        TAG_TRACK_NOT_FOUND("404802", "Tag or Track not found!"),
        EVENT_TICKET_NOT_FOUND("404803", "EventTicket Not found!"),
        SESSION_SPEAKER_NOT_FOUND("404804", "Session Speaker not found!"),
        N0_SPEAKER_PROFILE_PRESENT("404805", "No Speaker Profile Present");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SessionSpeakerNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum NetworkingRulesNotFOUND {
        NETWORKING_RULE_NOT_FOUND("404901", "Networking Rules not found!");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        NetworkingRulesNotFOUND(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum NetworkingMatchNotFOUND {
        NETWORKING_MATCH_NOT_FOUND("4041001", "No networking match found!");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        NetworkingMatchNotFOUND(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum SessionNotFound {
        SESSION_ENDED("4042001", "Session is ended!"),
        DEFAULT_PLAYBACK_NOT_FOUND("4042002", "No default playback found for session."),

        SESSION_NOT_FOUND("4042003", "Session not found!"),
        EVENT_PLAN_CONFIG_NOT_FOUND("4042004", "This flag is only applicable for virtual events",
                "Event Plan config details not found for this event"),
        TICKET_TYPE_DOES_NOT_GRANT_ACCESS("4042005", "Your registration type does not grant access to this session"),
        PARAMETER_MISSING("4042006", "Parameter missing","Parameter missing"),
        PLAYBACK_NOT_FOUND("4042007", "No playback found for session.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SessionNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        SessionNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum NeptuneDBNotFound {
        CONNECTION_REQUEST("4043001", "Connection Request Not Found"),
        CONNECTION_ALREADY_ACCEPTED("4043002", "Connection Request Already Accepted"),
        CONNECTION_ALREADY_REJECTED("4043003", "Connection Request Already Rejected");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        NeptuneDBNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        NeptuneDBNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum EventBillingAddOnsNotFound {
        EVENT_BILLING_ADD_ONS_NOT_FOUND("4044001", "Event billing Add Ons not found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventBillingAddOnsNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum EventChallengeNotFound {
        EVENT_CHALLENGE_CONFIG_NOT_FOUND("4044002", "Event Challenge Not Found."),
        EVENT_CRITERIA_CONFIG_NOT_FOUND("4044003", "Event Criteria Not Found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventChallengeNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum EventChallengeTierNotFound {
        EVENT_CHALLENGE_TIER_NOT_FOUND("4044002", "Event Challenge Tier Not Found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventChallengeTierNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


    public enum PlansNotConfigured {
        PLANS_CONFIGURATION_NOT_FOUND("4044003", "Plans Configuration Not Found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        PlansNotConfigured(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ChargebeeCustomerNotFound {
        CHARGEBEE_CUSTOMER_NOT_FOUND("4044004", "Customer Not Found In Chargebee.", "Customer Not Found In Chargebee.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ChargebeeCustomerNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum BillingPlanNotFound {
        BILLING_PLAN_NOT_FOUND("4044104", "No plan configuration found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        BillingPlanNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum StripeSubscriptionNotFound {
        STRIPE_SUBSCRIPTION_NOT_FOUND("40440106", "Subscription Not Found In Stripe.", "Subscription Not Found In Stripe.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        StripeSubscriptionNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ChargebeeSubscriptionNotFound {
        CHARGEBEE_SUBSCRIPTION_NOT_FOUND("40440105", "Subscription Not Found In Chargebee.", "Subscription Not Found In Chargebee.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ChargebeeSubscriptionNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ChargebeeInvoiceNotFound {
        CHARGEBEE_INVOICE_NOT_FOUND("40440105", "Invoice Not Found In Chargebee.", "Invoice Not Found In Chargebee.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ChargebeeInvoiceNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum TrayIONotFound {
        NOT_AUTHORIZED_TO_CREATE("40450105", "Not Authorized To Create Integration", "Not Authorized To Create Integration");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        TrayIONotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum LoungeDataNotFound {
        LOUNGE_DATA_NOT_FOUND("40460105", "Lounge data not found", "Lounge data you are looking for is not found"),
        LOUNGE_SETTING_NOT_FOUND("40460106", "Lounge setting not found", "Lounge setting not found"),
        LOUNGE_TIMESLOT_NOT_FOUND("40460107", "Lounge timeslot not found", "Lounge timeslot not found"),
        LOUNGE_STATUS_NOT_FOUND("40460108", "Lounge status not found", "Lounge status not found"),
        LOUNGE_NOT_FOUND("40460109", "Lounge not found", "Lounge not found");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        LoungeDataNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ChimeMeetingNotFound {
        CHIME_MEETING_NOT_FOUND("40440205", "Meeting not found or meeting expired.", "Meeting not found or meeting expired");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ChimeMeetingNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ChatNotFound {
        LOUNGE_CHAT_NOT_FOUND("40460305", "Lounge chat not found", "Lounge chat you are looking for is not found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ChatNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum BreakoutRoomNotFound {
        SESSION_BREAKOUT_ROOM_NOT_FOUND("40470105", "SessionBreakoutRoom chat not found", "SessionBreakoutRoom chat not found");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        BreakoutRoomNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum WhiteLabelNotFound {
        WHITE_LABEL_NOT_FOUND("********", "WhiteLabel account not found",
                "WhiteLabel account not found"),
        WHITE_LABEL_NOT_MATCH("********", "WhiteLabel Not Match",
                "This event template is not associated with the passed whitelabel URL");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        WhiteLabelNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


    public enum UserColumnSelectionNotFound {

        COLUMN_SELECTION_NOT_FOUND("********", "User column selection not found"),
        COLUMN_MASTER_NOT_FOUND("********", "Column master not found for given area.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        UserColumnSelectionNotFound(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum SurveyNotFound {
        SURVEY_NOT_FOUND("40490106", "Survey not found.", "Survey not found."),
        QUESTION_NOT_FOUND_IN_SURVEY("40490107", "Question not found in survey.", "Question not found in survey."),
        CAN_NOT_DUPLICATE_MORE_THEN_ONE_SURVEY("40490108", "Duplicate functionality is not supported for more then one survey", "Duplicate functionality is not supported for more then one survey");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SurveyNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }
    public enum EventRequestFormNotFound {
        EVENT_REQUEST_FORM_NOT_FOUND("4045001", "Event Request Form not found", "Event Request Form not found"),
        EVENT_REQUEST_FORM_FIELD_NOT_FOUND("4045002", "Form field not found", "Form field not found"),
        EVENT_REQUEST_NOT_FOUND("4045003", "Event request not found.", "Event request not found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventRequestFormNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ContactsList {
        CONTACTS_LIST_NOT_FOUND("40490206", "Contacts list not found.", "Contacts list not found."),
        CONTACT_NOT_FOUND_IN_CONTACTS_LIST("40490207","Contact not found in contacts list.","Contact not found in contacts list.");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ContactsList(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum MobileAppConfiguration {
        MOBILE_EVENT_CONFIGURATION_NOT_FOUND("40490300", "Mobile app configuration not found.", "Mobile app configuration not found."),
        CUSTOM_PAGE_NOT_FOUND("40490301", "Custom page not found.", "Custom page not found."),
        MOBILE_ICON_IMAGE_NOT_FOUND("40490302", "Icon not found.", "Icon not found.");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        MobileAppConfiguration(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum SessionLocationNotFound {
        SESSION_LOCATION_NOT_FOUND("40491101", "Session Location not found!","Session Location not found!");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SessionLocationNotFound(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum TicketingExchangeRuleExceptionMsg{

        TICKET_EXCHANGE_MAPPING_NOT_FOUND("40490301","Ticket exchange mapping not found.","Ticket exchange mapping not found."),
        TICKET_EXCHANGE_FROM_AND_TO_NOT_FOUND("40490302","Ticket Registration type or Exchange for not found.","Ticket Registration type or Exchange for not found."),
        TICKET_EXCHANGE_ALLOWED_ONLY_SAME_OR_HIGHER_PRICE("40490303","Ticket Exchange Allowed Only For Same Or Higher Price Ticket Type.","Ticket Exchange Allowed Only For Same Or Higher Price Ticket Type."),
        TICKET_EXCHANGE_ALLOWED_ONLY_FOR_SAME_FEES_BEHAVIOUR("40490303","Fee handling settings must match: Both tickets must either 'pass fee to buyer' or 'absorb fee'.","Fee handling settings must match: Both tickets must either 'pass fee to buyer' or 'absorb fee'.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        TicketingExchangeRuleExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }
}
