package com.accelevents.exceptions;

import com.accelevents.utils.Constants;

import java.util.Map;

public class NotAcceptableException extends BaseException { //NOSONAR

	private static final long serialVersionUID = 3928190032776894413L;

	public NotAcceptableException(TicketingExceptionMsg ticketingExceptionMsg) {
		super(406, ticketingExceptionMsg.getStatusCode(), ticketingExceptionMsg.getErrorMessage(),
                ticketingExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(VirtualEventExceptionMsg virtualEventExceptionMsg) {
		super(406, virtualEventExceptionMsg.getStatusCode(), virtualEventExceptionMsg.getErrorMessage(),
				virtualEventExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(AttendeeExceptionMsg attendeeExceptionMsg) {
		super(406, attendeeExceptionMsg.getStatusCode(), attendeeExceptionMsg.getErrorMessage(),
				attendeeExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(UserNotificationPreferenceExceptionMsg userNotificationPreferenceExceptionMsg) {
		super(406, userNotificationPreferenceExceptionMsg.getStatusCode(), userNotificationPreferenceExceptionMsg.getErrorMessage(),
				userNotificationPreferenceExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(RecurringExceptionMsg recurringExceptionMsg) {
		super(406, recurringExceptionMsg.getStatusCode(), recurringExceptionMsg.getErrorMessage(),
				recurringExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(TicketHolderAttributesMsg ticketHolderAttributesMsg) {
		super(406, ticketHolderAttributesMsg.getStatusCode(), ticketHolderAttributesMsg.getErrorMessage(),
				ticketHolderAttributesMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(WaitListExceptionMsg waitListExceptionMsg){
		super(406, waitListExceptionMsg.getStatusCode(), waitListExceptionMsg.getErrorMessage(),
                waitListExceptionMsg.getDeveloperMessage(), null, null);
	}

    public NotAcceptableException(RegistrationRequestExceptionMsg registrationRequestExceptionMsg){
        super(406, registrationRequestExceptionMsg.getStatusCode(), registrationRequestExceptionMsg.getErrorMessage(),
                registrationRequestExceptionMsg.getDeveloperMessage(), null, null);
    }

	public NotAcceptableException(ModuleExceptionMsg moduleExceptionMsg) {
		super(406, moduleExceptionMsg.getStatusCode(), moduleExceptionMsg.getErrorMessage(),
				moduleExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(AuctionExceptionMsg auctionExceptionMsg) {
		super(406, auctionExceptionMsg.getStatusCode(), auctionExceptionMsg.getErrorMessage(),
				auctionExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(FundANeedExceptionMsg fundANeedExceptionMsg) {
		super(406, fundANeedExceptionMsg.getStatusCode(), fundANeedExceptionMsg.getErrorMessage(),
				fundANeedExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(RaffleExceptionMsg raffleExceptionMsg) {
		super(406, raffleExceptionMsg.getStatusCode(), raffleExceptionMsg.getErrorMessage(),
				raffleExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(DonationExceptionMsg donationExceptionMsg) {
		super(406, donationExceptionMsg.getStatusCode(), donationExceptionMsg.getErrorMessage(),
				donationExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(PaymentCreationExceptionMsg paymentCreationExceptionMsg) {
		super(406, paymentCreationExceptionMsg.getStatusCode(), paymentCreationExceptionMsg.getErrorMessage(),
				paymentCreationExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(NotAceptableExeceptionMSG notAceptableExeceptionMSG) {
		super(406, notAceptableExeceptionMSG.getStatusCode(), notAceptableExeceptionMSG.getErrorMessage(),
				notAceptableExeceptionMSG.getDeveloperMessage(),notAceptableExeceptionMSG.getDefaultMessage(),notAceptableExeceptionMSG.getDefaultMessageParamMap());
	}

	public NotAcceptableException(ItemExceptionMsg itemExceptionMsg) {
		super(406, itemExceptionMsg.getStatusCode(), itemExceptionMsg.getErrorMessage(),
                itemExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(EventExceptionMsg eventExceptionMsg) {
		super(406, eventExceptionMsg.getStatusCode(), eventExceptionMsg.getErrorMessage(),
				eventExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(ContactExceptionMsg contactExceptionMsg) {
		super(406, contactExceptionMsg.getStatusCode(), contactExceptionMsg.getErrorMessage(),
				contactExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(AnalyticsExceptionMsg analyticsExceptionMsg) {
		super(406, analyticsExceptionMsg.getStatusCode(), analyticsExceptionMsg.getErrorMessage(),
				analyticsExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(Exception exception) {
		super(406, "4060001", exception.getMessage(), exception.getMessage(), null, null);
	}


	public NotAcceptableException(String statusCode, String errorMessage, String developerMessage) {
		super(406, statusCode, errorMessage, developerMessage, null, null);
	}

	public NotAcceptableException(EmbedWidgeSettingExceptionMsg embedWidgeSettingExceptionMsg) {
		super(406, embedWidgeSettingExceptionMsg.getStatusCode(), embedWidgeSettingExceptionMsg.getErrorMessage(), embedWidgeSettingExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(AttendeesSequenceExceptionMsg attendeesSequenceExceptionMsg) {
		super(406, attendeesSequenceExceptionMsg.getStatusCode(), attendeesSequenceExceptionMsg.getErrorMessage(), attendeesSequenceExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(IPLookUpExceptionMsg ipLookUpExceptionMsg) {
		super(406, ipLookUpExceptionMsg.getStatusCode(), ipLookUpExceptionMsg.getErrorMessage(),
				ipLookUpExceptionMsg.getDeveloperMessage(), null, null);
	}
	public NotAcceptableException(CartAbandonmentExceptionMsg cartAbandonmentExceptionMsg) {
		super(406, cartAbandonmentExceptionMsg.getStatusCode(), cartAbandonmentExceptionMsg.getErrorMessage(),
				cartAbandonmentExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(ExhibitorExceptionMsg exhibitorExceptionMsg) {
		super(406, exhibitorExceptionMsg.getStatusCode(), exhibitorExceptionMsg.getErrorMessage(),
				exhibitorExceptionMsg.getDeveloperMessage(), null, null);
	}

    public NotAcceptableException(SponsorsExceptionMsg sponsorsExceptionMsg) {
        super(406, sponsorsExceptionMsg.getStatusCode(), sponsorsExceptionMsg.getErrorMessage(),
                sponsorsExceptionMsg.getDeveloperMessage(), null, null);
    }

	public NotAcceptableException(FeedExceptionMsg feedExceptionMsg) {
		super(406, feedExceptionMsg.getStatusCode(), feedExceptionMsg.getErrorMessage(),
				feedExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(SessionSpeakerExceptionMsg sessionSpeakerExceptionMsg) {
		super(406, sessionSpeakerExceptionMsg.getStatusCode(), sessionSpeakerExceptionMsg.getErrorMessage(),
				sessionSpeakerExceptionMsg.getDeveloperMessage(), sessionSpeakerExceptionMsg.getDefaultMessage(), sessionSpeakerExceptionMsg.getDefaultMessageParamMap());
	}


	public NotAcceptableException(ExpoExceptionMsg expoExceptionMsg) {
		super(406, expoExceptionMsg.getStatusCode(), expoExceptionMsg.getErrorMessage(),
				expoExceptionMsg.getDeveloperMessage(), null, null);
	}


	public NotAcceptableException(NetworkingRulesExceptionMsg networkingRulesExceptionMsg) {
		super(406, networkingRulesExceptionMsg.getStatusCode(), networkingRulesExceptionMsg.getErrorMessage(),
				networkingRulesExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(NetworkingLoungeExceptionMsg networkingLoungeExceptionMsg) {
		super(406, networkingLoungeExceptionMsg.getStatusCode(), networkingLoungeExceptionMsg.getErrorMessage(),
				networkingLoungeExceptionMsg.getDeveloperMessage(), null, null);
	}

	public NotAcceptableException(ExhibitorProductExceptionMsg exhibitorProductExceptionMsg) {
		super(406, exhibitorProductExceptionMsg.getStatusCode(), exhibitorProductExceptionMsg.getErrorMessage(),
				exhibitorProductExceptionMsg.getDeveloperMessage(), null, null);
	}

    public NotAcceptableException(SessionExceptionMsg sessionExceptionMsg) {
        super(406, sessionExceptionMsg.getStatusCode(), sessionExceptionMsg.getErrorMessage(),
                sessionExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(LocationExceptionMsg locationExceptionMsg) {
        super(406, locationExceptionMsg.getStatusCode(), locationExceptionMsg.getErrorMessage(),
                locationExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(WhiteLabelExceptionMsg whiteLabelExceptionMsg) {
        super(406,whiteLabelExceptionMsg.getStatusCode(),whiteLabelExceptionMsg.getErrorMessage(),whiteLabelExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(TrayIOExceptionMsg trayIOExceptionMsg) {
        super(406,trayIOExceptionMsg.getStatusCode(),trayIOExceptionMsg.getErrorMessage(),trayIOExceptionMsg.getDeveloperMessage(), null, null);
    }

	public NotAcceptableException(ChallengeExceptionMsg challengeExceptionMsg) {
		super(406,challengeExceptionMsg.getStatusCode(),challengeExceptionMsg.getErrorMessage(),challengeExceptionMsg.getDeveloperMessage(), null, null);
	}

    public NotAcceptableException(SizeLimitExceptionMsg sizeLimitExceptionMsg) {
        super(406,sizeLimitExceptionMsg.getStatusCode(),sizeLimitExceptionMsg.getErrorMessage(),sizeLimitExceptionMsg.getDeveloperMessage(), null, null);
    }
    public NotAcceptableException(EventChallengeException eventChallengeException) {
        super(406, eventChallengeException.getStatusCode(), eventChallengeException.getErrorMessage(),
                eventChallengeException.getDeveloperMessage(), null, null);
    }


    public NotAcceptableException(EventChallengeTiersException eventChallengeTiersException) {
        super(406, eventChallengeTiersException.getStatusCode(), eventChallengeTiersException.getErrorMessage(),
                eventChallengeTiersException.getDeveloperMessage(), null, null);
    }
    public NotAcceptableException(EventCECriteriaException eventCECriteriaException) {
        super(406, eventCECriteriaException.getStatusCode(), eventCECriteriaException.getErrorMessage(),
                eventCECriteriaException.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(ColumnSelectionException eventChallengeException) {
        super(406, eventChallengeException.getStatusCode(), eventChallengeException.getErrorMessage(),
                eventChallengeException.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(WLSSOConfigurationException wlSsoConfigurationAlreadyExist) {
        super(406, wlSsoConfigurationAlreadyExist.getStatusCode(), wlSsoConfigurationAlreadyExist.getErrorMessage(),
                wlSsoConfigurationAlreadyExist.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(EventRequestFormExceptionMsg eventRequestFormExceptionMsg) {
        super(406, eventRequestFormExceptionMsg.getStatusCode(), eventRequestFormExceptionMsg.getErrorMessage(),
                eventRequestFormExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(SurveyException surveyException) {
        super(406,surveyException.getStatusCode(), surveyException.getErrorMessage(),
                surveyException.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(RecordingExceptionMsg recordingExceptionMsg) {
        super(406,recordingExceptionMsg.getStatusCode(), recordingExceptionMsg.getErrorMessage(),
                recordingExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(BadgesExceptionMsg badgesExceptionMsg) {
        super(406,badgesExceptionMsg.getStatusCode(), badgesExceptionMsg.getErrorMessage(),
                badgesExceptionMsg.getDeveloperMessage(), null, null);
    }
    public NotAcceptableException(TaskExceptionMsg taskExceptionMsg) {
        super(406,taskExceptionMsg.getStatusCode(), taskExceptionMsg.getErrorMessage(),
                taskExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(ReviewerExceptionMsg reviewerExceptionMsg) {
        super(406,reviewerExceptionMsg.getStatusCode(), reviewerExceptionMsg.getErrorMessage(),
                reviewerExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(ReviewProcessExceptionMsg reviewProcessExceptionMsg) {
        super(406,reviewProcessExceptionMsg.getStatusCode(), reviewProcessExceptionMsg.getErrorMessage(),
                reviewProcessExceptionMsg.getDeveloperMessage(), null, null);
    }

    public NotAcceptableException(EntryExitExceptionMsg entryExitExceptionMsg) {
        super(406,entryExitExceptionMsg.getStatusCode(), entryExitExceptionMsg.getErrorMessage(),
                entryExitExceptionMsg.getDeveloperMessage(), null, null);
    }


    public NotAcceptableException(MobileAppConfigurationExceptionMsg mobileAppConfigurationExceptionMsg) {
        super(406,mobileAppConfigurationExceptionMsg.getStatusCode(), mobileAppConfigurationExceptionMsg.getErrorMessage(),
                mobileAppConfigurationExceptionMsg.getDeveloperMessage(), null, null);
    }

    public enum VirtualEventExceptionMsg{

		VIRTUAL_EVENT_ALREADY_CREATED("4070001", "Virtual event settings already created", "Virtual event settings already created"),
		MEETING_SCHEDULE_SLOT_NOT_AVAILABLE("4070002", "Meeting schedule slot not available", "Meeting schedule slot not available"),
		ATTENDEE_AND_EXHIBITOR_ADMIN_ONLY_HAVE_SCHEDULE("4070003", "Each attendee and exhibitor admin can only have a schedule",
				"Each attendee and exhibitor admin can only have a schedule"),
        MEETING_ALREADY_BOOKED("4070004", "Meeting already booked", "Meeting already booked"),
        MEETING_ALREADY_DECLINED("4070005", "Meeting already declined", "Meeting already declined"),
        NOT_A_REJECTED_MEETING("4070006", "This is not a rejected meeting", "This is not a rejected meeting"),
        PUBLISH_EVENT_PROMPT(
                "407007",
                "Please click the Publish button above to proceed.",
                "Please click the Publish button above to proceed."
        ),
        SETTING_NOT_ALLOWED("4070008", "This settings is not allowed for your event plan"),
		WANTS_TO_LEARN_EMPTY_OR_NULL("4070009","Wants to learn must not be null or empty.","Wants to learn must not be null or empty."),
        WANTS_TO_LEARN_ALREADY_EXISTS("4070010","Wants to learn with same name already exists. Please give a different name."),
        TAB_NAME_IS_ALREADY_EXIST("4070012","Tab name already exists or tab name cannot be empty","Tab name already exists or tab name cannot be empty"),
        NAVIGATION_MENU_NOT_AVAILABLE("4070032","Custom navigation menu not allowed in the current plan","Custom navigation menu not allowed in the current plan"),
        VIRTUAL_IMAGE_BACKGROUND_SETTING_ALREADY_EXIST("4070033", "Virtual image background already exist.", "Virtual image background already exist."),
        MEETING_PARTICIPANT_INVALID("4070034","You can not schedule a meeting with the same person.","You can not schedule a meeting with the same person.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		VirtualEventExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
		}

		VirtualEventExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum NotAceptableExeceptionMSG {
		STAFF_NOT_REMOVE_OWN_ACCOUNT(
				"4060002",
				"You cannot delete yourself from the staff list.",
				"You cannot delete yourself from the staff list."),

		CAN_NOT_DELETE_LAST_ADMIN("4060003", "Cannot delete last event admin.", "Cannot delete last event admin."),
		NOT_VALID_PHONE_NUMBER("4060004", "Not a valid phone number", "Not a valid phone number"),
		ONE_MODULE_REQUIRED_TO_ACTIVE(
				"4060005",
				"Please select one module to active",
				"Please select one module to active"),
		COUNTRY_CODE_REQUIRD_TO_ACTIVE_MODULE(
				"4060006",
				"Country code required to active module",
				"Country code required to active module"),
		COUPON_ALREADY_APPLIED_TO_EVENT(
				"4060007",
				"Coupon already applied to this event",
				"Coupon already applied to this event"),
		STRIPE_ACCOUNT_IS_NOT_CONNECTED_TO_EVENT(
				"4060008",
				"Unable to disconnect account",
				"Unable to disconnect account"),
		STRIPE_ACCOUNT_CONNECT_ERROR_IN_CONNECT(
				"4060009",
				"Error during connecting please try after some time!",
				"Error during connecting please try after some time!"),
		URL_NOT_SUPPORT_SPECIAL_CHART(
				"4060010",
				"Special Characters Cannot Be Used in the Event URL.",
				"Special Characters Cannot Be Used in the Event URL."),
		UPLOAD_FILE_HEADER_NOT_CORRECT(
				"********",
				"Upload file headers name are not correct or not in proper sequence",
				"Upload file headers name are not correct or not in proper sequence"),
		URL_NOT_SUPPORTED("********", "Cant create url", "Cant create url"),
		NOT_VALID_EMAIL("********", "Not a well-formed email address", "Not a well-formed email address"),
		CONFIRM_PASSWORD_NOT_MATCH("4060015", "Password does not match the confirm password.", "Password does not match the confirm password."),
		NOT_VALID_PAYMENT("4060016", "Not valid payment type", "Not valid payment type"),
		STRIPE_CUSTOMER_ALREADY_CREATED_CONNECTED_TO_EVENT(
				"4060017",
				"Cannot disconnect, {PAYMENT_GATEWAY} customer already created ",
				"Cannot disconnect, {PAYMENT_GATEWAY} customer already created "),
		CODE_NOT_MATCHED(
				"4060018",
						"Incorrect code ",
						"Incorrect code "),
		WL_FEE_TRANSFERRED_CANNOT_DISCONNECT(
				"4060018",
						"Can not disconnect, White label fee already transferred ",
						"Can not disconnect, White label fee already transferred  "),
		CAN_NOT_UPDATE_TEMPLATE("4060020",
				                "Can not update email template, Please use duplicate email template functionality.",
								"Can not update email template, Please use duplicate email template functionality."),
		CAN_NOT_DUPLICATE_TEMPLATE("4060021",
				"Can not duplicate email template, You can update the current template directly.",
				"Can not duplicate email template, You can update the current template directly."),
		ALREAYD_UNSUBSCRIBED("4060022",
				"You have been already unsubscribed from emails for this event.",
				"You have been already unsubscribed from emails for this event."),
		CONNECTED_TO_OTHER_PAYMENT_GATEWAY("4060023",
				"Event is already connected to other payment gateway.",
				"Event is already connected to other payment gateway."),
		PAYMENT_NOT_CONFIGURED(
				"4060024",
				"Please connect stripe or square or activate ticketing/virtual Events module from billing page to use this functionality.",
				"Please connect stripe or square or activate ticketing/virtual Events module from billing page to use this functionality."),
		MUST_BE_STAFF_TO_CONNECT_SQUARE(
				"4060025",
				"You must be a staff of this event to connect the square account.",
				"Super admin is not authorized to connect the square account, You must be a staff of this event to connect the square account."),
		SQUARE_NOT_CONNECTED(
				"4060024",
				"Please connect square to use this functionality.",
				"Please connect square to use this functionality."),
		PASSWORD_CREATED_SUCCESSFULLY(
				"4060025",
				"You does not seems to have password. Password create link sent successfully, Please check your email.",
				"You does not seems to have password. Password create link sent successfully, Please check your email"),
		CAN_NOT_CHANGE_LAST_ADMIN_ROLE("4060026", "Cannot make last event admin as staff.", "Cannot make last event admin as staff."),
		EVENT_ALREADY_ENDED("4060027","Event already ended","Event already ended"),
		SEAT_SELECTION_NOT_ALLOWED_FOR_FREE_TICKET("4060028","Seat selection not allowed for FREE Tickets"),
		UNABLE_TO_CHANGE_SEAT("4060029","We are facing issue while changing seats. Please contact to system admin."),
		FOUND_UNKNOWN_ORGANIZER(
				"4060030",
				"Can not associated Selected organizer with current event."),
		STRIPE_WEBHOOK_UPDATE_ERROR_NOT_ENABLED_CHARGES(
				"4060031",
				"Charges or payout not enabled."),
		STRIPE_WEBHOOK_UPDATE_ERROR_NOT_ENABLED_TRANSFER(
				"40600031",
				"Transfer not enabled for white label."),
		WHITELABEL_STRIPE_NOT_CONFIGURED(
				"4060033",
				"Please connect stripe for whitelabel event.",
				"Please connect stripe for whitelabel event."),
		FROM_TO_DATE_NOT_NULL("4060032", "From and To dates must not be NULL"),
		CAN_NOT_DISCONNECT_STRIPE("4060034","You are not authorized to disconnect stripe", "You are not authorized to disconnect stripe"),
		AMOUNT_NOT_VALID("4060035", "Amount can not be zero.", "Amount can not be zero."),
		PASSWORD_RESET_CODE_EXPIRE("4060036", "Password reset code is expired", "Password reset code is expired"),
		PASSWORD_RESET_CODE_NOT_MATCH("4060037", "Password Reset code is incorrect. Please enter valid code", "Password Reset code is incorrect. Please enter valid code"),
		EVENT_ALREADY_DELETED("4060038", "Event is already deleted.", "Event is already deleted."),
		NUMBER_OF_TICKETS_ARE_LESS_THEN_SELECTED_TICKETS("4060039", "Please select number of tickets equal or more than ${numberOfTickets} for Ticket type: ${ticketTypeName}.", "Please select equal or more than ${numberOfTickets} for Ticket type: ${ticketTypeName}."),
		SALES_TAX_DETAILS_ALREADY_EXIST("4060040", "Sales tax details already present.", "Sales tax details already present."),
		SALES_TAX_DETAILS_NOT_PRESENT("4060041", "Sales tax details not present.", "Sales tax details not present."),
		CATEGORY_ALREADY_PRESENT("4060042", "category already present.", "category already present."),
		CATEGORY_ALREADY_IN_USE("4060043", "category already in use.", "category already in use."),
        CAN_NOT_CHANGE_LAST_EXHIBITOR_ADMIN_ROLE("4060044", "Cannot make last event exhibitorAdmin as leadRetriever.", "Cannot make last event exhibitorAdmin as leadRetriever."),
		FILE_SIZE_IS_EXCEED_THE_LIMIT("4060045", "Please upload file less than ${sizeLimit} MB of size.", "Please upload file less than ${sizeLimit} MB of size."),
        PURCHASE_EVENT_TICKET_REQUIRED_FOR_ADDONS_TICKET("4060046", "You cannot purchase add-ons without registering.", "You cannot purchase add-ons without registering."),
		ALL_THE_TRANSACTIONIDS_FROM_HAVE_ALREADY_ORDER_CREATED("4060047", "All the Transaction Id have already created order.", "All the Transaction ids have already created order."),
		UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_ATTENDEE_CSV(
				"4060048",
				"Upload file headers name are not correct or not in proper sequence. It should be Transaction Id,First Name,Last Name,Email.",
				"Upload file headers name are not correct or not in proper sequence. It should be Transaction Id,First Name,Last Name,Email."),
        UPLOAD_FILE_HEADER_MISSING_FOR_ATTENDEE_CSV("4060500",
                "CSV is missing required column: ${columnName}",
                "CSV is missing required column: ${columnName}"),
        NOT_VALID_JSON_MAPPING_FORMAT_FOR_ATTENDEE_CSV("4060600",
                "The mapping for the attendee CSV is not in valid JSON format.",
                "The mapping for the attendee CSV is not in valid JSON format."),
		SOME_OF_FIELDS_ARE_EMPTY_IN_ATTENDEE_CSV(
				"4060049",
				"Some of the fields having empty field in upload CSV.",
				"Some of the fields having empty field in upload CSV."),
        EMAIL_FIELD_SHOULD_CONTAIN_SINGLE_VALUE(
                "4060050",
                "Email field should contain single email value.",
                "email field should contain single email value."),
		ORDER_ID_HAVE_MUST_NUMBER_ONLY(
				"4060051",
				"Order Id have must number only.",
				"Order Id have must number only."),
        NUMBER_OF_TICKET_MUST_BE_GREATER_THAN_ZERO(
                "4060052",
                "Number of tickets must be greater than zero for Bundle type ${ticketBundleType}.",
                "Number of tickets must be greater than zero for Bundle type ${ticketBundleType}."),
		VIRTUAL_EVENTS_ARE_NOT_SUPPORT_WITH_RECURRING_EVENT(
				"4060053",
				"Please convert event to non-recurring OR contact to support team to make it non-recurring event.",
				"Please convert event to non-recurring OR contact to support team to make it non-recurring event."),
		COUNTRY_CODE_NOT_VALID(
				"4060054",
				"Country code not valid.",
				"Country code not valid."),
        CAN_NOT_DELETE_ADMIN_CONNECTED_WITH_STRIPE("4060055", "Cannot delete event admin connected with stripe/square.", "Cannot delete event admin connected with stripe/square."),
		TICKETING_NOT_FOUND(
				"4060055",
						"Ticket not found.",
						"Ticket not found."),
        INFO_DESK_ALREADY_EXIST("4060056", "Info desk already present.", "Info desk already present."),
        NOT_SUPPORT_ACCEPT_CASH_CHECK_OR_INVOICE_AS_PRE_PAYMENT_FOR_NOW(
                "4060057",
                "We are only support pre-payment as Cash, Check or Invoice for now.",
                "We are only support pre-payment as check or cash for now."),
        PAYMENT_STATUS_CAN_NOT_BE_NULL(
                "4060058",
                "Payment status can not be null. Either it is PAID or UNPAID.",
                "Payment status can not be null. Either it is PAID or UNPAID."),
		START_END_DATE_CAN_NOT_BE_NULL_WITH_ADD_ONS_TYPE_IS_SUPPORT(
				"4060059",
				"Start and End date&time can not be null when you are adding support person.",
				"Start and End date&time can not be null when you are adding support person."),
		PAYMENT_STATUS_OF_DISCOUNT_CAN_NOT_BE_PAID(
				"4060060",
				"Payment status of Discount can not be paid. It will PAID when we collect final bill.",
				"Payment status of Discount can not be paid. It will PAID when we collect final bill."),
		DISCOUNT_PERCENT_GREATER_100(
				"4060061",
				"Discount percentage should not be greater then 100",
				"Discount percentage should not be greater then 100"),
		DISCOUNT_TYPE_CAN_NOT_BE_EMPTY(
				"4060062",
				"Discount type can not be empty",
				"Discount type can not be empty"),
		CVENT_CONFIRMATION_NUMBER_NOT_PRESENT(
				"4060063",
				"Confirmation Number Not Present",
				"Confirmation Number Not Present"
		),
		CVENT_API_USER_DETAILS_NOT_VALID(
				"4060064",
				"API User details is not valid",
				"API User details is not valid"
		),
        BLOCK_USER(
                "4060065",
                "User is block by Admin . ${userEmail}",
                "User is block by Admin.  ${userEmail}"
        ),
        UNSUCCESSFUL_ENCODING_OF_EMAIL_TEMPLATE_ID(
                "4060067",
                "Unsuccessful encoding of email template id",
                "Unsuccessful encoding of email template id"
        ),
        UNSUCCESSFUL_DECODING_OF_EMAIL_TEMPLATE_ID(
                "4060068",
                "Unsuccessful decoding of email template id",
                "Unsuccessful decoding of email template id"
        ),
        NOT_AUTHORIZED_TO_DELETE_STAFF(
                "4060069",
                "Not authorized to delete the staff",
                "Not authorized to delete the staff"
        ),
        NOT_AUTHORIZED_TO_UPDATE_STAFF(
                "4060070",
                "Not authorized to update the staff",
                "Not authorized to update the staff"
        ),
        NOT_AUTHORIZED_TO_RESEND_STAFF_INVITATION(
                "4060071",
                "Not authorized to resend staff invitation",
                "Not authorized to resend staff invitation"
        ),
        PASSWORD_VALIDATION_FAILED(
                "4060072",
                "Password should contain at least 1 uppercase, 1 lowercase, 1 special character (Valid special characters: !@#$%^&*+-_=), 1 number, no whitespaces and should be at least 8 characters in length",
                "Password should contain at least 1 uppercase, 1 lowercase, 1 special character (Valid special characters: !@#$%^&*+-_=), 1 number, no whitespaces and should be at least 8 characters in length"
        ),
        CHANGE_EVENT_FORMAT_FROM_IN_PERSON_TO_USE_SESSION_AGENDA(
                "4060073",
                "Please change the event format to Either Online Or Hybrid to use Session / Agenda functionality, we can not allow creating sessions in In-Person event.",
                "Please change the event format to Either Online Or Hybrid to use Session / Agenda functionality, we can not allow creating sessions in In-Person event."
        ),
        INVALID_EMAIL(
                "4060074",
                "Email is not valid, please enter valid email.",
                "Email is not valid, please enter valid email."
        ),
        COUNTER_EXCEEDED_CAP_USAGE_ITEMS(
                "4060075",
                "The [plan] plan is limited to [noOfItems] [itemSingular]. Please upgrade your plan to add more [itemPlural].",
                "The [plan] plan is limited to [noOfItems] [itemSingular]. Please upgrade your plan to add more [itemPlural]."
        ),
        PRE_EVENT_ACCESS_IS_LIMITED(
                "4060075",
                "Pre-event access is limited to [hours] hrs prior to the start of the event on the [plan] plan. Please upgrade your plan to add more Pre Event Access Days.",
                "Pre-event access is limited to [hours] hrs prior to the start of the event on the [plan] plan. Please upgrade your plan to add more Pre Event Access Days."
        ),
        VONAGE_ID_NOT_FOUND(
                "4060076",
                "Email is not valid,Please enter valid email.",
                "Email is not valid,Please enter valid email."
        ),
        NOT_AUTHORIZED_TO_ACCESS_SESSION_USER_ANALYTICS(
                "4060077",
                "Admin with the current plan is not authorized to access this functionality. Please upgrade your plan to access.",
                "Admin with the current plan is not authorized to access this functionality. Please upgrade your plan to access."
        ),
        ORGANIZER_NAME_CANNOT_BE_EMPTY(
                "4060078",
                "Organizer name cannot be empty",
                "Organizer name cannot be empty"
        ),
        CANNOT_CHANGE_ORGANIZER_AFTER_EVENT_PUBLISH(
                "4060079",
                "Cannot change organizer after the event is published"
        ),
        INFO_DESK_NOT_FOUND("4060080", "Info desk not found.", "Info desk not found."),

        MAX_ATTENDEE_ALLOWED_LIMIT_REACHED("4060081",
                "The maximum number of attendees for this event has been reached. Please contact the event organizer.",
                "The maximum number of attendees for this event has been reached. Please contact the event organizer."
        ), CAN_NOT_DELETE_OWNER(
                "4060082",
                "You are not authorized to delete owner",
                "You are not authorized to delete owner"
        ), CAN_NOT_DELETE_LAST_OWNER_BILLING_CONTACT(
                "4060082",
                "Cannot delete last owner or billing contact",
                "Cannot delete last owner or billing contact"
        ), CAN_NOT_UPDATE_BILLING_CONTACT(
                "4060083",
                "You are not authorized to update Billing contact",
                "You are not authorized to update Billing contact"
        ),CAN_NOT_MARK_BILLING_CONTACT(
                "4060538",
                "You are not authorized to add team members with Billing contact enabled",
                "You are not authorized to add team members with Billing contact enabled"
        ), CAN_NOT_DELETE_BILLING_CONTACT(
                "4060084",
                "You are not authorized to delete Billing contact",
                "You are not authorized to delete Billing contact"

        ), CAN_NOT_UPDATE_OWNER_OF_THE_ORGANIZER(
                "4060540",
                "You are not authorized to change owner",
                "You are not authorized to change owner"

        ), CAN_NOT_REMOVE_OWNER_FROM_BILLING_CONTACT(
                "4060085",
                "You are not authorized to remove owner from Billing contact",
                "You are not authorized to remove owner from Billing contact"

        ), CAN_NOT_REMOVE_LAST_OWNER_FROM_BILLING_CONTACT(
                "4060085",
                "Cannot remove last owner from Billing contact",
                "Cannot remove last owner from Billing contact"

        ), CAN_NOT_REMOVE_LAST_BILLING_CONTACT(
                "4060539",
                "Cannot remove last Billing contact",
                "Cannot remove last Billing contact"

        ), NOT_ALLOW_TO_EDIT_ORGANIZER_PAGE("4060086",
                "Not authorized for edit organizer page",
                "Not authorized for edit organizer page"
        ), NOT_ALLOW_TO_UPLOAD_ATTENDEES("4060087",
                "Attendees upload not allowed for free plan.",
                "Attendees upload not allowed for free plan."
        ),
        NOT_ALLOW_TO_PAYMENT_PROCESSING("4060089",
                "You must be added as an event Admin to connect a payment processing account.",
                "You must be added as an event Admin to connect a payment processing account."),
        SEATING_CATEGORY_ALREADY_EXIST("4060088", "The seating category name ${category_name} already exists in the event URL ${eventURL}", "The seating category name ${category_name} already exists in the event URL ${eventURL}"),

        CANNOT_USE_RESTRICTED_FEATURE(
                "4060090",
                        "The organizer that this event is associated with is on the {plan_name} plan. You have configured features beyond the limits of this plan. Please upgrade the plan or switch to a different organizer in order to make your event Live.",
                        "The organizer that this event is associated with is on the {plan_name}. You have configured features beyond the limits of this plan. Please upgrade the plan or switch to a different organizer in order to make your event Live."),
        CAN_NOT_UPDATE_SENT_SCHEDULE_PUSH_NOTIFICATION("4060090",
                                        "Unable to update scheduled notification which is already sent.",
                                        "Unable to update scheduled notification which is already sent."),
        NEED_TO_PUBLISH_EVENT("4060091","The [plan] plan is limited to [noOfItems] [itemSingular]. Please publish event to add more [itemPlural]"
                ,"The [plan] plan is limited to [noOfItems] [itemSingular]. Please publish event to add more [itemPlural]"),
        INVALID_SESSION_FORMAT("4060092","Invalid Session Format For Selected Area","Invalid Session Format For Selected Area"),
        SUBSCRIPTION_ID_ALREADY_USED("4060093","Subscription id already used","Subscription id already used"),
        NOT_ALLOW_TO_ADD_INTEREST_TAGS("4060094",
                "Failed to add interest tags, Reason: An event can have a maximum of 500 tags.",
                "Failed to add interest tags, Reason: An event can have a maximum of 500 tags. "
        ),
        CHARGEBEE_CHARGE_CREATION_FAILED(
                "4060133",
                "Payment failed due to ${errorMsg}.",
                "Payment failed due to ${errorMsg}."
        ),
        NOT_ALLOW_TO_SELECT_INTEREST_TAGS("4060095",
                "Not allowed to select more than 10 interest tags.",
                "Not allowed to select more than 10 interest tags."
        ),UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_INTEREST_CSV(
                "4060096",
                "Upload file header name is not correct. It should be Interest.",
                "Upload file header name is not correct. It should be Interest."),
       NOT_ALLOW_TO_ADD_INTEREST_TAGS_ATTENDEE("4060097",
                "You are not allowed to create interest, your selected interest reached the limit.",
                "You are not allowed to create interest, your selected interest reached the limit."
        ),
        DUPLICATE_USER_ENTRY("4060098",
                "Record for ${userEmail} email is already present.",
                "Record for ${userEmail} email is already present."
        ),
        ALREADY_EXISTS_THRESHOLD_AND_OPERATOR("4060099",
                                            "Record for operator ${operator} and threshold ${threshold} already exists.",
                                            "Record for operator ${operator} and threshold ${threshold} already exists."
        ),
        EMAIL_OR_PASSWORD_INVALID(
                "4060100",
                "The email or password doesn't match",
                "The email or password doesn't match"
        ),
        DUPLICATE_SESSION_DETAIL_ID_ENTRY("4060101",
                                     "Duplicate session detail id is not allowed.",
                                     "Duplicate session detail id is not allowed."
        ),
        ORGANIZER_ALREADY_ASSOCIATED_WITH_USER("4060102",
                "Organizer already associated with user.",
                "Organizer already associated with user."
        ),
        ATTENDEE_ANALYTICS_NOT_AVAILBLE_IN_THIS_PLAN("4060103","Attendee analytics not available in this plan","Attendee analytics not available in this plan"),

        MEMBER_CANNOT_SUBSCRIBE_PLAN("4060105","Member are not allowed to subscribe the plan, only Admin/Owner can subscribe to the plan",
                "Member are not allowed to subscribe the plan, only Admin/Owner can subscribe to the plan"),

        THIS_DOWNLOAD_PDF_IS_NOT_ALLOWED_FOR_THIS_ORDER("4060104","Download pdf is not allowed for this order","Download pdf is not allowed for this order"),
        MEMBER_CANNAOT_BE_MARKED_AS_BILLING_CONTACT("4060106","Member can not be marked as billing contact","Member can not be marked as billing contact"),
        BREAKOUT_ROOM_ALREADY_EXIST("4060107", "BreakoutRoom Name ${session_breakout_room_name} is already exist in session ", "BreakoutRoom Name ${session_breakout_room_name} is already exist in session"),
        NOT_ENOUGH_QUANTITY_TO_LINK_WITH_EVENT("4060123","Not enough plan quantity to link","Not enough plan quantity to link"),
        BIRTH_DATE_NOT_ALLOWED_IN_FUTURE("4060124", "Birth date is not allowed in future", "Birth date is not allowed in future"),
        ONLY_ORGANIZER_TEAM_MEMBER_CAN_SUBSCRIBE_PLAN("4060125","Only organizer admin or owner can subscribe to the plan",
                "Only organizer admin or owner can subscribe to the plan"),
        INVOICE_ALREADY_USED("4060126","Provided invoice ID is already used to add plan quantity", "Provided invoice ID is already used to add plan quantity"),
        CANNOT_ASSOCIATE_CHARGE_AFTER_PUBLISH_EVENT("4060127","Cannot associate plan after event publish","Cannot associate plan after event publish"),
        UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_ACCESS_CODE_CSV(
                "4060128",
                "Upload file header is not correct. It should be Access code.",
                "Upload file header is not correct. It should be Access code."),
        UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_COUPON_CODE_CSV(
                "4060129",
                "Upload file header is not correct. It should be Coupon code.",
                "Upload file header is not correct. It should be Coupon code."),
        SUPERADMIN_NOT_DELETED("4060128","Superadmin account cannot be deleted","Superadmin account cannot be deleted"),
        CANNOT_PURCHASE_MORE_THEN_TWENTY("4060129","Max 20 quantity can be purchase at a time",
                "Max 20 quantity can be purchase at a time"),
        DOES_NOT_HAVE_ENOUGH_QUANTITY("4060130","Organizer does not have enough quantity of ${plan_name} plan. Purchase more quantity of ${plan_name} plan to publish the event",
                "Organizer does not have enough quantity of ${plan_name} plan. Purchase more quantity of ${plan_name} plan to publish the event"),
        CANNOT_ASSOCIATE_PLAN_TO_IN_PERSON_EVENT("4060131",
                "This feature is not applicable for in person event","This feature is not applicable for in person event"),
        AGE_IS_NOT_VALID("4060132", "Age is not valid","Age is not valid"),
        PAID_TICKET_PRICE_SHOULD_BE_GREATER_THEN_ZERO("4068934", "Paid Ticket price should be greater then zero"),
        ALREADY_WHITELABEL_ORGANIZER("4030103", "Already Whitelabel organizer, can not move.", "Already Whitelabel organizer"),
        CAN_NOT_CONVERT_RECURRING_EVENT_TO_SINGLE_DAY_EVENT("4060511",
                "Can not convert this recurring event to single day event as its have more then one dates with sold tickets.",
                "Can not convert this recurring event to single day event as its have more then one dates with sold tickets."),
        ALREADY_SINGLE_DAY_EVENT("4060512",
                "This event is already single day event.",
                "This event is already single day event."),
        SOCIAL_MEDIA_URL_IS_INVALID(
                "4060513",
                "social media URL is not valid, please enter the valid social media URL.",
                "social media URL is not valid, please enter the valid social media URL."
        ),
        CONNECTION_DATA_NOT_FOUND(
                "4060514",
                "No connection data found",
                "No connection data found"),
        NOT_AUTHORIZED_TO_CLEAR_CHAT(
                "4060515",
                "Not authorized to clear chat.",
                "Not authorized to clear chat."),
        SUBSCRIPTION_ID_ALREADY_LINKED("4060516",
                "Subscription id already linked with the {account}.",
                "Subscription id already linked with the {account}. "),
        PURCHASE_EVENT_TICKET_REQUIRED_ENABLE_SHOW_REGISTRATION_BUTTON("4060517", "There are currently no tickets available. Please, contact the event organizer.", "There are currently no tickets available. Please, contact the event organizer."),
        NOT_AUTHORIZED_TO_UPDATE_CHANNEL_SETTING(
                "4060518",
                "Not authorized to update channel setting.",
                "Not authorized to update channel setting."),
        NO_UNSUBSCRIBEDUSERS_ARE_PRESENT("4060519","There are no unsubscribed users for this event","There are no unsubscribed users for this event"),
        DOCUMENT_NAME_CANNOT_BE_BLANK("4060520", "Document name can't be blank.", "Document name can't be blank."),
        EVNET_NOT_ASSOCIATED_WITH_THIS_ORGANIZER("4060521","Event is not associated with this organizer","Event is not associated with this organizer"),
        INVALID_DATE(
                "4060522",
                "Date is not valid, please enter valid date.",
                "Date is not valid, please enter valid date."
        ),
        INVALID_LINK("4061001","Sorry, This link is not valid.","Sorry, This link is not valid."),
        TEXT_TO_GIVE_SUBSCRIPTION_NOT_CREATED("4060523", "Text to give subscription does not created", "Text to give subscription does not created"),
        MORE_THEN_ONE_USER_EXIST_WITH_THIS_EMAIL("406","More than one user exist with same email","More than one user exist with same email"),
        CAN_NOT_ACTIVATE_LEGACY_PLAN("4060524", "Can not activate legacy plan", "Can not activate legacy plan"),
        NO_ATTENDEES_ARE_CHECKEDIN("4060525","There are no checked in attendees yet or your event has not been published yet","There are no checked in attendees yet or your event has not been published yet"),
        NOT_AUTHORIZED_TO_DOWNLOAD_ATTENDEELIST(
                "4060526",
                "Not authorized to download list of Attendees.",
                "Not authorized to download list of Attendees."),
        CAN_NOT_ALLOWED_FOR_CONCURRENT_SESSION("406", "Only allowed for concurrent sessions", "Only allowed for concurrent sessions"),
        EMAIL_IS_ALREADY_PRESENT("4060098", "Record for [email_address] email is already present.", "Record for [email_address] email is already present."),
        TOKEN_CANNOT_BE_EMPTY("4060528", "Card token not found","Card token not found"),
        EVENT_CONNECTED_WITH_PAID_ORGANIZER("4060529", "All events organizer should be on Free plan to bulk delete","All events organizer should be on Free plan to bulk delete"),
        EVENT_HAS_MORE_THEN_TWO_ADMIN("4060530", "To delete events in bulk, Events should not have 2+ admins",
                "To delete events in bulk, Events should not have 2+ admins"),
        EVENT_HAS_MORE_THEN_TWO_SESSION("4060531", "To delete events in bulk, Event should not have 2+ sessions",
                "To delete events in bulk, Event should not have 2+ sessions"),
        EVENT_HAS_EXPO_CREATED("4060532", "To delete events in bulk, Event should not have exhibitor booths set up",
                "To delete events in bulk, Event should not have exhibitor booths set up"),
        EVENT_HAS_MORE_THEN_THREE_REG("4060532", "To delete events in bulk, Event should not have 3+ registrations",
                                       "To delete events in bulk, Event should not have have 3+ registrations"),
        NO_USAGE_FOR_ORGANIZER("4060533","There are no usages available for this organizer.","There are no usages available for this organizer."),
        NO_USAGE_FOR_WHITE_LABEL("4060534","There are no usages available for this Whitelabel.","There are no usages available for this Whitelabel."),
        INVALID_SESSION_ACTION("4060535","Invalid Session Action For Selected Area","Invalid Session Action For Selected Area"),
        CAN_NO_ACTIVATE_LEGACY_AFTER_2021("4060536","Cannot Activate Legacy Plan After 2021",
                "Cannot Activate Legacy Plan After 2021"),
        ISSUE_WHILE_LOGGIN_THROUGH_APPLE("4060541","Issue While Logging through Apple",
                "Issue While Logging through Apple"),
        SELECT_ATLEAST_ONE_TICKET("4060537","Please select atleast one ticket.",
                "Please select atleast one ticket."),
        ADMIN_CAN_NOT_DELETE(
                "4060538",
                "Organizer admin can not delete team member",
                "Organizer admin can not delete team member"

        ),
        NOT_ALLOW_TO_SELECT_WANTS_TO_LEARN_TAGS_MORE_THAN_10("4070011","Not allowed to select or add more than 10 wants to learn tags."),
        CANNOT_CHANGE_OVERAGE_CHARGE("4070012","Attendees Overage Charge can be changed only when the plan is activated."),
        CAN_NOT_DISCONNECT_PAYPAL("4070013","You are not authorized to disconnect paypal", "You are not authorized to disconnect paypal"),
        UPLOADED_FILE_SHOULD_HAVE_REQUIRE_HEADER("4070014","Upload file does not have required number of columns","Upload file does not have required number of columns"),
        MANDATORY_FIELD("4070015","${field} should not be null or empty.","${field} should not be null or empty."),
        MISMATCH_VALUE_WITH_FIELD_TYPE("4070016","${field} value should be ${type} type.","${field} value should be ${type} type."),
        API_KEY_NOT_AVAILABLE_WHITELABEL_ORGANIZER("4070017","API key functionality is not available in the organizer area, " +
                "please use the enterprise dashboard for the API key feature",
                "API key functionality is not available in the organizer area, " +
                        "please use the enterprise dashboard for the API key feature"),
        API_KEY_NOT_AVAILABLE_FOR_PLAN("4070018","API key feature is not available under current plan, please upgrade your plan.",
                "API key feature is not available under current plan, please upgrade your plan."),
        PAYMENT_PROCESSOR_ALREADY_CONNECTED("4070019","Payment processor already connected.",
                "Payment processor already connected."),
        END_DATE_MUST_BE_AFTER_THE_START_DATE("4070020","End date must be after the start date.",
                "End date must be after the start date."),
        DUPLICATE_BADGES_NAME("4070021","Badge name should not be duplicate, Please enter unique badge name","Badge name should not be duplicate, Please enter unique badge name"),
        LIVE_STREAM_SETTINGS("4070022","${settingName} setting is not allowed for this ${plan_name} plan. Kindly, upgrade your plan",
                "${settingName} setting is not allowed for this ${plan_name} plan. Kindly, upgrade your plan"),
        NUMBERS_OF_EMAILS_ALLOWED("4070023","Number of emails to send can not be less than 0",
                "Number of emails to send can not be less than 0"),
        DUPLICATE_TICKETING_TYPE_ID("4070024","Ticket type [ticketing_type_name] is already selected in [badge_name] badge","Ticket type [ticketing_type_name] is already selected in [badge_name] badge"),
        CAN_NOT_MOVE_ORGANIZER_TO_WL_ACCOUNT("4070025","Cannot move this organizer to Whitelabel Account",
                "Cannot move this organizer to Whitelabel Account"),
        GAMIFICATION_CHALLENGE("4070026","You have created ${countOfChallenge} gamification challenges under this ${plan_name} plan. Only 1 Standard and 1 Scavenger Hunt challenge are allowed under this plan. Please update your organizer plan or delete extra challenges from gamification.",
                " You have created ${countOfChallenge} gamification challenges under this ${plan_name} plan. Which is not allowed please either update your organizer plan or delete challenge from gamification"),
        ADDED_CUSTOM_TAB("4070027"," You have added custom tab which is not allowed. Kindly delete this extra added custom tab or update your organizer plan",
                "You have added custom tab which is not allowed. Kindly delete this extra added custom tab or update your organizer plan"),
        ADDED_NAVIGATION_MENU("4070028"," You have added navigation menu tab which is not allowed. Kindly delete this extra added navigation menu tab or update your organizer plan",
                                 "You have added navigation menu tab which is not allowed. Kindly delete this extra added navigation menu tab or update your organizer plan"),
        ASSOCIATE_ORGANIZER_OR_WHITELABEL("4070029","Please associate an organizer or whitelabel with this event.",
                "Please associate an organizer or whitelabel with this event."),
        DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_EVENT_START("4070030","The Event Format cannot be changed when the event is open.",
                "The Event Format cannot be changed when the event is open."),
        DOES_NOT_ALLOWED_TO_CHANGE_EVENT_FORMAT_AFTER_PRE_EVENT_START("4070031","The Event Format cannot be changed when the event has entered the Pre-Event Access period.",
                "The Event Format cannot be changed when the event has entered the Pre-Event Access period."),
        AGE_IS_NOT_VALID_CUSTOM("4060134", "Age value should be number type.","Age value should be number type."),
        PAYFLOW_CONFIG_NOT_FOUND("4060135","Pay Flow configurations not found","Pay Flow configurations not found"),
        VAT_TAX_DETAILS_ALREADY_EXIST("4060136", "Vat tax details already present.", "Vat tax details already present."),
        VAT_TAX_DETAILS_NOT_PRESENT("4060137", "Vat tax details not present.", "Vat tax details not present."),
        VAT_TAX_CANT_DISABLE("4060138","VAT Tax toggle can't turn off after first sale","VAT Tax toggle can't turn off after first sale"),
        VAT_TAX_CANT_ENABLE("4060139","VAT Tax toggle can't turn on after first sale","VAT Tax toggle can't turn on after first sale"),
        EVENT_IS_NOT_CONNECTED_WITH_PAYMENT("4060140", "Event is not connected with payment processor.", "Event is not connected with payment processor."),
        PAYFLOW_CONNECTION_FAIL("4060141", "There was an issue with the Payflow credentials you entered. Here is the response we received from the Payflow system - {message}", "There was an issue with the Payflow credentials you entered. Here is the response we received from the Payflow system - {message}"),
        CUSTOM_CSS_ALREADY_EXIST("4060142", "Custom CSS already exist with same target {$target}","Custom CSS already exist with same target {$target}"),
        CUSTOM_CSS_NOT_FOUND("4060143", "Custom CSS not found","Custom CSS not found"),
        CUSTOM_CSS_FILE_TYPE_NOT_SUPPORTED("4060144", "Custom CSS file must have .css extension","Custom CSS file must have .css extension"),
        CUSTOM_CSS_FILE_MUST_NOT_BE_NULL("4060145", "Custom CSS file must not be null","Custom CSS file must not be null"),
        ALREADY_UNSUBSCRIBED("4060146","You have already unsubscribed plan","You have already unsubscribed plan"),
        SAME_CARD_DETAIL_IS_NOT_ALLOWED("4060147", "Same card detail is not allowed.", "Same card detail is not allowed."),
        WL_CONNECTED_TO_OTHER_PAYMENT_GATEWAY("4060148",
                "White label is already connected to other payment gateway.",
                "White label is already connected to other payment gateway."),
        TICKET_DOES_NOT_ALLOW_LOUNGES("4060149","Your registration does not allow access to Lounges","Your registration does not allow access to Lounges"),
        TICKET_DOES_NOT_ALLOW_EXPO("4060150","Your registration does not allow access to Exhibitor Booths","Your registration does not allow access to Exhibitor Booths"),
        ALREADY_ADDED_KIOSK_DETAIL("4060148","You have already added kiosk mode check-in detail","You have already added kiosk mode check-in detail"),
        FEATURE_NOT_AVAILABLE_FOR_FREE_PLAN("4060149","Kindly upgrade your plan to use this feature","Kindly upgrade your plan to use this feature"),
        API_MAX_RATE_LIMIT_REACHED("4060151","API maximum rate limit reached","API maximum rate limit reached"),
        CANNOT_DELETE_ORGANIZER_WITH_PLAN_OR_INTEGRATION("4060152","Can not delete organizer with activated plan or integrations","Can not delete organizer with activated plan or integrations"),
        SQUARE_PAYMENT_IS_NOT_SUPPORT_FOR_ENTERPRISE_PLAN ("4060153","We do not support connecting of Square payment gateway for white label events","We do not support connecting of Square payment gateway for white label events"),
        NOT_ALLOWED_TO_ADD_MORE_THAN_ONE_CUSTOM_FIELD ("4060154","Not allowed to add more than one custom field on an event","Not Allowed to add more than one custom field on an event"),
        NOT_ALLOWED_TO_ASSIGNED_ATTRIBUTE_MORE_THAN_ONE_CUSTOM_FIELD ("4060155","This Holder Attribute is already mapped to another custom field","This Holder Attribute is already mapped to another custom field"),
        THIS_DOWNLOAD_INVOICE_PDF_IS_NOT_ALLOWED_FOR_THIS_ORDER("4060156","Download invoice pdf is not allowed for this order","Download invoice pdf is not allowed for this order"),
        EVENT_DOES_NOT_BELONG_TO_WHITELABEL("4060157", "This event does not belong to current domain.", "This event does not belong to current domain."),
        MAGIC_LINK_EMAIL_DOES_NOT_SENT_TO_GIVEN_EMAIL(
                "4060158",
                        "Magic link email does not sent for listed emails.",
                        "Magic link email does not sent for listed emails."),
        NOT_EVENT_HOST("4060159","Not Event Host","Not Event Host"),
        ORGANIZER_DOES_NOT_BELONG_TO_WHITELABEL("4060160", "This organizer does not belong to current domain.", "This organizer does not belong to current domain."),
        EXPO_MANAGE_TAB_ALREADY_EXISTS("4060161","Tab name already exists","Tab name already exists"),
        NUMBER_OF_TICKETS_ARE_MORE_THEN_SELECTED_TICKETS("4060162", "Please select number of tickets equal or less than ${numberOfTickets} for Ticket type: ${ticketTypeName}.",
                "Please select equal or less than ${numberOfTickets} for Ticket type: ${ticketTypeName}."),
        USER_ALREADY_REGISTER_WITH_ANOTHER_TICKET("4060163", "User already register in session with different ticket", "User already register in session with different ticket"),
        USER_ALREADY_CHECK_IN_SESSION("4060164", "Attendee already checked in for this session.", "Attendee already checked in for this session."),
        USER_ALREADY_CHECK_IN_WITH_ANOTHER_TICKET("4060168", "User already checked in session with different ticket", "User already checked in session with different ticket"),
        ATTENDEE_TICKET_NOT_GRANT_ACCESS_TO_THIS_SESSION("4060165","The tickets held by this attendee do not grant access to this session","The tickets held by this attendee do not grant access to this session"),
        CANNOT_ENABLE_INTEREST(
                "4060166",
                "Please add interest tags first",
                "Please add interest tags first"),
        REGISTRATION_REQUEST_ALREADY_EXISTS("4060167", "Registration request already exists", "Registration request already exists"),
        CANNOT_PROCEED_ORDER_FOR_UNPUBLISH_EVENT("4060168", "Order can not proceed for unpublished event", "Order can not proceed for unpublished event"),
        CANNOT_ALLOWED_ENTER_FOR_UNPUBLISH_EVENT("4060169", "Not allowed to enter virtual event hub for unpublished event", "Not allowed to enter virtual event hub for unpublished event"),
        CANNOT_ALLOWED_CHECK_IN_FOR_UNPUBLISH_EVENT("4060169", "Check-in is not allowed for unpublished event", "Check-in is not allowed for unpublished event"),
        INVALID_FILE_TYPE("4060171", "Invalid file type, please upload valid file.","Invalid file type, please upload valid file."),
        USER_REGISTERED_IN_SESSION_NOT_ALLOWED_CHANGE_HOLDER_EMAIL("4060172", "Not Allowed to change holder email because user already check-in/register in to session. Please unregister attendee from all session.", "Not Allowed to change holder email because user already check-in/register in to session.  Please unregister attendee from all session."),
        EMAIL_NOT_VALID("4060173", "This is not valid email address."),
        ERROR_WHILE_PARSING_BEEFREE_TOKEN("4060174", "Error while parsing beeFree token."),
        ISSUE_WHILE_LOGGIN_THROUGH_MICROSOFT("4060175","Issue While Logging through Microsoft", "Issue While Logging through Microsoft"),
        ERROR_CONTACT_MESSAGE_SEND_EMAIL("4060176", "Email delivery failed. Please contact support."),
        HIDDEN_ATTRIBUTE_CANNOT_SET_REQUIRED("4060177", "The hidden questions cannot be set as required.", "The hidden questions cannot be set as required."),

        RESTRICTION_DOMAINS_LIST_CAN_NOT_EMPTY("4060178","Restrictions domains list can not be empty.","Restrictions domains list can not be empty."),

        RESTRICTION_DOMAINS_SETTINGS_ALREADY_EXISTS("4060179","Restrictions domain setting already exists.","Restrictions domain setting already exists."),

        REGISTRATION_IS_NOT_PERMITTED("4060180","Registration is not permitted with your email address.","Registration is not permitted with your email address."),

		PARTIAL_PAYMENT_NOT_PERMITTED("4060181","Partial payment is permitted only with unpaid or pay latter order","Partial payment is permitted only with unpaid or pay latter order."),

		PARTIAL_PAYMENT_OVER_DUE("4060182","you have to pay only ${amount} amount","you have to pay only ${amount} amount"),

		PARTIAL_PAYMENT_ALREADY_DONE("4060183","you have already payed ${amount} amount","you have to pay only ${amount} amount"),
        NOT_ALLOWED_TO_UPLOAD_ATTENDEE_FOR_PAY_LATER("4060184","Pay Later is enabled for this ticket type, please disable pay later to upload attendees.","Pay Later is enabled for this ticket type, please disable pay later to upload attendees."),
        BADGE_NOT_AVALABLE_FOR_PLAN("4060185", "Badges are not available for this plan.", "Badges are not available for this plan."),
        API_ACCESS_NOT_AVALABLE_FOR_PLAN("4060185", "API Access is not available for this plan.", "API Access is not available for this plan."),
        ADVANCED_EMAIL_BUILDER_NOT_AVAILABLE_FOR_PLAN("4060186", "Advanced Email Builder is not available for this plan.", "Advanced Email Builder is not available for this plan."),
        KIOSK_CONFIG_NOT_ALLOWED_FOR_VIRTUAL_FORMAT("4060188", "Kiosk settings not allowed for virtual event format.", "Kiosk settings not allowed for virtual event format."),
        CHECKOUT_CUSTOM_DISCLAIMER_LIMIT("4060189", "The character limit for the checkout disclaimer is 65535 characters.", "The character limit for the checkout disclaimer is 65635 characters."),
        NETWORKING_LOUNGE_DOCUMENT_NAME_NOT_FOUND("4060190", "${documentType} not found.", "{documentType} not found."),
        YOU_CAN_NOT_DELETE_NETWORKING_DOCUMENT("4060191", "You can delete only own networking lounge ${documentType}.", "You can delete only own networking lounge ${documentType}."),
        YOU_CAN_NOT_EDIT_NETWORKING_DOCUMENT("4060192", "You can edit only own networking lounge ${documentType}.", "You can edit only own networking lounge ${documentType}."),

        TWO_FACTOR_CODE_EXPIRE_OR_INVALID("4060193", "This code is invalid or expired. Please double-check and try again.", "This code is invalid or expired. Please double-check and try again."),
        TWO_FACTOR_CODE_NOT_MATCH("4060193", "Two factor code is incorrect. Please enter valid code", "Two factor code is incorrect. Please enter valid code"),
        EVENT_LENGTH_IS_EXCEED_FOR_IN_PERSON_EVENT("4060075","${eventDays} days event length is allowed for In-Person event.","${eventDays} days event length is allowed for In-Person event." ),
        TEMPLTE_NAME_ALREADY_EXIST("4060194","Template name already exist.","Template name already exist." ),

        PRIVATE_EVENT_PASSWORD_INVALID("4060195", "Invalid Password.", "Invalid Password."),
        STARTTIME_OR_ENDTIME_IS_NOT_IN_VALID_FORMAT("4060196", "Please send ${date} value in 'yyyy/MM/dd HH:mm:ss' format.", "Please send ${date} value in 'yyyy/MM/dd HH:mm:ss' format."),
        CURRENT_PASSWORD_NOT_MATCH("4060197", "The current password you provided does not match. Please ensure that you have entered the correct password.", "The current password you provided does not match. Please ensure that you have entered the correct password."),
        CSV_FORMAT_NOT_VALID("4060198","CSV format not Valid.","CSV format not Valid."),
        BEE_FREE_PAGE_DELETE_NOT_ALLOWED("4060199","Not allowed to remove bee free page.","Not allowed to remove bee free page."),
        DUPLICATE_HOLDER_EMAIL_NOT_ALLOWED("4060200","Each registrant must have a different email address.","Each registrant must have a different email address."),
        SCHED_EVENT_DETAILS_NOT_VALID("4060210", "Sched integration details is not valid", "Sched integration details is not valid"),
        USER_ALREADY_CHECK_IN_SESSION_WITH_TICKET("4060164", "Attendee already checked in with this ticket for this session.", "Attendee already checked in with this ticket for this session."),
        BEE_PAGE_URL_NOT_SUPPORT_SPECIAL_CHARACTER("4060211","Special characters can not be used in the page url.", "Special characters can not be used in the page url."),
        BEE_FREE_PAGE_URL_NOT_BLANK("4060212","Page URL cannot be blank.","Page URL cannot be blank."),

        NOT_ALLOWED_TO_CREATE_RTMP_KEY_FOR_FREE_PLAN("4060213","Please upgrade your plan to use Accelevents RTMP and Accelevents Studio.","Please upgrade your plan to use Accelevents RTMP and Accelevents Studio."),
        SCHED_SYNC_PROCESS_ALREADY_STARTED("4060214","Sched sync process already running.","Sched sync process already running."),
        NOT_ALLOW_FOR_PAID_TICKETS("4060215","Not allowed for paid tickets.","Not allowed for paid tickets."),

        SEATING_FEATURE_NOT_ALLOWED_ON_FREE_PLAN("4060216", "Please upgrade your plan to use assigned seating for free ticket types.", "Please upgrade your plan to use assigned seating for free ticket types."),
        SEATING_FEATURE_NOT_ALLOWED_FOR_TICKET("4060217", "Assigned seating feature is not allowed with this ticket type.", "Assigned seating feature is not allowed with this ticket type."),
        SEATING_FEATURE_NOT_ALLOWED_ON_VIRTUAL_EVENT("4060218", "Assigned seating feature is not allowed for virtual event or virtual ticket type.", "Assigned seating feature is not allowed for virtual event and virtual ticket type."),
        SEATING_FEATURE_NOT_ALLOWED_TO_DISABLE("4060219","Assigned seating is not allowed to disable, Please first delete all the orders of this ticket type.","Assigned seating feature is not allowed to disable, Please first delete all the orders of this ticket type"),
        DATE_FORMAT_NOT_VALID("4060220", "Date format not valid.", "Date format not valid."),
        DATES_NOT_BETWEEN_EVENT("4060221","Start and end date time should be after pre-event start date time and before the event end date time.","Start and end date time should be after event start date time and before the event end date time."),
        EVENT_END_DATE_OUTSIDE_THE_SUBSCRIPTION_EXPIRE_DATE("4060221","Event end date outside the subscription expire date" ,"Event end date outside the subscription expire date"),
        END_TIME_SHOULD_BE_GREATER_THAN_START_TIME("4060222","Session end time should be greater than start time.","Session end time should be greater than start time."),
        START_TIME_SHOULD_BE_LESS_THAN_START_TIME("4060223","Session start time should be less than end time.","Session start time should be less than end time."),
        MAIN_STAGE_SESSIONS_CAN_NOT_BE_OVERLAP_WHILE_BULK_UPDATE("4060224","Please ensure that main stage session times do not overlap.","Please ensure that main stage session times do not overlap."),
        PURCHASE_EVENT_ADD_TICKET_NOT_MAPPED_WITH_SELECTED_TICKETS("40600225", "The selected tickets are not properly associated with the assigned addons in your addons ticket.", "The selected tickets are not properly associated with the assigned addons in your addons ticket."),
        UP_SELL_CONFIGURATION_NOT_FOUND_BY_MODULE("4060226", "Upsell configuration not found for module ${moduleName}.", "Upsell configuration not found for module ${moduleName}"),
        SESSION_BOARD_EVENT_ID_NOT_VALID("4060227", "Event ID ${eventId} is not found in SessionBoard organizer.", "Event ID ${eventId} is not found in SessionBoard organizer."),
        SESSION_BOARD_EVENT_DETAILS_NOT_VALID("4060228", "SessionBoard integration details is not valid", "SessionBoard integration details is not valid"),
        CADMIUM_EVENT_DETAILS_NOT_VALID("4060238", "Cadmium integration details is not valid.", "Cadmium integration details is not valid."),
        CADMIUM_EVENT_ID_NOT_VALID("4060239", "Event ID ${eventId} is not found in Cadmium.", "Event ID ${eventId} is not found in Cadmium."),
        SESSION_BOARD_SYNC_PROCESS_ALREADY_STARTED("4060229","SessionBoard sync process already running.","SessionBoard sync process already running."),
        EVENTS_DATES_ARE_OUTSIDE_THE_SUBSCRIPTION_EXPIRE_DATE("4060230","This event’s dates are after the end of your Accelevents plan. Please renew your plan to publish this event." ,"This event’s start date is after the end of your Accelevents plan. Please renew your plan to publish this event."),
        ZOOM_ACCOUNT_CONNECT_ERROR_IN_CONNECT(
                "4060231",
                "Error during connecting zoom, please try after some time!",
                "Error during connecting zoom, please try after some time!"),
        CAN_NOT_DISCONNECT_ZOOM("4060232","You are not authorized to disconnect zoom", "You are not authorized to disconnect zoom"),
        ZOOM_ACCOUNT_REVOKE_ERROR(
                "4060233",
                "Error during disconnecting zoom, please try after some time!",
                "Error during disconnecting zoom, please try after some time!"),
        DEFAULT_CONFIRMATION_PAGE_ALREADY_EXISTS("4060234","Default Confirmation page already exist." ,"Default Confirmation page already exist"),
        CONFIRMATION_PAGE_NAME_ALREADY_EXISTS("4060235","Confirmation page already exists with this name.","Confirmation page already exists with this name."),
        CONFIRMATION_PAGE_URL_REQUIRED("4060235","Confirmation page URL required.","Confirmation page URL required."),
        CONFIRMATION_PAGE_URL_ALREADY_EXISTS("4060236","Confirmation page URL already exists.","Confirmation page URL already exists."),
        DEFAULT_CONFIRMATION_PAGE_CANNOT_BE_DELETED("4060237","Default Confirmation page cannot be deleted.","Default Confirmation page cannot be deleted."),
        NOT_WHITELABEL_ORGANIZER("4060236","Not Whitelabel Organizer","Not Whitelabel Organizer"),
        CAN_NOT_MOVE_ORGANIZER_FROM_WL_ACCOUNT("4060237","Cannot move this organizer from Whitelabel Account","Cannot move this organizer from Whitelabel Account"),
        ORGANIZER_ALREADY_CONNECTED_WITH_SAME_WHITELABEL("4060238","Organizer already connected with same Whitelabel","Organizer already connected with same Whitelabel"),
        CADMIUM_SYNC_PROCESS_ALREADY_STARTED("4060240","Cadmium sync process already running.","Cadmium sync process already running."),
        NOT_AUTHORIZED_TO_CREATE_EVENT("4060241","Not authorized to create events under this organizer or whitelabel.","Not authorized to create events under this organizer or whitelabel."),
        DEFAULT_CUSTOM_EMAIL_ALREADY_EXISTS("4060242","Default Custom email already exist." ,"Default Custom email already exist."),
        CONFIRMATION_EMAIL_NAME_ALREADY_EXISTS("4060243","Confirmation email already exists with this name.","Confirmation email already exists with this name."),
        DEFAULT_CONFIRMATION_EMAIL_CANNOT_BE_DELETED("4060244","Default Confirmation email cannot be deleted.","Default Confirmation email cannot be deleted."),
        PROVIDED_HOLDER_EMAIL_ALREADY_HAS_TICKET("4060245","Email is already attached with another ticket, Each registrant must have a different email address.","Email is already attached with another ticket, Each registrant must have a different email address."),
        UPLOAD_FILE_HEADER_NOT_CORRECT_FOR_DISPLAY_CODE_CSV(
                "4060246",
                "Upload file header is not correct. It should be Limited display code.",
                "Upload file header is not correct. It should be Limited display code."),
        NO_REGISTRANTS_REGISTER_YET("4060246","There are no any registrants register yet or your event has not been published yet","There are no any registrants register yet or your event has not been published yet"),
        CUSTOM_EMAIL_NAME_OR_BODY_TEXT_NOT_BE_NULL("4060247","Custom email name or body text should not be null or empty.","Custom email name or body text should not be null or empty."),
        ATTENDEE_IMPORT_LIMIT_LESS_THAN_CURRENT("4060248", "Please add the attendee import limit more then imported attendee ${importedAttendee}","Please add the attendee import limit more then imported attendee ${importedAttendee}"),
        SEATS_ARE_NOT_LABELED("4060249", "Please add the label on the seats from the Venue Designer.","Please add the label on the seats from the Venue Designer."),
        DUPLICATE_CERTIFICATE_NAME("4060251","Certificate name should not be duplicate, Please enter unique certificate name.","Certificate name should not be duplicate, Please enter unique certificate name." ),
        INVALID_PROFILE_IMAGE_URL_HEADER_POSITION("4060253","Profile_Image_Url column header must be on 5th position in the upload CSV file.","Profile_Image_Url column header must be on 5th position in the upload CSV file." ),
        KIOSK_UNABLE_TO_ON_UPLOAD_PROFILE_PICTURE("4060254","Please enable Information Confirmation to allow the uploading of profile picture.","Please enable Information Confirmation to allow the uploading of profile picture."),
        TICKET_ORDER_PAYMENT_IN_PROCESSING_STATE("4060253","A payment is currently processing, please try again later.","A payment is currently processing, please try again later."),
        SPEAKER_REGISTRATION_APPROVAL_NOT_ENABLED("4060255","Please enable Speaker Registration Approval to create a confirmation page for speaker approval.","Please enable Speaker Registration Approval to create a confirmation page for speaker approval."),
        EXPO_REGISTRATION_APPROVAL_NOT_ENABLED("4060256","Please enable Expo Registration Approval to create a confirmation page for expo approval.","Please enable Expo Registration Approval to create a confirmation page for expo approval."),
        DISPLAY_VIEW_ALREADY_ADDED("4060257","Display view already added for this user.","Display view already added for this user."),
        TWO_FACTOR_ENTITLEMENT_IS_NOT_AVAILABLE("4060258","2-Factor Entitlement is not available for this enterprise","2-Factor Entitlement is not available for this enterprise"),
        DISPLAY_VIEW_NOT_ADDED("4060259","Display view not added for this user.","Display view not added for this user."),
        DISPLAY_VIEW_NAME_ALREADY_EXISTS("4060260","The view name you entered is already in use. Please choose a unique name for the view.","The view name you entered is already in use. Please choose a unique name for the view."),
        SMART_CONTACT_LIST_NAME_ALREADY_EXIST("4060261","A smart contact list with the same name already exists. Please provide a different name.","A smart contact list with the same name already exists. Please provide a different name."),
        CURRENT_PLAN_NOT_ALLOWED_ACCESS_FEATURE("4060262","This feature is not available on your current plan. Please upgrade plan to access it.", "This feature is not available on your current plan. Please upgrade plan to access it."),
        EVENT_DIRECTORY_PAGE_ALREADY_EXIST("4060263","Event directory page is already exist.","Event directory page is already exist."),
        STAFF_CAN_NOT_CHANGE_ROLE_IT_SELF("4060264", "You can not change your role it self", "You can not change your role it self"),
        NOT_PART_OF_THE_EVENT_ADMIN("4060265", "You are not part of the event admin list", "You are not part of the event admin list"),
        INVALID_URL("4060266", "Sorry, This URL is not valid.", "Sorry, This URL is not valid."),
        NOT_ALLOW_TO_DELETE_ATTRIBUTE("4060267","You are not allowed to delete the attribute as this is used in form logic.", "You are not allowed to delete the attribute as this is used in form logic"),
        ATTRIBUTE_IS_REQUIRED("4060268","{attribute} is required.", "{attribute} is required." ),
        ATTRIBUTE_IS_HIDDEN("4060269","{attribute} is hidden.","{attribute} is hidden." ),
        SSO_MAPPING_ALREADY_EXISTS("4060269","SSO mapping already exists for this attribute.","SSO mapping already exists for this attribute"),
        CALENDAR_INVITE_CONFIG_SAVE_FAILED("4060270", "Unable to save calendar invite configuration.", "Unable to save calendar invite configuration."),
        ATTRIBUTE_LINKED_TO_REVIEW_PROCESS("4060271","This attribute is currently used in a review process and cannot be deleted or modified in its field type.","This attribute is currently used in a review process and cannot be deleted or modified in its field type."),
        BLOCKED_USER_ADMIN_MSG(
                "4060272",
                "This person has been blocked from this event.",
                "This user is blocked. Please unblock this user first!"),
        ROUTING_URL_ALREADY_EXISTS("4060273", "Routing URL already exists.", "Routing URL already exists."),
        CARD_DETAILS_NOT_PRESENT("4060274", "Card details is not present for this request.", "Card details is not present for this request"),
        CUSTOM_DESIGN_NAME_ALREADY_EXISTS("4060276","Custom design already exists with this name.","Custom design already exists with this name."),
        DEFAULT_CUSTOM_DESIGN_CANNOT_BE_DELETED("4060277","Default Custom design cannot be deleted.","Default Custom design cannot be deleted."),
        ADD_ON_NOT_MAPPED_CORRECTLY_TO_HOLDER_TICKET("4060278","Add-on is not mapped correctly to the holder ticket.","Add-on is not mapped correctly to the holder ticket."),
        SALESFORCE_CONFIGURATION_MUST_BE_ENABLED("4060279","Salesforce configuration must be enabled to use SSO Mapping.","Salesforce configuration must be enabled to use SSO Mapping."),
		PIN_REQUIRED_TO_ENABLE_PIN("4060280", "PIN is required when PIN is enabled.", "PIN is required when PIN is enabled."),
		INVALID_ENTRY_EXIT_PIN_LENGTH("4060281", "Invalid PIN. It should contain exactly 4 digits.", "Invalid PIN. It should contain exactly 4 digits."),
		INVALID_ENTRY_EXIT_PIN("4060282", "Invalid PIN. It must contain exactly 4 numeric digits.", "Invalid PIN. It must contain exactly 4 numeric digits."),
		ENABLE_PIN_REQUIRED_TO_SET_PIN("4060283", "To set a PIN, please enable the PIN option first.", "To set a PIN, please enable the PIN option first."),
		ENTRY_ERROR_MESSAGE_IS_REQUIRED("4060284", "Entry error message cannot be empty.", "Entry error message cannot be empty."),
		EXIT_ERROR_MESSAGE_IS_REQUIRED("4060285", "Exit error message cannot be empty.", "Exit error message cannot be empty."),
        YOU_MUST_UNPUBLISH_EVENT_BEFORE_DELETE_IT("4060286","You must unpublish this event before you can delete it.","You must unpublish this event before you can delete it."),
        MAX_SESSION_LIMIT_REACHED("4060287","MAX 100 sessions are allowed to import via CSV.","MAX 100 sessions are allowed to import via CSV."),
        YOU_CANT_PERFORM_ACTION_AFTER_EVENT_POST_ACCESS_EXCEED(
				"4060287",
                        "You can not perform this action after event post time access exceed.",
                        "You can not perform this action after event post time access exceed."),
        FIELD_LENGTH_SHOULD_BE_LESS_THAN("4060288","${field} length should be less than ${characterCount} characters.","${field} length should be less than ${characterCount} characters."),
        UPLOAD_FILE_CUSTOM_HEADER_NOT_CORRECT(
                "4060289",
                "The following headers in the uploaded file are invalid: ${headers}",
                "The following headers in the uploaded file are invalid: ${headers}"
        ),
        ROUTING_URL_NOT_VALID("4060290", "The provided routing URL is invalid.", "The routing URL must be a valid http or https link."),
        DUPLICATE_BUYER_EMAIL_NOT_ALLOWED("4060291","This buyer already has an order and cannot purchase another one.","This buyer already has an order and cannot purchase another one."),
        NOT_VALID_ROUTING_URL_METHOD("4060292", "One routing method must be enabled: either SSO authentication or CRM lookup.", "One routing method must be enabled: either SSO authentication or CRM lookup."),
        NOT_VALID_ROUTING_REQUEST_FORMAT("4060293", "Invalid request payload: must provide either SalesforceCRMAttributes or PingFederateAttributes.", "Invalid request payload: must provide either SalesforceCRMAttributes or PingFederateAttributes.");


        private String statusCode;
		private String errorMessage;
		private String developerMessage;
		private String defaultMessage;
		private Map<String,String> defaultMessageParamMap;

		NotAceptableExeceptionMSG(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}


		NotAceptableExeceptionMSG(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

        NotAceptableExeceptionMSG(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage=defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String,String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String,String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }

    }

	public enum RecurringExceptionMsg{
		NOT_VALID_SCHEDULED_DAY("4060501", "Scheduled day is not valid"),
		MONTH_SHOULD_NOT_BE_ZERO("4060502", "Month should not be less than 1"),
		PARSING_ERRO_WITH_DATE_OR_TIME("4060503", "Parsing error with date or time"),
		DEFAULT_MSG("4060505", "msg"),
		CHECKIN_NOT_ALLOWED_BEFORE_TIME("4060506", "Please wait until the event start time to check in.\""),
		NOT_ACCEPT_MORE_THAN_100_EVENTS("4060507", "Maximum limit of creating recurring event is 100 at a time."),
		SET_START_END_DATE_BEETWEEN_SOLD_RECURRING_DATE("4060508", "Attendees have already registered for a session ${position} ${new_occurs_date_time}. " +
				"In order to changing the Occurs ${untilFrom} date a custom event date will be created for the dates that have registrations. " +
				" Would you like to create these custom sessions and change the Occurs ${untilFrom} date?"),
		ALL_DATES_ARE_PAST("4060509", "Please create new dates to update, As its have only past dates."),
                RECURRING_EVENT_ID_NOT_EMPTY("4060510", "Recurring event id can not null for recurring event"),
		USE_RECURRING_EVENT_API("4060511", "Use Recurring event API");

        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		RecurringExceptionMsg(String statusCode) {
			this.statusCode = statusCode;
		}

		RecurringExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}
	}

	public enum AuctionExceptionMsg {
		AUCTION_NOT_ACTIVE(
				"4060101",
				"Please activate this module to start accepting bids.",
				"Please activate this module to start accepting bids."),
		ITEM_ALREADY_PAID("4060102", "Item {ITEM_NAME} already has been paid", "Item {ITEM_NAME} already has been paid"),
		CONTACT_HOST_TO_ENABLE_PAYMENT(
				"4060103",
				"Please Contact Host To Enable Payment Service",
				"Please Contact Host To Enable Payment Service"),
		BID_SHOULD_BE_GREATER_THAN_STARTING_BID(
				"4060104",
				"Item bid should be greater than starting bid",
				"Item bid should be greater than starting bid"),
		BID_SHOULD_BE_GREATER_THAN_CURRENT_BID(
				"4060105",
				"Item bid should be greater than current bid",
				"Item bid should be greater than current bid"),
		MINIMUM_BID_INCREMENT_FOR_THIS_ITEM("4060106","The minimum bid increment for this item is {bidIncrement}. Please bid at least {minimumBid}.",
								"The minimum bid increment for this item is {bidIncrement}. Please bid at least {minimumBid}."),
		CAN_NOT_DELET_ALREADT_PAID(
				"4060107",
				"You cannot delete bid which have already been paid.",
				"You cannot delete bid which have already been paid."),
		AUCTION_IS_EXPIRE("4060108", "This Auction is Over!", "Auction is Expire"),
		ITEM_WITH_BIDS("4060109", "Cannot delete an item with bids", "Cannot delete an item with bids"),
		ITEM_ALREADY_CONFIRMED("4060110", "Item already has been confirmed", "Item already has been confirmed"),
		NOT_SMS_BID("4060111", "bid is not sms bid", "bid is not sms bid"),
		RESET_REQUIRED("4060112", "This auction has already ended. You must reset this auction before changing the ending time. Resettings this auction will delete all user activity and it will NOT be recoverable.", "You cannot set an end time that has already occurred, please try again."),
		ITEM_ALREADY_PURCHASED("4060113",
								"Sorry, this item has already been purchased.",
							    "Sorry, this item has already been purchased."),
		ONLY_PAID_BID_CAN_BE_REFUNDED("4060114",
				"Sorry, only paid bid can be refunded.",
				"Sorry, only paid bid can be refunded."),
		AUCTION_REFUND_FAILED("4060115", "Unable to refund the auction bid.", "Unable to refund the auction bid."),
		BID_ALREADY_REFUNDED("4060116", "Bid is already refunded.", "Bid is already refunded."),
		BIDDING_HAS_ENDED_FOR_ITEM_WITH_ITEMCODE("406117","Bidding has ended for item {item_short_name}.","Bidding has ended for item {item_short_name}."),
		USER_CONFLICT("4060118", "Item purchased by different User.", "Item purchased by different User."),
		BUY_IT_NOW_PRICE_NOT_CHANGE("4060119", "You cannot change the buy it now price for this item since it has already received a bid at the buy it now price.", "You cannot change the buy it now price for this item since it has already received a bid at the buy it now price."),
		STARTING_BID_NOT_CHANGE("4060120", "You cannot change the starting bid for this item since it has already received a bid at the buy it now price.", "You cannot change the starting bid for this item since it has already received a bid at the buy it now price."),
		NOT_A_WINNING_BID("4060121", "This is not a winning bid.", "This is not a winning bid."),
		FUNCTIONALITY_NOT_ACTIVE(
				"4060122",
				"Please activate silent auction module to use this functionality.",
				"Please activate silent auction module to use this functionality."),
        CANNOT_MARK_AS_DISTRIBUTED("4060123","Can't mark all item as distributed because amount is not paid","Can't mark all item as distributed because amount is not paid"),
        CANNOT_MARK_ITEM_AS_DISTRIBUTED("4060124","Can't mark item as distributed because amount is not paid","Can't mark item as distributed because amount is not paid"),
        BUY_IT_NOW_SHOULD_BE_GRATER("4060125","Buy it now price must be greater than Starting Bid","Buy it now price must be greater than Starting Bid");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		AuctionExceptionMsg(String statusCode) {
			this.statusCode = statusCode;
		}

		AuctionExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}
	}

	public enum FundANeedExceptionMsg {
		PLEDGE_SHOULD_GREATER_EQUAL(
				"4060201",
				"pledge amount should be greater than or equal to the stated pledge amount.",
				"pledge amount should be greater than or equal to the stated pledge amount."),
		PLEDGE_NOT_ACTIVE(
				"4060202",
				"Please activate this module to start accepting pledges.",
				"Please activate this module to start accepting pledges."),
		FUND_A_NEED_IS_EXPIRE("4060203", "This fund a need is over!", "Fund a need is Expire"),
		RESET_REQUIRED("4060204", "This fund a need has already ended. You must reset this fund a need before changing the ending time. Resettings this fund a need will delete all user activity and it will NOT be recoverable.", "You cannot set an end time that has already occurred, please try again."),
		ONLY_PAID_PLEDGE_CAN_BE_REFUNDED("4060205",
				"Sorry, only paid pledge can be refunded.",
				"Sorry, only paid pledge can be refunded."),
		PLEDGE_REFUND_FAILED("4060206", "Unable to refund the pledge.", "Unable to refund the pledge."),
		PLEDGE_ALREADY_REFUNDED("4060207", "Pledge is already refunded.", "Pledge is already refunded."),
		FUNCTIONALITY_NOT_ACTIVE(
				"4060208",
				"Please activate fund a need module to use this functionality.",
				"Please activate fund a need module to use this functionality."),
        STARTING_BID_AT_LEAST_ONE("4060209", "Starting bid needs to be set to at least 1", "Starting bid needs to be set to at least 1");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		FundANeedExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum RaffleExceptionMsg {
		RAFFLE_NOT_ACTIVE(
				"4060301",
				"Please activate this module to start accepting tickets.",
				"Please activate this module to start accepting tickets."),
		INVALID_COMPLIMENTARY_CODE("4060302", "Invalid Complimentary Code", "Invalid Complimentary Code"),
		TICKET_SHOULD_BE_MORE_THAN_ZERO("4060303", "Tickets should be more than 0.", "Tickets should be more than 0."),
		DO_NOT_HAVE_ENOUGH_TICKET(
				"4060304",
				"You do not have enough tickets, please purchase additional tickets below.",
				"You do not have enough tickets, please purchase additional tickets below."),
		EMPTY_RAFFLE_TICKET("4060305", "Unable to process null Raffle Ticket", "Unable to process null Raffle Ticket"),
		RAFFLE_IS_EXPIRE("4060306", "This Raffle is Over!", "Raffle is Expire"),
		ITEM_WITH_SUBMITTED_TICKETS(
				"4060307",
				"Cannot delete an item for which tickets has been submitted",
				"Cannot delete an item for which tickets has been submitted"),
		RAFFLE_TICKETS_ARE_SOLD_OUT("4060308", "Raffle tickets are sold out!", "Raffle tickets are sold out!"),
		LIMITTED_TICKET("4060309", "There are only %d remaining tickets available. Your maximum purchase must be  %d tickets or less.", "There are only %d remaining tickets available. Your maximum purchase must be  %d tickets or less."),
		RESET_REQUIRED("4060310", "This raffle has already ended. You must reset this raffle before changing the ending time. Resettings this raffle will delete all user activity and it will NOT be recoverable.", "You cannot set an end time that has already occurred, please try again."),
		RAFFLE_ALREADY_REFUNDED(
				"4060311",
						"Purchased raffle already refunded.",
						"Purchased raffle already refunded."),
		PURCHASED_RAFFLE_TICKET_NOT_FOUND(
				"4060311",
						"Purchased raffle ticket not found.",
						"Purchased raffle ticket not found."),
		FUNCTIONALITY_NOT_ACTIVE(
				"4060312",
				"Please activate raffle module to use this functionality.",
				"Please activate raffle module to use this functionality."),
        RAFFLE_TICKET_PRICE_INVALID(
                "4060313",
                "Raffle ticket price must be at least $1.",
                "Raffle ticket price must be at least $1."),
        RAFFLE_AVAILABLE_TICKETS_INVALID(
                "4060314",
                        "Available Tickets must be greater or equal to 0",
                        "Available Tickets must be greater or equal to 0");
		private String statusCode;
		private String errorMessage;
		private String developerMessage;
        private String defaultMessage;
        private Map<String,String> defaultMessageParamMap;

		RaffleExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

        RaffleExceptionMsg(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage= defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }

	public enum TicketingExceptionMsg {
		TICKET_ALREADY_PURCHASED(
				"4060401",
				"Tickets have already been purchased for this ticket type. If you do not wish to sell any more of this ticket type then please click the \"Hide this ticket type\" button.",
				"Tickets have already been purchased for this ticket type. If you do not wish to sell any more of this ticket type then please click the \"Hide this ticket type\" button."),
		REFUND_AMOUNT_SHOULD_NOT_GREATER_THEN_PAID(
				"4060402",
				"The refund amount must be less than the paid amount.",
				"The refund amount must be less than the paid amount."),
		TICKET_TYPE_NOT_EXIST(
				"4060403",
				"Ticket type you are looking for not exist",
				"Ticket type you are looking for not exist"),
		TICKET_PURCHASER_NOT_EXIST(
				"4060404",
				"Ticket purchaser not found in request",
				"Ticket purchaser not found in request"),
		BARCODE_NOT_EXIST("4060405", "Barcode not exist", "Barcode not exist"),
		BARCODE_ALREADY_CHECKED_IN("4060406", "Check-in failed. Ticket already used.", "Check-in failed. Ticket already used."),
		BARCODE_ALREADY_BOOK_STATUS("4060407", "Ticket already have book status", "Ticket already have book status"),
		BARCODE_ALREADY_REFUNDED_AND_CANCELED("4060408", "Check-in failed. This registration was canceled.", "Checked-in failed. Ticket was refunded and canceled."),
		CHECK_IN_FAILED("4060408", "Checked-in failed.", "Checked-in failed."),
		TICKET_REFUND_QTY_SHOULD_BE_GREATER_THEN_ZERO(
				"4060409",
				"Ticket refund quantity should be greater then zero",
				"Ticket refund quantity should be greater then zero"),
		TICKET_COUPON_ALREADY_EXIST("4060410", "Ticketing coupon already exist", "Ticketing coupon already exist"),
		TICKET_COUPON_NOT_FOUND("4060411", "The discount code you entered is not valid for this event.", "The discount code you entered is not valid for this event."),
		TICKET_COUPON_USED("4060412", "A discount code cannot be deleted once it has been used.", "A discount code cannot be deleted once it has been used."),
		MORE_TICKET_COUPON_USED("4060413", "More coupon is used then set", "More coupon is used then set"),
		TICKET_COUPON_PERCENT_GREATER_100(
				"4060414",
				"Ticketing coupon percentage should not be greater then 100",
				"Ticketing coupon percentage should not be greater then 100"),
		APPLIED_COUPON_NOT_FOUND("4060415", "Applied coupon not found", "Applied coupon not found"),
		COUPON_CODE_IS_NOT_APPLICABLE_FOR_TICKET_TYPE("4060416", "This coupon is not valid for this Registration type.", "This coupon is not valid for this Registration type."),
		COUPON_IS_NOT_AVAILABLE("4060417", "Coupon is not available", "Coupon is not available"),
		COUPON_CODE_EXPIRED("4060418", "This coupon has expired.", "This coupon has expired."),
		THIS_DISCOUNT_CODE_HAS_ALREADY_BEEN_APPLIED_TO_YOUR_TRANSACTION(
				"4060419",
				"This discount code has already been applied to your transaction.",
				"This discount code has already been applied to your transaction."),
		TICKETS_ARE_NOT_CURRENTLY_FOR_SALE_FOR_THIS_EVENT(
				"4060420",
				"Tickets are not currently for sale for this event.",
				"Tickets are not currently for sale for this event."),
		COUPON_IS_NOT_APPLIED_TO_THIS_ORDER(
				"4060421",
				"Coupon is not applied to this order",
				"Coupon is not applied to this order"),
		PAYMENT_METHOD_REQUIRED("4060422", "Payment method is required.", "Payment method is required."),
		TICKET_STATUS_NOT_EXIST("4060423", "Ticket status not exist", "Ticket status not exist"),
		ACTIVE_TO_START(
				"4060424",
				"Please activate Event Ticketing to start selling tickets.",
				"Please activate Event Ticketing to start selling tickets."),
		CHECK_OUT_TIME_EXPIRE("4060425", "You have not completed checkout in the allotted time. Please click here to restart the checkout process.", "You have not completed checkout in the allotted time. Please click here to restart the checkout process."),
		ALREADY_PAID_ORDER("4060426", "Order is already paid", "Order is already paid"),
		COUPON_REACHED_MAX_USAGE("4060427", "This coupon has reached max number of uses.", "This coupon has reached max number of uses."),
		COUPON_REACHED_MAX_USAGE_PER_USER("4060428", "You have already reached the maximum number of uses for this coupon.", "You have already reached the maximum number of uses for this coupon."),
		COUPON_CODE_NOT_APPLICABLE_ON_DONATION_TICKETING_TYPE("4060428", "Discount coupon can not be applied on donation ticket type.", "Discount coupon can not be applied on donation ticket type."),
		TICKET_CAN_NOT_DELETED_REFUND_FAILED("4060429", "Ticket can not be deleted because refund of ticket failed.", "Ticket can not be deleted because refund of ticket failed."),
		BUNDLE_TYPE_CAN_NOT_BE_CHANGED("4060430", "This setting cannot be edited once you have a sold a ticket from this ticket type.", "Can not change bundle type of ticketing type after tickets purchased for it."),
		NUMBER_OF_TICKETS_CAN_NOT_BE_LESS_THAN_ONE("4060431", "Minimum one ticket is required for bundle type.", "Minimum one ticket is required for bundle type."),
		SEATING_FLAG_CHANGE_NOT_ALLOWED("4060432", "Seating flag update is not allowed.",
									"Customer already created for seating type ticketing, So updating the seating flag is not allowed."),
		ASSOCIATION_NOT_ALLOWED("4060433",
				"Only %d number of ticket are available for association under this category.",
				"Only %d number of ticket are available for association under this category."),
		CAN_NOT_DECREASE_TOTAL_TICKET_COUNT("4060434", "Can not decreased the total ticket count. Please deassociate some tickets from category %d .",
													"Can not decreased the total ticket count. Please deassociate some tickets from category %d ."),
		ORDER_NOTE_INVALID_SIZE("4060435", "Note length can not be more than 250.", "Note length can not be more than 250."),
		NUMBER_OF_TICKETS_FOR_BUNDLE_TYPE_CAN_NOT_BE_CHANGED("4060436","The number of tickets per {bundle_type} cannot be changed after an order is filled.","The number of tickets per {bundle_type} cannot be changed after an order is filled."),
		MANDATORY_PURCHASER_FIELDS("4060437","{purchaser_info} is not present in ticket holder json.","{purchaser_info} is not present in ticket holder json."),
		CAN_NOT_DECRESS_NUMBER_OF_TICKETS("4060438","Ticket quantity can not be less than sold tickets.","Ticket quantity can not be less than sold tickets."),
		//From below are recurring event's exception
		CAN_NOT_DELETE_EVENT("4060439","Can not delete event schedule. Tickets are sold from this event schedule.","Can not delete event. Tickets are sold from this event."),
		CAN_NOT_DELETE_EVENT_ORDER_IS_IN_PROGRESS_BUT_YOU_CAN_CANCEL_EVENT("4060440","Can not delete event but you can cancel event. Order is in progress. Please try after sometimes.","Can not delete event but you can cancel event. Order is in progress. Please try after sometimes."),
		CAN_NOT_UPDATE_EVENT("4060441","Can not update event. Tickets are sold from this event.","Can not update event. Tickets are sold from this event."),
		CAN_NOT_UPDATE_PAST_EVENT("4060442","Can not update past event.","Can not update past event."),
		CAN_NOT_DISABLE_RECURRING_EVENT("4060443","Can not disable recurring event. Tickets are sold from this event.","Can not disable recurring event. Tickets are sold from this event."),
		RECURRING_EVENT_IS_NOT_ENABLE("4060444","Please enable recurring event first.","Please enable recurring event first."),
		NOT_UNPAID_ORDER("4060445", "Order type is not UNPAID.", "Order type is not UNPAID."),
		HIDDEN_FIELD_CAN_NOT_BE_REQUIRED_FIELD("4060446","You can not set required flag for hidden field.","You can not set required flag for hidden field."),
		TICKET_NOT_AVAILABLE("4060447",
						"There is no available tickets for this recurring event. All Sold out. ",
						"There is no available tickets for this recurring event. All Sold out. "),

        TICKET_NOT_AVAILABLE_SALE("4060495",
                "There is no available tickets for this event. All Sold out. ",
                "There is no available tickets for this event. All Sold out. "),
		CAN_NOT_NULL_XX_RELATIVE_TIME("4060444", "Please fill the recurring relative start and end time.", "Please fill the recurring relative start and end time."),
		RESEND_TICKET_EMAIL_ALREADY_SCHEDULED("4060446", "Resend event ticket already scheduled update existing one.", "Resend event ticket already scheduled update existing one."),
		NO_RESEND_EVENT_TICKET_EMAIL_SCHEDULED("4060447", "Resend event ticket email is not scheduled. Create New one.", "Resend event ticket email is not scheduled. Create New one."),
		NO_RECURRING_EVENT_TO_RESEND_EMAIL("4060448", "No recurring event found to send email at this time.", "No recurring event found to send email at this time."),
		HOLD_TOKEN_DOES_NOT_MATCH("4060449", "Selected seat already booked by someone else, Please try again.","Selected seat already booked by someone else, Please try again"),
		CAN_NOT_CANCEL_SCHEDULE_EVENT("4060439","Can not cancel event schedule. Tickets are sold from this event schedule.","Can not cancel event. Tickets are sold from this event."),//NOSONAR
		CAN_NOT_CANCEL_RECURRING_EVENT("4060451","Can not cancel event. Tickets are sold from this event.","Can not cancel event. Tickets are sold from this event."),//NOSONAR
        EVENT_CAPACITY_IS_MORE_THAN_SOLD_TICKETS("4060440","Please enter a number greater than or equal to the total number of sold tickets: {{sold_tickets}}.","Please enter a number greater than or equal to the total number of sold tickets: {{sold_tickets}}."),
		CATEGORY_REQUIRED("4060439","Category Id Is Required."),
        TICKET_TYPE_FORMAT_REQUIRED("4060452","Ticket type format is required."),
        EVENT_TICKETS_NOT_FOUND("4060450", "There is no available tickets for update ticket status to Check IN. ", "There is no available tickets for update ticket status to Check IN. "),
		TOTAL_QUANTITY_OF_TICKETS_IN_CSV_IS_MORE_THAN_REMAINING_TICKETS("4060451", "Total quantity of tickets in CSV are more than remaining tickets. Please increase the number of tickets from set up tickets page.", "Total quantity of tickets in CSV are more than remaining tickets. Please increase the number of tickets from set up tickets page."),
		PASS_EMAIL_ADDRESS("4060452", "Please pass email address.", "Please pass email address."),
        NO_EMAIL_ADDRESS_EXISTS_TO_SEND_MAGIC_LINK("4060480", "Not eligible email exists to send magic link.", "Not eligible email exists to send magic link."),
		EMAIL_IS_NOT_REGISTERED_FOR_EVENT("4060453", "${email_address} is not registered for ${event_name}.", "${email_address} is not registered for ${event_name}."),
		TICKETING_MODULE_NOT_ACTIVATED("4060454","Please activate your event to access the portal.","Please activate your event to access the portal."),
        AFTER_PUBLISH_EVENT_CAN_NOT_CHANGE_DATE ("4060455","Event date change not allowed after event been published.","Event date change not allowed after event been published."),
        INVALID_TRIGGER_STATUS ("4060456","Not valid trigger status.","Not valid trigger status."),
        CAN_NOT_CHANGE_EVENT_START_DATE_AFTER_EVENT_STARTED ("4060457","Event start date change not allowed after event started."),
        CAN_NOT_CHANGE_EVENT_END_DATE_AFTER_EVENT_ENDED ("4060458","Event end date change not allowed after event ended."),
        EVENT_CAN_NOT_USE_BEEFREE_TEMPLATE ("4060459","Event can not use beefree template."),
		INTEREST_TAG_FEATURE_IS_AVAILABLE_UNDER_X_PLAN_ONLY("4060460", "Interest tag feature is available under ${commaSeparatedPlanNames} plans only", "Interest tag feature is available under ${commaSeparatedPlanNames} plans only"),
        CAN_NOT_CHANGE_AFTER_EVENT_ENDED ("4060460","Can not change to single day event after event ended."),
        TICKET_COUPON_ALREADY_EXIST_WITH_NAME("4060461", "Ticketing coupon ${couponName} already exist.", "Ticketing coupon ${couponName} already exist."),
        TICKET_ACCESS_CODE_ALREADY_EXIST_WITH_NAME("4060462", "${accessCode} access code already present for this event.", "${accessCode} access code already present for this event."),
        TICKET_PRICE_SHOULD_NOT_BE_ZERO("4060463","Paid Ticket price should not be zero"),
        TICKET_TYPE_RESTRICTED("4060465","Ticket Type(s) ${ticketTypes} restricted for this session tag and/or tracks.", "Ticket Type(s) ${ticketTypes} restricted for this session tag and/or tracks."),
        TICKET_ACCESS_CODE_IS_EXPIRED("4060467","The access code you entered has expired.", "The access code you entered has expired."),
        TICKET_ACCESS_CODE_IN_FUTURE("4060468","The access code you entered is not valid yet.", "The access code you entered is not valid yet."),
        SAME_TICKET_NAME_IS_NOT_ALLOWED("4060469","You cannot use the same name for multiple ticket types.", "You cannot use the same name for multiple ticket types."),
        EVENT_CAPACITY_REACH("4060470","The Event has reached its maximum number of participants.", "The Event has reached its maximum number of participants."),
        EVENT_DOES_NOT_HAVE_ENOUGH_TICKETS_UPLOAD_CSV_CAPACITY_REACH("4060471","Event does not have enough tickets to upload this csv. Event capacity reach.", "Event does not have enough tickets to upload this csv. Event capacity reach."),
        NOT_VALID_EVENT_CAPACITY("4060472","Not valid event capacity.", "Not valid event capacity."),
        START_DATE_SHOULD_BE_BEFORE_END_DATE("4060473","Start date should be before end date.","Start date should be before end date."),
		START_DATE_AND_END_DATE_MUST_NOT_BE_NULL("4060474","Start date and end date must not be null.","Start date and end date must not be null."),
        ACCESS_CODE_START_DATE_SHOULD_BE_BEFORE_END_DATE("4060475","Access code start date should be before end date.","Access code start date should be before end date."),
        DISCOUNT_CODE_START_DATE_SHOULD_BE_BEFORE_END_DATE("4060476","Discount code start date should be before end date.",
                                                                   "Discount code start date should be before end date."),
        USE_BARCODE_OR_EVENT_TICKET_INFORMATION_TO_CHECKIN("4060477", "Please enter your registration information or scan your QR code.", "Please use Barcode or EventTicket Information to checkIn"),
        NO_SUCH_EVENT_TICKET_EXIST("4060478", "Check-in failed. This registration is not valid for this event.", "No Such EventTicket exist"),
        BADGE_NOT_ALLOWED_FOR_DONATION_OR_ADD_ON_TICKETS("4060479","Badges not allowed for donation or Addon Tickets.","Badges not allowed for donation or Addon Tickets."),

        MULTIPLE_RECORDS_FOUND_FOR_TICKET("4060481","Your credentials match more than one attendee.","Your credentials match more than one attendee."),

        TICKET_HAS_REGISTRATION_REQUEST("40604010", "Registration request already created for this ticket type. If you do not wish to register any more of this ticket type then please click the \"Hide this ticket type\" button.",
                "Registration request already created for this ticket type. If you do not wish to register any more of this ticket type then please click the \"Hide this ticket type\" button."
        ),
        TICKET_IS_UNPAID("40604011", "Your event registration has a pending balance. Please contact the event Admin for more information.",
                "Your event registration has a pending balance. Please contact the event Admin for more information."),
        CANCEL_ORDER_NOT_REFUNDED("40604012", "The order has not been canceled as all the tickets are not refunded.",
                "The order has not been canceled as all the tickets are not refunded."),
        CANCEL_TICKET_NOT_REFUNDED("40604013","Event ticket has not been canceled as the ticket is not refunded.","Event ticket has not been canceled as the ticket is not refunded"),
        ALREADY_PAID_TICKET("40604014", "Ticket is already paid", "Ticket is already paid"),
        LOWER_PAID_TICKET_TO_HIGHER_PAID_TICKET_NOT_ALLOWED("40604015", "Lower paid ticket to Higher paid ticket not allowed", "Lower paid ticket to Higher paid ticket not allowed"),
        PARTIAL_CARD_PAYMENT_NOT_ALLOWED("40604016", "Ticket is already partially paid by cash", "Ticket is already partially paid by cash"),
        TICKET_TYPE_NOT_ADDON(
                "40604017",
                "Provided ticket type is not addon.",
                "Provided ticket type is not addon."),
        INVALID_TICKET_DETAILS(
                "40604018",
                "Invalid ticket details.",
                "Invalid ticket details."),
        INDIVIDUAL_TICKET_CHECKOUT_NOT_ALLOWED_FOR_DONATION_OR_ADD_ON_TICKETS("4060480","Ticket checkout not allowed for donation or Addon Tickets.","Ticket checkout not allowed for donation or Addon Tickets."),
        DISPLAY_CODE_ALREADY_EXIST("4060481", "Ticketing limited display code already exist", "Ticketing limited display code already exist"),
        DISPLAY_CODE_VALIDATION_FAILED("4060482", "Ticketing limited display code should not contain whitespace and unicode character", "Ticketing limited display code should not contain whitespace and unicode character"),
        DISPLAY_CODE_LENGTH_VALIDATION_FAILED("4060483", "The character limit for the ticketing limited display code is 100 characters", "The character limit for the ticketing limited display code is 100 characters"),
        DISPLAY_CODE_NOT_BLANK("4060484", "Ticketing limited display code should not blank", "Ticketing limited display code should not blank"),
        DISPLAY_CODE_ALREADY_USED_IN_WIDGET("4060485","Limited display code already used in widget", "display code already used in widget" ),
        TICKET_DISPLAY_CODE_ALREADY_EXIST_WITH_NAME("4060486", "${displayCode} display code already present for this event.", "${displayCode} display code already present for this event."),
        EVENT_CAPACITY_IS_MORE_THAN_100_TICKETS("4060487","Please enter a number greater than or equal to the total number of sold tickets: {{sold_tickets}}.","Please enter a number greater than or equal to the total number of sold tickets: {{sold_tickets}}."),
        EVENT_CAPACITY_IS_REACHED("4060488","You can not buy tickets more then event capacity limit","You can not buy tickets more then event capacity limit"),
        CAN_NOT_ENABLE_DEPOSITE_FEATURE("4060489","To Enable Deposit feature Pay-later option is required.","To Enable Deposit feature Pay-later option is required."),
        DEPOSITE_AMOUNT_NOT_EXCEED_TO_PRICE("4060490","Deposit amount must be less than or equals to ticket price.","Deposit amount must be less than or equals to ticket price."),
        TICKET_ALREADY_CANCELED_OR_REFUNDED("40604091", "Ticket is already canceled or refunded", "Ticket is already canceled or refunded"),
        BARCODE_NOT_VALID("40604092","Barcode is not valid.","Barcode is not valid."),
        TICKET_TRANSFER_NOT_POSSIBLE_FOR_CHECK_IN("40604093", "Ticket holder is already checked in for the event.You cannot transfer ticket after check-in.", "Ticket holder is already checked in for the event.You cannot transfer ticket after check-in."),
        ALL_FIELDS_ARE_MANDATORY_FOR_TICKET_TRANSFER("40604094","First name, last name and email address should not be blank.","First name, last name and email address should not be blank."),
        TICKET_TRANSFER_NOT_POSSIBLE_FOR_PAST_EVENT("40604095","Not allowed to transfer a ticket after an event ends.","Not allowed to transfer a ticket after an event ends."),
        TICKET_TRANSFER_LIMIT_REACHED("40604096","Not allowed to transfer a ticket as the ticket transfer limit is exceeded.","Not allowed to transfer a ticket as the ticket transfer limit is exceeded."),
        BUYER_TRANSFER_LIMIT_REACHED("40604097","Not allowed to change a buyer email as the limit is exceeded.","Not allowed to change a buyer email as the limit is exceeded." ),
        PAYMENT_TOKEN_NOT_FOUND("40604098","Payment token not found.","Payment token not found."),
        TICKET_TRANSFER_NOT_POSSIBLE_TICKET_TYPE_NON_TRANSFERABLE("40604099", "Not allowed to transfer a ticket because ticket type is nonTransferable", "Not allowed to transfer a ticket because ticket type is nonTransferable"),
        TICKET_TRANSFER_NOT_POSSIBLE_ORDER_IS_DISABLE_FOR_TRANSFER("40604100", "Not allowed to transfer a order because order is nonTransferable", "Not allowed to transfer a order because order is nonTransferable"),
        TRANSACTION_NOT_SETTLED("40604101", "The transaction is not settled, please try again after some time.","The transaction is not settled, please try again after some time."),
        CANNOT_ENABLED_TICKET_EXCHANGE_TOGGLE("40604102","Ticket exchange cannot be enabled when VAT is enabled at the ticket type level.","Ticket exchange cannot be enabled when VAT is enabled at the ticket type level."),
        TICKET_EXCHANGE_NOT_ALLOWED("40604103","Ticket exchange is not allowed because it is disabled in the ticketing settings.","Ticket exchange is not allowed because it is disabled in the ticketing settings."),
        CANNOT_ENABLE_VAT_TAX_AT_TICKET_LEVEL("40604104","VAT cannot be enabled at the ticket type level because ticket exchange is currently enabled in the ticketing settings.","VAT cannot be enabled at the ticket type level because ticket exchange is currently enabled in the ticketing settings."),
        INVALID_ORDER_EXCHANGE_MSG("40604105", "Order Level Exchange is only available for free tickets.", "Order Level Exchange is only available for free tickets."),
        ORDER_EXCHANGE_CONFIGURATION_MISSING_MSG("40604106", "Order-level exchange configuration is missing. Please contact the administrator for assistance.", "Order-level exchange configuration is missing. Please contact the administrator for assistance."),
        THIS_ORDER_EXCHANGE_NOT_ALLOWED_MSG("40604107", "Your order is not eligible for exchange. Please contact the administrator for assistance.", "Your order is not eligible for exchange. Please contact the administrator for assistance."),
        ORDER_MUST_CONTAIN_EXACTLY_ONE_TICKET("40604108","Order must contain exactly one ticket.","Order must contain exactly one ticket."),
        COUPON_IS_ALREADY_APPLIED_TO_THIS_ORDER("40604109","A discount code is already applied to this order.","A discount code is already applied to this order."),
        COUPON_CODE_NOT_APPLICABLE_ON_TICKET_EXCHANGE("40604110", "This discount code cannot be applied during ticket exchange because your discount code is configured to apply at the order level, not per ticket.", "This discount code cannot be applied during ticket exchange because your discount code is configured to apply at the order level, not per ticket."),
		BARCODE_ID_REQUIRED_TO_MAP_RFID("40604111", "Barcode ID is required to map to the RFID.", "Barcode ID is required to map to the RFID."),
		RFID_REQUIRED_TO_MAP_BARCODE("40604112", "RFID is required to map to the Barcode ID.", "RFID is required to map to the Barcode ID."),
        ORDER_EXCHANGE_ALLOWED_ONLY_FOR_FREE_TICKETS("40604113", "Order exchange is only allowed for free tickets.", "Order exchange is only allowed for free tickets."),
        ORDER_EXCHANGE_NOT_ALLOWED("40604114", "Order exchange is currently disabled. Please enable it in the Settings to proceed.", "Order exchange is currently disabled. Please enable it in the Settings to proceed."),
        ORDER_EXCHANGE_NOT_ALLOWED_FOR_DIFFERENT_ORDER_TICKETS("40604115", "Selected tickets belong to a different order. Please select tickets from the same order to proceed.", "Selected tickets belong to a different order. Please select tickets from the same order to proceed."),
        DISCOUNT_CODE_NOT_APPLICABLE_FOR_ALL_FREE_TICKET("40604116", "All selected tickets are free; discount codes are not applicable.", "All selected tickets are free; discount codes are not applicable."),
        TICKET_ALREADY_CHECKIN("40604117", "This ticket has already been checked in. Re-check-in is not allowed.", "This ticket has already been checked in. Re-check-in is not allowed."),
        ADD_ON_TICKET_WITHOUT_EVENT_TICKET_EXCEPTION("40604118","Cannot upload addons ticket without purchase event ticket","Cannot upload addons ticket without purchase event ticket");
        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		TicketingExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		TicketingExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public void setStatusCode(String statusCode) {
			this.statusCode = statusCode;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}
	}

	public enum DonationExceptionMsg {
		DONATION_NOT_ACTIVE(
				"4060501",
				"Please activate this module to start accepting donation.",
				"Please activate this module to start accepting donation."),
		GOAL_AMOUNT_LESS_THAN_FUND_RAISED(
				"4060502",
				"You can not set goal amount less than total fund raised.",
				"You can not set goal amount less than total fund raised."),
		DONATION_ALREADY_REFUNDED("4060503", "Donation already refunded.", "Donation already refunded."),
		DONATION_REFUND_FAILED("4060504", "Unable to refund the donation.", "Unable to refund the donation."),
		PAYMENT_PROCESSING_IS_NOT_SETUP(
				"4060505",
				"Payment processing is not set up. Please notify the administrator.",
				"Payment processing is not set up. Please notify the administrator."),
		DONATION_NOT_FOUND("4060506", "Could not find donation.", "Could not find donation."),
		CASH_NOT_ACCEPTED("4060507", "Cash payment can be done by staff or admin only.",
				"Cash payment can be done by staff or admin only."),
		FUNCTIONALITY_NOT_ACTIVE(
				"4060508",
				"Please activate text to give module to use this functionality.",
				"Please activate text to give module to use this functionality."),
        DONATION_AMOUNT_ALREADY_EXISTS(
                "4060527",
                "Duplicate donation amount does not allowed.",
                "Duplicate donation amount does not allowed.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		DonationExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum ModuleExceptionMsg {
		MODULE_ENDED("4060601", "Module Ended", "Module Ended"),
		MISMATCH_MODUEL_TYPE("4060602", "Module type mismatch.", "Module type mismatch."),
		EVENT_CC_NOT_ENABLE("4060603", "Event CC is not enabled", "Event CC is not enabled"),
		MODULE_NOT_ENABLED("4060604", "Module Not Enabled", "Module Not Enabled");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ModuleExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum PaymentCreationExceptionMsg {
		PAYMENT_NOT_FOUND_IN_DB("4060701", "Payment Detail Not Found", "Payment Detail Not Found"),
		CARD_NOT_LINKED("4060702", "Card is not linked", "Card is not linked"),
		CARD_NOT_FOUND("4060703", "Card not found", "Card not found"),
		CREDIT_CARD_PROCESSING_NOT_ENABLE(
				"4060704",
				"Credit card processing not enabled",
				"Credit card processing not enabled"),
		RECURRING_PAYMENT_NOT_SUPPORTED_IN_SQUARE(
				"4060705",
				"Recurring payment is not supported in square.",
				"Recurring payment is not supported in square."),
		SQUARE_CONNECT_NOT_AVAILABLE_FOR_WL(
				"4060706",
				"Currently, We do not support connecting of square payment gateway for white label events.",
				"Currently, We do not support connecting of square payment gateway for white label events."),
		CHARGE_CREATION_FAILED("4060707", "Charge creation failed", "Charge creation failed while event billing"),
		PAYMENT_METHOD_NOT_PRESENT("4060708", "Payment details not found, please retry the payment", "Payment details not found, please retry the payment"),
		PAYMENT_METHOD_NOT_VALID_STATUS("4060709", "Payment details are not valid", "Payment details are not valid"),
		ACTIVATE_REGISTRATION(
				"4060710",
				"Please activate your event to begin accepting registrations",
				"Please activate your event to begin accepting registrations"),
        AMOUNT_TO_LARGE("4060711", "Credit card donations are limited to $999,999.99. For larger donations please contact the organization directly", "Credit card donations are limited to $999,999.99. For larger donations please contact the organization directly"),
        SUBSCRIPTION_NOT_ACTIVATED(
		        "4060712",
                        "Please activate your subscription to publish the event",
                        "Please activate your subscription to publish the event"),
         DONATION_CHARGE_FAILED(
                 "4060713",
                 "donation charges failed",//NOSONAR
                 "donation charges failed"),//NOSONAR
        DONATION_CHARGE_FAILED_MSG(
                "4060713",
                "donation charges failed",//NOSONAR
                "donation charges failed"),//NOSONAR
         AUCTION_CHARGE_FAILED(
                 "4060714",
                         "auction charges failed",
                         "auction charges failed"),
         RAFFLE_CHARGE_FAILED(
                 "4060715",
                         "raffle charges failed",
                         "raffle charges failed"),
         CAUSEAUCTION_CHARGE_FAILED(
                 "4060716",
                         "cause Auction charges failed",
                         "cause Auction charges failed"),
         TICKET_PURCHASE_FAILED(
                 "4060717",
                         "ticket purchase charges failed",
                         "ticket purchase charges failed")
        ,ACTIVATE_PAYMENT_PROCESSING_FOR_PAID_TICKETTYPES(
                "4060717",
                "Please activate payment processing, since the event has paid/donation ticket types.",
                "Please activate payment processing, since the event has paid/donation ticket types."),
        ACTIVATE_PAYMENT_PROCESSING_FOR_FUNDRAISER_MODULE(
                "4060717",
                "Please activate payment processing, since one of your fundraising module is enabled.",
                "Please activate payment processing, since one of your fundraising module is enabled."),
        CARD_DECLINED("4060718", "Card declined", "Card declined"),
        SUBSCRIPTION_IS_CANCELLED(
		        "4060719",
                        "Current plan subscription is expired, Please renew subscription to publish this event",
                        "Current plan subscription is expired, Please renew subscription to publish this event"),
        PAYPAL_ORDER_UPDATE_CANCELLED("4060720", "Issue while updating paypal order", "Issue while updating paypal order"),
        PAYPAL_ORDER_CAPTURE_ISSUE("4060721", "Issue while collecting payment for paypal order", "Issue while collecting payment for paypal order"),
        PAYPAL_ORDER_NOT_APPROVED("4060722", "Order is not approved by customer", "Order is not approved by customer"),
        PAYPAL_ORDER_ALREADY_COMPLETED("4060723", "Order is already completed", "Order is already completed"),
        PAYPAL_ORDER_ALREADY_REFUNDED_FROM_PAYPAL_SIDE("4060724", "Order is already refunded from paypal dashboard", "Order is already refunded from paypal dashboard"),
        PAYFLOW_DOES_NOT_CONNECTED("4060725", "Enterprise account does not connected with PayFlow gateway",
                "Enterprise account does not connected with PayFlow gateway"),
        PARTIAL_PAYMENT_NOT_ALLOWED("4060726","Order is already partially paid.", "Order is already partially paid."),
        OTHER_PAYMENT_METHOD_NOT_ALLOWED_FOR_PARTIAL_PAID_ORDER("4060727","Order is partially paid by ${oldPaymentMethod}, please use ${oldPaymentMethod} payment type.", "Order is partially paid by ${oldPaymentMethod}, please use ${oldPaymentMethod} payment type."),
        PREVIOUS_PAYMENT_STILL_IN_PROCESSING("4060728","Previous payment is in processing state. Please try after sometime.", "Previous payment is in processing state. Please try after sometime."),
        EVENT_CONNECTED_TO_PAYMENT_GATEWAY("4060729", "Event is connected to ${PAYMENT_GATEWAY} payment gateway.", "Event is connected to ${PAYMENT_GATEWAY} payment gateway."),
        EVENT_IS_NOT_CONNECTED_TO_STRIPE_OR_AUTHORIZE_NET_PAYMENT_GATEWAY("4060730", "This feature is only available with Stripe and Authorize.Net payment gateway", "This feature is only available with Stripe and Authorize.Net payment gateway");
        private final String statusCode;
		private String errorMessage;
		private String developerMessage;

		PaymentCreationExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        } //NOSONAR

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        } //NOSONAR
    }

	public enum ItemExceptionMsg {
		ITEM_ALREADY_EXISTS("4060801", "Item Code Already exists", "Item Code Already exists"),
		ITEM_NOT_EXISTS("4060802", "Item Not exists", "Item Not exists"),
		ITEM_CATEGORY_NOT_EXISTS("4060803", "Item Category Not exists", "Item Category Not exists"),
		ITEM_CATEGORY_ALREADY_EXISTS("4060804", "Item Category Already exists", "Item Category Already exists"),
		ITEM_CODE_NOT_VALID("4060805", "Item Code Not Valid", "Item Code Not Valid"),
		ITEM_CATEGORY_USED_FOR_ITEM("4060806", "Item Category used for item.", "Item Category used for item"),
		ITEM_IMAGE_NOT_EXISTS("4060807", "Item Image Not exists", "Item Image Not exists"),
		ITEM_SHORT_NAME_EXISTS("4060808", "Item short name Already exists", "Item short name Already exists"),
		MORE_PLEDGES_SUBMITTED("4060810", "The maximum number of pledges being accepted has already been reached.", "The maximum number of pledges being accepted has already been reached."),
		BID_INCREMENT_MINIMUM_ONE("4060811", "Minimum bid increment has to be at least one. It can't be zero.", "Minimum bid increment has to be at least one. It can't be zero."),
		ITEM_CODE_DEFAULT("4060811", "Cannot save item with Default Code", "Cannot save item with Default Code"),
		ITEM_ALREADY_FAVOURITE("4060812", "This item is already in favourite list", "This item is already in favourite list"),
		WINNER_GREATER_THAN_ZERO("4060813", "Number of winner should be greater than 0", "Number of winner should be greater than 0"),
		ITEM_ALREADY_PURCHASED("4060815", "Item already purchased", "Item already purchased"),
		ITEM_CATEGORY_NOT_VALID("4060816", "Uncategorized items will automatically be placed in an uncategorized category. You do not need to add a category called uncategorized."),
        ITEM_CODE_IS_REQUIRED("4060817", "Item Code is required","Item Code is required"),
        ONLY_25_CHARACTER_ALLOWED_IN_ITEM_CATEGORY_NAME("4060122","Only 25 character are allowed in item category name","Only 25 character are allowed in item category name");



		private final String statusCode;
		private final String errorMessage;
		private final String developerMessage;

		ItemExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		ItemExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum EventExceptionMsg {
		BIDDER_NUMBER_NOT_ENANLED("4060901", "Bidder number is not enabled for this event", "Bidder number is not enabled for this event"),
		EVENT_CAN_NOT_BE_HIDDEN("4060902", "This event has activity so it cannot be hidden.", "This event has activity so it cannot be hidden."),
		EVENT_CAN_NOT_BE_CONVERT_INTO_RECURRING("4060903", "This event has activity so it cannot be convert to recurring event. Please try with create duplicate event.", "This event has activity so it cannot be convert to recurring event. Please try with create duplicate event."),
		EVENT_NAME_CAN_NOT_NULL("4060904", "Event name can not be empty", "Event name can not be empty"),
		EVENT_URL_CAN_NOT_EMPTY("4060905", "Event url can not be empty", "Event url can not be empty"),
        CAN_NOT_DELETE_EVENT("4060906","{event_name} has more than "+ Constants.MAX_ATTENDEE_TO_DELETE_EVENT +" participants, due to the size of event it cannot be deleted, if this is a test event, please make sure that the event is marked as such.","event has more than 40 participants, due to the size of event it cannot be deleted, if this is a test event, please make sure that the event is marked as such."),
        EVENT_CAN_NOT_FIND_PAYMENT_DETAILS("4060907", "We are not able to get payment details of your event. Please reach out to our Customer Support.", "We are not able to get payment details of your event. Please reach out to our Customer Support."),
		EVENT_URL_LENGTH_EXCEPTION("4060908","The character limit for the Event URL is 50 characters, the URL you attempted to save is ${urlLength} characters. Please try a short event URL.","The character limit for the Event URL is 50 characters, the URL you attempted to save is ${urlLength} characters. Please try a short event URL."),
        NOT_ALLOWED_TO_ACCESS_VIRTUAL_EVENT_PAGE("4060909","The event format is In-Person and does not allow access to the Virtual Event Hub","The event format is In-Person and does not allow access to the Virtual Event Hub"),
        CAN_NOT_PUBLISH_EVENT_WITH_TEMPLATE("4060910","You cannot publish template event." , "You cannot publish template event."),
        MAGIC_LINK_EVENT_ENDED("4060911","The event associated with this link has ended." ,"The event associated with this link has ended." );

		private String statusCode;
		private String errorMessage;
		private String developerMessage;
        private String defaultMessage;
        private Map<String,String> defaultMessageParamMap;

		EventExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

        EventExceptionMsg(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage= defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setStatusCode(String statusCode) { this.statusCode = statusCode; }

        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public void setDeveloperMessage(String developerMessage) { this.developerMessage = developerMessage; }

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }

	public enum ExhibitorExceptionMsg {
		EXHIBITOR_NOT_EXISTS("4060906", "Exhibitor Not exists", "Exhibitor Not exists"),
		MAX_TEAM_MEMBER_LIMIT_REACHED("4068948", "The maximum number of team members has been reached",
                "The maximum number of team members has been reached"),
		CAN_NOT_GENERATE_LEAD("4060908", "Lead retrieval is not allowed for this exhibitor", "Lead retrieval is not allowed for this exhibitor"),
		ALREADY_USED_TICKET("4060909", "You have already used your ticket", "You have already used your ticket"),
		LEAD_NOT_EXISTS("4060911", "Lead Not exists", "Lead Not exists"),
		EXHIBITOR_STAFF_ROLE_NOT_MATCHED("4060912", "exhibitoradmin & leadretriever are valid roles for exhibitor staff"),
		EXHIBITOR_DOES_NOT_BELONGS_TO_USER("4060913", "You are not authorized to access this exhibitor"),
		LEAD_IS_NOT_BELONG_TO_THIS_LEADRETRIEVER("4060914", "You have not added this lead. You can delete only lead which are added by you."),
		PASSED_MAX_TEAM_MEMBER_ARE_LESS_THAN_ADDED_STAFF("4060915", "You have passed the number of team members is less than you have added it."),
		NOT_ALLOW_TO_AUTO_GENERATE_LEAD("4060916", "Not allow to auto generate lead by event host"),
        EXHIBITOR_CATEGORY_NOT_EXISTS("4060919", "Exhibitor Category Not exists", "Exhibitor Category Not exists"),
        EXHIBITOR_CATEGORY_USED_FOR_EXHIBITOR("4060920", "Exhibitor Category used for exhibitor.", "Exhibitor Category used for exhibitor"),
        EXHIBITOR_CATEGORY_ALREADY_EXISTS("4060921", "Exhibitor Category Already exists", "Exhibitor Category Already exists"),
        EXHIBITOR_CATEGORY_NOT_VALID("4060922", "Uncategorized exhibitor will automatically be placed in an uncategorized category. You do not need to add a category called uncategorized."),
		EXHIBITOR_ALREADY_EXISTS("4060923", "Exhibitor already exists", "Exhibitor already exists"),
		EXHIBITOR_ADMIN_CAN_NOT_REMOVE_EXHIBITOR_ADMIN("4060924", "Exhibitors admins should not be able to remove exhibitor admins","Exhibitors admins should not be able to remove exhibitor admins"),
		LIVE_STREAM_EXHIBITORS_REACH("4060925", "You have purchased ${number} number of exhibitors for live streaming and you have reached that number.","You have purchased ${number} number of exhibitors for live streaming and you have reached that number."),
		PLEASE_PURCHASE_EXHIBITOR_TO_LIVE_STREAM("4060926", "Please purchase the number of exhibitors you want to enable live stream from billing page.","Please purchase the number of exhibitors you want to enable live stream from billing page."),
		EXHIBITOR_ALREADY_USED_LIVE_STREAM("4060927", "Exhibitor already used live streaming, Can not disable after used.","Exhibitor already used live streaming, Can not disable after used."),
		PLEASE_SELECT_NUMBER_OF_EXHIBITORS_TO_PURCHASE_FOR_LIVE_STREAM("4060928", "Please select the number of exhibitor in you want use live stream functionality.","Please select the number of exhibitor in you want use live stream functionality."),
        REMOVE_THE_PRO_PLAN_FROM_EXHIBITOR_FIRST_BEFORE_DELETE_IT("4060929", "Please remove the Pro Plan from this exhibitor before deleting this exhibitor, so that you can allocate the Pro Plan to another exhibitor."),
        STAFF_ALREADY_EXISTS_IN_EXHIBITOR("4060930", "Staff already exists in this exhibitor."),
		ATTENDEE_ACCESS_ONLY_FOR_FREE_TICKET_TYPE("4060931","Attendee access to staff is only available for free ticket types."),
        SWITCH_OFF_PRO_EXHIBITOR_SETTING_BEFORE_DELETING_EXHIBITOR("4060932", "Please, switch off the Pro Exhibitor Setting before deleting the exhibitor"),
        NOT_POSSIBLE_TO_DELETE_PRO_EXHIBITOR("4060933", "It is not possible to delete a Pro Exhibitor"),
        USER_IS_STAFF_CANNOT_ADDED_AS_LEAD("4060936", "User has admin/staff/exhibitor/lead retriever level access, so can not be added as a lead.","User has admin/staff/exhibitor/lead retriever level access, so can not be added as a lead."),
        LEAD_CANT_UPDATE("4068944", "It looks like this user has not purchased any tickets or the ticket is refunded for the current event so can't update its lead details"),
        EMAIL_USER_NOT_EXIST("4068945", "Enter email user not exists, please add existing user email id"),
        USER_IS_NOT_EXHIBITOR_STAFF("4060941", "Participant user does not have exhibitor/lead retriever level access, so can not be changed in the meeting participant.","Participant user does not have exhibitor/lead retriever level access, so can not be changed in the meeting participant.");


		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ExhibitorExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		ExhibitorExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public void setStatusCode(String statusCode) {
			this.statusCode = statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}
	}

    public enum SponsorsExceptionMsg {
        SPONSOR_NOT_EXISTS("4060916", "Sponsor Not exists", "Sponsor Not exists"),
        SPONSOR_STAFF_ROLE_NOT_MATCHED("4060917", "sponsor admin are valid roles for sponsor staff"),
        SPONSOR_DOES_NOT_BELONGS_TO_USER("4060918", "You are not authorized to access this sponsor"),
        SPONSOR_ALREADY_EXISTS("4060919", "Sponsor already exists", "Sponsor already exists"),
        SPONSOR_CARD_SIZE_NOT_ALLOWED("4060920", "Sponsor card size not allowed", "Sponsor card size not allowed"),
        SPONSOR_URL_LENGTH_NOT_ALLOWED("4060921", "More than 255 characters are not allowed.", "More than 255 characters are not allowed.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SponsorsExceptionMsg(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        SponsorsExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }
    public enum WhiteLabelExceptionMsg {
        ALREADY_WHITELABEL_EVENT("4030103", "Already Whitelabel Event, can not move.", "Already Whitelabel Event"),
        NOT_WHITELABEL_EVENT("4030104", "Not Whitelabel Event, allowed only for whitelabel event", "Not Whitelabel Event, allowed only for whitelabel event"),
        NOT_VALID_WHITELABEL_URL("4030105", "Please enter valid enterprise URL", "Please enter valid enterprise URL");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;
        WhiteLabelExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }
            public String getStatusCode() {
                return statusCode;
            }

            public String getErrorMessage() {
                return errorMessage;
            }

            public String getDeveloperMessage() {
                return developerMessage;
            }

            public void setErrorMessage(String errorMessage) {
                this.errorMessage = errorMessage;
            }

            public void setDeveloperMessage(String developerMessage) {
                this.developerMessage = developerMessage;
            }
        }

	public enum ContactExceptionMsg {
		CONTACT_NOT_EXISTS("406110", "Contact Not exists", "Contact Not exists"),
		CONTACT_UPLOAD_MAX_LIMIT_REACHED("406111", "You have reached the contact upload limit of ${MAX_CONTACTS} contacts", "You have reached the contact upload limit of ${MAX_CONTACTS} contacts"),
		SEND_EMAIL_MAX_LIMIT_REACHED("406112", "You have already used your {EMAIL_BLASTS} email blasts", "You have already used your {EMAIL_BLASTS} email blasts"),
		CONTACT_ID_OR_PARAMETERS_NOT_PROVIDED("4060999", "Either provide the contact ids to delete or pass deleteAll parameters to delete all the contacts", "Either provide the contact ids to delete or pass deleteAll parameters to delete all the contacts"),
        NOT_VALID_TEMPLATE_ID("4060100", "Template id is passing inappropriate.", "Template id is passing inappropriate."),
        INTEREST_TAG_FEATURE_IS_NOT_AVAILABLE_UNDER_CURRENT_PLAN("4060460", "Interest tag feature is not available under current plan, please upgrade your plan.", "Interest tag feature is not available under current plan, please upgrade your plan."),
		MORE_THAN_X_INTEREST_TAG_IS_NOT_ALLOWED_UNDER_THIS_PLAN("4060461", "More than {limitValue} interest tag is not allowed under this plan, please upgrade your plan.", "More than {limitValue} interest tag is not allowed under this plan, please upgrade your plan."),
        INTEREST_TAG_FEATURE_IS_AVAILABLE_UNDER_X_PLAN_ONLY("4060462", "Interest tag feature is available under {commaSeparatedPlanNames} plans only. if you have already upgraded plan, publish your event to associate upgraded plan with this event.", "Interest tag feature is available under {commaSeparatedPlanNames} plans only. if you have already upgraded plan, publish your event to associate upgraded plan with this event."),
        CANNOT_ADD_INTEREST_TAG_MORE_THAN_X_UNDER_THIS_PLAN("4060463", "you can not add interest tag more than {limitValue} under this plan. if you have already upgraded plan, publish your event to associate upgraded plan with this event.", "you can not add interest tag more than {limitValue} under this plan. if you have already upgraded plan, publish your event to associate upgraded plan with this event."),
        SEND_EMAIL_SCHDULE_MAX_LIMIT_REACHED("4060464"),
        CANNOT_SET_LESS_THAN_EMAILS_USED("4060466", "Event has already used %d email blasts, we can't set less than that.", "Event has already used %d email blasts, we can't set less than that."),
        FREE_PLAN_TEST_EMAIL_LIMITATION("4060467","Please contact Accelevents, or connect your paid subscription","Please contact Accelevents, or connect your paid subscription"),
        CONTACT_LIST_NAME_EXISTS("406113", "A contact list with the same name already exists. Please provide a different name.", "A contact list with the same name already exists. Please provide a different name."),

        CONTACT_LIST_NAME_IS_BLANK("406114","The contact list name can not be blank.","The contact list name can not be blank."),

        CONTACT_ALREADY_EXIST_IN_CONTACT_LIST("406114","Contact already exist with same email in the contact list.","Contact already exist with same email in the contact list."),

        ERROR_MSG_FOR_CUSTOM_RECIPIENTS_WHEN_UPLOAD_MAX_LIMIT_REACHED("406115", "You have reached the limit of ${MAX_CONTACTS} non-participant addresses allowed by your plan. Please contact Support for more information.", "You have reached the limit of ${MAX_CONTACTS} non-participant addresses allowed by your plan. Please contact Support for more information."),
        CONTACT_UPLOAD_FILE_HEADER_NOT_CORRECT(
                "406116",
                "Upload file headers name contains ${REQUIRED_COLUMNS} ${DUPLICATE_COLUMNS}",
                "Upload file headers name contains ${REQUIRED_COLUMNS} ${DUPLICATE_COLUMNS}"),
        CONTACT_UPLOAD_FILE_MISSING_HEADER(
                "406117",
                "The uploaded contact file is missing a header.",
                "The uploaded contact file is missing a header.");

        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ContactExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

        ContactExceptionMsg(String statusCode) {
            this.statusCode = statusCode;
        }

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

	}

	public enum TicketHolderAttributesMsg {
        ATTRIBUTE_NAME_EXIST("4060901", "Attribute name already exist", "Attribute name already exist"),
        ATTRIBUTE_NOT_EXISTS("4060902", "Attribute Not exists", "Attribute Not exists"),
        MCQ_DOES_NOT_ALLOWED("4060903", "Multiple choice question is only for profession and enterprise plan, please upgrade your plan.", "Multiple choice question is only for profession and enterprise plan, please upgrade your plan."),
        CONDITIONAL_QUESTION_SELECTED_ANS_ID("4060934", "Conditional question's one record must present having 0 selectedAnsId", "Conditional question's one record must present having 0 selectedAnsId,"),
        MINIMUM_ONE_SUB_QUESTION_NEEDED("4060935", "At least 1 sub question is required", "At least 1 sub question is required"),
        PARENT_QUESTION_NOT_FOUND("4060937", "Conditional questions must have one parent question", "Conditional questions must have one parent question"),
        INVALID_SUB_QUESTION_TYPE("4060938", "You can create sub question only for Conditional question type", "Wrong question type selected"),
        INTEREST_TAGS_PRESENT_CANNOT_DISABLE_ATTRIBUTE("4060939", "Cannot disable Interest attribute, Interest tag is present in the event", "Cannot disable Interest attribute, Interest tag is present in the event"),
        ATTRIBUTE_NOT_ACTIVATED("4060940","Can not add disabled holder/buyer attribute in kiosk confirmation","Can not add disabled holder/buyer attribute in kiosk confirmation"),
        KIOSK_INFORMATION_CONFIRMATION_NOT_ENABLED("4040088","Kiosk information confirmation not enabled","Kiosk information confirmation not enabled"),
        CAN_NOT_DISABLE_DEFAULT_FIELDS("4040089","Default fields can not be disabled","Default fields can not be disabled"),
        ATTRIBUTE_CAPACITY_EXCEEDED("4040090","Your selection is no longer available with ${value} for ${field}. Please choose another option.","Your selection is no longer available with ${value} for ${field}. Please choose another option."),
        ATTRIBUTE_NOT_ENABLED_FOR_HOLDER_AND_PURCHASER("4040091","Attribute is not enabled for holder or buyer.","Attribute is not enabled for holder or buyer."),
        CAN_NOT_BULK_UPDATE_WITH_SAME_VALUE("4040094","You can update this field {field} with only a single attendee.","You can update this field {field} with only a single attendee."),
        HOLDER_ATTRIBUTE_IS_NOT_VALID_FOR_PEOPLE_FILTER("4040096", "Only questions for type Multi choice and Dropdown are allowed for people filtering.", "Only questions for type Multi choice and Dropdown are allowed for people filtering."),
        ATTRIBUTE_NOT_ACTIVATED_IN_PEOPLE_FILTER("4040097","Can not add disabled holder attribute in people filtering.","Can not add disabled holder attribute in people filtering."),
        DUPLICATE_TICKET_TYPE_IN_REQUIRED_AND_OPTIONAL_FIELD("4040098","Same registration types are present in both required and optional fields.","Same registration types are present in both required and optional fields."),
        DEFAULT_VALUE_CAN_NOT_BE_NULL("4040107", "Default value can't be null because this field is hidden and mandatory for some ticket types.", "Default value can't be null because this field is hidden and mandatory for some ticket types.");
        private final String statusCode;
		private String errorMessage;
		private String developerMessage;

		TicketHolderAttributesMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

    }

	public enum WaitListExceptionMsg {
		WAIT_LIST_NOT_ENABLED("4061201", "Wait List is not enabled for this event.", "Wait List is not enabled for this event."),
		INVALID_WAIT_LIST_TRIGGER("4061202", "Please provide valid wait list trigger.", "Please provide valid wait list trigger."),
		WAIT_LIST_NOT_FOUND("4061203", "Wait list not found for given id.", "Wait list not found for given id."),
		INVALID_WAIT_LIST_IDS("4061204", "Invalid waitlist details.", "Invalid waitlist details."),
		WAIT_LIST_LIMIT_EXCEED("4061205", "There are only {SPOT_COUNT} spots remaining on the waitlist. Please adjust your request accordingly.", "There are only {SPOT_COUNT} spots remaining on the waitlist. Please adjust your request accordingly."),
		CANNOT_DECREASE_WAITLIST_SIZE("4061206", "Waitlist size should be greater than size of tickets already joined in waitlist.", "Waitlist size should be greater than size of tickets already joined in waitlist."),
		RELEASE_LIST_LIMIT_EXCEED("4061207", "To release this ticket from the waitlist, you must increase the Quantity Available on the Set Up Tickets page.", "To release this ticket from the waitlist, you must increase the Quantity Available on the Set Up Tickets page."),
        EVENT_RELEASE_LIST_LIMIT_EXCEED("4061207", "To release this ticket from the waitlist, you must increase the Number of Registrants Allowed on the Set Up Tickets page.", "To release this ticket from the waitlist, you must increase the Number of Registrants Allowed on the Set Up Tickets page."),
		INVALID_WAIT_LIST_TIME_TO_RESPOND("4061208", "Invalid time to respond. Please enter time to respond greater then 1 hour.","Invalid time to respond. Please enter time to respond greater then 1 hour."),
 		REORDER_ARGUMENTS_INVALID("4061209", "Invalid reorder request. Please provide waitlistId and valid before/after references.", "Invalid reorder request. Please provide waitlistId and valid before/after references."),
        WAITLIST_NOT_FOUND("4061210", "WaitList not found. Please try again", "WaitList not found."),
        UNABLE_TO_REORDER_WAITLIST("4061211", "Cannot reorder waitlist, please retry.", "Cannot reorder waitlist after normalization, please retry.");
        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		WaitListExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}
	}

    public enum RegistrationRequestExceptionMsg {
        INVALID_REQ_LIST_IDS("4060170", "Invalid request list.", "Invalid request list."),
        APPROVE_REQUEST_LIMIT_EXCEED("4060172", "To approve this ticket, you must increase the Quantity Available on the Set Up Tickets page.",
                "To approve this ticket, you must increase the Quantity Available on the Set Up Tickets page."),
        CHECKOUT_LINK_EXPIRE("4060174", "Checkout link expired.", "Checkout link expired."),
        NOT_ALLOW_WITH_ASSIGNED_SEATING("4060175", "Assigned seating is not supported yet with registration approval.",
                "Assigned seating is not supported yet with registration approval." ),
        NOT_ALLOW_WITH_REGISTRATION_APPROVAL("4060176","Registration approval does not yet work with events with assigned seating." ,
                "Registration approval does not yet work with events with assigned seating." ),
        REGISTRATION_APPROVAL_WORKFLOW_NOT_AVAILABLE_IN_CURRENT_PLAN("Registration approval workflow does not available in current plan.","Registration approval workflow does not available in current plan." ,
                "Registration approval does not yet work with events with assigned seating." ),
        REGISTRATION_APPROVAL_WORKFLOW_UNABLE_TO_DELETE_SPEAKER("4060265","Session Requests require at least one speaker." ,
                "Session Requests require at least one speaker." ),
        REGISTRATION_APPROVAL_WORKFLOW_SPEAKER_NOT_FOUND("4060266","Specified speaker is not found in this session request." ,
                "Specified speaker is not found in this session request." ),
        SESSION_REGISTRATION_REQUEST_REQUIRED_SPEAKER("4060267","Session Registration request require At least one speaker." ,
                "Session Registration request require At least one speaker." ),
        SESSION_REGISTRATION_REQUEST_UNIQUE_SPEAKER("4060268","Session Registration requests must have a unique speaker email." ,
                "Session Registration requests must have a unique speaker email." ),
        CUSTOM_ATTRIBUTE_IS_ALREADY_MAPPED("4060267","Mapped attribute is already assigned to another speaker information attribute.",
                "Mapped attribute is already assigned to another speaker information attribute."),
        REGISTRATION_EMAIL_NOT_SUPPORTED_FOR_STATUS("4060269",
                "Email notifications are not supported for PRELIMINARY_APPROVAL or REVIEW_COMPLETED status.",
                "Email notifications are not supported for PRELIMINARY_APPROVAL or REVIEW_COMPLETED status."),
        REGISTRATION_REQUEST_STATUS_NOT_MATCHED("4060271",
                "Unable to send email: No email template is configured for this registration status",
                "Unable to send email: No email template is configured for this registration status"),
        MAX_SPEAKER_PER_APPLICATION("4060288",
                "You cannot add more speakers than the maximum allowed per application.",
                "You cannot add more speakers than the maximum allowed per application.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        RegistrationRequestExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

	public enum AnalyticsExceptionMsg {
		TOKEN_NOT_FOUND("406301", "Unable to get access token.", "Unable to get access token."),
		UNABLE_TO_REFRESH_TOKEN("406302", "Unable to refresh new token.", "Unable to refresh new token."),
		UNABLE_TO_OBTAIN_TOKEN("406303", "Unable to generate token.", "Unable to generate token."),
		SERVER_ERROR("406304", "Google API server error.", "Google API server error."),
		USER_ALREADY_CONNECTED("406305", "User is already connected to application.", "User is already connected to application.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		AnalyticsExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

	}
	public enum EmbedWidgeSettingExceptionMsg  {
		ORGANIZER_OR_EVENT_ID_NOT_EXIST("4064901", "Organizer/Event id not exists"),
		WIDGET_SETTING_TYPE_NOT_EXISTS("4064902", "Embed widget setting type not exists"),
		WIDGET_SETTING_ALREADY_EXISTS("4064903", "Embed widget setting already exists"),
		WIDGET_SETTING_NOT_FOUND("4064904", "Embed widget setting not found"),
		NOT_VALID_JSON_FORMAT("4060036", "Json Format is not valid");

		private final String statusCode;
		private final String errorMessage;
		private final String developerMessage;

		EmbedWidgeSettingExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		EmbedWidgeSettingExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum AttendeesSequenceExceptionMsg  {
		SEQUENCE_NAME_NOT_EXIST("4065901", "Sequence name not exist"),
		INVALID_NUMBER_TO_EXCLUD("4065902", "The excluded number list is in an invalid format. Please use commas to separate individual numbers (4,11,36) or create ranges with dashes (12-22)."),
		TICKET_TYPE_NOT_EMPTY("4065903", "Ticket type should not empty", "Select at least one ticket type"),
		SEQUENCE_NOT_EXIST("40604", "Sequence not exist"),
		SEQUENCE_NUMBER_ALREADY_ASSIGNED("4065905", "Sequence number already assigned"),
		EXCLUDED_NUMBER_ALREADY_ASSIGNED("4065906", "Some of your excluded numbers have already been assigned. Please review the list of assigned numbers and only exclude numbers which have not already been issued."),
		INVALID_AGE("4065907", "Invalid min age and max age","Selected minimum age should be less then maximum age"),
		INVALID_MIN_STARTING_NUMBER("4065908", "Invalid minimum staring number","Minimum starting should be greater then zero"),
		INVALID_ENDING_NUMBER("4065909", "Invalid ending number","Ending number should be greater than Minimum starting number"),
		SEQUENCE_NUMBER_ALREADY_ASSIGNED_BETWEEN_RANGE("4065910", "Sequence numbers already assigned between Ending Number and Starting Number");
		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		AttendeesSequenceExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}
		AttendeesSequenceExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum IPLookUpExceptionMsg  {
		IP_LOOK_UP_NOT_NULL("4066901", "IPLookUp object can not null. Please verify json string"),
		JSON_STRING_NOT_NULL("4066902", "Json string can not null. Please verify json string");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		IPLookUpExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}
		IPLookUpExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum CartAbandonmentExceptionMsg  {
		NOT_VALID_CART_KEY("4061301", "Cart key is not valid."),
        CART_ABANDONMENT_ALREADY_EXIST("4061302","Entry for card abandonment already exists.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		CartAbandonmentExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}
		CartAbandonmentExceptionMsg(String statusCode, String errorMessage) {
			this(statusCode, errorMessage, errorMessage);
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum FeedExceptionMsg {
		UNABLE_TO_CREATE_FEED("4067901", "Unable to create new feed"),
		UNABLE_TO_UPDATE_FEED("4067902", "Unable to update feed"),
		UNABLE_TO_RETRIEVE_FEED("4067903", "Unable to retrieve feeds"),
		UNABLE_TO_FOLLOW_FEED("4067904", "Unable to follow feeds"),
		UNABLE_TO_GET_CLIENT("4067905", "Unable to get client"),
        UNABLE_TO_RETRIEVE_FEED_REACTION("4067906", "Unable to retrieve feeds reaction"),
        LOUNGE_FEED_NOT_FOUND("4067907", "Activity stream not found"),
        NOT_AUTHORIZED_TO_UPDATE_THIS_FEED("4067908", "You are not authorized to update this feed.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		FeedExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}
		FeedExceptionMsg(String statusCode, String errorMessage) {
			this(statusCode, errorMessage, errorMessage);
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum AttendeeExceptionMsg {
		ATTENDEE_NOT_FOUND("4068001", "Attendee Not Found", "Attendee Not Found"),
		ATTENDEE_ALREADY_ADDED("4068002", "Attendee already added.", "Attendee already added."),
		ATTENDEE_ALREADY_CONNECTED("4068003", "Attendee already connected.", "Attendee already connected."),
		ATTENDEE_CAN_NOT_ADDED("4068004", "Attendee doesn't have ticket or not match role admin, exhibitor, speaker.", "Attendee doesn't have ticket or not match role admin, exhibitor, speaker."),
		ATTENDEE_CAN_NOT_CONNECTED("4068005", "Attendee can not connect to self.", "Attendee can not connect to self."),
		LINK_HAS_BEEN_EXPIRE_OR_USED("4068006", "Link has been expired Or used.", "Link has been expired Or used."),
        ATTENDEE_NAME_CAN_NOT_EMPTY("4068007", "Attendee name can not empty.", "Attendee name can not empty."),
        ATTENDEE_CAN_NOT_UPLOADED_PAYMENT_FAIL("4068008", "Attendees can not upload due to a payment failed.", "Attendees can not upload due to a payment failed."),
        SUPER_ADMIN_CANNOT_CONNECT_WITHOUT_TICKET("4068009", "Super admin can not connect without purchasing event ticket.", "Super admin can not connect without purchasing event ticket."),
        ATTENDEE_HAVE_IN_PERSON_TICKET("4068010", "The ticket you hold only grants access to the in-person event. Please, contact the organizers for more information", "The ticket you hold only grants access to the in-person event. Please, contact the organizers for more information"),
        ATTENDEE_HAVE_VIRTUAL_TICKET("4068011", "This registration type is for the virtual event only.", "This ticket only grants access to the virtual event "),
        NOT_ALLOW_TO_ACCESS_VIRTUAL_PORTAL("4068012","You have not purchased Virtual Event Tickets. Please, contact the organizers for more information","You have not purchased Virtual Event Tickets. Please, contact the organizers for more information"),
        NOT_REGISTERED_FOR_THIS_EVENT("4068013","[email_address]","Looks like you are not registered for this event. Make sure the email [email_address] is the correct one or register by clicking the button below"),
		ATTENDEE_ALREADY_CONNECTED_OR_REJECTED("4068014", "This connection request is not in pending status and hence can not be cancelled", "This connection request is not in pending status and hence can not be cancelled"),
        ATTENDEE_HAS_NOT_CONNECTED("4068015", "You are not connected with this attendee.", "You are not connected with this attendee."),

        NOT_ALLOW_TO_MORE_THAN_10_INTERESTS("4068016","You can use a maximum of up to 10 interests in a filter.","You can use a maximum of up to 10 interests in a filter."),

        NO_REGISTRATION_FOUND_FOR_SESSION_MAGIC_LINK("4068017","No registrations were found for this email address. Please double-check and try again.","No registrations were found for this email address. Please double-check and try again."),
        ATTENDEE_NOT_ALLOWED_TO_SCHEDULE_MEETING("4068018","Attendees are not allowed to Schedule Meetings.","Attendees are not allowed to Schedule Meetings."),
        LINK_HAS_BEEN_EXPIRED("4068020", "Link has been expired.", "Link has been expired."),
        ATTENDEE_SHOW_CONTACTS_INFO_IS_DISABLED("4068019","This attendee’s information is private.","This attendee’s information is private."),
        NOT_VALID_CODE("4068019","Provided 4 digit code is not valid or has been used.","Provided 4 digit code is not valid or has been used."),
        ATTENDEE_HAVE_PURCHASED_IN_PERSON_TYPE_TICKETS_ONLY("4068021","You have purchased an In-Person ticket type, it is not allowed to access the virtual portal. Please purchase a valid ticket by clicking the button below.","You have purchased an In-Person ticket type, it is not allowed to access the virtual portal. Please purchase a valid ticket by clicking the button below.");


		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		AttendeeExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

	public enum UserNotificationPreferenceExceptionMsg {
		NOTIFICATION_PREFERENCE_ADDED("406002", "Notification Preference already added.", "Notification Preference already added.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		UserNotificationPreferenceExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum ExpoExceptionMsg {
		EXPO_SETTING_GLOBAL_AND_EXHIBITOR_SETTINGS_PRESENT_INPUT("4068901","System error, Global config & exhibitor Id also present"),
		EXPO_SETTING_GLOBAL_AND_EXHIBITOR_SETTINGS_MISSING_INPUT("4068901","System error, Global config not present & exhibitor Id also not present");


		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ExpoExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

	public enum SessionSpeakerExceptionMsg {
		SESSION_NAME_NOT_EXIST("4068901", "Session name not exist"),
		NOT_VALID_NAME("4068902", "Please enter valid name"),
		SESSION_SLOTS_COLLIDES("4068903", "Main stage sessions cannot overlap. It looks like you have a conflict with {SESSION_NAME}"),
		TICKET_TYPE_NOT_MATCHED("4068904", "Your registration does not permit access to this session"),
		REGISTER_MAX_LIMIT_REACHED("4068905", "Your registration type only permits registering for {number_of_sessions_permitted} sessions. If you would like to register for this session, please un-register from a different session first."),
		SPEAKER_ALREADY_EXIST("4068906", "Speaker already exist with same email"),
		KEY_VALUE_ALREADY_EXIST("4068907", "{KEY} name already exist"),
		PERMISSION_DENIED_REGISTER_EVENT("4068908", "This is not registered user"),
		PERMISSION_DENIED_ENTER_EVENT("4068909", "This is attendee user"),
		REGISTER_FAILED("4068910", "You have not yet registered for the event. You must register for the event before you can register for individual sessions."),
		SESSION_ALREADY_STARTED("4068911", "Session already started"),
		CAN_NOT_GENERATE_STREAM_IN_PROGRESS("4068912", "Can not generate stream key, live stream in process"),
		EXCEED_SESSION_CAPACITY("4068913", "Max user registration capacity reached"),
		CAN_NOT_SET_DEFAULT_PLAYBACK("4068914", "Can not set default playback for active session"),
		REQUIRED_FOR_AE_STUDIO("4068915", "Stream Key, Stream Url can not be blank for accelevents provider"),
		EVENT_ENDED("4068916", "This event is no longer available"),
		MAX_FREE_ATTENDEES_REACHED_FOR_PORTAL("4068917", "All capacity for free registrations have been redeemed. You can purchase a pass to access the event!"),
		CONTACT_EVENT_ORGANIZER_TO_BE_A_MODERATOR("4068918", "Session moderator only can START/STOP the session. Please contact event organizer to be a moderator."),
		NOT_SPEAKER_IN_EVENT("4068919", "You are not speaker in this session. Please contact event organizer."),
		SESSION_STARTED_20_SEC("4068920", "Please wait for while, Moderator had already started broadcast or try after 20 sec."),
		BLOCKED("4068921", "You have been blocked by admin. Please contact system administrator."),
        BLOCKED_ADMIN_MSG("4068922", "This user is blocked. Please unblock user first!"),
        USER_DID_DONATION_NOT_PURCHASE_TICKET("4068923", "Looks like you forgot to get a ticket when you made a donation, please purchase a ticket from the event page or contact the event organizer."),
        STREAM_DISRUPT("4068924", "Stream seems be live but disconnected, System will wait for 120 seconds before marking the stream as stopped."),
        SESSION_END_TIME_SHOULD_BE_BEFORE_11_59PM("4068925","Session end time should be before 11:59 PM"),
        USER_NOT_ALLOWED_TO_REGITER_WITHOUT_TICKET("4068926", "User not allowed to regiter without ticket"),
        SESSION_ALREADY_EXIST("4068927", "Session already exist with same Title"),
        PAST_EVENT_DATE_TIME("4068928", "Please enter a date/time in the future."),
        SESSION_SPEAKER_NOT_FOUND("4068929", "Session Speaker not found."),
        PAST_EVENT_START_DATE_TIME("4068930", "Please enter a start date/time in the future."),
        ALREADY_TICKET_TYPE_USED("4068932", "Can not remove this ticket type. Attendees have already registered using this ticket type"),
        PAST_EVENT_END_DATE_TIME("4068931", "Please enter an end date/time in the future."),
        EXCEED_WORKSHOP_SESSION_CAPACITY("4068933", "All 250 spaces in this meeting have already been taken. Would you like to remove someone from the session so that you can join?"),
        INVALID_SESSION_DATE_TIME("4068935", "Please enter valid date/time format. Date is not in yyyy/MM/dd Or Time HH:mm format"),
		AUDIENCE_FILTER_ALREADY_EXISTS("4068936", "Audience filter already exist with same name"),
        SPEAKER_ALREADY_EXIST_WITH_EMAIL("4068927", "Speaker already exist with [email_address]"),
		STAFF_NOT_REGISTER_IN_SESSION("4068937","You need an attendee registration type for this session. Contact your Event Admin for more information."),
        BROADCAST_ALREADY_STOPPED("4068938", "Broadcast already stopped"),
        BROADCAST_ALREADY_STARTED("4068939", "Broadcast already started"),
        BROADCAST_NOT_STARTED("4068940", "Broadcast not started"),
        CAN_NOT_ENABLE_LIVE_CAPTIONING_IN_LIVE_STREAMING("4068941", "Live closed captions can not be configured to an active live stream."),
        LIVE_CAPTIONS_DOES_NOT_SUPPORT_LOW_LATENCY("4068942", "live captions does not work with low latency"),
        USER_DID_NOT_PURCHASE_TICKET("4068943", "If you believe you are receiving this message in error, please contact the event organizer."),
        SHORT_DESCRIPTION_OF_SESSION("4068944", "Short description should not be more than 150 characters."),
        FIRST_NAME_NOT_BE_NULL("4068945", "First name must be not null OR First name must be less than 50 characters."),
        LAST_NAME_NOT_BE_NULL("4068946", "Last name must be not null OR Last name must be less than 50 characters."),
        STREAM_ID_NOT_BE_NULL("4068940", "Stream id must not be null"),
        STREAM_PROVIDER_CHANGED_FOR_SESSION("4068947","Can not start the broadcast due to a stream provider change for this session."),
        INVALID_INVITED_ATTENDEE("4068941", "Invited attendee is invalid"),
        SPEAKER_REQUIRED_UPDATE_PLAN("4068949", "To add more than {XX} speakers to an event please upgrade your plan."),
        TAGS_TRACKS_CHARACTER_LIMIT("4068950", "Single tags/tracks should not be more than 50 characters."),
        SPEAKER_ATTRIBUTE_EXISTS("4068950","Speaker attribute already exist with same name."),
        SPEAKER_ATTRIBUTE_NOT_EXISTS("4068951","Speaker attribute does not exists."),
        PARENT_QUESTION_NOT_FOUND("4068952", "Conditional questions must have one parent question"),
        CONDITIONAL_QUESTION_SELECTED_ANS_ID("4068953", "Conditional question's one record must present having 0 selectedAnsId"),
        MINIMUM_ONE_SUB_QUESTION_NEEDED("4068954", "At least 1 sub question is required"),
        USER_ALREADY_REGISTER_IN_SESSION("4068955","Users already registered in this session so you can't make it as a draft session."),
        INVALID_SESSION_TYPE_FORMAT("4068956","Session type format is not valid for current event."),
        SESSION_NOT_FOUND_FOR_THE_GIVEN_ID("4068957","Session not found for the given ID."),
        DRAFT_SESSION_NOT_ACCESSIBLE("4068958","You don't have permission to access the draft session."),
        SESSION_ATTRIBUTE_EXISTS("4068959","Session attribute already exist with same name."),
        ATTRIBUTE_TYPE_INVALID("4068960","Custom attribute type invalid."),
        SESSION_ATTRIBUTE_NOT_EXISTS("4068961","Session attribute does not exists."),
        CUSTOM_ATTRIBUTE_NOT_EXISTS("4068962","Custom attribute does not exists."),
        UNABLE_TO_FETCH_CUSTOM_ATTRIBUTES_FROM_SSO_PROVIDER("4068963", "Unable to fetch custom attributes from the SSO provider."),
        CONTACT_ATTRIBUTE_EXISTS("4068964","The contact attribute with the same name already exists."),
        CONTACT_DEFAULT_ATTRIBUTE_UPDATE_NOT_ALLOWED("4068965","Cannot update default contact fields (First Name, Last Name, Email)."),
        RESERVED_SESSION_ATTRIBUTE_NAME("4068966","Attribute name 'Primary Sessions' or 'Secondary Sessions' or 'Speaker Id' is reserved and cannot be used for custom fields.");

        private String statusCode;
		private String errorMessage;
		private String developerMessage;
        private String defaultMessage;
        private Map<String,String> defaultMessageParamMap;

		SessionSpeakerExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

        SessionSpeakerExceptionMsg(String statusCode, String errorMessage, String defaultMessage, Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
            this.defaultMessage = defaultMessage;
            this.defaultMessageParamMap = defaultMessageParamMap;
        }

        SessionSpeakerExceptionMsg(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage=defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

		public void setErrorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
		}

		public void setDeveloperMessage(String developerMessage) {
			this.developerMessage = developerMessage;
		}

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }

	public enum NetworkingRulesExceptionMsg {
		NETWORKING_RULE_ALREADY_EXISTS("406901", "Networking Rule already exists.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		NetworkingRulesExceptionMsg(String statusCode, String errorMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = errorMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}
	public enum NetworkingLoungeExceptionMsg {
		NETWORKING_LOUNGE_NOT_FOUND("4070001", "Networking Lounge Not Found.", "Networking Lounge Not Found."),
        NETWORKING_LOUNGE_DELETED_BY_THE_ADMIN("4070006", "Networking Lounge Deleted By The Admin. Please Refresh The Page And Try Again.", "Networking Lounge Deleted By The Admin. Please Refresh The Page And Try Again."),
		NETWORKING_LOUNGE_ALREADY_CONNECTED("4070002", "Networking Lounge Already Connected.", "Networking Lounge Already Connected."),
        YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_PHOTO("4070003", "You Can Delete Only Own Networking Lounge Photo.", "You Can Delete Only Own Networking Lounge Photo."),
        YOU_CAN_NOT_DELETE_NETWORKING_LOUNGE_VIDEO("4070004", "You Can Delete Only Own Networking Lounge Video.", "You Can Delete Only Own Networking Lounge Video."),
		NETWORKING_LOUNGE_WITH_SAME_NAME_ALREADY_EXIST("4070005","A networking lounge with the same name already exists", "A networking lounge with the same name already exists"),
        NETWORKING_LOUNGE_VIDEO_NOT_FOUND("4070007", "Networking lounge video not Found.", "Networking lounge video not Found."),

        NETWORKING_LOUNGE_MAX_APPROVED_LIMIT("4070008", "Networking lounge max approved limit ${count} has been reached.", "Networking lounge max approved limit ${count} has been reached."),
        NETWORKING_LOUNGE_ATTENDEE_NOT_ALLOWED_JOINED(  "4070009","Networking lounge max video occupancy limit has been reached." , "Networking lounge max video occupancy limit has been reached." ),
        NETWORKING_LOUNGE_MAX_ATTENDEE_CAPACITY(  "4080010","The lounge has reached its maximum attendee capacity of ${count}." , "The lounge has reached its maximum attendee capacity of ${count}." );

        private String statusCode;
		private String errorMessage;
		private String developerMessage;

		NetworkingLoungeExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

	public enum ExhibitorProductExceptionMsg {
		INVALID_PRODUCT_PERCENTAGE_DISCOUNT_AMOUNT("4071001", "Percentage discount can not be greater than 100.", "Percentage discount can not be greater than 100."),
		INVALID_PRODUCT_FLAT_DISCOUNT_AMOUNT("4071001", "Discount amount can not be greater than product price.", "Discount amount can not be greater than product price.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ExhibitorProductExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}
	}

    public enum SessionExceptionMsg {
        SESSION_CAN_NOT_BE_DELETE("4072001", "This session cannot be deleted because broadcast is running. Please stop broadcast."),
        TIME_ACCESS_ELAPSED_LIVE("4072002", "The time limit for accessing this session is expired."),
        TIME_ACCESS_ELAPSED_RECORDING("4072003", "The recording of this session is only available to users who watched it live."),
        SESSION_IS_NOT_PRIVATE("4072004", "Sorry, This session is not private, Bulk registration is only supported in private sessions."),
        CAN_NOT_UPLOAD_EMPTY_FILE("4072005", "Can not upload empty file. Please add data and try again."),
        NOT_POSSIBLE_TO_ACTIVATE_AUTOPLAY("4072006","It's not possible to activate Autoplay before uploading a video"),
        SESSION_ALREADY_EXPIRED("4072007","Session already expired."),
        SESSION_VIDEO_RECORDING_TRIM_END_TIME_INVALID("4072008","Video trim end time invalid"),
        SESSION_IS_PRIVATE("4072009", "Sorry, This session is private, Contact your Event Admin for more information."),
        SESSION_BOOKMARK_CAPACITY_EXCEEDED("4072010","Registration capacity for this session has been reached. Please contact the Event Admin for further assistance."),

        SESSION_LOCATION_CHARACTER_LIMIT("4072011","Session Location should not be more than 255 characters"),
        SESSION_REGISTRATION_NOT_ALLOWED("4072012","You are not allowed to register this session as you already registered for another session that takes place at the same time."),
        SESSION_REGISTRATION_NOT_ALLOWED_WITH_TRACK_LIMIT("4072013","You are not allowed to register for this session because your ticket's allowed session limits for all associated tags and tracks have been exhausted."),
        SESSION_SPEAKER_DUPLICATE("4072014","Duplicate speaker emails found in both Primary and Secondary columns: {duplicateList}"),
        SESSION_CUSTOM_ATTRIBUTE_DATA_INVALID("4072015","Session custom attribute data is invalid.")
        ;

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SessionExceptionMsg(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum LocationExceptionMsg {
        LOCATION_ALREADY_EXISTS("4072101","Location with same name already exists.","Location with same name already exists.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        LocationExceptionMsg(String statusCode, String errorMessage,String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum TrayIOExceptionMsg {
        FAILED_TO_GET_AUTH_CODE("4073001", "Error while generating authorization code"),
        FAILED_TO_CREATE_SOLUTION_INSTANCE("4073002", "Error while creating solution instance"),
            FAILED_TO_GENERATE_USER_TOKEN("4073003", "Error while creating user token"),
        FAILED_TO_GET_USER_INFO("4073004", "Error while fetching users details"),
        FAILED_TO_UPDATE_SOLUTION_INSTANCE("4073005", "Error while updating solution instance"),
        FAILED_TO_DELETE_SOLUTION_INSTANCE("4073006", "Error while delete solution instance"),
        FAILED_TO_RECONFIGURE_SOLUTION_INSTANCE("4073007", "Error while reconfigure solution instance"),
        CROSS_RECORD_TYPE_AVAILABILITY("4073008", "Cross record type available only in SalesForce integration"),
        DEAL_PIPELINE_STAGE_AVAILABILITY("4073009", "Pipeline & stage are mandatory property for enabling deal in hubspot"),
        HUBSPOT_INTEGRATION_IS_NOT_CONFIGURED("4073010", "Look like HubSpot integration is not configured or disabled"),
        HUBSPOT_INTEGRATION_RECONFIGURE_REQUIRED("4073011", "Please uninstall HubSpot integration and reconfigure it again");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        TrayIOExceptionMsg(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


	public enum ChallengeExceptionMsg {
		CHALLENGE_NOT_ALLOWED_TO_EDIT ("4069001",
				"Challenge is only editable before event Start Date / Pre-Event Access date.",
				"Challenge is only editable before event Start Date / Pre-Event Access date."),

        EARLY_BIRD_CHALLENGE_ALREADY_EXISTS("4069002",
                "Challenge for this action already exists.",
                "Challenge for this action already exists."),

        NOT_ALLOW_TO_CREATE_MULTIPLE_SCAVENGER_HUNT_CHALLENGE("4069003",
                "You can create only one scavenger hunt challenge with your current plan.",
                "You can create only one scavenger hunt challenge with your current plan."),

        EXHIBITOR_AND_SESSION_NOT_FOUND("4069004",
                "Not enough exhibitors, sessions, and networking sessions found to create a Scavenger hunt challenge.",
                "Not enough exhibitors, sessions, and networking sessions found to create a Scavenger hunt challenge."),
        TICKET_TYPES_NOT_FOUND("4069006",
                "Valid ticket types not found, Please create tickets and try again.",
                "Valid ticket types not found, Please create tickets and try again."),
        RESTRICTED_SESSION_NOT_ALLOWED_TO_SAVE("4069007",
                "Restricted sessions not allowed to save in challenge, Sessions ($sessionList)",
                "Restricted sessions not allowed to save in challenge, Sessions ($sessionList)"),
        NOT_ALLOW_TO_UPDATE_CHALLENGE_IN_PLAN("4069008",
                "Updating this challenge is not allowed in your plan, Please upgrade the plan.",
                "Updating this challenge is not allowed in your plan, Please upgrade the plan."),
        NOT_ALLOW_TO_APPLY_TRACKS_TO_CHALLENGE("4069009",
                "You are not allowed to apply for credit.",
                "You are not allowed to apply for credit.");

		private String statusCode;
		private String errorMessage;
		private String developerMessage;

		ChallengeExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
			this.statusCode = statusCode;
			this.errorMessage = errorMessage;
			this.developerMessage = developerMessage;
		}

		public String getStatusCode() {
			return statusCode;
		}

		public String getErrorMessage() {
			return errorMessage;
		}

		public String getDeveloperMessage() {
			return developerMessage;
		}

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum SizeLimitExceptionMsg {

        FIRST_NAME_SIZE_LIMIT ("4069101", "Only 50 Characters are allowed in firstName", "First Name"),
        LAST_NAME_SIZE_LIMIT ("4069102", "Only 50 Characters are allowed in lastName", "Last Name"),
        EMAIL_SIZE_LIMIT ("4069103", "Only 75 Characters are allowed in email", "Email"),
        TRANSACTION_ORDER_SIZE_LIMIT("4069104", "Only 45 digits are allowed in Transaction ID", "Only 45 digits are allowed in Transaction ID"),
        FIRST_NAME_EMPTY("4069105", "First Name must not be empty", "First Name must not be empty"),
        LAST_NAME_EMPTY("4069106", "Last Name must not be empty", "Last Name must not be empty"),
        EMAIL_EMPTY("4069107", "Email must not be empty", "Email must not be empty"),
        FILE_UPLOAD_SIZE_LIMIT_EXCEEDS("4069108", "The file you are trying to upload exceeds the limit.  Please try to upload a smaller file.", "The file you are trying to upload exceeds the limit.  Please try to upload a smaller file.");


        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SizeLimitExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum EventChallengeException {
        CHALLENGE_NAME_EXCEPTION ("4070101", "Challenge name should be unique.", "Challenge name should be unique."),
        CHALLENGE_NOT_AVALABLE_FOR_PLAN("4070102", "{} not available for this plan.", "{} not available for this plan.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventChallengeException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum EventChallengeTiersException {
        CHALLENGE_TIER_NAME_EXCEPTION ("4070201", "Challenge tier name should be unique.", "Challenge tier name should be unique."),

        PRE_REQUISITE_CHALLENGE_TIER_EXIST_EXCEPTION ("4070202", "Pre-requisite tier is already exists in another tier.", "Pre-requisite tier is already exists in another tier."),
        CHALLENGE_TIER_NAME_EXCEEDS_EXCEPTION ("4070201", "Unable to duplicate tier. The generated name exceeds the 255 character limit.", "Unable to duplicate tier. The generated name exceeds the 255 character limit.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventChallengeTiersException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum ColumnSelectionException {
        COLUMN_SELECTION_ALREADY_CREATED("4069201",
                "Column selection is already created for provided details.",
                "Column selection is already created for provided details."),
        REQUIRED_COLUMNS_ARE_MISSING("4069202",
                "Required columns are missing (${column})",
                "Required columns are missing (${column})");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ColumnSelectionException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum EventRequestFormExceptionMsg {
        EVENT_REQUEST_FORM_NAME_ALREADY_EXISTS("4069501", "Event Request Form with this name already exists", "Event Request Form with this name already exists"),
        EVENT_REQUEST_FORM_URL_ALREADY_EXISTS("4069502", "Event Request Form with this URL already exists", "Event Request Form with this URL already exists"),
        EVENT_REQUEST_FORM_FIELD_NAME_ALREADY_EXISTS("4069503", "Event Request Form Field with this name already exists", "Event Request Form Field with this name already exists"),
        EVENT_REQUEST_FORM_EMAIL_NAME_ALREADY_EXISTS("4069504", "Email with this name already exists", "Email with this name already exists"),
        EVENT_REQUEST_FORM_EMAIL_NOT_EXISTS("4069505", "Email not found", "Email not found"),
        EVENT_REQUEST_FORM_NOT_PUBLISHED("4069506", "Event Request Form is not published.", "Event Request Form is not published."),
        EVENT_REQUEST_FORM_FIELD_VALUE_NOT_VALID("4069507", "Event Request Form Field value is not valid.", "Event Request Form Field value is not valid."),
        EVENT_REQUEST_FORM_CANNOT_DELETE_APPROVED("4069508", "Approved event requests cannot be deleted.", "Approved event requests cannot be deleted."),
        EVENT_CANNOT_CREATE_USING_NON_APPROVED_REQUEST("4069509", "Event cannot be created using non-approved event request.", "Event cannot be created using non-approved event request."),
        EVENT_REQUEST_DATA_EXCEPTION_MSG("4069510", "Event request data is invalid for event creation.", "Event request data is invalid for event creation"),
        EVENT_REQUEST_FROM_USED_FOR_EVENT_REQUEST("4069511", "You can't delete this request form because submissions have been received or an event has been created from it.", "You can't delete this request form because submissions have been received or an event has been created from it."),
        EVENT_NOT_CREATED("4069512", "Event not created due to some error.", "Event not created due to some error."),
        EVENT_REQUEST_FORM_PLANNER_ASSIGN_EXCEPTION_MSG(
                "4069513",
                        "You can assign a planner role user only when the request status is Approved.",
                        "You can assign a planner role user only when the request status is Approved."
        ),
        EVENT_REQUEST_FORM_USER_HAVNT_AUTH_ASSIGN_REQUEST_EXCEPTION_MSG("4069514", "You haven't been authorized to assign requests.", "You haven't been authorized to assign requests."),
        EVENT_REQUEST_FORM_USER_HAVNT_VALID_ROLE_EXCEPTION_MSG(
                "4069515",
                        "Selected user is not valid for this role.",
                        "Selected user is not valid for this role."),
        EVENT_REQUEST_FORM_VALID_ROLE_EXCEPTION_MSG(
                "4069515",
                        "Assignee role is not valid.",
                        "Assignee role is not valid."
        ),
        EVENT_REQUEST_FORM_NO_AUTH_TO_CHANGE_STATUS_EXCEPTION_MSG(
                "4069516",
                "You are not authorized to change the status of this event request.",
                "You are not authorized to change the status of this event request."
        ),
        EVENT_REQUEST_FORM_NO_ASSIGNEE_TO_REQUEST_CHANGE_STATUS_EXCEPTION_MSG(
                "4069517",
                "You are not assigned to this request, so you cannot change its status.",
                "You are not assigned to this request, so you cannot change its status."
        ),
        EVENT_REQUEST_FORM_NOT_WHITELABEL_ADMIN_OR_PLANNER(
                    "4069518",
                    "Only WhiteLabel Admins or WhiteLabel Event Planners can create events.",
                    "Only WhiteLabel Admins or WhiteLabel Event Planners can create events."
            );

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventRequestFormExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum WLSSOConfigurationException {
        WL_SSO_CONFIGURATION_ALREADY_EXIST("4069301",
                "White Label SSO configuration already exist for SSO Identity Provider Type {ssoType}.",
                "White Label SSO configuration already exist for SSO Identity Provider Type {ssoType}."),
        WL_SSO_CONFIGURATION_NOT_SUPPORTED("4069302","White Label SSO configuration not supported for ping federated.","White Label SSO configuration not supported for ping federated.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        WLSSOConfigurationException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setStatusCode(String statusCode) {
            this.statusCode = statusCode;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum SurveyException {
        SURVEY_NAME_CANNOT_BE_EMPTY("4069401",
                "Survey name cannot be empty.",
                "Survey name cannot be empty."),
        SURVEY_CONFIGURATION_IS_NOT_ENABLED("4069402",
                "Survey configuration is not enabled for the session.",
                "Survey configuration is not enabled for the session."),
        SURVEY_IS_NOT_PRESENT("4069403",
                "Please select survey from dropdown or create new survey.",
                "Please select survey from dropdown or create new survey."),
        SURVEY_QUESTIONS_NOT_MATCHED("4069404",
                "Survey questions not matched with survey configuration.",
                "Survey questions not matched with survey configuration."),
        ANSWER_THE_REQUIRED_QUESTIONS("4069405",
                "You must answer the required questions.",
                "You must answer the required questions."),
        ADD_ATLEAST_ONE_QUESTION("4069406",
                "Please add at least one question to the survey.",
                "Please add at least one question to the survey."),
        SURVEY_IS_IN_USE_CANNOT_DELETE("4069407",
                                         "Survey is in use so cannot be deleted.",
                                         "Survey is in use so cannot be deleted."),

        SURVEY_HEADLINE_CANNOT_BE_EMPTY("4069408",
                "Survey headline cannot be empty.",
                "Survey headline cannot be empty."),
        ANONYMOUS_USER_UUID_CANNOT_BE_BLANK("4069409","Anonymous user uuid cannot be blank.","Anonymous user uuid cannot be blank."),
        SURVEY_SUBMISSION_IS_ONLY_ALLOWED_AFTER_SESSION_CHECKIN("4069410","Survey submission is only allowed after checking in to the session.","Survey submission is only allowed after checking in to the session.");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        SurveyException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum RecordingExceptionMsg {
        DEFAULT_PLAYBACK_NOT_DELETE("4072001", "Default playback can not be delete."),
        DEFAULT_PLAYBACK_NOT_HIDE("4082001", "Default playback can not be hide.");


        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        RecordingExceptionMsg(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }
    public enum BadgesExceptionMsg {
        BADGE_REPRINT_ACCESS("4093001","You don't have badge reprint access"),
        BADGE_PDF_GENERATION_FAILED("4093002","Badge PDF generation failed"),
        BADGE_PDF_DETAILS_NOT_FOUND("4093003","Badge PDF details not found"),
        BADGE_PDF_NAME_ALREADY_EXISTS("4093004","Exported PDF name exists with same name."),
        BADGE_PDF_NAME_EMPTY("4093005","PDF name can not be empty"),;

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        BadgesExceptionMsg(String statusCode, String errorMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = errorMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }


    public enum MobileAppConfigurationExceptionMsg {
        ACTION_BUTTONS_DATA_INCORRECT("4093101", "Action buttons data are incorrect.","Action buttons data are incorrect."),
        BOTTOM_NAVIGATION_DATA_INCORRECT("4093102", "Bottom navigation data are incorrect.","Bottom navigation data are incorrect."),
        COLORS_DATA_INCORRECT("4093103", "Colors data are incorrect.","Colors data are incorrect."),
        CUSTOM_PAGE_LABEL_EXISTS("4093104",
                "Custom page with same name already exists.",
                "Custom page with same name already exists.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        MobileAppConfigurationExceptionMsg(String statusCode, String errorMessage,String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum TaskExceptionMsg {
        SCHEDULE_DATE_IS_PAST_DATE("4094001", "The scheduled date must be in the future.", "The scheduled date must be in the future."),
        TASK_WITH_SAME_TITLE_EXIST("4094002", "Task with the same title already exists.", "Task with the same title already exists."),
        TASK_NOT_FOUND("4094003", "Task not found", "Task not found."),
        USER_TASK_NOT_FOUND("4094004","User task not found","User task not found"),
        PUBLISH_TASK_CANNOT_BE_SAVE_AS_DRAFT("4094005", "A published task cannot be save as a draft.", "A published task cannot be save as a draft."),
        TASK_TITLE_CHARACTER_LIMIT_EXCEED("4094006", "The task title exceeds 255 characters after duplication.",
                "The task title exceeds 255 characters after duplication."),
        TASK_COMPLETION_CRITERIA_REQUIRED("4094007", "Completion criteria must be provided for system-verified tasks.",
                                                  "Completion criteria must be provided for system-verified tasks."),
        SURVEY_ID_REQUIRED_EXCEPTION("4094008", "The completion criteria survey cannot be submitted without a Survey.",
                                                  "The completion criteria survey cannot be submitted without a Survey.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        TaskExceptionMsg(String statusCode, String errorMessage,String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum ReviewerExceptionMsg {
        EMAIL_ALREADY_REGISTERED_AS_REVIEWER("4095001", "This email is already associated with a reviewer for this event.", "This email is already in the reviewer list."),
        REVIEWER_NOT_FOUND("4095002", "No reviewer found with the given details.", "No reviewer found with the given details."),
        REVIEWER_EMAIL_SETTING_NOT_FOUND("4095003", "Reviewer email setting not found.", "Reviewer email setting not found."),
        REVIEWER_EMAIL_SETTING_ALREADY_EXIST("4095004", "Reviewer email setting already exist.", "Reviewer email setting already exist."),
        REVIEWER_DETAILS_REQUIRED("4095005", "Reviewer first name, last name, or email cannot be blank.", "Reviewer first name, last name, or email cannot be blank."),
        RECUSAL_REASON_IS_REQUIRED("4095006","A recusal reason is required when performing the \"Can't Review\" action.","A recusal reason is required when performing the \"Can't Review\" action.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ReviewerExceptionMsg(String statusCode, String errorMessage,String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
    }

    public enum EventCECriteriaException {
        CRITERIA_NAME_EXCEPTION ("4096001", "Criteria name should be unique.", "Criteria name should be unique."),
        CRITERIA_NOT_AVAILABLE_FOR_PLAN("4096002", "{} not available for this plan.", "{} not available for this plan."),
        CRITERIA_NOT_ALLOWED_TO_EDIT ("4096003","Criteria is only editable before event Start Date / Pre-Event Access date.", "Criteria is only editable before event Start Date / Pre-Event Access date."),
        CRITERIA_NOT_ALLOW_TO_CREATE("4096004",
                "Criteria is not available in your plan.",
                "Criteria is not available in your plan."),
        CRITERIA_ALREADY_ASSOCIATED_WITH_CERTIFICATE("4096005","Challenge {challenge_name} is already selected in {certificate_name} certificate.","Challenge {challenge_name} is already selected in {certificate_name} certificate.");
        private final String statusCode;
        private String errorMessage;
        private String developerMessage;

        EventCECriteriaException(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }
    }

    public enum ReviewProcessExceptionMsg {
        TITLE_ALREADY_EXIST("4097001", "Title is already associated with a review process for this event.", "Title is already associated with a review process for this event."),
        MINIMUM_ONE_QUESTION_REQUIRED("4097002", "At least one question must be present in the review process.", "At least one question must be included in the review process."),
        REVIEW_PROCESS_NOT_FOUND("4097003","Review process not found.","Review process not found."),
        TITLE_CHARACTER_LIMIT_EXCEED("4094006", "The Review Process title exceeds 255 characters after duplication.",
                                                  "he Review Process title exceeds 255 characters after duplication.");
        private String statusCode;
        private String errorMessage;
        private String developerMessage;

        ReviewProcessExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }
        }

    public enum EntryExitExceptionMsg {
        BARCODE_OR_RFID_TAG_REQUIRED_TO_ENTER_EXIT_TICKET(
                "4094010",
                "Entry or exit requires a valid Barcode or RFID tag.",
                "Entry or exit requires a valid Barcode or RFID tag."),
        ENTRY_EXIT_MODULE_DISABLED(
                "4094011",
                "Entry/Exit module is disabled. Please contact the admin to enable it for attendee entry or exit.",
                "Entry/Exit module is disabled. Please contact the admin to enable it for attendee entry or exit."),
        ENTRY_REQUIRED_BEFORE_EXIT(
                "4094012",
                "To exit, the attendee must have first entered the event.",
                "To exit, the attendee must have first entered the event."),
        EXIT_REQUIRED_BEFORE_REENTER(
                "4094013",
                "To re-enter, the attendee must have exited the event first.",
                "To re-enter, the attendee must have exited the event first."),
        BARCODE_IDS_REQUIRED_TO_ENTER_EXIT_TICKET(
                "4094014",
                "Entry or exit requires a valid Barcode.",
                "Entry or exit requires a valid Barcode."),
        BULK_ENTRY_NOT_ALLOWED("4094015", "Bulk ticket entry is not permitted.", "Bulk ticket entry is not permitted."),
        NO_CHECKED_IN_TICKETS_FOUND("4094016", "Unable to proceed: no check-in ticket found.", "Unable to proceed: no check-in ticket found.");

        private String statusCode;
        private String errorMessage;
        private String developerMessage;
        private String defaultMessage;
        private Map<String,String> defaultMessageParamMap;

        EntryExitExceptionMsg(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        EntryExitExceptionMsg(String statusCode, String errorMessage, String developerMessage,String defaultMessage,Map<String,String> defaultMessageParamMap) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
            this.defaultMessage= defaultMessage;
            this.defaultMessageParamMap=defaultMessageParamMap;
        }

        public String getStatusCode() {
            return statusCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getDeveloperMessage() {
            return developerMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public void setDeveloperMessage(String developerMessage) {
            this.developerMessage = developerMessage;
        }

        public String getDefaultMessage() {
            return defaultMessage;
        }

        public void setDefaultMessage(String defaultMessage) {
            this.defaultMessage = defaultMessage;
        }

        public Map<String, String> getDefaultMessageParamMap() {
            return defaultMessageParamMap;
        }

        public void setDefaultMessageParamMap(Map<String, String> defaultMessageParamMap) {
            this.defaultMessageParamMap = defaultMessageParamMap;
        }
    }
}